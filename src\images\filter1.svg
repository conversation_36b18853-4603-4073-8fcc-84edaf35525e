<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24.145" height="14.822" viewBox="0 0 24.145 14.822">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_1577" data-name="Path 1577" d="M299.242,480.038a4.355,4.355,0,0,0-.672-.875c-.232-.183-.3-.331-.414-.341s-.212.053-.281-.03-.1-.222-.222-.232a.2.2,0,0,0-.178.05.193.193,0,0,1-.222-.03c-.1-.07-.424-.163-.513-.222s-.044.1-.01.133-.046.01-.208-.03-.285-.03-.143.109.218.282.321.331.024.1-.149.183-1.5.994-2.016,1.3a14.522,14.522,0,0,0-1.431.932,11.952,11.952,0,0,1-3.344.189c-.443-.075-2.564-.292-3.1-.377a3.938,3.938,0,0,0-2.094-.028c-.758.288-1.807.835-1.957,1.847s-.165,1.615.708,2.2,2.2,1.146,2.214,1.871a9.079,9.079,0,0,1-.076,1.3c-.015.256.015.586.362.693a12.317,12.317,0,0,1,2,.875c.3.228,1.2.86,1.265.966a.836.836,0,0,1,.045.378c0,.091.242.226.377.286s.33.165.436.211.151-.18.136-.421a.965.965,0,0,0-.331-.786,4.058,4.058,0,0,1-.5-.512,1.025,1.025,0,0,0-.466-.347,10.446,10.446,0,0,1-1.627-.844.93.93,0,0,1-.42-.875c0-.453.074-1.374.1-1.584a2.513,2.513,0,0,0,.091-.558c-.015-.2.089-.121.225.015a6.866,6.866,0,0,0,1.341.768,15.059,15.059,0,0,0,1.49.393c.407.076.721.121.812.152s.076.182.091.286.288.212.407.286a10.41,10.41,0,0,0,1.129.994,5.454,5.454,0,0,1,.888.618c.106.091.045.241.045.287s-.151.154-.437.228a4.716,4.716,0,0,1-.979.15,1.316,1.316,0,0,1-.706-.135c-.151-.076-.015-.211-.076-.3a.666.666,0,0,0-.6-.3.769.769,0,0,0-.5.165c-.076.03-.108.12.1.271s.409.378.528.408a1.088,1.088,0,0,1,.481.256c.15.15.347.423.617.378s.994-.317,1.235-.393a2.416,2.416,0,0,1,.511-.121.382.382,0,0,0,.437-.18c.165-.256.21-.3.225-.423s.119-.347-.106-.512a5.414,5.414,0,0,1-.663-1c-.119-.165-.451-.753-.451-.753a3.685,3.685,0,0,0,.5-.211c.227-.121.526-.438.648-.453a2.017,2.017,0,0,0,.542-.241,1.551,1.551,0,0,0,.513-.725,5,5,0,0,0,.316-1.3c.03-.378.059-.634.059-.634a.944.944,0,0,1,.166-.358,7.722,7.722,0,0,0,.672-1.034,1.452,1.452,0,0,1,.454-.613.846.846,0,0,1,.523-.143c.178.02.327.133.489.163s.778.421,1.006.48.46.143.5.242.034.232.143.232.42.05.44-.02.063-.117.143-.083.321-.069.341-.179-.04-.252.059-.322.178-.258.079-.361-.2-.189-.281-.282-.688-.929-.82-1.1A2.121,2.121,0,0,1,299.242,480.038Z" transform="translate(-282.537 -478.344)" fill="#fff" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_1576" data-name="Path 1576" d="M299.242,480.038a4.355,4.355,0,0,0-.672-.875c-.232-.183-.3-.331-.414-.341s-.212.053-.281-.03-.1-.222-.222-.232a.2.2,0,0,0-.178.05.193.193,0,0,1-.222-.03c-.1-.07-.424-.163-.513-.222s-.044.1-.01.133-.046.01-.208-.03-.285-.03-.143.109.218.282.321.331.024.1-.149.183-1.5.994-2.016,1.3a14.522,14.522,0,0,0-1.431.932,11.952,11.952,0,0,1-3.344.189c-.443-.075-2.564-.292-3.1-.377a3.938,3.938,0,0,0-2.094-.028c-.758.288-1.807.835-1.957,1.847s-.165,1.615.708,2.2,2.2,1.146,2.214,1.871a9.079,9.079,0,0,1-.076,1.3c-.015.256.015.586.362.693a12.317,12.317,0,0,1,2,.875c.3.228,1.2.86,1.265.966a.836.836,0,0,1,.045.378c0,.091.242.226.377.286s.33.165.436.211.151-.18.136-.421a.965.965,0,0,0-.331-.786,4.058,4.058,0,0,1-.5-.512,1.025,1.025,0,0,0-.466-.347,10.446,10.446,0,0,1-1.627-.844.93.93,0,0,1-.42-.875c0-.453.074-1.374.1-1.584a2.513,2.513,0,0,0,.091-.558c-.015-.2.089-.121.225.015a6.866,6.866,0,0,0,1.341.768,15.059,15.059,0,0,0,1.49.393c.407.076.721.121.812.152s.076.182.091.286.288.212.407.286a10.41,10.41,0,0,0,1.129.994,5.454,5.454,0,0,1,.888.618c.106.091.045.241.045.287s-.151.154-.437.228a4.716,4.716,0,0,1-.979.15,1.316,1.316,0,0,1-.706-.135c-.151-.076-.015-.211-.076-.3a.666.666,0,0,0-.6-.3.769.769,0,0,0-.5.165c-.076.03-.108.12.1.271s.409.378.528.408a1.088,1.088,0,0,1,.481.256c.15.15.347.423.617.378s.994-.317,1.235-.393a2.416,2.416,0,0,1,.511-.121.382.382,0,0,0,.437-.18c.165-.256.21-.3.225-.423s.119-.347-.106-.512a5.414,5.414,0,0,1-.663-1c-.119-.165-.451-.753-.451-.753a3.685,3.685,0,0,0,.5-.211c.227-.121.526-.438.648-.453a2.017,2.017,0,0,0,.542-.241,1.551,1.551,0,0,0,.513-.725,5,5,0,0,0,.316-1.3c.03-.378.059-.634.059-.634a.944.944,0,0,1,.166-.358,7.722,7.722,0,0,0,.672-1.034,1.452,1.452,0,0,1,.454-.613.846.846,0,0,1,.523-.143c.178.02.327.133.489.163s.778.421,1.006.48.46.143.5.242.034.232.143.232.42.05.44-.02.063-.117.143-.083.321-.069.341-.179-.04-.252.059-.322.178-.258.079-.361-.2-.189-.281-.282-.688-.929-.82-1.1A2.121,2.121,0,0,1,299.242,480.038Z" transform="translate(-282.537 -478.344)" fill="#fff"/>
    </clipPath>
  </defs>
  <g id="Group_6766" data-name="Group 6766" transform="translate(1332.343 -1776.481)">
    <g id="Group_6616" data-name="Group 6616" transform="translate(-1317.143 1784.724)">
      <path id="Path_1510" data-name="Path 1510" d="M495.874,642.09c-.056.106-.07.12-.155.162a.3.3,0,0,1-.12.021c-.049-.007.049-.028.1-.078S495.931,641.984,495.874,642.09Z" transform="translate(-495.586 -642.055)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6617" data-name="Group 6617" transform="translate(-1317.466 1785.018)">
      <path id="Path_1511" data-name="Path 1511" d="M488.45,648.934c-.014.028-.119.162-.183.176a.521.521,0,0,1-.177.036c-.1,0,.048-.015.113-.057s-.188.134-.113.014a.955.955,0,0,1,.156-.169C488.281,648.9,488.464,648.906,488.45,648.934Z" transform="translate(-488.057 -648.91)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6618" data-name="Group 6618" transform="translate(-1317.725 1785.044)">
      <path id="Path_1512" data-name="Path 1512" d="M482.35,649.522a.485.485,0,0,1-.148.148.322.322,0,0,1-.177.021c-.035,0,.027-.014.12-.07S482.385,649.459,482.35,649.522Z" transform="translate(-482.015 -649.502)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6619" data-name="Group 6619" transform="translate(-1319.007 1785.279)">
      <path id="Path_1513" data-name="Path 1513" d="M452.922,655a1.688,1.688,0,0,1-.424.1,1.1,1.1,0,0,1-.282-.035c-.07-.021-.134-.021.056-.028s.388-.028.508-.035A.562.562,0,0,1,452.922,655Z" transform="translate(-452.146 -655.002)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6620" data-name="Group 6620" transform="translate(-1319.363 1784.992)">
      <path id="Path_1514" data-name="Path 1514" d="M444.142,648.368c-.177-.007-.2-.07-.247-.063s-.113-.014.07.07a.491.491,0,0,0,.331.071C444.375,648.432,444.318,648.375,444.142,648.368Z" transform="translate(-443.842 -648.304)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6621" data-name="Group 6621" transform="translate(-1319.363 1784.54)">
      <path id="Path_1515" data-name="Path 1515" d="M443.865,637.783a.965.965,0,0,0,.3.191c.084.028.027-.008-.12-.107S443.809,637.72,443.865,637.783Z" transform="translate(-443.849 -637.755)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6622" data-name="Group 6622" transform="translate(-1318.915 1783.9)">
      <path id="Path_1516" data-name="Path 1516" d="M454.35,622.869c.091.085.162.121.176.17s-.041.021-.141-.063S454.258,622.784,454.35,622.869Z" transform="translate(-454.294 -622.838)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6623" data-name="Group 6623" transform="translate(-1319.437 1783.758)">
      <path id="Path_1517" data-name="Path 1517" d="M442.165,619.563c.106.12.185.135.283.255s-.141-.02-.219-.106S442.058,619.444,442.165,619.563Z" transform="translate(-442.117 -619.524)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6624" data-name="Group 6624" transform="translate(-1318.483 1783.855)">
      <path id="Path_1518" data-name="Path 1518" d="M464.456,621.814a1.568,1.568,0,0,1,.233.177c.056.056.036.049-.113-.007s-.184-.114-.2-.156S464.407,621.786,464.456,621.814Z" transform="translate(-464.367 -621.794)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6625" data-name="Group 6625" transform="translate(-1318.376 1784.082)">
      <path id="Path_1519" data-name="Path 1519" d="M466.869,627.083c.064.035.155,0,.226.07s.113.143-.028.092S466.805,627.048,466.869,627.083Z" transform="translate(-466.852 -627.078)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6626" data-name="Group 6626" transform="translate(-1318.57 1784.114)">
      <path id="Path_1520" data-name="Path 1520" d="M462.414,627.888a1.07,1.07,0,0,0,.247.205c.148.085-.022.035-.12-.014s-.12-.114-.183-.177S462.315,627.8,462.414,627.888Z" transform="translate(-462.321 -627.838)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6627" data-name="Group 6627" transform="translate(-1318.983 1783.566)">
      <path id="Path_1521" data-name="Path 1521" d="M452.843,615.11c.2.128.142.206-.021.085S452.646,614.982,452.843,615.11Z" transform="translate(-452.698 -615.048)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6628" data-name="Group 6628" transform="translate(-1318.686 1783.798)">
      <path id="Path_1522" data-name="Path 1522" d="M459.718,620.518c.106.121.049.085.113.149s.078.114-.056.028-.191-.233-.142-.191S459.612,620.4,459.718,620.518Z" transform="translate(-459.615 -620.47)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6634" data-name="Group 6634" transform="translate(-1320.833 1781.645)">
      <path id="Path_1532" data-name="Path 1532" d="M415.291,570.267a1.951,1.951,0,0,0-.866.551,4.865,4.865,0,0,1-.99.849,4.406,4.406,0,0,1-.968.549,1.544,1.544,0,0,1-.512-.076.781.781,0,0,1-.319-.294c-.023-.059-.008.075.062.149a.813.813,0,0,0,.355.216c.123.023.224.06.277.06s-.147.067-.224.067a1.491,1.491,0,0,1-.47-.276c-.085-.082-.069-.022.008.052a1.029,1.029,0,0,0,.17.134,1.252,1.252,0,0,1-.324-.149,2.257,2.257,0,0,0-.485-.194,4.451,4.451,0,0,0-.524-.074c-.093,0-.462.015-.532.03s.069.037.185.022a2.391,2.391,0,0,1,.409-.007c.161.007.239.112.177.082s-.4-.075-.254-.022.193.075.077.1a.868.868,0,0,0-.254.112.377.377,0,0,1-.27.03c-.1-.015-.008.045.1.075a.875.875,0,0,1,.254.119,2.818,2.818,0,0,0,.709.238c.154.007.146.045.3.045s.131.052-.077.06a1.92,1.92,0,0,1-.594-.052,2.2,2.2,0,0,0-.385-.112c-.092-.015-.108-.038.162.082s-.077.022-.193-.015a1.263,1.263,0,0,0-.347-.059c-.093,0,.069.022.177.045s.5.209.632.253a1.152,1.152,0,0,0,.254.045c.115.022-.309,0-.424-.037s-.447-.194-.57-.216a1.851,1.851,0,0,0-.208-.022c-.054-.008.078.045.185.059a2.074,2.074,0,0,1,.431.172,1.077,1.077,0,0,0,.316.112,1.389,1.389,0,0,1-.355-.022c-.115-.03-.339-.119-.462-.164s-.247-.022-.077.022a6.1,6.1,0,0,0,.694.216c.123,0,.323.015.161.074a1.276,1.276,0,0,1-.547-.022,1.76,1.76,0,0,1-.262-.157c-.085-.045-.093-.029.031.053a1.337,1.337,0,0,0,.555.193s.17-.015.046.022a2.631,2.631,0,0,0-.6.253,2.215,2.215,0,0,0-.331.3c-.046.074.047,0,.147-.09a2.221,2.221,0,0,1,.377-.268c.093-.045.27-.1.347-.126s.254-.052-.024.074a3.3,3.3,0,0,0-.655.387c.138-.059.386-.216.486-.246a1.871,1.871,0,0,1,.377-.126c.1,0,.215.082.308.082a3.748,3.748,0,0,0,.74.015,2.089,2.089,0,0,0,.663-.291c.108-.074.146-.023.038.067a1.639,1.639,0,0,1-.386.238.852.852,0,0,1-.385.053c-.17-.007-.462-.052-.277.015a.928.928,0,0,0,.339.067c.046,0-.131.074-.239.1a7.792,7.792,0,0,0-.786.179.885.885,0,0,0-.254.127c-.108.074-.024.082.046.03a.676.676,0,0,1,.278-.127c.131-.03.6-.1.809-.111a3.423,3.423,0,0,0,.724-.209,1.575,1.575,0,0,0,.455-.224c.085-.082.131-.089.085-.007s-.176.142-.238.194.131,0,.185-.06.17-.156.224-.231.308-.276.239-.127a1.716,1.716,0,0,1-.447.432,1.666,1.666,0,0,1-.462.2,1.254,1.254,0,0,1-.286.037c-.115.007.1.037.2.015a1.959,1.959,0,0,0,.493-.157c.162-.089.285-.171.285-.171a1.509,1.509,0,0,1-.224.208,1.1,1.1,0,0,1-.316.149c-.154.037-.024.059.092.015a.839.839,0,0,0,.339-.164,2.5,2.5,0,0,0,.285-.261c.046-.074.147-.29.239-.417a1.944,1.944,0,0,1,.308-.388.607.607,0,0,1,.216-.1c.038,0,.016.157-.069.276a3.41,3.41,0,0,1-.362.484,1.459,1.459,0,0,1-.262.171c-.092.053-.192.119-.115.1a.715.715,0,0,0,.262-.082c.077-.052.03.038-.054.112s-.223.186-.115.141.146-.014.285-.171.3-.439.409-.551.239-.321.308-.373.355-.066.447-.119a2.455,2.455,0,0,0,.354-.276c.038-.045-.077-.782-.008-1.036S415.291,570.267,415.291,570.267Z" transform="translate(-409.58 -570.267)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6635" data-name="Group 6635" transform="translate(-1319.641 1783.433)">
      <path id="Path_1533" data-name="Path 1533" d="M437.973,612.208a1.151,1.151,0,0,0-.4-.224c-.177-.052-.3-.06-.116.045a1.564,1.564,0,0,0,.362.179A.463.463,0,0,0,437.973,612.208Z" transform="translate(-437.365 -611.947)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6636" data-name="Group 6636" transform="translate(-1320.17 1783.604)">
      <path id="Path_1534" data-name="Path 1534" d="M426.368,616.246c.1.007-.362-.075-.493-.12a3.787,3.787,0,0,0-.424-.149,2.17,2.17,0,0,0-.331-.03c-.123,0-.131.03.054.052a1.526,1.526,0,0,1,.424.1,2.243,2.243,0,0,0,.408.157C426.129,616.268,426.268,616.239,426.368,616.246Z" transform="translate(-425.031 -615.948)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6637" data-name="Group 6637" transform="translate(-1319.656 1784.131)">
      <path id="Path_1535" data-name="Path 1535" d="M437.895,628.534c-.054-.015-.37-.172-.478-.224a1.621,1.621,0,0,0-.377-.089c-.085-.015.093.037.231.089s.293.157.37.186a.6.6,0,0,0,.254.082,1.213,1.213,0,0,0,.416-.082c.085-.045-.061-.015-.2.015S437.949,628.549,437.895,628.534Z" transform="translate(-437.018 -628.219)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6638" data-name="Group 6638" transform="translate(-1317.082 1783.618)">
      <path id="Path_1536" data-name="Path 1536" d="M497.522,616.336a2.015,2.015,0,0,0-.331.395c-.085.134-.115.193-.154.253s0,.015.146-.156a4.655,4.655,0,0,1,.354-.41C497.653,616.314,497.676,616.188,497.522,616.336Z" transform="translate(-497.023 -616.268)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6641" data-name="Group 6641" transform="translate(-1319.243 1779.787)">
      <path id="Path_1542" data-name="Path 1542" d="M447.654,526.952c-.052.013-.947.393-.947.393l-.065.144s.535-.19.653-.242l.558-.248Z" transform="translate(-446.642 -526.952)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6642" data-name="Group 6642" transform="translate(-1319.755 1780.018)">
      <path id="Path_1543" data-name="Path 1543" d="M436.71,532.343a3.806,3.806,0,0,1-.575.307,3.062,3.062,0,0,1-.705.131c-.163.013-.281.013-.281.013l-.059.124s.274-.039.366-.039a4.449,4.449,0,0,0,.653-.124,2.391,2.391,0,0,1,.248-.078s-.15.635-.3,1.053a2.056,2.056,0,0,1-.659.949,4.256,4.256,0,0,1-.5.216.546.546,0,0,1-.189-.013l-.013.131a.483.483,0,0,0,.2,0,4.624,4.624,0,0,0,.856-.412,6.176,6.176,0,0,0,.438-.883c.052-.144.268-1.027.32-1.093a2.683,2.683,0,0,1,.41-.258C436.983,532.328,436.71,532.343,436.71,532.343Z" transform="translate(-434.692 -532.34)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6643" data-name="Group 6643" transform="translate(-1326.282 1777.702)">
      <g id="Group_6593" data-name="Group 6593" transform="translate(0 0)">
        <g id="Group_6592" data-name="Group 6592" clip-path="url(#clip-path)">
          <g id="Group_6591" data-name="Group 6591">
            <g id="Group_6590" data-name="Group 6590">
              <g id="Group_6589" data-name="Group 6589" clip-path="url(#clip-path-2)">
                <g id="Group_6588" data-name="Group 6588" transform="translate(-10.21 -3.855)">
                  <path id="Path_1544" data-name="Path 1544" d="M300.62,478.344H282.536v13.384H300.62V478.344Z" transform="translate(-272.326 -474.489)" fill="#fff"/>
                  <ellipse id="Ellipse_65" data-name="Ellipse 65" cx="6.52" cy="8.46" rx="6.52" ry="8.46" transform="matrix(0.351, -0.936, 0.936, 0.351, 0, 12.21)" fill="#fff"/>
                  <ellipse id="Ellipse_66" data-name="Ellipse 66" cx="6.451" cy="8.37" rx="6.451" ry="8.37" transform="translate(0.108 12.177) rotate(-69.445)" fill="#fff"/>
                  <ellipse id="Ellipse_67" data-name="Ellipse 67" cx="6.381" cy="8.28" rx="6.381" ry="8.28" transform="translate(0.217 12.143) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_68" data-name="Ellipse 68" cx="6.312" cy="8.19" rx="6.312" ry="8.19" transform="translate(0.327 12.11) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_69" data-name="Ellipse 69" cx="6.242" cy="8.1" rx="6.242" ry="8.1" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.435, 12.077)" fill="#fff"/>
                  <ellipse id="Ellipse_70" data-name="Ellipse 70" cx="6.173" cy="8.01" rx="6.173" ry="8.01" transform="translate(0.543 12.043) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_71" data-name="Ellipse 71" cx="6.104" cy="7.92" rx="6.104" ry="7.92" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.652, 12.011)" fill="#fff"/>
                  <ellipse id="Ellipse_72" data-name="Ellipse 72" cx="6.034" cy="7.83" rx="6.034" ry="7.83" transform="translate(0.76 11.978) rotate(-69.445)" fill="#fff"/>
                  <ellipse id="Ellipse_73" data-name="Ellipse 73" cx="5.965" cy="7.74" rx="5.965" ry="7.74" transform="translate(0.87 11.944) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_74" data-name="Ellipse 74" cx="5.896" cy="7.65" rx="5.896" ry="7.65" transform="translate(0.978 11.911) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_75" data-name="Ellipse 75" cx="5.826" cy="7.56" rx="5.826" ry="7.56" transform="translate(1.086 11.878) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_76" data-name="Ellipse 76" cx="5.757" cy="7.47" rx="5.757" ry="7.47" transform="translate(1.195 11.844) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_77" data-name="Ellipse 77" cx="5.688" cy="7.38" rx="5.688" ry="7.38" transform="translate(1.303 11.811) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_78" data-name="Ellipse 78" cx="5.618" cy="7.29" rx="5.618" ry="7.29" transform="translate(1.413 11.779) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_79" data-name="Ellipse 79" cx="5.549" cy="7.2" rx="5.549" ry="7.2" transform="translate(1.521 11.743) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_80" data-name="Ellipse 80" cx="5.48" cy="7.11" rx="5.48" ry="7.11" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.63, 11.71)" fill="#fff"/>
                  <ellipse id="Ellipse_81" data-name="Ellipse 81" cx="5.41" cy="7.02" rx="5.41" ry="7.02" transform="translate(1.738 11.676) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_82" data-name="Ellipse 82" cx="5.341" cy="6.93" rx="5.341" ry="6.93" transform="translate(1.846 11.643) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_83" data-name="Ellipse 83" cx="5.271" cy="6.84" rx="5.271" ry="6.84" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.956, 11.611)" fill="#fff"/>
                  <ellipse id="Ellipse_84" data-name="Ellipse 84" cx="5.202" cy="6.75" rx="5.202" ry="6.75" transform="translate(2.065 11.577) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_85" data-name="Ellipse 85" cx="5.133" cy="6.66" rx="5.133" ry="6.66" transform="translate(2.173 11.544) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_86" data-name="Ellipse 86" cx="5.063" cy="6.57" rx="5.063" ry="6.57" transform="translate(2.281 11.511) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_87" data-name="Ellipse 87" cx="4.994" cy="6.48" rx="4.994" ry="6.48" transform="translate(2.39 11.477) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_88" data-name="Ellipse 88" cx="4.925" cy="6.39" rx="4.925" ry="6.39" transform="translate(2.499 11.444) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_89" data-name="Ellipse 89" cx="4.855" cy="6.3" rx="4.855" ry="6.3" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.608, 11.411)" fill="#fff"/>
                  <ellipse id="Ellipse_90" data-name="Ellipse 90" cx="4.786" cy="6.21" rx="4.786" ry="6.21" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.716, 11.377)" fill="#fff"/>
                  <ellipse id="Ellipse_91" data-name="Ellipse 91" cx="4.716" cy="6.12" rx="4.716" ry="6.12" transform="translate(2.825 11.344) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_92" data-name="Ellipse 92" cx="4.647" cy="6.03" rx="4.647" ry="6.03" transform="translate(2.933 11.31) rotate(-69.444)" fill="#fff"/>
                  <ellipse id="Ellipse_93" data-name="Ellipse 93" cx="4.578" cy="5.94" rx="4.578" ry="5.94" transform="translate(3.043 11.276) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_94" data-name="Ellipse 94" cx="4.508" cy="5.85" rx="4.508" ry="5.85" transform="translate(3.151 11.244) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_95" data-name="Ellipse 95" cx="4.439" cy="5.76" rx="4.439" ry="5.76" transform="translate(3.259 11.21) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_96" data-name="Ellipse 96" cx="4.37" cy="5.67" rx="4.37" ry="5.67" transform="translate(3.368 11.177) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_97" data-name="Ellipse 97" cx="4.3" cy="5.58" rx="4.3" ry="5.58" transform="translate(3.476 11.144) rotate(-69.445)" fill="#fff"/>
                  <ellipse id="Ellipse_98" data-name="Ellipse 98" cx="4.231" cy="5.49" rx="4.231" ry="5.49" transform="translate(3.586 11.11) rotate(-69.449)" fill="#fff"/>
                  <ellipse id="Ellipse_99" data-name="Ellipse 99" cx="4.162" cy="5.4" rx="4.162" ry="5.4" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.694, 11.077)" fill="#fff"/>
                  <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="4.092" cy="5.31" rx="4.092" ry="5.31" transform="translate(3.803 11.044) rotate(-69.446)" fill="#fff"/>
                  <path id="Path_1545" data-name="Path 1545" d="M163.947,505.861c0-2.28,2.278-3.807,5.088-3.412s5.088,2.563,5.088,4.843-2.278,3.807-5.088,3.412S163.947,508.141,163.947,505.861Z" transform="translate(-158.824 -497.5)" fill="#fff"/>
                  <ellipse id="Ellipse_101" data-name="Ellipse 101" cx="3.954" cy="5.13" rx="3.954" ry="5.13" transform="translate(4.019 10.977) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_102" data-name="Ellipse 102" cx="3.884" cy="5.04" rx="3.884" ry="5.04" transform="translate(4.129 10.945) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_103" data-name="Ellipse 103" cx="3.815" cy="4.95" rx="3.815" ry="4.95" transform="translate(4.238 10.911) rotate(-69.449)" fill="#fff"/>
                  <ellipse id="Ellipse_104" data-name="Ellipse 104" cx="3.745" cy="4.86" rx="3.745" ry="4.86" transform="translate(4.346 10.878) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_105" data-name="Ellipse 105" cx="3.676" cy="4.77" rx="3.676" ry="4.77" transform="translate(4.454 10.842) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_106" data-name="Ellipse 106" cx="3.607" cy="4.68" rx="3.607" ry="4.68" transform="translate(4.563 10.81) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_107" data-name="Ellipse 107" cx="3.537" cy="4.59" rx="3.537" ry="4.59" transform="translate(4.672 10.777) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_108" data-name="Ellipse 108" cx="3.468" cy="4.5" rx="3.468" ry="4.5" transform="translate(4.781 10.743) rotate(-69.449)" fill="#fff"/>
                  <path id="Path_1546" data-name="Path 1546" d="M182.362,520.476c0-1.926,1.925-3.217,4.3-2.883s4.3,2.165,4.3,4.091-1.925,3.217-4.3,2.883S182.362,522.4,182.362,520.476Z" transform="translate(-176.449 -512.004)" fill="#fff"/>
                  <ellipse id="Ellipse_109" data-name="Ellipse 109" cx="3.329" cy="4.32" rx="3.329" ry="4.32" transform="translate(4.997 10.677) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_110" data-name="Ellipse 110" cx="3.26" cy="4.23" rx="3.26" ry="4.23" transform="matrix(0.351, -0.936, 0.936, 0.351, 5.106, 10.643)" fill="#fff"/>
                  <ellipse id="Ellipse_111" data-name="Ellipse 111" cx="3.191" cy="4.14" rx="3.191" ry="4.14" transform="translate(5.214 10.61) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_112" data-name="Ellipse 112" cx="3.121" cy="4.05" rx="3.121" ry="4.05" transform="translate(5.324 10.578) rotate(-69.447)" fill="#fff"/>
                  <path id="Path_1547" data-name="Path 1547" d="M192.59,528.6c0-1.729,1.728-2.888,3.86-2.589s3.86,1.944,3.86,3.674-1.728,2.888-3.86,2.589S192.59,530.331,192.59,528.6Z" transform="translate(-186.238 -520.068)" fill="#fff"/>
                  <ellipse id="Ellipse_113" data-name="Ellipse 113" cx="2.982" cy="3.87" rx="2.982" ry="3.87" transform="translate(5.541 10.511) rotate(-69.448)" fill="#fff"/>
                  <path id="Path_1548" data-name="Path 1548" d="M196.668,531.879c0-1.651,1.65-2.757,3.684-2.471s3.684,1.856,3.684,3.507-1.65,2.757-3.684,2.471S196.668,533.53,196.668,531.879Z" transform="translate(-190.141 -523.32)" fill="#fff"/>
                  <ellipse id="Ellipse_114" data-name="Ellipse 114" cx="2.844" cy="3.69" rx="2.844" ry="3.69" transform="translate(5.757 10.444) rotate(-69.448)" fill="#fff"/>
                  <path id="Path_1549" data-name="Path 1549" d="M200.778,535.09c0-1.572,1.571-2.626,3.509-2.353s3.509,1.768,3.509,3.34-1.571,2.626-3.509,2.353S200.778,536.662,200.778,535.09Z" transform="translate(-194.075 -526.508)" fill="#fff"/>
                  <ellipse id="Ellipse_115" data-name="Ellipse 115" cx="2.705" cy="3.51" rx="2.705" ry="3.51" transform="translate(5.976 10.376) rotate(-69.449)" fill="#fff"/>
                  <path id="Path_1550" data-name="Path 1550" d="M204.856,538.334c0-1.494,1.493-2.495,3.334-2.236s3.333,1.679,3.333,3.173-1.493,2.494-3.334,2.236S204.856,539.827,204.856,538.334Z" transform="translate(-197.978 -529.727)" fill="#fff"/>
                  <ellipse id="Ellipse_116" data-name="Ellipse 116" cx="2.566" cy="3.33" rx="2.566" ry="3.33" transform="translate(6.192 10.31) rotate(-69.445)" fill="#fff"/>
                  <path id="Path_1551" data-name="Path 1551" d="M208.934,541.577c0-1.415,1.414-2.363,3.158-2.118s3.158,1.591,3.158,3.006-1.414,2.363-3.158,2.118S208.934,542.992,208.934,541.577Z" transform="translate(-201.881 -532.945)" fill="#fff"/>
                  <ellipse id="Ellipse_117" data-name="Ellipse 117" cx="2.428" cy="3.15" rx="2.428" ry="3.15" transform="translate(6.41 10.243) rotate(-69.449)" fill="#fff"/>
                  <ellipse id="Ellipse_118" data-name="Ellipse 118" cx="2.358" cy="3.06" rx="2.358" ry="3.06" transform="translate(6.519 10.21) rotate(-69.447)" fill="#fff"/>
                  <path id="Path_1552" data-name="Path 1552" d="M215.084,546.459c0-1.3,1.3-2.166,2.895-1.942a3.172,3.172,0,0,1,2.895,2.755c0,1.3-1.3,2.166-2.9,1.942A3.172,3.172,0,0,1,215.084,546.459Z" transform="translate(-207.767 -537.79)" fill="#fff"/>
                  <ellipse id="Ellipse_119" data-name="Ellipse 119" cx="2.22" cy="2.88" rx="2.22" ry="2.88" transform="translate(6.736 10.144) rotate(-69.448)" fill="#fff"/>
                  <path id="Path_1553" data-name="Path 1553" d="M219.162,549.737c0-1.218,1.218-2.035,2.719-1.824A2.98,2.98,0,0,1,224.6,550.5c0,1.218-1.218,2.035-2.72,1.824A2.98,2.98,0,0,1,219.162,549.737Z" transform="translate(-211.67 -541.042)" fill="#fff"/>
                  <ellipse id="Ellipse_120" data-name="Ellipse 120" cx="2.081" cy="2.7" rx="2.081" ry="2.7" transform="translate(6.954 10.077) rotate(-69.452)" fill="#fff"/>
                  <ellipse id="Ellipse_121" data-name="Ellipse 121" cx="2.011" cy="2.61" rx="2.011" ry="2.61" transform="matrix(0.351, -0.936, 0.936, 0.351, 7.062, 10.044)" fill="#fff"/>
                  <ellipse id="Ellipse_122" data-name="Ellipse 122" cx="1.942" cy="2.52" rx="1.942" ry="2.52" transform="translate(7.17 10.011) rotate(-69.447)" fill="#fff"/>
                  <path id="Path_1554" data-name="Path 1554" d="M227.35,556.223c0-1.061,1.061-1.772,2.369-1.589a2.6,2.6,0,0,1,2.368,2.254c0,1.061-1.061,1.772-2.369,1.588A2.6,2.6,0,0,1,227.35,556.223Z" transform="translate(-219.507 -547.48)" fill="#fff"/>
                  <ellipse id="Ellipse_123" data-name="Ellipse 123" cx="1.803" cy="2.34" rx="1.803" ry="2.34" transform="translate(7.387 9.943) rotate(-69.445)" fill="#fff"/>
                  <path id="Path_1555" data-name="Path 1555" d="M231.46,559.435c0-.983.982-1.641,2.193-1.471a2.4,2.4,0,0,1,2.193,2.087c0,.983-.982,1.641-2.193,1.471A2.4,2.4,0,0,1,231.46,559.435Z" transform="translate(-223.441 -550.668)" fill="#fff"/>
                  <ellipse id="Ellipse_124" data-name="Ellipse 124" cx="1.665" cy="2.16" rx="1.665" ry="2.16" transform="translate(7.605 9.876) rotate(-69.45)" fill="#fff"/>
                  <ellipse id="Ellipse_125" data-name="Ellipse 125" cx="1.595" cy="2.07" rx="1.595" ry="2.07" transform="matrix(0.351, -0.936, 0.936, 0.351, 7.714, 9.843)" fill="#fff"/>
                  <path id="Path_1556" data-name="Path 1556" d="M237.578,564.317c0-.865.864-1.444,1.93-1.294a2.115,2.115,0,0,1,1.93,1.837c0,.865-.864,1.444-1.93,1.294A2.115,2.115,0,0,1,237.578,564.317Z" transform="translate(-229.296 -555.513)" fill="#fff"/>
                  <path id="Path_1557" data-name="Path 1557" d="M239.617,565.956c0-.825.825-1.378,1.842-1.235a2.019,2.019,0,0,1,1.842,1.753c0,.825-.825,1.378-1.842,1.235A2.019,2.019,0,0,1,239.617,565.956Z" transform="translate(-231.248 -557.139)" fill="#fff"/>
                  <path id="Path_1558" data-name="Path 1558" d="M241.688,567.595c0-.786.786-1.313,1.755-1.177a1.922,1.922,0,0,1,1.754,1.67c0,.786-.786,1.313-1.755,1.177A1.922,1.922,0,0,1,241.688,567.595Z" transform="translate(-233.23 -558.765)" fill="#fff"/>
                  <path id="Path_1559" data-name="Path 1559" d="M243.727,569.2c0-.747.746-1.247,1.667-1.118a1.826,1.826,0,0,1,1.667,1.586c0,.747-.746,1.247-1.667,1.118A1.826,1.826,0,0,1,243.727,569.2Z" transform="translate(-235.181 -560.358)" fill="#fff"/>
                  <ellipse id="Ellipse_126" data-name="Ellipse 126" cx="1.248" cy="1.62" rx="1.248" ry="1.62" transform="translate(8.257 9.677) rotate(-69.451)" fill="#fff"/>
                  <path id="Path_1560" data-name="Path 1560" d="M247.805,572.477c0-.668.668-1.116,1.491-1a1.634,1.634,0,0,1,1.491,1.419c0,.668-.668,1.116-1.491,1A1.634,1.634,0,0,1,247.805,572.477Z" transform="translate(-239.085 -563.61)" fill="#fff"/>
                  <ellipse id="Ellipse_127" data-name="Ellipse 127" cx="1.11" cy="1.44" rx="1.11" ry="1.44" transform="translate(8.474 9.61) rotate(-69.453)" fill="#fff"/>
                  <path id="Path_1561" data-name="Path 1561" d="M251.915,575.72c0-.589.589-.984,1.316-.882a1.442,1.442,0,0,1,1.316,1.252c0,.59-.589.985-1.316.882A1.442,1.442,0,0,1,251.915,575.72Z" transform="translate(-243.018 -566.828)" fill="#fff"/>
                  <path id="Path_1562" data-name="Path 1562" d="M253.954,577.359c0-.55.55-.919,1.228-.823a1.346,1.346,0,0,1,1.228,1.169c0,.55-.55.919-1.228.824A1.346,1.346,0,0,1,253.954,577.359Z" transform="translate(-244.97 -568.454)" fill="#fff"/>
                  <path id="Path_1563" data-name="Path 1563" d="M255.993,578.931c0-.511.511-.853,1.141-.765a1.25,1.25,0,0,1,1.14,1.086c0,.511-.511.853-1.141.765A1.25,1.25,0,0,1,255.993,578.931Z" transform="translate(-246.921 -570.016)" fill="#fff"/>
                  <path id="Path_1564" data-name="Path 1564" d="M258.033,580.571c0-.472.471-.788,1.053-.706a1.153,1.153,0,0,1,1.053,1c0,.472-.471.788-1.053.706A1.154,1.154,0,0,1,258.033,580.571Z" transform="translate(-248.874 -571.643)" fill="#fff"/>
                  <path id="Path_1565" data-name="Path 1565" d="M260.072,582.174c0-.432.432-.722.965-.647a1.057,1.057,0,0,1,.965.918c0,.432-.432.722-.965.647A1.057,1.057,0,0,1,260.072,582.174Z" transform="translate(-250.825 -573.235)" fill="#fff"/>
                  <path id="Path_1566" data-name="Path 1566" d="M262.143,583.814c0-.393.393-.656.877-.588a.961.961,0,0,1,.877.835c0,.393-.393.656-.877.588A.961.961,0,0,1,262.143,583.814Z" transform="translate(-252.807 -574.861)" fill="#fff"/>
                  <path id="Path_1567" data-name="Path 1567" d="M264.182,585.452c0-.354.354-.591.79-.529a.865.865,0,0,1,.789.751c0,.354-.354.591-.79.529A.865.865,0,0,1,264.182,585.452Z" transform="translate(-254.759 -576.487)" fill="#fff"/>
                  <path id="Path_1568" data-name="Path 1568" d="M266.221,587.057c0-.314.314-.525.7-.471a.769.769,0,0,1,.7.668c0,.314-.314.525-.7.471A.769.769,0,0,1,266.221,587.057Z" transform="translate(-256.711 -578.08)" fill="#fff"/>
                  <path id="Path_1569" data-name="Path 1569" d="M268.26,588.7c0-.275.275-.459.614-.412a.673.673,0,0,1,.614.584c0,.275-.275.459-.614.412A.673.673,0,0,1,268.26,588.7Z" transform="translate(-258.662 -579.706)" fill="#fff"/>
                  <path id="Path_1570" data-name="Path 1570" d="M270.3,590.334c0-.236.236-.394.526-.353a.577.577,0,0,1,.526.5c0,.236-.236.394-.526.353A.577.577,0,0,1,270.3,590.334Z" transform="translate(-260.614 -581.331)" fill="#fff"/>
                  <path id="Path_1571" data-name="Path 1571" d="M272.37,591.939c0-.2.2-.328.439-.294a.481.481,0,0,1,.439.417c0,.2-.2.328-.439.294A.481.481,0,0,1,272.37,591.939Z" transform="translate(-262.596 -582.925)" fill="#fff"/>
                  <path id="Path_1572" data-name="Path 1572" d="M274.41,593.577c0-.157.157-.263.351-.235a.385.385,0,0,1,.351.334c0,.157-.157.262-.351.235A.384.384,0,0,1,274.41,593.577Z" transform="translate(-264.548 -584.55)" fill="#fff"/>
                  <path id="Path_1573" data-name="Path 1573" d="M276.449,595.217c0-.118.118-.2.263-.176a.288.288,0,0,1,.263.25c0,.118-.118.2-.263.176A.288.288,0,0,1,276.449,595.217Z" transform="translate(-266.5 -586.177)" fill="#fff"/>
                  <path id="Path_1574" data-name="Path 1574" d="M278.488,596.821c0-.079.079-.131.176-.118a.192.192,0,0,1,.175.167c0,.079-.079.131-.176.118A.192.192,0,0,1,278.488,596.821Z" transform="translate(-268.451 -587.769)" fill="#fff"/>
                  <path id="Path_1575" data-name="Path 1575" d="M280.527,598.46c0-.039.039-.065.088-.059a.1.1,0,0,1,.088.083c0,.039-.039.065-.088.059A.1.1,0,0,1,280.527,598.46Z" transform="translate(-270.403 -589.396)" fill="#fff"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_6644" data-name="Group 6644" transform="translate(-1315.968 1785.566)">
      <path id="Path_1578" data-name="Path 1578" d="M524.939,661.672a2.733,2.733,0,0,0,.855.507c.49.205.807.369.935.414a.578.578,0,0,1,.365.347,1.614,1.614,0,0,1,.189.732,11.686,11.686,0,0,0-.125,1.46,1.873,1.873,0,0,1-.1.748,5.52,5.52,0,0,0-.3.857c0,.112-.029.16-.205.4s-.192.379-.4.109a1,1,0,0,1-.221-.777c.045-.208.218-.189.346-.237a.332.332,0,0,0,.16-.43.891.891,0,0,1,.048-.523c.032-.128.109-1.162.109-1.415a.671.671,0,0,0-.141-.491,1.015,1.015,0,0,0-.57-.238c-.16,0-.714-.048-.967-.048s-.794-.1-1.015-.1a1.682,1.682,0,0,1-.743-.205c-.3-.144-.205-.077.064-.221S524.939,661.672,524.939,661.672Z" transform="translate(-522.978 -661.672)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6645" data-name="Group 6645" transform="translate(-1315.349 1778.278)">
      <path id="Path_1579" data-name="Path 1579" d="M540.482,491.961a.454.454,0,0,0-.263-.14c-.131-.038-.358-.057-.311,0s.045.15-.1.15-.3-.019-.3.047a.293.293,0,0,1-.208.254c-.14.028-.434.18-.273.19s-.112.1-.254.1-.239.006-.36.006-.738-.11-.625-.053.058.122-.046.094-.318-.038-.187.019.117.151.023.189-.205-.047-.113,0,.321.19.188.219-.283.082-.141.139.187.2.235.264.087.142.208.085,1.534-.6,1.711-.689a8.877,8.877,0,0,0,.762-.81C540.47,492.008,540.555,492.044,540.482,491.961Z" transform="translate(-537.426 -491.785)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6646" data-name="Group 6646" transform="translate(-1320.245 1780.436)">
      <path id="Path_1580" data-name="Path 1580" d="M423.821,542.124a4.134,4.134,0,0,0-.443-.036c-.088-.024-.14.031-.076.107a1.04,1.04,0,0,0,.413.29c.166.047,1.027.195,1.121.195s.756,0,1.093-.024.242-.361.242-.361S424.01,542.172,423.821,542.124Z" transform="translate(-423.273 -542.083)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6647" data-name="Group 6647" transform="translate(-1320.146 1778.225)">
      <path id="Path_1581" data-name="Path 1581" d="M426.515,490.563c.059.036.115.288.274.43s.354.254.365.337.1.076.266.136,1.235.361,1.465.456.666.261.726.408a.713.713,0,0,1-.135.68c-.189.178-.348.137-.419.214a1.454,1.454,0,0,1-.348.183l-.5-.243s-.163-.133-.765-.122a2.125,2.125,0,0,1-1.316-.37c-.26-.163-.567-.378-.543-.763a1.16,1.16,0,0,1,.3-.82c.206-.225.256-.28.363-.4S426.456,490.527,426.515,490.563Z" transform="translate(-425.583 -490.551)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6648" data-name="Group 6648" transform="translate(-1319.494 1777.518)">
      <path id="Path_1582" data-name="Path 1582" d="M444.853,474.115a1.371,1.371,0,0,1,.32.782c.038.444.541.326.373.362s-.693.046-.664.11a1.141,1.141,0,0,1,.019.461c-.028.169-.14.209.028.349s.168.226.254.188a5.182,5.182,0,0,0,.574-.413c.149-.123.237-.235.367-.321s.112-.093.216-.01a.911.911,0,0,1,.187.283c.047.074.106.064-.064.207s-.282.332-.415.463a7.376,7.376,0,0,1-.659.639c-.121.066-.327.275-.46.228a1.522,1.522,0,0,1-.443-.444c-.057-.095-.187-.366-.216-.432a2.551,2.551,0,0,1-.171-.311c-.036-.114-.093-.123-.206-.1a1,1,0,0,1-.348.029c-.15-.019-1.782-.277-1.914-.334a.72.72,0,0,1-.286-.294,3.875,3.875,0,0,0-.532-.48c-.1-.076.049-.22.248-.362a5.284,5.284,0,0,1,1.413-.546,8.013,8.013,0,0,1,1.223-.095c.225.01.725.01.884.029S444.815,474.087,444.853,474.115Z" transform="translate(-440.778 -474.066)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6649" data-name="Group 6649" transform="translate(-1319.733 1780.549)">
      <path id="Path_1583" data-name="Path 1583" d="M437.732,544.733s-.318.114-.578.225-.673.409-1,.6a1.4,1.4,0,0,0-.479.425c-.064.083-.166.171-.289.3a.725.725,0,0,0-.171.385c0,.047.076.066.183.119s.277.183.325.147.09.012.2.059.354.29.46.3.484.024.567.024.064-.147-.071-.195a.5.5,0,0,1-.33-.273.823.823,0,0,1-.095-.437c.024-.107.166-.171.3-.254s.643-.3.82-.4a4.611,4.611,0,0,1,.507-.219c.095-.036-.017-.249-.147-.492A1.79,1.79,0,0,0,437.732,544.733Z" transform="translate(-435.217 -544.733)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6650" data-name="Group 6650" transform="translate(-1332.343 1781.495)">
      <path id="Path_1584" data-name="Path 1584" d="M147.8,566.762a2.232,2.232,0,0,0-1,.656,5.654,5.654,0,0,1-1.138,1.011,5.021,5.021,0,0,1-1.112.654,1.717,1.717,0,0,1-.589-.09.911.911,0,0,1-.367-.35c-.027-.071-.009.089.071.178a.929.929,0,0,0,.408.257c.141.027.257.071.319.071s-.169.08-.257.08a1.7,1.7,0,0,1-.54-.328c-.1-.1-.08-.027.009.062a1.2,1.2,0,0,0,.2.16,1.413,1.413,0,0,1-.372-.178,2.542,2.542,0,0,0-.557-.231,4.948,4.948,0,0,0-.6-.089c-.107,0-.531.018-.611.035s.079.044.212.027a2.663,2.663,0,0,1,.47-.009c.186.009.275.133.2.1s-.46-.089-.292-.027.222.089.088.125a.984.984,0,0,0-.292.133.419.419,0,0,1-.31.035c-.116-.018-.009.054.115.089a.991.991,0,0,1,.292.142,3.163,3.163,0,0,0,.815.284c.177.009.168.053.345.053s.151.061-.088.071a2.133,2.133,0,0,1-.682-.062,2.463,2.463,0,0,0-.443-.133c-.106-.018-.124-.045.186.1s-.088.026-.222-.018a1.4,1.4,0,0,0-.4-.071c-.107,0,.079.027.2.053s.576.249.727.3a1.281,1.281,0,0,0,.292.053c.133.027-.355,0-.487-.044s-.513-.231-.655-.257a2.066,2.066,0,0,0-.239-.027c-.062-.009.089.053.213.071a2.331,2.331,0,0,1,.5.2,1.207,1.207,0,0,0,.364.133,1.535,1.535,0,0,1-.408-.027c-.133-.035-.39-.142-.532-.195s-.283-.027-.088.027a6.841,6.841,0,0,0,.8.257c.141,0,.371.018.186.089a1.418,1.418,0,0,1-.629-.027,2.008,2.008,0,0,1-.3-.187c-.1-.053-.107-.035.035.063a1.5,1.5,0,0,0,.638.23s.2-.018.053.027a2.967,2.967,0,0,0-.691.3,2.581,2.581,0,0,0-.381.355c-.053.089.054,0,.169-.107a2.543,2.543,0,0,1,.434-.319c.107-.053.311-.115.4-.151s.292-.062-.027.089a3.765,3.765,0,0,0-.752.461c.159-.071.443-.257.558-.293a2.091,2.091,0,0,1,.434-.151c.116,0,.247.1.354.1a4.152,4.152,0,0,0,.85.018,2.361,2.361,0,0,0,.762-.346c.124-.089.168-.027.044.08a1.876,1.876,0,0,1-.443.284.947.947,0,0,1-.442.063c-.2-.009-.532-.062-.319.018a1.033,1.033,0,0,0,.389.08c.053,0-.151.089-.275.115a8.711,8.711,0,0,0-.9.213,1,1,0,0,0-.292.151c-.124.089-.027.1.053.035a.764.764,0,0,1,.319-.151c.15-.035.691-.115.93-.133a3.824,3.824,0,0,0,.832-.249,1.789,1.789,0,0,0,.523-.266c.1-.1.15-.106.1-.009s-.2.169-.274.231.15,0,.212-.071.2-.186.257-.275.354-.329.275-.151a2,2,0,0,1-.514.515,1.879,1.879,0,0,1-.531.24,1.391,1.391,0,0,1-.328.044c-.133.009.115.044.23.018a2.2,2.2,0,0,0,.567-.187c.186-.106.328-.2.328-.2a1.759,1.759,0,0,1-.257.248,1.249,1.249,0,0,1-.363.178c-.177.044-.027.071.106.018a.95.95,0,0,0,.39-.2,2.913,2.913,0,0,0,.328-.311c.053-.089.169-.345.275-.5a2.3,2.3,0,0,1,.354-.462.686.686,0,0,1,.248-.124c.044,0,.018.187-.08.328a4.049,4.049,0,0,1-.417.577,1.667,1.667,0,0,1-.3.2c-.106.063-.221.142-.133.125a.8.8,0,0,0,.3-.1c.088-.062.035.045-.062.133s-.256.221-.133.168.168-.017.328-.2.346-.523.47-.656.275-.382.354-.444.408-.079.514-.142a2.84,2.84,0,0,0,.407-.328c.044-.054-.088-.932-.009-1.233S147.8,566.762,147.8,566.762Z" transform="translate(-141.241 -566.762)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6651" data-name="Group 6651" transform="translate(-1328.032 1783.844)">
      <path id="Path_1588" data-name="Path 1588" d="M242.312,621.629a2.381,2.381,0,0,0-.381.47c-.1.16-.132.23-.177.3s0,.018.168-.186a5.5,5.5,0,0,1,.407-.488C242.463,621.6,242.489,621.451,242.312,621.629Z" transform="translate(-241.738 -621.547)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6652" data-name="Group 6652" transform="translate(-1327.156 1784.02)">
      <path id="Path_1589" data-name="Path 1589" d="M262.643,625.7c-.169.116-.571.247-.443.213a.931.931,0,0,0,.39-.124C262.811,625.66,262.811,625.58,262.643,625.7Z" transform="translate(-262.175 -625.64)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6653" data-name="Group 6653" transform="translate(-1322.267 1784.643)">
      <path id="Path_1590" data-name="Path 1590" d="M377.231,640.894c0,.326.107,1.689.107,2.088a2.468,2.468,0,0,0,.519,1.3c.2.18,1.948,1.547,2.19,1.654a1.879,1.879,0,0,1,.879.579c.267.36.155.3-.253.3a3.177,3.177,0,0,1-.707-.019c-.062-.037-.146-.142-.115-.224a.163.163,0,0,0-.047-.182,2.245,2.245,0,0,0-.192-.14c-.036-.021-3.25-2.584-3.407-2.693a10.983,10.983,0,0,1,.032-2.365c.016-.507.032-.87.032-.966s.013-.1.189.077A8.9,8.9,0,0,0,377.231,640.894Z" transform="translate(-376.141 -640.165)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6654" data-name="Group 6654" transform="translate(-1314.206 1780.837)">
      <path id="Path_1591" data-name="Path 1591" d="M569.056,552.271s-1.626-.25-1.9-.3-1.867-.319-2.144-.358-.465-.094-.687-.134-.253-.047-.253-.047l-.008.11s.078.029.316.1.823.144,1.029.191.95.183,1.251.23,1.28.159,1.416.2.619.118.7.126.278.019.278.019Z" transform="translate(-564.061 -551.435)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6655" data-name="Group 6655" transform="translate(-1315.473 1777.553)">
      <path id="Path_1592" data-name="Path 1592" d="M536.1,474.95a.313.313,0,0,1-.067.23c-.046.03-.046.048,0,.153s.062.154.079.2,0,.09-.078.079-.117-.008-.119.056,0,.094-.033.1-.054.025-.031.055-.015.044-.035.076,0,.1,0,.126a.1.1,0,0,1-.105.076c-.065-.008-.226-.032-.277-.033a3.258,3.258,0,0,0-.33-.006c-.047.011-.227-.24-.342-.35a1.344,1.344,0,0,1-.2-.433c-.121-.252.031-.163.143-.235a.446.446,0,0,1,.384-.089c.167.04.427-.07.585-.071A2.319,2.319,0,0,1,536.1,474.95Z" transform="translate(-534.519 -474.885)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6656" data-name="Group 6656" transform="translate(-1315.754 1776.481)">
      <path id="Path_1593" data-name="Path 1593" d="M529.753,450.248a1.082,1.082,0,0,0-.748-.354,1.049,1.049,0,0,0-.527.148.839.839,0,0,0-.342.447c-.072.2-.021.254-.092.407s-.091.134.042.219.177.158.26.09a.642.642,0,0,1,.364-.145.587.587,0,0,0,.328.02,2.172,2.172,0,0,1,.6-.015c.1.014.3.019.326.027a1.7,1.7,0,0,0-.031-.469A.776.776,0,0,0,529.753,450.248Z" transform="translate(-527.988 -449.893)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6657" data-name="Group 6657" transform="translate(-1314.308 1776.999)">
      <path id="Path_1594" data-name="Path 1594" d="M562.237,461.992a.574.574,0,0,1,.066.337c-.026.112-.054.106-.124.1a1.826,1.826,0,0,1-.381-.073c-.076-.039-.153-.15-.06-.235a.591.591,0,0,1,.316-.151C562.152,461.956,562.205,461.946,562.237,461.992Z" transform="translate(-561.695 -461.958)" fill="#fff" fill-rule="evenodd"/>
    </g>
  </g>
</svg>
