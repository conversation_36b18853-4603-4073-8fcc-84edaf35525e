import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Typography,
  Breadcrumbs,
  Button,
  Checkbox,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";

import { Editor } from "react-draft-wysiwyg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import { Loader, ActionMessage } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import CreateBookkeeper from "../CreateBookkeeper";
import { config } from "../../../helpers/config";
import Pagination from "@mui/material/Pagination";
import {
  EditorState,
  convertToRaw,
  ContentState,
  AtomicBlockUtils,
  convertFromHTML,
} from "draft-js";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import ImageUploader from "../ImageUploader";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";
import SearchIcons from "../../../images/searchIcon.svg";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../../library/common/components/Button";
import ReviewTableModal from "../ReviewTableModal";
import { ReactComponent as Unchecked } from "../../../images/uncheck-star.svg";
import { ReactComponent as Checked } from "../../../images/check-star.svg";
import Select from "react-select";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import "./review.scss";
import { setReviewCount } from "../../../store/common/actions/review.actions";
import { connect } from "react-redux";

const featureBookMakerOption = [
  {
    label: "All",
    value: 0,
  },

  {
    label: "Featured",
    value: 1,
  },
];

const newsStatusOption = [
  {
    label: "All",
    value: 0,
  },
  {
    label: "draft",
    value: 1,
  },

  {
    label: "accepted",
    value: 2,
  },
  {
    label: "rejected",
    value: 3,
  },
];

const newsModalStatusOption = [
  {
    label: "accepted",
    value: 0,
  },
  {
    label: "draft",
    value: 1,
  },
  {
    label: "rejected",
    value: 2,
  },
];
class Review extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      bookkeeper: [],
      allApiProvider: [],
      isInputModalOpen: false,
      idToSend: null,
      serachValue: "",
      isEditMode: false,
      isCMSModalOpen: false,
      isReviewModalOpen: false,
      isAddToCMSInfoMode: false,
      isDetailsAvailable: false,

      cmsData: {
        id: "",
        details: "",
      },
      uploadImage: "",
      authorUploadImage: "",
      editorState: EditorState.createEmpty(),
      image: [],
      authorImage: [],

      bookKeeperId: null,
      selectedBookkeeperIds: [],
      draggedItem: null,
      isSortChange: false,
      checkBoxValues: [],
      selectedFeatureBookMaker: null,
      content: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      reviewsData: [],
      reviewsCount: 0,
      isLoading: false,
      isBookkeeperLoading: false,
      selectedReviewStatus: "draft",
      selectedModalNewsStatus: null,
      currentPage: 1,
      rowPerPage: 20,
      totalCount: 0,
      // offset: 0,
      itemToDelete: null,
      isModalOpen: false,
      totalPage: 0,
    };
  }

  componentDidMount() {
    const { location } = this.props;

    const bookkeeperObj = location?.state
      ? [
          {
            label: location?.state?.name,
            value: location?.state?.bookkeeperId,
          },
        ]
      : [];

    this.setState({
      bookkeeper: bookkeeperObj,
      bookKeeperId: location?.state?.bookkeeperId
        ? [location?.state?.bookkeeperId]
        : null,
      selectedBookkeeperIds: location?.state?.bookkeeperId
        ? [location?.state?.bookkeeperId]
        : null,
    });
    this.fetchAllReviews(
      1,
      "draft",
      "",
      location?.state?.bookkeeperId ? [location?.state?.bookkeeperId] : null
    );
    this.fetchAllBookkeeper();
  }

  handleClearClick = () => {
    this.setState({ serachValue: "" });
  };

  handleKeyDown = (event) => {
    var { sortDate, SelectedSport, serachValue, sortType, SelectedUsers } =
      this.state;
    if (event.key === "Enter") {
      this.fetchAllReviews(
        1,
        this.state.selectedReviewStatus,
        this.state.serachValue,
        this.state.selectedBookkeeperIds
      );
      this.setState({ currentPage: 1 });
    }
  };

  async fetchAllBookkeeper() {
    this.setState({ isBookkeeperLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeeper?status=all&isFeatured=`
      );
      if (status === 200) {
        const sortedData = data?.result
          ?.sort((a, b) => a.featured_order - b.featured_order)
          ?.filter((ele) => ele?.status === "active");

        let newdata = [];
        let bookkeeperDatas = sortedData?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });

        this.setState({ bookkeeper: newdata, isBookkeeperLoading: false });
      }
    } catch (err) {
      this.setState({ bookkeeper: [], isBookkeeperLoading: false });
    }
  }
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  // Function to convert ContentState to HTML
  convertContentStateToHTML = (contentState) => {
    const contentStateWithHTML = ContentState.createFromBlockArray(
      contentState.getBlockMap()
    );

    return contentStateWithHTML.getPlainText();
  };

  afterChangeRefresh = () => {
    this.fetchAllBookkeeper();
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, bookkeeper } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < bookkeeper.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  handleReviewStatusChange = (e) => {
    this.setState({
      selectedReviewStatus: e.label,
      currentPage: 1,
    });
    this.fetchAllReviews(
      1,
      e.label,
      this.state.serachValue,
      this.state.selectedBookkeeperIds
    );
  };

  async fetchAllReviews(
    page,
    selectedReviewStatus,
    search,
    selectedBookkeeperIds
  ) {
    let { rowPerPage } = this.state;

    this.setState({ isLoading: true });

    try {
      let passApi = `/reviews/admin?bookKeeperId=${
        selectedBookkeeperIds && selectedBookkeeperIds?.length > 0
          ? selectedBookkeeperIds.toString()
          : ""
      }&perPage=${rowPerPage}&page=${page}&status=${
        selectedReviewStatus && selectedReviewStatus !== "All"
          ? selectedReviewStatus
          : ""
      }&search=${search}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          reviewsData: data?.data,
          isLoading: false,
          currentPage: page,
          totalPage: data?.totalPages,
          totalCount: data?.totalCount,
        });
        this.fetchReviewsRead();
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  async fetchReviewsRead() {
    try {
      let passApi = `/reviews/read`;
      const { status, data } = await axiosInstance.put(passApi);
      if (status === 200) {
        this.props.setReviewCount({
          unread: 0,
          read: this.props?.reviews?.read,
        });
      } else {
      }
    } catch {}
  }

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  deleteItem = async () => {
    try {
      const passApi = `reviews/${this.state.itemToDelete}`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllReviews(
            this.state.currentPage,
            this.state.selectedReviewStatus,
            this.state.serachValue,
            this.state.selectedBookkeeperIds
          );
        });
        this.setActionMessage(true, "Success", "Review Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  updateStatus = async (id, status) => {
    var payload = {
      publish: status,
    };
    try {
      const passApi = `reviews/${id}`;
      const { status } = await axiosInstance.put(passApi, payload);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllReviews(
            this.state.currentPage,
            this.state.selectedReviewStatus,
            this.state.serachValue,
            this.state.selectedBookkeeperIds
          );
        });
        this.setActionMessage(
          true,
          "Success",
          "Review Status Updated Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handleStatusUpdateChange = (e, id) => {
    this.setState({
      selectedModalNewsStatus: e?.label,
    });

    this.updateStatus(id, e?.label);
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
    this.fetchAllReviews(
      page,
      this.state.selectedReviewStatus,
      this.state.serachValue,
      this.state.selectedBookkeeperIds
    );
  };
  render() {
    var {
      selectedFeatureBookMaker,
      selectedReviewStatus,
      messageBox,
      isLoading,
      reviewsData,
      isModalOpen,
      currentPage,
      rowPerPage,
      totalPage,
      totalCount,
      serachValue,
      bookKeeperId,
      bookkeeper,
    } = this.state;
    const pageNumbers = [];
    let currentPageRow = reviewsData;

    if (reviewsData?.length > 0) {
      for (let i = 1; i <= Math.ceil(reviewsData?.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper > */}
            <Box
              style={{
                position: "fixed",
                top: "62px",
                backgroundColor: "transparent",
                zIndex: "1",
                width: "100%",
              }}
            >
              {messageBox?.display && (
                <ActionMessage
                  message={messageBox?.message}
                  type={messageBox?.type}
                  styleClass={messageBox?.styleClass}
                />
              )}
            </Box>
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Bookkeeper Review</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                {/* <h3 className="text-left">Bookkeeper</h3> */}
                <Typography variant="h1" align="left">
                  Bookkeeper Review
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <ButtonComponent
                className="addButton admin-btn-green"
                onClick={this.inputModal(null, "create")}
                color="primary"
                value="Add New"
              /> */}
                <Select
                  className="React cricket-select  external-select review-select"
                  classNamePrefix="select"
                  placeholder="Select Status"
                  value={newsStatusOption?.find((item) => {
                    return item?.label == selectedReviewStatus;
                  })}
                  // //   isLoading={isLoading}
                  onChange={(e) => this.handleReviewStatusChange(e)}
                  options={newsStatusOption}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="event-search"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={serachValue}
                  onChange={(e) => {
                    this.setState({ serachValue: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {serachValue && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                    width: "27.5%",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    // this.fetchAllEvent(
                    //   0,
                    //   sortDate,
                    //   SelectedSport,
                    //   serachValue,
                    //   sortType,
                    //   false,
                    //   SelectedUsers
                    // );

                    this.fetchAllReviews(
                      1,
                      this.state.selectedReviewStatus,
                      serachValue,
                      this.state.selectedBookkeeperIds
                    );
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
              </Grid>
              <Grid
                item
                xs={7}
                className="admin-filter-wrap"
                style={{ marginBottom: "10px" }}
              ></Grid>
              <Grid
                item
                xs={5}
                className="admin-filter-wrap"
                style={{ marginBottom: "10px" }}
              >
                <Select
                  className="React cricket-select sponsored-select-modal"
                  classNamePrefix="select"
                  placeholder="Bookkeeper"
                  menuPosition="fixed"
                  isMulti
                  value={bookkeeper?.find((item) => {
                    return item?.value == bookKeeperId;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => {
                    const selectedIds = [];
                    const ids = e?.map((bookkeeper) =>
                      selectedIds?.push(bookkeeper?.value)
                    );

                    this.fetchAllReviews(
                      1,
                      this.state.selectedReviewStatus,
                      this.state.serachValue,
                      selectedIds
                    );
                    this.setState({
                      bookKeeperId: e,
                      currentPage: 1,
                      selectedBookkeeperIds: selectedIds,
                    });
                  }}
                  options={bookkeeper}
                />
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && reviewsData.length === 0 && <p>No Data Available</p>}
            {!isLoading && reviewsData.length > 0 && (
              <>
                <Grid item xs={12} style={{ marginTop: "10px" }}>
                  <Paper className="pageWrapper api-provider table-height modalTable">
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    {isLoading ? (
                      <Box className="message tablePadding">
                        <Loader />{" "}
                      </Box>
                    ) : reviewsData?.length > 0 ? (
                      <>
                        {" "}
                        <TableContainer>
                          <Table
                            className="listTable"
                            aria-label="simple table"
                          >
                            <TableHead className="tableHead-row">
                              <TableRow className="table-rows listTable-Row">
                                <TableCell>Id</TableCell>
                                <TableCell>Rating</TableCell>
                                <TableCell>Bookkeeper</TableCell>
                                <TableCell>Description</TableCell>
                                <TableCell>User</TableCell>
                                <TableCell></TableCell>
                                <TableCell style={{ width: "25%" }}>
                                  Status
                                </TableCell>
                                <TableCell>Action</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody className="table_body">
                              <TableRow className="table_row">
                                <TableCell
                                  colSpan={100}
                                  className="table-seprator"
                                ></TableCell>
                              </TableRow>
                              {currentPageRow?.map((data, i) => (
                                <TableRow
                                  key={i}
                                  className="table-rows listTable-Row"
                                >
                                  <TableCell>{data?.id}</TableCell>
                                  <TableCell>{data?.rating}</TableCell>
                                  <TableCell>
                                    {data?.bookkeeper?.name}
                                  </TableCell>
                                  <TableCell className="text-wrap">
                                    <Typography>{data?.review}</Typography>
                                  </TableCell>
                                  <TableCell>
                                    {data?.user?.firstName
                                      ? data?.user?.firstName +
                                        " " +
                                        data?.user?.lastName
                                      : ""}
                                  </TableCell>
                                  <TableCell>
                                    {data?.isRead ? (
                                      ""
                                    ) : (
                                      <span className="new-title">New</span>
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    <Select
                                      className="React review-select"
                                      classNamePrefix="select"
                                      placeholder="Update Status"
                                      value={newsModalStatusOption?.find(
                                        (item) => {
                                          return item?.label === data?.publish;
                                        }
                                      )}
                                      //   isLoading={isLoading}
                                      onChange={(e) =>
                                        this.handleStatusUpdateChange(
                                          e,
                                          data?.id
                                        )
                                      }
                                      options={newsModalStatusOption}
                                    />
                                  </TableCell>
                                  <TableCell>
                                    <Button
                                      style={{ minWidth: "0px" }}
                                      onClick={this.setItemToDelete(data?.id)}
                                      className="table-btn delete-btn"
                                    >
                                      Delete
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                              <TableRow>
                                <TableCell colSpan={100} className="pagination">
                                  <div className="tablePagination reviewPagination">
                                    {/* <button
                            className={
                              bookkeeper.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              bookkeeper.length / rowPerPage > 1
                                ? false
                                : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                                    <Pagination
                                      hideNextButton
                                      hidePrevButton
                                      disabled={
                                        totalCount / rowPerPage > 1
                                          ? false
                                          : true
                                      }
                                      page={currentPage}
                                      onChange={this.handlePaginationClick}
                                      count={totalPage}
                                      siblingCount={2}
                                      boundaryCount={1}
                                      size="small"
                                    />
                                    {/* <button
                            className={
                              bookkeeper.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              bookkeeper.length / rowPerPage > 1
                                ? false
                                : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                                  </div>
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                        <ShowModal
                          isModalOpen={isModalOpen}
                          onClose={this.toggleModal}
                          Content="Are you sure you want to delete?"
                          onOkayLabel="Yes"
                          onOkay={this.deleteItem}
                          onCancel={this.toggleModal}
                        />
                      </>
                    ) : (
                      <Box className="message tablePadding">
                        No Reviews Available
                      </Box>
                    )}
                  </Paper>
                </Grid>
              </>
            )}
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}

const mapStateToProps = ({ reviewReducer }) => {
  return {
    reviews: reviewReducer.reviews,
  };
};

export default connect(mapStateToProps, { setReviewCount })(Review);
// export default Review;
