import React, { createRef } from "react";
import { Box, Grid } from "@mui/material";
import { bookkeeperFormModel } from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";
import DeleteIcon from "@mui/icons-material/Delete";
import { ChromePicker } from "react-color";

class CreateBookkeeper extends React.Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      values: {
        name: "",
        variation: "",
        status: "",
        logo: "",
        small_logo: "",
        long_logo: "",
        featured_logo: "",
        em_logo: "",
        graph_logo: "",
        currentBest_logo: "",
        bookMakerList_logo: "",
      },
      apiProviderId: null,
      logo: [],
      small_logo: [],
      long_logo: [],
      featured_logo: [],
      em_logo: [],
      graph_logo: [],
      currentBest_logo: [],
      bookMakerList_logo: [],
      logo_text: "#191919",
      logo_bg: "#ffffff",
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
    };
  }

  componentDidMount() {
    if (this.props.isEditMode) {
      this.fetchCurrentBookkeeper(this.props.id);
    }
  }

  fetchCurrentBookkeeper = async (id) => {
    const { status, data } = await axiosInstance.get(
      URLS.bookkeeper + `/${id}`
    );
    if (status === 200) {
      this.setState((prevState) => ({
        values: {
          ...data.result,
          apiProviderId: data.result[0].apiProviderId,
          logo_text: data?.result?.[0]?.logo_text
            ? data?.result?.[0]?.logo_text
            : "#191919",
          logo_bg: data?.result?.[0]?.logo_bg
            ? data?.result?.[0]?.logo_bg
            : "#ffffff",
          updateRequired: data.result.updateRequired === true ? "1" : "0",
        },
      }));
      this.setState({
        values: data.result[0],
        apiProviderId: data.result[0].apiProviderId,
        logo_text: data?.result?.[0]?.logo_text
          ? data?.result?.[0]?.logo_text
          : "#191919",
        logo_bg: data?.result?.[0]?.logo_bg
          ? data?.result?.[0]?.logo_bg
          : "#ffffff",
      });
      // let variationArray = JSON.parse('["' + data.result[0].variation + '"]');
      // if (variationArray.length > 0) {
      //   this.setState(() => {
      //     return {
      //       values: {
      //         ...this.state.values,
      //         ["variation"]: variationArray[0],
      //       },
      //     };
      //   });
      // }
    }
  };

  validate = () => {
    let { name, status } = this.state.values;
    let { apiProviderId, logo_text, logo_bg } = this.state;
    let flag = true;
    if (
      name === "" ||
      status === "" ||
      apiProviderId === null ||
      logo_text === "" ||
      logo_bg === ""
    ) {
      flag = false;
      this.props.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      this.props.setActionMessage(false);
    }

    return flag;
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handleSave = async () => {
    const {
      apiProviderId,
      logo,
      small_logo,
      long_logo,
      featured_logo,
      em_logo,
      graph_logo,
      currentBest_logo,
      bookMakerList_logo,
      logo_text,
      logo_bg,
    } = this.state;
    const { isEditMode } = this.props;
    this.setState({ isLoading: true });

    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode
        ? `${URLS.bookkeeper}/${this.props.id}`
        : URLS.bookkeeper;

      const values = removeErrorFieldsFromValues(form.formData);
      values["apiProviderId"] = apiProviderId;
      values["logo_text"] = logo_text;
      values["logo_bg"] = logo_bg;
      values["isPartner"] =
        values?.isPartner === "true" || values?.isPartner === true
          ? true
          : false;

      if (this.validate()) {
        if (logo.length > 0) {
          let fileData = await this.setMedia(logo[0]);
          if (fileData) {
            values["logo"] = fileData?.image?.filePath;
          }
        } else {
          values["logo"] =
            this.state.values?.logo && this.state.values?.logo !== ""
              ? this.state.values?.logo
              : null;
        }
        if (small_logo?.length > 0) {
          let fileData = await this.setMedia(small_logo[0]);
          if (fileData) {
            values["small_logo"] = fileData?.image?.filePath;
          }
        } else {
          values["small_logo"] =
            this.state.values?.small_logo &&
            this.state.values?.small_logo !== ""
              ? this.state.values?.small_logo
              : null;
        }
        if (long_logo?.length > 0) {
          let fileData = await this.setMedia(long_logo[0]);
          if (fileData) {
            values["long_logo"] = fileData?.image?.filePath;
          }
        } else {
          values["long_logo"] =
            this.state.values?.long_logo && this.state.values?.long_logo !== ""
              ? this.state.values?.long_logo
              : null;
        }
        if (featured_logo?.length > 0) {
          let fileData = await this.setMedia(featured_logo[0]);
          if (fileData) {
            values["featured_logo"] = fileData?.image?.filePath;
          }
        } else {
          values["featured_logo"] =
            this.state.values?.featured_logo &&
            this.state.values?.featured_logo !== ""
              ? this.state.values?.featured_logo
              : null;
        }
        if (em_logo?.length > 0) {
          let fileData = await this.setMedia(em_logo[0]);
          if (fileData) {
            values["em_logo"] = fileData?.image?.filePath;
          }
        } else {
          values["em_logo"] =
            this.state.values?.em_logo && this.state.values?.em_logo !== ""
              ? this.state.values?.em_logo
              : null;
        }
        if (graph_logo?.length > 0) {
          let fileData = await this.setMedia(graph_logo[0]);
          if (fileData) {
            values["graph_logo"] = fileData?.image?.filePath;
          }
        } else {
          values["graph_logo"] =
            this.state.values?.graph_logo &&
            this.state.values?.graph_logo !== ""
              ? this.state.values?.graph_logo
              : null;
        }
        if (currentBest_logo?.length > 0) {
          let fileData = await this.setMedia(currentBest_logo[0]);
          if (fileData) {
            values["currentBest_logo"] = fileData?.image?.filePath;
          }
        } else {
          values["currentBest_logo"] =
            this.state.values?.currentBest_logo &&
            this.state.values?.currentBest_logo !== ""
              ? this.state.values?.currentBest_logo
              : null;
        }
        if (bookMakerList_logo?.length > 0) {
          let fileData = await this.setMedia(bookMakerList_logo[0]);
          if (fileData) {
            values["bookMakerList_logo"] = fileData?.image?.filePath;
          }
        } else {
          values["bookMakerList_logo"] =
            this.state.values?.bookMakerList_logo &&
            this.state.values?.bookMakerList_logo !== ""
              ? this.state.values?.bookMakerList_logo
              : null;
        }
        const { status } = await axiosInstance[method](url, values);
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          this.props.fetchAllBookkeeper();
          this.props.setActionMessage(
            true,
            "Success",
            `Bookkeeper ${isEditMode ? "Edited" : "Created"} Successfully`
          );
        }
      }
    } catch (err) {
      this.setState({ isLoading: false });
      this.props.setActionMessage(
        true,
        "Error",
        `An error occurred while ${
          isEditMode ? "editing" : "creating"
        } Bookkeeper`
      );
    }
  };

  handleChange = (field, value) => {
    this.setState(() => {
      return {
        values: { ...this.state.values, [field]: value },
      };
    });
    this.props.setActionMessage(false);
  };

  handleSelect = (e) => {
    let value = e.target.value;
    this.setState({ apiProviderId: value });
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  handleLogoRemove = () => {
    const { logo, values } = this.state;
    {
      logo?.length > 0
        ? this.setState({ logo: [] })
        : values?.logo &&
          values?.logo !== "" &&
          this.setState({
            values: {
              ...values,
              logo: "",
            },
          });
    }
  };
  handleSmallLogoRemove = () => {
    const { small_logo, values } = this.state;
    {
      small_logo?.length > 0
        ? this.setState({ small_logo: [] })
        : values?.small_logo &&
          values?.small_logo !== "" &&
          this.setState({
            values: {
              ...values,
              small_logo: "",
            },
          });
    }
  };
  handleLongLogoRemove = () => {
    const { long_logo, values } = this.state;
    {
      long_logo?.length > 0
        ? this.setState({ long_logo: [] })
        : values?.long_logo &&
          values?.long_logo !== "" &&
          this.setState({
            values: {
              ...values,
              long_logo: "",
            },
          });
    }
  };
  handleFeatureLogoRemove = () => {
    const { featured_logo, values } = this.state;
    {
      featured_logo?.length > 0
        ? this.setState({ featured_logo: [] })
        : values?.featured_logo &&
          values?.featured_logo !== "" &&
          this.setState({
            values: {
              ...values,
              featured_logo: "",
            },
          });
    }
  };

  handleEmailLogoRemove = () => {
    const { em_logo, values } = this.state;
    {
      em_logo?.length > 0
        ? this.setState({ em_logo: [] })
        : values?.em_logo &&
          values?.em_logo !== "" &&
          this.setState({
            values: {
              ...values,
              em_logo: "",
            },
          });
    }
  };
  handleGraphLogoRemove = () => {
    const { graph_logo, values } = this.state;
    {
      graph_logo?.length > 0
        ? this.setState({ graph_logo: [] })
        : values?.graph_logo &&
          values?.graph_logo !== "" &&
          this.setState({
            values: {
              ...values,
              graph_logo: "",
            },
          });
    }
  };

  handleCurrentBestLogoRemove = () => {
    const { currentBest_logo, values } = this.state;
    {
      currentBest_logo?.length > 0
        ? this.setState({ currentBest_logo: [] })
        : values?.currentBest_logo &&
          values?.currentBest_logo !== "" &&
          this.setState({
            values: {
              ...values,
              currentBest_logo: "",
            },
          });
    }
  };

  handleBookMakerListLogoRemove = () => {
    const { bookMakerList_logo, values } = this.state;
    {
      bookMakerList_logo?.length > 0
        ? this.setState({ bookMakerList_logo: [] })
        : values?.bookMakerList_logo &&
          values?.bookMakerList_logo !== "" &&
          this.setState({
            values: {
              ...values,
              bookMakerList_logo: "",
            },
          });
    }
  };

  handleChangeLogoText = (selectedColor) => {
    this.setState({ logo_text: selectedColor?.hex });
  };

  handleChangeLogoBG = (selectedColor) => {
    this.setState({ logo_bg: selectedColor?.hex });
  };

  render() {
    const {
      values,
      messageBox,
      isLoading,
      apiProviderId,
      logo,
      small_logo,
      long_logo,
      featured_logo,
      em_logo,
      graph_logo,
      currentBest_logo,
      bookMakerList_logo,
      logo_text,
      logo_bg,
    } = this.state;
    var { isEditMode } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            {/* <Paper> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}

            <Form
              values={values}
              model={bookkeeperFormModel}
              ref={this.formRef}
              onChange={this.handleChange}
            />

            <div style={{ marginTop: 15 }} className="select-box">
              <label className="modal-label">API Provider</label>
              <select
                className="select-box-manual select-box-arrow React"
                name="eventFeatureId"
                onChange={this.handleSelect}
                value={apiProviderId}
              >
                <option value={null}>No API Provider Selected</option>
                {this.props.allApiProvider?.map((obj, i) => (
                  <option key={i} value={obj.id}>
                    {obj.providerName}
                  </option>
                ))}
              </select>
            </div>
            <div className="upload-grid">
              <div className="blog-file-upload">
                <h6>Small logo</h6>
                <FileUploader
                  onDrop={(logo) => this.handleFileUpload("small_logo", logo)}
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {small_logo?.length > 0
                      ? small_logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.small_logo &&
                        values?.small_logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.small_logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(small_logo?.length > 0 ||
                    (values?.small_logo && values?.small_logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleSmallLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
              <div className="blog-file-upload">
                <h6> Ourpartner Logo</h6>
                <FileUploader
                  onDrop={(logo) => this.handleFileUpload("logo", logo)}
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {logo?.length > 0
                      ? logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.logo &&
                        values?.logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(logo?.length > 0 ||
                    (values?.logo && values?.logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
              <div className="blog-file-upload">
                <h6>Long Logo</h6>
                <FileUploader
                  onDrop={(logo) => this.handleFileUpload("long_logo", logo)}
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {long_logo?.length > 0
                      ? long_logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.long_logo &&
                        values?.long_logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.long_logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(long_logo?.length > 0 ||
                    (values?.long_logo && values?.long_logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleLongLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
              <div className="blog-file-upload">
                <h6>Feature logo</h6>
                <FileUploader
                  onDrop={(logo) =>
                    this.handleFileUpload("featured_logo", logo)
                  }
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {featured_logo?.length > 0
                      ? featured_logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.featured_logo &&
                        values?.featured_logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.featured_logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(featured_logo?.length > 0 ||
                    (values?.featured_logo &&
                      values?.featured_logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleFeatureLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
              <div className="blog-file-upload">
                <h6>Email logo</h6>
                <FileUploader
                  onDrop={(logo) => this.handleFileUpload("em_logo", logo)}
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {em_logo?.length > 0
                      ? em_logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.em_logo &&
                        values?.em_logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.em_logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(em_logo?.length > 0 ||
                    (values?.em_logo && values?.em_logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleEmailLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
              <div className="blog-file-upload">
                <h6>Graph logo</h6>
                <FileUploader
                  onDrop={(logo) => this.handleFileUpload("graph_logo", logo)}
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {graph_logo?.length > 0
                      ? graph_logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.graph_logo &&
                        values?.graph_logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.graph_logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(graph_logo?.length > 0 ||
                    (values?.graph_logo && values?.graph_logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleGraphLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
              <div className="blog-file-upload">
                <h6>Current Best logo</h6>
                <FileUploader
                  onDrop={(logo) =>
                    this.handleFileUpload("currentBest_logo", logo)
                  }
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {currentBest_logo?.length > 0
                      ? currentBest_logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.currentBest_logo &&
                        values?.currentBest_logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.currentBest_logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(currentBest_logo?.length > 0 ||
                    (values?.currentBest_logo &&
                      values?.currentBest_logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleCurrentBestLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
              <div className="blog-file-upload">
                <h6>BookMaker List logo</h6>
                <FileUploader
                  onDrop={(logo) =>
                    this.handleFileUpload("bookMakerList_logo", logo)
                  }
                />
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <div className="logocontainer">
                    {bookMakerList_logo?.length > 0
                      ? bookMakerList_logo?.map((file, index) => (
                          <img
                            className="auto-width"
                            key={index}
                            src={file.preview}
                            alt="file"
                          />
                        ))
                      : values?.bookMakerList_logo &&
                        values?.bookMakerList_logo !== "" && (
                          <img
                            className="auto-width"
                            src={config.mediaUrl + values?.bookMakerList_logo}
                            alt="file"
                          />
                        )}
                  </div>
                  {(bookMakerList_logo?.length > 0 ||
                    (values?.bookMakerList_logo &&
                      values?.bookMakerList_logo !== "")) && (
                    <Box className="delete-icon-wrap">
                      <DeleteIcon
                        className="delete-icon"
                        onClick={() => this.handleBookMakerListLogoRemove()}
                        style={{ cursor: "pointer" }}
                      />
                    </Box>
                  )}
                </Box>
              </div>
            </div>

            <Box className="color-picker-wrap">
              <div className="color-picker-box">
                <h6>logo Text</h6>
                <ChromePicker
                  color={logo_text}
                  onChange={this.handleChangeLogoText}
                  disableAlpha={true}
                />
              </div>
              <div className="color-picker-box">
                <h6>logo BackGround</h6>
                <ChromePicker
                  color={logo_bg}
                  onChange={this.handleChangeLogoBG}
                  disableAlpha={true}
                />
              </div>
            </Box>
            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateBookkeeper;
