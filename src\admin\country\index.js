import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  TextField,
  Typography,
  Box,
  Breadcrumbs,
  InputAdornment,
  Button,
  IconButton,
} from "@mui/material";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import RateReviewIcon from "@mui/icons-material/RateReview";
// import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import CreateCountry from "./CreateCountry";
import CreateVariation from "./CreateVariation";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
import SearchIcons from "../../images/searchIcon.svg";
import { config } from "../../helpers/config";
// import { showVariations } from "../../helpers/common";

class Country extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      country: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      search: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      variationToSend: null,
      isVariationModalOpen: false,
      countryToSend: null,
      offset: 0,
      sportCount: null,
    };
  }

  componentDidMount() {
    this.fetchAllCountry();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match !== this.props.match) {
      this.fetchAllCountry();
    }
    if (prevState.offset !== this.state.offset) {
      this.fetchAllCountry();
    }
  }
  async fetchAllCountry() {
    let { rowPerPage, offset, search } = this.state;
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      // `${URLS.country}?limit=${rowPerPage}&offset=${offset}&search=${search}`
      `public/country?limit=${rowPerPage}&offset=${offset}&search=${search}`
    );
    if (status === 200) {
      this.setState({
        country: data?.result?.rows,
        isLoading: false,
        // search: "",
        sportCount: data?.result?.count,
      });
    }
  }

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };
  toggleVariationModal = () => {
    this.setState({ isVariationModalOpen: !this.state.isVariationModalOpen });
  };

  inputModal = (id, type, variation) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        idToSend: id,
        isEditMode: true,
        variationToSend: variation,
      });
    } else {
      this.setState({ isEditMode: false });
    }
  };
  inputVariationModal = (id, country) => () => {
    this.setState({
      isVariationModalOpen: true,
      idToSend: id,
      countryToSend: country,
    });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  navigateToState = (id, country) => () => {
    this.props.navigate(`/countries/states/${id}/${country}`);
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllCountry();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.country}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllCountry();
        });
        this.setActionMessage(true, "Success", "Country Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      // offset
    } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  // handlePaginationButtonClick = (navDirection) => {
  //   let { currentPage, rowPerPage, country } = this.state;
  //   if (navDirection === "prev") {
  //     if (currentPage > 1) {
  //       this.setState({ currentPage: currentPage - 1 });
  //     }
  //   } else {
  //     if (currentPage < country?.length / rowPerPage)
  //       this.setState({ currentPage: currentPage + 1 });
  //   }
  // };
  handlePaginationButtonClick = (navDirection) => {
    let {
      currentPage,
      //  rowPerPage,
      offset,
    } = this.state;
    // if (navDirection === "prev") {
    //   if (currentPage > 1) {
    //     this.setState({ currentPage: currentPage - 1 });
    //   }
    // } else {
    //   if (currentPage < tracksDetails?.length / rowPerPage)
    //     this.setState({ currentPage: currentPage + 1 });
    // }

    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.setState({
        currentPage: 1,
        offset: 0,
      });
      this.fetchAllCountry();
    }
  };

  render() {
    var {
      country,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      search,
      variationToSend,
      isVariationModalOpen,
      countryToSend,
      // offset,
      sportCount,
    } = this.state;

    const pageNumbers = [];
    if (sportCount > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = tracksDetails.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    // search !== "" &&
    //   (country = country?.filter(
    //     (obj) =>
    //       obj?.country
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(search.toString().toLowerCase()) ||
    //       obj?.variation
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(search.toString().toLowerCase())
    //   ));

    let currentPageRow = country;

    // if (country?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = country.slice(indexOfFirstTodo, indexOfLastTodo);

    //   for (let i = 1; i <= Math.ceil(country?.length / rowPerPage); i++) {
    //     pageNumbers.push(i);
    //   }
    // }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Master Data
                </Link>
                <Typography className="active_p">Country</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                {/* <h3 className="text-left">Countries</h3> */}
                <Typography variant="h1" align="left">
                  Countries
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{ display: "flex", justifyContent: "flex-end" }}
                className="admin-filter-wrap"
              >
                <TextField
                  className="textfield-tracks search-track"
                  style={{ marginTop: 3, width: "478px", color: "#D4D6D8" }}
                  // label="Search"
                  size="small"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  variant="outlined"
                  placeholder="Search Country"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                        {/* <SearchIcon /> */}
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  value={search}
                  onChange={(e) => {
                    this.setState({
                      ...this.state.search,
                      search: e.target.value,
                    });
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "10px 15px",
                    marginTop: "5px",
                  }}
                  onClick={(e) => {
                    this.setState({
                      currentPage: 1,
                      offset: 0,
                    });
                    this.fetchAllCountry(e);
                  }}
                >
                  Search
                </Button>

                {/* &nbsp;&nbsp;&nbsp; */}
                <div>
                  {/* <ButtonComponent
                  className="addButton admin-btn-green"
                  onClick={this.inputModal(null, "create")}
                  color="primary"
                  value="Add New"
                /> */}
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "10px 15px",
                      marginTop: "5px",
                    }}
                    onClick={this.inputModal(null, "create", null)}
                  >
                    Add New
                  </Button>
                </div>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && country?.length === 0 && <p>No Data Available</p>}
            {!isLoading && country?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>Country Name</TableCell>
                        <TableCell>Country Code</TableCell>
                        <TableCell>Phone Code</TableCell>
                        <TableCell>variation</TableCell>
                        <TableCell>Update Required</TableCell>
                        <TableCell> States</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((country, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell className="table-country-flag">
                            {" "}
                            {country?.country_flag ? (
                              country?.country_flag?.includes("uploads") ? (
                                <img
                                  src={config.mediaUrl + country?.country_flag}
                                  alt="Flag"
                                />
                              ) : (
                                <img src={country?.country_flag} alt="Flag" />
                              )
                            ) : (
                              <></>
                            )}
                            <span>{country?.country}</span>
                          </TableCell>

                          <TableCell>{country?.countryCode}</TableCell>
                          <TableCell>{country?.phoneCode}</TableCell>
                          <TableCell>
                            {" "}
                            <Button
                              variant="contained"
                              style={{
                                backgroundColor: "#4455C7",
                                color: "#fff",
                                borderRadius: "8px",
                                textTransform: "capitalize",
                                padding: "8px 10px ",
                                marginLeft: "15px",
                              }}
                              onClick={this.inputVariationModal(
                                country?.id,
                                // "edit",
                                country?.country
                              )}
                            >
                              Add/Edit variation
                            </Button>
                          </TableCell>
                          <TableCell>
                            {country?.updateRequired === true ? "yes" : "no"}
                          </TableCell>
                          <TableCell>
                            <RateReviewIcon
                              onClick={this.navigateToState(
                                country?.id,
                                country?.country
                              )}
                              color="primary"
                              className="cursor iconBtn admin-btn-green"
                            />
                          </TableCell>
                          <TableCell>
                            {/* <EditIcon
                            onClick={this.inputModal(country.id, "edit")}
                            color="primary"
                            className="mr10 cursor iconBtn admin-btn-green"
                          /> */}
                            <Button
                              onClick={this.inputModal(
                                country?.id,
                                "edit",
                                country?.variation
                              )}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            {/* <DeleteOutlineIcon
                            onClick={this.setItemToDelete(country.id)}
                            color="secondary"
                            className="mr10 cursor iconBtn admin-btn-orange"
                          /> */}
                            <Button
                              onClick={this.setItemToDelete(country?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                offset >= 20
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={offset >= 20 ? false : true}
                              // disabled={
                              //   meetingsDetails?.length / rowPerPage > 1 ? false : true
                              // }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={sportCount > 0 ? false : true}
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                rowPerPage + offset < sportCount
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                rowPerPage + offset < sportCount
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Country" : "Edit Country"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateCountry
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  variation={variationToSend}
                  isEditMode={isEditMode}
                  fetchAllCountry={this.afterChangeRefresh}
                />
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isVariationModalOpen}
              onClose={this.toggleVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {/* {!isEditMode ? "Create New Country" : "Edit Country"} */}
                  Variation Details
                  <span className="modal-country-name">{countryToSend}</span>
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleVariationModal}
                />
                <CreateVariation
                  inputModal={this.toggleVariationModal}
                  id={this.state.idToSend}
                  variation={variationToSend}
                  isEditMode={isEditMode}
                  fetchAllCountry={this.afterChangeRefresh}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Country;
