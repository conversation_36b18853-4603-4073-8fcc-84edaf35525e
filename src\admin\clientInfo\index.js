import React, { Component } from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  TableSortLabel,
  IconButton,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
import { Link } from "react-router-dom";
// import CSVExport from "../csvExport/CSVExport";
import moment from "moment-timezone";
import SearchIcons from "../../images/searchIcon.svg";
import CreateClient from "./CreateClient";

let clientData = [
  {
    id: 1,
    client_name: "<PERSON>",
    contact_name: "XY<PERSON>",
    email: "<EMAIL>",
    website: "www.xyz.com",
    number: "9966558844",
  },
  {
    id: 2,
    client_name: "ABC",
    contact_name: "XYZ",
    email: "<EMAIL>",
    website: "www.xyz.com",
    number: "9966558844",
  },
  {
    id: 3,
    client_name: "ABC",
    contact_name: "XYZ",
    email: "<EMAIL>",
    website: "www.xyz.com",
    number: "9966558844",
  },
];

class ClientInfo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      client: [],
      isInputModalOpen: false,
      isActivityModalOpen: false,
      isDetailsModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      userId: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      search: "",
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      clientCount: 0,
      csvListData: [],
      clientDetail: [],
      paginationPage: [],
      sortType: "id",
      sortLabelid: false,
      sortName: false,
    };
  }

  componentDidMount() {
    this.fetchClientlist(0, "id", false);
  }

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  goTOMedia = (id) => () => {
    this.props.navigate(`/mediagallery/${id}`);
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  afterChangeRefresh = () => {
    this.fetchClientlist(this.state.offset, "id", false);
  };

  fetchClientlist = async (offsetsize, type, order) => {
    const { rowPerPage, search } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/campaign/client?limit=${rowPerPage}&offset=${offsetsize}&search=${search}&key=${
          type ? type : ""
        }&order=${order ? "ASC" : "DESC"}`
      );
      if (status === 200) {
        const pageNumbers = [];
        if (data?.count > 0) {
          for (let i = 1; i <= Math.ceil(data?.count / rowPerPage); i++) {
            pageNumbers.push(i);
          }
        }
        this.setState({
          isLoading: false,
          client: data?.result,
          paginationPage: pageNumbers,
          offset: offsetsize,
          clientCount: data?.count,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({ isLoading: false });
    }
  };

  sortLabelHandler = (type) => {
    const { sortLabelid, sortName } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchClientlist(0, type, !sortLabelid);
      this.setState({
        sortLabelid: !sortLabelid,
        sortName: true,
        currentPage: 1,
      });
    } else {
      this.fetchClientlist(0, type, !sortName);
      this.setState({
        sortLabelid: false,
        sortName: !sortName,
        currentPage: 1,
      });
    }
  };

  deleteItem = async () => {
    this.setState({ isModalOpen: false });
    const { offset } = this.state;
    try {
      const { status } = await axiosInstance.delete(
        `/campaign/client/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchClientlist(offset, "id", false);
        });
        this.setActionMessage(true, "Success", "Client Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage, key, order } = this.state;

    const offsetNew = (Number(page) - 1) * rowPerPage;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
    this.fetchClientlist((Number(page) - 1) * rowPerPage, key, order);
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { key, order } = this.state;
    if (event.key === "Enter") {
      this.fetchClientlist(0, key, order);
      this.setState({ currentPage: 1 });
    }
  };
  render() {
    var {
      client,
      isModalOpen,
      rowPerPage,
      currentPage,
      messageBox,
      isLoading,
      isInputModalOpen,
      isEditMode,
      offset,
      paginationPage,
      clientCount,
      userId,
      search,
      clientDetail,
      key,
      order,
      sortType,
      sortLabelid,
      sortName,
    } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Campaign
                </Link>
                <Typography className="active_p">Clients</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Clients
                </Typography>
              </Grid>
              {/* <Grid
              item
              xs={8}
              style={{ display: "flex", justifyContent: "flex-end" }}
              className="admin-filter-wrap"
            >
              <TextField
                className="textfield-tracks search-track"
                style={{ marginTop: 3, width: "478px", color: "#D4D6D8" }}
                size="small"
                variant="outlined"
                placeholder="Search Client"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <img
                        src={SearchIcons}
                        alt="icon"
                      />
                    </InputAdornment>
                  ),
                }}
                value={search}
                onChange={(e) => {
                  this.setState({
                    ...this.state.search,
                    search: e.target.value,
                  });
                }}
              />
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "10px 15px",
                  marginTop: "5px",
                }}
              >
                Search
              </Button>

              <div>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "10px 15px",
                    marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create", null)}
                >
                  Add New
                </Button>
              </div>
            </Grid> */}
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "20%", marginTop: "0px" }}
              >
                <option value="">Select Category</option>
                {CategoryData?.length > 0 &&
                  CategoryData?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.categoryName}
                    </option>
                  ))}
              </SelectBox> */}
                {/* <Select
                className="React teamsport-select"
                classNamePrefix="select"
                // onMenuScrollToBottom={(e) =>
                //   this.handleOnScrollBottomDistance(e)
                // }
                // onInputChange={(e) => this.handleDistanceInputChange(1, e)}
                // value={
                //   isDistanceSearch
                //     ? searchDistance?.find((item) => {
                //         return item?.value == distance;
                //       })
                //     : distanceAll?.find((item) => {
                //         return item?.value == distance;
                //       })
                // }
                // onChange={(e) =>
                //   this.setState({
                //     distance: e.value,
                //   })
                // }
                options={countryData}
              /> */}
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchClientlist(0, key, order);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && client.length === 0 && <p>No Data Available</p>}
            {!isLoading && client.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable"
                    aria-label="simple table"
                    style={{ minWidth: "max-content" }}
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer" }}
                        >
                          DID
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortLabelid
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("name")}
                          style={{ cursor: "pointer" }}
                        >
                          Client Name
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "name"
                                ? sortName
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell>Contact Name</TableCell>
                        <TableCell style={{ width: "180px" }}>Email</TableCell>
                        <TableCell style={{ width: "450px" }}>
                          Website
                        </TableCell>
                        <TableCell>Number</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {client?.length > 0 ? (
                        client?.map((client, i) => (
                          <TableRow
                            key={i}
                            className="table-rows listTable-Row"
                          >
                            <TableCell>{client?.id}</TableCell>
                            <TableCell>{client?.name}</TableCell>
                            <TableCell>{client?.contact_name}</TableCell>
                            <TableCell>{client?.email}</TableCell>
                            <TableCell>{client?.website}</TableCell>
                            <TableCell>{client?.phone_number}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(client?.id, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(client?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                              <Button
                                onClick={this.goTOMedia(client?.id)}
                                className="table-btn info-btn"
                              >
                                Ad Management
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                            className={
                              users.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              users.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}

                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                clientCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={paginationPage[paginationPage?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                            className={
                              users.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              users.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Client" : "Edit Client"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateClient
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllUsers={this.afterChangeRefresh}
                  offset={offset}
                  isInputModalOpen={isInputModalOpen}
                  sortType={sortType}
                  sortLabelid={sortLabelid}
                  sortName={sortName}
                  setActionMessage={(
                    display = false,
                    type = "",
                    message = ""
                  ) => {
                    this.setState(
                      { messageBox: { display, type, message } },
                      () =>
                        setTimeout(
                          () =>
                            this.setState({
                              messageBox: {
                                display: false,
                                type: "",
                                message: "",
                              },
                            }),
                          3000
                        )
                    );
                  }}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}

export default ClientInfo;
