.tablistContainer {
  @media (min-width: 768px) {
    padding-left: 30px;
    padding-right: 30px;
  }
  .tablistWrap {
    position: relative;
    @media (min-width: 768px) {
      margin-left: -20px;
      margin-right: -20px;
    }
    .MuiTabScrollButton-root {
      width: 20px;
      svg {
        font-size: 35px;
      }
      @media (max-width: 768px) {
        top: -5px;
      }
    }
  }
  &.pd-15 {
    @media (min-width: 768px) {
      padding-left: 15px;
      padding-right: 15px;
    }
    .tablistWrap {
      @media (min-width: 768px) {
        padding-left: -10px;
        padding-right: -10px;
      }
    }
  }
  .MuiTabs-indicator {
    display: none;
  }
  .MuiAppBar-root {
    background: none;
    box-shadow: none;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .MuiTab-wrapper {
    font-size: 15px;
    font-weight: 500;
    text-transform: none;
    font-family: "Open Sans";
    @media (max-width: 768px) {
      font-size: 13px;
      line-height: 20px;
    }
  }
  .MuiTab-root {
    min-width: 80px;
    position: relative;
    border-radius: 8px;
    @media (max-width: 768px) {
      min-width: 45px;
      padding: 10px;
      min-height: auto;
    }
    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 1px;
      background: #464545;
    }
    &.Mui-selected {
      color: #ffffff;
      fill: #ffffff;
      background: #00833e;
      margin-left: -1px;
      & svg * {
        fill: #fff;
      }
      &:after {
        display: none;
      }
    }
    &.tablistItem.subtabItem {
      min-height: auto;
      line-height: 25px;
    }
    &.tablistItem.subtabItem.Mui-selected {
      background: #6abf4b;
    }
  }
}
