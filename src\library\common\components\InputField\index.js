import React, { useState } from "react";
import PropTypes from "prop-types";
import "./input.scss";
import { validateInput } from "../../../utilities/Validators";
import visiblityOff from "../../../../images/visiblity-off.svg";
import visiblity from "../../../../images/visiblity.svg";

const InputField = ({
  type,
  validators,
  onChange,
  label,
  field,
  testId,
  inputValue,
  placeholder,
  extraProps,
  isDisabled,
  page,
}) => {
  const [error, setError] = useState(null);
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleChange = (event) => {
    const { value } = event.target;
    const inputError = validateInput(validators, value);
    setError(inputError);
    onChange(value, field, inputError);
  };
  return (
    <div className="input-field">
      {label !== "" ? <label htmlFor="inputField">{label}</label> : null}
      {type === "textarea" ? (
        <textarea
          {...extraProps}
          className="form-control rounded-0"
          data-test={testId}
          aria-describedby="inputField"
          placeholder={placeholder}
          value={inputValue}
          onChange={handleChange}
        />
      ) : type == "password" && page !== "userpage" ? (
        <input
          {...extraProps}
          type={type}
          className="form-control rounded-0"
          data-test={testId}
          aria-describedby="inputField"
          placeholder={placeholder}
          onChange={handleChange}
          disabled={isDisabled}
        />
      ) : (
        <>
          {page === "userpage" ? (
            <>
              <input
                {...extraProps}
                type={showPassword ? "text" : "password"}
                className="form-control rounded-0"
                data-test={testId}
                aria-describedby="inputField"
                placeholder={placeholder}
                value={inputValue}
                onChange={handleChange}
              />
              <span
                className="password-icon"
                onClick={togglePasswordVisibility}
              >
                {!showPassword ? (
                  <img src={visiblityOff} alt="visiblityOff" />
                ) : (
                  <img src={visiblity} alt="visiblity" />
                )}
              </span>
            </>
          ) : (
            <input
              {...extraProps}
              type={type}
              className="form-control rounded-0"
              data-test={testId}
              aria-describedby="inputField"
              placeholder={placeholder}
              value={inputValue}
              onChange={handleChange}
            />
          )}
        </>
      )}
      {error && error.error && (
        <div className="text-left errorText">{error.message}</div>
      )}
    </div>
  );
};

InputField.propTypes = {
  type: PropTypes.string,
  inputValue: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  validators: PropTypes.array,
  label: PropTypes.string,
  testId: PropTypes.string,
  placeholder: PropTypes.string,
  field: PropTypes.string,
  extraProps: PropTypes.object,
  isDisabled: PropTypes.bool,
};

InputField.defaultProps = {
  validators: [],
  label: "",
  testId: "",
  placeholder: "",
  field: "",
  type: "text",
  extraProps: {},
  isDisabled: false,
};

export default InputField;
