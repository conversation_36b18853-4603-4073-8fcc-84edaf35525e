{"homepage": "https://testing.smartb.com.au/admin/", "name": "smartb", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "^3.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.0.1", "@mui/lab": "^6.0.0-beta.8", "@mui/material": "^6.0.2", "@mui/styled-engine-sc": "^6.0.2", "@mui/styles": "^6.0.1", "@mui/x-date-pickers": "^7.15.0", "@progress/kendo-drawing": "^1.20.2", "@progress/kendo-licensing": "^1.3.5", "@progress/kendo-react-charts": "^8.2.0", "@progress/kendo-react-intl": "^8.2.0", "@testing-library/react": "^16.0.1", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.7", "country-state-city-plus": "^1.1.1", "crypto-js": "^4.2.0", "d3-array": "^3.2.4", "date-fns": "^3.6.0", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "he": "^1.2.0", "html-to-draftjs": "^1.5.0", "i": "^0.3.7", "install": "^0.13.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "npm": "^10.8.3", "prop-types": "^15.8.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-ckeditor-component": "^1.1.0", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-countdown": "^2.3.6", "react-date-picker": "^11.0.0", "react-datepicker": "^7.3.0", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-dropzone": "^14.2.3", "react-ga": "^3.3.1", "react-helmet": "^6.1.0", "react-icons": "^5.3.0", "react-number-format": "^5.4.1", "react-places-autocomplete": "^7.3.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.1", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-share": "^5.1.0", "react-slick": "^0.30.2", "react-svg": "^16.1.34", "react-timer-hook": "^3.0.7", "recharts": "^2.12.7", "redux": "^5.0.1", "redux-pack": "^0.1.5", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "sass": "^1.77.8", "slick-carousel": "^1.8.1", "slugify": "^1.6.6", "styled-components": "^6.1.13", "suneditor": "^2.47.0", "suneditor-react": "^3.6.1", "uninstall": "0.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.js src/**/*.jsx"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}