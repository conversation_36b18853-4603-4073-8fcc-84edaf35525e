import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
  Typography,
  // InputAdornment,
} from "@mui/material";
import styled from "styled-components";
// import AsyncSelect from "react-select/async";
import Select from "react-select";
import { MdKeyboardBackspace } from "react-icons/md";
import { Loader } from "../../../library/common/components";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
import ButtonComponent from "../../../library/common/components/Button";
import { URLS } from "../../../library/common/constants";
import axiosInstance from "../../../helpers/Axios";
// import moment from "moment";
import RacePlayersDetails from "./racePlayersDetails";
import CancelIcon from "@mui/icons-material/Cancel";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import EditRunnerDetail from "./editRunnerDetail";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import _ from "lodash";
// import SearchIcons from "../../../images/searchIcon.svg";
// import { Label } from "@mui/icons-material";
import EditRunnerExtraInfo from "./editRunnerExtraInfo";
import EditPreviousRunnerInfo from "./editPreviousRunnerInfo";

class RunnersDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      runnersDetails: [],
      isInputModalOpen: false,
      idToSend: null,
      isLoading: false,
      isEditMode: true,
      currentPage: 1,
      rowPerPage: 20,
      searchInput: "",
      rowToSend: null,
      itemToDelete: null,
      isDeleteRunnerModalOpen: false,
      isDeleteLoading: "",
      animalDetails: [],
      playersDetails: [],
      TrainnerDetail: [],
      SearchplayersDetails: [],
      SearchanimalDetails: [],
      SearchTrainerDetails: [],
      loading: false,
      page: 1,
      TotalCount: "",
      isSearch: "",
      isanimalSearch: "",
      ResultPage: 1,
      animalResultPage: 1,
      ResultTotalCount: "",
      AnimalResultTotalCount: "",
      selectedJockey: "",
      animalPage: 1,
      AnimalTotalCount: "",
      SelectedAnimal: "",
      SelectedTrainer: "",
      selectedbarrierNumber: "",
      selectedRunnerNumber: "",
      JockeyWeight: "",
      values: {
        particiantId: null,
        JockeyWeight: "",
        runnerNumber: "",
        jockeyId: null,
        trainerId: null,
        barrierNumber: "",
      },
      errorAnimal: "",
      errorJockeyWeight: "",
      errorJockey: "",
      erroTrainner: "",
      errobarrierNumber: "",
      errorRunnerNumber: "",
      isRunnerInfoInputModalOpen: false,
      isRunnerPreviousInputModalOpen: false,
      runnerId: "",
      previousRuns: [],
      runnerExtraInfoData: {},
      runnerinfoData: {},
      rowPerPages: 100,
      offset: 0,
    };
  }

  componentDidMount() {
    this.fetchAllRunners();
    // this.fetchAllAnimal(1, []);
    // this.fetchAllTrainer(1, []);
  }

  componentDidUpdate(prevProps) {
    if (this.props.match.params.id !== prevProps.match.params.id) {
      this.fetchAllRunners();
    }
  }
  fetchAllRunners = async () => {
    let id = this.props.match.params.id;
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      `/events/runnerwithodds/${id}`
    );
    if (status === 200) {
      let data_obj = _.orderBy(data?.result, ["animal.name"], ["asc"]);
      this.setState({ runnersDetails: data_obj, isLoading: false });
    }
  };

  fetchAllAnimal = async (page, selectedRunner) => {
    const { status, data } = await axiosInstance.get(
      `animal?size=100&page=${page}`
    );
    if (status === 200) {
      let Aarray = [...this.state.animalDetails];

      let count = data?.result?.count / 100;
      let newdata = [];
      let finalData = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      const filterData = newdata.sort((a, b) => {
        return a.label > b.label ? 1 : -1;
      });

      this.setState({
        animalDetails: _.unionBy(
          selectedRunner,
          this.state.animalDetails,
          filterData
        ),
        AnimalTotalCount: Math.ceil(count),
        SelectedAnimal: "",
      });
    }
  };

  fetchAllTrainer = async (page, selectedRunner) => {
    const { rowPerPages } = this.state;
    const { status, data } = await axiosInstance.get(
      // `race/participant/trainers/trainer?size=100&page=${page}`
      `race/participant/trainers/trainer?limit=${rowPerPages}&offset=${page}`
    );

    if (status === 200) {
      // let Parray = [...this.state.playersDetails];
      let Tarray = [...this.state.TrainnerDetail];

      let count = data?.result?.count / 100;

      let newdata = [];
      let finalData = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      const filterData = newdata.sort((a, b) => {
        return a.label > b.label ? 1 : -1;
      });

      this.setState({
        TrainnerDetail: _.unionBy(
          selectedRunner,
          this.state.TrainnerDetail,
          filterData
        ),
        TotalCount: Math.ceil(count),
        selectedJockey: "",
        SelectedTrainer: "",
      });
    }
  };
  fetchAllJockeyTrainer = async (page, selectedTrainer, selectJocky) => {
    const { rowPerPages } = this.state;
    let sportName = this.props?.match.params.sportname;
    let url =
      sportName === "Horse Racing" || sportName === "Harness Racing"
        ? URLS.getJockeys + `jockeys/jockey?limit=${rowPerPages}&offset=${page}`
        : URLS.getJockeys +
          `trainers/trainer?limit=${rowPerPages}&offset=${page}`;

    const { status, data } = await axiosInstance.get(url);
    if (status === 200) {
      let Parray = [...this.state.playersDetails];

      let count = data?.result?.count / 100;

      let newdata = [];
      let finalData = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      const filterData = newdata.sort((a, b) => {
        return a.label > b.label ? 1 : -1;
      });
      let selectedRunner =
        sportName === "Horse Racing" || sportName === "Harness Racing"
          ? selectJocky
          : selectedTrainer;
      this.setState({
        playersDetails: _.unionBy(
          selectedRunner,
          this.state.playersDetails,
          filterData
        ),
        // TrainnerDetail: _.unionBy(
        //   selectedRunner,
        //   this.state.TrainnerDetail,
        //   newdata
        // ),
        TotalCount: Math.ceil(count),
        selectedJockey: "",
        SelectedTrainer: "",
      });
    }
  };

  handleInputChange = (page, value) => {
    const { rowPerPages } = this.state;

    if (value.length > 2) {
      axiosInstance
        .get(
          `race/participant/trainers/trainer?limit=${rowPerPages}&offset=${page}&search=${value}`
        )
        .then((res) => {
          if (res.status === 200) {
            let response = res?.data?.result;

            // let Parray = [...this.state.playersDetails];
            let count = response.count / 100;

            let newdata = [];
            let track = response?.rows?.map((item) => {
              newdata.push({
                label: item?.name,
                value: item?.id,
              });
            });
            const filterTrainersData = newdata.sort((a, b) => {
              return a.label > b.label ? 1 : -1;
            });

            let filterData = _.unionBy(
              this.state.SearchTrainerDetails,
              filterTrainersData
            );

            this.setState({
              SearchTrainerDetails: _.uniqBy(filterData, function (e) {
                return e.value;
              }),
              ResultTotalCount: Math.ceil(count),
              isSearch: value,
            });
          }
        });
    } else {
      this.setState({
        isSearch: "",
        ResultPage: 1,
        SearchTrainerDetails: [],
      });
    }
  };
  handleInputChangeJockeyTrainer = (page, value) => {
    const { rowPerPages } = this.state;
    let sportName = this.props?.match.params.sportname;
    let url =
      sportName === "Horse Racing" || sportName === "Harness Racing"
        ? URLS.getJockeys +
          `jockeys/jockey?limit=${rowPerPages}&offset=${page}&search=${value}`
        : URLS.getJockeys +
          `trainers/trainer?limit=${rowPerPages}&offset=${page}&search=${value}`;
    if (value.length > 2) {
      axiosInstance.get(url).then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result;
          // let Parray = [...this.state.playersDetails];
          let count = response.count / 100;
          let newdata = [];
          let track = response?.rows?.map((item) => {
            newdata.push({
              label: item?.name,
              value: item?.id,
            });
          });
          const filterJockeyTrainersData = newdata.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          });

          let filterData = _.unionBy(
            this.state.SearchplayersDetails,
            filterJockeyTrainersData
          );

          this.setState({
            SearchplayersDetails: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            ResultTotalCount: Math.ceil(count),
            isSearch: value,
          });
        }
      });
    } else {
      this.setState({
        isSearch: "",
        ResultPage: 1,
        SearchplayersDetails: [],
      });
    }
  };

  handleAnimalInputChange = (page, value) => {
    if (value.length > 2) {
      axiosInstance
        .get(`animal?size=100&page=${page}&search=${value}`)
        .then((res) => {
          if (res.status === 200) {
            let response = res?.data;
            let Parray = [...this.state.animalDetails];
            let count = response?.result?.count / 100;
            let newdata = [];
            let FinalData = response?.result?.rows?.map((item) => {
              newdata.push({
                label: item?.name,
                value: item?.id,
              });
            });
            const filterAnimalData = newdata.sort((a, b) => {
              return a.label > b.label ? 1 : -1;
            });

            let filterData = _.unionBy(
              this.state.SearchanimalDetails,
              filterAnimalData
            );

            this.setState({
              SearchanimalDetails: _.uniqBy(filterData, function (e) {
                return e.value;
              }),
              AnimalResultTotalCount: Math.ceil(count),
              isanimalSearch: value,
            });
          }
        });
    } else {
      this.setState({
        isanimalSearch: "",
        animalResultPage: 1,
        searchanimalDetails: [],
      });
    }
  };

  handleOnScrollBottom = () => {
    let { TotalCount, page, ResultTotalCount, ResultPage, isSearch, offset } =
      this.state;
    if (isSearch !== "" && ResultTotalCount !== ResultPage) {
      this.handleInputChange(ResultPage + 1, isSearch);
      this.setState({
        ResultPage: ResultPage + 1,
      });
    } else {
      if (TotalCount * 100 !== offset && isSearch == "") {
        this.fetchAllTrainer(offset + 100, []);
        this.setState({
          offset: offset + 100,
        });
      }
    }
  };
  handleOnScrollBottomJockeyTrainer = () => {
    let { TotalCount, ResultTotalCount, ResultPage, isSearch, offset } =
      this.state;
    if (isSearch !== "" && ResultTotalCount !== ResultPage) {
      this.handleInputChangeJockeyTrainer(ResultPage + 1, isSearch);
      this.setState({
        ResultPage: ResultPage + 1,
      });
    } else {
      if (TotalCount * 100 !== offset && isSearch == "") {
        this.fetchAllJockeyTrainer(offset + 100, []);
        this.setState({
          offset: offset + 100,
        });
      }
    }
  };

  handleAnimalOnScrollBottom = () => {
    let {
      AnimalTotalCount,
      animalPage,
      AnimalResultTotalCount,
      animalResultPage,
      isanimalSearch,
    } = this.state;

    if (isanimalSearch !== "" && AnimalResultTotalCount !== animalResultPage) {
      this.handleAnimalInputChange(animalResultPage + 1, isanimalSearch);
      this.setState({
        animalResultPage: animalResultPage + 1,
      });
    } else {
      if (AnimalTotalCount !== animalPage && isanimalSearch == "") {
        this.fetchAllAnimal(animalPage + 1, []);
        this.setState({
          animalPage: animalPage + 1,
        });
      }
    }
  };

  // handleJockey = (e) => {
  //   this.setState({
  //     selectedJockey: e?.value,
  //   });
  // };
  // handleAnimal = (e) => {
  //   this.setState({
  //     SelectedAnimal: e?.value,
  //   });
  // };

  // handleTrainner = (e) => {
  //   this.setState({
  //     SelectedTrainer: e?.value,
  //   });
  // };

  // fetchCurrentRunner = async (id, value) => {
  //   const { match } = this.props;
  //   const { status, data } = await axiosInstance.get(`/race/participant/${id}`);
  //   if (status === 200) {
  //     this.setState({ values: data.result });
  //     this.setState(() => {
  //       return {
  //         values: {
  //           ...this.state.values,
  //           jockeyId: value?.players[0]?.id,
  //           TrainnerId:
  //             match?.params?.sportname === "Horse Racing" ||
  //             match?.params?.sportname === "Harness Racing"
  //               ? value?.players[1]?.id
  //               : "",
  //         },
  //       };
  //     });
  //   }
  // };

  inputModal = (id, type, data) => () => {
    // this.fetchCurrentRunner(id, data);
    this.setState({
      isInputModalOpen: true,
      idToSend: id,
      isEditMode: type === "edit" ? true : false,
      rowToSend: data,
      selectedbarrierNumber: data?.barrierNumber,
      selectedRunnerNumber: data?.runnerNumber,
      JockeyWeight: data?.JockeyWeight,
    });
    let SelectJocky = [
      {
        label: data?.Jockey?.name,
        value: data?.Jockey?.id,
      },
    ];
    let SelectedAnimal = [
      {
        label: data?.animal?.name,
        value: data?.animal?.id,
      },
    ];
    let SelectedTrainer = [
      {
        label: data?.Trainer?.name,
        value: data?.Trainer?.id,
      },
    ];
    let SelectJockyTrainer =
      this.props?.match.params.sportname === "Horse Racing" ||
      this.props?.match.params.sportname === "Harness Racing"
        ? SelectJocky
        : SelectedTrainer;
    this.setState({
      playersDetails: SelectJockyTrainer,
      selectedJockey: data?.Jockey?.id,
      SelectedRunner: data,
      animalDetails: SelectedAnimal,
      SelectedAnimal: data?.animal?.id,
      TrainnerDetail: SelectedTrainer,
      SelectedTrainer: data?.Trainer?.id,
    });
    if (this.props?.match.params.sportname !== "Greyhound Racing") {
      this.fetchAllTrainer(this.state.offset, SelectedTrainer);
    }

    this.fetchAllAnimal(this.state.animalPage, SelectedAnimal);
    this.fetchAllJockeyTrainer(this.state.offset, SelectedTrainer, SelectJocky);
  };
  handalValidate = () => {
    let {
      // SelectedAnimal,
      JockeyWeight,
      // selectedJockey,
      // SelectedTrainer,
      selectedbarrierNumber,
      selectedRunnerNumber,
    } = this.state;

    let flag = true;
    // if (SelectedAnimal == "") {
    //   let flag = true;
    //   this.setState({
    //     errorAnimal: "This field is mandatory",
    //   });
    // } else {
    //   let flag = false;
    //   this.setState({
    //     errorAnimal: "",
    //   });
    // }

    if (JockeyWeight == "") {
      flag = false;
      this.setState({
        errorJockeyWeight: "This field is mandatory",
      });
    } else {
      this.setState({
        errorJockeyWeight: "",
      });
    }
    // if (selectedJockey == "") {
    //   let flag = true;
    //   this.setState({
    //     errorJockey: "This field is mandatory",
    //   });
    // } else {
    //   let flag = false;
    //   this.setState({
    //     errorJockey: "",
    //   });
    // }
    // if (SelectedTrainer == "") {
    //   let flag = true;
    //   this.setState({
    //     erroTrainner: "This field is mandatory",
    //   });
    // } else {
    //   let flag = false;
    //   this.setState({
    //     erroTrainner: "",
    //   });
    // }

    if (selectedbarrierNumber == "") {
      flag = false;
      this.setState({
        errobarrierNumber: "This field is mandatory",
      });
    } else {
      this.setState({
        errobarrierNumber: "",
      });
    }
    if (selectedRunnerNumber == "") {
      flag = false;
      this.setState({
        errorRunnerNumber: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRunnerNumber: "",
      });
    }
    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      const {
        rowToSend,
        idToSend,
        JockeyWeight,
        selectedRunnerNumber,
        selectedJockey,
        SelectedTrainer,
        SelectedAnimal,
        selectedbarrierNumber,
      } = this.state;
      const { match } = this.props;
      // this.setState({ isLoading: true });

      let HorsedataToPass = {
        barrierNumber: selectedbarrierNumber,
        particiantId:
          SelectedAnimal == "" ? rowToSend?.animal?.id : SelectedAnimal,
        raceId: rowToSend?.raceId,
        runnerNumber: selectedRunnerNumber,
        JockeyId: selectedJockey == "" ? rowToSend?.Jockey?.id : selectedJockey,
        TrainerId:
          SelectedTrainer == "" ? rowToSend?.Trainer?.id : SelectedTrainer,
        JockeyWeight: JockeyWeight,
      };

      let GreyhounddataToPass = {
        barrierNumber: selectedbarrierNumber,
        particiantId:
          SelectedAnimal == "" ? rowToSend?.animal?.id : SelectedAnimal,
        raceId: rowToSend?.raceId,
        runnerNumber: selectedRunnerNumber,
        // JockeyId: selectedJockey == "" ? rowToSend?.Jockey?.id : selectedJockey,
        TrainerId:
          SelectedTrainer == "" ? rowToSend?.Trainer?.id : SelectedTrainer,
        // playerId:
        //   rowToSend?.players.length > 0 &&
        //   rowToSend?.players[0].racePartiicipantMemberId !== "undefined"
        //     ? rowToSend?.players[0].racePartiicipantMemberId
        //     : "",
      };

      let dataToPass = {
        barrierNumber: selectedbarrierNumber,
        particiantId:
          SelectedAnimal == "" ? rowToSend?.animal?.id : SelectedAnimal,
        raceId: rowToSend?.raceId,
        runnerNumber: selectedRunnerNumber,
        JockeyId: selectedJockey == "" ? rowToSend?.Jockey?.id : selectedJockey,
        TrainerId:
          SelectedTrainer == "" ? rowToSend?.Trainer?.id : SelectedTrainer,

        // playerId:
        //   rowToSend?.players.length > 0 &&
        //   rowToSend?.players[0].racePartiicipantMemberId !== "undefined"
        //     ? rowToSend?.players[0].racePartiicipantMemberId
        //     : "",
      };

      let paylod =
        match.params.sportname === "Horse Racing"
          ? HorsedataToPass
          : match.params.sportname === "Greyhound Racing"
          ? GreyhounddataToPass
          : dataToPass;

      try {
        const method = "put";
        const url = `/race/participant/${idToSend}`;

        const { status } = await axiosInstance[method](url, paylod);
        if (status === 200) {
          this.toggleInputModal();
          this.setState({ isLoading: false });
          this.fetchAllRunners();
        }
      } catch (err) {
        this.setState({ isLoading: false });
      }
    } else {
    }
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      page: 1,
      TotalCount: "",
      isSearch: "",
      ResultPage: 1,
      ResultTotalCount: "",
      selectedJockey: "",
      SelectedTrainer: "",
      errorAnimal: "",
      errorJockeyWeight: "",
      errorJockey: "",
      erroTrainner: "",
      errobarrierNumber: "",
      errorRunnerNumber: "",
    });
  };

  // afterChangeRefresh = () => {
  //   this.fetchAllRunners();
  // };

  backToNavigatePage = () => {
    // const { params } = this.props.match;
    this.props.navigate(
      // `/racing/${params?.sportname}/${params?.sportid}/${params?.eventid}/${params?.eventname}`
      `/racing/all`
    );
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, runnersDetails } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < runnersDetails.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  handleRunnerDelete = async (id) => {
    this.setState({
      isDeleteLoading: "runnerDelete",
      isDeleteRunnerModalOpen: false,
    });
    try {
      const { status } = await axiosInstance.delete(`/race/participant/${id}`);
      if (status === 200) {
        this.afterChangeRefresh();
        this.setState({ isDeleteLoading: "", itemToDelete: null });
      }
    } catch (err) {
      // console.log(err);
    }
  };

  setRunnerToDelete = (id) => {
    this.setState({ itemToDelete: id, isDeleteRunnerModalOpen: true });
  };

  toggleRunnerDeleteModal = () => {
    this.setState({
      isDeleteRunnerModalOpen: !this.state.isDeleteRunnerModalOpen,
      itemToDelete: null,
    });
  };
  runnerExtraInfoInputModal = (id) => {
    this.setState({ isRunnerInfoInputModalOpen: true, runnerId: id });
    this.fetchRunnerExtraInfo(id);
  };
  runnerPreviousInfoInputModal = (id) => {
    this.setState({
      isRunnerPreviousInputModalOpen: true,
      runnerId: id,
    });
    this.fetchRunnerExtraInfo(id);
  };
  afterChangeRefresh = () => {
    this.fetchRunnerExtraInfo(this.state?.runnerId);
  };
  fetchRunnerExtraInfo = async (id) => {
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/events/runnerExtraInfo/${id}`
      );
      if (status === 200) {
        let runnerExtraInfoDatas = JSON.parse(data?.result?.info);
        this.setState({
          isLoading: false,
          previousRuns: runnerExtraInfoDatas?.previous_runs,
          runnerExtraInfoData: runnerExtraInfoDatas,
          runnerinfoData: data?.result,
        });
      }
    } catch (err) {}
  };
  toggleRunnerExtraInfoModal = () => {
    this.setState({ isRunnerInfoInputModalOpen: false });
  };
  toggleRunnerPreviousInfoModal = () => {
    this.setState({ isRunnerPreviousInputModalOpen: false });
  };

  render() {
    const { match } = this.props;
    let {
      runnersDetails,
      isLoading,
      rowPerPage,
      currentPage,
      isInputModalOpen,
      // isEditMode,
      searchInput,
      // rowToSend,
      isDeleteLoading,
      isDeleteRunnerModalOpen,
      animalDetails,
      playersDetails,
      TrainnerDetail,
      SearchplayersDetails,
      SearchTrainerDetails,
      selectedJockey,
      SelectedTrainer,
      // values,
      SelectedAnimal,
      SearchanimalDetails,
      // isanimalSearch,
      selectedbarrierNumber,
      selectedRunnerNumber,
      JockeyWeight,
      // errorAnimal,
      errorJockeyWeight,
      // errorJockey,
      // erroTrainner,
      errobarrierNumber,
      errorRunnerNumber,
      isRunnerInfoInputModalOpen,
      isRunnerPreviousInputModalOpen,
      // runnerId,
      previousRuns,
      runnerExtraInfoData,
      runnerinfoData,
    } = this.state;
    const pageNumbers = [];

    let selectedValue =
      match?.params?.sportname === "Horse Racing" ||
      match?.params?.sportname === "Harness Racing"
        ? selectedJockey
        : SelectedTrainer;

    searchInput !== "" &&
      (runnersDetails = runnersDetails?.filter(
        (obj) =>
          obj?.animal?.name
            ?.toString()
            .toLowerCase()
            .includes(searchInput.toString().toLowerCase()) ||
          obj?.players[0].name
            ?.toString()
            .toLowerCase()
            .includes(searchInput.toString().toLowerCase())
      ));

    let currentPageRow = runnersDetails;

    if (runnersDetails?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = runnersDetails.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(runnersDetails.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    const newData = previousRuns?.map((data, index) => {
      return {
        id: index + 1,
        barrier: data?.barrier ? data?.barrier : "",
        classes: data?.class ? data?.class : "",
        date: data?.date ? data?.date : "",
        distance: data?.distance ? data?.distance : "",
        finish: data?.finish ? data?.finish : "",
        in_run: data?.in_run ? data?.in_run : "",
        margin: data?.margin ? data?.margin : "",
        number: data?.number ? data?.number : "",
        number_of_runners: data?.number_of_runners
          ? data?.number_of_runners
          : data?.number_of_runners,
        prize_money: data?.prize_money ? data?.prize_money : "",
        second: data?.second ? data?.second : "",
        starting_price: data?.starting_price ? data?.starting_price : "",
        time: data?.time ? data?.time : "",
        time_ran: data?.time_ran ? data?.time_ran : "",
        track: data?.track ? data?.track : "",
        track_condition: data?.track_condition ? data?.track_condition : "",
        weight_carried: data?.weight_carried ? data?.weight_carried : "",
        winner: data?.winner ? data?.winner : "",
        third: data?.third ? data?.third : "",
      };
    });
    return (
      <>
        <Grid container className="page-content adminLogin">
          <div className="container">
            {/* <div style={{ minHeight: "800px" }}>
              {this.state.photos.map((user) => (
                <img src={user.url} height="10px" width="10px" />
              ))}
            </div> */}
            <div ref={(loadingRef) => (this.loadingRef = loadingRef)}>
              <span>Loading...</span>
            </div>
          </div>
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {isDeleteLoading === "runnerDelete" && (
              <div class="admin-delete-loader">
                <Loader />
              </div>
            )}
            <Box>
              <RacePlayersDetails />
            </Box>
            <Grid
              container
              direction="row"
              alignItems="center"
              style={{ marginBottom: "10px", marginTop: "10px" }}
            >
              <Grid item xs={6}>
                <Button
                  className="admin-btn-back"
                  onClick={this.backToNavigatePage}
                >
                  <MdKeyboardBackspace />
                </Button>
                {/* <h3
                  className="text-left admin-page-heading"
                  style={{ margin: "5px 0px 0px 5px" }}
                >
                  Runners Details {`( ${match.params.raceName} )`}
                </h3> */}
                <Typography variant="h1" align="left">
                  Runners Details
                  {/* {`( ${match.params.raceName} )`} */}
                </Typography>
              </Grid>
              <Grid
                item
                xs={6}
                style={{ display: "flex", justifyContent: "flex-end" }}
                className="admin-filter-wrap"
              >
                {/* <TextField
                  className="textfield-tracks"
                  variant="outlined"
                  color="primary"
                  size="small"
                  label="Search"
                  placeholder="Search (searches tracks)"
                  style={{
                    width: "478px",
                    color: "#D4D6D8",
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} />
                      </InputAdornment>
                    ),
                  }}
                  value={searchInput}
                  onChange={(e) =>
                    this.setState({
                      ...this.state.searchInput,
                      searchInput: e.target.value,
                    })
                  }
                /> */}
                {/* </Grid>
              <Grid item xs={2}> */}
                {/* <ButtonComponent
                  className="mt-3 admin-btn-green"
                  onClick={this.inputModal(null, "add", null)}
                  color="primary"
                  value={"Add Runner"}
                /> */}
                {/* <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "13px 24px 12px",
                  }}
                  onClick={this.inputModal(null, "add", null)}
                >
                  Add Runner
                </Button> */}
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && runnersDetails?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && runnersDetails?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>
                          {match?.params?.sportname === "Greyhound Racing"
                            ? "Dogs Name"
                            : "Horse Name"}
                        </TableCell>
                        {match?.params?.sportname === "Horse Racing" && (
                          <TableCell>Weight</TableCell>
                        )}
                        <TableCell>
                          {match?.params?.sportname === "Horse Racing"
                            ? "Jockey Name"
                            : match?.params?.sportname === "Harness Racing"
                            ? "Drivers Name"
                            : "Trainers Name"}
                        </TableCell>
                        {match?.params?.sportname === "Horse Racing" ||
                        match?.params?.sportname === "Harness Racing" ? (
                          <TableCell>Trainers Name</TableCell>
                        ) : (
                          ""
                        )}
                        <TableCell>Barrier Number</TableCell>
                        <TableCell>Runner Number</TableCell>
                        <TableCell>Runner Extra Info</TableCell>
                        <TableCell>Runner previous Info</TableCell>
                        <TableCell>Action</TableCell>
                        {/* <TableCell>Weather</TableCell> */}
                        {/* <TableCell>Action</TableCell> */}
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((runnersDetails, i) => (
                        <TableRow key={i} className=" listTable-Row">
                          <TableCell>{runnersDetails?.id}</TableCell>
                          <TableCell>{runnersDetails?.animal?.name}</TableCell>
                          {match?.params?.sportname === "Horse Racing" && (
                            <TableCell>{runnersDetails.JockeyWeight}</TableCell>
                          )}

                          <TableCell>
                            {match?.params?.sportname === "Horse Racing"
                              ? runnersDetails?.Jockey?.name
                              : match?.params?.sportname === "Harness Racing"
                              ? runnersDetails?.Jockey?.name
                              : runnersDetails?.Trainer?.name}
                          </TableCell>
                          {match?.params?.sportname === "Horse Racing" ||
                          match?.params?.sportname === "Harness Racing" ? (
                            <TableCell>
                              {runnersDetails?.Trainer?.name}
                            </TableCell>
                          ) : (
                            ""
                          )}
                          <TableCell>{runnersDetails.barrierNumber}</TableCell>
                          <TableCell>{runnersDetails.runnerNumber}</TableCell>
                          <TableCell>
                            <Button
                              style={{ cursor: "pointer" }}
                              onClick={() =>
                                this.runnerExtraInfoInputModal(
                                  runnersDetails?.id
                                )
                              }
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                          </TableCell>
                          <TableCell>
                            <Button
                              style={{ cursor: "pointer" }}
                              onClick={() =>
                                this.runnerPreviousInfoInputModal(
                                  runnersDetails?.id
                                )
                              }
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                          </TableCell>
                          <TableCell>
                            <Button
                              style={{ cursor: "pointer" }}
                              onClick={this.inputModal(
                                runnersDetails?.id,
                                "edit",
                                runnersDetails
                              )}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                          </TableCell>
                          <TableCell>
                            <Button
                              style={{ cursor: "pointer" }}
                              onClick={() =>
                                this.setRunnerToDelete(runnersDetails?.id)
                              }
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          {" "}
                          <div className="tablePagination">
                            {/* <button
                              className={
                                runnersDetails.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                runnersDetails.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                runnersDetails.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                runnersDetails.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                runnersDetails.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}
            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {/* {isEditMode ? "Edit Runners" : "Add Runners"} */}
                  Edit Runners
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                {/* <EditRunnerDetail
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllRunners={this.afterChangeRefresh}
                  raceId={match.params.id}
                  allAnimal={animalDetails}
                  allPlayers={playersDetails}
                  raceName={match.params.sportname}
                  rowToSend={rowToSend}
                /> */}

                <Box className="EditraceWrap api-provider">
                  <Grid container item xs={12}>
                    <Grid item xs={6}>
                      <Box className="inner-edit-runner select-box">
                        <label className="modal-label">
                          {match?.params?.sportname === "Greyhound Racing"
                            ? "Dogs Name"
                            : "Horse Name"}
                        </label>

                        <Select
                          className="React"
                          classNamePrefix="select"
                          onMenuScrollToBottom={(e) =>
                            this.handleAnimalOnScrollBottom(e)
                          }
                          onInputChange={(e) =>
                            this.handleAnimalInputChange(1, e)
                          }
                          value={
                            this.state.isanimalSearch
                              ? SearchanimalDetails?.find((item) => {
                                  return item?.value == SelectedAnimal;
                                })
                              : animalDetails?.find((item) => {
                                  return item?.value == SelectedAnimal;
                                })
                          }
                          onChange={(e) =>
                            this.setState({
                              SelectedAnimal: e.value,
                            })
                          }
                          options={
                            this.state.isanimalSearch
                              ? SearchanimalDetails
                              : animalDetails
                          }
                        />
                      </Box>
                    </Grid>
                    {match?.params?.sportname === "Horse Racing" && (
                      <Grid item xs={6}>
                        <Box className="inner-edit-runner">
                          <label className="text-label"> Weight</label>
                          <br />
                          <TextField
                            className="textfield-tracks"
                            variant="outlined"
                            color="primary"
                            size="small"
                            placeholder="Description"
                            value={JockeyWeight}
                            onChange={(e) =>
                              this.setState({
                                JockeyWeight: e.target.value,
                              })
                            }
                          />
                          {errorJockeyWeight ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorJockeyWeight}
                            </p>
                          ) : (
                            ""
                          )}
                        </Box>
                      </Grid>
                    )}
                    <Grid item xs={6}>
                      <Box className="inner-edit-runner select-box">
                        <label className="modal-label">
                          {match?.params?.sportname === "Horse Racing"
                            ? "Jockey Name"
                            : match?.params?.sportname === "Harness Racing"
                            ? "Driver Name"
                            : "Trainer Name"}
                        </label>
                        <Select
                          className="React"
                          classNamePrefix="select"
                          // isSearchable={false}
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomJockeyTrainer(e)
                          }
                          onInputChange={(e) =>
                            this.handleInputChangeJockeyTrainer(1, e)
                          }
                          value={
                            this.state.isSearch
                              ? SearchplayersDetails?.find((item) => {
                                  return item?.value == selectedValue;
                                })
                              : playersDetails?.find((item) => {
                                  return item?.value == selectedValue;
                                })
                          }
                          onChange={(e) => {
                            match?.params?.sportname === "Horse Racing" ||
                            match?.params?.sportname === "Harness Racing"
                              ? this.setState({
                                  selectedJockey: e.value,
                                })
                              : this.setState({
                                  SelectedTrainer: e.value,
                                });
                          }}
                          options={
                            this.state.isSearch
                              ? SearchplayersDetails
                              : playersDetails
                          }
                        />
                      </Box>
                    </Grid>
                    {match?.params?.sportname === "Horse Racing" ||
                    match?.params?.sportname === "Harness Racing" ? (
                      <Grid item xs={6}>
                        <Box className="inner-edit-runner select-box">
                          <label className="modal-label"> Trainer Name</label>
                          <Select
                            className="React"
                            classNamePrefix="select"
                            // isSearchable={false}
                            onMenuScrollToBottom={(e) =>
                              this.handleOnScrollBottom(e)
                            }
                            onInputChange={(e) => this.handleInputChange(1, e)}
                            value={
                              this.state.isSearch
                                ? SearchTrainerDetails?.find((item) => {
                                    return item?.value == SelectedTrainer;
                                  })
                                : TrainnerDetail?.find((item) => {
                                    return item?.value == SelectedTrainer;
                                  })
                            }
                            onChange={(e) =>
                              this.setState({
                                SelectedTrainer: e.value,
                              })
                            }
                            options={
                              this.state.isSearch
                                ? SearchTrainerDetails
                                : TrainnerDetail
                            }
                          />
                        </Box>
                      </Grid>
                    ) : (
                      ""
                    )}{" "}
                    <Grid item xs={6}>
                      <Box className="inner-edit-runner">
                        <label className="text-label">Barrier Number*</label>
                        <br />
                        <TextField
                          className="textfield-tracks"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Barrier Number"
                          value={selectedbarrierNumber}
                          onChange={(e) =>
                            this.setState({
                              selectedbarrierNumber: e.target.value,
                            })
                          }
                        />
                        {errobarrierNumber ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errobarrierNumber}
                          </p>
                        ) : (
                          ""
                        )}
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box className="inner-edit-runner">
                        <label className="text-label">Runner Number*</label>
                        <br />
                        <TextField
                          className="textfield-tracks"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Runner Number"
                          value={selectedRunnerNumber}
                          onChange={(e) =>
                            this.setState({
                              selectedRunnerNumber: e.target.value,
                            })
                          }
                        />
                        {errorRunnerNumber ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRunnerNumber}
                          </p>
                        ) : (
                          ""
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid container>
                    <Grid item xs={3}>
                      <div style={{ marginTop: "20px", display: "flex" }}>
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleSave()}
                          color="primary"
                          value={!isLoading ? "Update" : "Loading..."}
                          disabled={isLoading}
                        />

                        <ButtonComponent
                          onClick={this.toggleInputModal}
                          className="mr-lr-30 back-btn"
                          value="Back"
                        />
                      </div>
                    </Grid>
                  </Grid>
                </Box>
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isRunnerInfoInputModalOpen}
              onClose={this.toggleRunnerExtraInfoModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {/* {isEditMode ? "Edit Runners" : "Add Runners"} */}
                  Edit Runners Extra Info
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleRunnerExtraInfoModal}
                />
                <EditRunnerExtraInfo
                  inputModal={this.toggleRunnerExtraInfoModal}
                  id={runnerinfoData?.id}
                  runnerExtraInfoData={runnerExtraInfoData}
                  runnerInfoData={runnerinfoData}
                  previousRuns={previousRuns}
                  fetchAllRunnersInfo={() => this.afterChangeRefresh()}
                />
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isRunnerPreviousInputModalOpen}
              onClose={this.toggleRunnerPreviousInfoModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {/* {isEditMode ? "Edit Runners" : "Add Runners"} */}
                  Edit Runners previous Info
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleRunnerPreviousInfoModal}
                />
                <EditPreviousRunnerInfo
                  inputModal={this.toggleRunnerPreviousInfoModal}
                  newData={newData}
                  runnerExtraInfoData={runnerExtraInfoData}
                  id={runnerinfoData?.id}
                  fetchAllRunnersInfo={() => this.afterChangeRefresh()}
                  runnerInfoData={runnerinfoData}
                  previousRuns={previousRuns}
                  isLoading={isLoading}
                />
              </div>
            </Modal>
            <ShowModal
              isModalOpen={isDeleteRunnerModalOpen}
              onClose={this.toggleRunnerDeleteModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.handleRunnerDelete}
              onCancel={this.toggleRunnerDeleteModal}
            />
          </Grid>
        </Grid>
      </>
    );
  }
}
export default RunnersDetails;
// const SelectBox = styled.select`
//   width: 100%;
//   margin-top: 5px;
//   font-size: 16px;
//   border-radius: 3px;
//   min-height: 38px;
//   border: 1px solid #ddd;
//   padding-left: 10px;
// `;
