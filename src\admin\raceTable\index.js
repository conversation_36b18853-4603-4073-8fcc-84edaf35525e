import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import CreateRaceTable from "./CreateRaceTable";
import Pagination from '@mui/material/Pagination';
import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
import { ReactSVG } from "react-svg";
import { getFormetedDate } from "../../helpers/common";

class RaceTable extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      races: [],
      eventsAll: [],
      distanceAll: [],
      weatherAll: [],
      allSports: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      searchInput: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
    };
  }

  componentDidMount() {
    this.fetchAllRace();
  }

  async fetchAllRace() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(URLS.races);
    if (status === 200) {
      this.setState({ races: data.result, isLoading: false });
      this.fetchAllEvents();
      this.fetchAllDistance();
      this.fetchAllWeather();
      this.fetchAllSport();
    }
  }

  fetchAllEvents = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.events);
    if (status === 200) {
      this.setState({ eventsAll: data.result });
    }
  };

  getEvent = (id) => {
    let { eventsAll } = this.state;
    let eventName = eventsAll
      .filter((obj) => obj.id === id)
      .map((object) => object.eventName);
    return eventName;
  };

  fetchAllDistance = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.distance);
    if (status === 200) {
      this.setState({ distanceAll: data.result });
    }
  };

  getDistance = (id) => {
    let { distanceAll } = this.state;
    let distance = distanceAll
      .filter((obj) => obj.id === id)
      .map((object) => object.name);
    return distance;
  };

  fetchAllWeather = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.weather);
    if (status === 200) {
      this.setState({ weatherAll: data.result });
    }
  };

  getWeather = (id) => {
    let { weatherAll } = this.state;
    let name = weatherAll
      .filter((obj) => obj.id === id)
      .map((object) => object.weatherType);
    return name;
  };

  async fetchAllSport() {
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      this.setState({ allSports: data.result });
    }
  }

  getSports = (id) => {
    let { allSports } = this.state;
    let sportName = allSports
      .filter((obj) => obj.id === id)
      .map((object) => object.sportName);
    return sportName;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllRace();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.races}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllRace();
        });
        this.setActionMessage(true, "Success", "Race Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, races } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < races.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  render() {
    var {
      races,
      isLoading,
      isModalOpen,
      idToSend,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      distanceAll,
      eventsAll,
      weatherAll,
      allSports,
      searchInput,
    } = this.state;
    const pageNumbers = [];

    searchInput !== "" &&
      (races = races?.filter((obj) =>
        obj?.raceName
          ?.toString()
          .toLowerCase()
          .includes(searchInput.toString().toLowerCase())
      ));

    let currentPageRow = races;

    if (races?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = races.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(races.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12}>
            <Paper className="pageWrapper">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}

              <Grid container direction="row" alignItems="space-around">
                <Grid item xs={8}>
                  <h3 className="text-left">Race Table</h3>
                </Grid>
                <Grid item xs={2}>
                  <input
                    type="text"
                    className=""
                    placeholder="search"
                    value={searchInput}
                    onChange={(e) =>
                      this.setState({
                        ...this.state.searchInput,
                        searchInput: e.target.value,
                      })
                    }
                    style={{
                      fontSize: "16px",
                      borderRadius: "3px",
                      minHeight: "40px",
                      border: "1px solid #ddd",
                      paddingLeft: "10px",
                    }}
                  />
                </Grid>
                <Grid item xs={2}>
                  <ButtonComponent
                    className="addButton admin-btn-green"
                    onClick={this.inputModal(null, "create")}
                    color="primary"
                    value="Add New"
                  />
                </Grid>
              </Grid>
              {isLoading && <Loader />}
              {!isLoading && races.length === 0 && <p>No Data Available</p>}
              {!isLoading && races.length > 0 && (
                <>
                  <TableContainer component={Paper}>
                    <Table
                      className="listTable api-provider-listTable"
                      aria-label="simple table"
                    >
                      <TableHead>
                        <TableRow>
                          <TableCell>DID</TableCell>
                          <TableCell>Event</TableCell>
                          {/* <TableCell>Race Id</TableCell> */}
                          <TableCell>Race Name</TableCell>
                          <TableCell>Description</TableCell>
                          <TableCell>Distance</TableCell>
                          <TableCell>Track</TableCell>
                          <TableCell>Sport</TableCell>
                          <TableCell>Start Date</TableCell>
                          <TableCell>Start Time Date</TableCell>
                          <TableCell>comment</TableCell>
                          <TableCell>Weather</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {currentPageRow?.map((race, i) => (
                          <TableRow key={i}>
                            <TableCell>{race.id}</TableCell>
                            <TableCell>{this.getEvent(race.eventId)}</TableCell>
                            {/* <TableCell>{race.raceId}</TableCell> */}
                            <TableCell>{race.raceName}</TableCell>
                            <TableCell>{race.description}</TableCell>
                            <TableCell>
                              {this.getDistance(race.distance)}
                            </TableCell>
                            <TableCell>{"1"}</TableCell>
                            <TableCell>
                              {this.getSports(race.sportId)}
                            </TableCell>
                            <TableCell>
                              {getFormetedDate(race.startDate)}
                            </TableCell>
                            <TableCell>
                              {getFormetedDate(race.startTimeDate)}
                            </TableCell>
                            <TableCell>{race.comment}</TableCell>
                            <TableCell>
                              {this.getWeather(race.weather)}
                            </TableCell>
                            <TableCell>
                              <EditIcon
                                onClick={this.inputModal(race.id, "edit")}
                                color="primary"
                                className="mr10 cursor iconBtn admin-btn-green"
                              />
                              <DeleteOutlineIcon
                                onClick={this.setItemToDelete(race.id)}
                                color="secondary"
                                className="mr10 cursor iconBtn admin-btn-orange"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <div className="tablePagination">
                    <button
                      className={
                        races.length / rowPerPage > 1
                          ? "btn-navigation"
                          : "btn-navigation-disabled"
                      }
                      onClick={() => this.handlePaginationButtonClick("prev")}
                      disabled={races.length / rowPerPage > 1 ? false : true}
                    >
                      <ReactSVG src={arrowLeft} />
                    </button>
                    <Pagination
                      hideNextButton
                      hidePrevButton
                      disabled={races.length / rowPerPage > 1 ? false : true}
                      page={currentPage}
                      onChange={this.handlePaginationClick}
                      count={pageNumbers[pageNumbers?.length - 1]}
                      siblingCount={2}
                      boundaryCount={1}
                      size="small"
                    />
                    <button
                      className={
                        races.length / rowPerPage > 1
                          ? "btn-navigation"
                          : "btn-navigation-disabled"
                      }
                      onClick={() => this.handlePaginationButtonClick("next")}
                      disabled={races.length / rowPerPage > 1 ? false : true}
                    >
                      <ReactSVG src={arrowRight} />
                    </button>
                  </div>
                </>
              )}
            </Paper>

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Race Table" : "Edit Race Table"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateRaceTable
                  inputModal={this.toggleInputModal}
                  id={idToSend}
                  isEditMode={isEditMode}
                  fetchAllRace={this.afterChangeRefresh}
                  eventsAll={eventsAll}
                  distanceAll={distanceAll}
                  weatherAll={weatherAll}
                  allSports={allSports}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default RaceTable;
