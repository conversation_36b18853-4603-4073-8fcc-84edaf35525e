import React, { Component, createRef } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
} from "@mui/material";
import Select from "react-select";
import axiosInstance from "../../../helpers/Axios";
import { ActionMessage, Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import Pagination from '@mui/material/Pagination';

export default class ActivityTableModal extends Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      activityData: [],
      reviewsCount: 0,
      isLoading: false,
      selectedReviewStatus: null,
      selectedModalNewsStatus: null,
      currentPage: 1,
      rowPerPage: 10,
      totalCount: 0,
      // offset: 0,
      itemToDelete: null,
      isModalOpen: false,
      totalPage: 0,
    };
  }

  componentDidMount() {
    this.fetchAllActivities();
  }

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  async fetchAllActivities() {
    let { rowPerPage } = this.state;
    var { userId } = this.props;
    this.setState({ isLoading: true });

    try {
      let passApi = `/user/getUserReport/${userId}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          activityData: data?.result,
          isLoading: false,
          // currentPage: page,
          // totalPage: data?.totalPages,
          // totalCount: data?.totalCount,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  render() {
    var {
      messageBox,
      isLoading,
      activityData,
      selectedReviewStatus,
      selectedModalNewsStatus,
      isModalOpen,
      currentPage,
      rowPerPage,
      totalPage,
      totalCount,
    } = this.state;

    const pageNumbers = [];
    let currentPageRow = activityData;

    if (activityData?.length > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = activityData?.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(activityData?.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          {/* <Select
            className="React cricket-select  external-select"
            classNamePrefix="select"
            placeholder="Select Status"
            value={newsStatusOption?.find((item) => {
              return item?.label == selectedReviewStatus;
            })}
            // //   isLoading={isLoading}
            onChange={(e) => this.handleReviewStatusChange(e)}
            options={newsStatusOption}
          /> */}

          <Grid item xs={12} style={{ marginTop: "10px" }}>
            <Paper className="pageWrapper api-provider table-height modalTable">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              {isLoading ? (
                <Box className="message">
                  <Loader />{" "}
                </Box>
              ) : activityData?.length > 0 ? (
                <>
                  {" "}
                  <TableContainer>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow className="table-rows listTable-Row">
                          <TableCell>Id</TableCell>
                          <TableCell>Name</TableCell>
                          <TableCell>Clicks</TableCell>
                          <TableCell>Featured Clicks</TableCell>
                          {/* <TableCell style={{ width: "25%" }}>Status</TableCell> */}
                          {/* <TableCell>Action</TableCell> */}
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {currentPageRow?.map((data, i) => (
                          <TableRow
                            key={i}
                            className="table-rows listTable-Row"
                          >
                            <TableCell>{data?.Bookkeeper?.id}</TableCell>
                            <TableCell>{data?.Bookkeeper?.name}</TableCell>
                            <TableCell>{data?.totalClicks}</TableCell>
                            <TableCell>{data?.featuredClicks}</TableCell>
                            {/* <TableCell>
                              <Select
                                className="React"
                                classNamePrefix="select"
                                placeholder="Update Status"
                                value={newsModalStatusOption?.find((item) => {
                                  return item?.label === data?.publish;
                                })}
                                onChange={(e) =>
                                  this.handleStatusUpdateChange(e, data?.id)
                                }
                                options={newsModalStatusOption}
                              />
                            </TableCell>
                            <TableCell>
                              <Button
                                style={{ minWidth: "0px" }}
                                onClick={this.setItemToDelete(data?.id)}
                                className="table-btn"
                              >
                                Delete
                              </Button>
                            </TableCell> */}
                          </TableRow>
                        ))}
                        {/* <TableRow>
                          <TableCell
                            colSpan={100}
                            className="pagination"
                          >
                            <div className="tablePagination reviewPagination">
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  activityData?.length / rowPerPage > 1
                                    ? false
                                    : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />
                            </div>
                          </TableCell>
                        </TableRow> */}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <ShowModal
                    isModalOpen={isModalOpen}
                    onClose={this.toggleModal}
                    Content="Are you sure you want to delete?"
                    onOkayLabel="Yes"
                    onOkay={this.deleteItem}
                    onCancel={this.toggleModal}
                  />
                </>
              ) : (
                <Box className="message tablePadding">
                  No Activities Available
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </>
    );
  }
}
