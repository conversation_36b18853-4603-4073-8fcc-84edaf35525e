import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../teamsport.scss";
import moment from "moment";
import TableCellWithBTags from "../MarketError/ErrorHighlight";

const StatusOptions = [
  { label: "open", value: "open" },
  { label: "completed", value: "completed" },
  { label: "inprogress", value: "inprogress" },
];

const typeOptions = [
  {
    label: "All",
    value: 1,
  },
  {
    label: "Event",
    value: 2,
  },
  {
    label: "Player",
    value: 3,
  },
  {
    label: "Team",
    value: 4,
  },
  {
    label: "Tournament",
    value: 5,
  },
  {
    label: "Season",
    value: 6,
  },
];

class EventError extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      itemToDeleteData: {},
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      EventErrorlist: [],
      filteredEventErrorlist: [],
      EventErrorCount: 0,
      errorRequire: "",
      pathName: "",
      teamsports: {
        name: "",
        api: "",
        key: "",
      },
      errorStatus: "",
      errorId: "",
      providersDetails: [],
      selectedProvider: 0,
      serachValue: "",
      checkBoxValues: [],
      isSelectedLogsOpen: false,
      isDeletedAllOpen: false,
      errorFilter: "",
    };
  }

  componentDidMount() {
    this.fetchAllEventError(0, "", 0, "");
    this.fetchProviders();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllEventError(
        this.state.offset,
        this.state?.serachValue,
        this.state?.selectedProvider,
        this.state?.errorFilter
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEventError(0, "", 0, "");
      this.fetchProviders();
      this.setState({
        offset: 0,
        currentPage: 1,
        checkBoxValues: [],
        errorFilter: "",
        serachValue: "",
        selectedProvider: 0,
      });
    }
  }

  isFrontendFilteredSport = () =>
    this.props.match.path?.includes("cricket") ||
    this.props.match.path?.includes("rugbyleague") ||
    this.props.match.path?.includes("australianrules") ||
    this.props.match.path?.includes("soccer");

  async fetchAllEventError(page, search, apiProvider, filterType) {
    let { rowPerPage, offset, teamsports } = this.state;
    this.setState({ isLoading: true });

    try {
      // For cricket, rugbyleague, australianrules and soccer, we'll get all data without filters
      const isFrontendFilteredSport = this.isFrontendFilteredSport();

      const passApi = isFrontendFilteredSport
        ? this.props.match.path?.includes("cricket")
          ? `crickets/error/event`
          : this.props.match.path?.includes("rugbyleague")
          ? `rls/error/event?SportId=12`
          : this.props.match.path?.includes("australianrules")
          ? `ar/error/event`
          : `soccer/error/event`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/error/event?limit=${rowPerPage}&offset=${page}&SportId=13&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("basketball")
        ? `nba/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("afl")
        ? `afl/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("golf")
        ? `golf/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("mma")
        ? `mma/error/event?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
        : `rls/error/event?limit=${rowPerPage}&offset=${page}&SportId=14&search=${search}&ApiProviderId=${
            apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`;

      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let filteredData = data?.result?.rows;
        let totalCount = data?.result?.count;

        this.setState({
          EventErrorlist: filteredData,
          filteredEventErrorlist: filteredData,
          isLoading: false,
          EventErrorCount: totalCount,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isLoading: false,
      });
    }
  }
  handalValidate = () => {
    let { errorStatus } = this.state;

    let flag = true;
    if (!errorStatus) {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    return flag;
  };

  // handleSave = async () => {
  //     if (this.handalValidate()) {
  //         this.setState({ isLoading: true, isEditMode: false });
  //         const payload = {
  //             name: this.state?.EventErrorValues?.EventErrorName,
  //             rapidId: this.state?.EventErrorValues?.rapidId,
  //             SportId: this.props.match.path?.includes("cricket")
  //                 ? 4
  //                 : this.props.match.path?.includes("rugbyleague")
  //                     ? 12
  //                     : this.props.match.path?.includes("rugbyunion")
  //                         ? 13
  //                         : 14,
  //         };
  //         // const { status } = await axiosInstance.post(`rls/category`, payload);
  //         // if (status === 200) {
  //         //     this.setState({ isLoading: false, isInputModalOpen: false });
  //         //     this.fetchAllEventError(this.state.offset);
  //         //     //   this.setActionMessage(
  //         //     //     true,
  //         //     //     "Success",
  //         //     //     `Country variation Created Successfully`
  //         //     //   );
  //         // }
  //     }
  // };

  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        errorStatus: this.state?.errorStatus,
        id: this.state.errorId,
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props?.match?.path?.includes("boxing")
          ? 6
          : this.props?.match?.path?.includes("mma")
          ? 5
          : this.props?.match?.path?.includes("soccer")
          ? 8
          : 14,
      };
      try {
        const passApi = this.props.match.path?.includes("cricket")
          ? `crickets/event/error`
          : this.props.match.path?.includes("rugbyleague")
          ? `rls/event/error`
          : this.props.match.path?.includes("rugbyunion")
          ? `rls/event/error`
          : this.props.match.path?.includes("basketball")
          ? `nba/event/error`
          : this.props.match.path?.includes("afl")
          ? `afl/event/error`
          : this.props.match.path?.includes("australianrules")
          ? `ar/event/error`
          : this.props.match.path?.includes("golf")
          ? `golf/event/error`
          : this.props.match.path?.includes("tennis")
          ? `tennis/event/error`
          : this.props.match.path?.includes("baseball")
          ? `baseball/event/error`
          : this.props.match.path?.includes("icehockey")
          ? `icehockey/event/error`
          : this.props.match.path?.includes("boxing")
          ? `boxing/event/error`
          : this.props.match.path?.includes("mma")
          ? `mma/event/error`
          : this.props.match.path?.includes("soccer")
          ? `soccer/event/error`
          : `rls/event/error`;
        const { status } = await axiosInstance.put(passApi, payload);
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEventError(
            this.state.offset,
            this.state?.serachValue,
            this.state?.selectedProvider,
            this.state?.errorFilter
          );
          this.setActionMessage(
            true,
            "Success",
            `Event error Edited Successfully`
          );
        }
      } catch {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
      itemToDeleteData: {},
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
    });
  };
  toggleSelectedLogModal = () => {
    this.setState({
      isSelectedLogsOpen: !this.state.isSelectedLogsOpen,
    });
  };
  toggleDeletedAllModal = () => {
    this.setState({
      isDeletedAllOpen: !this.state.isDeletedAllOpen,
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        errorStatus: item?.status,
        errorId: item?.id,
        isEditMode: true,
      });
    } else {
      this.setState({
        errorStatus: "",
        errorId: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id, item) => () => {
    this.setState({
      itemToDelete: id,
      isModalOpen: true,
      itemToDeleteData: item,
    });
  };
  setSelectedDelete = () => {
    this.setState({ isSelectedLogsOpen: true });
  };
  setDeleteAll = () => {
    this.setState({ isDeletedAllOpen: true });
  };
  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const {
      itemToDelete,
      itemToDeleteData,
      offset,
      serachValue,
      selectedProvider,
      errorFilter,
    } = this.state;
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/error/event/1?error=${itemToDeleteData?.error}&type=${itemToDeleteData?.type}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/error/event/1?SportId=12&error=${itemToDeleteData?.error}&type=${itemToDeleteData?.type}`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/error/event/${itemToDelete}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/error/event/${itemToDelete}`
        : this.props.match.path?.includes("afl")
        ? `afl/error/event/${itemToDelete}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/error/event/1?error=${itemToDeleteData?.error}&type=${itemToDeleteData?.type}`
        : this.props.match.path?.includes("golf")
        ? `golf/error/event/${itemToDelete}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/error/event/${itemToDelete}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/error/event/${itemToDelete}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/error/event/${itemToDelete}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/error/event/${itemToDelete}`
        : this.props.match.path?.includes("mma")
        ? `mma/error/event/${itemToDelete}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/error/event/1?error=${itemToDeleteData?.error}&type=${itemToDeleteData?.type}`
        : `rls/error/event/${itemToDelete}?SportId=14`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState(
          { itemToDelete: null, isModalOpen: false, itemToDeleteData: {} },
          () => {
            this.fetchAllEventError(
              offset,
              serachValue,
              selectedProvider,
              errorFilter
            );
          }
        );
        this.setActionMessage(true, "Success", "Deleted Successfully!");
      } else {
        this.setState({
          itemToDelete: null,
          isModalOpen: false,
          itemToDeleteData: {},
        });
      }
    } catch (err) {
      this.setState({
        itemToDelete: null,
        isModalOpen: false,
        itemToDeleteData: {},
      });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, EventErrorlist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  fetchProviders = async () => {
    try {
      let SportId = this.props.match.path?.includes("cricket")
        ? 4
        : this.props.match.path?.includes("rugbyleague")
        ? 12
        : this.props.match.path?.includes("rugbyunion")
        ? 13
        : this.props.match.path?.includes("basketball")
        ? 10
        : this.props.match.path?.includes("afl")
        ? 15
        : this.props.match.path?.includes("australianrules")
        ? 9
        : this.props.match.path?.includes("golf")
        ? 16
        : this.props.match.path?.includes("tennis")
        ? 7
        : this.props.match.path?.includes("baseball")
        ? 11
        : this.props.match.path?.includes("icehockey")
        ? 17
        : this.props.match.path?.includes("boxing")
        ? 6
        : this.props.match.path?.includes("mma")
        ? 5
        : this.props.match.path?.includes("soccer")
        ? 8
        : 14;
      const { status, data } = await axiosInstance.get(
        `apiProviders/apiprovidersports?SportId=${SportId}`
      );
      if (status === 200) {
        let newdata = [];
        let providerData = data?.result?.map((item) => {
          newdata.push({
            label: item?.ApiProvider?.providerName,
            value: item?.ApiProviderId,
          });
        });
        const allproviderData = { label: "All", value: 0 };
        let allproviderDataList = [...newdata, allproviderData];
        this.setState({
          isLoading: false,
          providersDetails: allproviderDataList?.sort((a, b) => {
            return a.value > b.value ? 1 : -1;
          }),
        });
      }
    } catch (err) {}
  };
  bookmakerName = (bookmakerId) => {
    let { providersDetails } = this.state;
    let provider = providersDetails?.filter((item) => {
      return item?.value === bookmakerId;
    });
    return provider?.[0]?.label;
  };
  handleMarketError = (error) => {
    // <TableCellWithBTags apiContent={item?.error} />

    let Apicontent = (
      <span className="danger" onClick={() => this.handleCopyClick(error)}>
        {error}
      </span>
    );

    // let Apicontent = error?.map((item, index) => {
    //   return (
    //     <>
    //       <div key={index}>
    //         <span>{item.type} : </span>
    //         <span
    //           className="danger"
    //           onClick={() => this.handleCopyClick(item.error)}
    //         >
    //           {item.error}
    //         </span>
    //       </div>
    //       <br />
    //     </>
    //   );
    // });

    return Apicontent;
  };

  handleCopyClick = (content) => {
    // Create a temporary input element to hold the content to be copied
    const tempInput = document.createElement("input");
    tempInput.value = content;
    document.body.appendChild(tempInput);

    // Select the text inside the temporary input element
    tempInput.select();
    tempInput.setSelectionRange(0, 99999); // For mobile devices

    // Execute the copy command
    document.execCommand("copy");

    // Remove the temporary input element from the DOM
    document.body.removeChild(tempInput);

    // Show a success message to the user
    // alert('Content copied to clipboard!');
  };

  isItemChecked = (item) => {
    const checkBoxValues = this.state.checkBoxValues || [];
    return checkBoxValues?.some((obj) => this.isSameItem(obj, item));
  };

  isSameItem = (a, b) => {
    // If both have id, compare by id
    if (a?.id != null && b?.id != null) {
      return a?.id === b?.id;
    }

    // Fallback match using error, type, and SportId
    return (
      a?.error === b?.error && a?.type === b?.type && a?.SportId === b?.SportId
    );
  };

  handleCheckBoxChange = (e, item) => {
    const checkBoxValues = [...(this.state.checkBoxValues || [])];

    const exists = checkBoxValues.some((obj) => this.isSameItem(obj, item));

    if (e.target.checked && !exists) {
      checkBoxValues.push(item);
    } else if (!e.target.checked && exists) {
      const updatedValues = checkBoxValues.filter(
        (obj) => !this.isSameItem(obj, item)
      );
      this.setState({ checkBoxValues: updatedValues });
      return;
    }

    this.setState({ checkBoxValues });
  };
  handleDeleteSelected = async () => {
    const {
      checkBoxValues,
      offset,
      serachValue,
      selectedProvider,
      errorFilter,
    } = this.state;
    const errorObject = checkBoxValues?.map((item) => ({
      error: item.error,
      type: item.type,
    }));

    let payload = {};

    if (
      this.props.match.path?.includes("cricket") ||
      this.props.match.path?.includes("rugbyleague") ||
      this.props.match.path?.includes("australianrules") ||
      this.props.match.path?.includes("soccer")
    ) {
      payload = {
        errorObjet: errorObject,
      };
    } else {
      payload = {
        ids: checkBoxValues?.map((item) => item?.id),
      };
    }
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/error/event/all`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/error/event/all?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/error/event/all?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/error/event/all`
        : this.props.match.path?.includes("afl")
        ? `afl/error/event/all`
        : this.props.match.path?.includes("australianrules")
        ? `ar/error/event/all`
        : this.props.match.path?.includes("golf")
        ? `golf/error/event/all`
        : this.props.match.path?.includes("tennis")
        ? `tennis/error/event/all`
        : this.props.match.path?.includes("baseball")
        ? `baseball/error/event/all`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/error/event/all`
        : this.props.match.path?.includes("boxing")
        ? `boxing/error/event/all`
        : this.props.match.path?.includes("mma")
        ? `mma/error/event/all`
        : this.props.match.path?.includes("soccer")
        ? `soccer/error/event/all`
        : `rls/error/event/all?SportId=14`;

      const { status } = await axiosInstance.post(`${passApi}`, payload);
      if (status === 200) {
        this.setState({ checkBoxValues: [], isSelectedLogsOpen: false });
        this.fetchAllEventError(
          offset,
          serachValue,
          selectedProvider,
          errorFilter
        );
        this.setActionMessage(true, "Success", "Deleted Successfully!");
      } else {
        this.setState({ checkBoxValues: [], isSelectedLogsOpen: false });
      }
    } catch (err) {
      this.setState({ checkBoxValues: [], isSelectedLogsOpen: false });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };
  handleDeleteAll = async () => {
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/error/event/all?isAll=1`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/error/event/all?isAll=1&SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/error/event/all?isAll=1&SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/error/event/all?isAll=1`
        : this.props.match.path?.includes("afl")
        ? `afl/error/event/all?isAll=1`
        : this.props.match.path?.includes("australianrules")
        ? `ar/error/event/all?isAll=1`
        : this.props.match.path?.includes("golf")
        ? `golf/error/event/all?isAll=1`
        : this.props.match.path?.includes("tennis")
        ? `tennis/error/event/all?isAll=1`
        : this.props.match.path?.includes("baseball")
        ? `baseball/error/event/all?isAll=1`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/error/event/all?isAll=1`
        : this.props.match.path?.includes("boxing")
        ? `boxing/error/event/all?isAll=1`
        : this.props.match.path?.includes("mma")
        ? `mma/error/event/all?isAll=1`
        : this.props.match.path?.includes("soccer")
        ? `soccer/error/event/all?isAll=1`
        : `rls/error/event/all?isAll=1&SportId=14`;
      const { status } = await axiosInstance.post(`${passApi}`);
      if (status === 200) {
        this.fetchAllEventError(
          this.state.offset,
          this.state?.serachValue,
          this.state?.selectedProvider,
          this.state?.errorFilter
        );
        this.setState({ checkBoxValues: [], isDeletedAllOpen: false });
        this.setActionMessage(true, "Success", "Deleted Successfully!");
      } else {
        this.setState({ checkBoxValues: [], isDeletedAllOpen: false });
      }
    } catch (err) {
      this.setState({ checkBoxValues: [], isDeletedAllOpen: false });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handleFEFilter = (filterValue, type) => {
    let filteredData = this.state.EventErrorlist || [];
    if (type === "searchFilter") {
      if (filterValue) {
        return filteredData.filter(
          (item) =>
            item.name?.toLowerCase().includes(filterValue.toLowerCase()) ||
            item.error?.toLowerCase().includes(filterValue.toLowerCase())
        );
      }
    }

    if (type === "apiProvider") {
      if (filterValue !== 0) {
        return filteredData.filter(
          (item) => item.ApiProviderId === filterValue
        );
      }
    }
    if (type === "typeFilter") {
      if (filterValue && filterValue !== "All") {
        return filteredData.filter((item) => item.type === filterValue);
      }
    }
    return filteredData;
  };

  handleErrorTypeChange = (e) => {
    const isFrontendFilteredSport = this.isFrontendFilteredSport();
    this.setState({
      errorFilter: e?.label,
      currentPage: 1,
    });

    if (isFrontendFilteredSport) {
      this.setState({
        filteredEventErrorlist:
          this.handleFEFilter(e?.label, "typeFilter") || [],
      });
    } else {
      this.fetchAllEventError(0, "", this.state?.selectedProvider, e?.label);
    }
  };

  handleClearClick = () => {
    this.setState({ serachValue: "" });
  };

  handleKeyDown = (event) => {
    var { serachValue, selectedProvider, errorFilter } = this.state;
    if (event.key === "Enter") {
      if (this.isFrontendFilteredSport()) {
        this.setState({
          filteredEventErrorlist: this.handleFEFilter(
            serachValue,
            "searchFilter"
          ),
        });
      } else {
        this.fetchAllEventError(0, serachValue, selectedProvider, errorFilter);
      }
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      EventErrorValues,
      EventErrorlist,
      filteredEventErrorlist,
      EventErrorCount,
      errorRequire,
      pathName,
      teamsports,
      errorStatus,
      providersDetails,
      selectedProvider,
      serachValue,
      checkBoxValues,
      isSelectedLogsOpen,
      isDeletedAllOpen,
      errorFilter,
    } = this.state;
    const pageNumbers = [];
    // sportType !== "" &&
    //   (EventErrorlist = EventErrorlist.filter((obj) => obj.sportTypeId == sportType));

    // let currentPageRow = EventErrorlist;

    // if (EventErrorlist?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = EventErrorlist.slice(indexOfFirstTodo, indexOfLastTodo);
    if (EventErrorCount > 0) {
      for (let i = 1; i <= Math.ceil(EventErrorCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Event Error</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Typography variant="h1" align="left">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}{" "}
                  Event Error
                </Typography>
              </Grid>

              <Grid item xs={7} className="admin-filter-wrap">
                <Select
                  className="React cricket-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Type"
                  value={
                    errorFilter &&
                    typeOptions?.find((item) => {
                      return item?.label == errorFilter;
                    })
                  }
                  // isLoading={isLoading}
                  onChange={(e) => this.handleErrorTypeChange(e)}
                  options={typeOptions}
                />
                <Select
                  className="React cricket-select error-select"
                  classNamePrefix="select"
                  placeholder="provider id"
                  menuPosition="fixed"
                  value={providersDetails?.find((item) => {
                    return item?.value == selectedProvider;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => {
                    this.setState({
                      selectedProvider: e.value,
                      currentPage: 1,
                      offset: 0,
                    });
                    if (this.isFrontendFilteredSport()) {
                      this.setState({
                        filteredEventErrorlist: this.handleFEFilter(
                          e.value,
                          "apiProvider"
                        ),
                      });
                    } else {
                      this.fetchAllEventError(
                        0,
                        this.state?.serachValue,
                        e.value,
                        errorFilter
                      );
                    }
                  }}
                  options={providersDetails}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="event-search"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={serachValue}
                  onChange={(e) => {
                    this.setState({ serachValue: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {serachValue && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    if (this.isFrontendFilteredSport()) {
                      this.setState({
                        filteredEventErrorlist: this.handleFEFilter(
                          serachValue,
                          "searchFilter"
                        ),
                      });
                    } else {
                      this.fetchAllEventError(
                        0,
                        serachValue,
                        selectedProvider,
                        errorFilter
                      );
                    }
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>

                {/* <Button
                                  variant="contained"
                                  style={{
                                      backgroundColor: "#4455C7",
                                      color: "#fff",
                                      borderRadius: "8px",
                                      textTransform: "capitalize",
                                      padding: "6px 10px",
                                      // marginTop: "5px",
                                  }}
                                  onClick={this.inputModal(null, "create")}
                              >
                                  Add New
                              </Button> */}
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{
                  marginBottom: "10px",
                  marginTop: "12px",
                  justifyContent: "space-between",
                }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={() => this.setSelectedDelete()}
                >
                  Delete Selected
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={() => this.setDeleteAll()}
                >
                  Delete All
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && filteredEventErrorlist?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && filteredEventErrorlist?.length > 0 && (
              <TableContainer component={Paper}>
                <Table
                  className="listTable market-error-table"
                  aria-label="simple table"
                >
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell style={{ width: "20px" }}>Select</TableCell>
                      <TableCell>DID</TableCell>
                      <TableCell style={{ width: "15%" }}>Name</TableCell>
                      <TableCell> Start Date </TableCell>
                      <TableCell style={{ width: "22%" }}>Error</TableCell>
                      <TableCell>Error Date</TableCell>
                      <TableCell>Error Status</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Provider</TableCell>
                      <TableCell style={{ width: "15%" }}>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {filteredEventErrorlist?.map((item, index) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={index}
                        >
                          <TableCell>
                            <Checkbox
                              className="mz-checkbox"
                              checked={this.isItemChecked(item)}
                              onChange={(e) =>
                                this.handleCheckBoxChange(e, item)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            {item?.id ? item?.id : index + 1}{" "}
                          </TableCell>
                          <TableCell>{item?.name}</TableCell>
                          <TableCell>
                            {" "}
                            {item?.startDate
                              ? moment(item?.startDate).format(
                                  "DD/MM/YYYY h:mm:ss a"
                                )
                              : ""}
                          </TableCell>
                          <TableCell>
                            {" "}
                            {item?.error
                              ? this.handleMarketError(item?.error)
                              : item?.error}{" "}
                          </TableCell>
                          <TableCell>
                            {item?.updatedAt
                              ? moment(item?.updatedAt).format(
                                  "DD/MM/YYYY h:mm:ss a"
                                )
                              : ""}
                          </TableCell>
                          <TableCell>{item?.errorStatus}</TableCell>
                          <TableCell>{item?.type}</TableCell>
                          <TableCell>
                            {this.bookmakerName(item?.ApiProviderId)}
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id, item)}
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    {this.props.match.path?.includes("cricket") ||
                    this.props.match.path?.includes("rugbyleague") ||
                    this.props.match.path?.includes("australianrules") ||
                    this.props.match.path?.includes("soccer") ? (
                      ""
                    ) : (
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                EventErrorCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />
            <ShowModal
              isModalOpen={isSelectedLogsOpen}
              onClose={this.toggleSelectedLogModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.handleDeleteSelected}
              onCancel={this.toggleSelectedLogModal}
            />
            <ShowModal
              isModalOpen={isDeletedAllOpen}
              onClose={this.toggleDeletedAllModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.handleDeleteAll}
              onCancel={this.toggleDeletedAllModal}
            />
            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">Error Update Status</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Box>
                  <Grid item xs={12}>
                    <Box className="select-box">
                      <label className="modal-label">Status</label>
                      <Select
                        className="React "
                        classNamePrefix="select"
                        menuPosition="fixed"
                        value={
                          // StatusOptions &&
                          StatusOptions?.find((op) => {
                            return op?.value === errorStatus;
                          })
                        }
                        onChange={(e) =>
                          this.setState({
                            errorStatus: e?.value,
                          })
                        }
                        options={StatusOptions && StatusOptions}
                        // options={StatusOptions}
                      />
                      {errorRequire ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorRequire}
                        </p>
                      ) : (
                        ""
                      )}
                    </Box>
                  </Grid>
                  <Grid container>
                    <Grid item xs={3}>
                      <div style={{ marginTop: "20px", display: "flex" }}>
                        <ButtonComponent
                          className="mt-3 purple"
                          onClick={this.handleUpdate}
                          color="secondary"
                          value={!isLoading ? "Update" : "Updating..."}
                          disabled={isLoading}
                          style={{ minWidth: "auto" }}
                        />

                        <Button
                          className="mr-lr-30 outlined"
                          variant="outlined"
                          // color="primary"
                          onClick={this.toggleInputModal}
                          style={{ minWidth: "auto" }}
                        >
                          Back
                        </Button>
                      </div>
                    </Grid>
                  </Grid>
                </Box>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default EventError;
