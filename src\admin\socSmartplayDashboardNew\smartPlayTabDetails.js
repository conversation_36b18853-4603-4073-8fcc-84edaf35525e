import { Box, Typography } from "@mui/material";
import React, { useState, useMemo, useEffect } from "react";
import EastIcon from "@mui/icons-material/East";
import WestIcon from "@mui/icons-material/West";
import Select from "react-select";
import UpPrice from "../../images/upArrowPrice.png";
import DownPrice from "../../images/downArrowPrice.png";
import CardSection from "./cardSection";
import { useMonthSelector } from "./useMonthSelector";
import MyBarChart from "./MyBarChart";

const cardData = [
  {
    title: "Total Players",
    value: 1000,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "Total SmatCoins in Circulation",
    value: 20000,
    unit: "coin",
    currency: null,
    change: 13.3,
    changeType: "decrease",
  },
  {
    title: "SmartCoins Purchased",
    value: 10000,
    unit: "coin",
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "Payment",
    value: 10284,
    unit: null,
    currency: "USD",
    change: 13.3,
    changeType: "increase",
  },
];

const SmartPlayTabDetails = ({ selectedYear }) => {
  // Use the custom hook for 4 selectors
  const { monthOptions, selectedMonths, setSelectedMonth } = useMonthSelector(
    selectedYear,
    4
  );

  const scoreChartData = [
    { name: "Sun", paid: 100, free: 51 },
    { name: "Mon", paid: 100, free: 33 },
    { name: "Tue", paid: 102, free: 51 },
    { name: "Wed", paid: 100, free: 51 },
    { name: "Thu", paid: 73, free: 51 },
    { name: "Fri", paid: 120, free: 51.3 },
    { name: "Sat", paid: 130, free: 58.2 },
  ];

  const multipleBarsConfig = [
    { dataKey: "paid", color: "#4455C7", label: "Paid users" },
    { dataKey: "free", color: "#FDA289", label: "Free trial" },
  ];

  const CirculationChartData = [
    { name: "Sun", circulation: 10000 },
    { name: "Mon", circulation: 9100 },
    { name: "Tue", circulation: 500 },
    { name: "Wed", circulation: 21000 },
    { name: "Thu", circulation: 5100 },
    { name: "Fri", circulation: 1000 },
  ];

  const CirculationBarsConfig = [
    {
      dataKey: "circulation",
      color: "#1C9A6C",
      label: "SmatCoins in Circulation",
    },
  ];
  const PurchasedChartData = [
    { name: "Sun", purchase: 2200, bronze: 900, silver: 800, gold: 500 },
    { name: "Mon", purchase: 1800, bronze: 700, silver: 600, gold: 500 },
    { name: "Tue", purchase: 2400, bronze: 1000, silver: 900, gold: 500 },
    { name: "Wed", purchase: 2100, bronze: 850, silver: 750, gold: 500 },
    { name: "Thu", purchase: 1700, bronze: 600, silver: 700, gold: 400 },
    { name: "Fri", purchase: 2600, bronze: 1100, silver: 900, gold: 600 },
    { name: "Sat", purchase: 3000, bronze: 1200, silver: 1000, gold: 800 },
  ];

  const PurchasedBarsConfig = [
    { dataKey: "purchase", color: "#FC6B43", label: "One-off Purchase" },
    { dataKey: "bronze", color: "#BE8565", label: "Bronze Plan" },
    { dataKey: "silver", color: "#D0CBCA", label: "Silver Plan" },
    { dataKey: "gold", color: "#E8B97B", label: "Gold Plan" },
  ];

  const PaymentChartData = [
    { name: "Sun", purchase: 100, subscription: 51 },
    { name: "Mon", purchase: 100, subscription: 33 },
    { name: "Tue", purchase: 102, subscription: 51 },
    { name: "Wed", purchase: 100, subscription: 51 },
    { name: "Thu", purchase: 73, subscription: 51 },
    { name: "Fri", purchase: 120, subscription: 51.3 },
    { name: "Sat", purchase: 130, subscription: 58.2 },
  ];

  const PaymentBarsConfig = [
    { dataKey: "purchase", color: "#FC6B43", label: "One-off Purchase" },
    { dataKey: "subscription", color: "#4455C7", label: "Subscription" },
  ];

  return (
    <>
      <Box className="details-wrap">
        <Box className="card-section">
          {cardData?.map((item, index, arr) => {
            return (
              <CardSection
                itemData={item}
                itemIndex={index}
                fullData={arr}
                key={index}
              />
            );
          })}
        </Box>
        <Box className="bar-chart-section">
          <Box className="d-flex-ss align-items-center gap-18">
            {/* Total Players */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">Total Players</Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Month"
                    options={monthOptions}
                    value={selectedMonths[0]}
                    onChange={(option) => setSelectedMonth(0, option)}
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">1000</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={UpPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <Box className="d-flex-ss align-items-center justify-content-between">
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    <WestIcon />
                    Previous Week
                  </Typography>
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    Next Week <EastIcon />
                  </Typography>
                </Box>
                <MyBarChart
                  data={scoreChartData}
                  bars={multipleBarsConfig}
                  xAxisKey="name"
                />
              </Box>
            </Box>
            {/* Total SmartCoins in Circulation */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">
                  Total SmatCoins in Circulation
                </Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Month"
                    options={monthOptions}
                    value={selectedMonths[1]}
                    onChange={(option) => setSelectedMonth(1, option)}
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">20000</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={DownPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <Box className="d-flex-ss align-items-center justify-content-between">
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    <WestIcon />
                    Previous Week
                  </Typography>
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    Next Week <EastIcon />
                  </Typography>
                </Box>
                <MyBarChart
                  data={CirculationChartData}
                  bars={CirculationBarsConfig}
                  xAxisKey="name"
                />
              </Box>
            </Box>
          </Box>
          <Box className="d-flex-ss align-items-center gap-18 mt-18">
            {/* SmartCoins Purchased */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">
                  SmartCoins Purchased
                </Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Month"
                    options={monthOptions}
                    value={selectedMonths[2]}
                    onChange={(option) => setSelectedMonth(2, option)}
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">10000</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={UpPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <Box className="d-flex-ss align-items-center justify-content-between">
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    <WestIcon />
                    Previous Week
                  </Typography>
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    Next Week <EastIcon />
                  </Typography>
                </Box>
                <MyBarChart
                  data={PurchasedChartData}
                  bars={PurchasedBarsConfig}
                  xAxisKey="name"
                />
              </Box>
            </Box>
            {/* Payment */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">Payment</Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Month"
                    options={monthOptions}
                    value={selectedMonths[3]}
                    onChange={(option) => setSelectedMonth(3, option)}
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">10284</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={UpPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <Box className="d-flex-ss align-items-center justify-content-between">
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    <WestIcon />
                    Previous Week
                  </Typography>
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    Next Week <EastIcon />
                  </Typography>
                </Box>
                <MyBarChart
                  data={PaymentChartData}
                  bars={PaymentBarsConfig}
                  xAxisKey="name"
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default SmartPlayTabDetails;
