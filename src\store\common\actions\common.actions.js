import axiosInstance from "../../../helpers/Axios";
import { config } from "../../../helpers/config";

export function loader(action) {
  return {
    type: "LOADER_VIEW",
    payload: action,
  };
}

export function errorHandler(error) {
  return {
    type: "ERROR_HANDLER",
    payload: error,
  };
}

export function authHandler(auth) {
  return {
    type: "AUTH_HANDLER",
    payload: auth,
  };
}

export function clearAuth() {
  return {
    type: "CLEAR_AUTH",
  };
}

export const removeComplatedUpcommingRace =
  (upcomingRacesItem) => (dispatch) => {
    dispatch({ type: "REMOVE_UPCOMMING_RACE", payload: upcomingRacesItem });
  };

export const fetchUpcomingRaces = (filter, country) => async (dispatch) => {
  let raceUrl = `/events/upcoming?`;
  if (typeof filter !== "undefined" && filter !== "") {
    let allremoved = filter.filter((item) => item.isChecked);
    let sportId = allremoved?.map((item) => item.id);

    raceUrl += "sportId=" + sportId.join(",");
  } else {
    raceUrl += "sportId=";
  }
  // if (typeof country !== "undefined" && country !== "") {
  //   let allcountryremoved = country.filter((item) => item.isChecked);
  //   let MeetingState = allcountryremoved?.map((item) => item.value);
  //   raceUrl += "&MeetingState=" + MeetingState.join(",");
  // } else {
  //   raceUrl += "&MeetingState=";
  // }

  axiosInstance
    .get(raceUrl)
    .then((res) => {
      dispatch({ type: "SET_UPCOMING_RACE", payload: res.data.result });
    })
    .catch((err) => console.log(err));
};
