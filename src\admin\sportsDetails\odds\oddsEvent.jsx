import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  TableHead,
  TableRow,
  TableContainer,
  Table,
  TableCell,
  TableBody,
  Button,
} from "@mui/material";
import { useParams, useNavigate } from "react-router-dom";
import { MdKeyboardBackspace } from "react-icons/md";

import { Loader } from "../../../library/common/components";
import { config } from "../../../helpers/config";
import axiosInstance from "../../../helpers/Axios";

import Bet365 from "../../../images/logo/bet365.png";
import LadBrokes from "../../../images/logo/ladbrokes.png";
import BetStar from "../../../images/logo/betstar.png";
import BookMaker from "../../../images/logo/bookmaker.png";
import Neds from "../../../images/logo/neds.png";
import PlayUp from "../../../images/logo/playup_.png";
// import palmerbet from "../../../images/logo/palmerbet.png";
// import vicbet from "../../../images/logo/vicebet.png";
import UniBet from "../../../images/logo/unibet.svg";
// import winningedge from "../../../images/logo/winningedge.png";
// import realbookie from "../../../images/logo/realbookie.png";
import Draftstars from "../../../images/logo/draftstart-thumb.svg";
import TopSport from "../../../images/logo/top-sport-thumb.svg";
import betFair from "../../../images/logo/betfair-thumb.svg";
import BlueBet from "../../../images/logo/BlueBet.png";
import BoomBet from "../../../images/logo/BoomBet.png";
import SouthernCrossBet from "../../../images/logo/SouthernCrossBet.png";
import PuntOnDogs from "../../../images/logo/Puntondogs.webp";
import BetRight from "../../../images/logo/BETRIGHT-icon.webp";
import EliteBet from "../../../images/logo/ELITEBET-icon.webp";
import GetSetBet from "../../../images/logo/GSB-icon.webp";

function OddsEvent() {
  const params = useParams();
  const history = useNavigate();
  const [isTRaceeventLoading, setisisTRaceeventLoading] = useState(false);
  const [racingData, setracingData] = useState([]);
  const [racingBookKeepers, setracingBookKeepers] = useState([]);
  const [pageHeadingData, setPageHeadingData] = useState([]);
  const [BookkeeperData, setBookKeeperData] = useState([]);

  const fetchAllEvents = async () => {
    setisisTRaceeventLoading(true);

    try {
      const { status, data } = await axiosInstance.get(
        `adminNotification/missed?eventId=${params?.eventId}`
      );

      if (status === 200) {
        let racedata = { ...data?.events?.[0] };
        let newobj = {
          ...racedata,
          race: racedata?.race?.filter(
            (obj) => obj?.raceName !== racedata?.eventName
          ),
        };
        setracingData(newobj?.race);
        setracingBookKeepers(data?.events?.[0]?.bookKeepers);
        setisisTRaceeventLoading(false);
      }
    } catch (error) {
      setisisTRaceeventLoading(false);
    }
  };
  useEffect(() => {
    fetchAllEvents();
    fetchTableHeading();
    fetchBookKeeper();
  }, []);

  const handleNavigate = (raceId) => {
    history(`/racing/odds/${params?.eventId}/${raceId}`);
  };
  const backToNavigatePage = (raceId) => {
    history(`/racing/odds`);
  };
  const handleCellColor = (item, apiProviderId) => {
    const raceCell = item?.bookKeepers?.filter((obj) => {
      return obj?.apiProviderId === apiProviderId;
    });
    // const raceCell2 = raceCell.filter((obj) => {
    //   if (obj?.isOdd === true) {
    //     return true;
    //   } else return false;
    // });
    // if (raceCell2?.[0] === true) {
    //   return "fixture";
    // } else if (raceCell2?.[0] === false) {
    //   return "notfixture";
    // } else {
    //   return "";
    // }
    const raceCell2 = raceCell?.filter((obj) => {
      if (obj?.isOdd === true) {
        return true;
      } else return false;
    });

    return raceCell?.[0]?.oddStatus === 1
      ? "fixture"
      : raceCell?.[0]?.oddStatus === 2
      ? "partialfixture"
      : "notfixture";
    // return raceCell2?.length > 0 ? "fixture" : "notfixture";
  };
  const handleIsLiveOdds = (item, apiProviderId) => {
    const raceCell = item?.bookKeepers?.filter((obj) => {
      return obj?.apiProviderId === apiProviderId;
    });
    return raceCell?.[0]?.isLive ? "live" : "";
  };

  const fetchTableHeading = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookkeeperproviders?SportId=1,2,3`
      );
      if (status === 200) {
        setPageHeadingData(data?.result);
      } else {
      }
    } catch (err) {}
  };

  const fetchBookKeeper = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/apiProviders/bookkeeperproviders`
      );
      if (status === 200) {
        setBookKeeperData(data?.result);
      } else {
      }
    } catch (err) {}
  };
  const oddsicon = (BookKeeperId) => {
    let icon = BookkeeperData?.filter(
      (obj) => obj?.BookKeeperId === BookKeeperId
    );
    let iconData = icon?.[0]?.BookKeeper;
    let longlogo = [3, 4, 8, 10, 15];
    return (
      <img
        className="square-bookmaker"
        src={
          iconData?.small_logo?.includes("uploads")
            ? config.mediaUrl + iconData?.small_logo
            : iconData?.small_logo
        }
        alt="Bookkeeper"
      />
    );
  };
  return (
    <>
      <Box className="fixture-colleps-wrap">
        <Box className="racing-colleps">
          <Box className="colleps-accordion">
            <Box className="accordion_details event">
              <Button
                className="admin-btn-back"
                style={{ height: "40px" }}
                onClick={backToNavigatePage}
              >
                <MdKeyboardBackspace />
              </Button>
              <TableContainer className="fixture-table-wrap">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell className="rtable-hc1"></TableCell>
                      {pageHeadingData?.map((id) => (
                        <TableCell className="rt-thead">
                          {oddsicon(id?.BookKeeperId)}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                </Table>
              </TableContainer>

              <Box className="fixture-wrape">
                <Typography className="country-title">Race Name</Typography>
                {isTRaceeventLoading ? (
                  <Box style={{ paddingTop: "20px" }}>
                    <Loader />
                  </Box>
                ) : (
                  <TableContainer className="fixture-table-wrap">
                    <Table>
                      <TableBody>
                        {!isTRaceeventLoading && racingData?.length > 0
                          ? racingData?.map((item) => {
                              return (
                                <>
                                  <TableRow>
                                    <TableCell component="th">
                                      <Typography
                                        variant="h6"
                                        onClick={() => {
                                          handleNavigate(item?.id);
                                        }}
                                      >
                                        {item?.raceName}
                                      </Typography>
                                    </TableCell>
                                    {pageHeadingData?.map((id) => (
                                      <TableCell
                                        className={`${handleCellColor(
                                          item,
                                          id?.ApiProviderId
                                        )} ${handleIsLiveOdds(
                                          item,
                                          id?.ApiProviderId
                                        )}`}
                                      ></TableCell>
                                    ))}
                                  </TableRow>
                                </>
                              );
                            })
                          : "No Races Available"}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default OddsEvent;
