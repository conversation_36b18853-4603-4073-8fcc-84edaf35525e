import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { <PERSON> } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import { MdKeyboardBackspace } from "react-icons/md";
import SearchIcons from "../../../../images/searchIcon.svg";
import axiosInstance from "../../../../helpers/Axios";
import { ActionMessage, Loader } from "../../../../library/common/components";
import ButtonComponent from "../../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import "../../teamsport.scss";
import "./lineupEvent.scss";
import { config } from "../../../../helpers/config";
import ShowModal from "../../../../components/common/ShowModal/ShowModal";
// let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const CricketPositionOption = [
  {
    label: "Batting Allrounder",
    value: "Batting Allrounder",
  },
  {
    label: "Bowling Allrounder",
    value: "Bowling Allrounder",
  },
  {
    label: "Batter",
    value: "Batter",
  },
  {
    label: "Bowler",
    value: "Bowler",
  },
  {
    label: "WK-Batter",
    value: "WK-Batter",
  },
];

const RLPositionOption = [
  { label: "Wing", value: "Wing" },
  { label: "Fullback", value: "Fullback" },
  { label: "Full Back", value: "Full Back" },
  { label: "FB", value: "FB" },
  { label: "Centre", value: "Centre" },
  { label: "Utility", value: "Utility" },
  { label: "Outside Back", value: "Outside Back" },
  { label: "Back", value: "Back" },
  { label: "Halfback", value: "Halfback" },
  { label: "half back", value: "half back" },
  { label: "Five Eighth", value: "Five Eighth" },
  { label: "Back Row", value: "Back Row" },
  { label: "Lock", value: "Lock" },
  { label: "Second Row", value: "Second Row" },
  { label: "Prop Forward", value: "Prop Forward" },
  { label: "Prop", value: "Prop" },
  { label: "Hooker", value: "Hooker" },
  { label: "Interchange", value: "Interchange" },
];

const ARPositionOption = [
  { label: "Centre Half Back", value: "Centre Half Back" },
  { label: "Rover", value: "Rover" },
  { label: "Full Back", value: "Full Back" },
  { label: "Ruck Rover", value: "Ruck Rover" },
  { label: "Forward Pocket Right", value: "Forward Pocket Right" },
  { label: "Forward Pocket Left", value: "Forward Pocket Left" },
  { label: "Back Pocket Left", value: "Back Pocket Left" },
  { label: "Half Back Flank Left", value: "Half Back Flank Left" },
  { label: "Forward Pocket", value: "Forward Pocket" },
  { label: "Full Forward", value: "Full Forward" },
  { label: "Wing Right", value: "Wing Right" },
  { label: "Half Back Flank Righ", value: "Half Back Flank Righ" },
  { label: "Wing Left", value: "Wing Left" },
  { label: "Ruck", value: "Ruck" },
  { label: "Key Forward", value: "Key Forward" },
  { label: "Key Defender", value: "Key Defender" },
  { label: "Half Forward Flank L", value: "Half Forward Flank L" },
  { label: "Centre Half Forward", value: "Centre Half Forward" },
  { label: "Back Pocket Right", value: "Back Pocket Right" },
  { label: "Half Forward Flank R", value: "Half Forward Flank R" },
  { label: "Defender", value: "Defender" },
  { label: "Forward", value: "Forward" },
  { label: "Interchange", value: "Interchange" },
  { label: "Midfielder", value: "Midfielder" },
  { label: "Emergency", value: "Emergency" },
];

const SoccerPositionOption = [
  {
    label: "Goal Keeper",
    value: "G",
  },
  {
    label: "Defender",
    value: "D",
  },
  {
    label: "Midfielder",
    value: "M",
  },
  {
    label: "Forward",
    value: "F",
  },
];

class LineupEvent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isSelectedEventLoading: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      EventLineupList: [],
      EventCount: 0,
      search: "",
      eventDetails: {},
      filterEventTeamData: [],
      eventTeamData: [],
      eventFilterTeamSelected: null,
      isTeamSquadsLoading: false,
      lineupValue: {
        positionType: null,
        playerId: null,
        teamId: null,
      },
      lineupId: null,
      errorPositionType: "",
      errorTeam: "",
      playerData: [],
      playerCount: 0,
      playerPage: 0,
      isPlayerSearch: "",
      searchPlayerData: [],
      searchPlayerCount: 0,
      searchPlayerPage: 0,
      isPlayerLoading: false,
    };
  }
  componentDidMount() {
    const eventId = this.props.match.params.id;
    this.fetchSelectedEventAPI(eventId);
  }

  componentDidUpdate(prevProps, prevState) {
    // if (prevState.offset !== this.state.offset) {
    //   API call if pagination add
    // }
    if (prevProps.match.path !== this.props.match.path) {
      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
        eventFilterTeamSelected: "",
        eventTeamData: [],
        filterEventTeamData: [],
      });
    }
  }

  handalValidate = () => {
    let { lineupValue, isEditMode } = this.state;
    let flag = true;
    if (lineupValue?.positionType === null) {
      flag = false;
      this.setState({
        errorPositionType: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPositionType: "",
      });
    }

    if (lineupValue?.teamId === null) {
      flag = false;
      this.setState({
        errorTeam: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTeam: "",
      });
    }

    if (lineupValue?.playerId === null && !isEditMode) {
      flag = false;
      this.setState({
        errorPlayer: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPlayer: "",
      });
    }

    return flag;
  };

  getSportId = () => {
    const { path } = this.props.match;
    if (!path) return 14;

    if (path.includes("cricket")) return 4;
    if (path.includes("rugbyleague")) return 12;
    if (path.includes("rugbyunion")) return 13;
    if (path.includes("basketball")) return 10;
    if (path.includes("afl")) return 15;
    if (path.includes("australianrules")) return 9;
    if (path.includes("golf")) return 16;
    if (path.includes("tennis")) return 7;
    if (path.includes("baseball")) return 11;
    if (path.includes("icehockey")) return 17;
    if (path.includes("boxing")) return 6;
    if (path.includes("mma")) return 5;
    if (path.includes("soccer")) return 8;

    return 14; // default
  };

  fetchSelectedEventAPI = async (id) => {
    this.setState({
      isSelectedEventLoading: true,
    });
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/event/${id}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/event/${id}?SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/event/${id}?SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/event/${id}`
      : this.props.match.path?.includes("afl")
      ? `afl/event/${id}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/event/${id}`
      : this.props.match.path?.includes("golf")
      ? `golf/event/${id}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/event/${id}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/event/${id}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/event/${id}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/event/${id}`
      : this.props.match.path?.includes("mma")
      ? `mma/event/${id}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/event/${id}`
      : `rls/event/${id}?SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(`${passApi}`);
      if (status === 200) {
        const eventData = data?.result;

        // Step 1: Only home and away in TeamNameOption
        let TeamNameOption = [
          {
            label: eventData?.homeTeam?.name,
            value: eventData?.homeTeamId,
          },
          {
            label: eventData?.awayTeam?.name,
            value: eventData?.awayTeamId,
          },
        ];

        // Step 2: Sort
        const sortedData = TeamNameOption?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });

        // Step 3: Dedupe
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });

        // Step 4: Add "All Team" to finalData only
        finalData.unshift({
          label: "All Team",
          value: 0,
        });

        // Step 5: Set state
        this.setState({
          eventDetails: eventData,
          filterEventTeamData: finalData, // includes "All Team"
          eventTeamData: TeamNameOption, // just home/away
          isSelectedEventLoading: false,
        });
        this.LineUpDetailsAPI(eventData?.id, "", "");
      } else {
        this.setState({
          isSelectedEventLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isSelectedEventLoading: false,
      });
    }
  };

  LineUpDetailsAPI = async (eventId, search, teamId) => {
    this.setState({
      isLoading: true,
    });
    const SportId = this.getSportId();
    const passApi = `/allsport/event-lineup/event/${eventId}?sportId=${SportId}&search=${
      search || ""
    }&teamId=${teamId === 0 || teamId === null ? "" : teamId}`;
    try {
      const { status, data } = await axiosInstance.get(`${passApi}`);
      if (status === 200) {
        this.setState({
          EventLineupList: data?.rows,
          isLoading: false,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isLoading: false,
      });
    }
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      const { lineupValue, eventDetails, search, eventFilterTeamSelected } =
        this.state;
      this.setState({ isLoading: true, isEditMode: false });

      let payload = {
        eventId: eventDetails?.id,
        sportId: eventDetails?.SportId,
        teamId: lineupValue?.teamId,
        playerId: lineupValue?.playerId,
        position: lineupValue?.positionType,
      };
      const passApi = "/allsport/event-lineup";
      try {
        const { status, data } = await axiosInstance.post(passApi, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            lineupValue: {
              positionType: null,
              playerId: null,
              teamId: null,
            },
          });
          this.setActionMessage(true, "Success", data?.message);
          this.LineUpDetailsAPI(
            eventDetails?.id,
            search,
            eventFilterTeamSelected
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
        }
      } catch (error) {
        this.setActionMessage(true, "Error", error?.response?.data?.message);
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
        });
      }
    }
  };

  handleUpdate = async () => {
    const {
      lineupValue,
      eventDetails,
      lineupId,
      search,
      eventFilterTeamSelected,
    } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      try {
        let payload = {
          // eventId:eventDetails?.id,
          sportId: eventDetails?.SportId,
          teamId: lineupValue?.teamId,
          position: lineupValue?.positionType,
        };

        const { status, data } = await axiosInstance.put(
          `/allsport/event-lineup/${lineupId}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            lineupValue: {
              positionType: null,
              playerId: null,
              teamId: null,
            },
          });
          this.LineUpDetailsAPI(
            eventDetails?.id,
            search,
            eventFilterTeamSelected
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  handleFilterTeamChange = (e) => {
    const { eventDetails, search } = this.state;
    this.setState({
      eventFilterTeamSelected: e.value,
    });
    this.LineUpDetailsAPI(eventDetails?.id, search, e.value);
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      isEditMode: false,
      errorPositionType: "",
      errorPlayer: "",
      errorTeam: "",
      lineupValue: {
        positionType: null,
        playerId: null,
        teamId: null,
      },
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        isEditMode: true,
        lineupId: item?.id,
        lineupValue: {
          positionType: item?.role,
          teamId: item?.teamId,
        },
      });
    } else {
      this.fetchAllPlayers(this.state.playerPage);
      this.setState({
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { itemToDelete, eventDetails } = this.state;
    const sportId = this.getSportId();
    try {
      const passApi = `/allsport/event-lineup/${itemToDelete}?sportId=${sportId}`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false, search: "" });
        this.LineUpDetailsAPI(eventDetails?.id, "", "");
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  // handlePaginationClick = (event, page) => {
  //   let { rowPerPage } = this.state;
  //   this.setState({
  //     currentPage: Number(page),
  //     offset: (Number(page) - 1) * rowPerPage,
  //   });
  // };

  handleClearClick = () => {
    const { eventDetails, eventFilterTeamSelected } = this.state;
    this.setState({ search: "" });
    this.LineUpDetailsAPI(eventDetails?.id, "", eventFilterTeamSelected);
  };

  handleKeyDown = (event) => {
    if (event.key === "Enter") {
      // API calling
      this.setState({ currentPage: 1 });
    }
  };

  fetchAllPlayers = async (playerPage) => {
    this.setState({ isPlayerLoading: true });
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/player?limit=20&offset=${playerPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/player?limit=20&offset=${playerPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/player?limit=20&offset=${playerPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/player?limit=20&offset=${playerPage}`
      : `rls/player?limit=20&offset=${playerPage}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let players = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterdata = newdata?.filter((item) => item?.value !== 0);
      let mergeData = _.unionBy(this.state?.playerData, filterdata);
      const sortedData = mergeData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });

      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        playerData: finalData,
        playerCount: Math.ceil(count),
        isPlayerLoading: false,
      });
    } else {
      this.setState({
        isPlayerLoading: false,
      });
    }
  };

  handleOnScrollBottomPlayer = (e) => {
    let {
      playerCount,
      playerPage,
      isPlayerSearch,
      searchPlayerCount,
      searchPlayerPage,
    } = this.state;
    if (
      isPlayerSearch !== "" &&
      searchPlayerCount !== Math.ceil(searchPlayerPage / 20 + 1)
    ) {
      this.handlePlayerInputChange(searchPlayerPage + 20, isPlayerSearch);
      this.setState({
        searchPlayerPage: searchPlayerPage + 20,
      });
    } else {
      if (playerCount !== Math.ceil(playerPage / 20) && isPlayerSearch == "") {
        this.fetchAllPlayers(playerPage + 20);
        this.setState({
          playerPage: playerPage + 20,
        });
      }
    }
  };

  handlePlayerInputChange = _.debounce((playerPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/player?limit=20&offset=${playerPage}&SportId=12&search=${value}`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/player?limit=20&offset=${playerPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/player?limit=20&offset=${playerPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/player?limit=20&offset=${playerPage}&search=${value}`
      : `rls/player?limit=20&offset=${playerPage}&SportId=14&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let players = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchPlayerData, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });

        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchPlayerData: finalData,
          searchPlayerCount: Math.ceil(count),
          isPlayerSearch: value.trim(),
        });
      }
    });
  }, 300);

  handlePlayerChange = (e) => {
    this.setState({
      lineupValue: {
        ...this.state.lineupValue,
        playerId: e.value,
      },
      errorPlayer: e?.value ? "" : this.state.errorPlayer,
    });
  };

  backToNavigatePage = () => {
    this.props.navigate(-1);
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isSelectedEventLoading,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      eventDetails,
      EventLineupList,
      EventCount,
      search,
      filterEventTeamData,
      eventTeamData,
      eventFilterTeamSelected,
      lineupValue,
      errorPositionType,
      errorPlayer,
      errorTeam,
      playerData,
      isPlayerSearch,
      searchPlayerData,
      isPlayerLoading,
    } = this.state;
    const pageNumbers = [];

    if (EventCount > 0) {
      for (let i = 1; i <= Math.ceil(EventCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    const propsType = this.props.match.path;

    const positionOption = propsType?.includes("cricket")
      ? CricketPositionOption
      : propsType?.includes("rugbyleague")
      ? RLPositionOption
      : propsType?.includes("australianrules")
      ? ARPositionOption
      : propsType?.includes("soccer")
      ? SoccerPositionOption
      : [];
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {isSelectedEventLoading ? (
              <Loader />
            ) : (
              <>
                {messageBox.display && (
                  <ActionMessage
                    message={messageBox.message}
                    type={messageBox.type}
                    styleClass={messageBox.styleClass}
                  />
                )}
                <Box className="bredcrumn-wrap">
                  <Breadcrumbs
                    separator="/"
                    aria-label="breadcrumb"
                    className="breadcrumb"
                  >
                    <Link underline="hover" color="inherit" to="/dashboard">
                      Home
                    </Link>
                    <Link underline="hover" color="inherit">
                      {this.props.match.path?.includes("cricket")
                        ? "Cricket"
                        : this.props.match.path?.includes("rugbyleague")
                        ? "Rugby League"
                        : this.props.match.path?.includes("rugbyunion")
                        ? "Rugby Union"
                        : this.props.match.path?.includes("basketball")
                        ? "Basketball"
                        : this.props.match.path?.includes("afl")
                        ? "American Football"
                        : this.props.match.path?.includes("australianrules")
                        ? "Australian Rules"
                        : this.props.match.path?.includes("golf")
                        ? "Golf"
                        : this.props.match.path?.includes("tennis")
                        ? "Tennis"
                        : this.props.match.path?.includes("baseball")
                        ? "Baseball"
                        : this.props.match.path?.includes("icehockey")
                        ? "Ice Hockey"
                        : this.props.match.path?.includes("boxing")
                        ? "Boxing"
                        : this.props.match.path?.includes("mma")
                        ? "Mixed Martial Arts"
                        : this.props.match.path?.includes("soccer")
                        ? "Soccer"
                        : "Rugby Union Sevens"}
                    </Link>
                    <Typography
                      className="active_p"
                      style={{ color: "#191919" }}
                    >
                      {eventDetails?.eventName}
                    </Typography>
                    <Typography className="active_p">LineUp</Typography>
                  </Breadcrumbs>
                </Box>
                <Grid container direction="row" alignItems="center">
                  <Grid item xs={3}>
                    <Box
                      style={{
                        display: "flex",
                        alignItems: "center",
                        columnGap: "20px",
                      }}
                    >
                      <Button
                        className="admin-btn-margin admin-btn-back"
                        onClick={this.backToNavigatePage}
                      >
                        <MdKeyboardBackspace />
                      </Button>
                      <Typography variant="h1" align="left">
                        LineUp
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={9} className="admin-filter-wrap">
                    <Select
                      className="React teamsport-select external-select"
                      classNamePrefix="select"
                      placeholder="All Teams"
                      value={
                        eventFilterTeamSelected &&
                        filterEventTeamData?.find((item) => {
                          return item?.value == eventFilterTeamSelected;
                        })
                      }
                      onChange={(e) => this.handleFilterTeamChange(e)}
                      menuPosition="absolute"
                      options={filterEventTeamData}
                    />
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "6px 10px",
                      }}
                      onClick={this.inputModal(null, "create")}
                      className="add-new-btn"
                    >
                      Add New
                    </Button>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    className="admin-filter-wrap"
                    style={{
                      marginBottom: "10px",
                      justifyContent: "end",
                    }}
                  >
                    <Box className="admin-filter-wrap">
                      <TextField
                        placeholder="Search "
                        size="small"
                        variant="outlined"
                        className="search-field txt-field-class"
                        onKeyDown={(e) => this.handleKeyDown(e)}
                        value={search}
                        onChange={(e) => {
                          this.setState({ search: e.target.value });
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <img src={SearchIcons} alt="icon" />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              {search && (
                                <IconButton
                                  onClick={() => this.handleClearClick()}
                                  edge="end"
                                  style={{ minWidth: "unset" }}
                                  size="large"
                                >
                                  <CancelIcon />
                                </IconButton>
                              )}
                            </InputAdornment>
                          ),
                        }}
                      />
                      <Button
                        variant="contained"
                        style={{
                          backgroundColor: "#4455c7",
                          color: "#fff",
                          borderRadius: "8px",
                          textTransform: "capitalize",
                        }}
                        onClick={() => {
                          this.LineUpDetailsAPI(
                            eventDetails?.id,
                            search,
                            eventFilterTeamSelected
                          );
                        }}
                      >
                        Search
                      </Button>
                    </Box>
                  </Grid>
                </Grid>

                {isLoading && <Loader />}
                {!isLoading && EventLineupList?.length === 0 && (
                  <p>No Data Available</p>
                )}

                {!isLoading && EventLineupList?.length > 0 && (
                  <>
                    <TableContainer component={Paper}>
                      <Table
                        className="listTable"
                        aria-label="simple table"
                        // style={{ minWidth: "max-content" }}
                      >
                        <TableHead className="tableHead-row">
                          <TableRow>
                            <TableCell>DID</TableCell>
                            <TableCell>Player ID</TableCell>
                            <TableCell>Player Name</TableCell>
                            <TableCell>Player Role</TableCell>
                            <TableCell>Team Name</TableCell>
                            <TableCell>Action</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody className="table_body">
                          <TableRow className="table_row">
                            <TableCell
                              colSpan={100}
                              className="table-seprator"
                            ></TableCell>
                          </TableRow>
                          {EventLineupList?.map((item, index) => {
                            return (
                              <TableRow
                                className="table-rows listTable-Row"
                                key={`lineUp-${index + 1}`}
                              >
                                <TableCell>{item?.id}</TableCell>
                                <TableCell>{item?.playerId}</TableCell>

                                <TableCell>
                                  {/* {this.getPlayerName(
                                    this.props.match.path,
                                    item
                                  )} */}
                                  {item?.playerName}
                                </TableCell>
                                <TableCell>{item?.role}</TableCell>
                                <TableCell>
                                  {/* {this.getTeamName(
                                    this.props.match.path,
                                    item
                                  )} */}
                                  {item?.teamName}
                                </TableCell>
                                <TableCell
                                  style={{
                                    display: "flex",
                                    borderBottom: "none",
                                  }}
                                >
                                  <Button
                                    onClick={this.inputModal(item, "edit")}
                                    style={{
                                      cursor: "pointer",
                                      minWidth: "0px",
                                    }}
                                    className="table-btn edit-btn"
                                  >
                                    Edit
                                  </Button>
                                  <Button
                                    onClick={this.setItemToDelete(item?.id)}
                                    style={{
                                      cursor: "pointer",
                                      minWidth: "0px",
                                    }}
                                    className="table-btn delete-btn"
                                  >
                                    Delete
                                  </Button>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                          {/* <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                EventCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow> */}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                )}
                {/* </Paper> */}

                <ShowModal
                  isModalOpen={isModalOpen}
                  onClose={this.toggleModal}
                  Content="Are you sure you want to delete?"
                  onOkayLabel="Yes"
                  onOkay={this.deleteItem}
                  onCancel={this.toggleModal}
                />

                <Modal
                  className="modal modal-input"
                  open={isInputModalOpen}
                  onClose={this.toggleInputModal}
                >
                  <div
                    className={"paper modal-show-scroll"}
                    style={{ position: "relative" }}
                  >
                    <h3 className="text-center">
                      {!isEditMode ? "Create New Lineup" : "Edit Lineup"}
                    </h3>
                    <CancelIcon
                      className="admin-close-icon"
                      onClick={this.toggleInputModal}
                    />
                    <Grid
                      container
                      className="page-content adminLogin text-left"
                    >
                      <Grid item xs={12} className="pageWrapper api-provider">
                        {/* <Paper className="pageWrapper api-provider"> */}
                        {/* {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )} */}
                        <Grid container>
                          {!isEditMode && (
                            <Grid
                              item
                              xs={12}
                              style={{
                                display: "flex",
                                flexDirection: "column",
                                marginBottom: "16px",
                              }}
                            >
                              <label className="modal-label">Player</label>
                              <Select
                                className="React teamsport-select event-Tournament-select premium-key-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                placeholder="Select Player"
                                onMenuScrollToBottom={(e) =>
                                  this.handleOnScrollBottomPlayer(e)
                                }
                                onInputChange={(e) =>
                                  this.handlePlayerInputChange(0, e)
                                }
                                value={
                                  isPlayerSearch
                                    ? searchPlayerData?.find((item) => {
                                        return (
                                          item?.value == lineupValue?.playerId
                                        );
                                      })
                                    : playerData?.find((item) => {
                                        return (
                                          item?.value == lineupValue?.playerId
                                        );
                                      })
                                }
                                onChange={(e) => this.handlePlayerChange(e)}
                                options={
                                  isPlayerSearch ? searchPlayerData : playerData
                                }
                                isLoading={isPlayerLoading}
                              />
                              {errorPlayer ? (
                                <p
                                  className="errorText"
                                  style={{ margin: "5px 0px 0px 0px" }}
                                >
                                  {errorPlayer}
                                </p>
                              ) : (
                                ""
                              )}
                            </Grid>
                          )}
                          <Grid
                            item
                            xs={12}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              marginBottom: "16px",
                            }}
                          >
                            <label className="modal-label">Team</label>
                            <Select
                              className="React teamsport-select event-Tournament-select premium-key-select"
                              classNamePrefix="select"
                              menuPosition="fixed"
                              placeholder="Team"
                              value={
                                lineupValue?.teamId &&
                                eventTeamData?.find((item) => {
                                  return item?.value === lineupValue?.teamId;
                                })
                              }
                              options={eventTeamData}
                              onChange={(e) => {
                                this.setState({
                                  lineupValue: {
                                    ...lineupValue,
                                    teamId: e?.value,
                                  },
                                  errorTeam: e?.value ? "" : errorTeam,
                                });
                              }}
                            />
                            {errorTeam ? (
                              <p
                                className="errorText"
                                style={{ margin: "5px 0px 0px 0px" }}
                              >
                                {errorTeam}
                              </p>
                            ) : (
                              ""
                            )}
                          </Grid>
                          <Grid
                            item
                            xs={12}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              marginBottom: "8px",
                            }}
                          >
                            <label className="modal-label">Position</label>
                            <Select
                              className="React teamsport-select event-Tournament-select premium-key-select"
                              classNamePrefix="select"
                              menuPosition="fixed"
                              placeholder="Position"
                              value={
                                lineupValue?.positionType &&
                                positionOption?.find((item) => {
                                  return (
                                    item?.value === lineupValue?.positionType
                                  );
                                })
                              }
                              options={positionOption}
                              onChange={(e) => {
                                this.setState({
                                  lineupValue: {
                                    ...lineupValue,
                                    positionType: e?.value,
                                  },
                                  errorPositionType: e?.value
                                    ? ""
                                    : errorPositionType,
                                });
                              }}
                            />
                            {errorPositionType ? (
                              <p
                                className="errorText"
                                style={{ margin: "5px 0px 0px 0px" }}
                              >
                                {errorPositionType}
                              </p>
                            ) : (
                              ""
                            )}
                          </Grid>
                        </Grid>
                        <Grid container>
                          <Grid item xs={3}>
                            <div style={{ marginTop: "20px", display: "flex" }}>
                              {!isEditMode ? (
                                <ButtonComponent
                                  className="mt-3 admin-btn-green"
                                  onClick={this.handleSave}
                                  color="primary"
                                  value={!isLoading ? "Save" : "Loading..."}
                                  disabled={isLoading}
                                />
                              ) : (
                                <ButtonComponent
                                  className="mt-3 admin-btn-green"
                                  onClick={this.handleUpdate}
                                  color="secondary"
                                  value={!isLoading ? "Update" : "Loading..."}
                                  disabled={isLoading}
                                />
                              )}

                              <ButtonComponent
                                onClick={this.toggleInputModal}
                                className="mr-lr-30 back-btn-modal"
                                value="Back"
                              />
                            </div>
                          </Grid>
                        </Grid>
                        {/* </Paper> */}
                      </Grid>
                    </Grid>
                  </div>
                </Modal>
              </>
            )}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default LineupEvent;
