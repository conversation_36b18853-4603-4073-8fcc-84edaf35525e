import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHead,
  Modal,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import Pagination from "@mui/material/Pagination";
import { ReactSVG } from "react-svg";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import "./trainers.scss";
import CreatePlayers from "../../players/CreatePlayers";
import { Loader } from "../../../library/common/components";
import _ from "lodash";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";

const Index = () => {
  const [search, setSearch] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isInputModalOpen, setIsInputModalOpen] = useState(false);
  const [idToSend, setIdToSend] = useState(null);
  const [data, setData] = useState([]);
  const [allPlayersType, setAllPlayersType] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowPerPage, setRowPerPage] = useState(20);
  const [pageNumbers] = useState([]);

  const handlePaginationButtonClick = (navDirection) => {
    if (navDirection === "prev") {
      if (currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    } else {
      if (currentPage < data.length / rowPerPage)
        setCurrentPage(currentPage + 1);
    }
  };

  const handlePaginationClick = (event, page) => {
    setCurrentPage(Number(page));
  };

  if (data?.length > 0) {
    for (let i = 1; i <= Math.ceil(data.length / rowPerPage); i++) {
      pageNumbers.push(i);
    }
  }

  const getJockey = async () => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstance.get(URLS.getJockeys);
      setData(data?.result);
      setIsLoading(false);
    } catch (err) {
      // console.log(err)
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getJockey();
    fetchAllPlayersType();
  }, []);

  const setActionMessage = (display = false, type = "", message = "") => {
    //  this.setState({ messageBox: { display, type, message } }, () =>
    //    setTimeout(
    //      () =>
    //        this.setState({
    //          messageBox: { display: false, type: "", message: "" },
    //        }),
    //      3000
    //    )
    //  );
    setmessageBox(
      {
        display,
        type,
        message,
      },
      () =>
        setTimeout(
          () => setmessageBox({ display: false, type: "", message: "" }),
          3000
        )
    );
  };

  const afterChangeRefresh = () => {
    getJockey();
  };

  const toggleInputModal = () => {
    setIsInputModalOpen(!isInputModalOpen);
  };

  const fetchAllPlayersType = async () => {
    const { status, data } = await axiosInstance.get(URLS.playersType);
    if (status === 200) {
      setAllPlayersType(data.result);
    }
  };

  const mergeModal = (item) => {
    setisMergeModalOpen(true);
    fetchModalParentTrack(0, "");
    fetchModalChildTrack(0, "");
    let Tracks = "";
    Tracks = {
      value: item?.id,
      label: item?.name,
    };
    setparentTrack(Tracks);
    setchildTrack([]);
    // this.setState({
    //   parentTrack: Tracks,
    //   childTrack: [],
    // });
  };

  const toggleMergeModal = () => {
    setisMergeModalOpen(false);
    setcreateError("");
    setparentTrack("");
    setchildTrack("");
  };

  const fetchModalParentTrack = async (ModalTrackPage, searchvalue) => {
    const passApi = `track?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${searchvalue}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(ModalTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      setModalTrackCount(Math.ceil(count));
      setModalTrackData(finalData);
    }
  };

  const handleOnScrollBottomModalParentTrack = (e, type) => {
    if (
      isModalTrackSearch !== "" &&
      searchModalTrackCount !== Math.ceil(searchModalTrackPage / 20 + 1)
    ) {
      handleModalParentTrackInputChange(
        searchModalTrackPage + 20,
        isModalTrackSearch
      );
      //  this.setState({
      //    searchModalTrackPage: searchModalTrackPage + 20,
      //  });
      setsearchModalChildTrackPage(searchModalTrackPage + 20);
    } else {
      if (
        ModalTrackCount !==
          (ModalTrackCount == 1 ? 1 : Math.ceil(ModalTrackPage / 20)) &&
        isModalTrackSearch == ""
      ) {
        fetchModalParentTrack(ModalTrackPage + 20, isModalTrackSearch);
        //  this.setState({
        //    ModalTrackPage:,
        //  });
        setModalTrackPage(ModalTrackPage + 20);
      }
    }
  };
  const handleModalParentTrackInputChange = (ModalTrackPage, value) => {
    const passApi = `track?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        setsearchModalTrack(finalData);
        setsearchModalTrackCount(Math.ceil(count));
        setisModalTrackSearch(value);
      }
    });
  };

  const fetchModalChildTrack = async (ModalTrackPage, searchvalue) => {
    const passApi = `track?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${searchvalue}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(ModalChildTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      }).filter((Track) => Track?.value !== parentTrack?.value);

      setModalChildTrackCount(Math.ceil(count));
      const dummyObject1 = { label: "AAAAA", value: 100056 };
      const dummyObject2 = { label: "AAAAAAB", value: 100057 };
      finalData?.push(dummyObject1, dummyObject2);
      setModalChildTrackData(finalData);
    }
  };

  const handleOnScrollBottomModalChildTrack = (e, type) => {
    if (
      isModalChildTrackSearch !== "" &&
      searchModalChildTrackCount !==
        Math.ceil(searchModalChildTrackPage / 20 + 1)
    ) {
      handleModalChildTrackInputChange(
        searchModalChildTrackPage + 20,
        isModalChildTrackSearch
      );
      setsearchModalChildTrackPage(searchModalChildTrackPage + 20);
    } else {
      if (
        ModalChildTrackCount !==
          (ModalChildTrackCount == 1
            ? 1
            : Math.ceil(ModalChildTrackPage / 20)) &&
        isModalChildTrackSearch == ""
      ) {
        fetchModalChildTrack(ModalChildTrackPage + 20, isModalChildTrackSearch);
        setModalChildTrackPage(ModalChildTrackPage + 20);
      }
    }
  };
  const handleModalChildTrackInputChange = (ModalTrackPage, value) => {
    const passApi = `track?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        })?.filter((Track) => Track?.value !== parentTrack?.value);

        setsearchModalChildTrack(finalData);
        setsearchModalChildTrackCount(Math.ceil(count));
        setisModalChildTrackSearch(value);
      }
    });
  };

  // const handalMergeTrack = async () => {
  //   setIsLoading(true);
  //   let payload = {
  //     parentTrainerId: parentTrack?.value,
  //     childTrainerId: childTrack?.map((item) => item?.value),
  //   };
  //   try {
  //     const passApi = `race/participant/merge/trainers`;
  //     const { status, data } = await axiosInstance.post(passApi, payload);
  //     if (status === 200) {
  //       setActionMessage(true, "Success", data?.message);
  //       setIsLoading(false);
  //       setisMergeModalOpen(false);
  //       fetchAnimal();
  //     } else {
  //       setActionMessage(true, "Error", data?.message);
  //       setIsLoading(false);
  //       setcreateError(data?.message);
  //     }
  //   } catch (err) {
  //     setActionMessage(true, "Error", err?.response?.data?.message);
  //     setIsLoading(false);
  //     setcreateError(err?.response?.data?.message);
  //   }
  // };

  const handleTrackDelete = async () => {
    setisDeleteLoading("trackDelete");
    setIsDeleteModalOpen(false);

    try {
      const { status } = await axiosInstance.delete(`track/${itemToDelete}`);
      if (status === 200) {
        afterChangeRefresh();
        setisDeleteLoading("");
        setItemToDelete(null);
      }
    } catch (err) {
      // console.log(err);
    }
  };

  const inputModal = (id) => {
    setIsInputModalOpen(true);
    setIdToSend(id);
  };

  const handlePlayersDelete = async (id) => {
    try {
      const { status } = await axiosInstance.delete(URLS.getJockeys + `/${id}`);
      if (status === 200) {
        afterChangeRefresh();
      }
    } catch (err) {}
  };

  return (
    <Grid container>
      <Grid item xs={12}>
        <Paper className="pageWrapper">
          <Box display="flex" justifyContent="space-between">
            <Typography variant="h4">Trainers</Typography>
            <TextField
              sx={{ margin: "8px" }}
              label="Search"
              size="small"
              variant="outlined"
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
            />
          </Box>
          {isLoading && <Loader />}
          {!isLoading && data.length === 0 && <p>No Data Available</p>}
          {!isLoading && data.length > 0 && (
            <TableContainer component={Paper}>
              <Table className="listTable" aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>DID</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Weight</TableCell>
                    <TableCell>Variation</TableCell>
                    <TableCell align="right">Edit</TableCell>
                    <TableCell align="right">Delete</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data
                    .filter((val) => {
                      if (search === "" || search === null) return val;
                      else if (
                        val?.name
                          .toString()
                          .toLowerCase()
                          .includes(search.toString().toLowerCase())
                      ) {
                        return val;
                      }
                    })
                    .slice(
                      currentPage * rowPerPage - rowPerPage,
                      currentPage * rowPerPage
                    )
                    .map((row, index) => (
                      <TableRow key={row?.id}>
                        <TableCell>{row?.id}</TableCell>
                        <TableCell>{row?.name}</TableCell>
                        <TableCell>{row?.weight}</TableCell>
                        <TableCell>{row?.variation}</TableCell>
                        <TableCell align="right">
                          <EditIcon
                            onClick={() => {
                              inputModal(row?.id);
                            }}
                            color="primary"
                            className="mr10 cursor iconBtn admin-btn-green"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <DeleteOutlinedIcon
                            onClick={() => {
                              handlePlayersDelete(row?.id);
                            }}
                            color="secondary"
                            className="cursor iconBtn admin-btn-orange"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
              {data?.length > 0 && (
                <div className="tablePagination trainers-pagination">
                  <button
                    className={
                      data.length / rowPerPage > 1
                        ? "btn-navigation"
                        : "btn-navigation-disabled"
                    }
                    disabled={data.length / rowPerPage > 1 ? false : true}
                    onClick={() => handlePaginationButtonClick("prev")}
                  >
                    <ReactSVG src={arrowLeft} />
                  </button>
                  <Pagination
                    hideNextButton
                    hidePrevButton
                    disabled={data.length / rowPerPage > 1 ? false : true}
                    page={currentPage}
                    onChange={handlePaginationClick}
                    count={pageNumbers[pageNumbers?.length - 1]}
                    siblingCount={2}
                    boundaryCount={1}
                    size="small"
                  />
                  <button
                    className={
                      data.length / rowPerPage > 1
                        ? "btn-navigation"
                        : "btn-navigation-disabled"
                    }
                    disabled={data.length / rowPerPage > 1 ? false : true}
                    onClick={() => handlePaginationButtonClick("next")}
                  >
                    <ReactSVG src={arrowRight} />
                  </button>
                </div>
              )}
            </TableContainer>
          )}
        </Paper>
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={toggleInputModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">{"Edit Trainers"}</h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={toggleInputModal}
            />
            <CreatePlayers
              inputModal={toggleInputModal}
              id={idToSend}
              isEditMode={true}
              fetchAllPlayers={afterChangeRefresh}
              allPlayersType={allPlayersType}
            />
          </div>
        </Modal>
      </Grid>
    </Grid>
  );
};

export default Index;
