import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import Pagination from "@mui/material/Pagination";
import Select from "react-select";
import moment from "moment-timezone";
import "./subscriptionDashboard.scss";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const statusFilterOption = [
  {
    label: "All",
    value: 0,
  },
  {
    label: "admin",
    value: 1,
  },
  {
    label: "couponcode",
    value: 2,
  },
  {
    label: "freeTier",
    value: 3,
  },
  {
    label: "paidUser",
    value: 4,
  },
];

class SubscriptionDashboard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      subscriptionList: [],
      subscriptionCount: 0,
      subscriptionStatus: "",
      isSearch: "",
    };
  }

  componentDidMount() {
    this.fetchSubscriptions(0, "", "");
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset, subscriptionStatus, isSearch } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchSubscriptions(offset, subscriptionStatus, isSearch);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchSubscriptions(0, "", "");
      this.setState({
        offset: 0,
        currentPage: 1,
        subscriptionStatus: "",
      });
    }
  }

  /**
   * Fetches subscriptions based on the given parameters
   * @param {number} page - Page number to fetch
   * @param {string} subscriptionStatus - Status of the subscription to filter by
   * @param {string} search - Search query to filter by
   */
  async fetchSubscriptions(page, subscriptionStatus, search) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        // Get purchase plan by limit, offset, subscriptionsType and search
        `/subscription/admin/get-purchase-plan?limit=${rowPerPage}&offset=${page}&subscriptionsType=${
          subscriptionStatus === "All" ? "" : subscriptionStatus
        }&search=${search}`
      );
      if (status === 200) {
        this.setState({
          subscriptionList: data?.data,
          isLoading: false,
          subscriptionCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  // Update state with new page number and offset for pagination
  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      // Calculate offset based on page number and row per page
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  // Update state with new page number and offset based on nav direction
  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  // Update status filter and reload subscriptions with new filter
  handleStatuschange = async (e) => {
    this.setState({
      subscriptionStatus: e.label,
      isLoading: true,
      offset: 0,
      currentPage: 1,
    });
    this.fetchSubscriptions(0, e.label, this.state?.isSearch);
  };

  // Clear search filter and reload subscriptions with new filter
  handleClearClick = () => {
    const { subscriptionStatus } = this.state;
    this.setState({ isSearch: "", offset: 0, currentPage: 1 });
    this.fetchSubscriptions(0, subscriptionStatus, "");
  };

  render() {
    var {
      messageBox,
      isLoading,
      rowPerPage,
      currentPage,
      subscriptionList,
      subscriptionCount,
      subscriptionStatus,
      isSearch,
    } = this.state;
    const pageNumbers = [];

    if (subscriptionCount > 0) {
      for (let i = 1; i <= Math.ceil(subscriptionCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  Subscriptions Dashboard
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Typography variant="h1" align="left">
                  Subscriptions Dashboard
                </Typography>
              </Grid>

              <Grid
                item
                xs={7}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <Select
                  className="React cricket-select sponsored-select external-select"
                  classNamePrefix="select"
                  placeholder="Select subscription Status"
                  value={statusFilterOption?.find((item) => {
                    return item?.label === subscriptionStatus;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => this.handleStatuschange(e)}
                  options={statusFilterOption}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchSubscriptions(0, subscriptionStatus, isSearch);
                    this.setState({
                      offset: 0,
                      currentPage: 1,
                    });
                  }}
                >
                  Search
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && subscriptionList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && subscriptionList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>User ID</TableCell>
                      <TableCell>User</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Plan Name</TableCell>
                      <TableCell>PlatForm</TableCell>
                      <TableCell>Payment Type</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Coupon Code</TableCell>
                      <TableCell
                        align="center"
                        style={{ cursor: "pointer", width: "10%" }}
                      >
                        Start Date{" "}
                      </TableCell>
                      <TableCell align="center" style={{ cursor: "pointer" }}>
                        End Date{" "}
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {subscriptionList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell>{item?.id}</TableCell>
                          <TableCell> {item?.UserId} </TableCell>
                          <TableCell>
                            {item?.User?.firstName + " " + item?.User?.lastName}
                          </TableCell>
                          <TableCell> {item?.User?.username}</TableCell>
                          <TableCell>{item?.SubscriptionPlan?.name}</TableCell>
                          <TableCell>{item?.plateform}</TableCell>
                          <TableCell>
                            {item?.trialStatus === "active"
                              ? "Free"
                              : item?.trialStatus === "none" ||
                                item?.trialStatus === "completed"
                              ? "Paid"
                              : ""}
                          </TableCell>
                          <TableCell>{item?.status}</TableCell>
                          <TableCell>
                            {item?.CouponCode ? item?.CouponCode?.code : ""}
                          </TableCell>
                          <TableCell align="center">
                            {item?.startAt
                              ? moment(item?.startAt)
                                  .tz(timezone)
                                  .format("YYYY/MM/DD")
                              : "-"}
                          </TableCell>
                          <TableCell align="center">
                            {item?.expireAt
                              ? moment(item?.expireAt)
                                  .tz(timezone)
                                  .format("YYYY/MM/DD")
                              : "-"}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              subscriptionCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default SubscriptionDashboard;
