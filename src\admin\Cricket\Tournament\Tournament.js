import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../cricket.scss";

class Tournament extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      TournamentValues: {
        TournamentName: "",
        rapidTournamentId: "",
        CricketCategoryId: "",
        id: "",
      },
      selectCategory: "",
      CategoryPage: 0,
      categoryData: [],
      externalCategoryData: [],
      categoryCount: 0,
      TournamentCount: 0,
      TournamentList: [],
      SelectedTournamentList: [],
      errorRequire: "",
      errorCategory: "",
      searchCategory: [],
      searchCategoryCount: 0,
      searchCategoryPage: 0,
      isCategorySearch: "",
    };
  }

  componentDidMount() {
    this.fetchAllCategory(this.state.CategoryPage, "ExternalCategory");
    this.fetchAllTournament();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllTournament();
    }
  }
  async fetchAllTournament() {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `crickets/tournament?limit=${rowPerPage}&offset=${offset}`
      );
      if (status === 200) {
        this.setState({
          TournamentList: data?.result?.rows,
          isLoading: false,
          TournamentCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  async fetchAllCategory(CategoryPage, type) {
    let { rowPerPage, offset } = this.state;

    const { status, data } = await axiosInstance.get(
      `crickets/category?limit=20&offset=${CategoryPage}`
    );
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      if (type === "ExternalCategory") {
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.externalCategoryData, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Categories",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          externalCategoryData: finalData,
          categoryCount: Math.ceil(count),
        });
      }

      if (type === "ModalCategory") {
        let filterData = _.unionBy(this.state?.categoryData, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        finalData = finalData?.filter((item) => item?.value !== 0);
        this.setState({
          categoryData: finalData,
          categoryCount: Math.ceil(count),
        });
      }
    }
  }

  handleOnScrollBottomCategory = (e, type) => {
    let {
      categoryCount,
      CategoryPage,
      isCategorySearch,
      searchCategoryCount,
      searchCategoryPage,
    } = this.state;
    if (
      isCategorySearch !== "" &&
      searchCategoryCount !== Math.ceil(searchCategoryPage / 20 + 1)
    ) {
      this.handleCategoryInputChange(searchCategoryPage + 20, isCategorySearch);
      this.setState({
        searchCategoryPage: searchCategoryPage + 20,
      });
    } else {
      if (
        categoryCount !==
          (categoryCount == 1 ? 1 : Math.ceil(CategoryPage / 20)) &&
        isCategorySearch == ""
      ) {
        this.fetchAllCategory(CategoryPage + 20, type);
        this.setState({
          CategoryPage: CategoryPage + 20,
        });
      }
    }
  };
  handleCategoryInputChange = (page, value) => {
    const passApi = `crickets/category?limit=20&offset=${page}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchCategory, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Categories",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchCategory: finalData,
          searchCategoryCount: Math.ceil(count),
          isCategorySearch: value,
        });
      }
    });
  };
  fetchModalSelectedCategory = (CategoryId, CategoryName) => {
    let seletedCategory = [
      {
        label: CategoryName,
        value: CategoryId,
      },
    ];

    this.setState({
      categoryData: CategoryId ? seletedCategory : this.state.categoryData,
    });
  };

  handleSelectCategoryChange = async (e) => {
    this.setState({
      selectCategory: e.value,
      isLoading: true,
    });
    try {
      const { status, data } = await axiosInstance.get(
        `crickets/tournament/category/${e.value}`
      );
      if (status === 200) {
        this.setState({
          SelectedTournamentList: data?.result,
          isLoading: false,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };
  handalValidate = () => {
    let { TournamentValues } = this.state;

    let flag = true;
    if (TournamentValues?.TournamentName === "") {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    if (TournamentValues?.CricketCategoryId === "") {
      flag = false;
      this.setState({
        errorCategory: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCategory: "",
      });
    }
    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        name: this.state?.TournamentValues?.TournamentName,
        rapidTournamentId: this.state?.TournamentValues?.rapidTournamentId,
        CricketCategoryId: this.state?.TournamentValues?.CricketCategoryId,
      };
      const { status } = await axiosInstance.post(
        `crickets/tournament`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllTournament();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        name: this.state?.TournamentValues?.TournamentName,
        rapidTournamentId: this.state?.TournamentValues?.rapidTournamentId,
        CricketCategoryId: this.state?.TournamentValues?.CricketCategoryId,
      };
      const { status } = await axiosInstance.put(
        `crickets/tournament/${this.state.TournamentValues?.id}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllTournament();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Edited Successfully`
        //   );
      }
    }
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
      errorCategory: "",
    });
  };

  inputModal = (item, type) => () => {
    this.fetchAllCategory(0, "ModalCategory");
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchModalSelectedCategory(
        item?.CricketCategoryId,
        item?.CricketCategory?.name
      );
      this.setState({
        TournamentValues: {
          TournamentName: item?.name,
          rapidTournamentId: item?.rapidTournamentId,
          CricketCategoryId: item?.CricketCategoryId,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        TournamentValues: {
          TournamentName: "",
          rapidTournamentId: "",
          CricketCategoryId: "",
          id: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `crickets/tournament/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllTournament();
        });
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };
  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  inputSportVariationModal = (id, name, data) => {
    this.setState({
      SportVariationModal: true,
      sportId: id,
      sportName: name,
      sportVariationData: data,
    });
  };
  toggleinputSportVariationModal = () => {
    this.setState({ SportVariationModal: false });
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      SportVariationModal,
      TournamentValues,
      CategoryPage,
      categoryData,
      externalCategoryData,
      selectCategory,
      TournamentList,
      TournamentCount,
      SelectedTournamentList,
      errorRequire,
      errorCategory,
      searchCategory,
      isCategorySearch,
    } = this.state;
    const pageNumbers = [];

    if (TournamentCount > 0) {
      for (let i = 1; i <= Math.ceil(TournamentCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    let FinalTournamentList = selectCategory
      ? SelectedTournamentList
      : TournamentList;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {/* {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )} */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Cricket
                </Link>
                <Typography className="active_p">Tournaments</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Tournaments
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                  onChange={(e) => this.setState({ sportType: e.target.value })}
                  style={{ width: "20%", marginTop: "0px" }}
                >
                  <option value="">Select Category</option>
                  {CategoryData?.length > 0 &&
                    CategoryData?.map((obj, i) => (
                      <option key={i} value={obj?.id}>
                        {obj?.categoryName}
                      </option>
                    ))}
                </SelectBox> */}
                <Select
                  className="React cricket-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Category"
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomCategory(e, "ExternalCategory")
                  }
                  onInputChange={(e) => this.handleCategoryInputChange(0, e)}
                  value={
                    isCategorySearch
                      ? searchCategory?.find((item) => {
                          return item?.value == selectCategory;
                        })
                      : externalCategoryData?.find((item) => {
                          return item?.value == selectCategory;
                        })
                  }
                  onChange={(e) => this.handleSelectCategoryChange(e)}
                  options={
                    isCategorySearch ? searchCategory : externalCategoryData
                  }
                />
                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  // value={search}
                  onChange={(e) => {
                    // setSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  // onClick={() => fetchAnimal()}
                >
                  Search
                </Button> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TournamentList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TournamentList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell style={{ width: "25%" }}>
                          rapid Tournament Id
                        </TableCell>
                        <TableCell style={{ width: "25%" }}>
                          Tournament Name
                        </TableCell>
                        {/* <TableCell style={{ width: "25%" }}>
                          variation
                        </TableCell> */}
                        {/* <TableCell style={{ width: "25%" }}>variation</TableCell> */}
                        <TableCell>Category</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {FinalTournamentList?.length > 0 ? (
                        FinalTournamentList?.map((item) => {
                          return (
                            <TableRow
                              className="table-rows listTable-Row"
                              key={item?.id}
                            >
                              <TableCell> {item?.id} </TableCell>
                              <TableCell>{item?.rapidTournamentId}</TableCell>
                              <TableCell>{item?.name}</TableCell>
                              <TableCell>
                                {item?.CricketCategory?.name}
                              </TableCell>
                              <TableCell>
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  className="table-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  className="table-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      {!selectCategory ? (
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}

                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  TournamentCount / rowPerPage > 1
                                    ? false
                                    : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />

                              {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        <></>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Tournament" : "Edit Tournament"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="cricket-text"
                      >
                        <label className="modal-label"> Tournament Name</label>
                        <TextField
                          className="cricket-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Tournament Name"
                          value={TournamentValues?.TournamentName}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                TournamentName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorRequire ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRequire}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="cricket-text"
                      >
                        <label className="modal-label">
                          rapid Tournament Id
                        </label>
                        <TextField
                          className="cricket-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Tournament Id"
                          value={TournamentValues?.rapidTournamentId}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                rapidTournamentId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Category </label>
                        <Select
                          className="React cricket-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Category"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomCategory(
                              e,
                              "ModalCategory"
                            )
                          }
                          onInputChange={(e) =>
                            this.handleCategoryInputChange(0, e)
                          }
                          value={
                            isCategorySearch
                              ? searchCategory?.find((item) => {
                                  return (
                                    item?.value ==
                                    TournamentValues?.CricketCategoryId
                                  );
                                })
                              : categoryData?.find((item) => {
                                  return (
                                    item?.value ===
                                    TournamentValues?.CricketCategoryId
                                  );
                                })
                          }
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                CricketCategoryId: e.value,
                              },
                            })
                          }
                          options={
                            isCategorySearch ? searchCategory : categoryData
                          }
                        />
                        {errorCategory ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorCategory}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Tournament;

const SelectBox = styled.select`
  width: 100%;
  margin-top: 5px;
  font-size: 16px;
  border-radius: 3px;
  min-height: 38px;
  border: 1px solid #ddd;
  padding-left: 10px;
`;
