import React, { Component, createRef } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
} from "@mui/material";
// import { variationFormModel } from "./form-constant";
import Form from "../../../../library/common/components/Form";
import ButtonComponent from "../../../../library/common/components/Button";
import ActionMessage from "../../../../library/common/components/ActionMessage";
import { URLS } from "../../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../../library/utilities";
import axiosInstance from "../../../../helpers/Axios";
import { setValidation } from "../../../../helpers/common";
import CancelIcon from "@mui/icons-material/Cancel";
import ShowModal from "../../../../components/common/ShowModal/ShowModal";
import { Loader } from "../../../../library/common/components";
import moment from "moment";
import Select from "react-select";

class CreateIdentifierEvent extends Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
      isEditMode: false,
      isInputModalOpen: false,
      isModalOpen: false,
      itemToDelete: null,
      variationToSend: "",
      addInput: "",
      idToSend: "",
      MarketVariationData: [],
      IdentifierData: [],
      providersDetails: [],
      IdentifierValue: {
        providervalue: "",
        addEventIdValue: "",
        editEventIdValue: "",
      },
      errorRequire: "",
      Identifierid: "",
      errorprovidervalue: "",
    };
  }
  componentDidMount() {
    this.fetchAllIdentifier();
    this.fetchProviders();
  }

  fetchAllIdentifier = async () => {
    this.setState({ isLoading: true });
    const passApi = this.props?.pathName?.includes("cricket")
      ? `crickets/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("rugbyleague")
      ? `rls/getIdentifiersByEventId/${this.props?.IdentifierEventId}?SportId=12`
      : this.props?.pathName?.includes("rugbyunion")
      ? `rls/getIdentifiersByEventId/${this.props?.IdentifierEventId}?SportId=13`
      : this.props?.pathName?.includes("basketball")
      ? `nba/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("afl")
      ? `afl/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("australianrules")
      ? `ar/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("golf")
      ? `golf/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("tennis")
      ? `tennis/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("baseball")
      ? `baseball/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("icehockey")
      ? `icehockey/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("boxing")
      ? `boxing/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("mma")
      ? `mma/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : this.props?.pathName?.includes("soccer")
      ? `soccer/getIdentifiersByEventId/${this.props?.IdentifierEventId}`
      : `rls/getIdentifiersByEventId/${this.props?.IdentifierEventId}?SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(passApi);

      if (status === 200) {
        this.setState({
          isLoading: false,
          //   isInputModalOpen: false,
          IdentifierData: data?.result?.eventIdentifiers,
        });
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      } else {
        this.setState({
          isLoading: false,
          //   isInputModalOpen: false,
        });
      }
    } catch (err) {
      this.setState({
        isLoading: false,
        // isInputModalOpen: false,
      });
    }
  };

  inputModal = (id, type, data) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        Identifierid: id,
        isEditMode: true,
        IdentifierValue: {
          ...this?.state?.IdentifierValue,
          providervalue: data?.ApiProviderId,
          editEventIdValue: data?.apiEventId,
        },

        // variationToSend: variation,
      });
    } else {
      this.setState({
        isEditMode: false,
        IdentifierValue: {
          ...this?.state?.IdentifierValue,
          providervalue: "",
          editEventIdValue: "",
          addEventIdValue: "",
        },
      });
    }
  };
  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
      errorprovidervalue: "",
      IdentifierValue: {
        ...this?.state?.IdentifierValue,
        providervalue: "",
        editEventIdValue: "",
        addEventIdValue: "",
      },
    });
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };
  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };
  //   setActionMessage = (display = false, type = "", message = "") => {
  //     this.setState({ messageBox: { display, type, message } }, () =>
  //       setTimeout(
  //         () =>
  //           this.setState({
  //             messageBox: { display: false, type: "", message: "" },
  //           }),
  //         3000
  //       )
  //     );
  //   };

  handalValidate = () => {
    let { IdentifierValue, isEditMode } = this.state;

    let flag = true;

    if (
      isEditMode
        ? IdentifierValue?.editEventIdValue === ""
        : IdentifierValue?.addEventIdValue === ""
    ) {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    if (IdentifierValue?.providervalue === "") {
      flag = false;
      this.setState({
        errorprovidervalue: "This field is mandatory",
      });
    } else {
      this.setState({
        errorprovidervalue: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const passApi = this.props?.pathName?.includes("cricket")
        ? `crickets/createEventIdentifiers`
        : this.props?.pathName?.includes("rugbyleague")
        ? `rls/createEventIdentifiers`
        : this.props?.pathName?.includes("rugbyunion")
        ? `rls/createEventIdentifiers`
        : this.props?.pathName?.includes("basketball")
        ? `nba/createEventIdentifiers`
        : this.props?.pathName?.includes("afl")
        ? `afl/createEventIdentifiers`
        : this.props?.pathName?.includes("australianrules")
        ? `ar/createEventIdentifiers`
        : this.props?.pathName?.includes("golf")
        ? `golf/createEventIdentifiers`
        : this.props?.pathName?.includes("tennis")
        ? `tennis/createEventIdentifiers`
        : this.props?.pathName?.includes("baseball")
        ? `baseball/createEventIdentifiers`
        : this.props?.pathName?.includes("icehockey")
        ? `icehockey/createEventIdentifiers`
        : this.props?.pathName?.includes("boxing")
        ? `boxing/createEventIdentifiers`
        : this.props?.pathName?.includes("mma")
        ? `mma/createEventIdentifiers`
        : this.props?.pathName?.includes("soccer")
        ? `soccer/createEventIdentifiers`
        : `rls/createEventIdentifiers`;
      const payload = {
        eventId: this?.props?.IdentifierEventId,
        ApiProviderId: this.state?.IdentifierValue?.providervalue,
        apiEventId: this.state?.IdentifierValue?.addEventIdValue,
      };
      try {
        const { status } = await axiosInstance.post(`${passApi}`, payload);
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllIdentifier();
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  handleUpdate = async () => {
    const payload = {
      eventId: this?.props?.IdentifierEventId,
      ApiProviderId: this.state?.IdentifierValue?.providervalue,
      apiEventId: this.state?.IdentifierValue?.editEventIdValue,
    };

    this.setState({ isLoading: true, isEditMode: true });
    const passApi = this.props?.pathName?.includes("cricket")
      ? `crickets/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("rugbyleague")
      ? `rls/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("rugbyunion")
      ? `rls/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("basketball")
      ? `nba/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("afl")
      ? `afl/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("australianrules")
      ? `ar/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("golf")
      ? `golf/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("tennis")
      ? `tennis/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("baseball")
      ? `baseball/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("icehockey")
      ? `icehockey/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("boxing")
      ? `boxing/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("mma")
      ? `mma/updateEventIdentifiers/${this.state?.Identifierid}`
      : this.props?.pathName?.includes("soccer")
      ? `soccer/updateEventIdentifiers/${this.state?.Identifierid}`
      : `rls/updateEventIdentifiers/${this.state?.Identifierid}`;
    try {
      const { status } = await axiosInstance.put(`${passApi}`, payload);
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllIdentifier();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Edited Successfully`
        //   );
      } else {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    } catch (err) {
      this.setState({ isLoading: false, isInputModalOpen: false });
    }
  };
  deleteItem = async () => {
    try {
      const passApi = this.props?.pathName?.includes("cricket")
        ? `crickets/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("rugbyleague")
        ? `rls/deleteEventIdentifiers/${this.state.itemToDelete}?SportId=12`
        : this.props?.pathName?.includes("rugbyunion")
        ? `rls/deleteEventIdentifiers/${this.state.itemToDelete}?SportId=13`
        : this.props?.pathName?.includes("basketball")
        ? `nba/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("afl")
        ? `afl/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("australianrules")
        ? `ar/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("golf")
        ? `golf/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("tennis")
        ? `tennis/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("baseball")
        ? `baseball/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("icehockey")
        ? `icehockey/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("boxing")
        ? `boxing/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("mma")
        ? `mma/deleteEventIdentifiers/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("soccer")
        ? `soccer/deleteEventIdentifiers/${this.state.itemToDelete}`
        : `rls/deleteEventIdentifiers/${this.state.itemToDelete}?SportId=14`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllIdentifier();
        });
        // this.setActionMessage(
        //   true,
        //   "Success",
        //   "Country variation Deleted Successfully!"
        // );
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
      this.setState({ itemToDelete: null, isModalOpen: false });
    }
  };

  fetchProviders = async () => {
    try {
      let SportId = this.props?.pathName?.includes("cricket")
        ? 4
        : this.props?.pathName?.includes("rugbyleague")
        ? 12
        : this.props?.pathName?.includes("rugbyunion")
        ? 13
        : this.props?.pathName?.includes("basketball")
        ? 10
        : this.props?.pathName?.includes("afl")
        ? 15
        : this.props?.pathName?.includes("australianrules")
        ? 9
        : this.props?.pathName?.includes("golf")
        ? 16
        : this.props?.pathName?.includes("tennis")
        ? 7
        : this.props?.pathName?.includes("baseball")
        ? 11
        : this.props?.pathName?.includes("icehockey")
        ? 17
        : this.props?.pathName?.includes("boxing")
        ? 6
        : this.props?.pathName?.includes("mma")
        ? 5
        : this.props?.pathName?.includes("soccer")
        ? 8
        : 14;
      const { status, data } = await axiosInstance.get(
        `apiProviders/apiprovidersports?SportId=${SportId}`
      );
      if (status === 200) {
        let newdata = [];
        let providerData = data?.result?.map((item) => {
          newdata.push({
            label: item?.ApiProvider?.providerName,
            value: item?.ApiProviderId,
          });
        });
        this.setState({
          isLoading: false,
          providersDetails: newdata?.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          }),
        });
      }
    } catch (err) {}
  };
  bookmakerName = (bookmakerId) => {
    let { providersDetails } = this.state;
    let provider = providersDetails?.filter((item) => {
      return item?.value === bookmakerId;
    });
    return provider?.[0]?.label;
  };

  render() {
    var {
      values,
      messageBox,
      isLoading,
      isEditMode,
      isInputModalOpen,
      isModalOpen,
      variationToSend,
      addInput,
      idToSend,
      MarketVariationData,
      IdentifierData,
      providersDetails,
      IdentifierValue,
      errorRequire,
      errorprovidervalue,
    } = this.state;

    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          {/* <Button
            className="modal-btn admin-btn-green"
            style={{ marginLeft: "auto", marginBottom: "10px" }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button> */}
          <Button
            variant="contained"
            style={{
              backgroundColor: "#4455C7",
              color: "#fff",
              borderRadius: "8px",
              textTransform: "capitalize",
              padding: "13px 24px 12px",
              marginLeft: "auto",
              marginBottom: "10px",
            }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button>
          <Grid item xs={12}>
            <Paper className="pageWrapper api-provider">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              {isLoading ? (
                <Box className="message">
                  <Loader />
                </Box>
              ) : IdentifierData?.length > 0 ? (
                <>
                  {" "}
                  <TableContainer>
                    <Table
                      className="listTable"
                      aria-label="simple table"
                      style={{ minWidth: "max-content" }}
                    >
                      <TableHead className="tableHead-row">
                        <TableRow className="table-rows listTable-Row">
                          <TableCell>Id</TableCell>
                          <TableCell>Api EventId</TableCell>
                          <TableCell>Bookmaker</TableCell>
                          <TableCell style={{ width: "80px" }}>
                            updatedAt
                          </TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {IdentifierData?.map((data) => (
                          <TableRow>
                            <TableCell>{data?.id}</TableCell>
                            <TableCell>{data?.apiEventId}</TableCell>
                            <TableCell>
                              {this.bookmakerName(data?.ApiProviderId)}
                            </TableCell>
                            <TableCell>
                              {moment(data?.updatedAt).format(
                                "DD/MM/YYYY hh:mm:ss a"
                              )}
                            </TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(
                                  data?.id,
                                  "edit",
                                  data
                                )}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(data?.id)}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              ) : (
                <Box className="message">No Variation Data Available</Box>
              )}
            </Paper>
          </Grid>
        </Grid>
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={this.toggleInputModal}
        >
          {/* {isLoading && <Loader />} */}
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {!isEditMode ? "Create Identifier" : "Edit Identifier"}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleInputModal}
            />

            <Grid
              item
              xs={12}
              className="runnerInfo"
              style={{ display: "flex", justifyContent: "space-between" }}
            >
              <Grid
                item
                xs={6}
                className="runnerInfo-text"
                style={{
                  display: "flex",
                  flexDirection: "column",
                  maxWidth: "48%",
                }}
              >
                <label className="modal-label">Bookmaker</label>
                <Select
                  className="React cricket-select sponsored-select-modal"
                  classNamePrefix="select"
                  placeholder="provider id"
                  menuPosition="fixed"
                  value={providersDetails?.find((item) => {
                    return item?.value == IdentifierValue?.providervalue;
                  })}
                  isLoading={isLoading}
                  onChange={(e) =>
                    this.setState({
                      IdentifierValue: {
                        ...IdentifierValue,
                        providervalue: e.value,
                      },
                    })
                  }
                  options={providersDetails}
                />
                {errorprovidervalue ? (
                  <p className="errorText" style={{ margin: "0 0 0 0" }}>
                    {errorprovidervalue}
                  </p>
                ) : (
                  ""
                )}
              </Grid>
              <Grid
                item
                xs={6}
                className="runnerInfo-text"
                style={{
                  display: "flex",
                  flexDirection: "column",
                  maxWidth: "48%",
                }}
              >
                <label className="modal-label">Api EventId</label>
                <TextField
                  className="search-field"
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder="Api EventId"
                  value={
                    isEditMode
                      ? IdentifierValue?.editEventIdValue
                      : IdentifierValue?.addEventIdValue
                  }
                  onChange={(e) => {
                    isEditMode
                      ? this.setState({
                          IdentifierValue: {
                            ...IdentifierValue,
                            editEventIdValue: e.target.value,
                          },
                        })
                      : this.setState({
                          IdentifierValue: {
                            ...IdentifierValue,
                            addEventIdValue: e.target.value,
                          },
                        });
                  }}
                />
                {errorRequire ? (
                  <p className="errorText" style={{ margin: "0px 0 0 0" }}>
                    {errorRequire}
                  </p>
                ) : (
                  ""
                )}
              </Grid>
            </Grid>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      //   disabled={!addInput}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleUpdate}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      //   disabled={!variationToSend}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.toggleInputModal}
                    className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
        <ShowModal
          isModalOpen={isModalOpen}
          onClose={this.toggleModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={this.deleteItem}
          onCancel={this.toggleModal}
        />
      </>
    );
  }
}

export default CreateIdentifierEvent;
