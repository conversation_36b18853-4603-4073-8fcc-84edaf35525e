import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Box,
  Breadcrumbs,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  Tabs,
  Tab,
  Paper,
} from "@mui/material";
import { Link } from "react-router-dom";
import { Loader } from "../../library/common/components";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import Select from "react-select";
import "./socSmartPlayDashboard.scss";
import moment from "moment";
import ArrowBack from "@mui/icons-material/ArrowBack";
import ArrowForward from "@mui/icons-material/ArrowForward";
import axiosInstance from "../../helpers/Axios";

const barDataOption = [
  {
    label: "Weekly",
    value: 0,
  },

  {
    label: "Monthly",
    value: 1,
  },
  {
    label: "Yearly",
    value: 2,
  },
];

// const initialData = [
//   { date: "2023-08-31", label: "Thursday", userCount: 2 },
//   { date: "2023-09-01", label: "Friday", userCount: 6 },
//   { date: "2023-09-02", label: "Saturday", userCount: 9 },
//   { date: "2023-09-29", label: "Friday", userCount: 5 },
//   { date: "2023-09-28", label: "Thursday", userCount: 2 },
// ];

const SOCSmartplayDashboard = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [initialData, setInitialData] = useState([]);
  const [selectedOption, setSelectedOption] = useState("Weekly");
  const [activeTab, setActiveTab] = useState(0); // 0 for SOC, 1 for SmartPlay
  const [currentStartDate, setCurrentStartDate] = useState(
    moment(Date()).startOf("week").format("YYYY-MM-DD")
  );

  const [weeklyStartDate, setWeeklyStartDate] = useState("");
  const [monthlyStartDate, setMonthlyStartDate] = useState("");
  const [yearlyStartDate, setYearlyStartDate] = useState("");
  const [weeklyEndDate, setWeeklyEndDate] = useState("");
  const [monthlyEndDate, setMonthlyEndDate] = useState("");
  const [yearlyEndDate, setYearlyEndDate] = useState("");
  const [totalUserCount, setTotalUserCount] = useState({
    Paid: 0,
    Cancelled: 0,
    Freetier: 0,
  });
  const [currentUserCount, setCurrentUserCount] = useState({});
  const [visibility, setVisibility] = useState({
    Paid: true,
    Cancelled: true,
    Freetier: true,
    // FridayNewsLetterCount: true,
  });

  const fetchChartData = async (startDate, endDate, week, month, day, year) => {
    setIsLoading(true);
    try {
      const category = activeTab === 0 ? "" : "fantasy";
      const { status, data } = await axiosInstance.get(
        `emailChart/subscription?timezone=Asia/Calcutta&startDate=${startDate}&endDate=${endDate}&week=${week}&month=${month}&day=${day}&year=${year}&category=${category}`
      );

      if (status === 200) {
        const transformedData = data?.result?.map((item) => {
          const obj = { date: item?.date, label: item?.label };
          item.types.forEach((type) => {
            obj[type?.type] = type?.count;
            obj[`${type?.type}_todayCount`] = type?.todayCount;
          });
          return obj;
        });

        const totals = transformedData?.reduce(
          (acc, curr) => {
            acc.Paid += curr?.Paid;
            acc.Cancelled += curr?.Cancelled;
            acc.Freetier += curr?.Freetier;
            return acc;
          },
          {
            Paid: 0,
            Cancelled: 0,
            Freetier: 0,
          }
        );
        setCurrentUserCount(totals);
        setTotalUserCount({
          Paid: data?.todayCount?.PaidCount,
          Cancelled: data?.todayCount?.CancelledCount,
          Freetier: data?.todayCount?.FreetierCount,
        });
        setInitialData(transformedData);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    let startDate, endDate;
    startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
    setWeeklyStartDate(startDate);
    endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
    setWeeklyEndDate(endDate);
    setSelectedOption("Weekly");
    setCurrentStartDate(startDate);
    fetchChartData(startDate, endDate, "", "", true, "");
  }, [activeTab]); // Add activeTab as a dependency to refetch when tab changes

  const handleSelectOptionChnage = (option) => {
    let startDate, endDate;
    switch (option) {
      case "Weekly":
        startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
        setWeeklyStartDate(startDate);
        setWeeklyEndDate(endDate);
        setMonthlyStartDate("");
        setMonthlyEndDate("");
        setYearlyStartDate("");
        setYearlyEndDate("");
        setSelectedOption(option);
        fetchChartData(startDate, endDate, "", "", true, "");
        setCurrentStartDate(startDate);
        break;
      case "Monthly":
        startDate = moment(Date()).startOf("month").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("month").format("YYYY-MM-DD");
        setWeeklyStartDate("");
        setWeeklyEndDate("");
        let monthYear = moment(startDate).year();
        const monthName = moment(startDate).format("MMMM");
        setMonthlyStartDate(monthName);
        setMonthlyEndDate(monthYear);
        setYearlyStartDate("");
        setYearlyEndDate("");
        setSelectedOption(option);
        fetchChartData(startDate, "", "", "", true, "");
        setCurrentStartDate(startDate);
        break;
      case "Yearly":
        startDate = moment(Date()).startOf("year").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("year").format("YYYY-MM-DD");
        setWeeklyStartDate("");
        setWeeklyEndDate("");
        setMonthlyStartDate("");
        setMonthlyEndDate("");
        let year = moment(startDate).year();
        setYearlyStartDate(year);
        // setYearlyEndDate(year);
        setSelectedOption(option);
        fetchChartData(startDate, "", "", true, "", "");
        setCurrentStartDate(startDate);
        break;
      default:
        startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
        setCurrentStartDate(startDate);
        fetchChartData(startDate, endDate, "", "", true, "");
    }
  };

  const handlePrevious = () => {
    let newStartDate, newEndDate;
    switch (selectedOption) {
      case "Weekly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "weeks")
          .startOf("weeks")
          .format("YYYY-MM-DD");
        setWeeklyStartDate(newStartDate);

        newEndDate = moment(currentStartDate)
          .subtract(1, "weeks")
          .endOf("weeks")
          .format("YYYY-MM-DD");
        setWeeklyEndDate(newEndDate);
        fetchChartData(newStartDate, newEndDate, "", "", true, "");
        setCurrentStartDate(newStartDate);
        break;
      case "Monthly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "months")
          .startOf("isoMonth")
          .format("YYYY-MM-DD");

        const monthName = moment(newStartDate).format("MMMM");
        setMonthlyStartDate(monthName);

        newEndDate = moment(currentStartDate)
          .subtract(1, "months")
          .endOf("isoMonth")
          .format("YYYY-MM-DD");
        let monthYear = moment(newEndDate).year();
        setMonthlyEndDate(monthYear);
        fetchChartData(newStartDate, "", "", "", true, "");
        setCurrentStartDate(newStartDate);
        break;
      case "Yearly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "years")
          .startOf("isoYear")
          .format("YYYY-MM-DD");
        let year = moment(newStartDate).year();
        setYearlyStartDate(year);
        setYearlyEndDate(newEndDate);
        fetchChartData(newStartDate, "", "", true, "", "");
        setCurrentStartDate(newStartDate);
        break;
      default:
        break;
    }
  };

  const isNextDisable = useCallback(() => {
    const conditionToCheck =
      (selectedOption === "Weekly" &&
        currentStartDate ===
          moment(Date()).startOf("week").format("YYYY-MM-DD")) ||
      (selectedOption === "Monthly" &&
        currentStartDate ===
          moment(Date()).startOf("month").format("YYYY-MM-DD")) ||
      (selectedOption === "Yearly" &&
        currentStartDate ===
          moment(Date()).startOf("year").format("YYYY-MM-DD"));
    if (conditionToCheck) {
      return true;
    } else {
      return false;
    }
  }, [selectedOption, currentStartDate]);

  const handleNext = () => {
    if (!isNextDisable()) {
      let newStartDate, newEndDate;

      switch (selectedOption) {
        case "Weekly":
          newStartDate = moment(currentStartDate)
            .add(1, "weeks")
            .startOf("weeks")
            .format("YYYY-MM-DD");

          setWeeklyStartDate(newStartDate);

          newEndDate = moment(currentStartDate)
            .add(1, "weeks")
            .endOf("weeks")
            .format("YYYY-MM-DD");
          setWeeklyEndDate(newEndDate);
          fetchChartData(newStartDate, newEndDate, "", "", true, "");
          setCurrentStartDate(newStartDate);
          break;
        case "Monthly":
          newStartDate = moment(currentStartDate)
            .add(1, "months")
            .endOf("isoMonth")
            .format("YYYY-MM-DD");

          const monthName = moment(newStartDate).format("MMMM");
          setMonthlyStartDate(monthName);
          newEndDate = moment(currentStartDate)
            .subtract(1, "months")
            .endOf("isoMonth")
            .format("YYYY-MM-DD");
          let monthYear = moment(newEndDate).year();
          setMonthlyEndDate(monthYear);
          fetchChartData(newStartDate, "", "", "", true, "");
          setCurrentStartDate(newStartDate);
          break;
        case "Yearly":
          newStartDate = moment(currentStartDate)
            .add(1, "years")
            .startOf("isoYear")
            .format("YYYY-MM-DD");

          let year = moment(newStartDate).year();
          setYearlyStartDate(year);
          fetchChartData(newStartDate, "", "", true, "", "");
          setCurrentStartDate(newStartDate);
          break;
        default:
          break;
      }
    }
  };

  const getTimePeriod = () => {
    const timePeriod =
      selectedOption === "Weekly"
        ? "Week"
        : selectedOption === "Monthly"
        ? "Month"
        : selectedOption === "Yearly"
        ? "Year"
        : "";
    return timePeriod;
  };

  const handleLegendClick = (e) => {
    setVisibility((prev) => ({
      ...prev,
      [e.value]: !prev[e.value],
    }));
  };

  // Custom Legend Renderer
  const renderLegend = (props) => {
    const { payload } = props;

    return (
      <div
        style={{ display: "flex", justifyContent: "center", marginTop: "10px" }}
      >
        {payload.map((entry, index) => (
          <span
            key={`item-${index}`}
            onClick={() => handleLegendClick(entry)}
            style={{
              cursor: "pointer",
              margin: "0 10px",
              opacity: visibility[entry?.value] ? 1 : 0.6,
              display: "inline-flex",
              alignItems: "center",
            }}
          >
            <svg width="10" height="10" style={{ marginRight: "5px" }}>
              <rect width="10" height="10" fill={entry?.color} />
            </svg>
            {entry?.value}
          </span>
        ))}
      </div>
    );
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length > 0) {
      return (
        <div className="custom-tooltip">
          {payload?.map((ele) => {
            return (
              <p
                className="label"
                style={{ color: ele.color }}
              >{`${ele.name} : ${ele.value} `}</p>
            );
          })}
        </div>
      );
    }

    return null;
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <>
      <Grid container className="page-content adminLogin">
        <Grid item xs={12} className="pageWrapper">
          <Box className="bredcrumn-wrap">
            <Breadcrumbs
              separator="/"
              aria-label="breadcrumb"
              className="breadcrumb"
            >
              <Link underline="hover" color="inherit" to="/dashboard">
                Home
              </Link>
              <Link underline="hover" color="inherit">
                User Management
              </Link>
              <Typography className="active_p">
                SOC and SmartPlay Dashboard
              </Typography>
            </Breadcrumbs>
          </Box>
          <Grid container direction="row" alignItems="center">
            <Grid item xs={4}>
              <Typography variant="h1" align="left">
                SOC and SmartPlay Dashboard
              </Typography>
            </Grid>
            <Grid item xs={8} className="admin-filter-wrap">
              <Select
                className="React cricket-select external-select"
                classNamePrefix="select"
                placeholder="Select Status"
                options={barDataOption}
                value={barDataOption.find(
                  (option) => option?.label === selectedOption
                )}
                onChange={(option) => {
                  handleSelectOptionChnage(option?.label);
                }}
              />
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "20px" }}
              justifyContent={"flex-start"}
            >
              <Paper
                elevation={0}
                sx={{ borderRadius: "8px", overflow: "hidden" }}
              >
                <Box
                  sx={{
                    borderBottom: 1,
                    borderColor: "divider",
                    width: "100%",
                    backgroundColor: "#f5f5f5",
                    borderRadius: "8px 8px 0 0",
                  }}
                >
                  <Tabs
                    value={activeTab}
                    onChange={handleTabChange}
                    aria-label="dashboard tabs"
                    centered
                    sx={{
                      "& .MuiTabs-indicator": {
                        backgroundColor: "#1976d2",
                        height: "4px",
                      },
                      "& .MuiTab-root": {
                        textTransform: "none",
                        fontWeight: 600,
                        fontSize: "16px",
                        minWidth: "120px",
                        padding: "12px 16px",
                        transition: "all 0.3s ease",
                        "&.Mui-selected": {
                          color: "#1976d2",
                        },
                        "&:hover": {
                          backgroundColor: "rgba(25, 118, 210, 0.08)",
                        },
                      },
                    }}
                  >
                    <Tab
                      label="SOC"
                      sx={{
                        borderRight: "1px solid #e0e0e0",
                      }}
                    />
                    <Tab label="SmartPlay" />
                  </Tabs>
                </Box>
              </Paper>
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Table className="subscriber-dashboared-table">
                <TableBody>
                  <TableRow>
                    <TableCell
                      style={{ textAlign: "center", fontWeight: "700" }}
                    >
                      {" "}
                      Total Subscribed
                    </TableCell>
                  </TableRow>
                  {visibility?.Paid && (
                    <TableRow>
                      {/* <TableCell>
                        <Typography>
                          <span> Total Paid: </span>
                          {totalUserCount?.Paid}
                        </Typography>
                      </TableCell> */}
                      <TableCell>
                        <Typography>
                          <span>Current Paid: </span>
                          {totalUserCount?.Paid}{" "}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                  {visibility?.Cancelled && (
                    <TableRow>
                      {/* <TableCell>
                        <Typography>
                          <span> Total Cancelled:</span>
                          {totalUserCount?.Cancelled}
                        </Typography>
                      </TableCell> */}
                      <TableCell>
                        <Typography>
                          <span> Current Cancelled: </span>
                          {totalUserCount?.Cancelled}{" "}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                  {visibility?.Freetier && (
                    <TableRow>
                      {/* <TableCell>
                        <Typography>
                          <span>Total Freetier:</span>
                          {totalUserCount?.Freetier}
                        </Typography>
                      </TableCell> */}
                      <TableCell>
                        <Typography>
                          <span>Current Freetier:</span>
                          {totalUserCount?.Freetier}{" "}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Grid>

            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Typography align="center">
                {weeklyStartDate && weeklyEndDate
                  ? moment(weeklyStartDate).format("DD-MM-YYYY") +
                    " - " +
                    moment(weeklyEndDate).format("DD-MM-YYYY")
                  : ""}
                {monthlyStartDate && monthlyEndDate
                  ? monthlyStartDate + " - " + monthlyEndDate
                  : ""}
                {yearlyStartDate ? yearlyStartDate : ""}
              </Typography>
            </Grid>
          </Grid>
          {isLoading && <Loader />}
          {!isLoading && initialData?.length === 0 && (
            <p className="text-center NoDataPadding">No data available.</p>
          )}
          <Box className="dashboard-box">
            <Box className="line-chart-box">
              {!isLoading && initialData?.length > 0 && (
                <>
                  <Typography variant="h6" align="center">
                    {activeTab === 0 ? "SOC" : "SmartPlay"} Subscriber Dashboard
                  </Typography>

                  <Box className="mt-22">
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart data={initialData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey={
                            selectedOption === "Monthly" ? "date" : "label"
                          }
                        />
                        <YAxis />
                        <Tooltip
                          className="custom-tooltip-wrap"
                          content={<CustomTooltip />}
                        />
                        <Legend
                          content={renderLegend}
                          payload={[
                            {
                              value: "Paid",
                              type: "square",
                              color: "#8884d8",
                            },
                            {
                              value: "Cancelled",
                              type: "square",
                              color: "#82ca9d",
                            },
                            {
                              value: "Freetier",
                              type: "square",
                              color: "#ffc658",
                            },
                          ]}
                        />
                        {visibility?.Paid && (
                          <Bar dataKey="Paid" fill="#8884d8" />
                        )}
                        {visibility?.Cancelled && (
                          <Bar dataKey="Cancelled" fill="#82ca9d" />
                        )}
                        {visibility?.Freetier && (
                          <Bar dataKey="Freetier" fill="#ffc658" />
                        )}
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </>
              )}
              {!isLoading && initialData.length > 0 ? (
                <Box className="arrow-box mt-22 text-center">
                  <Box
                    className="flex mr-30 arrow"
                    onClick={() => handlePrevious()}
                  >
                    <ArrowBack />
                    <Typography>{`Prev ${getTimePeriod()}`}</Typography>
                  </Box>

                  <Box
                    className="flex arrow"
                    onClick={() => handleNext()}
                    style={{ opacity: isNextDisable() ? "0.5" : "1" }}
                  >
                    <Typography>{`Next ${getTimePeriod()}`}</Typography>
                    <ArrowForward />
                  </Box>
                </Box>
              ) : (
                <></>
              )}
            </Box>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default SOCSmartplayDashboard;
