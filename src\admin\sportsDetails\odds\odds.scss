@import "../../../assets/scss/variables";

.pageWrapper {
  .breadcrumb {
    color: #191919;
    font-size: 11.42px;
    line-height: 14px;
    text-transform: uppercase;
  }

  .active_p {
    font-size: 11.42px;
    line-height: 14px;
    color: #4455c7;
    text-transform: uppercase;
  }

  h1 {
    font-size: 43.9px;
    font-weight: normal;
    line-height: 65px;
    letter-spacing: 0;
    font-family: VeneerClean-Soft;
    margin: 0px;
  }
}

.addButton.btn {
  border-radius: 8px;
  padding: 13px 24px 12px;
  width: 106px;
}

.sport-tab {
  padding: 19px 18px 9px 18px;
  box-shadow: 0px 3px 9px 0px #0000000d;
  background: #ffffff;

  .racing-tab-detail {
    border-bottom: 2px solid #4455c7;
  }

  .MuiTab-root {
    min-width: 120px;
    opacity: 1;
  }

  .MuiButtonBase-root.active {
    color: #003764;
  }
  .MuiTabs-indicator {
    height: 3px;
    background-color: #003764;
  }

  .MuiButtonBase-root {
    font-size: 16px;
    font-family: VeneerClean-Soft !important;
    color: #191919;
    line-height: 23px;
  }

  .PrivateTabIndicator-root-7 {
    height: 3px;
    color: #003764;
  }

  .Filteritemlist-wrap {
    display: flex;
    align-items: center;
    margin-top: 9px;

    img {
      max-width: none;
    }

    .Filteritemlist-datepicker {
      display: contents;

      .MuiGrid-container {
        background-color: #ffffff;
      }

      .MuiFormControl-marginNormal {
        margin: 0px;
      }

      .MuiOutlinedInput-input {
        padding: 10.5px 14px;
      }
    }
  }

  .Filteritemlist-racing {
    display: flex;
    list-style-type: none;
    padding: 0px;
    margin: 0px;

    li {
      margin-right: 14px;

      label {
        display: flex;
        grid-column-gap: 5.2px;
        column-gap: 5.2px;
        font-size: 12px;
        line-height: 15px;
        align-items: center;

        .PrivateSwitchBase-root-3 {
          padding: 0px;
        }

        .PrivateSwitchBase-root-4 {
          padding: 0px;
        }
      }
    }
  }
}

.fixture {
  background: #78c2a7;
}

.partialfixture {
  background: #ffb80c;
}

.notfixture {
  background: #d84727;
  cursor: pointer;
}

.event {
  .notfixture {
    cursor: inherit;
  }

  .ignore {
    cursor: inherit;
  }
}

.ignore {
  background: #d4d6d8;
  cursor: pointer;
}

.live {
  position: relative;
}

.live::after {
  content: "LO";
  top: 0;
  position: absolute;
  right: 0;
  font-weight: 600;
  font-size: 10px;
  background-color: #4455c7;
  color: #fefefe;
  border-bottom-left-radius: 0.25rem;
  border-top: none;
  border-right: none;
  text-align: center;
  padding: 2px 4px;
}

.liveindicator::after {
  border: none;
  padding: 2px 3px;
  border-bottom-left-radius: 0px;
}

.racing-colleps {
  margin-top: 18px;

  .colleps-accordion {
    margin-bottom: 18px;
    box-shadow: 0px 3px 9px 0px #0000000d;
  }

  .MuiAccordionSummary-root {
    padding: 0px 33px;
    background: linear-gradient(90deg, #4455c7 0%, #003764 68%);
    min-height: 45px;
  }

  .MuiAccordionSummary-root.Mui-expanded {
    min-height: 45px;
  }

  .MuiAccordionSummary-content {
    margin: 0px;
  }

  .MuiAccordionSummary-content.Mui-expanded {
    margin: 0px;
  }

  .MuiTypography-body1 {
    font-size: 22px;
    font-family: $primaryFont;
    color: #fff;
    line-height: 31.36px;
    margin-left: 9px;
  }

  .MuiSvgIcon-root {
    fill: #ffffff;
  }

  .accordion_details {
    width: 100%;
    max-width: fit-content;
  }

  .accordion_details.event {
    padding: 8px 16px 16px;
  }

  // .event {}

  .country-title {
    font-family: "VeneerClean-Soft" !important;
    color: #000000;
    font-size: 22.4px;
    line-height: 31.36px;
    border-bottom: 1px solid #4455c7;
    text-align: left;
    margin: 10px 0px 0px;
  }

  .rt-thead {
    max-width: 84px;
    min-width: 74px;

    img {
      transform: rotate(270deg);
      max-width: 74px;
      margin-top: 4px;
    }

    .square-bookmaker {
      // max-width: 35px;
      width: 75%;
    }
  }

  .fixture {
    background: #78c2a7;
  }

  .notfixture {
    background: #d84727;
  }

  .ignore {
    background: #d4d6d8;
  }
}

.fixture-table-wrap {
  padding-top: 17px;

  thead {
    .MuiTableCell-root:first-child {
      border: none;
      min-width: 224.2px;
    }

    .MuiTableCell-root:not(:first-child) {
      border: 1px solid #707070;
    }

    th {
      font-size: 16px;
      line-height: 19px;
      font-weight: 600;
      padding: 7px 0;
      text-align: center;
      border-width: 0 0 1px;
      border-color: #d4d6d8;
      font-family: "Inter", sans-serif;
      height: 70px;
    }
  }

  tbody {
    .seprator {
      padding: 0px;
      height: 13px !important;
    }

    th {
      cursor: pointer;
      display: flex;
      padding: 13px 0;
      grid-column-gap: 9px;
      -webkit-column-gap: 9px;
      column-gap: 9px;
      border: none;
      text-align: left;
      align-items: center;
      min-width: 220px;
    }

    .MuiTableCell-root:first-child {
      border: none;
    }

    .MuiTableCell-root:not(:first-child) {
      border: 1px solid #d4d6d8;
      min-width: 44px;
    }

    .flag-icon {
      max-width: 37px;
      height: 21.58px;
      object-fit: cover;
    }

    h6 {
      font-size: 16px;
      line-height: 19px;
      color: #191919;
      font-weight: 400;
      word-break: break-word;
      max-width: 180px;
    }
  }
}

.fixture-info {
  padding: 22px 33px;

  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    grid-column-gap: 25px;
    -webkit-column-gap: 25px;
    column-gap: 25px;
    align-items: center;

    span.sqare {
      width: 15px;
      height: 15px;
    }

    li {
      display: flex;
      align-items: center;
      grid-column-gap: 8px;
      -webkit-column-gap: 8px;
      column-gap: 8px;
    }
  }
}
