import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  Typography,
  Tabs,
  Tab,
  Button,
  Grid,
  Modal,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Checkbox,
} from "@mui/material";
// import RaceCountdown from "./raceCountdown";
import axiosInstance from "../../../helpers/Axios";
import { useParams, useNavigate } from "react-router-dom";
import moment from "moment";
import RaceCountdown from "./racecountdown";
import Select from "react-select";
import "./raceplayerdetails.scss";
import Weather from "../../../images/w-good.svg";
import Showers from "../../../images/showers.svg";
import Rain from "../../../images/rain.svg";
import Overcast from "../../../images/overcast.svg";
import Cloudy from "../../../images/cloudy.svg";
import { Loader } from "../../../library/common/components";
import CancelIcon from "@mui/icons-material/Cancel";
import RunnerModal from "./runnerModal";
import { URLS } from "../../../library/common/constants";
import _ from "lodash";
import CreateApiRaceIdentifire from "../../apiRaceIdentifire/CreateApiRaceIdentifire";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import { config } from "../../../helpers/config";
import ActionMessage from "../../../library/common/components/ActionMessage";

// Clent's current Timezone
let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const RunnersTablePage = (props) => {
  // const { state } = useLocation();
  const params = useParams();
  const history = useNavigate();
  // const [selectedOption, setSelectedOption] = useState(
  //   state?.CurrentData?.track?.id
  // );
  // const [raceTrackListData, setraceTrackListData] = useState([]);
  const [raceTrackdata, setraceTrackdata] = useState([]);
  // const [runnerData, setRunnerData] = useState([]);

  const [eventData, seteventData] = useState([]);
  const [isEventLoading, setisEventLoading] = useState(false);
  const [isrunnnerLoading, setisrunnnerLoading] = useState(false);

  const [selectedDate, setselectedDate] = useState();
  const [trackListData, settrackListData] = useState([]);
  // const [selectedTrack, setselectedTrack] = useState("");
  const [selectedEvent, setselectedEvent] = useState();
  const [selectedRaceId, setselectedRaceId] = useState("");
  // const [raceTrackClicked, setraceTrackClicked] = useState(false);
  const [eventLocation, setEventLocation] = useState([]);
  const [sportAll, setsportAll] = useState([]);
  const [distanceAll, setdistanceAll] = useState([]);
  const [weatherAll, setweatherAll] = useState([]);
  const [isModalOpen, setisModalOpen] = useState(false);
  const [modalId, setmodalId] = useState("");
  const [sportId, setsportId] = useState("");
  const [selectedIdentifier, setselectedIdentifier] = useState("");
  const [raceIdToSend, setraceIdToSend] = useState("");
  const [identifireModalOpen, setidentifireModalOpen] = useState(false);
  const [identifireDetails, setidentifireDetails] = useState([]);
  const [searchIdentifire, setsearchIdentifire] = useState("");
  const [allProvider, setallProvider] = useState([]);
  const [isInputFormModalOpen, setisInputFormModalOpen] = useState(false);
  const [idToSend, setidToSend] = useState("");
  const [isEditMode, setisEditMode] = useState(false);
  const [allRaces, setallRaces] = useState([]);
  const [isDeleteIdentifireModalOpen, setisDeleteIdentifireModalOpen] =
    useState(false);
  const [itemToDelete, setitemToDelete] = useState("");
  const [isDeleteLoading, setisDeleteLoading] = useState("");
  const [isLoading, setisLoading] = useState(false);
  const [checkBoxValues, setCheckBoxValues] = useState([]);
  const [selectAllChecked, setSelectAllChecked] = useState(false);
  const [messageBox, setMessageBox] = useState({
    display: false,
    type: "",
    message: "",
  });

  const fetchRaceRunner = async (id, isTrackChange) => {
    setisrunnnerLoading(true);
    try {
      const { status, data } = await axiosInstance.get(`/events/runner/${id}`);
      if (status === 200) {
        setraceTrackdata(data?.data?.race);
        setdistanceAll(data?.data?.race?.Distance);
        setweatherAll(data?.data?.race?.Weather);
        // setRunnerData(data?.data?.runners);
        setisrunnnerLoading(false);
        if (isTrackChange) {
          let findEvent = eventData?.find((item) => {
            return item?.value == selectedEvent;
          });

          let eventName = findEvent?.label.split(",")[0];
          let sportsName =
            data?.data?.race?.sportId === 1
              ? "Horse Racing"
              : data?.data?.race?.sportId === 2
              ? "Harness Racing"
              : data?.data?.race?.sportId === 3
              ? "Greyhound Racing"
              : "";

          history(
            `/${sportsName}/${data?.data?.race?.sportId}/${eventName}/${data?.data?.race?.eventId}/runners/${data?.data?.race.id}`
          );
        }
      }
    } catch (err) {
      setisrunnnerLoading(false);
    }
  };

  // handle changes of track
  const handleChangeTrack = (e, newValue) => {
    setselectedRaceId(newValue);
    fetchRaceRunner(newValue, true);
  };

  useEffect(() => {
    // check race track is clicked or not by using route (raceID)
    let isRaceTrackClicked = params?.id !== undefined ? true : false;
    if (isRaceTrackClicked) {
      fetchTrackList();
    }

    // setselectedTrack(
    //   params?.trackId !== undefined && JSON.parse(params?.trackId)
    // );
    params?.eventid !== undefined && setselectedEvent(params?.eventid);
    params?.id !== undefined && setselectedRaceId(params?.id);
    params?.id !== undefined && fetchRaceRunner(params?.id);
  }, []);

  useEffect(() => {
    let head_date = new URLSearchParams(window.location.search).get("date");

    // if selected date and Route's date not match then set route date as selected date
    if (head_date !== null) {
      setselectedDate(head_date);
    }

    // check race track is clicked or not by using route (raceID)
    // let isRaceTrackClicked = params?.raceId !== undefined ? true : false;

    // setraceTrackClicked(isRaceTrackClicked);

    // let SelectedRacing = state?.raceData?.filter((item) => {
    //   return item?.track?.id == selectedOption;
    // });

    // setselectedTrack(
    //   params?.trackId !== undefined && JSON.parse(params?.trackId)
    // );
    params?.eventid !== undefined && setselectedEvent(params?.eventid);
    params?.id !== undefined && setselectedRaceId(params?.id);
  }, [params]);

  const handleEvent = (e) => {
    setselectedEvent(e?.value);
    let selected_obj = trackListData?.find((obj) => obj?.id === e?.value);
    // setselectedRaceId(selected_obj?.race?.[0]?.id);
    fetchRaceRunner(selected_obj?.race?.[0]?.id, true);
  };

  useEffect(() => {
    let eventData = trackListData?.filter((item) => {
      return item?.id == selectedEvent;
    });
    setEventLocation(eventData);
  }, [trackListData, selectedEvent]);

  useEffect(() => {
    fetchAllSport();
    // fetchAllDistance();
    // fetchAllWeather();
    fetchAllProvider();
    fetchAllRaces();
  }, []);

  // Fetch Tracklist Data
  const fetchTrackList = async (isRunnersChange) => {
    setisEventLoading(true);
    let head_date = new URLSearchParams(window.location.search).get("date");
    let date_to_pass =
      head_date !== null
        ? head_date
        : selectedDate !== ""
        ? moment(selectedDate).utc().format("YYYY-MM-DD")
        : moment(new Date()).utc().format("YYYY-MM-DD");

    let id = params.sportid;
    let meetingState = new URLSearchParams(window.location.search).get("intl");

    let meetingStateValue = meetingState == "true" ? "Intl" : "Aus/NZ,Intl";

    try {
      const { status, data } = await axiosInstance.get(
        `events/trackList/?sportId=${id}&MeetingState=${meetingStateValue}&todate=${date_to_pass}&countryId=${""}&stateId=${""}&timezone=${timezone}`
      );
      if (status === 200) {
        let data_pass =
          data?.events?.length > 0
            ? data?.events?.map((obj) => {
                // remove raceNumber === 0 and duplicate raceNumber's races from tracklist
                return {
                  ...obj,
                  race: Array.from(
                    new Set(obj?.race?.map((a) => a?.raceNumber))
                  )
                    .map((id) => {
                      return obj?.race.find((a) => a?.raceNumber === id);
                    })
                    .filter((race) => race?.raceNumber != 0),
                };
              })
            : [];

        let sportId =
          data_pass?.length > 0
            ? data_pass[0]?.sportId
            : Number(params?.sportId);

        let race =
          sportId === 1 ? "horse" : sportId === 2 ? "harness" : "greyhounds";

        // Race details change that time call
        if (isRunnersChange) {
          settrackListData(data_pass);
          // setselectedTrack(
          //   data_pass?.length > 0 ? data_pass[0]?.trackId : null
          // );
          // setraceTrackListData(data_pass?.length > 0 ? data_pass[0]?.race : []);
          selectedRaceId(
            data_pass?.length > 0
              ? data_pass[0]?.race?.length > 0
                ? data_pass[0]?.race[0]?.id !== undefined
                  ? data_pass[0]?.race[0]?.id
                  : null
                : null
              : null
          );
          const newData = [];
          let track = data_pass?.map((item) => {
            newData.push({
              label: item?.eventName + "," + item?.state?.stateCode,
              value: item?.id,
            });
          });
          seteventData(newData);
          setisEventLoading(false);
        } else {
          // let all_data = data_pass;

          // find selected races from tracklist data
          // let selected_track_races = all_data?.find(
          //   (obj) => obj?.id === Number(selectedEvent)
          // );

          settrackListData(data_pass);
          // setraceTrackListData(selected_track_races?.race);
          const newData = [];
          let track = data_pass?.map((item) => {
            newData.push({
              label: item?.state?.stateCode
                ? item?.eventName + "," + item?.state?.stateCode
                : item?.eventName,
              value: item?.id,
            });
          });
          seteventData(newData);
          setisEventLoading(false);
        }
      }
    } catch (err) {
      setisEventLoading(false);
    }
  };
  const WeatherIcon = (type) => {
    if (type === "showers") {
      return Showers;
    } else if (type === "Rain") {
      return Rain;
    } else if (type === "fine") {
      return Weather;
    } else if (type === "FINE") {
      return Weather;
    } else if (type === "Good") {
      return Weather;
    } else if (type === "Overcast") {
      return Overcast;
    } else if (type === "Cloudy") {
      return Cloudy;
    } else {
      return Weather;
    }
  };
  const inputModal = (id, sendId) => {
    setisModalOpen(true);
    setmodalId(id);
    setsportId(sendId);
  };

  const fetchAllSport = async () => {
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      let data_obj = _.orderBy(data?.result, ["sportName"], ["asc"]);
      // this.setState({ sportAll: data_obj });
      setsportAll(data_obj);
    }
  };
  // const fetchAllDistance = async () => {
  //   const { status, data } = await axiosInstance.get(URLS.distance);
  //   if (status === 200) {
  //     // this.setState({ distanceAll: data?.result?.rows });
  //     setdistanceAll(data?.result?.rows);
  //   }
  // };
  // const fetchAllWeather = async (id) => {
  //   const { status, data } = await axiosInstance.get(URLS.weather);
  //   if (status === 200) {
  //     let data_obj = _.orderBy(data?.result?.rows, ["weatherType"], ["asc"]);
  //     //  this.setState({ weatherAll: data?.result?.rows });
  //     setweatherAll(data_obj);
  //   }
  // };
  const fetchAllRaces = async () => {
    const { status, data } = await axiosInstance.get(URLS.races);
    if (status === 200) {
      // this.setState({ allRaces: data.result });
      setallRaces(data?.result?.rows);
    }
  };

  const fetchAllProvider = async () => {
    const { status, data } = await axiosInstance.get(URLS.apiProvider);
    if (status === 200) {
      let data_obj = _.orderBy(data?.result, ["providerName"], ["asc"]);
      // this.setState({ allProvider: data_obj });
      setallProvider(data_obj);
    }
  };
  const toggleInputModal = () => {
    setisModalOpen(false);
  };
  const getProvider = (id) => {
    // let { allProvider } = this.state;
    let provider = allProvider
      .filter((obj) => obj?.id === id)
      .map((object) => object?.providerName);
    return provider;
  };

  const showIdentifire = async (id, data) => {
    // this.setState({
    //   identifireModalOpen: true,
    //   isLoading: true,
    // });
    setidentifireModalOpen(true);
    setisLoading(true);
    // let getId = raceIdToSend === null ? id : raceIdToSend;
    try {
      const { status, data } = await axiosInstance.get(
        `race/identifier/getByRaceId/${id}`
      );
      if (status === 200) {
        // this.setState({
        //   identifireDetails: data.result,
        //   isLoading: false,
        // });
        setidentifireDetails(data?.result);
        setisLoading(false);
      }
    } catch (err) {
      // console.log(err);
      setisLoading(false);
    }
  };
  const toggleIdentifireModal = () => {
    // this.setState({
    //   identifireModalOpen: !this.state.identifireModalOpen,
    //   searchIdentifire: "",
    // });
    setidentifireModalOpen(false);
    setsearchIdentifire("");
    setCheckBoxValues([]);
  };

  const inputFormModal = (id, type) => () => {
    // this.setState({
    //   isInputFormModalOpen: true,
    //   idToSend: id,
    //   isEditMode: type,
    //   searchIdentifire: "",
    // });
    setisInputFormModalOpen(true);
    setidToSend(id);
    setisEditMode(type);
    setsearchIdentifire("");
  };
  const toggleInputFormModal = () => {
    setisInputFormModalOpen(false);
  };
  const setIdentifireToDelete = (id) => {
    // this.setState({ itemToDelete: id, isDeleteIdentifireModalOpen: true });
    setitemToDelete(id);
    setisDeleteIdentifireModalOpen(true);
  };
  const toggleIdentifireDeleteModal = () => {
    // this.setState({
    //   isDeleteIdentifireModalOpen: !this.state.isDeleteIdentifireModalOpen,
    //   itemToDelete: null,
    // });
    setisDeleteIdentifireModalOpen(false);
    setitemToDelete(null);
  };
  const handleIdentifireDelete = async (id) => {
    // this.setState({
    //   isDeleteLoading: "identifireDelete",
    //   isDeleteIdentifireModalOpen: false,
    // });
    setisDeleteLoading("identifireDelete");
    setisDeleteIdentifireModalOpen(false);
    try {
      const { status } = await axiosInstance.delete(
        `race/identifier/${itemToDelete}`
      );
      if (status === 200) {
        showIdentifire(raceTrackdata?.id, raceTrackdata);
        // this.setState({ isDeleteLoading: "", itemToDelete: null });
        setisDeleteLoading("");
        setitemToDelete(null);
      }
    } catch (err) {}
  };
  const searchData = identifireDetails?.filter((obj) => {
    if (searchIdentifire === "") return obj;
    else if (
      obj?.apiRaceId
        ?.toString()
        .toLowerCase()
        .includes(searchIdentifire.toString().toLowerCase()) ||
      obj?.id
        ?.toString()
        .toLowerCase()
        .includes(searchIdentifire.toString().toLowerCase())
    ) {
      return obj;
    }
    return "";
  });

  const handleCheckBoxChange = (e, item) => {
    const { value, checked } = e.target;

    if (checked) {
      let checkboxdata = [...checkBoxValues, Number(value)];
      setCheckBoxValues(checkboxdata);
    } else {
      let checkboxdata = checkBoxValues?.filter(
        (element) => element !== Number(value)
      );
      setCheckBoxValues(checkboxdata);
    }
  };

  const syncOdd = async (item) => {
    const passEndpoint =
      item?.type === "scrap"
        ? `adminNotification/scrapOddSync`
        : `sync/fixture`;
    let payload;
    if (item?.type === "scrap") {
      payload = { bookkeeperId: [item?.bookkeeperId], raceId: item?.raceId };
    } else {
      payload = {
        providerId: item?.providerId,
      };
    }
    try {
      const { status } = await axiosInstance.post(passEndpoint, payload);
      if (status === 200) {
        setActionMessage(true, "Success", "Odds Sync Successfully!");
        setCheckBoxValues([]);
      }
    } catch (err) {
      setActionMessage(true, "Error", "An Error Occurred");
      setCheckBoxValues([]);
    }
  };

  const syncSelectedOdds = async () => {
    const passEndpoint = `adminNotification/scrapOddSync`;
    const raceId = identifireDetails?.[0]?.raceId;
    let payload;
    if (selectAllChecked === true) {
      payload = { bookkeeperId: [], raceId: raceId };
    } else {
      payload = { bookkeeperId: checkBoxValues, raceId: raceId };
    }
    try {
      const { status } = await axiosInstance.post(passEndpoint, payload);
      if (status === 200) {
        setActionMessage(true, "Success", "Odds Sync Successfully!");
        setCheckBoxValues([]);
        setSelectAllChecked(false);
      }
    } catch (err) {
      setActionMessage(true, "Error", "An Error Occurred");
      setCheckBoxValues([]);
      setSelectAllChecked(false);
    }
  };

  const handleSelectAll = (e) => {
    setSelectAllChecked(e.target.checked);
    if (e.target.checked) {
      const scrapData = identifireDetails?.filter(
        (item) => item?.type === "scrap"
      );
      const bookkeeperIds = [];
      scrapData.map((item) => bookkeeperIds.push(item?.bookkeeperId));
      setCheckBoxValues(bookkeeperIds);
    } else {
      setCheckBoxValues([]);
    }
  };

  const setActionMessage = useCallback(
    (display = false, type = "", message = "") => {
      setMessageBox({ display, type, message });
      setTimeout(() => {
        setMessageBox({ display: false, type: "", message: "" });
      }, 3000);
    },
    []
  );
  return (
    <>
      <Box className="sport-tab">
        {isEventLoading && <Loader />}
        <Grid item xs={12}>
          <Box className="racing-tab">
            {/* Racing DropDown */}
            <Box className="select-wrap">
              <Select
                className="React"
                value={eventData?.find((item) => {
                  return item?.value == selectedEvent;
                })}
                onChange={(e) => handleEvent(e)}
                options={eventData}
                classNamePrefix="select"
                isSearchable={false}
              />
            </Box>
            {/* Racing Location */}
            {eventLocation?.length > 0 ? (
              <Box className="racing-location">
                <ul>
                  {eventLocation[0]?.country ? (
                    <li>
                      <img
                        src={
                          eventLocation[0]?.country?.country_flag?.includes(
                            "uploads"
                          )
                            ? config.mediaUrl +
                              eventLocation[0]?.country?.country_flag
                            : eventLocation[0]?.country?.country_flag
                        }
                        width="22px"
                        alt="Race Country"
                      />
                      <span>{eventLocation[0]?.country?.country}</span>
                    </li>
                  ) : (
                    ""
                  )}
                  {eventLocation[0]?.state ? (
                    <li>
                      <span>{eventLocation[0]?.state?.state}</span>
                    </li>
                  ) : (
                    ""
                  )}
                  <li>
                    <span>{eventLocation[0]?.eventName}</span>
                  </li>
                </ul>
              </Box>
            ) : (
              ""
            )}

            {/* Racing Track */}
            <Box className="racing-track">
              <span>Track :</span>
              {/* <Rating name="customized-10" value={5} readOnly /> */}
              <span>{raceTrackdata?.trackCondition?.replace(/"/g, "")}</span>
            </Box>
            {/* Racing Wether */}
            <Box className="racing-Weather">
              <span>Weather:</span>
              <Box className="weather">
                {raceTrackdata?.Weather &&
                raceTrackdata?.Weather?.weatherType ? (
                  <>
                    <img
                      src={WeatherIcon(raceTrackdata?.Weather?.weatherType)}
                      alt="weather"
                    />
                    <span>{raceTrackdata?.Weather?.weatherType}</span>{" "}
                  </>
                ) : (
                  ""
                )}
              </Box>
            </Box>

            <Box className="racing-Modal">
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "5px 10px",
                  marginRight: "10px",
                }}
                // onClick={() =>
                //   inputModal(raceTrackdata?.id, raceTrackdata?.sportId)
                // }
                // onClick={() => {
                //   // this.setState(
                //   //   {
                //   //     selectedEvent: sportsDetails?.raceName,
                //   //     raceIdToSend: sportsDetails?.id,
                //   //   },
                //   setselectedEvent(raceTrackdata?.raceName)
                //   setraceIdToSend(raceTrackdata?.id)
                //     () => this.showIdentifire(raceTrackdata?.id)
                //   // );
                // }}
                // onClick={() => {
                //   {
                //     setselectedIdentifier(raceTrackdata?.raceName);
                //     setraceIdToSend(raceTrackdata?.id);
                //   }
                // }}
                onClick={() => {
                  showIdentifire(raceTrackdata?.id);
                  setraceIdToSend(raceTrackdata?.id);
                  setselectedIdentifier(raceTrackdata?.raceName);
                }}
              >
                View/Add Identifier
              </Button>

              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "5px 10px ",
                }}
                onClick={() =>
                  inputModal(raceTrackdata?.id, raceTrackdata?.sportId)
                }
              >
                Edit Race
              </Button>
            </Box>
          </Box>
        </Grid>

        {/* Tracklist TabBar */}

        <Box className="race-track-list">
          <Tabs
            value={Number(selectedRaceId)}
            onChange={handleChangeTrack}
            className="race-track"
          >
            {eventLocation[0]?.race.length > 0 &&
              eventLocation[0]?.race?.map((obj, i) => (
                <Tab
                  disableRipple
                  disableFocusRipple
                  label={obj?.raceNumber}
                  key={i}
                  value={obj?.id}
                  disabled={obj?.id === undefined ? true : false}
                />
              ))}
          </Tabs>
        </Box>
        {/* Racing Detail */}

        <Box className="racing-detail-head">
          <Box>
            <h6>
              <b>{raceTrackdata?.raceName}</b>
            </h6>
          </Box>
          <Box>
            <span>Distance: </span>
            <span>
              {raceTrackdata?.Distance?.name}
              {"m"}
            </span>
          </Box>
          <Box>
            <span>Jump Time: </span>
            <span>
              {moment(new Date(), "YYYY-MM-DD HH:mm:ss").isSameOrBefore(
                moment(raceTrackdata?.startTimeDate, "YYYY-MM-DD HH:mm:ss")
              )
                ? moment
                    .utc(raceTrackdata?.startTimeDate)
                    .local()
                    .format("hh:mm a")
                : moment
                    .utc(raceTrackdata?.startTimeDate)
                    .local()
                    .format("YYYY/MM/DD hh:mm a")}
            </span>
          </Box>
          <Box>
            <span>Time till jump: </span>
            <span>
              <RaceCountdown
                expiryTimestamp={new Date(
                  raceTrackdata?.startTimeDate
                ).getTime()}
              />
            </span>
          </Box>
        </Box>

        {/* Racing Comment */}
        <Box className="race-comment">
          <h6>Race Comments</h6>
          <Typography variant="body2">
            {raceTrackdata?.comment == "{}"
              ? "No Comment Available"
              : raceTrackdata?.comment
              ? raceTrackdata?.comment
              : "No Comment Available"}
          </Typography>
        </Box>
      </Box>
      <Modal
        className="modal modal-input"
        open={isModalOpen}
        onClose={() => toggleInputModal()}
      >
        <div
          className={"paper modal-show-scroll"}
          style={{ position: "relative" }}
        >
          <h3 className="text-center">
            {/* {isEditMode ? "Edit Runners" : "Add Runners"} */}
            Edit Race
          </h3>
          <CancelIcon
            className="admin-close-icon"
            onClick={() => toggleInputModal()}
          />
          <RunnerModal
            inputModal={() => toggleInputModal()}
            modalId={modalId}
            sportId={sportId}
            sportAll={sportAll}
            Racedistance={distanceAll}
            Raceweather={weatherAll}
            fetchAllRace={fetchAllRaces}
          />
        </div>
      </Modal>
      <Modal
        className="modal modal-input"
        open={isInputFormModalOpen}
        onClose={toggleInputFormModal}
      >
        <div
          className={"paper modal-show-scroll"}
          style={{ position: "relative" }}
        >
          <h3 className="text-center">
            {!isEditMode
              ? "Create Bookmaker And Feed Identifier"
              : "Edit Bookmaker And Feed Identifier"}
          </h3>
          <CancelIcon
            className="admin-close-icon"
            onClick={toggleInputFormModal}
          />
          <CreateApiRaceIdentifire
            inputModal={toggleInputFormModal}
            id={idToSend}
            raceIdToSend={raceIdToSend}
            isEditMode={isEditMode}
            fetchAllRaceIdentifire={showIdentifire}
            isFixture={true}
            allRaces={allRaces}
            allApiProvider={allProvider}
          />
        </div>
      </Modal>
      <Modal
        className="modal modal-input view-identifier-modal"
        open={identifireModalOpen}
        onClose={toggleIdentifireModal}
      >
        <div
          className={"paper modal-show-scroll"}
          // style={{ position: "relative" }}
        >
          <Grid container>
            <Grid item xs={12}>
              <h3 className="text-center">{`Bookmaker Feed Identifiers (${selectedIdentifier})`}</h3>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Box
              style={{
                position: "absolute",
                top: "10px",
                width: "94%",
                zIndex: "11",
              }}
            >
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
            </Box>
            <div
              style={{
                marginTop: "15px",
                marginBottom: "10px",
                display: "flex",
              }}
            >
              <Grid item xs={6}>
                <Button
                  onClick={() => syncSelectedOdds()}
                  className="info-btn"
                  style={{ cursor: "pointer" }}
                  disabled={checkBoxValues?.length == 0}
                >
                  {" "}
                  Sync Selected
                </Button>
              </Grid>
              <Grid
                item
                xs={6}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
              >
                <input
                  type="text"
                  placeholder="search"
                  value={searchIdentifire}
                  onChange={(e) => {
                    // this.setState({
                    //   ...this.state.searchIdentifire,
                    //   searchIdentifire: e.target.value,
                    // });
                    setsearchIdentifire(...searchIdentifire);
                    setsearchIdentifire(e.target.value);
                  }}
                  style={{
                    fontSize: "16px",
                    borderRadius: "8px",
                    minHeight: "35px",
                    border: "1px solid #ddd",
                    marginRight: "8px",
                    width: "100%",
                    maxWidth: "300px",
                    padding: "1px 5px",
                  }}
                />
                <Button
                  // style={{
                  //   textTransform: "none",
                  //   height: "40px",
                  //   fontSize: "12px",
                  //   backgroundColor: "RGB(68, 85, 199)",
                  //   color: "#fff",
                  //   fontWeight: "400",
                  // }}
                  className="info-btn"
                  onClick={inputFormModal(null, false)}
                >
                  Add Identifier
                </Button>
              </Grid>
            </div>
          </Grid>

          <CancelIcon
            className="admin-close-icon"
            onClick={toggleIdentifireModal}
            style={{ top: "15px", right: "15px" }}
          />
          {isDeleteLoading === "identifireDelete" && (
            <div class="admin-delete-modal-loader">
              <Loader />
            </div>
          )}
          {isLoading && (
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Loader />
            </div>
          )}
          {!isLoading && searchData?.length === 0 && <p>No Data Available</p>}
          {!isLoading && searchData?.length > 0 && (
            <>
              <TableContainer component={Paper}>
                <Table
                  className="listTable api-provider-listTable bookkeeper-identifier-table"
                  aria-label="simple table"
                  style={{ minWidth: "max-content" }}
                >
                  <TableHead>
                    <TableRow className="tableHead-row">
                      <TableCell>
                        {" "}
                        <Checkbox
                          className="mz-checkbox"
                          checked={selectAllChecked}
                          value={selectAllChecked}
                          onChange={(e) => handleSelectAll(e)}
                          style={{ padding: "0px" }}
                        />
                        Select
                      </TableCell>
                      <TableCell>DID</TableCell>
                      <TableCell style={{ width: "90px" }}>
                        Race FeedId
                      </TableCell>
                      <TableCell>Bookmaker</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell style={{ width: "80px" }}>CreatedAt</TableCell>
                      <TableCell>URL</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {searchData.map((api, i) => (
                      <TableRow key={i}>
                        <TableCell>
                          {api?.type === "scrap" && (
                            <Checkbox
                              className="mz-checkbox"
                              checked={checkBoxValues?.includes(
                                api?.bookkeeperId
                              )}
                              value={api?.bookkeeperId}
                              onChange={(e) => handleCheckBoxChange(e, api)}
                            />
                          )}
                        </TableCell>
                        <TableCell>{api?.id}</TableCell>
                        <TableCell>{api?.apiRaceId}</TableCell>
                        <TableCell>{getProvider(api?.providerId)}</TableCell>
                        <TableCell>{api?.type}</TableCell>
                        <TableCell>
                          {moment(api?.createdAt).format(
                            "DD/MM/YYYY hh:mm:ss a"
                          )}
                        </TableCell>
                        <TableCell>
                          <a href={api?.url} target="_blank">
                            URL
                          </a>
                        </TableCell>
                        <TableCell>
                          <Button
                            onClick={inputFormModal(api?.id, true)}
                            className="edit-btn"
                            style={{ cursor: "pointer" }}
                          >
                            {" "}
                            Edit
                          </Button>
                          <Button
                            onClick={() => setIdentifireToDelete(api?.id)}
                            className="delete-btn"
                            style={{ cursor: "pointer" }}
                          >
                            Delete
                          </Button>
                          <Button
                            onClick={() => syncOdd(api)}
                            className="info-btn"
                            style={{ cursor: "pointer" }}
                          >
                            {" "}
                            Sync Odd
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}
        </div>
      </Modal>
      <ShowModal
        isModalOpen={isDeleteIdentifireModalOpen}
        onClose={toggleIdentifireDeleteModal}
        Content="Are you sure you want to delete?"
        onOkayLabel="Yes"
        onOkay={handleIdentifireDelete}
        onCancel={toggleIdentifireDeleteModal}
      />
    </>
  );
};

export default RunnersTablePage;
