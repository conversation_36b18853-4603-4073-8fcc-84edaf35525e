<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16.664" height="10.23" viewBox="0 0 16.664 10.23">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_1577" data-name="Path 1577" d="M294.066,479.513a3.005,3.005,0,0,0-.464-.6c-.16-.126-.21-.229-.286-.236s-.146.037-.194-.021-.071-.153-.153-.16a.14.14,0,0,0-.123.034.133.133,0,0,1-.153-.021c-.068-.048-.293-.112-.354-.153s-.03.071-.007.092-.032.007-.144-.021-.2-.021-.1.075.15.195.221.229.016.071-.1.126-1.034.686-1.391.895a10.017,10.017,0,0,0-.988.644,8.248,8.248,0,0,1-2.308.131c-.305-.052-1.77-.2-2.137-.26a2.717,2.717,0,0,0-1.445-.02c-.523.2-1.247.577-1.351,1.275s-.114,1.114.489,1.52,1.517.791,1.528,1.291a6.264,6.264,0,0,1-.052.9c-.01.177.01.4.25.478a8.495,8.495,0,0,1,1.382.6c.208.157.831.593.873.667a.577.577,0,0,1,.031.261c0,.063.167.156.26.2s.227.114.3.145.1-.124.094-.291a.666.666,0,0,0-.229-.542,2.8,2.8,0,0,1-.344-.354.709.709,0,0,0-.322-.24,7.2,7.2,0,0,1-1.123-.583.642.642,0,0,1-.29-.6c0-.313.051-.948.072-1.093a1.737,1.737,0,0,0,.063-.385c-.01-.135.061-.084.156.01a4.736,4.736,0,0,0,.925.53,10.4,10.4,0,0,0,1.029.271c.281.052.5.084.561.1s.052.126.063.2.2.147.281.2a7.181,7.181,0,0,0,.779.686,3.761,3.761,0,0,1,.613.427c.073.063.031.166.031.2s-.1.106-.3.157a3.251,3.251,0,0,1-.676.1.908.908,0,0,1-.488-.093c-.1-.052-.01-.145-.052-.208a.46.46,0,0,0-.416-.208.531.531,0,0,0-.342.114c-.052.021-.074.082.072.187s.282.261.365.282a.751.751,0,0,1,.332.177c.1.1.239.292.426.261s.686-.219.852-.271a1.665,1.665,0,0,1,.353-.084.264.264,0,0,0,.3-.124c.114-.177.145-.21.156-.292s.082-.24-.073-.354a3.736,3.736,0,0,1-.457-.687c-.082-.114-.311-.52-.311-.52a2.542,2.542,0,0,0,.342-.145c.157-.084.363-.3.447-.313a1.391,1.391,0,0,0,.374-.166,1.07,1.07,0,0,0,.354-.5,3.447,3.447,0,0,0,.218-.894c.021-.261.041-.437.041-.437a.652.652,0,0,1,.115-.247,5.333,5.333,0,0,0,.464-.714,1,1,0,0,1,.313-.423.584.584,0,0,1,.361-.1c.123.014.226.092.338.112s.537.29.695.332.317.1.345.167.023.16.1.16.29.034.3-.014.044-.081.1-.057.221-.048.235-.123-.027-.174.041-.222.123-.178.055-.249-.139-.13-.194-.194-.475-.641-.566-.758A1.464,1.464,0,0,1,294.066,479.513Z" transform="translate(-282.537 -478.344)" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_1576" data-name="Path 1576" d="M294.066,479.513a3.005,3.005,0,0,0-.464-.6c-.16-.126-.21-.229-.286-.236s-.146.037-.194-.021-.071-.153-.153-.16a.14.14,0,0,0-.123.034.133.133,0,0,1-.153-.021c-.068-.048-.293-.112-.354-.153s-.03.071-.007.092-.032.007-.144-.021-.2-.021-.1.075.15.195.221.229.016.071-.1.126-1.034.686-1.391.895a10.017,10.017,0,0,0-.988.644,8.248,8.248,0,0,1-2.308.131c-.305-.052-1.77-.2-2.137-.26a2.717,2.717,0,0,0-1.445-.02c-.523.2-1.247.577-1.351,1.275s-.114,1.114.489,1.52,1.517.791,1.528,1.291a6.264,6.264,0,0,1-.052.9c-.01.177.01.4.25.478a8.495,8.495,0,0,1,1.382.6c.208.157.831.593.873.667a.577.577,0,0,1,.031.261c0,.063.167.156.26.2s.227.114.3.145.1-.124.094-.291a.666.666,0,0,0-.229-.542,2.8,2.8,0,0,1-.344-.354.709.709,0,0,0-.322-.24,7.2,7.2,0,0,1-1.123-.583.642.642,0,0,1-.29-.6c0-.313.051-.948.072-1.093a1.737,1.737,0,0,0,.063-.385c-.01-.135.061-.084.156.01a4.736,4.736,0,0,0,.925.53,10.4,10.4,0,0,0,1.029.271c.281.052.5.084.561.1s.052.126.063.2.2.147.281.2a7.181,7.181,0,0,0,.779.686,3.761,3.761,0,0,1,.613.427c.073.063.031.166.031.2s-.1.106-.3.157a3.251,3.251,0,0,1-.676.1.908.908,0,0,1-.488-.093c-.1-.052-.01-.145-.052-.208a.46.46,0,0,0-.416-.208.531.531,0,0,0-.342.114c-.052.021-.074.082.072.187s.282.261.365.282a.751.751,0,0,1,.332.177c.1.1.239.292.426.261s.686-.219.852-.271a1.665,1.665,0,0,1,.353-.084.264.264,0,0,0,.3-.124c.114-.177.145-.21.156-.292s.082-.24-.073-.354a3.736,3.736,0,0,1-.457-.687c-.082-.114-.311-.52-.311-.52a2.542,2.542,0,0,0,.342-.145c.157-.084.363-.3.447-.313a1.391,1.391,0,0,0,.374-.166,1.07,1.07,0,0,0,.354-.5,3.447,3.447,0,0,0,.218-.894c.021-.261.041-.437.041-.437a.652.652,0,0,1,.115-.247,5.333,5.333,0,0,0,.464-.714,1,1,0,0,1,.313-.423.584.584,0,0,1,.361-.1c.123.014.226.092.338.112s.537.29.695.332.317.1.345.167.023.16.1.16.29.034.3-.014.044-.081.1-.057.221-.048.235-.123-.027-.174.041-.222.123-.178.055-.249-.139-.13-.194-.194-.475-.641-.566-.758A1.464,1.464,0,0,1,294.066,479.513Z" transform="translate(-282.537 -478.344)"/>
    </clipPath>
  </defs>
  <g id="Group_24476" data-name="Group 24476" transform="translate(1332.343 -1776.481)">
    <g id="Group_6616" data-name="Group 6616" transform="translate(-1321.853 1782.17)">
      <path id="Path_1510" data-name="Path 1510" d="M495.785,642.079c-.039.073-.048.083-.107.112a.207.207,0,0,1-.083.015c-.034,0,.033-.019.068-.054S495.824,642.006,495.785,642.079Z" transform="translate(-495.586 -642.055)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6617" data-name="Group 6617" transform="translate(-1322.075 1782.373)">
      <path id="Path_1511" data-name="Path 1511" d="M488.328,648.927c-.01.02-.082.112-.126.122a.359.359,0,0,1-.122.025c-.068,0,.033-.01.078-.039s-.13.092-.078.01a.66.66,0,0,1,.107-.117C488.211,648.9,488.338,648.907,488.328,648.927Z" transform="translate(-488.057 -648.91)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6618" data-name="Group 6618" transform="translate(-1322.254 1782.391)">
      <path id="Path_1512" data-name="Path 1512" d="M482.246,649.516a.335.335,0,0,1-.1.1.222.222,0,0,1-.122.015c-.024,0,.019-.01.083-.049S482.27,649.472,482.246,649.516Z" transform="translate(-482.015 -649.503)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6619" data-name="Group 6619" transform="translate(-1323.139 1782.554)">
      <path id="Path_1513" data-name="Path 1513" d="M452.681,655a1.167,1.167,0,0,1-.293.068.764.764,0,0,1-.195-.024c-.049-.015-.093-.015.039-.02s.268-.019.351-.024A.388.388,0,0,1,452.681,655Z" transform="translate(-452.146 -655.002)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6620" data-name="Group 6620" transform="translate(-1323.385 1782.355)">
      <path id="Path_1514" data-name="Path 1514" d="M444.049,648.348c-.122,0-.136-.049-.17-.044s-.078-.01.049.049a.338.338,0,0,0,.229.049C444.21,648.392,444.171,648.353,444.049,648.348Z" transform="translate(-443.842 -648.304)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6621" data-name="Group 6621" transform="translate(-1323.384 1782.043)">
      <path id="Path_1515" data-name="Path 1515" d="M443.86,637.775a.666.666,0,0,0,.209.132c.058.019.019-.005-.083-.073S443.821,637.731,443.86,637.775Z" transform="translate(-443.849 -637.755)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6622" data-name="Group 6622" transform="translate(-1323.075 1781.601)">
      <path id="Path_1516" data-name="Path 1516" d="M454.332,622.86c.063.059.112.083.121.117s-.029.015-.1-.044S454.269,622.8,454.332,622.86Z" transform="translate(-454.294 -622.838)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6623" data-name="Group 6623" transform="translate(-1323.436 1781.503)">
      <path id="Path_1517" data-name="Path 1517" d="M442.15,619.551c.073.083.127.093.2.176s-.1-.014-.151-.073S442.076,619.469,442.15,619.551Z" transform="translate(-442.117 -619.524)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6624" data-name="Group 6624" transform="translate(-1322.777 1781.57)">
      <path id="Path_1518" data-name="Path 1518" d="M464.428,621.808a1.081,1.081,0,0,1,.161.122c.039.039.025.034-.078,0s-.127-.078-.141-.108S464.394,621.788,464.428,621.808Z" transform="translate(-464.367 -621.794)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6625" data-name="Group 6625" transform="translate(-1322.703 1781.727)">
      <path id="Path_1519" data-name="Path 1519" d="M466.864,627.082c.044.024.107,0,.156.049s.078.1-.019.064S466.82,627.057,466.864,627.082Z" transform="translate(-466.852 -627.078)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6626" data-name="Group 6626" transform="translate(-1322.837 1781.749)">
      <path id="Path_1520" data-name="Path 1520" d="M462.385,627.872a.74.74,0,0,0,.171.142c.1.058-.015.024-.083-.01s-.082-.078-.126-.122S462.317,627.813,462.385,627.872Z" transform="translate(-462.321 -627.838)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6627" data-name="Group 6627" transform="translate(-1323.122 1781.371)">
      <path id="Path_1521" data-name="Path 1521" d="M452.8,615.091c.136.088.1.142-.015.059S452.662,615,452.8,615.091Z" transform="translate(-452.698 -615.048)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6628" data-name="Group 6628" transform="translate(-1322.918 1781.531)">
      <path id="Path_1522" data-name="Path 1522" d="M459.686,620.5c.073.083.034.059.078.1s.054.078-.039.019-.132-.161-.1-.132S459.613,620.42,459.686,620.5Z" transform="translate(-459.615 -620.47)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6634" data-name="Group 6634" transform="translate(-1324.399 1780.045)">
      <path id="Path_1532" data-name="Path 1532" d="M413.521,570.267a1.346,1.346,0,0,0-.6.38,3.36,3.36,0,0,1-.684.586,3.04,3.04,0,0,1-.668.379,1.066,1.066,0,0,1-.354-.052.539.539,0,0,1-.22-.2c-.016-.041-.006.052.042.1a.562.562,0,0,0,.245.149c.085.016.154.041.191.041s-.1.046-.154.046a1.029,1.029,0,0,1-.324-.19c-.058-.057-.048-.015.005.036a.711.711,0,0,0,.117.093.862.862,0,0,1-.224-.1,1.556,1.556,0,0,0-.335-.134,3.084,3.084,0,0,0-.362-.051c-.064,0-.319.01-.367.021s.047.026.127.015a1.635,1.635,0,0,1,.282-.005c.111.005.165.077.122.057s-.276-.052-.175-.015.133.052.053.072a.6.6,0,0,0-.175.077.261.261,0,0,1-.186.02c-.069-.01-.006.031.069.052a.606.606,0,0,1,.176.082,1.946,1.946,0,0,0,.489.165c.106.005.1.031.207.031s.091.036-.053.041a1.324,1.324,0,0,1-.41-.036,1.525,1.525,0,0,0-.266-.077c-.064-.01-.074-.026.112.056s-.053.015-.133-.01a.87.87,0,0,0-.239-.041c-.064,0,.047.015.122.031s.346.144.436.175a.791.791,0,0,0,.176.031c.08.015-.213,0-.293-.026s-.308-.134-.393-.149a1.269,1.269,0,0,0-.144-.015c-.037-.005.053.031.128.041a1.432,1.432,0,0,1,.3.118.741.741,0,0,0,.218.077.959.959,0,0,1-.245-.015c-.08-.021-.234-.082-.319-.113s-.17-.015-.053.015a4.215,4.215,0,0,0,.479.149c.085,0,.223.01.111.051a.882.882,0,0,1-.378-.016,1.21,1.21,0,0,1-.181-.108c-.058-.031-.064-.02.021.036a.924.924,0,0,0,.383.133s.117-.011.032.015a1.816,1.816,0,0,0-.415.175,1.529,1.529,0,0,0-.229.206c-.032.051.032,0,.1-.062a1.525,1.525,0,0,1,.26-.185c.064-.031.187-.067.24-.087s.175-.036-.016.051a2.281,2.281,0,0,0-.452.267c.1-.041.266-.149.335-.17a1.294,1.294,0,0,1,.26-.087c.069,0,.149.056.213.056a2.585,2.585,0,0,0,.511.01,1.442,1.442,0,0,0,.458-.2c.074-.051.1-.016.027.046a1.133,1.133,0,0,1-.266.165.589.589,0,0,1-.266.036c-.117-.005-.319-.036-.191.01a.641.641,0,0,0,.234.046c.032,0-.091.051-.165.067a5.381,5.381,0,0,0-.542.124.611.611,0,0,0-.175.088c-.074.051-.016.057.032.021a.466.466,0,0,1,.192-.088c.09-.02.415-.067.558-.077a2.359,2.359,0,0,0,.5-.144,1.089,1.089,0,0,0,.314-.154c.058-.056.09-.062.058-.005s-.122.1-.164.134.09,0,.127-.041.117-.108.154-.159.213-.191.165-.088a1.184,1.184,0,0,1-.309.3,1.148,1.148,0,0,1-.319.139.862.862,0,0,1-.2.026c-.08.005.069.026.138.01a1.355,1.355,0,0,0,.34-.108c.112-.062.2-.118.2-.118a1.038,1.038,0,0,1-.154.144.762.762,0,0,1-.218.1c-.106.026-.016.041.064.01a.579.579,0,0,0,.234-.113,1.724,1.724,0,0,0,.2-.18c.032-.051.1-.2.165-.288a1.344,1.344,0,0,1,.213-.268.419.419,0,0,1,.149-.072c.026,0,.011.108-.048.19a2.359,2.359,0,0,1-.25.334,1.006,1.006,0,0,1-.181.118c-.064.036-.133.082-.08.072a.494.494,0,0,0,.181-.057c.053-.036.021.026-.037.077s-.154.128-.08.1.1-.01.2-.118.208-.3.282-.38.165-.221.213-.257.245-.046.309-.082a1.7,1.7,0,0,0,.245-.19c.027-.031-.053-.54-.005-.715S413.521,570.267,413.521,570.267Z" transform="translate(-409.58 -570.267)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6635" data-name="Group 6635" transform="translate(-1323.576 1781.279)">
      <path id="Path_1533" data-name="Path 1533" d="M437.785,612.127a.792.792,0,0,0-.276-.154c-.122-.036-.208-.042-.08.031a1.081,1.081,0,0,0,.25.124A.321.321,0,0,0,437.785,612.127Z" transform="translate(-437.365 -611.947)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6636" data-name="Group 6636" transform="translate(-1323.941 1781.397)">
      <path id="Path_1534" data-name="Path 1534" d="M425.954,616.154c.069.005-.25-.052-.34-.083a2.628,2.628,0,0,0-.293-.1,1.5,1.5,0,0,0-.229-.021c-.085,0-.091.021.037.036a1.054,1.054,0,0,1,.293.067,1.547,1.547,0,0,0,.282.108C425.789,616.169,425.885,616.149,425.954,616.154Z" transform="translate(-425.031 -615.948)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6637" data-name="Group 6637" transform="translate(-1323.586 1781.761)">
      <path id="Path_1535" data-name="Path 1535" d="M437.623,628.436c-.037-.01-.256-.118-.33-.154a1.118,1.118,0,0,0-.26-.062c-.059-.01.064.026.16.062s.2.108.255.129a.413.413,0,0,0,.176.056.836.836,0,0,0,.287-.056c.058-.031-.042-.01-.138.01S437.66,628.447,437.623,628.436Z" transform="translate(-437.018 -628.219)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6638" data-name="Group 6638" transform="translate(-1321.81 1781.407)">
      <path id="Path_1536" data-name="Path 1536" d="M497.367,616.315a1.39,1.39,0,0,0-.229.272c-.058.093-.079.133-.106.175s0,.011.1-.108a3.212,3.212,0,0,1,.245-.283C497.458,616.3,497.474,616.213,497.367,616.315Z" transform="translate(-497.023 -616.268)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6641" data-name="Group 6641" transform="translate(-1323.302 1778.763)">
      <path id="Path_1542" data-name="Path 1542" d="M447.341,526.952c-.036.009-.654.271-.654.271l-.045.1s.37-.131.451-.167l.385-.171Z" transform="translate(-446.642 -526.952)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6642" data-name="Group 6642" transform="translate(-1323.655 1778.922)">
      <path id="Path_1543" data-name="Path 1543" d="M436.085,532.343a2.622,2.622,0,0,1-.4.212,2.109,2.109,0,0,1-.487.09c-.113.009-.194.009-.194.009l-.041.086s.189-.027.252-.027a3.077,3.077,0,0,0,.451-.086,1.636,1.636,0,0,1,.171-.054s-.1.438-.207.727a1.419,1.419,0,0,1-.455.655,2.927,2.927,0,0,1-.347.149.377.377,0,0,1-.131-.009l-.009.09a.334.334,0,0,0,.14,0,3.186,3.186,0,0,0,.59-.285,4.259,4.259,0,0,0,.3-.61c.036-.1.185-.709.221-.754a1.852,1.852,0,0,1,.283-.178C436.273,532.332,436.085,532.343,436.085,532.343Z" transform="translate(-434.692 -532.34)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6643" data-name="Group 6643" transform="translate(-1328.16 1777.324)">
      <g id="Group_6593" data-name="Group 6593" transform="translate(0 0)">
        <g id="Group_6592" data-name="Group 6592" clip-path="url(#clip-path)">
          <g id="Group_6591" data-name="Group 6591">
            <g id="Group_6590" data-name="Group 6590">
              <g id="Group_6589" data-name="Group 6589" clip-path="url(#clip-path-2)">
                <g id="Group_6588" data-name="Group 6588" transform="translate(-7.047 -2.661)">
                  <path id="Path_1544" data-name="Path 1544" d="M295.017,478.344H282.536v9.237h12.481v-9.237Z" transform="translate(-275.489 -475.683)"/>
                  <ellipse id="Ellipse_65" data-name="Ellipse 65" cx="4.5" cy="5.839" rx="4.5" ry="5.839" transform="matrix(0.351, -0.936, 0.936, 0.351, 0, 8.427)"/>
                  <ellipse id="Ellipse_66" data-name="Ellipse 66" cx="4.452" cy="5.777" rx="4.452" ry="5.777" transform="translate(0.075 8.404) rotate(-69.445)"/>
                  <ellipse id="Ellipse_67" data-name="Ellipse 67" cx="4.404" cy="5.715" rx="4.404" ry="5.715" transform="translate(0.15 8.381) rotate(-69.446)"/>
                  <ellipse id="Ellipse_68" data-name="Ellipse 68" cx="4.356" cy="5.653" rx="4.356" ry="5.653" transform="translate(0.225 8.358) rotate(-69.447)"/>
                  <ellipse id="Ellipse_69" data-name="Ellipse 69" cx="4.308" cy="5.591" rx="4.308" ry="5.591" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.3, 8.336)"/>
                  <ellipse id="Ellipse_70" data-name="Ellipse 70" cx="4.261" cy="5.529" rx="4.261" ry="5.529" transform="translate(0.375 8.312) rotate(-69.447)"/>
                  <ellipse id="Ellipse_71" data-name="Ellipse 71" cx="4.213" cy="5.466" rx="4.213" ry="5.466" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.45, 8.289)"/>
                  <ellipse id="Ellipse_72" data-name="Ellipse 72" cx="4.165" cy="5.404" rx="4.165" ry="5.404" transform="translate(0.524 8.267) rotate(-69.445)"/>
                  <ellipse id="Ellipse_73" data-name="Ellipse 73" cx="4.117" cy="5.342" rx="4.117" ry="5.342" transform="translate(0.6 8.243) rotate(-69.447)"/>
                  <ellipse id="Ellipse_74" data-name="Ellipse 74" cx="4.069" cy="5.28" rx="4.069" ry="5.28" transform="translate(0.675 8.221) rotate(-69.447)"/>
                  <ellipse id="Ellipse_75" data-name="Ellipse 75" cx="4.021" cy="5.218" rx="4.021" ry="5.218" transform="translate(0.75 8.198) rotate(-69.446)"/>
                  <ellipse id="Ellipse_76" data-name="Ellipse 76" cx="3.973" cy="5.156" rx="3.973" ry="5.156" transform="translate(0.825 8.174) rotate(-69.447)"/>
                  <ellipse id="Ellipse_77" data-name="Ellipse 77" cx="3.925" cy="5.094" rx="3.925" ry="5.094" transform="translate(0.899 8.152) rotate(-69.446)"/>
                  <ellipse id="Ellipse_78" data-name="Ellipse 78" cx="3.878" cy="5.032" rx="3.878" ry="5.032" transform="translate(0.975 8.129) rotate(-69.447)"/>
                  <ellipse id="Ellipse_79" data-name="Ellipse 79" cx="3.83" cy="4.97" rx="3.83" ry="4.97" transform="translate(1.05 8.105) rotate(-69.447)"/>
                  <ellipse id="Ellipse_80" data-name="Ellipse 80" cx="3.782" cy="4.907" rx="3.782" ry="4.907" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.125, 8.082)"/>
                  <ellipse id="Ellipse_81" data-name="Ellipse 81" cx="3.734" cy="4.845" rx="3.734" ry="4.845" transform="translate(1.2 8.059) rotate(-69.447)"/>
                  <ellipse id="Ellipse_82" data-name="Ellipse 82" cx="3.686" cy="4.783" rx="3.686" ry="4.783" transform="translate(1.274 8.036) rotate(-69.446)"/>
                  <ellipse id="Ellipse_83" data-name="Ellipse 83" cx="3.638" cy="4.721" rx="3.638" ry="4.721" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.35, 8.013)"/>
                  <ellipse id="Ellipse_84" data-name="Ellipse 84" cx="3.59" cy="4.659" rx="3.59" ry="4.659" transform="translate(1.425 7.99) rotate(-69.448)"/>
                  <ellipse id="Ellipse_85" data-name="Ellipse 85" cx="3.542" cy="4.597" rx="3.542" ry="4.597" transform="translate(1.5 7.967) rotate(-69.447)"/>
                  <ellipse id="Ellipse_86" data-name="Ellipse 86" cx="3.495" cy="4.535" rx="3.495" ry="4.535" transform="translate(1.574 7.945) rotate(-69.446)"/>
                  <ellipse id="Ellipse_87" data-name="Ellipse 87" cx="3.447" cy="4.473" rx="3.447" ry="4.473" transform="translate(1.649 7.921) rotate(-69.447)"/>
                  <ellipse id="Ellipse_88" data-name="Ellipse 88" cx="3.399" cy="4.411" rx="3.399" ry="4.411" transform="translate(1.725 7.899) rotate(-69.447)"/>
                  <ellipse id="Ellipse_89" data-name="Ellipse 89" cx="3.351" cy="4.348" rx="3.351" ry="4.348" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.8, 7.876)"/>
                  <ellipse id="Ellipse_90" data-name="Ellipse 90" cx="3.303" cy="4.286" rx="3.303" ry="4.286" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.875, 7.852)"/>
                  <ellipse id="Ellipse_91" data-name="Ellipse 91" cx="3.255" cy="4.224" rx="3.255" ry="4.224" transform="translate(1.949 7.83) rotate(-69.447)"/>
                  <ellipse id="Ellipse_92" data-name="Ellipse 92" cx="3.207" cy="4.162" rx="3.207" ry="4.162" transform="translate(2.024 7.806) rotate(-69.444)"/>
                  <ellipse id="Ellipse_93" data-name="Ellipse 93" cx="3.16" cy="4.1" rx="3.16" ry="4.1" transform="translate(2.1 7.783) rotate(-69.447)"/>
                  <ellipse id="Ellipse_94" data-name="Ellipse 94" cx="3.112" cy="4.038" rx="3.112" ry="4.038" transform="translate(2.175 7.76) rotate(-69.446)"/>
                  <ellipse id="Ellipse_95" data-name="Ellipse 95" cx="3.064" cy="3.976" rx="3.064" ry="3.976" transform="translate(2.25 7.737) rotate(-69.448)"/>
                  <ellipse id="Ellipse_96" data-name="Ellipse 96" cx="3.016" cy="3.914" rx="3.016" ry="3.914" transform="translate(2.324 7.714) rotate(-69.447)"/>
                  <ellipse id="Ellipse_97" data-name="Ellipse 97" cx="2.968" cy="3.851" rx="2.968" ry="3.851" transform="translate(2.399 7.691) rotate(-69.445)"/>
                  <ellipse id="Ellipse_98" data-name="Ellipse 98" cx="2.92" cy="3.789" rx="2.92" ry="3.789" transform="translate(2.475 7.668) rotate(-69.449)"/>
                  <ellipse id="Ellipse_99" data-name="Ellipse 99" cx="2.872" cy="3.727" rx="2.872" ry="3.727" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.55, 7.645)"/>
                  <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="2.824" cy="3.665" rx="2.824" ry="3.665" transform="translate(2.624 7.622) rotate(-69.446)"/>
                  <path id="Path_1545" data-name="Path 1545" d="M163.947,504.785c0-1.573,1.572-2.628,3.512-2.355s3.512,1.769,3.511,3.342-1.572,2.628-3.512,2.355S163.947,506.358,163.947,504.785Z" transform="translate(-160.411 -499.014)"/>
                  <ellipse id="Ellipse_101" data-name="Ellipse 101" cx="2.729" cy="3.541" rx="2.729" ry="3.541" transform="translate(2.774 7.576) rotate(-69.447)"/>
                  <ellipse id="Ellipse_102" data-name="Ellipse 102" cx="2.681" cy="3.479" rx="2.681" ry="3.479" transform="translate(2.85 7.554) rotate(-69.447)"/>
                  <ellipse id="Ellipse_103" data-name="Ellipse 103" cx="2.633" cy="3.417" rx="2.633" ry="3.417" transform="translate(2.925 7.53) rotate(-69.449)"/>
                  <ellipse id="Ellipse_104" data-name="Ellipse 104" cx="2.585" cy="3.354" rx="2.585" ry="3.354" transform="translate(2.999 7.508) rotate(-69.448)"/>
                  <ellipse id="Ellipse_105" data-name="Ellipse 105" cx="2.537" cy="3.292" rx="2.537" ry="3.292" transform="translate(3.074 7.483) rotate(-69.448)"/>
                  <ellipse id="Ellipse_106" data-name="Ellipse 106" cx="2.489" cy="3.23" rx="2.489" ry="3.23" transform="translate(3.149 7.46) rotate(-69.446)"/>
                  <ellipse id="Ellipse_107" data-name="Ellipse 107" cx="2.441" cy="3.168" rx="2.441" ry="3.168" transform="translate(3.225 7.438) rotate(-69.447)"/>
                  <ellipse id="Ellipse_108" data-name="Ellipse 108" cx="2.394" cy="3.106" rx="2.394" ry="3.106" transform="translate(3.3 7.415) rotate(-69.449)"/>
                  <path id="Path_1546" data-name="Path 1546" d="M182.362,519.566c0-1.329,1.328-2.22,2.967-1.99A3.251,3.251,0,0,1,188.3,520.4c0,1.329-1.328,2.22-2.967,1.99A3.251,3.251,0,0,1,182.362,519.566Z" transform="translate(-178.281 -513.719)"/>
                  <ellipse id="Ellipse_109" data-name="Ellipse 109" cx="2.298" cy="2.982" rx="2.298" ry="2.982" transform="translate(3.449 7.369) rotate(-69.446)"/>
                  <ellipse id="Ellipse_110" data-name="Ellipse 110" cx="2.25" cy="2.92" rx="2.25" ry="2.92" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.524, 7.346)"/>
                  <ellipse id="Ellipse_111" data-name="Ellipse 111" cx="2.202" cy="2.857" rx="2.202" ry="2.857" transform="translate(3.599 7.323) rotate(-69.446)"/>
                  <ellipse id="Ellipse_112" data-name="Ellipse 112" cx="2.154" cy="2.795" rx="2.154" ry="2.795" transform="translate(3.674 7.3) rotate(-69.447)"/>
                  <path id="Path_1547" data-name="Path 1547" d="M192.59,527.784c0-1.194,1.193-1.993,2.664-1.787a2.919,2.919,0,0,1,2.664,2.536c0,1.194-1.193,1.994-2.664,1.787A2.919,2.919,0,0,1,192.59,527.784Z" transform="translate(-188.206 -521.895)"/>
                  <ellipse id="Ellipse_113" data-name="Ellipse 113" cx="2.058" cy="2.671" rx="2.058" ry="2.671" transform="translate(3.824 7.254) rotate(-69.448)"/>
                  <path id="Path_1548" data-name="Path 1548" d="M196.668,531.1c0-1.139,1.139-1.9,2.543-1.705a2.786,2.786,0,0,1,2.543,2.42c0,1.139-1.139,1.9-2.543,1.706A2.787,2.787,0,0,1,196.668,531.1Z" transform="translate(-192.163 -525.192)"/>
                  <ellipse id="Ellipse_114" data-name="Ellipse 114" cx="1.963" cy="2.547" rx="1.963" ry="2.547" transform="translate(3.974 7.208) rotate(-69.448)"/>
                  <path id="Path_1549" data-name="Path 1549" d="M200.778,534.348c0-1.085,1.084-1.812,2.422-1.624a2.654,2.654,0,0,1,2.422,2.305c0,1.085-1.084,1.812-2.422,1.624A2.654,2.654,0,0,1,200.778,534.348Z" transform="translate(-196.152 -528.424)"/>
                  <ellipse id="Ellipse_115" data-name="Ellipse 115" cx="1.867" cy="2.423" rx="1.867" ry="2.423" transform="translate(4.124 7.161) rotate(-69.449)"/>
                  <path id="Path_1550" data-name="Path 1550" d="M204.856,537.628c0-1.031,1.03-1.722,2.3-1.543a2.521,2.521,0,0,1,2.3,2.19c0,1.031-1.03,1.722-2.3,1.543A2.521,2.521,0,0,1,204.856,537.628Z" transform="translate(-200.109 -531.688)"/>
                  <ellipse id="Ellipse_116" data-name="Ellipse 116" cx="1.771" cy="2.298" rx="1.771" ry="2.298" transform="translate(4.274 7.116) rotate(-69.445)"/>
                  <path id="Path_1551" data-name="Path 1551" d="M208.934,540.909c0-.977.976-1.631,2.18-1.462a2.388,2.388,0,0,1,2.18,2.075c0,.977-.976,1.631-2.18,1.462A2.388,2.388,0,0,1,208.934,540.909Z" transform="translate(-204.066 -534.952)"/>
                  <ellipse id="Ellipse_117" data-name="Ellipse 117" cx="1.675" cy="2.174" rx="1.675" ry="2.174" transform="translate(4.424 7.07) rotate(-69.449)"/>
                  <ellipse id="Ellipse_118" data-name="Ellipse 118" cx="1.628" cy="2.112" rx="1.628" ry="2.112" transform="translate(4.499 7.047) rotate(-69.447)"/>
                  <path id="Path_1552" data-name="Path 1552" d="M215.084,545.847c0-.9.895-1.5,2-1.34a2.189,2.189,0,0,1,2,1.9c0,.9-.895,1.5-2,1.34A2.189,2.189,0,0,1,215.084,545.847Z" transform="translate(-210.034 -539.864)"/>
                  <ellipse id="Ellipse_119" data-name="Ellipse 119" cx="1.532" cy="1.988" rx="1.532" ry="1.988" transform="translate(4.649 7.001) rotate(-69.448)"/>
                  <path id="Path_1553" data-name="Path 1553" d="M219.162,549.161c0-.841.84-1.4,1.877-1.259a2.057,2.057,0,0,1,1.877,1.786c0,.841-.84,1.4-1.877,1.259A2.057,2.057,0,0,1,219.162,549.161Z" transform="translate(-213.991 -543.16)"/>
                  <ellipse id="Ellipse_120" data-name="Ellipse 120" cx="1.436" cy="1.864" rx="1.436" ry="1.864" transform="translate(4.799 6.955) rotate(-69.452)"/>
                  <ellipse id="Ellipse_121" data-name="Ellipse 121" cx="1.388" cy="1.802" rx="1.388" ry="1.802" transform="matrix(0.351, -0.936, 0.936, 0.351, 4.874, 6.932)"/>
                  <ellipse id="Ellipse_122" data-name="Ellipse 122" cx="1.34" cy="1.739" rx="1.34" ry="1.739" transform="translate(4.949 6.909) rotate(-69.447)"/>
                  <path id="Path_1554" data-name="Path 1554" d="M227.35,555.722c0-.732.732-1.223,1.635-1.1a1.791,1.791,0,0,1,1.635,1.556c0,.732-.732,1.223-1.635,1.1A1.791,1.791,0,0,1,227.35,555.722Z" transform="translate(-221.937 -549.688)"/>
                  <ellipse id="Ellipse_123" data-name="Ellipse 123" cx="1.245" cy="1.615" rx="1.245" ry="1.615" transform="translate(5.098 6.862) rotate(-69.445)"/>
                  <path id="Path_1555" data-name="Path 1555" d="M231.46,558.971c0-.678.678-1.133,1.514-1.015a1.658,1.658,0,0,1,1.514,1.441c0,.678-.678,1.133-1.514,1.015A1.658,1.658,0,0,1,231.46,558.971Z" transform="translate(-225.925 -552.92)"/>
                  <ellipse id="Ellipse_124" data-name="Ellipse 124" cx="1.149" cy="1.491" rx="1.149" ry="1.491" transform="translate(5.249 6.816) rotate(-69.45)"/>
                  <ellipse id="Ellipse_125" data-name="Ellipse 125" cx="1.101" cy="1.429" rx="1.101" ry="1.429" transform="matrix(0.351, -0.936, 0.936, 0.351, 5.324, 6.794)"/>
                  <path id="Path_1556" data-name="Path 1556" d="M237.578,563.909c0-.6.6-1,1.332-.893a1.459,1.459,0,0,1,1.332,1.268c0,.6-.6,1-1.332.893A1.459,1.459,0,0,1,237.578,563.909Z" transform="translate(-231.862 -557.832)"/>
                  <path id="Path_1557" data-name="Path 1557" d="M239.617,565.566c0-.57.569-.951,1.272-.853a1.393,1.393,0,0,1,1.271,1.21c0,.57-.569.951-1.272.853A1.393,1.393,0,0,1,239.617,565.566Z" transform="translate(-233.841 -559.481)"/>
                  <path id="Path_1558" data-name="Path 1558" d="M241.688,567.224c0-.543.542-.906,1.211-.812a1.327,1.327,0,0,1,1.211,1.153c0,.543-.542.906-1.211.812A1.327,1.327,0,0,1,241.688,567.224Z" transform="translate(-235.85 -561.129)"/>
                  <path id="Path_1559" data-name="Path 1559" d="M243.727,568.846c0-.515.515-.861,1.15-.772a1.26,1.26,0,0,1,1.15,1.095c0,.515-.515.861-1.15.771A1.26,1.26,0,0,1,243.727,568.846Z" transform="translate(-237.829 -562.744)"/>
                  <ellipse id="Ellipse_126" data-name="Ellipse 126" cx="0.862" cy="1.118" rx="0.862" ry="1.118" transform="translate(5.699 6.679) rotate(-69.451)"/>
                  <path id="Path_1560" data-name="Path 1560" d="M247.8,572.162c0-.461.461-.77,1.029-.69a1.128,1.128,0,0,1,1.029.98c0,.461-.461.77-1.029.69A1.128,1.128,0,0,1,247.8,572.162Z" transform="translate(-241.786 -566.041)"/>
                  <ellipse id="Ellipse_127" data-name="Ellipse 127" cx="0.766" cy="0.994" rx="0.766" ry="0.994" transform="translate(5.848 6.633) rotate(-69.453)"/>
                  <path id="Path_1561" data-name="Path 1561" d="M251.915,575.442c0-.407.407-.679.908-.609a1,1,0,0,1,.908.864c0,.407-.407.68-.908.609A1,1,0,0,1,251.915,575.442Z" transform="translate(-245.775 -569.305)"/>
                  <path id="Path_1562" data-name="Path 1562" d="M253.954,577.1c0-.38.38-.634.848-.568a.929.929,0,0,1,.848.807c0,.38-.38.634-.848.568A.929.929,0,0,1,253.954,577.1Z" transform="translate(-247.753 -570.953)"/>
                  <path id="Path_1563" data-name="Path 1563" d="M255.993,578.689c0-.353.352-.589.787-.528a.863.863,0,0,1,.787.749c0,.353-.352.589-.787.528A.862.862,0,0,1,255.993,578.689Z" transform="translate(-249.732 -572.536)"/>
                  <path id="Path_1564" data-name="Path 1564" d="M258.033,580.348c0-.326.325-.544.727-.487a.8.8,0,0,1,.726.692c0,.326-.325.544-.727.487A.8.8,0,0,1,258.033,580.348Z" transform="translate(-251.712 -574.186)"/>
                  <path id="Path_1565" data-name="Path 1565" d="M260.072,581.97c0-.3.3-.5.666-.447a.73.73,0,0,1,.666.634c0,.3-.3.5-.666.447A.73.73,0,0,1,260.072,581.97Z" transform="translate(-253.69 -575.8)"/>
                  <path id="Path_1566" data-name="Path 1566" d="M262.143,583.628c0-.271.271-.453.606-.406a.663.663,0,0,1,.605.576c0,.271-.271.453-.606.406A.663.663,0,0,1,262.143,583.628Z" transform="translate(-255.7 -577.449)"/>
                  <path id="Path_1567" data-name="Path 1567" d="M264.182,585.285c0-.244.244-.408.545-.365a.6.6,0,0,1,.545.519c0,.244-.244.408-.545.365A.6.6,0,0,1,264.182,585.285Z" transform="translate(-257.679 -579.098)"/>
                  <path id="Path_1568" data-name="Path 1568" d="M266.221,586.909c0-.217.217-.362.484-.325a.53.53,0,0,1,.484.461c0,.217-.217.362-.484.325A.531.531,0,0,1,266.221,586.909Z" transform="translate(-259.657 -580.713)"/>
                  <path id="Path_1569" data-name="Path 1569" d="M268.26,588.566c0-.19.19-.317.424-.284a.464.464,0,0,1,.424.4c0,.19-.19.317-.424.284A.464.464,0,0,1,268.26,588.566Z" transform="translate(-261.636 -582.362)"/>
                  <path id="Path_1570" data-name="Path 1570" d="M270.3,590.223c0-.163.163-.272.363-.244a.4.4,0,0,1,.363.346c0,.163-.163.272-.363.244A.4.4,0,0,1,270.3,590.223Z" transform="translate(-263.614 -584.009)"/>
                  <path id="Path_1571" data-name="Path 1571" d="M272.37,591.847c0-.136.136-.227.3-.2a.332.332,0,0,1,.3.288c0,.136-.136.226-.3.2A.332.332,0,0,1,272.37,591.847Z" transform="translate(-265.624 -585.625)"/>
                  <path id="Path_1572" data-name="Path 1572" d="M274.41,593.5c0-.109.108-.181.242-.162a.265.265,0,0,1,.242.23c0,.108-.108.181-.242.162A.265.265,0,0,1,274.41,593.5Z" transform="translate(-267.604 -587.273)"/>
                  <path id="Path_1573" data-name="Path 1573" d="M276.449,595.161c0-.081.081-.136.182-.122a.2.2,0,0,1,.182.173c0,.081-.081.136-.182.122A.2.2,0,0,1,276.449,595.161Z" transform="translate(-269.582 -588.922)"/>
                  <path id="Path_1574" data-name="Path 1574" d="M278.488,596.784c0-.054.054-.091.121-.081a.133.133,0,0,1,.121.115c0,.054-.054.091-.121.081A.132.132,0,0,1,278.488,596.784Z" transform="translate(-271.561 -590.536)"/>
                  <path id="Path_1575" data-name="Path 1575" d="M280.527,598.442c0-.027.027-.045.061-.041a.066.066,0,0,1,.06.058c0,.027-.027.045-.061.04A.066.066,0,0,1,280.527,598.442Z" transform="translate(-273.54 -592.186)"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_6644" data-name="Group 6644" transform="translate(-1321.042 1782.751)">
      <path id="Path_1578" data-name="Path 1578" d="M524.331,661.672a1.887,1.887,0,0,0,.59.35c.338.142.557.255.646.286a.4.4,0,0,1,.252.239,1.114,1.114,0,0,1,.13.505,8.066,8.066,0,0,0-.086,1.008,1.293,1.293,0,0,1-.066.516,3.809,3.809,0,0,0-.208.591c0,.077-.02.111-.141.275s-.133.261-.274.075a.691.691,0,0,1-.153-.536c.031-.144.15-.131.239-.164a.229.229,0,0,0,.111-.3.614.614,0,0,1,.033-.361c.022-.089.075-.8.075-.977a.463.463,0,0,0-.1-.339.7.7,0,0,0-.393-.164c-.111,0-.493-.033-.668-.033s-.548-.066-.7-.066a1.161,1.161,0,0,1-.513-.142c-.208-.1-.142-.053.044-.153S524.331,661.672,524.331,661.672Z" transform="translate(-522.978 -661.672)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6645" data-name="Group 6645" transform="translate(-1320.614 1777.721)">
      <path id="Path_1579" data-name="Path 1579" d="M539.535,491.906a.314.314,0,0,0-.182-.1c-.09-.026-.247-.039-.214,0s.031.1-.072.1-.206-.013-.206.033a.2.2,0,0,1-.144.175c-.1.02-.3.125-.188.131s-.077.071-.175.071-.165,0-.249,0-.51-.076-.431-.036.04.084-.032.065-.219-.026-.129.013.081.1.016.13-.142-.033-.078,0,.221.131.13.151-.2.056-.1.1.129.136.162.182.06.1.144.059,1.058-.412,1.181-.476a6.116,6.116,0,0,0,.526-.559C539.527,491.939,539.586,491.964,539.535,491.906Z" transform="translate(-537.426 -491.785)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6646" data-name="Group 6646" transform="translate(-1323.994 1779.211)">
      <path id="Path_1580" data-name="Path 1580" d="M423.651,542.111a2.865,2.865,0,0,0-.306-.025c-.061-.016-.1.021-.053.074a.718.718,0,0,0,.285.2c.114.033.709.135.774.135s.522,0,.754-.016.167-.249.167-.249S423.782,542.144,423.651,542.111Z" transform="translate(-423.273 -542.083)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6647" data-name="Group 6647" transform="translate(-1323.925 1777.685)">
      <path id="Path_1581" data-name="Path 1581" d="M426.226,490.559c.041.025.079.2.189.3s.244.175.252.233.069.053.183.094.852.249,1.011.315.46.18.5.282a.492.492,0,0,1-.093.469c-.131.123-.241.095-.29.147a1,1,0,0,1-.241.126l-.344-.168s-.112-.092-.528-.084a1.467,1.467,0,0,1-.908-.255c-.18-.113-.391-.261-.375-.526a.8.8,0,0,1,.209-.566c.142-.156.177-.193.251-.275S426.185,490.535,426.226,490.559Z" transform="translate(-425.583 -490.551)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6648" data-name="Group 6648" transform="translate(-1323.475 1777.197)">
      <path id="Path_1582" data-name="Path 1582" d="M443.591,474.1a.947.947,0,0,1,.221.54c.026.306.373.225.257.25s-.478.032-.458.076a.788.788,0,0,1,.013.318c-.02.116-.1.144.02.241s.116.156.175.13a3.579,3.579,0,0,0,.4-.285c.1-.085.164-.162.254-.221s.077-.064.149-.007a.627.627,0,0,1,.129.2c.033.051.073.044-.044.143s-.195.229-.286.32a5.075,5.075,0,0,1-.455.441c-.084.046-.226.19-.317.157a1.05,1.05,0,0,1-.306-.306c-.039-.066-.129-.253-.149-.3a1.76,1.76,0,0,1-.118-.215c-.025-.079-.064-.085-.142-.072a.692.692,0,0,1-.24.02c-.1-.013-1.23-.191-1.321-.231a.5.5,0,0,1-.2-.2,2.671,2.671,0,0,0-.367-.331c-.072-.052.034-.152.171-.25a3.647,3.647,0,0,1,.975-.377,5.532,5.532,0,0,1,.844-.066c.155.007.5.007.61.02S443.565,474.081,443.591,474.1Z" transform="translate(-440.778 -474.066)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6649" data-name="Group 6649" transform="translate(-1323.64 1779.289)">
      <path id="Path_1583" data-name="Path 1583" d="M436.953,544.733s-.219.078-.4.156-.465.282-.689.417a.965.965,0,0,0-.33.294c-.044.057-.114.118-.2.208a.5.5,0,0,0-.118.266c0,.033.053.046.126.082s.191.126.224.1.062.008.139.041.244.2.318.208.334.016.391.016.044-.1-.049-.135a.348.348,0,0,1-.228-.188.568.568,0,0,1-.065-.3c.016-.074.114-.118.208-.175s.444-.208.566-.274a3.205,3.205,0,0,1,.35-.151c.065-.025-.012-.172-.1-.339A1.235,1.235,0,0,0,436.953,544.733Z" transform="translate(-435.217 -544.733)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6650" data-name="Group 6650" transform="translate(-1332.343 1779.941)">
      <path id="Path_1584" data-name="Path 1584" d="M145.771,566.762a1.541,1.541,0,0,0-.687.453,3.9,3.9,0,0,1-.786.7,3.465,3.465,0,0,1-.767.451,1.187,1.187,0,0,1-.407-.062.629.629,0,0,1-.253-.242c-.018-.049-.007.062.049.123a.642.642,0,0,0,.281.177c.1.019.177.049.22.049s-.116.055-.177.055a1.173,1.173,0,0,1-.373-.227c-.067-.067-.055-.018.006.043a.816.816,0,0,0,.135.11.973.973,0,0,1-.257-.123,1.75,1.75,0,0,0-.385-.159,3.418,3.418,0,0,0-.416-.061c-.074,0-.366.012-.422.024s.054.031.146.018a1.829,1.829,0,0,1,.324-.006c.128.006.19.092.141.067s-.318-.061-.2-.018.153.062.061.086a.679.679,0,0,0-.2.092.29.29,0,0,1-.214.024c-.08-.012-.007.037.079.062a.683.683,0,0,1,.2.1,2.183,2.183,0,0,0,.562.2c.122.006.116.037.238.037s.1.042-.061.049a1.472,1.472,0,0,1-.471-.043,1.7,1.7,0,0,0-.305-.092c-.073-.012-.085-.031.128.067s-.061.018-.153-.012a.967.967,0,0,0-.275-.049c-.074,0,.054.018.14.037s.4.172.5.208a.885.885,0,0,0,.2.037c.092.018-.245,0-.336-.031s-.354-.159-.452-.178a1.427,1.427,0,0,0-.165-.018c-.043-.006.061.037.147.049a1.606,1.606,0,0,1,.342.141.835.835,0,0,0,.251.092,1.059,1.059,0,0,1-.281-.018c-.091-.024-.269-.1-.367-.134s-.2-.018-.061.018a4.725,4.725,0,0,0,.55.178c.1,0,.256.012.128.061a.977.977,0,0,1-.434-.018,1.385,1.385,0,0,1-.208-.129c-.067-.037-.074-.024.024.043a1.037,1.037,0,0,0,.44.159s.135-.013.037.018a2.046,2.046,0,0,0-.477.208,1.778,1.778,0,0,0-.263.245c-.037.061.037,0,.116-.074a1.752,1.752,0,0,1,.3-.22c.074-.037.214-.079.275-.1s.2-.043-.019.061a2.6,2.6,0,0,0-.519.318c.11-.049.306-.178.385-.2a1.447,1.447,0,0,1,.3-.1c.08,0,.171.067.244.067a2.865,2.865,0,0,0,.587.012,1.629,1.629,0,0,0,.526-.239c.085-.061.116-.019.031.055a1.294,1.294,0,0,1-.306.2.654.654,0,0,1-.305.043c-.135-.006-.367-.043-.22.012a.713.713,0,0,0,.269.055c.037,0-.1.061-.189.079a6.02,6.02,0,0,0-.623.147.69.69,0,0,0-.2.1c-.085.061-.019.068.037.024a.527.527,0,0,1,.22-.1c.1-.024.477-.079.642-.092a2.644,2.644,0,0,0,.574-.172,1.235,1.235,0,0,0,.361-.184c.067-.067.1-.073.067-.006s-.14.117-.189.159.1,0,.146-.049.135-.128.177-.19.244-.227.19-.1a1.382,1.382,0,0,1-.355.355,1.3,1.3,0,0,1-.366.165.96.96,0,0,1-.227.031c-.092.006.079.031.159.012a1.518,1.518,0,0,0,.391-.129c.129-.073.226-.141.226-.141a1.212,1.212,0,0,1-.177.171.861.861,0,0,1-.251.123c-.122.031-.019.049.073.012a.656.656,0,0,0,.269-.135,2,2,0,0,0,.226-.214c.037-.061.116-.238.189-.343a1.591,1.591,0,0,1,.244-.319.473.473,0,0,1,.171-.086c.031,0,.013.129-.055.227a2.8,2.8,0,0,1-.288.4,1.15,1.15,0,0,1-.208.141c-.073.043-.152.1-.091.086a.552.552,0,0,0,.208-.068c.061-.043.024.031-.043.092s-.177.153-.091.116.116-.012.226-.141.239-.361.324-.453.189-.264.244-.306.281-.055.355-.1a1.963,1.963,0,0,0,.281-.227c.03-.037-.061-.643-.006-.851S145.771,566.762,145.771,566.762Z" transform="translate(-141.241 -566.762)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6651" data-name="Group 6651" transform="translate(-1329.368 1781.563)">
      <path id="Path_1588" data-name="Path 1588" d="M242.134,621.6a1.646,1.646,0,0,0-.263.324c-.067.11-.091.159-.122.208s0,.013.116-.128a3.8,3.8,0,0,1,.281-.337C242.238,621.585,242.256,621.481,242.134,621.6Z" transform="translate(-241.738 -621.548)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6652" data-name="Group 6652" transform="translate(-1328.763 1781.684)">
      <path id="Path_1589" data-name="Path 1589" d="M262.5,625.678c-.116.08-.394.171-.306.147a.643.643,0,0,0,.269-.086C262.614,625.654,262.614,625.6,262.5,625.678Z" transform="translate(-262.175 -625.64)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6653" data-name="Group 6653" transform="translate(-1325.389 1782.115)">
      <path id="Path_1590" data-name="Path 1590" d="M376.893,640.668c0,.225.074,1.165.074,1.441a1.7,1.7,0,0,0,.358.9c.141.124,1.344,1.068,1.512,1.142a1.3,1.3,0,0,1,.607.4c.184.249.107.208-.174.208a2.192,2.192,0,0,1-.488-.013c-.043-.025-.1-.1-.079-.155a.112.112,0,0,0-.032-.126,1.566,1.566,0,0,0-.132-.1c-.025-.014-2.243-1.784-2.351-1.859a7.586,7.586,0,0,1,.022-1.633c.011-.35.022-.6.022-.667s.009-.069.13.053A6.139,6.139,0,0,0,376.893,640.668Z" transform="translate(-376.141 -640.165)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6654" data-name="Group 6654" transform="translate(-1319.825 1779.487)">
      <path id="Path_1591" data-name="Path 1591" d="M567.509,552.012s-1.122-.172-1.313-.2-1.289-.22-1.48-.247-.321-.065-.474-.092-.175-.033-.175-.033l-.005.076s.054.02.218.071.568.1.71.132.656.126.863.159.883.11.977.137.427.081.481.087.192.013.192.013Z" transform="translate(-564.061 -551.435)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6655" data-name="Group 6655" transform="translate(-1320.7 1777.221)">
      <path id="Path_1592" data-name="Path 1592" d="M535.614,474.93a.216.216,0,0,1-.046.159c-.032.02-.032.033,0,.106s.043.106.054.137,0,.062-.054.055-.081-.006-.082.039,0,.065-.023.07-.037.017-.021.038-.01.03-.024.053,0,.07,0,.087a.065.065,0,0,1-.073.053c-.045-.005-.156-.022-.191-.022a2.248,2.248,0,0,0-.228,0c-.032.007-.157-.166-.236-.242a.929.929,0,0,1-.14-.3c-.084-.174.022-.112.1-.162a.308.308,0,0,1,.265-.061c.115.028.294-.048.4-.049A1.6,1.6,0,0,1,535.614,474.93Z" transform="translate(-534.519 -474.885)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6656" data-name="Group 6656" transform="translate(-1320.893 1776.481)">
      <path id="Path_1593" data-name="Path 1593" d="M529.206,450.138a.747.747,0,0,0-.516-.245.723.723,0,0,0-.364.1.579.579,0,0,0-.236.308c-.049.141-.014.175-.063.281s-.063.092.029.151.122.109.179.062a.443.443,0,0,1,.252-.1.405.405,0,0,0,.226.014,1.5,1.5,0,0,1,.411-.01c.069.01.205.013.225.019a1.168,1.168,0,0,0-.022-.324A.535.535,0,0,0,529.206,450.138Z" transform="translate(-527.988 -449.893)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6657" data-name="Group 6657" transform="translate(-1319.895 1776.839)">
      <path id="Path_1594" data-name="Path 1594" d="M562.069,461.981a.4.4,0,0,1,.046.233c-.018.077-.037.073-.086.066a1.266,1.266,0,0,1-.263-.05c-.052-.027-.106-.1-.042-.162a.408.408,0,0,1,.218-.1C562.01,461.957,562.047,461.95,562.069,461.981Z" transform="translate(-561.695 -461.958)" fill-rule="evenodd"/>
    </g>
  </g>
</svg>
