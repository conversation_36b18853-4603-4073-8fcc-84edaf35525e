import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  TableSortLabel,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import { config } from "../../../helpers/config";
import FileUploader from "../../../library/common/components/FileUploader";
import DeleteIcon from "@mui/icons-material/Delete";
// import "../teamsport.scss";
import "../expertTips.scss";
import moment from "moment-timezone";
import { identifiers } from "../../../library/common/constants";
import _, { includes } from "lodash";
import { fetchFromStorage } from "../../../library/utilities";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import he from "he";
import items from "../../Module/DefaultLayout/sidebar/menuItems";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import TodayIcon from "@mui/icons-material/Today";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class WeeklyNewsLetter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isCreateModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      expertTipsValues: {
        mailDate: "",
        raceDetail: "",
        modalSelectedRace: null,
        topSelection: [],
        review: "",
        mailWeek: "",
        hongKongSection: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      serachValue: "",
      isTrackLoading: false,
      isTrackRaceLoading: false,
      isTrackAllRaceLoading: false,

      stateData: [],
      filteredStateData: [],
      TrackRaceData: [],
      errorRaceDetail: "",
      errorTopSelection: "",
      errorRaceCourseUrl: "",
      tipsModalDetailsOpen: false,
      tipsModalDetails: "",
      tipsModalDetailsIsLoading: false,
      SelectedId: "",
      raceDetailContent: "",
      hongKongSectionContent: "",
      selectDateOpen: false,
      startDate: null,
      isPreviewLoading: false,
    };
  }

  componentDidMount() {
    this.fetchAllEvent(0);
  }

  componentDidUpdate(prevProps, prevState) {
    let { offset } = this.state;
    if (prevState.offset !== offset) {
      this.fetchAllEvent(offset);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEvent(0);
      this.setState({
        offset: 0,
        currentPage: 1,
      });
    }
  }

  fetchAllEvent = async (page) => {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    // /expertTips/getAllNewsLetter?mailDate=2024-04-17&limit=10&offset=0&status=pending
    try {
      const passApi = `/expertTips/getAllNewsLetter?limit=${rowPerPage}&offset=${page}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          // ExpertTipsList: data?.result?.rows,
          ExpertTipsList: data?.result,
          isLoading: false,
          ExpertTipsCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchSingleEvent = async (id, date, raceData, stateData) => {
    this.setState({ tipsModalDetailsIsLoading: true });
    try {
      const passApi = `/expertTips/newsLetter/${id}?mailDate=${date}&timezone=${timezone}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let newdata = [];
        const selectedInternationalRace = data?.result?.internationalRace?.map(
          (item) => {
            newdata.push({
              label: item?.trackName,
              value: item?.expertTipId,
            });
          }
        );
        const htmlRaceDetailString = he.decode(
          String(data?.result?.stateComment)
        );

        const htmlReviewString = he.decode(String(data?.result?.review || ""));

        const htmlHongKongString = he.decode(
          String(data?.result?.stateSectionData || "")
        );

        this.setState({
          tipsModalDetailsIsLoading: false,
          expertTipsValues: {
            mailDate: date,
            raceDetail: he.decode(String(data?.result?.stateComment)),
            modalSelectedRace: data?.result?.stateExpertTipId,
            topSelection: newdata,
            review: he.decode(String(data?.result?.review || "")),
            hongKongSection: he.decode(
              String(data?.result?.stateSectionData || "")
            ),
            mailWeek: data?.result?.weekDay,
          },
          TrackData: raceData,
          stateData: stateData,
          filteredStateData: stateData,
        });
        if (typeof htmlRaceDetailString === "string") {
          this.setState({
            raceDetailContent: htmlRaceDetailString,
          });
        }
        if (typeof htmlReviewString === "string") {
          this.setState({
            reviewContent: htmlReviewString,
          });
        }
        if (typeof htmlHongKongString === "string") {
          this.setState({
            hongKongSectionContent: htmlHongKongString,
          });
        }
        const runnerComment = data?.result?.FeaturedRaceRunners?.map((runner) =>
          newdata?.push(runner?.comment ? runner?.comment : "")
        );
      } else {
        this.setState({
          tipsModalDetailsIsLoading: false,
        });
      }
    } catch {
      this.setState({
        tipsModalDetailsIsLoading: false,
      });
    }
  };

  fetchPreviewData = async (id, date) => {
    this.setState({ isPreviewLoading: true });
    try {
      const passApi = `/expertTips/previewNewsLetter/${id}?mailDate=${date}&timezone=${timezone}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          isPreviewLoading: false,
        });

        this.setState({
          tipsModalDetails: data?.html,
        });
      } else {
        this.setState({
          isPreviewLoading: false,
        });
      }
    } catch {
      this.setState({
        isPreviewLoading: false,
      });
    }
  };

  async fetchAllTrack(id, date) {
    this.setState({ isTrackLoading: true });
    const passApi = `/expertTips/getInterNationalRace?mailDate=${date}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackLoading: false });
      let newdata = [];
      let stateData = [];
      let Track = data?.result?.map((item) => {
        newdata.push({
          label: item?.trackName,
          value: item?.expertTipId,
        });
        stateData.push({
          label: item?.stateName,
          value: item?.expertTipId,
        });
      });
      let filterData = _.unionBy(this.state?.TrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      // this.setState({
      //   TrackData: finalData,
      //   stateData: stateData,
      // });
      this.fetchSingleEvent(id, date, finalData, stateData);
    } else {
      this.setState({ isTrackLoading: false });
    }
  }

  filterStates = (selectedTrack) => {
    const { stateData } = this.state;
    let selectedTrackIds = [];
    const filterTrack = selectedTrack?.map((item) => {
      selectedTrackIds.push(item?.value);
    });
    const filterStateData = stateData?.filter((item) =>
      selectedTrackIds?.includes(item?.value)
    );
    this.setState({
      filteredStateData: filterStateData,
    });
  };
  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.sports + `?sportTypeId=1`
    );
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Races",
        value: 0,
      };
      let alldata = [alldatas, ...newdata];
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        modalSportsOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        // isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let {
      expertTipsValues,
      selectedModalSport,
      selectedValues,
      TrackAllRaceData,
      runnerCommentValues,
    } = this.state;
    let flag = true;

    if (expertTipsValues?.raceDetail?.trim() === "") {
      flag = false;
      this.setState({
        errorRaceDetail: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRaceDetail: "",
      });
    }
    if (
      expertTipsValues?.topSelection?.length == 0 ||
      expertTipsValues?.topSelection == null
    ) {
      flag = false;
      this.setState({
        errorTopSelection: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTopSelection: "",
      });
    }

    if (!Boolean(expertTipsValues?.modalSelectedRace)) {
      flag = false;
      this.setState({
        errorRaceCourseUrl: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRaceCourseUrl: "",
      });
    }
    return flag;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      selectedModalSport: null,
      TrackData: [],
      stateData: [],
      filteredStateData: [],
      expertTipsValues: {
        mailDate: "",
        raceDetail: "",
        modalSelectedRace: null,
        topSelection: [],
        review: "",
        mailWeek: "",
        hongKongSection: "",
      },
      errorStartDate: "",
      errorModalSport: "",
      errorTrack: "",
      errorAdShortCode: "",
      errorRaceDetail: "",
      errorPreview: "",
      errorTopSelection: "",
      errorTitle: "",
      errorRaceDetailTitle: "",
      errorPreviewTitle: "",
      errorRunnerByRunnerTitle: "",
      errorTipsTitle: "",
      errorRaceResultTitle: "",
      errorRaceCourseUrl: "",
      errorWeekRacePreview: "",
      errorBannerImage: "",
      defaultImage: [],
      defaultUploadImage: "",
      errorModalwpCategory: "",
      raceDetailContent: "",
      hongKongSectionContent: "",
      reviewContent: "",
      weekRacePreviewContent: "",
    });
  };

  toggleCreateModal = () => {
    this.setState({
      isCreateModalOpen: !this.state.isCreateModalOpen,
      startDate: null,
    });
  };

  createModal = () => {
    this.setState({
      isCreateModalOpen: true,
    });
  };
  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files, errorBannerImage: "" });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      // this.fetchSingleEvent(item?.id, item?.mailDate);
      this.fetchAllTrack(item?.id, item?.mailDate);

      this.setState({
        SelectedId: item?.id,
        isEditMode: true,
      });
    } else {
      this.setState({
        isEditMode: false,
        selectedModalSport: null,
        selectedValues: {},
        defaultUploadImage: "",
        raceDetailContent: "",
        hongKongSectionContent: "",
        reviewContent: "",
        weekRacePreviewContent: "",
        expertTipsValues: {
          mailDate: "",
          raceDetail: "",
          modalSelectedRace: null,
          topSelection: [],
          review: "",
          hongKongSection: "",
          mailWeek: "",
        },
      });
    }
  };

  tipsDetailsModalOpen = (item) => {
    this.fetchPreviewData(item?.id, item?.mailDate);
    this.setState({ tipsModalDetailsOpen: true });
    // this.bestBetRaceRunner(item?.RaceId, item?.FeaturedRaceRunners);
  };

  toggleTipsDetailsModalOpen = () => {
    this.setState({ tipsModalDetailsOpen: false });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (item) => () => {
    this.setState({ itemToDelete: item?.id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const {
      offset,

      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "raceName"
        ? sortEventName
        : sortType === "startDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    try {
      const passApi = `/expertTips/deleteNewsLetter/${this.state.itemToDelete}`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvent(offset);
        });
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handleSave = async () => {
    const { offset, startDate } = this.state;

    let payload = {
      mailDate: startDate,
    };

    try {
      const { status, data } = await axiosInstance.post(
        `/expertTips/createNewsLetter`,
        payload
      );
      if (status === 200) {
        this.setState({
          isCreateModalOpen: false,
        });
        this.fetchAllEvent(offset);
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({
          isCreateModalOpen: false,
        });
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setState({
        isCreateModalOpen: false,
      });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handleUpdate = async () => {
    const { expertTipsValues, offset, raceDetailContent, SelectedId } =
      this.state;
    // if (this.handalValidate()) {
    this.setState({ isLoading: true, isEditMode: true });
    let seletedRaceIds = [];
    const selectedRace = expertTipsValues?.topSelection?.map((item) =>
      seletedRaceIds?.push(item?.value)
    );
    let payload = {
      mailDate: expertTipsValues?.mailDate,
      expertTips: seletedRaceIds,
      stateExpertTipId: expertTipsValues?.modalSelectedRace,
      stateComment: expertTipsValues?.raceDetail,
      review: expertTipsValues?.review,
      stateSectionData: expertTipsValues?.hongKongSection,
    };

    try {
      const { status, data } = await axiosInstance.put(
        `/expertTips/updateNewsLetter/${SelectedId}`,
        payload
      );
      if (status === 200) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          raceDetailContent: "",
          hongKongSectionContent: "",
        });
        this.fetchAllEvent(offset);
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          raceDetailContent: "",
          hongKongSectionContent: "",
        });
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setState({
        isLoading: false,
        isInputModalOpen: false,
        raceDetailContent: "",
        hongKongSectionContent: "",
      });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
    // }
  };

  handleClearClick = () => {
    this.setState({ serachValue: "" });
  };

  clearSelectDate = () => {
    this.setState({
      startDate: null,
      selectDateOpen: false,
    });
  };
  handleSelectedStartDate = (date) => {
    this.setState({
      startDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
  };
  handleRaceDetailChange = (content) => {
    const { expertTipsValues, errorRaceCourseUrl } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        raceDetail: content,
      },
      errorRaceCourseUrl: content?.trim() == "" ? errorRaceCourseUrl : "",
    });
  };

  handleReviewChange = (content) => {
    const { expertTipsValues } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        review: content,
      },
    });
  };

  handleHongKongSectionChange = (content) => {
    const { expertTipsValues } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        hongKongSection: content,
      },
    });
  };

  handleKeyDown = (event) => {
    var { sortDate, SelectedSport, serachValue, sortType, SelectedUsers } =
      this.state;
    if (event.key === "Enter") {
      this.fetchAllEvent(0);
      this.setState({ currentPage: 1 });
    }
  };

  shouldDisableDate = (date) => {
    if (!date) return true; // Guard clause for invalid dates
    const day = date?.getDay(); // Safely access getDay with optional chaining
    return day !== 3 && day !== 5; // Only allow Wednesday (3) and Friday (5)
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isCreateModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      ExpertTipsList,
      ExpertTipsCount,
      expertTipsValues,
      TrackData,
      stateData,
      filteredStateData,
      errorRaceDetail,
      errorPreview,
      errorRace,
      errorTopSelection,
      errorRaceCourseUrl,
      errorWeekRacePreview,
      errorBannerImage,
      tipsModalDetailsOpen,
      tipsModalDetails,
      tipsModalDetailsIsLoading,
      raceDetailContent,
      hongKongSectionContent,
      reviewContent,
      weekRacePreviewContent,
      selectDateOpen,
      startDate,
    } = this.state;
    const pageNumbers = [];
    if (ExpertTipsCount > 0) {
      for (let i = 1; i <= Math.ceil(ExpertTipsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    const user = fetchFromStorage(identifiers.user);

    return (
      <>
        {!isInputModalOpen ? (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              {/* <Paper className="pageWrapper"> */}
              {messageBox?.display && (
                <ActionMessage
                  message={messageBox?.message}
                  type={messageBox?.type}
                  styleClass={messageBox?.styleClass}
                />
              )}
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>
                  <Typography className="active_p">
                    Weekly Newsletter
                  </Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={3}>
                  <Typography variant="h1" align="left">
                    Weekly Newsletter
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={9}
                  className="admin-filter-wrap admin-fixture-wrap"
                >
                  {/*
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Race Type"
                  value={AllSportsOption?.find((item) => {
                    return item?.value == SelectedSport;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handlesportchange(e)}
                  options={AllSportsOption}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="event-search"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={serachValue}
                  onChange={(e) => {
                    this.setState({ serachValue: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {serachValue && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllEvent(
                      0,
                      sortDate,
                      SelectedSport,
                      serachValue,
                      sortType,
                      false,
                      SelectedUsers
                    );
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button> */}

                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    onClick={() => this.createModal()}
                  >
                    Add New
                  </Button>
                </Grid>
              </Grid>
              {/* {user?.role !== "expertTip" ? (
              <Grid container direction="row" alignItems="center">
                <Grid item xs={12} className="user-select-wrap cg-15">
                 
                  <Select
                    className="React teamsport-select external-select"
                    classNamePrefix="select"
                    placeholder="Select User"
                    value={AllUserOption?.find((item) => {
                      return item?.value == SelectedUsers;
                    })}
                    //   isLoading={isLoading}
                    onChange={(e) => this.handleUserchange(e)}
                    options={AllUserOption}
                  />
                </Grid>
              </Grid>
            ) : (
              <></>
            )} */}

              {isLoading && <Loader />}
              {!isLoading && ExpertTipsList?.length === 0 && (
                <p>No Data Available</p>
              )}

              {!isLoading && ExpertTipsList?.length > 0 && (
                <TableContainer component={Paper}>
                  <Table
                    className="listTable market-error-table"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer" }}
                        >
                          DID
                          {/* <TableSortLabel
                          active={true}
                          direction={
                            sortType === "id"
                              ? sortId
                                ? "asc"
                                : "desc"
                              : "desc"
                          }
                        /> */}
                        </TableCell>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("raceName")}
                          style={{ cursor: "pointer" }}
                        >
                          Email Date
                          {/* <TableSortLabel
                          active={true}
                          direction={
                            sortType === "raceName"
                              ? sortEventName
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        /> */}
                        </TableCell>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("startDate")}
                          style={{ cursor: "pointer" }}
                        >
                          Email Day
                          {/* <TableSortLabel
                          active={true}
                          direction={
                            sortType === "startDate"
                              ? sortEventDate
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        /> */}
                        </TableCell>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("SportId")}
                          style={{ cursor: "pointer" }}
                        >
                          Status
                          {/* <TableSortLabel
                          active={true}
                          direction={
                            sortType === "SportId"
                              ? sortSportType
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        /> */}
                        </TableCell>

                        <TableCell>Preview</TableCell>
                        <TableCell style={{ width: "15%" }}>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {ExpertTipsList?.map((item) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={item?.id}
                          >
                            <TableCell>{item?.id} </TableCell>
                            <TableCell>
                              {item?.mailDate
                                ? moment(item?.mailDate).format("DD/MM/YYYY")
                                : ""}
                            </TableCell>
                            <TableCell>{item?.mailWeek}</TableCell>
                            <TableCell> {item?.status}</TableCell>
                            <TableCell>
                              <Button
                                className="table-btn"
                                variant="contained"
                                style={{
                                  fontSize: "14px",
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  fontWeight: "400",
                                  textTransform: "none",
                                  width: "max-content",
                                }}
                                onClick={() => {
                                  this.tipsDetailsModalOpen(item);
                                }}
                              >
                                Preview
                              </Button>
                            </TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className="table-btn edit-btn"
                                // disabled={moment(
                                //   item?.Race?.Event?.eventDate
                                // ).isBefore(moment(), "day")}
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item)}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className={"table-btn delete-btn"}
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                ExpertTipsCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              {/* </Paper> */}

              <ShowModal
                isModalOpen={isModalOpen}
                onClose={this.toggleModal}
                Content="Are you sure you want to delete?"
                onOkayLabel="Yes"
                onOkay={this.deleteItem}
                onCancel={this.toggleModal}
              />
            </Grid>
          </Grid>
        ) : (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>
                  <Typography className="active_p">
                    Weekly NewsLetter
                  </Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={4}>
                  <Typography variant="h1" align="left">
                    Edit Weekly NewsLetter
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={8}
                  style={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    onClick={this.toggleInputModal}
                  >
                    Back
                  </Button>
                </Grid>
              </Grid>
              <Box className="expert-tips-modal">
                <Grid container>
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text mb-8"
                    >
                      <label className="modal-label">
                        International Race
                        {/* <span className="color-red">*</span> */}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select tipselection-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="International Race"
                        isMulti
                        value={expertTipsValues?.topSelection}
                        options={TrackData}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              topSelection: e,
                            },
                            errorTopSelection:
                              e && e?.length > 0 ? "" : errorTopSelection,
                          });
                          this.filterStates(e);
                        }}
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorTopSelection ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorTopSelection}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text mb-8"
                    >
                      <label className="modal-label">
                        State
                        {/* <span className="color-red">*</span> */}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="State"
                        isDisabled={expertTipsValues?.topSelection?.length == 0}
                        value={
                          expertTipsValues?.modalSelectedRace &&
                          filteredStateData?.find((item) => {
                            return (
                              item?.value == expertTipsValues?.modalSelectedRace
                            );
                          })
                        }
                        options={filteredStateData}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalSelectedRace: e?.value,
                            },

                            errorRaceCourseUrl: e?.value
                              ? ""
                              : errorRaceCourseUrl,
                          });
                        }}
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorRaceCourseUrl ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorRaceCourseUrl}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  <Grid
                    item
                    xs={12}
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      textAlign: "left",
                    }}
                    className="teamsport-text mb-8"
                  >
                    <label className="modal-label">
                      {" "}
                      State Comment
                      {/* <span className="color-red">*</span> */}
                    </label>
                    <div className="featured-race-editor news-letter-editor">
                      <SunEditor
                        onChange={this.handleRaceDetailChange}
                        setContents={raceDetailContent}
                        setOptions={{
                          buttonList: [
                            ["undo", "redo"],
                            ["font", "fontSize", "formatBlock"],
                            [
                              "bold",
                              "underline",
                              "italic",
                              "strike",
                              "subscript",
                              "superscript",
                            ],
                            ["removeFormat"],
                            ["outdent", "indent"],
                            ["align", "horizontalRule", "list", "table"],
                            ["link"],
                            ["fullScreen", "showBlocks", "codeView"],
                          ],
                          defaultStyle: "font-size: 16px;",
                        }}
                      />
                    </div>
                    {errorRaceDetail ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorRaceDetail}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      textAlign: "left",
                    }}
                    className="teamsport-text mb-8"
                  >
                    <label className="modal-label"> Hong Kong section</label>
                    <div className="featured-race-editor news-letter-editor">
                      <SunEditor
                        onChange={this.handleHongKongSectionChange}
                        setContents={hongKongSectionContent}
                        setOptions={{
                          buttonList: [
                            ["undo", "redo"],
                            ["font", "fontSize", "formatBlock"],
                            [
                              "bold",
                              "underline",
                              "italic",
                              "strike",
                              "subscript",
                              "superscript",
                            ],
                            ["removeFormat"],
                            ["outdent", "indent"],
                            ["align", "horizontalRule", "list", "table"],
                            ["link"],
                            ["fullScreen", "showBlocks", "codeView"],
                          ],
                          defaultStyle: "font-size: 16px;",
                        }}
                      />
                    </div>
                  </Grid>
                  {expertTipsValues?.mailWeek === "Wednesday" && (
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        textAlign: "left",
                      }}
                      className="teamsport-text mb-8"
                    >
                      <label className="modal-label"> Review</label>
                      <div className="featured-race-editor news-letter-editor">
                        <SunEditor
                          onChange={this.handleReviewChange}
                          setContents={reviewContent}
                          setOptions={{
                            buttonList: [
                              ["undo", "redo"],
                              ["font", "fontSize", "formatBlock"],
                              [
                                "bold",
                                "underline",
                                "italic",
                                "strike",
                                "subscript",
                                "superscript",
                              ],
                              ["removeFormat"],
                              ["outdent", "indent"],
                              ["align", "horizontalRule", "list", "table"],
                              ["link"],
                              ["fullScreen", "showBlocks", "codeView"],
                            ],
                            defaultStyle: "font-size: 16px;",
                          }}
                        />
                      </div>
                    </Grid>
                  )}
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        className="mt-3 admin-btn-green"
                        onClick={this.handleUpdate}
                        color="secondary"
                        value={!isLoading ? "Update" : "Loading..."}
                        disabled={isLoading}
                      />

                      <ButtonComponent
                        onClick={this.toggleInputModal}
                        className="mr-lr-30 back-btn"
                        value="Back"
                      />
                    </div>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        )}
        <Modal
          className="modal modal-input tips-modal-details"
          open={tipsModalDetailsOpen}
          onClose={this.toggleTipsDetailsModalOpen}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">Preview</h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleTipsDetailsModalOpen}
            />
            {tipsModalDetailsIsLoading ? (
              <Box className="modal-loader">
                <Loader />
              </Box>
            ) : (
              <Box
                className="tips-details weekly-preview"
                dangerouslySetInnerHTML={{
                  __html: tipsModalDetails,
                }}
              ></Box>
            )}
            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    onClick={this.toggleTipsDetailsModalOpen}
                    className="mr-lr-30 back-btn"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
        <Modal
          className="modal modal-input"
          open={isCreateModalOpen}
          onClose={this.toggleCreateModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">Create Weekly NewsLetter</h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleCreateModal}
            />
            <Grid container className="page-content adminLogin text-left">
              <Grid item xs={12} className="pageWrapper api-provider">
                <Grid container>
                  <Grid
                    item
                    xs={12}
                    className="teamsport-textfield date-time-picker-wrap teamsport-text"
                    style={{
                      marginTop: "15px",
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <label className="modal-label">
                      {" "}
                      Select Date <span className="color-red">*</span>{" "}
                    </label>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DesktopDatePicker
                        clearable
                        autoOk
                        placeholder="YYYY/MM/DD"
                        format="yyyy/MM/dd"
                        margin="normal"
                        id="date-picker-inline"
                        inputVariant="outlined"
                        slots={{
                          openPickerIcon: TodayIcon,
                        }}
                        slotProps={{
                          field: {
                            placeholder: "DD/MM/YYYY",
                            clearable: true,
                            // onClear: () => this.handleSelectedStartDate(null)
                          },
                        }}
                        value={startDate ? parseISO(startDate) : null} // Parse ISO string to Date object
                        onChange={(e) => this.handleSelectedStartDate(e)} // Prop function to handle date change
                        KeyboardButtonProps={{
                          "aria-label": "change date",
                        }}
                        shouldDisableDate={this.shouldDisableDate} // Hook the disable logic
                        className="date-picker-fixture expert-tips-date-picker date-time-picker"
                        style={{ margin: "0px 10px 0px 0px" }}
                      />
                    </LocalizationProvider>
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        className="mt-3 admin-btn-green"
                        // onClick={this.handleSave}
                        onClick={this.handleSave}
                        color="primary"
                        value={!isLoading ? "Save" : "Loading..."}
                        disabled={isLoading || !Boolean(startDate)}
                        // disabled={isLoading || TipsListByDate?.length === 1}
                      />

                      <ButtonComponent
                        onClick={this.toggleCreateModal}
                        className="mr-lr-30"
                        value="Back"
                      />
                    </div>
                    {/* {createError ? (
                    <p
                      className="errorText"
                      style={{ margin: "0px 0 0 0", width: "300px" }}
                    >
                      {createError}
                    </p>
                  ) : (
                    ""
                  )} */}
                  </Grid>
                </Grid>
                {/* </Paper> */}
              </Grid>
            </Grid>
          </div>
        </Modal>
      </>
    );
  }
}
export default WeeklyNewsLetter;
