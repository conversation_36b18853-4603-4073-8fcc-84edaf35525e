import React from 'react';
import './barChart.scss';

import {
  <PERSON>,
  <PERSON><PERSON>,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>Axis,
} from 'recharts';

const generateUniqueId = () => {
  return '_' + Math.random().toString(36).substr(2, 9);
};

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload?.length) {
    return (
      <div className="custom-tooltip">
        <p className="tooltip-title">{`Rd: ${label}`}</p>
        {payload.map((item) => (
          <p key={generateUniqueId()} className="tooltip-item">
            {`${item?.name}: ${item?.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const CustomLegend = ({ payload }) => {
  return (
    <div className="custom-legend">
      {payload?.map((entry) => (
        <div key={generateUniqueId()} className="legend-item">
          <span
            className="legend-color"
            style={{ backgroundColor: entry?.color }}
          ></span>
          <span className="legend-label">{entry?.value}</span>
        </div>
      ))}
    </div>
  );
};

const MyBarChart = ({ data, bars, xAxisKey }) => {
  return (
    <div className="bar-chart-wrap">
      <ResponsiveContainer width="100%" height={350}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="0" stroke="#D4D6D8" vertical={false} />
          <XAxis
            dataKey={xAxisKey}
            fontSize={12}
            fontFamily="Inter"
            color="#989898"
            padding={{ left: 16, right: 16 }}
          />
          <YAxis fontSize={12} fontFamily="Inter" color="#989898" />
          <Tooltip content={<CustomTooltip />} cursor={{ fill: 'transparent' }} />
          <Legend content={<CustomLegend />} />
          {bars?.map((bar) => (
            <Bar
              key={generateUniqueId()}
              dataKey={bar?.dataKey}
              fill={bar?.color}
              name={bar?.label || bar?.dataKey}
              radius={8}
              isAnimationActive={false}
              label={{ position: 'top' }}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default MyBarChart;
