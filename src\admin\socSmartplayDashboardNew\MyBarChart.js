import React from "react";
import "./barChart.scss";

import {
  <PERSON>,
  <PERSON><PERSON>,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON>xi<PERSON>,
} from "recharts";
import { Box } from "@mui/material";
import moment from "moment";

const generateUniqueId = () => {
  return "_" + Math.random().toString(36).substr(2, 9);
};

const CustomTooltip = ({ active, payload, label, coordinate }) => {
  console.log("object", payload, label, coordinate);
  if (active && payload?.length) {
    return (
      <div className="custom-tooltip">
        {payload.map((item) => (
          <>
            <Box className="tooltip-header">
              <p className="tooltip-title">{`${item?.name}`}</p>
              <p className="tooltip-title">
                <b>{item?.value}</b>
              </p>
            </Box>
            <p key={generateUniqueId()} className="tooltip-item">
              {moment(new Date()).format("ddd, <PERSON><PERSON>, <PERSON>YYY")}
            </p>
          </>
        ))}
      </div>
    );
  }
  return null;
};

const CustomLegend = ({ payload }) => {
  return (
    <div className="custom-legend">
      {payload?.map((entry) => (
        <div key={generateUniqueId()} className="legend-item">
          <span
            className="legend-color"
            style={{ backgroundColor: entry?.color }}
          ></span>
          <span className="legend-label">{entry?.value}</span>
        </div>
      ))}
    </div>
  );
};

const MyBarChart = ({ data, bars, xAxisKey }) => {
  console.log("object125478963", data, bars, xAxisKey);
  return (
    <div className="bar-chart-wrap">
      <ResponsiveContainer width="100%" height={350}>
        <BarChart
          data={data}
          margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
        >
          <CartesianGrid
            strokeDasharray="0"
            stroke="#D4D6D8"
            vertical={false}
          />
          <XAxis
            dataKey={xAxisKey}
            fontSize={12}
            fontFamily="Inter"
            color="#989898"
            padding={{ left: 16, right: 16 }}
          />
          <YAxis fontSize={12} fontFamily="Inter" color="#989898" />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ fill: "transparent" }}
            shared={false}
          
          />
          <Legend content={<CustomLegend />} />
          {bars?.map((bar) => (
            <Bar
              key={generateUniqueId()}
              dataKey={bar?.dataKey}
              fill={bar?.color}
              name={bar?.label || bar?.dataKey}
              radius={8}
              isAnimationActive={false}
              label={{ position: "top" }}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default MyBarChart;
