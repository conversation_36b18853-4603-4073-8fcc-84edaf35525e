import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  FormControlLabel,
  Radio,
} from "@mui/material";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import moment from "moment-timezone";
import axiosInstance from "../../helpers/Axios";
import { URLS, identifiers } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import "../TeamSport/teamsport.scss";
import "./sportExpertTips.scss";
import { fetchFromStorage } from "../../library/utilities";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
class SportExpertTips extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      itemToSelected: null,
      currentPage: 1,
      rowPerPage: 20,
      sportExpertTipsList: [],
      sportExpertTipsCount: 0,
      isInputModalOpen: false,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      sportExpertTipsValues: {
        modalTournamentId: null,
        id: null,
      },
      SelectedSport: null,
      selectedModalSport: null,
      AllSportsOption: [],
      modalSportsOption: [],
      selectedSportObj: {},
      selectedModalSportObj: {},
      externalTournamentData: [],
      externalTournamentCount: 0,
      selectTournament: null,
      SelectedExternalTournamentList: [],
      ExternalTournamentPage: 0,
      isExternalTournamentSearch: "",
      isExternalTournamentLoading: false,
      searchExternalTournamentCount: 0,
      searchExternalTournamentPage: 0,
      searchExternalTournament: [],
      searchTournament: [],
      searchTournamentCount: 0,
      searchTournamentPage: 0,
      isTournamentSearch: "",
      isTournamentLoading: false,
      sportExpertTipsValuesRadio: [],
      tipsModalDetailsOpen: false,
      tipsModalDetails: {},
      tipsModalDetailsIsLoading: false,
      modalRounds: [],
      selectedModalRound: null,
      isModalRoundLoading: false,
      noRoundError: "",
      roundExpireError: "",
      tipsModalEventDetails: [],
      authorList: [],
      selectedAuthor: null,
      allEventData: [],
      isEventLoading: false,
      errorSport: "",
      errorTournament: "",
      errorRound: "",
      tipsUserId: null,
      selectedActiveSeason: null,
    };
  }

  componentDidMount() {
    this.fetchAllSports();
    this.fetchAllEvent(1, null, null);
    // this.fetchAuthor();
  }
  componentDidUpdate(prevProps, prevState) {
    // if (prevState.currentPage !== this.state.currentPage) {
    // }
    if (prevProps.match.path !== this.props.match.path) {
    }
  }

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  fetchAllEvent = async (page, sportId, tournamentId) => {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true, currentPage: page });
    try {
      const passApi = `expertTips/getAll?limit=${rowPerPage}&page=${page}&SportId=${
        sportId ? sportId : ""
      }&tournamentId=${tournamentId ? tournamentId : ""}`;

      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          sportExpertTipsList: data?.result?.rows,
          isLoading: false,
          sportExpertTipsCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.sports + `?sportTypeId=2`
    );
    if (status === 200) {
      let newdata = [];
      const filteredSportData = data?.result?.filter(
        (item) => item?.id == 12 || item?.id == 9 || item?.id == 4
      );
      let sportData = filteredSportData?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Sport",
        value: 0,
      };

      let alldata = [alldatas, ...newdata];
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        modalSportsOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        isLoading: false,
      });
    }
  }

  fetchAuthor = async (tipId) => {
    try {
      const passApi = `/user?role=expertTip&ExpertSportTipId=${tipId}`;
      const user = fetchFromStorage(identifiers.user);
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let newdata = [];
        let authorData = data?.result?.data?.map((item) => {
          newdata.push({
            label: item?.firstName + " " + item?.lastName,
            value: item?.id,
          });
        });
        const sortedAuthor = newdata?.sort((a, b) => {
          return a.label.toLowerCase() > b.label.toLowerCase() ? 1 : -1;
        });
        let adminuser = sortedAuthor?.unshift({
          label: "You",
          value: user?.id,
        });
        const uniqueAuthor = _.uniqBy(sortedAuthor, function (e) {
          return e.value;
        });

        this.setState({
          authorList: uniqueAuthor,
          selectedAuthor: user?.id,
        });
      } else {
        this.setState({});
      }
    } catch {
      this.setState({});
    }
  };

  handlesportchange = (e) => {
    this.setState({
      SelectedSport: e.value,
      externalTournamentData: [],
      externalTournamentCount: 0,
      ExternalTournamentPage: 0,
      isExternalTournamentSearch: "",
      searchExternalTournamentCount: 0,
      searchExternalTournamentPage: 0,
      searchExternalTournament: [],
      selectTournament: null,
    });
    let obj = {};
    switch (e.value) {
      case 4:
        obj = {
          passApi: "crickets",
          SportId: e.value,
        };

        break;
      case 5:
        obj = {
          passApi: "mma",
          SportId: e.value,
        };

        break;
      case 6:
        obj = {
          passApi: "boxing",
          SportId: e.value,
        };

        break;
      case 7:
        obj = {
          passApi: "tennis",
          SportId: e.value,
        };

        break;
      case 8:
        obj = {
          passApi: "soccer",
          SportId: e.value,
        };

        break;
      case 9:
        obj = {
          passApi: "ar",
          SportId: e.value,
        };

        break;
      case 10:
        obj = {
          passApi: "nba",
          SportId: e.value,
        };

        break;
      case 11:
        obj = {
          passApi: "baseball",
          SportId: e.value,
        };

        break;
      case 12:
        obj = {
          passApi: "rls",
          SportId: e.value,
        };

        break;
      case 13:
        obj = {
          passApi: "rls",
          SportId: e.value,
        };

        break;
      case 15:
        obj = {
          passApi: "afl",
          SportId: e.value,
        };

        break;
      case 16:
        obj = {
          passApi: "golf",
          SportId: e.value,
        };

        break;
      case 17:
        obj = {
          passApi: "icehockey",
          SportId: e.value,
        };

        break;
      default:
        obj = {
          passApi: null,
          SportId: null,
        };
        break;
    }
    this.setState({
      selectedSportObj: obj,
    });
    this.fetchAllEvent(1, e.value, null);
    if (e.value) {
      this.fetchAllExternalTournament(0, obj);
    }
  };

  fetchAllExternalTournament = async (TournamentPage, obj) => {
    this.setState({ isExternalTournamentLoading: true });
    const passApi = !obj?.passApi?.includes("rls")
      ? `${obj?.passApi}/tournament?limit=20&offset=${TournamentPage}`
      : `rls/tournament?limit=20&offset=${TournamentPage}&SportId=${obj?.SportId}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: obj?.passApi?.includes("nba")
            ? item?.name + " " + item?.NBACategory?.name
            : item?.name,
          value: item?.id,
        });
      });
      let filterdata = newdata?.filter((item) => item?.value !== 0);
      let mergeData = _.unionBy(this.state?.externalTournamentData, filterdata);
      const sortedData = mergeData?.sort((a, b) => {
        return a?.label?.localeCompare(b?.label);
      });
      let alldatas = sortedData?.unshift({
        label: "All Tournaments",
        value: 0,
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        externalTournamentData: finalData,
        externalTournamentCount: Math.ceil(count),
        ExternalTournamentPage: TournamentPage,
      });
    }
  };

  handleOnScrollBottomExternalTournament = (e, type) => {
    let {
      externalTournamentCount,
      ExternalTournamentPage,
      isExternalTournamentSearch,
      searchExternalTournamentCount,
      searchExternalTournamentPage,
    } = this.state;
    if (
      isExternalTournamentSearch !== "" &&
      searchExternalTournamentCount !==
        Math.ceil(searchExternalTournamentPage / 20 + 1)
    ) {
      this.handleExternalTournamentInputChange(
        searchExternalTournamentPage + 20,
        isExternalTournamentSearch,
        this.state.selectedSportObj
      );
      this.setState({
        searchExternalTournamentPage: searchExternalTournamentPage + 20,
      });
    } else {
      if (
        externalTournamentCount !== Math.ceil(ExternalTournamentPage / 20) &&
        isExternalTournamentSearch == ""
      ) {
        this.fetchAllExternalTournament(
          ExternalTournamentPage + 20,
          this.state.selectedSportObj
        );
        this.setState({
          ExternalTournamentPage: ExternalTournamentPage + 20,
        });
      }
    }
  };
  handleExternalTournamentInputChange = (
    ExternalTournamentPage,
    value,
    obj
  ) => {
    const passApi = !obj?.passApi?.includes("rls")
      ? `${obj?.passApi}/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : `rls/tournament?limit=20&offset=${ExternalTournamentPage}&SportId=${obj?.SportId}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: obj?.passApi?.includes("nba")
              ? item?.name + " " + item?.NBACategory?.name
              : item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(
          this.state?.searchExternalTournament,
          filterdata
        );
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label?.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Tournaments",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchExternalTournament: finalData,
          searchExternalTournamentCount: Math.ceil(count),
          isExternalTournamentSearch: value,
          searchExternalTournamentPage: ExternalTournamentPage,
        });
      }
    });
  };
  handleExternalTournamentChange = (e) => {
    let { currentPage, SelectedSport } = this.state;
    this.setState({
      selectTournament: e.value,
      externalTeamData: [],
      selectTeam: null,
    });
    this.fetchAllEvent(1, SelectedSport, e.value);
  };

  handlePaginationClick = (event, page) => {
    let { SelectedSport, selectTournament } = this.state;
    this.fetchAllEvent(Number(page), SelectedSport, selectTournament);
  };

  inputModal = (item, type) => () => {
    const { authorList } = this.state;
    this.setState({ isInputModalOpen: true, itemToSelected: item?.id });
    if (type === "edit") {
      this.fetchAuthor(item?.id);
      const user = fetchFromStorage(identifiers.user);
      this.getAllEvents(
        item?.SportId,
        item?.tournamentId,
        item?.round,
        item?.id,
        user?.id,
        item?.seasonId
      );
      // const authourData = [
      //   ...authorList,
      //   {
      //     label: item?.User?.firstName + " " + item?.User?.lastName,
      //     value: item?.User?.id,
      //   },
      // ];
      // const sortedAuthor = _.uniqBy(authourData, function (e) {
      //   return e.value;
      // });
      // const isUserExisted = authorList?.some(
      //   (user) => user?.value == item?.User?.id
      // );
      // const userId =
      //   user?.id !== item?.User?.id && !isUserExisted ? item?.User?.id : null;
      this.setState({
        isEditMode: true,
        selectedModalSport: item?.SportId,
        selectedModalRound: item?.round?.toString(),
        selectedAuthor: user?.id,
        sportExpertTipsValues: {
          modalTournamentId: item?.tournamentId,
          id: item?.id,
        },
        modalRounds: [
          {
            label: `${
              item?.displayName
                ? item?.displayName
                : (item?.SportId == 4 ? "Day" : "Round") + " " + item?.round
            } `,
            value: item?.round,
          },
        ],
        TournamentData: [
          {
            label: item?.tournamentName,
            value: item?.tournamentId,
            activeSeason: item?.seasonId,
          },
        ],
        selectedActiveSeason: item?.seasonId,
        // authorList: sortedAuthor,
        // tipsUserId: userId,
      });
    } else {
      const user = fetchFromStorage(identifiers.user);
      this.setState({
        sportExpertTipsValues: {
          modalTournamentId: null,
          id: null,
        },
        isEditMode: false,
        selectedModalSport: null,
        selectedModalRound: null,
        selectedAuthor: user?.id,
      });
    }
  };

  toggleInputModal = () => {
    const { sportExpertTipsValues, authorList, tipsUserId } = this.state;
    // const RemoveOtherAdmin = tipsUserId
    //   ? authorList?.filter((item) => item?.value !== tipsUserId)
    //   : authorList;
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorSport: "",
      errorTournament: "",
      errorRound: "",
      TournamentPage: 0,
      selectedModalSport: null,
      selectedModalRound: null,
      sportExpertTipsValues: {
        ...sportExpertTipsValues,
        modalTournamentId: null,
      },
      selectedAuthor: null,
      allEventData: [],
      noRoundError: "",
      authorList: [],
      itemToSelected: null,
      roundExpireError: "",
    });
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  handalValidate = () => {
    let {
      selectedModalSport,
      sportExpertTipsValues,
      selectedModalRound,
      roundExpireError,
    } = this.state;
    let flag = true;
    if (!selectedModalSport) {
      flag = false;
      this.setState({
        errorSport: "This field is mandatory",
      });
    } else {
      this.setState({
        errorSport: "",
      });
    }
    if (selectedModalSport) {
      if (!sportExpertTipsValues?.modalTournamentId) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    }
    if (sportExpertTipsValues?.modalTournamentId) {
      if (!selectedModalRound) {
        flag = false;
        this.setState({
          errorRound: "This field is mandatory",
        });
      } else {
        this.setState({
          errorRound: "",
        });
      }
    }
    if (selectedModalRound && roundExpireError === "") {
      flag = true;
    } else {
      flag = false;
    }
    return flag;
  };

  handleModalsportchange = (e) => {
    this.setState({
      selectedModalSport: e?.value,
      TournamentData: [],
      TournamentCount: 0,
      TournamentPage: 0,
      searchTournament: [],
      searchTournamentCount: 0,
      searchTournamentPage: 0,
      isTournamentSearch: "",
      sportExpertTipsValues: {
        ...this.state.sportExpertTipsValues,
        modalTournamentId: null,
      },
      selectedModalRound: null,
      noRoundError: "",
      roundExpireError: "",
      errorSport: e?.value ? "" : this.state?.errorSport,
      errorTournament: "",
      selectedActiveSeason: null,
    });

    let obj = {};
    switch (e.value) {
      case 4:
        obj = {
          passApi: "crickets",
          SportId: e.value,
        };

        break;
      case 5:
        obj = {
          passApi: "mma",
          SportId: e.value,
        };

        break;
      case 6:
        obj = {
          passApi: "boxing",
          SportId: e.value,
        };

        break;
      case 7:
        obj = {
          passApi: "tennis",
          SportId: e.value,
        };

        break;
      case 8:
        obj = {
          passApi: "soccer",
          SportId: e.value,
        };

        break;
      case 9:
        obj = {
          passApi: "ar",
          SportId: e.value,
        };

        break;
      case 10:
        obj = {
          passApi: "nba",
          SportId: e.value,
        };

        break;
      case 11:
        obj = {
          passApi: "baseball",
          SportId: e.value,
        };

        break;
      case 12:
        obj = {
          passApi: "rls",
          SportId: e.value,
        };

        break;
      case 13:
        obj = {
          passApi: "rls",
          SportId: e.value,
        };

        break;
      case 15:
        obj = {
          passApi: "afl",
          SportId: e.value,
        };

        break;
      case 16:
        obj = {
          passApi: "golf",
          SportId: e.value,
        };

        break;
      case 17:
        obj = {
          passApi: "icehockey",
          SportId: e.value,
        };

        break;
      default:
        obj = {
          passApi: null,
          SportId: null,
        };
        break;
    }
    this.setState({
      selectedModalSportObj: obj,
    });
    this.fetchAllTournament(0, obj);
  };

  async fetchAllTournament(TournamentPage, obj) {
    const passApi = !obj?.passApi?.includes("rls")
      ? `${obj?.passApi}/tournament?limit=20&offset=${TournamentPage}`
      : `rls/tournament?limit=20&offset=${TournamentPage}&SportId=${obj?.SportId}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let tournament = data?.result?.rows?.map((item) => {
        newdata.push({
          label: obj?.passApi?.includes("nba")
            ? item?.name + " " + item?.NBACategory?.name
            : item?.name,
          value: item?.id,
          activeSeason: item?.activeSeason ? item?.activeSeason?.id : null,
        });
      });
      let filterData = _.unionBy(this.state?.TournamentData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label?.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      finalData = finalData?.filter((item) => item?.value !== 0);
      this.setState({
        TournamentData: finalData,
        TournamentCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomTournament = (e, type) => {
    let {
      TournamentCount,
      TournamentPage,
      isTournamentSearch,
      searchTournamentCount,
      searchTournamentPage,
    } = this.state;
    if (
      isTournamentSearch !== "" &&
      searchTournamentCount !== Math.ceil(searchTournamentPage / 20 + 1)
    ) {
      this.handleTournamentInputChange(
        searchTournamentPage + 20,
        isTournamentSearch,
        this.state.selectedModalSportObj
      );
      this.setState({
        searchTournamentPage: searchTournamentPage + 20,
      });
    } else {
      if (
        TournamentCount !==
          (TournamentCount == 1 ? 1 : Math.ceil(TournamentPage / 20)) &&
        isTournamentSearch == ""
      ) {
        this.fetchAllTournament(
          TournamentPage + 20,
          this.state.selectedModalSportObj
        );
        this.setState({
          TournamentPage: TournamentPage + 20,
        });
      }
    }
  };
  handleTournamentInputChange = (TournamentPage, value, obj) => {
    const passApi = !obj?.passApi?.includes("rls")
      ? `${obj?.passApi}/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : `rls/tournament?limit=20&offset=${TournamentPage}&SportId=${obj?.SportId}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: obj?.passApi?.includes("nba")
              ? item?.name + " " + item?.NBACategory?.name
              : item?.name,
            value: item?.id,
            activeSeason: item?.activeSeason ? item?.activeSeason?.id : null,
          });
        });
        let filterData = _.unionBy(this.state?.searchTournament, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label?.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTournament: finalData,
          searchTournamentCount: Math.ceil(count),
          isTournamentSearch: value,
          searchTournamentPage: TournamentPage,
        });
      }
    });
  };

  handleCheckboxClick = (event, item, type) => {
    let { allEventData } = this.state;
    const eventList = allEventData?.map((obj) => {
      if (obj?.id == item?.id) {
        if (type === "homeTeam") {
          return {
            ...obj,
            awayTeam: {
              ...obj?.awayTeam,
              isTip: event.target.checked ? 0 : obj?.awayTeam?.isTip,
            },
            homeTeam: {
              ...obj?.homeTeam,
              isTip: event.target.checked ? 1 : 0,
            },
          };
        } else {
          return {
            ...obj,
            awayTeam: {
              ...obj?.awayTeam,
              isTip: event.target.checked ? 1 : 0,
            },
            homeTeam: {
              ...obj?.homeTeam,
              isTip: event.target.checked ? 0 : obj?.homeTeam?.isTip,
            },
          };
        }
      } else {
        return obj;
      }
    });
    this.setState({ allEventData: eventList });
  };

  tipsDetailsModalOpen = (item) => {
    this.setState({ tipsModalDetailsOpen: true, itemToSelected: item?.id });
    this.fetchSingleEvent(item?.id, null, this.state.allEventData);
  };

  fetchSingleEvent = async (id, userId, allEventData) => {
    this.setState({ tipsModalDetailsIsLoading: true, isEventLoading: true });
    try {
      const passApi = `expertTips/get/${id}?UserId=${userId ? userId : ""}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let newEventData = allEventData?.map((item) => {
          const homeTeamData = data?.events?.[0]?.selectedTeam?.find(
            (obj) => obj?.id === item?.id
          )?.homeTeam;
          const awayTeamData = data?.events?.[0]?.selectedTeam?.find(
            (obj) => obj?.id === item?.id
          )?.awayTeam;
          return {
            ...item,
            homeTeam: {
              ...item?.homeTeam,
              isTip: data?.events?.length > 0 ? homeTeamData?.isTip : 0,
            },
            awayTeam: {
              ...item?.awayTeam,
              isTip: data?.events?.length > 0 ? awayTeamData?.isTip : 0,
            },
          };
        });
        this.setState({
          isEventLoading: false,
          allEventData: newEventData,
          tipsModalDetailsIsLoading: false,
          tipsModalDetails: data?.result,
          tipsModalEventDetails: data?.events,
        });
      } else {
        this.setState({
          tipsModalDetailsIsLoading: false,
          isEventLoading: false,
        });
      }
    } catch {
      this.setState({
        tipsModalDetailsIsLoading: false,
        isEventLoading: false,
      });
    }
  };

  fetchRoundData = async (sID, tID, tName) => {
    this.setState({ isModalRoundLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/tipping/rounds?timezone=${timezone}&tournamentId=${tID}&SportId=${sID}`
      );
      if (status === 200) {
        let newdata = [];

        const filteredRound = data?.roundList?.filter(
          (round) => !data?.completedRound.includes(round?.round)
        );
        let track = filteredRound?.map((item) => {
          newdata.push({
            label: `${
              item?.displayName
                ? item?.displayName
                : (this.state?.selectedModalSport == 4 ? "Day" : "Round") +
                  " " +
                  item?.round
            } ${
              item?.startTime
                ? "  (" +
                  moment(item?.startTime)
                    .tz(timezone)
                    .format("DD-MM-YYYY hh:mm A") +
                  ")"
                : ""
            }`,
            value: item?.round,
            roundStartDate: item?.startTime,
          });
        });
        if (newdata && newdata?.length > 0) {
          this.setState({ modalRounds: newdata, noRoundError: "" });
        } else {
          this.setState({
            modalRounds: newdata,
            noRoundError:
              "There are no rounds available for this tournament!, Try selecting a different League.",
          });
        }
        this.setState({ isModalRoundLoading: false });
      }
    } catch (err) {
      console.log(err);
      this.setState({ isModalRoundLoading: false });
    }
  };

  getAllEvents = async (sport, tournament, round, id, userId, seasonId) => {
    this.setState({ isEventLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `tipping/events?timezone=${timezone}&SportId=${
          sport ? sport : ""
        }&tournamentId=${tournament ? tournament : ""}&startDate=${moment()
          .tz(timezone)
          .format("YYYY-MM-DD")}&round=${round}&seasonId=${
          seasonId ? seasonId : ""
        }`
      );
      if (status === 200) {
        this.setState({
          // isEventLoading: false,
          allEventData: data?.result,
        });
        this.fetchSingleEvent(id, userId, data?.result);
      } else {
      }
    } catch (err) {
      this.setState({ isEventLoading: false });
      console.log(err);
    }
  };

  handleSave = async () => {
    const {
      currentPage,
      SelectedSport,
      selectTournament,
      selectedModalSport,
      sportExpertTipsValues,
      selectedModalRound,
      selectedActiveSeason,
    } = this.state;
    const user = fetchFromStorage(identifiers.user);
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        expert: {
          SportId: selectedModalSport,
          tournamentId: sportExpertTipsValues?.modalTournamentId,
          round: Number(selectedModalRound),
          seasonId: selectedActiveSeason,
        },
      };
      try {
        const { status, data } = await axiosInstance.post(
          `/expertTips/sport/create`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEvent(currentPage, SelectedSport, selectTournament);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (error) {
        this.setActionMessage(true, "Error", error?.response?.data?.message);
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  handleUpdate = async () => {
    const {
      currentPage,
      SelectedSport,
      selectTournament,
      sportExpertTipsValues,
      selectedAuthor,
      allEventData,
    } = this.state;
    const user = fetchFromStorage(identifiers.user);
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const eventData = allEventData?.map((item) => {
        return {
          teamId:
            item?.homeTeam?.isTip == 1
              ? item?.homeTeamId
              : item?.awayTeam?.isTip == 1
              ? item?.awayTeamId
              : null,
          eventId: item?.id,
        };
      });
      const payload = {
        ExpertSportTipId: sportExpertTipsValues?.id,
        UserId: selectedAuthor ? selectedAuthor : user?.id,
        events: eventData,
      };
      try {
        const { status, data } = await axiosInstance.put(
          `/expertTips/addTip`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEvent(currentPage, SelectedSport, selectTournament);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { currentPage, SelectedSport, selectTournament } = this.state;
    try {
      const passApi = `/expertTips/sport/delete/${this.state.itemToDelete}`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false });
        this.fetchAllEvent(currentPage, SelectedSport, selectTournament);
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  toggleTipsDetailsModalOpen = () => {
    this.setState({
      tipsModalDetailsOpen: false,
      tipsModalDetails: {},
      tipsModalEventDetails: [],
    });
  };

  tipperToDelete = async (id) => {
    try {
      const passApi = `/expertTips/admin/delete/${this.state.itemToSelected}?UserId=${id}`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.fetchSingleEvent(
          this.state.itemToSelected,
          null,
          this.state.allEventData
        );
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  render() {
    var {
      isModalOpen,
      isLoading,
      messageBox,
      rowPerPage,
      currentPage,
      isInputModalOpen,
      isEditMode,
      sportExpertTipsCount,
      SelectedSport,
      AllSportsOption,
      modalSportsOption,
      isExternalTournamentSearch,
      searchExternalTournament,
      externalTournamentData,
      selectTournament,
      selectedModalSport,
      searchTournament,
      isTournamentSearch,
      TournamentData,
      isTournamentLoading,
      sportExpertTipsValues,
      sportExpertTipsList,
      tipsModalDetailsOpen,
      tipsModalDetails,
      tipsModalEventDetails,
      tipsModalDetailsIsLoading,
      modalRounds,
      selectedModalRound,
      isModalRoundLoading,
      noRoundError,
      roundExpireError,
      authorList,
      selectedAuthor,
      allEventData,
      isEventLoading,
      errorSport,
      errorTournament,
      errorRound,
      itemToSelected,
    } = this.state;

    const pageNumbers = [];
    if (sportExpertTipsCount > 0) {
      for (let i = 1; i <= Math.ceil(sportExpertTipsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    const user = fetchFromStorage(identifiers.user);
    return (
      <>
        <Grid container className="page-content adminLogin sport-expert-tips">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  Sports Expert Tips{" "}
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Sports Expert Tips
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Sport"
                  value={AllSportsOption?.find((item) => {
                    return item?.value == SelectedSport;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => this.handlesportchange(e)}
                  options={AllSportsOption}
                />
                {/* <Box
                  className={`React teamsport-select external-select ${
                    !SelectedSport ? "c-not-allowed" : ""
                  }`}
                > */}
                <Select
                  className="React teamsport-select external-select"
                  // className={`React teamsport-select external-select ${
                  //   !SelectedSport ? "c-not-allowed" : ""
                  // }`}
                  classNamePrefix="select"
                  placeholder="All Tournaments"
                  isDisabled={!SelectedSport}
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomExternalTournament(e)
                  }
                  onInputChange={(e) =>
                    this.handleExternalTournamentInputChange(
                      0,
                      e,
                      this.state.selectedSportObj
                    )
                  }
                  value={
                    selectTournament
                      ? isExternalTournamentSearch
                        ? searchExternalTournament?.find((item) => {
                            return item?.value == selectTournament;
                          })
                        : externalTournamentData?.find((item) => {
                            return item?.value == selectTournament;
                          })
                      : null
                  }
                  onChange={(e) => this.handleExternalTournamentChange(e)}
                  menuPosition="absolute"
                  options={
                    isExternalTournamentSearch
                      ? searchExternalTournament
                      : externalTournamentData
                  }
                />
                {/* </Box> */}

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && !sportExpertTipsList?.length > 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && sportExpertTipsList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell> Sport </TableCell>
                        <TableCell> Tournament </TableCell>
                        <TableCell>Round</TableCell>
                        <TableCell> Owner </TableCell>
                        <TableCell style={{ width: "15%" }}>
                          Updated At
                        </TableCell>
                        <TableCell>Tips Details</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {sportExpertTipsList?.map((item, index) => {
                        return (
                          <TableRow>
                            <TableCell> {item?.id} </TableCell>
                            <TableCell>{item?.Sport?.sportName}</TableCell>
                            <TableCell>{item?.tournamentName}</TableCell>
                            <TableCell>
                              {item?.displayName
                                ? item?.displayName + `( Round ${item?.round})`
                                : item?.round}
                            </TableCell>
                            <TableCell>
                              {" "}
                              {item?.User?.firstName +
                                " " +
                                item?.User?.lastName}
                            </TableCell>
                            <TableCell>
                              {item?.updatedAt
                                ? moment(item?.updatedAt).format(
                                    "DD/MM/YYYY hh:mm:ss a"
                                  )
                                : "-"}
                            </TableCell>
                            <TableCell>
                              <Button
                                className="table-btn"
                                variant="contained"
                                style={{
                                  fontSize: "14px",
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  fontWeight: "400",
                                  textTransform: "none",
                                  width: "max-content",
                                }}
                                onClick={() => {
                                  this.tipsDetailsModalOpen(item);
                                }}
                              >
                                Tips Details
                              </Button>
                            </TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className={`table-btn edit-btn `}
                              >
                                Add Tips
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className={`table-btn delete-btn ${
                                  user?.role !== "admin" &&
                                  user?.id !== item?.createById
                                    ? "disabled-btn"
                                    : ""
                                }`}
                                disabled={
                                  user?.role !== "admin" &&
                                  user?.id !== item?.createById
                                }
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                sportExpertTipsCount / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />
            <Modal
              className="modal modal-input sport-expert-tips-modal"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create Sports Expert Tips"
                    : "Add Sports Expert Tips"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        className="teamsport-text"
                        style={{ marginBottom: "15px" }}
                      >
                        <label className="modal-label">
                          {" "}
                          Sport <span className="color-red">*</span>
                        </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          placeholder="Select Sport"
                          value={modalSportsOption?.find((item) => {
                            return item?.value == selectedModalSport;
                          })}
                          isDisabled={isEditMode}
                          isLoading={isLoading}
                          onChange={(e) => this.handleModalsportchange(e)}
                          options={modalSportsOption}
                        />
                        {errorSport ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorSport}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid item xs={12} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">
                          {" "}
                          Tournament <span className="color-red">*</span>
                        </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Tournament"
                          isDisabled={!selectedModalSport || isEditMode}
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomTournament(e)
                          }
                          onInputChange={(e) =>
                            this.handleTournamentInputChange(
                              0,
                              e,
                              this.state.selectedModalSportObj
                            )
                          }
                          isLoading={isTournamentLoading}
                          value={
                            sportExpertTipsValues?.modalTournamentId
                              ? isTournamentSearch
                                ? searchTournament?.find((item) => {
                                    return (
                                      item?.value ==
                                      sportExpertTipsValues?.modalTournamentId
                                    );
                                  })
                                : TournamentData?.find((item) => {
                                    return (
                                      item?.value ==
                                      sportExpertTipsValues?.modalTournamentId
                                    );
                                  })
                              : null
                          }
                          options={
                            isTournamentSearch
                              ? searchTournament
                              : TournamentData
                          }
                          onChange={(e) => {
                            this.setState({
                              sportExpertTipsValues: {
                                ...sportExpertTipsValues,
                                modalTournamentId: e?.value,
                              },
                              selectedModalRound: null,
                              errorTournament: e?.value ? "" : errorTournament,
                              errorRound: "",
                              roundExpireError: "",
                              selectedActiveSeason: e?.activeSeason,
                            });
                            this.fetchRoundData(
                              selectedModalSport,
                              e?.value,
                              e?.label
                            );
                          }}
                        />
                        {errorTournament ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorTournament}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid
                        item
                        xs={12}
                        className="teamsport-text"
                        style={{ marginBottom: "15px" }}
                      >
                        <label className="modal-label">
                          {" "}
                          Round <span className="color-red">*</span>{" "}
                        </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Round"
                          isDisabled={
                            !(
                              selectedModalSport &&
                              sportExpertTipsValues?.modalTournamentId
                            ) ||
                            noRoundError ||
                            isEditMode
                          }
                          isLoading={isModalRoundLoading}
                          value={
                            selectedModalRound &&
                            modalRounds?.find((item) => {
                              return item?.value == selectedModalRound;
                            })
                          }
                          options={modalRounds}
                          onChange={(e) => {
                            this.setState({
                              selectedModalRound: e?.value?.toString(),
                              errorRound: e?.value ? "" : errorRound,
                            });
                            if (moment(e?.roundStartDate) < moment()) {
                              this.setState({
                                roundExpireError:
                                  "You can not select this Round!, Please Try another round",
                              });
                            } else {
                              this.setState({
                                roundExpireError: "",
                              });
                            }
                          }}
                        />

                        <span className="errorText">
                          {roundExpireError && roundExpireError !== ""
                            ? roundExpireError
                            : ""}
                          {noRoundError && noRoundError !== ""
                            ? noRoundError
                            : ""}
                        </span>
                        {errorRound ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorRound}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {user?.role === "admin" && isEditMode ? (
                        <Grid
                          item
                          xs={12}
                          className="teamsport-text"
                          style={{ marginBottom: "15px" }}
                        >
                          <label className="modal-label"> Tipper Name </label>
                          <Select
                            className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                            classNamePrefix="select"
                            menuPosition="fixed"
                            placeholder="Tipper Name"
                            value={
                              selectedAuthor &&
                              authorList?.find((item) => {
                                return item?.value === selectedAuthor;
                              })
                            }
                            options={authorList}
                            onChange={(e) => {
                              this.setState({
                                selectedAuthor: e?.value,
                                allEventData: [],
                              });
                              this.fetchSingleEvent(
                                itemToSelected,
                                e?.value,
                                allEventData
                              );
                            }}
                          />
                        </Grid>
                      ) : (
                        <></>
                      )}
                      {isEditMode && (
                        <>
                          {isEventLoading && <Loader />}
                          {!isEventLoading && allEventData?.length == 0 && (
                            <p>No Data Available</p>
                          )}
                          <Box className="w-100 event-radio">
                            {!isEventLoading &&
                              allEventData?.length > 0 &&
                              allEventData?.map((item, index) => {
                                return (
                                  <>
                                    <label
                                      className="modal-label d-flex"
                                      style={{
                                        display: "flex",
                                        alignItems: "baseline",
                                      }}
                                    >
                                      {" "}
                                      {item?.eventName}{" "}
                                    </label>
                                    <p className="date-time-txt">
                                      {item?.startTime
                                        ? moment(item?.startTime).format(
                                            "DD/MM/YYYY hh:mm A"
                                          )
                                        : "-"}
                                    </p>
                                    <Grid
                                      item
                                      xs={12}
                                      className="d-flex mb-12"
                                      style={{
                                        display: "flex",
                                        alignItems: "baseline",
                                      }}
                                    >
                                      <Grid item xs={6}>
                                        <FormControlLabel
                                          value={item?.homeTeamId}
                                          control={
                                            <Radio
                                              color="primary"
                                              checked={
                                                item?.homeTeam?.isTip == 1
                                              }
                                            />
                                          }
                                          label={item?.homeTeam?.name}
                                          onChange={(e) => {
                                            this.handleCheckboxClick(
                                              e,
                                              item,
                                              "homeTeam"
                                            );
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={6}>
                                        <FormControlLabel
                                          value={item?.awayTeamId}
                                          control={
                                            <Radio
                                              color="primary"
                                              checked={
                                                item?.awayTeam?.isTip == 1
                                              }
                                            />
                                          }
                                          label={item?.awayTeam?.name}
                                          onChange={(e) => {
                                            this.handleCheckboxClick(
                                              e,
                                              item,
                                              "awayTeam"
                                            );
                                          }}
                                        />
                                      </Grid>
                                    </Grid>
                                  </>
                                );
                              })}
                          </Box>
                        </>
                      )}
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
            <Modal
              className="modal modal-input tips-modal-details sport-tips-modal-details"
              open={tipsModalDetailsOpen}
              onClose={this.toggleTipsDetailsModalOpen}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">Tips Details</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleTipsDetailsModalOpen}
                />
                {tipsModalDetailsIsLoading ? (
                  <Box className="modal-loader">
                    <Loader />
                  </Box>
                ) : (
                  <Box className="tips-details">
                    <Box className="d-flex align-item-baseline col-35 mb-18 details">
                      <Typography className="detsils-header">
                        Sport :
                      </Typography>
                      <Box className="w-60">
                        <Typography className="details-para">
                          {tipsModalDetails?.Sport?.sportName}
                        </Typography>
                      </Box>
                    </Box>
                    <Box className="d-flex align-item-baseline col-35 mb-18 details">
                      <Typography className="detsils-header">
                        Tournament :
                      </Typography>
                      <Box className="w-60">
                        <Typography className="details-para">
                          {tipsModalDetails?.tournamentName
                            ? tipsModalDetails?.tournamentName
                            : "-"}
                        </Typography>
                      </Box>
                    </Box>
                    {tipsModalDetails?.User && (
                      <Box className="d-flex align-item-baseline col-35 mb-18 details">
                        <Typography className="detsils-header">
                          User :
                        </Typography>
                        <Box className="w-60">
                          <Typography className="details-para">
                            {tipsModalDetails?.User?.firstName +
                              " " +
                              tipsModalDetails?.User?.lastName}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    <Box className="d-flex align-item-baseline col-35 mb-18 details">
                      <Typography className="detsils-header">
                        {" "}
                        Round :
                      </Typography>
                      <Box className="w-60">
                        <Typography className="details-para">
                          {tipsModalDetails?.round !== null
                            ? tipsModalDetails?.round
                            : "-"}
                        </Typography>
                      </Box>
                    </Box>
                    {tipsModalEventDetails?.length > 0 && (
                      <h3 className="text-center modal-head">Event Details</h3>
                    )}

                    {tipsModalEventDetails?.map((item, index) => {
                      return (
                        <>
                          <Box className="user-name-wrap" key={index}>
                            <Typography className="user-name">
                              {item?.tipper?.firstName +
                                " " +
                                item?.tipper?.lastName}
                            </Typography>
                            <Button
                              onClick={() =>
                                this.tipperToDelete(item?.tipper?.id)
                              }
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className={`table-btn delete-btn ${
                                user?.role !== "admin" &&
                                user?.id !== item?.tipper?.id
                                  ? "disabled-btn"
                                  : ""
                              }`}
                              disabled={
                                user?.role !== "admin" &&
                                user?.id !== item?.tipper?.id
                              }
                            >
                              Delete
                            </Button>
                          </Box>
                          {item?.selectedTeam?.map((obj) => {
                            return (
                              <>
                                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                                  <Typography className="detsils-header">
                                    {obj?.eventName}:
                                  </Typography>
                                  <Box className="w-60">
                                    <Typography className="details-para">
                                      {obj?.homeTeam?.isTip == 1
                                        ? obj?.homeTeam?.name
                                        : obj?.awayTeam?.isTip == 1
                                        ? obj?.awayTeam?.name
                                        : "-"}
                                    </Typography>
                                  </Box>
                                </Box>
                              </>
                            );
                          })}
                        </>
                      );
                    })}
                  </Box>
                )}
                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        onClick={this.toggleTipsDetailsModalOpen}
                        // className="mr-lr-30"
                        value="Back"
                        style={{ minWidth: "auto" }}
                      />
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default SportExpertTips;
