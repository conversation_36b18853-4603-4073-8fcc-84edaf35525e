.sportdetailsteam-picker {
  .MuiIconButton-root:hover {
    background-color: transparent;
  }
  .MuiTouchRipple-root {
    display: none;
  }
}
.filter-wrap {
  .MuiGrid-container {
    justify-content: space-around;
    margin-bottom: 10px;
  }
  .date-wrap {
    .MuiGrid-root {
      margin-top: 10px;
      border: 1px solid rgb(221, 221, 221);
      border-radius: 3px;
      height: 38px;
    }
    .MuiInputBase-root {
      margin-top: 10px;
    }
    .MuiIconButton-root {
      justify-content: end;
    }
    .MuiFormLabel-root,
    .MuiFormControl-root {
      padding: 0px 5px;
    }
  }
  .year-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    #all_year_rl {
      font-weight: 600;
      color: #000;
    }
  }
  .submit-wrap {
    .MuiBox-root {
      margin-top: 10px;
      justify-content: end;
    }
  }
}
