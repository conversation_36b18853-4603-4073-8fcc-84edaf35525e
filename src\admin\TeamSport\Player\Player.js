import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  InputAdornment,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  IconButton,
} from "@mui/material";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import moment from "moment";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../teamsport.scss";
import CreatePlayerVariation from "./createPlayerVariation/CreatePlayerVariation";
class Player extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      playerValues: {
        playerName: "",
        dob: "",
        team: "",
        rapidPlayerId: "",
        CountryId: "",
        id: "",
        CountryName: "",
      },
      countryAll: [],
      countryCount: 0,
      pageCountry: 0,
      searchCountry: [],
      searchCountryCount: 0,
      SearchCountrypage: 0,
      isCountrySearch: "",
      PlayerList: [],
      PlayerCount: 0,
      errorName: "",
      errorDob: "",
      errorTeam: "",
      errorCountry: "",
      externalTeamData: [],
      externalTeamCount: 0,
      selectTeam: "",
      SelectedExternalTeamList: [],
      ExternalTeamPage: 0,
      ModalTeamData: [],
      ModalTeamCount: 0,
      ModalTeamPage: 0,
      image: [],
      uploadImage: "",
      searchTeam: [],
      searchTeamCount: 0,
      searchTeamPage: 0,
      isTeamSearch: "",
      searchModalTeam: [],
      searchModalTeamCount: 0,
      searchModalTeamPage: 0,
      isModalTeamSearch: "",
      createError: "",
      isVariationModalOpen: false,
      search: "",
    };
  }

  componentDidMount() {
    this.fetchAllPlayer(0, "");
    this.fetchAllTeam(this.state.ExternalTeamPage);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllPlayer(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllPlayer(0, "");
      this.fetchAllTeam(0);
      this.setState({
        offset: 0,
        selectTeam: "",
        currentPage: 1,
        externalTeamData: [],
        ExternalTeamPage: 0,
        searchTeam: [],
        searchTeamPage: 0,
        ModalTeamData: [],
        ModalTeamPage: 0,
        SelectedExternalTeamList: [],
        searchModalTeam: [],
        searchModalTeamPage: 0,
        search: "",
      });
    }
  }

  fetchAllCountry = async (page) => {
    const { status, data } = await axiosInstance.get(
      // `${URLS.distance}?size=20&page=${page}`
      `${URLS.country}?limit=20&offset=${page}`
    );

    if (status === 200) {
      let Parray = [...this.state.countryAll];

      let count = data?.result?.count / 20;
      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.country,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state.countryAll, newdata);

      this.setState({
        countryAll: _.uniqBy(filterData, function (e) {
          return e.value;
        }),
        countryCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomCountry = () => {
    let {
      pageCountry,
      isCountrySearch,
      searchCountryCount,
      SearchCountrypage,
      countryCount,
    } = this.state;

    if (
      isCountrySearch !== "" &&
      searchCountryCount !== Math.ceil(SearchCountrypage / 20 + 1)
    ) {
      this.handleCountryInputChange(SearchCountrypage + 20, isCountrySearch);
      this.setState({
        SearchCountrypage: SearchCountrypage + 20,
      });
    } else {
      if (countryCount !== Math.ceil(pageCountry / 20)) {
        this.fetchAllCountry(pageCountry + 20);
        this.setState({
          pageCountry: pageCountry + 20,
        });
      }
    }
  };

  handleCountryInputChange = (page, value) => {
    // if (value.length > 2) {
    axiosInstance
      .get(`${URLS.country}?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          //     let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(this.state.searchCountry, newdata);

          this.setState({
            searchCountry: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchCountryCount: Math.ceil(count),
            isCountrySearch: value,
          });
        }
      });
  };
  fetchSelectedCountry = (CountryId, CountryName) => {
    let seletedCountry = [
      {
        label: CountryName,
        value: CountryId,
      },
    ];

    this.setState({
      countryAll: CountryId ? seletedCountry : this.state.countryAll,
    });
  };
  fetchAllPlayer = async (page, searchvalue) => {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/player?limit=${rowPerPage}&offset=${page}&SportId=12&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/player?limit=${rowPerPage}&offset=${page}&SportId=13&search=${searchvalue}`
        : this.props.match.path?.includes("basketball")
        ? `nba/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("afl")
        ? `afl/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("golf")
        ? `golf/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("mma")
        ? `mma/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/player?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : `rls/player?limit=${rowPerPage}&offset=${page}&SportId=14`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          PlayerList: data?.result?.rows,
          isLoading: false,
          PlayerCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchAllTeam = async (ExternalTeamPage) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ExternalTeamPage}`
      : `rls/team?limit=20&offset=${ExternalTeamPage}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterdata = newdata?.filter((item) => item?.value !== 0);
      let mergeData = _.unionBy(this.state?.externalTeamData, filterdata);
      const sortedData = mergeData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let alldatas = sortedData?.unshift({
        label: "All Teams",
        value: 0,
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        externalTeamData: finalData,
        externalTeamCount: Math.ceil(count),
      });
    }
  };
  handleOnScrollBottomExternalTeam = (e, type) => {
    let {
      externalTeamCount,
      ExternalTeamPage,
      isTeamSearch,
      searchTeamCount,
      searchTeamPage,
    } = this.state;

    if (
      isTeamSearch !== "" &&
      searchTeamCount !== Math.ceil(searchTeamPage / 20 + 1)
    ) {
      this.handleTeamInputChange(searchTeamPage + 20, isTeamSearch);
      this.setState({
        searchTeamPage: searchTeamPage + 20,
      });
    } else {
      if (
        externalTeamCount !== Math.ceil(ExternalTeamPage / 20) &&
        isTeamSearch == ""
      ) {
        this.fetchAllTeam(ExternalTeamPage + 20);
        this.setState({
          ExternalTeamPage: ExternalTeamPage + 20,
        });
      }
    }
  };
  handleTeamInputChange = (ExternalTeamPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchTeam, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Teams",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTeam: finalData,
          searchTeamCount: Math.ceil(count),
          isTeamSearch: value,
        });
      }
    });
  };
  handleExternalTeamChange = async (id, type) => {
    let selectTeamId = type ? id.value : id;
    this.setState({
      selectTeam: selectTeamId,
      isLoading: true,
    });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/player/team/${selectTeamId}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/player/team/${selectTeamId}?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/player/team/${selectTeamId}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/player/team/${selectTeamId}`
        : this.props.match.path?.includes("afl")
        ? `afl/player/team/${selectTeamId}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/player/team/${selectTeamId}`
        : this.props.match.path?.includes("golf")
        ? `golf/player/team/${selectTeamId}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/player/team/${selectTeamId}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/player/team/${selectTeamId}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/player/team/${selectTeamId}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/player/team/${selectTeamId}`
        : this.props.match.path?.includes("mma")
        ? `mma/player/team/${selectTeamId}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/player/team/${selectTeamId}`
        : `rls/player/team/${selectTeamId}?SportId=14`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let newdata = data?.result?.map((item) => {
          return this.props.match.path?.includes("cricket")
            ? {
                CricketPlayerId: item?.CricketPlayerId,
                CricketTeam: item?.CricketTeam,
                CricketTeamId: item?.CricketTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.CricketPlayer,
              }
            : this.props.match.path?.includes("basketball")
            ? {
                NBAPlayerId: item?.NBAPlayerId,
                NBATeam: item?.NBATeam,
                NBATeamId: item?.NBATeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.NBAPlayer,
              }
            : this.props.match.path?.includes("afl")
            ? {
                AFLPlayerId: item?.AFLPlayerId,
                AFLTeam: item?.AFLTeam,
                AFLTeamId: item?.AFLTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.AFLPlayer,
              }
            : this.props.match.path?.includes("australianrules")
            ? {
                ARPlayerId: item?.ARPlayerId,
                ARTeam: item?.ARTeam,
                ARTeamId: item?.ARTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.ARPlayer,
              }
            : this.props.match.path?.includes("golf")
            ? {
                GolfPlayerId: item?.GolfPlayerId,
                GolfTeam: item?.GolfTeam,
                GolfTeamId: item?.GolfTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.GolfPlayer,
              }
            : this.props.match.path?.includes("tennis")
            ? {
                TennisPlayerId: item?.TennisPlayerId,
                TennisTeam: item?.TennisTeam,
                TennisTeamId: item?.TennisTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.TennisPlayer,
              }
            : this.props.match.path?.includes("baseball")
            ? {
                BaseballPlayerId: item?.BaseballPlayerId,
                BaseballTeam: item?.BaseballTeam,
                BaseballTeamId: item?.BaseballTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.BaseballPlayer,
              }
            : this.props.match.path?.includes("icehockey")
            ? {
                IceHockeyPlayerId: item?.IceHockeyPlayerId,
                IceHockeyTeam: item?.IceHockeyTeam,
                IceHockeyTeamId: item?.IceHockeyTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.IceHockeyPlayer,
              }
            : this.props.match.path?.includes("boxing")
            ? {
                BoxingPlayerId: item?.BoxingPlayerId,
                BoxingTeam: item?.BoxingTeam,
                BoxingTeamId: item?.BoxingTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.BoxingPlayer,
              }
            : this.props.match.path?.includes("mma")
            ? {
                MMAPlayerId: item?.MMAPlayerId,
                MMATeam: item?.MMATeam,
                MMATeamId: item?.MMATeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.MMAPlayer,
              }
            : this.props.match.path?.includes("soccer")
            ? {
                SoccerPlayerId: item?.SoccerPlayerId,
                SoccerTeam: item?.SoccerTeam,
                SoccerTeamId: item?.SoccerTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.SoccerPlayer,
              }
            : {
                RLPlayerId: item?.RLPlayerId,
                RLTeam: item?.RLTeam,
                RLTeamId: item?.RLTeamId,
                createdAt: item?.createdAt,
                id: item?.id,
                updatedAt: item?.updatedAt,
                ...item.RLPlayer,
              };
        });
        this.setState({
          SelectedExternalTeamList: newdata,
          isLoading: false,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchModalAllTeam = async (ModalTeamPage) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        ModalTeamData: finalData,
        ModalTeamCount: Math.ceil(count),
      });
    }
  };
  handleOnScrollBottomModalTeam = (e, type) => {
    let {
      ModalTeamCount,
      ModalTeamPage,
      isModalTeamSearch,
      searchModalTeamCount,
      searchModalTeamPage,
    } = this.state;
    if (
      isModalTeamSearch !== "" &&
      searchModalTeamCount !== Math.ceil(searchModalTeamPage / 20 + 1)
    ) {
      this.handleModalTeamInputChange(
        searchModalTeamPage + 20,
        isModalTeamSearch
      );
      this.setState({
        searchModalTeamPage: searchModalTeamPage + 20,
      });
    } else {
      if (
        ModalTeamCount !==
          (ModalTeamCount == 1 ? 1 : Math.ceil(ModalTeamPage / 20)) &&
        isModalTeamSearch == ""
      ) {
        this.fetchModalAllTeam(ModalTeamPage + 20);
        this.setState({
          ModalTeamPage: ModalTeamPage + 20,
        });
      }
    }
  };
  handleModalTeamInputChange = (ModalTeamPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchModalTeam, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchModalTeam: finalData,
          searchModalTeamCount: Math.ceil(count),
          isModalTeamSearch: value,
        });
      }
    });
  };
  fetchSelectedModalTeam = (TeamId, TeamName) => {
    let seletedTeam = [
      {
        label: TeamId,
        value: TeamName,
      },
    ];

    this.setState({
      ModalTeamData: TeamId ? seletedTeam : this.state.ModalTeamData,
    });
  };
  handalValidate = () => {
    let { playerValues } = this.state;

    let flag = true;
    if (playerValues?.playerName?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (playerValues?.dob === "" || playerValues?.dob === null) {
      flag = false;
      this.setState({
        errorDob: "This field is mandatory",
      });
    } else {
      this.setState({
        errorDob: "",
      });
    }
    if (playerValues?.team === "") {
      flag = false;
      this.setState({
        errorTeam: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTeam: "",
      });
    }
    if (playerValues?.CountryId === "") {
      flag = false;
      this.setState({
        errorCountry: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCountry: "",
      });
    }

    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      const { playerValues, image, selectTeam } = this.state;
      let teamsId = playerValues?.team?.map((item) => {
        return item?.value;
      });
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        name: playerValues?.playerName.trim(),
        rapidPlayerId: playerValues?.rapidPlayerId,
        dob: playerValues?.dob,
        CountryId: playerValues?.CountryId,
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props.match.path?.includes("boxing")
          ? 6
          : this.props.match.path?.includes("mma")
          ? 5
          : this.props.match.path?.includes("soccer")
          ? 8
          : 14,
      };
      if (this.props.match.path?.includes("cricket")) {
        payload = {
          ...payload,
          CricketTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("basketball")) {
        payload = {
          ...payload,
          NBATeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("afl")) {
        payload = {
          ...payload,
          AFLTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("australianrules")) {
        payload = {
          ...payload,
          ARTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("golf")) {
        payload = {
          ...payload,
          GolfTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("tennis")) {
        payload = {
          ...payload,
          TennisTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("baseball")) {
        payload = {
          ...payload,
          BaseballTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("icehockey")) {
        payload = {
          ...payload,
          IceHockeyTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("boxing")) {
        payload = {
          ...payload,
          BoxingTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("mma")) {
        payload = {
          ...payload,
          MMATeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("soccer")) {
        payload = {
          ...payload,
          SoccerTeamIds: teamsId,
        };
      } else {
        payload = {
          ...payload,
          RLTeamIds: teamsId,
          SportId: this.props.match.path?.includes("rugbyleague")
            ? 12
            : this.props.match.path?.includes("rugbyunion")
            ? 13
            : 14,
        };
      }
      if (image?.length > 0) {
        let fileData = await this.setMedia(image[0]);
        if (fileData) {
          payload = {
            ...payload,
            image: fileData?.image?.filePath,
          };
          this.setState({
            uploadImage: fileData?.image?.filePath,
          });
        }
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";
      try {
        const { status, data } = await axiosInstance.post(
          `${passApi}/player`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            uploadImage: "",
          });
          let listdata = selectTeam
            ? (this.handleExternalTeamChange(selectTeam, false),
              this.fetchAllPlayer(this.state.offset, this.state?.search))
            : this.fetchAllPlayer(this.state.offset, this.state?.search);
          this.setActionMessage(
            true,
            "Success",
            data?.status ? "Player created Successfully..." : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      const { playerValues, image, selectTeam } = this.state;
      let teamsId = playerValues?.team?.map((item) => {
        return item?.value;
      });
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        name: playerValues?.playerName.trim(),
        rapidPlayerId: playerValues?.rapidPlayerId,
        dob: playerValues?.dob,
        CountryId: playerValues?.CountryId,
      };
      if (this.props.match.path?.includes("cricket")) {
        payload = {
          ...payload,
          CricketTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("basketball")) {
        payload = {
          ...payload,
          NBATeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("afl")) {
        payload = {
          ...payload,
          AFLTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("australianrules")) {
        payload = {
          ...payload,
          ARTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("golf")) {
        payload = {
          ...payload,
          GolfTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("tennis")) {
        payload = {
          ...payload,
          TennisTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("baseball")) {
        payload = {
          ...payload,
          BaseballTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("icehockey")) {
        payload = {
          ...payload,
          IceHockeyTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("boxing")) {
        payload = {
          ...payload,
          BoxingTeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("mma")) {
        payload = {
          ...payload,
          MMATeamIds: teamsId,
        };
      } else if (this.props.match.path?.includes("soccer")) {
        payload = {
          ...payload,
          SoccerTeamIds: teamsId,
        };
      } else {
        payload = {
          ...payload,
          RLTeamIds: teamsId,
          SportId: this.props.match.path?.includes("rugbyleague")
            ? 12
            : this.props.match.path?.includes("rugbyunion")
            ? 13
            : 14,
        };
      }
      if (image?.length > 0) {
        let fileData = await this.setMedia(image[0]);
        if (fileData) {
          payload = {
            ...payload,
            image: fileData?.image?.filePath,
          };
          this.setState({
            uploadImage: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          image: this.state.uploadImage,
        };
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";

      try {
        const { status, data } = await axiosInstance.put(
          `${passApi}/player/${this.state.playerValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            uploadImage: "",
          });
          let listdata = selectTeam
            ? (this.handleExternalTeamChange(selectTeam, false),
              this.fetchAllPlayer(this.state.offset, this.state?.search))
            : this.fetchAllPlayer(this.state.offset, this.state?.search);
          this.setActionMessage(
            true,
            "Success",
            data?.status ? "Player Updated Successfully..." : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorDob: "",
      errorTeam: "",
      errorCountry: "",
      image: [],
      uploadImage: "",
      createError: "",
    });
  };

  inputModal = (item, type) => () => {
    this.fetchAllCountry(0);
    this.fetchModalAllTeam(this.state.ModalTeamPage);
    this.setState({ isInputModalOpen: true });
    let teams = "";
    if (type === "edit") {
      this.fetchSelectedCountry(item?.CountryId, item?.Country?.country);
      this.props.match.path?.includes("cricket")
        ? !this.state.selectTeam
          ? (teams = item?.CricketTeamPlayers?.map((obj) => {
              return {
                value: obj?.CricketTeamId,
                label: obj?.CricketTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.CricketTeam?.id,
                label: item?.CricketTeam?.name,
              },
            ])
        : this.props.match.path?.includes("basketball")
        ? !this.state.selectTeam
          ? (teams = item?.NBATeamPlayers?.map((obj) => {
              return {
                value: obj?.NBATeamId,
                label: obj?.NBATeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.NBATeam?.id,
                label: item?.NBATeam?.name,
              },
            ])
        : this.props.match.path?.includes("afl")
        ? !this.state.selectTeam
          ? (teams = item?.AFLTeamPlayers?.map((obj) => {
              return {
                value: obj?.AFLTeamId,
                label: obj?.AFLTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.AFLTeam?.id,
                label: item?.AFLTeam?.name,
              },
            ])
        : this.props.match.path?.includes("australianrules")
        ? !this.state.selectTeam
          ? (teams = item?.ARTeamPlayers?.map((obj) => {
              return {
                value: obj?.ARTeamId,
                label: obj?.ARTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.ARTeam?.id,
                label: item?.ARTeam?.name,
              },
            ])
        : this.props.match.path?.includes("golf")
        ? !this.state.selectTeam
          ? (teams = item?.NBATeamPlayers?.map((obj) => {
              return {
                value: obj?.GolfTeamId,
                label: obj?.GolfTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.GolfTeam?.id,
                label: item?.GolfTeam?.name,
              },
            ])
        : this.props.match.path?.includes("tennis")
        ? !this.state.selectTeam
          ? (teams = item?.TennisTeamPlayers?.map((obj) => {
              return {
                value: obj?.TennisTeamId,
                label: obj?.TennisTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.TennisTeam?.id,
                label: item?.TennisTeam?.name,
              },
            ])
        : this.props.match.path?.includes("baseball")
        ? !this.state.selectTeam
          ? (teams = item?.BaseballTeamPlayers?.map((obj) => {
              return {
                value: obj?.BaseballTeamId,
                label: obj?.BaseballTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.BaseballTeam?.id,
                label: item?.BaseballTeam?.name,
              },
            ])
        : this.props.match.path?.includes("icehockey")
        ? !this.state.selectTeam
          ? (teams = item?.IceHockeyTeamPlayers?.map((obj) => {
              return {
                value: obj?.IceHockeyTeamId,
                label: obj?.IceHockeyTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.IceHockeyTeam?.id,
                label: item?.IceHockeyTeam?.name,
              },
            ])
        : this.props.match.path?.includes("boxing")
        ? !this.state.selectTeam
          ? (teams = item?.BoxingTeamPlayers?.map((obj) => {
              return {
                value: obj?.BoxingTeamId,
                label: obj?.BoxingTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.BoxingTeam?.id,
                label: item?.BoxingTeam?.name,
              },
            ])
        : this.props.match.path?.includes("mma")
        ? !this.state.selectTeam
          ? (teams = item?.MMATeamPlayers?.map((obj) => {
              return {
                value: obj?.MMATeamId,
                label: obj?.MMATeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.MMATeam?.id,
                label: item?.MMATeam?.name,
              },
            ])
        : this.props.match.path?.includes("soccer")
        ? !this.state.selectTeam
          ? (teams = item?.SoccerTeamPlayers?.map((obj) => {
              return {
                value: obj?.SoccerTeamId,
                label: obj?.SoccerTeam?.name,
              };
            }))
          : (teams = [
              {
                value: item?.SoccerTeam?.id,
                label: item?.SoccerTeam?.name,
              },
            ])
        : !this.state.selectTeam
        ? (teams = item?.RLTeamPlayers?.map((obj) => {
            return {
              value: obj?.RLTeamId,
              label: obj?.RLTeam?.name,
            };
          }))
        : (teams = [
            {
              value: item?.RLTeam?.id,
              label: item?.RLTeam?.name,
            },
          ]);

      this.setState({
        playerValues: {
          playerName: item?.name,
          dob: item?.dob,
          team: teams,
          rapidPlayerId: item?.rapidPlayerId,
          CountryId: item?.CountryId,
          id: item?.id,
          CountryName: item?.Country?.country,
        },
        uploadImage: item?.image,
        isEditMode: true,
      });
    } else {
      this.setState({
        playerValues: {
          playerName: "",
          dob: new Date(),
          team: "",
          rapidPlayerId: "",
          CountryId: "",
          id: "",
          CountryName: "",
        },
        image: [],
        uploadImage: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { selectTeam } = this.state;
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/player/${this.state.itemToDelete}?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/player/${this.state.itemToDelete}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("afl")
        ? `afl/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("golf")
        ? `golf/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("mma")
        ? `mma/player/${this.state.itemToDelete}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/player/${this.state.itemToDelete}`
        : `rls/player/${this.state.itemToDelete}?SportId=14`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false });
        let listdata = selectTeam
          ? (this.handleExternalTeamChange(selectTeam, false),
            this.fetchAllPlayer(this.state.offset, this.state?.search))
          : this.fetchAllPlayer(this.state.offset, this.state?.search);
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  fetchPlayersTeams = (item) => {
    return this.props.match.path?.includes("cricket")
      ? item?.CricketTeamPlayers?.map((obj) => {
          return obj?.CricketTeam?.name;
        }).toString()
      : this.props.match.path?.includes("basketball")
      ? item?.NBATeamPlayers?.map((obj) => {
          return obj?.NBATeam?.name;
        }).toString()
      : this.props.match.path?.includes("afl")
      ? item?.AFLTeamPlayers?.map((obj) => {
          return obj?.AFLTeam?.name;
        }).toString()
      : this.props.match.path?.includes("australianrules")
      ? item?.ARTeamPlayers?.map((obj) => {
          return obj?.ARTeam?.name;
        }).toString()
      : this.props.match.path?.includes("golf")
      ? item?.GolfTeamPlayers?.map((obj) => {
          return obj?.GolfTeam?.name;
        }).toString()
      : this.props.match.path?.includes("tennis")
      ? item?.TennisTeamPlayers?.map((obj) => {
          return obj?.TennisTeam?.name;
        }).toString()
      : this.props.match.path?.includes("baseball")
      ? item?.BaseballTeamPlayers?.map((obj) => {
          return obj?.BaseballTeam?.name;
        }).toString()
      : this.props.match.path?.includes("icehockey")
      ? item?.IceHockeyTeamPlayers?.map((obj) => {
          return obj?.IceHockeyTeam?.name;
        }).toString()
      : this.props.match.path?.includes("boxing")
      ? item?.BoxingTeamPlayers?.map((obj) => {
          return obj?.BoxingTeam?.name;
        }).toString()
      : this.props.match.path?.includes("mma")
      ? item?.MMATeamPlayers?.map((obj) => {
          return obj?.MMATeam?.name;
        }).toString()
      : this.props.match.path?.includes("soccer")
      ? item?.SoccerTeamPlayers?.map((obj) => {
          return obj?.SoccerTeam?.name;
        }).toString()
      : item?.RLTeamPlayers?.map((obj) => {
          return obj?.RLTeam?.name;
        }).toString();
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  inputVariationModal = (item) => {
    this.setState({
      isVariationModalOpen: true,
      playerValues: {
        playerName: item?.name,
        id: item?.id,
      },
    });
  };
  toggleVariationModal = () => {
    this.setState({
      isVariationModalOpen: false,
      playerValues: {
        playerName: "",
        id: "",
      },
    });
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllPlayer(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      playerValues,
      countryAll,
      searchCountry,
      isCountrySearch,
      PlayerList,
      PlayerCount,
      errorName,
      errorDob,
      errorTeam,
      errorCountry,
      externalTeamData,
      selectTeam,
      SelectedExternalTeamList,
      ModalTeamData,
      image,
      uploadImage,
      searchTeam,
      isTeamSearch,
      searchModalTeam,
      isModalTeamSearch,
      createError,
      isVariationModalOpen,
      search,
    } = this.state;
    const pageNumbers = [];
    if (PlayerCount > 0) {
      for (let i = 1; i <= Math.ceil(PlayerCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    let FinalPlayerList = selectTeam ? SelectedExternalTeamList : PlayerList;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Player</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Player
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Team"
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomExternalTeam(e)
                  }
                  onInputChange={(e) => this.handleTeamInputChange(0, e)}
                  value={
                    isTeamSearch
                      ? searchTeam?.find((item) => {
                          return item?.value == selectTeam;
                        })
                      : externalTeamData?.find((item) => {
                          return item?.value == selectTeam;
                        })
                  }
                  onChange={(e) => this.handleExternalTeamChange(e, true)}
                  options={isTeamSearch ? searchTeam : externalTeamData}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllPlayer(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && PlayerList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && PlayerList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>rapidPlayerId</TableCell>
                        <TableCell>Image</TableCell>
                        <TableCell>Player Name</TableCell>
                        <TableCell> Country </TableCell>
                        <TableCell width="175px">Teams</TableCell>
                        <TableCell>variation</TableCell>
                        <TableCell>DOB</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {FinalPlayerList?.length > 0 ? (
                        FinalPlayerList?.map((item) => {
                          return (
                            <TableRow className="table-rows listTable-Row">
                              <TableCell> {item?.id} </TableCell>
                              <TableCell>
                                {item?.rapidPlayerId
                                  ? item?.rapidPlayerId
                                  : "-"}
                              </TableCell>
                              <TableCell className="upload-img">
                                {item?.image?.includes("uploads") ? (
                                  <img
                                    src={config.mediaUrl + item?.image}
                                    alt="player"
                                  />
                                ) : item?.image ? (
                                  <img src={item?.image} alt="player" />
                                ) : (
                                  ""
                                )}
                              </TableCell>
                              <TableCell>{item?.name}</TableCell>
                              <TableCell
                                style={{
                                  textAlign: !item?.Country?.country
                                    ? "center"
                                    : "-",
                                }}
                              >
                                {item?.Country?.country
                                  ? item?.Country?.country
                                  : "-"}
                              </TableCell>
                              <TableCell>
                                {selectTeam
                                  ? this.props.match.path?.includes("cricket")
                                    ? item?.CricketTeam?.name
                                    : this.props.match.path?.includes(
                                        "basketball"
                                      )
                                    ? item?.NBATeam?.name
                                    : this.props.match.path?.includes("afl")
                                    ? item?.AFLTeam?.name
                                    : this.props.match.path?.includes(
                                        "australianrules"
                                      )
                                    ? item?.ARTeam?.name
                                    : this.props.match.path?.includes("golf")
                                    ? item?.GolfTeam?.name
                                    : this.props.match.path?.includes("tennis")
                                    ? item?.TennisTeam?.name
                                    : this.props.match.path?.includes(
                                        "baseball"
                                      )
                                    ? item?.BaseballTeam?.name
                                    : this.props.match.path?.includes(
                                        "icehockey"
                                      )
                                    ? item?.IceHockeyTeam?.name
                                    : this.props.match.path?.includes("boxing")
                                    ? item?.BoxingTeam?.name
                                    : this.props.match.path?.includes("mma")
                                    ? item?.MMATeam?.name
                                    : this.props.match.path?.includes("soccer")
                                    ? item?.SoccerTeam?.name
                                    : item?.RLTeam?.name
                                  : this.fetchPlayersTeams(item)}
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="contained"
                                  style={{
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    borderRadius: "8px",
                                    textTransform: "capitalize",
                                    // padding: "13px 24px 12px",
                                    marginLeft: "15px",
                                  }}
                                  onClick={() => {
                                    this.inputVariationModal(item);
                                  }}
                                >
                                  Add/Edit variation
                                </Button>
                              </TableCell>
                              <TableCell
                                style={{
                                  textAlign: !item?.dob ? "center" : "-",
                                }}
                              >
                                {item?.dob
                                  ? moment(item?.dob).format("DD-MM-YYYY")
                                  : "-"}
                              </TableCell>
                              <TableCell>
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  style={{ cursor: "pointer", minWidth: "0px" }}
                                  className="table-btn edit-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  style={{ cursor: "pointer", minWidth: "0px" }}
                                  className="table-btn delete-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      {!selectTeam ? (
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  PlayerCount / rowPerPage > 1 ? false : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />

                              {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        <></>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Player" : "Edit Player"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Player Name</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Player Name"
                          value={playerValues?.playerName}
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                playerName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid item xs={6} className="teamsport-dob-wrap">
                        <label className="modal-label"> DOB </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            // disableToolbar
                            variant="inline"
                            format="dd/MM/yyyy"
                            placeholder="DD/MM/YYYY"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              playerValues?.dob
                                ? typeof playerValues?.dob === "string"
                                  ? parseISO(playerValues?.dob)
                                  : playerValues?.dob
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                playerValues: {
                                  ...playerValues,
                                  dob: e,
                                },
                              })
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            autoOk
                            className="details-runner-picker dob-picker"
                          />
                          {errorDob ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0 0 0" }}
                            >
                              {errorDob}
                            </p>
                          ) : (
                            ""
                          )}
                        </LocalizationProvider>
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">rapid Player Id</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Player Id"
                          value={playerValues?.rapidPlayerId}
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                rapidPlayerId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Country </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Country"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomCountry(e)
                          }
                          // isSearchable={false}
                          onInputChange={(e) =>
                            this.handleCountryInputChange(0, e)
                          }
                          value={
                            isCountrySearch
                              ? searchCountry?.find((item) => {
                                  return item?.value == playerValues?.CountryId;
                                })
                              : countryAll?.find((item) => {
                                  return item?.value == playerValues?.CountryId;
                                })
                          }
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                CountryId: e.value,
                              },
                            })
                          }
                          options={isCountrySearch ? searchCountry : countryAll}
                        />
                        {errorCountry ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorCountry}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        <label className="modal-label"> Teams </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          isMulti
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomModalTeam(e)
                          }
                          onInputChange={(e) =>
                            this.handleModalTeamInputChange(0, e)
                          }
                          value={
                            isModalTeamSearch
                              ? searchModalTeam?.find((item) => {
                                  return item?.value == playerValues?.team;
                                })
                              : playerValues?.team
                          }
                          options={
                            isModalTeamSearch ? searchModalTeam : ModalTeamData
                          }
                          // value={playerValues?.team}
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                team: e,
                              },
                            })
                          }
                        />
                        {errorTeam ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorTeam}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <div className="blog-file-upload">
                      <h6>image</h6>
                      <FileUploader
                        onDrop={(image) =>
                          this.handleFileUpload("image", image)
                        }
                      />
                      <div className="logocontainer">
                        {image?.length > 0
                          ? image?.map((file, index) => (
                              <img
                                className="auto-width"
                                key={index}
                                src={file.preview}
                                alt="player"
                              />
                            ))
                          : uploadImage &&
                            uploadImage !== "" && (
                              <img
                                className="auto-width"
                                src={config.mediaUrl + uploadImage}
                                alt="player"
                              />
                            )}
                      </div>
                    </div>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                        {createError ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0", width: "300px" }}
                          >
                            {createError}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isVariationModalOpen}
              onClose={this.toggleVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Variation Details
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleVariationModal}
                />
                <CreatePlayerVariation
                  inputModal={this.toggleVariationModal}
                  playerValues={playerValues}
                  pathName={this.props?.match?.path}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Player;
