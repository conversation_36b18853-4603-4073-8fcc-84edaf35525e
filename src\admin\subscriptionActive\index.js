import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select from "react-select";
import moment from "moment-timezone";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import _ from "lodash";
import "./subscriptionActive.scss";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const statusFilterOption = [
  {
    label: "All",
    value: 0,
  },
  {
    label: "active",
    value: 1,
  },
  {
    label: "expired",
    value: 2,
  },
  {
    label: "deleted",
    value: 3,
  },
  {
    label: "cancelled",
    value: 4,
  },
  {
    label: "hold",
    value: 5,
  },
];

const subscriptionsStatusOption = [
  {
    label: "active",
    value: 1,
  },
  {
    label: "expired",
    value: 2,
  },
  {
    label: "deleted",
    value: 3,
  },
  {
    label: "cancelled",
    value: 4,
  },
];

class SubscriptionActive extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      subscriptionsValues: {
        selectedUserID: "",
        selectedStatus: "",
        expireAt: null,
      },
      subscriptionList: [],
      subscriptionCount: 0,
      errorSelectUserId: "",
      errorSelectStatus: "",
      subscriptionStatus: "",
      userAll: [],
      userCount: 0,
      pageUser: 0,
      searchUser: [],
      searchUserCount: 0,
      SearchUserpage: 0,
      isUserSearch: "",
      isSearch: "",
      subscriptionID: "",
    };
  }

  componentDidMount() {
    this.fetchSubscriptions(0, "", "");
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset, subscriptionStatus, isSearch } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchSubscriptions(offset, subscriptionStatus, isSearch);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchSubscriptions(0, "", "");
      this.setState({
        offset: 0,
        currentPage: 1,
        subscriptionStatus: "",
      });
    }
  }

  async fetchAllUsers(offset) {
    // this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.users + `?limit=20&offset=${offset}&allUser=&status=active`
    );
    if (status === 200) {
      const filterData = data?.result?.data;
      let count = data?.result?.count / 20;
      let newdata = [];
      let track = filterData?.map((item) => {
        newdata.push({
          label:
            item?.firstName +
            " " +
            item?.lastName +
            " " +
            "(" +
            item?.username +
            ")",
          value: item?.id,
        });
      });
      let filterFinalData = _.unionBy(this.state.userAll, newdata);
      this.setState({
        userAll: _.uniqBy(filterFinalData, function (e) {
          return e.value;
        }),
        userCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomUser = () => {
    let { pageUser, isUserSearch, searchUserCount, SearchUserpage, userCount } =
      this.state;

    if (
      isUserSearch !== "" &&
      searchUserCount !== Math.ceil(SearchUserpage / 20 + 1)
    ) {
      this.handleUserInputChange(SearchUserpage + 20, isUserSearch);
      this.setState({
        SearchUserpage: SearchUserpage + 20,
      });
    } else {
      if (userCount !== Math.ceil(pageUser / 20)) {
        this.fetchAllUsers(pageUser + 20);
        this.setState({
          pageUser: pageUser + 20,
        });
      }
    }
  };

  handleUserInputChange = (page, value) => {
    axiosInstance
      .get(
        `${URLS.users}?search=${value}&limit=20&offset=${page}&allUser=&status=active`
      )
      .then((res) => {
        if (res.status === 200) {
          const filterData = res?.data?.result?.data;
          let count = res?.data?.result?.count / 20;
          let newdata = [];
          let track = filterData?.map((item) => {
            newdata.push({
              label:
                item?.firstName +
                " " +
                item?.lastName +
                " " +
                "(" +
                item?.username +
                ")",
              value: item?.id,
            });
          });
          let filterFinalData = _.unionBy(this.state.searchUser, newdata);

          this.setState({
            searchUser: _.uniqBy(filterFinalData, function (e) {
              return e.value;
            }),
            searchUserCount: Math.ceil(count),
            isUserSearch: value,
          });
        }
      });
  };

  async fetchSubscriptions(page, subscriptionStatus, search) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/subscription/admin/get-purchase-plan?limit=${rowPerPage}&offset=${page}&status=${
          subscriptionStatus === "All" ? "" : subscriptionStatus
        }&search=${search}`
      );
      if (status === 200) {
        this.setState({
          subscriptionList: data?.data,
          isLoading: false,
          subscriptionCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { subscriptionsValues, isEditMode } = this.state;

    let flag = true;

    if (!isEditMode) {
      if (subscriptionsValues?.selectedUserID === "") {
        flag = false;
        this.setState({
          errorSelectUserId: "This field is mandatory",
        });
      } else {
        this.setState({
          errorSelectUserId: "",
        });
      }
    }

    if (subscriptionsValues?.selectedStatus === "") {
      flag = false;
      this.setState({
        errorSelectStatus: "This field is mandatory",
      });
    } else {
      this.setState({
        errorSelectStatus: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      const { subscriptionsValues } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        userId: subscriptionsValues?.selectedUserID,
        status: subscriptionsValues?.selectedStatus,
        expireAt: subscriptionsValues?.expireAt
          ? moment(subscriptionsValues?.expireAt)
              .tz(timezone)
              .format("YYYY-MM-DD")
          : null,
      };

      try {
        const { status, data } = await axiosInstance.post(
          `/subscription/admin/purchase-plan`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllUsers(
            this.state.offset,
            this.state.subscriptionStatus,
            this.state?.isSearch
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const {
      subscriptionsValues,
      subscriptionID,
      offset,
      subscriptionStatus,
      isSearch,
    } = this.state;

    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });

      const payload = {
        status: subscriptionsValues?.selectedStatus,
        expireAt: subscriptionsValues?.expireAt
          ? moment(subscriptionsValues?.expireAt)
              .tz(timezone)
              .format("YYYY-MM-DD")
          : null,
      };

      try {
        const { status, data } = await axiosInstance.put(
          `/subscription/admin/update-purchase-plan/${subscriptionID}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchSubscriptions(offset, subscriptionStatus, isSearch);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorSelectUserId: "",
      errorSelectStatus: "",
      subscriptionsValues: {
        selectedUserID: "",
        selectedStatus: "",
        expireAt: null,
      },
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    this.fetchAllUsers(0);
    if (type === "edit") {
      this.setState({
        subscriptionsValues: {
          selectedStatus: item?.status,
          expireAt: item?.expireAt ? item?.expireAt : null,
        },
        subscriptionID: item?.id,
        isEditMode: true,
      });
    } else {
      this.setState({
        subscriptionsValues: {
          selectedUserID: "",
          selectedStatus: "",
          expireAt: null,
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { itemToDelete, offset, subscriptionStatus, isSearch } = this.state;

    try {
      const { status, data } = await axiosInstance.delete(
        `/subscription/admin/delete-purchase-plan/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchSubscriptions(offset, subscriptionStatus, isSearch);
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, subscriptionList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handleStatuschange = async (e) => {
    this.setState({
      subscriptionStatus: e.label,
      isLoading: true,
      offset: 0,
      currentPage: 1,
    });
    this.fetchSubscriptions(0, e.label, this.state?.isSearch);
  };

  handleClearClick = () => {
    const { offset, subscriptionStatus } = this.state;
    this.setState({ isSearch: "", offset: 0, currentPage: 1 });
    this.fetchSubscriptions(0, subscriptionStatus, "");
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      subscriptionsValues,
      subscriptionList,
      subscriptionCount,
      errorSelectUserId,
      errorSelectStatus,
      subscriptionStatus,
      userAll,
      searchUser,
      isUserSearch,
      isSearch,
    } = this.state;
    const pageNumbers = [];

    if (subscriptionCount > 0) {
      for (let i = 1; i <= Math.ceil(subscriptionCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  Subscriptions Active
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Typography variant="h1" align="left">
                  Subscriptions Active
                </Typography>
              </Grid>

              <Grid
                item
                xs={7}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <Select
                  className="React cricket-select sponsored-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Status"
                  value={statusFilterOption?.find((item) => {
                    return item?.label == subscriptionStatus;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => this.handleStatuschange(e)}
                  options={statusFilterOption}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchSubscriptions(0, subscriptionStatus, isSearch);
                    this.setState({
                      offset: 0,
                      currentPage: 1,
                    });
                  }}
                >
                  Search
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && subscriptionList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && subscriptionList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>User ID</TableCell>
                      <TableCell>User</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Plan Name</TableCell>
                      <TableCell>Plat Form</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Admin Given Plan</TableCell>
                      <TableCell
                        align="center"
                        style={{ cursor: "pointer", width: "10%" }}
                      >
                        Start Date{" "}
                      </TableCell>
                      <TableCell align="center" style={{ cursor: "pointer" }}>
                        End Date{" "}
                      </TableCell>

                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {subscriptionList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell>{item?.id}</TableCell>
                          <TableCell> {item?.UserId} </TableCell>
                          <TableCell>
                            {item?.User?.firstName + " " + item?.User?.lastName}
                          </TableCell>
                          <TableCell> {item?.User?.username}</TableCell>
                          <TableCell>{item?.SubscriptionPlan?.name}</TableCell>
                          <TableCell>{item?.plateform}</TableCell>
                          <TableCell>{item?.status}</TableCell>
                          <TableCell>
                            {item?.isAdmin ? "True" : "False"}
                          </TableCell>
                          <TableCell align="center">
                            {item?.startAt
                              ? moment(item?.startAt)
                                  .tz(timezone)
                                  .format("YYYY/MM/DD")
                              : "-"}
                          </TableCell>
                          <TableCell align="center">
                            {item?.expireAt
                              ? moment(item?.expireAt)
                                  .tz(timezone)
                                  .format("YYYY/MM/DD")
                              : "-"}
                          </TableCell>
                          <TableCell>
                            <Button
                              // onClick={
                              //   item?.isAdmin && this.inputModal(item, "edit")
                              // }
                              onClick={this.inputModal(item, "edit")}
                              // className={
                              //   !item?.isAdmin
                              //     ? "disabled-btn table-btn edit-btn"
                              //     : "table-btn edit-btn"
                              // }
                              className="table-btn edit-btn"
                              // disabled={!item?.isAdmin}
                            >
                              Edit
                            </Button>
                            <Button
                              // onClick={
                              //   item?.isAdmin && this.setItemToDelete(item?.id)
                              // }
                              onClick={this.setItemToDelete(item?.id)}
                              // className={
                              //   !item?.isAdmin
                              //     ? "disabled-btn table-btn delete-btn"
                              //     : "table-btn delete-btn"
                              // }
                              className="table-btn delete-btn"
                              // disabled={!item?.isAdmin}
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                            className={
                              subscriptionList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              subscriptionList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              subscriptionCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                            className={
                              subscriptionList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              subscriptionList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New subscriptions"
                    : "Edit subscriptions"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      {!isEditMode && (
                        <Grid
                          item
                          xs={12}
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            marginBottom: "8px",
                          }}
                        >
                          <label className="modal-label">User Name</label>
                          <Select
                            className="React cricket-select sponsored-select-modal"
                            classNamePrefix="select"
                            placeholder="User Name"
                            menuPosition="fixed"
                            onMenuScrollToBottom={(e) =>
                              this.handleOnScrollBottomUser(e)
                            }
                            // isSearchable={false}
                            onInputChange={(e) =>
                              this.handleUserInputChange(0, e)
                            }
                            value={
                              isUserSearch
                                ? searchUser?.find((item) => {
                                    return (
                                      item?.value ==
                                      subscriptionsValues?.selectedUserID
                                    );
                                  })
                                : userAll?.find((item) => {
                                    return (
                                      item?.value ==
                                      subscriptionsValues?.selectedUserID
                                    );
                                  })
                            }
                            onChange={(e) =>
                              this.setState({
                                subscriptionsValues: {
                                  ...subscriptionsValues,
                                  selectedUserID: e.value,
                                },
                                errorSelectUserId: e?.value
                                  ? ""
                                  : errorSelectUserId,
                              })
                            }
                            options={isUserSearch ? searchUser : userAll}
                            // isLoading={isLoading}
                          />
                          {errorSelectUserId ? (
                            <p
                              className="errorText"
                              style={{ margin: "0 0 0 0" }}
                            >
                              {errorSelectUserId}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                      )}
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">
                          Subscriptions Status
                        </label>
                        <Select
                          className="React cricket-select sponsored-select-modal"
                          classNamePrefix="select"
                          placeholder="Subscriptions Status"
                          menuPosition="fixed"
                          value={subscriptionsStatusOption?.find((item) => {
                            return (
                              item?.label == subscriptionsValues?.selectedStatus
                            );
                          })}
                          // isLoading={isLoading}
                          onChange={(e) =>
                            this.setState({
                              subscriptionsValues: {
                                ...subscriptionsValues,
                                selectedStatus: e?.label,
                              },
                              errorSelectStatus: e?.label
                                ? ""
                                : errorSelectStatus,
                            })
                          }
                          options={subscriptionsStatusOption}
                        />
                        {errorSelectStatus ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorSelectStatus}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Expiry Date</label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            // disableToolbar
                            variant="inline"
                            format="yyyy/MM/dd"
                            placeholder="Expiry Date"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              subscriptionsValues?.expireAt
                                ? typeof subscriptionsValues?.expireAt ===
                                  "string"
                                  ? parseISO(
                                      moment(subscriptionsValues?.expireAt)
                                        .tz(timezone)
                                        .format("YYYY-MM-DD")
                                    )
                                  : subscriptionsValues?.expireAt
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                subscriptionsValues: {
                                  ...subscriptionsValues,
                                  expireAt: e,
                                },
                              })
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal"
                            disablePast={!isEditMode}
                          />
                        </LocalizationProvider>
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default SubscriptionActive;
