/*
xs, extra-small: 0px
sm, small: 600px
md, medium: 960px
lg, large: 1280px
xl, extra-large: 1920px
*/

@media (min-width: 1366px) {
  .MuiContainer-maxWidthLg {
    max-width: 1366px !important;
  }
}
@media (min-width: 1440px) {
  .MuiContainer-maxWidthLg {
    max-width: 1440px !important;
    &.MuiContainer-root {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
    & > .MuiGrid-container {
      & > .MuiGrid-item {
        padding-left: 15px;
        padding-right: 15px;
      }
      & > .MuiGrid-grid-lg-9 {
        max-width: 73%;
        flex-basis: 73%;
      }
      & > .MuiGrid-grid-lg-3 {
        max-width: 26%;
        flex-basis: 26%;
      }
    }
  }
}
/* xs */
@media (max-width: 599px) {
  .hide-xs {
    display: none !important;
  }
  .show-xs {
    display: block !important;
  }
}
/* sm */
@media (min-width: 600px) and (max-width: 959px) {
  .hide-sm {
    display: none !important;
  }
  .show-sm {
    display: block !important;
  }
}
/* md */
@media (min-width: 960px) and (max-width: 1279px) {
  .hide-md {
    display: none !important;
  }
  .show-md {
    display: block !important;
  }
}
/* lg */
@media (min-width: 1280px) and (max-width: 1919px) {
  .hide-lg {
    display: none !important;
  }
  .show-lg {
    display: block !important;
  }
}
/* xl */
@media (min-width: 1920px) {
  .hide-xl {
    display: none !important;
  }
  .show-xl {
    display: block !important;
  }
}

/*sm-tablet*/
@media (min-width: 600px) and (max-width: 799px) {
  .hide-sm-tab {
    display: none !important;
  }
}
/*lg-tablet*/
@media (min-width: 800px) and (max-width: 1100px) {
  .hide-lg-tab {
    display: none !important;
  }
}
@media (max-width: 599px) {
  body .title {
    font-size: 20px;
    line-height: 31px;
  }
  body .big-title {
    font-size: 20px;
    line-height: 41px;
  }
  .footer-logo-wrap {
    text-align: center !important;
  }
}

@media (max-width: 768px) {
  .login-container {
    align-items: flex-start !important;
  }
}

.membershipMain {
  .image-wrap {
    background: #1d9256;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 3px 3px 6px #00000029;
    text-align: center;
  }
  .title {
    margin-top: 0;
  }
}
@media (max-width: 1279px) {
  .membershipMain {
    .image-wrap {
      padding: 30px 10px;
    }
  }
}

@media (max-width: 1024px) {
  .membershipMain {
    padding: 0 20px;
  }
}

@media (max-width: 1023px) {
  .membershipMain {
    .image-wrap {
      width: 70%;
    }
  }
}
@media (max-width: 767px) {
  body .MuiTypography-subtitle1,
  .regular-contents .MuiTypography-subtitle2,
  .regular-contents p,
  .regular-contents li,
  .regular-contents strong {
    font-size: 14px;
    line-height: 20px;
    text-align: justify;
  }
  body .cap-title {
    font-size: 21px;
    line-height: 23px;
    margin-bottom: 20px;
  }
  .membershipMain {
    .image-wrap {
      width: 85%;
    }
  }
}
@media (max-width: 599px) {
  .membershipMain {
    .image-wrap {
      width: 55px;
      padding: 20px;
      margin: 27px auto 0;
    }
    h5,
    h6 {
      text-align: center;
    }
  }
  .terms-main {
    .cap-title {
      font-size: 30px;
      line-height: 41px;
      color: #1d9256;
    }
    ol {
      text-align: left;
      padding-left: 15px;
    }
  }
}
@media (max-width: 479px) {
  // .membershipMain {
  //   .image-wrap {
  //     width: 30%;
  //   }
  // }
}
@media (max-width: 359px) {
  // .membershipMain {
  //   .image-wrap {
  //     width: 35%;
  //   }
  // }
  .terms-main {
    text-align: center;
  }
}
