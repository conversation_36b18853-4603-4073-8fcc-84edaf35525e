import React from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";

const withRouter = (Component) => {
  function ComponentWithRouterProp(props) {
    let location = useLocation();
    let navigate = useNavigate();
    let params = useParams();
    const history = {
      navigate,
      location,
      length: window.history.length,
      action: "POP", // Mock action for example
    };

    const match = {
      path: location.pathname,
      url: location.pathname,
      isExact: true,
      params,
    };

    return (
      <Component
        {...props}
        history={history}
        location={location}
        navigate={navigate}
        match={match}
      />
    );
  }

  return ComponentWithRouterProp;
};

export default withRouter;
