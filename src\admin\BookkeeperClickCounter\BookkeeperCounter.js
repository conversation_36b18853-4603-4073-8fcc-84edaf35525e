import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";

import axiosInstance from "../../helpers/Axios";
import moment from "moment-timezone";
import { Link } from "react-router-dom";
// import { advData } from "./adslist-constant";
import CancelIcon from "@mui/icons-material/Cancel";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import { config } from "../../helpers/config";
import FileUploader from "../../library/common/components/FileUploader";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Select from "react-select";
import _, { values } from "lodash";
import Pagination from "@mui/material/Pagination";
import TableSortLabel from "@mui/material/TableSortLabel";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class BookkeeperCounter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      AdValues: {
        title: "",
        type: "",
        url: "",
        startDate: "",
        endDate: "",
        id: "",
      },
      script: "",
      dataCount: 0,
      counterListData: [],
      categoryData: [],
      categoryCount: 0,
      TournamentCount: 0,
      TournamentList: [],
      SelectedTournamentList: [],
      errorTitle: "",
      errorPage: "",
      errorPosition: "",
      errorType: "",
      errorUrl: "",
      errorScript: "",
      errorstartDate: "",
      errorendDate: "",
      searchCategory: [],
      searchCategoryCount: 0,
      searchCategoryPage: 0,
      isCategorySearch: "",
      selectedPage: "",
      advPageListOptions: [],
      ExternalAdvPageListOptions: [],
      selectedBanner: "",
      advBannerListOptions: [],
      selectedModalPage: "",
      selectedModalBanner: "",
      image: [],
      uploadImage: "",
      createError: "",
      paginationPage: [],
      HomeArticleData: [],
      sortType: "id",
      sortLabelid: true,
      sortName: true,
      sortStartDate: true,
      sortEndDate: true,
      BookkeeperDetails: [],
      SelectedBookkeeper: 0,
      SelectedSport: 0,
      AllSportsOption: [],
      SelectedType: 0,
      TypeOption: [
        {
          label: "All Type",
          value: 0,
        },
        {
          label: "Header",
          value: "header",
        },
        {
          label: "Sponsored",
          value: "sponsored",
        },
        {
          label: "Our Partner",
          value: "ourpartner",
        },
        {
          label: "Featured",
          value: "featured",
        },
      ],
    };
  }

  componentDidMount() {
    this.fetchBookkeeperCounter(0, true, 0, 0, 0);
    this.fetchProviders();
    this.fetchAllSports();
  }
  componentDidUpdate(prevState, prevProps) {
    const { selectedPage, selectedBanner } = this.state;
    // if (prevState.currentPage !== this.state.currentPage) {
    //     this.fetchBookkeeperCounter(selectedPage, selectedBanner)
    // }
  }
  fetchBookkeeperCounter = async (
    offsetsize,
    order,
    Bookkeeper,
    Sport,
    Type
  ) => {
    const { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `provider/providerClickget?BookKeeperId=${
          Bookkeeper === 0 ? "" : Bookkeeper
        }&type=${Type === 0 ? "" : Type}&SportId=${
          Sport === 0 ? "" : Sport
        }&key=id&order=${
          order ? "ASC" : "DESC"
        }&limit=${rowPerPage}&offset=${offsetsize}`
      );
      if (status === 200) {
        const pageNumbers = [];
        if (data?.count > 0) {
          for (let i = 1; i <= Math.ceil(data?.count / rowPerPage); i++) {
            pageNumbers.push(i);
          }
        }
        this.setState({
          isLoading: false,
          counterListData: data?.result,
          dataCount: data?.count,
          paginationPage: pageNumbers,
          offset: offsetsize,
        });
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({ isLoading: false });
    }
  };

  handlePaginationClick = (event, page) => {
    let {
      currentPage,
      rowPerPage,
      sortLabelid,
      SelectedBookkeeper,
      SelectedSport,
      SelectedType,
    } = this.state;
    let sortData = sortLabelid;
    //   sortType === "id"
    //     ? sortLabelid
    //     : sortType === "pageName"
    //     ? sortName
    //     : sortType === "startDate"
    //     ? sortStartDate
    //     : sortEndDate;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
    this.fetchBookkeeperCounter(
      (Number(page) - 1) * rowPerPage,
      sortData,
      SelectedBookkeeper,
      SelectedSport,
      SelectedType
    );
  };

  fetchProviders = async () => {
    this.setState({ isLoading: true });

    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeepers/`
      );
      if (status === 200) {
        let activeProvide = data?.result?.filter((item) => {
          return item?.status === "active";
        });
        let newdata = [];
        let providerData = activeProvide?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let alldatas = {
          label: "All Bookkeeper",
          value: 0,
        };

        let alldata = [alldatas, ...newdata];
        this.setState({
          isLoading: false,
          BookkeeperDetails: alldata?.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          }),
        });
      }
    } catch (err) {
      this.setState({ isLoading: false });
    }
  };

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Sport",
        value: 0,
      };

      let alldata = [alldatas, ...newdata];

      // let pushData = newdata.push(alldatas);
      // let sortData = newdata?.sort((a, b) => {
      //   return a.value > b.value ? 1 : -1;
      // });
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        isLoading: false,
      });
    }
  }
  handleBookkeeperchange = (e) => {
    const { sortLabelid, SelectedSport, SelectedType } = this.state;
    this.setState({
      SelectedBookkeeper: e.value,
      currentPage: 1,
    });
    this.fetchBookkeeperCounter(
      0,
      sortLabelid,
      e.value,
      SelectedSport,
      SelectedType
    );
  };
  handlesportchange = (e) => {
    const { sortLabelid, SelectedBookkeeper, SelectedType } = this.state;
    this.setState({
      SelectedSport: e.value,
      isLoading: true,
      currentPage: 1,
    });
    this.fetchBookkeeperCounter(
      0,
      sortLabelid,
      SelectedBookkeeper,
      e.value,
      SelectedType
    );
  };
  handletypechange = (e) => {
    const { sortLabelid, SelectedBookkeeper, SelectedSport } = this.state;
    this.setState({
      SelectedType: e.value,
      currentPage: 1,
    });
    this.fetchBookkeeperCounter(
      0,
      sortLabelid,
      SelectedBookkeeper,
      SelectedSport,
      e.value
    );
  };

  sortLabelHandler = (type) => {
    const {
      sortType,
      sortLabelid,
      offset,
      SelectedBookkeeper,
      SelectedSport,
      SelectedType,
    } = this.state;
    this.setState({ sortType: type });
    // if (type === "id") {
    this.fetchBookkeeperCounter(
      0,
      !sortLabelid,
      SelectedBookkeeper,
      SelectedSport,
      SelectedType
    );
    this.setState({
      sortLabelid: !sortLabelid,
      currentPage: 1,
    });
    // } else if (type === "pageName") {
    //   this.fetchBookkeeperCounter(selectedPage, selectedBanner, 0, type, !sortName);
    //   this.setState({
    //     sortLabelid: true,
    //     sortName: !sortName,
    //     sortStartDate: true,
    //     sortEndDate: true,
    //     currentPage: 1,
    //   });
    // } else if (type === "startDate") {
    //   this.fetchBookkeeperCounter(
    //     selectedPage,
    //     selectedBanner,
    //     0,
    //     type,
    //     !sortStartDate
    //   );
    //   this.setState({
    //     sortLabelid: true,
    //     sortName: true,
    //     sortStartDate: !sortStartDate,
    //     sortEndDate: true,
    //     currentPage: 1,
    //   });
    // } else {
    //   this.fetchBookkeeperCounter(
    //     selectedPage,
    //     selectedBanner,
    //     0,
    //     type,
    //     !sortEndDate
    //   );
    //   this.setState({
    //     sortLabelid: true,
    //     sortName: true,
    //     sortStartDate: true,
    //     sortEndDate: !sortEndDate,
    //     currentPage: 1,
    //   });
    // }
  };
  render() {
    var {
      isLoading,
      rowPerPage,
      currentPage,
      advPageListOptions,
      ExternalAdvPageListOptions,
      TournamentList,
      TournamentCount,
      SelectedTournamentList,
      errorTitle,
      errorPage,
      errorPosition,
      errorType,
      errorUrl,
      errorScript,
      errorstartDate,
      errorendDate,
      searchCategory,
      isCategorySearch,
      selectedPage,
      selectedBanner,
      selectedModalPage,
      selectedModalBanner,
      advBannerListOptions,
      image,
      uploadImage,
      counterListData,
      script,
      createError,
      dataCount,
      advPage,
      paginationPage,
      HomeArticleData,
      sortType,
      sortLabelid,
      sortName,
      sortStartDate,
      sortEndDate,
      BookkeeperDetails,
      SelectedBookkeeper,
      SelectedSport,
      AllSportsOption,
      SelectedType,
      TypeOption,
    } = this.state;

    let sortData = sortLabelid;
    // : sortType === "pageName"
    // ? sortName
    // : sortType === "startDate"
    // ? sortStartDate
    // : sortEndDate;

    let FinalTournamentList = selectedPage
      ? SelectedTournamentList
      : TournamentList;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Bookkeeper Counter</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Bookkeeper Counter
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Bookkeeper"
                  menuPosition="fixed"
                  value={BookkeeperDetails?.find((item) => {
                    return item?.value == SelectedBookkeeper;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => this.handleBookkeeperchange(e)}
                  options={BookkeeperDetails}
                />
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Sport"
                  value={AllSportsOption?.find((item) => {
                    return item?.value == SelectedSport;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => this.handlesportchange(e)}
                  options={AllSportsOption}
                />
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Type"
                  value={TypeOption?.find((item) => {
                    return item?.value == SelectedType;
                  })}
                  onChange={(e) => this.handletypechange(e)}
                  options={TypeOption}
                />
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && counterListData?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && counterListData?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer", width: "6%" }}
                        >
                          DID{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortLabelid
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        <TableCell>Bookkeeper</TableCell>
                        <TableCell>Clicks</TableCell>
                        <TableCell>Sports</TableCell>
                        <TableCell>Type</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {counterListData?.length > 0 ? (
                        counterListData?.map((item, index) => {
                          return (
                            <TableRow className="listTable-Row" key={index}>
                              <TableCell> {item?.id} </TableCell>
                              <TableCell> {item?.BookKeeper?.name} </TableCell>
                              <TableCell> {item?.click} </TableCell>
                              <TableCell>{item?.Sport?.sportName}</TableCell>
                              <TableCell>{item?.type}</TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      {/* {(!selectedPage && !selectedBanner) || (!selectedPage === 9 || !selectedPage === 10) ? ( */}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}

                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                dataCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={paginationPage[paginationPage?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />

                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                      {/* ) : (
                                                <></>
                                            )} */}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default BookkeeperCounter;
