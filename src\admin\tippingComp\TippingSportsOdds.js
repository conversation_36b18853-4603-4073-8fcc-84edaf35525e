import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  TableContainer,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  Checkbox,
  TextField,
  ClickAwayListener,
} from "@mui/material";

import { config } from "../../helpers/config";
import axiosInstance from "../../helpers/Axios";

import { useDispatch, useSelector } from "react-redux";

import { ReactComponent as OddsLock } from "../../images/tips/odds-lock.svg";
import DefaultImg from "../../images/tips/smartb_default.png";
import "./tippingSportsOdds.scss";
const TippingSportsOdds = ({
  data,
  type,
  team,
  teamId,
  isScore,
  eventLength,
  eventByIdData,
  BookkeeperData,
}) => {
  const oddsicon = (BookKeeperId, type, item, fullData, oddsType) => {
    const Identifiers =
      data?.SportId == 4
        ? fullData?.CricketIdentifiers
        : data?.SportId == 10
        ? fullData?.NBAIdentifiers
        : data?.SportId == 15
        ? fullData?.AFLIdentifiers
        : data?.SportId == 9
        ? fullData?.ARIdentifiers
        : data?.SportId == 16
        ? fullData?.GolfIdentifiers
        : data?.SportId == 7
        ? fullData?.TennisIdentifiers
        : data?.SportId == 11
        ? fullData?.BaseballIdentifiers
        : data?.SportId == 17
        ? fullData?.IceHockeyIdentifiers
        : data?.SportId == 6
        ? fullData?.BoxingIdentifiers
        : data?.SportId == 5
        ? fullData?.MMAIdentifiers
        : data?.SportId == 8
        ? fullData?.SoccerIdentifiers
        : fullData?.RLIdentifiers;
    const newData = Identifiers?.map((obj) => {
      const BookkeeperObj = obj?.ApiProvider?.BookKeeperProviders?.filter(
        (item) => {
          return item?.BookKeeperId === BookKeeperId;
        }
      );
      return {
        ...obj,
        BookKeeperId:
          BookkeeperObj?.length > 0 ? BookkeeperObj?.[0]?.BookKeeperId : null,
      };
    });
    const filteredData = newData
      ?.filter?.((obj) => obj?.BookKeeperId === BookKeeperId)
      ?.filter((obj) => obj?.ApiProviderId !== 17);

    let icon = BookkeeperData?.filter((obj) => obj?.id === BookKeeperId);
    let iconData = icon?.[0];
    let imageUrl =
      oddsType == "currentbest"
        ? iconData?.currentBest_logo
        : iconData?.long_logo;

    const urlLink =
      filteredData?.length > 0 && filteredData?.[0]?.url
        ? filteredData?.[0]?.url + `?Referrer=SmartB`
        : iconData?.affiliate_link;
    return (
      <Box className="current-best-odds-icon">
        {oddsType == "currentbest" ? (
          <img
            className="currentbest-bookmaker-thumb"
            src={imageUrl ? config.mediaUrl + imageUrl : DefaultImg}
            alt="Odds Icon"
          />
        ) : (
          <Box className="odd-img">
            <img
              src={imageUrl ? config.mediaUrl + imageUrl : DefaultImg}
              alt="Odds Icon"
            />
          </Box>
        )}
      </Box>
    );
    // }
  };

  const fetchCurrentBestOdds = (data, type, team, teamid, Isscore) => {
    let allTeamOdds =
      data?.SportId == 4
        ? data?.CricketBetOffers
        : data?.SportId == 10
        ? data?.NBABetOffers
        : data?.SportId == 15
        ? data?.AFLBetOffers
        : data?.SportId == 9
        ? data?.ARBetOffers
        : data?.SportId == 16
        ? data?.GolfBetOffers
        : data?.SportId == 7
        ? data?.TennisBetOffers
        : data?.SportId == 11
        ? data?.BaseballBetOffers
        : data?.SportId == 17
        ? data?.IceHockeyBetOffers
        : data?.SportId == 6
        ? data?.BoxingBetOffers
        : data?.SportId == 5
        ? data?.MMABetOffers
        : data?.SportId == 8
        ? data?.SoccerBetOffers
        : data?.RLBetOffers;
    let HomeTeamOdds = allTeamOdds?.homeOdds;
    let AwayTeamOdds = allTeamOdds?.awayOdds;
    let teamInfo = team === "hometeam" ? HomeTeamOdds : AwayTeamOdds;
    if (teamInfo?.odd) {
      return (
        <>
          <span className="odds-point">{teamInfo?.point}</span>
          {fetchClickableOdds(
            teamInfo?.odd,
            teamInfo?.BookKeeperId,
            "header",
            teamInfo,
            data,
            "betslip",
            Isscore,
            "currentbest",
            team
          )}
        </>
      );
    } else {
      return null;
    }
  };
  const fetchCurrentBestsOddsIcon = (data, type, team, teamid) => {
    let allTeamOdds =
      data?.SportId == 4
        ? data?.CricketBetOffers
        : data?.SportId == 10
        ? data?.NBABetOffers
        : data?.SportId == 15
        ? data?.AFLBetOffers
        : data?.SportId == 9
        ? data?.ARBetOffers
        : data?.SportId == 16
        ? data?.GolfBetOffers
        : data?.SportId == 7
        ? data?.TennisBetOffers
        : data?.SportId == 11
        ? data?.BaseballBetOffers
        : data?.SportId == 17
        ? data?.IceHockeyBetOffers
        : data?.SportId == 6
        ? data?.BoxingBetOffers
        : data?.SportId == 5
        ? data?.MMABetOffers
        : data?.SportId == 8
        ? data?.SoccerBetOffers
        : data?.RLBetOffers;
    let HomeTeamOdds = allTeamOdds?.homeOdds;
    let AwayTeamOdds = allTeamOdds?.awayOdds;
    let teamInfo = team === "hometeam" ? HomeTeamOdds : AwayTeamOdds;

    if (teamInfo?.odd && teamInfo?.odd !== 0) {
      return oddsicon(
        teamInfo?.BookKeeperId,
        "header",
        teamInfo,
        data,
        "currentbest"
      );
    } else {
      return "";
      // <span className="odds"> - </span>
    }
  };
  const fetchClickableOdds = (
    odds,
    BookKeeperId,
    type,
    item,
    fulldata,
    IsBetslip,
    Isscore,
    OddsType,
    team
  ) => {
    let icon = BookkeeperData?.filter((obj) => obj?.id === BookKeeperId);
    let iconData = icon?.[0];
    const oddsInfo = item;
    return (
      <>
        {eventLength !== 0 &&
        eventByIdData?.tippingType != "winning" &&
        (team == "hometeam"
          ? fulldata?.homeTeam?.isTip === 1
          : fulldata?.awayTeam?.isTip === 1) ? (
          <span className={`odds-lock-wrap`}>
            <OddsLock />
          </span>
        ) : null}

        {odds || odds == 0 ? (
          <span className={`cursor-pointer current-best-odds-value  `}>
            <span className="odds-link">
              {IsBetslip === "betslip" && !Isscore ? (
                <Tooltip title="Bet" className="bet-tooltip" placement="top">
                  <span className="bet-now">
                    {" "}
                    {odds == 0 ? "SP" : Number(odds).toFixed(2)}{" "}
                  </span>
                </Tooltip>
              ) : (
                <>{odds == 0 ? "SP" : Number(odds).toFixed(2)}</>
              )}
            </span>
          </span>
        ) : null}
      </>
    );
  };

  const handleAnchorTagClick = (e, url) => {
    window.open(url, "_blank");
    e.stopPropagation();
    // href={
    //   Odds?.[0]?.providerMarketId && Odds?.[0]?.providerParticipantId
    //     ? `https://www.bet365.com/dl/sportsbookredirect?affiliate=365_00967140&bs=${Odds?.[0]?.providerMarketId}-${Odds?.[0]?.providerParticipantId}~${Odds?.[0]?.odd}~1&bet=1`
    //     : `${iconData?.affiliate_link}`
    // }
  };

  return (
    <Box className="sports-odds-wrap  sports-current-best-odds-wrap">
      <span>
        <span className="odds-container">
          {fetchCurrentBestOdds(data, type, team, teamId, isScore)}
        </span>
      </span>
      {fetchCurrentBestsOddsIcon(data, type, team)}
    </Box>
  );
};

export default TippingSportsOdds;
