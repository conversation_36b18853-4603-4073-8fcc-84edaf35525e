import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";

import { IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import axiosInstance from "../../helpers/Axios";
import moment from "moment-timezone";
import { Link } from "react-router-dom";
// import { advData } from "./adslist-constant";
import CancelIcon from "@mui/icons-material/Cancel";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import { config } from "../../helpers/config";
import FileUploader from "../../library/common/components/FileUploader";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import DateFnsUtils from "@date-io/date-fns";
import TableSortLabel from "@mui/material/TableSortLabel";
import CSVExport from "../csvExport/CSVExport";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class AdvertisementSection extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      AdValues: {
        title: "",
        type: "",
        url: "",
        startDate: "",
        endDate: "",
        id: "",
      },
      script: "",
      advCount: 0,
      advTableListData: [],
      categoryData: [],
      categoryCount: 0,
      TournamentCount: 0,
      TournamentList: [],
      SelectedTournamentList: [],
      errorTitle: "",
      errorPage: "",
      errorPosition: "",
      errorType: "",
      errorUrl: "",
      errorScript: "",
      errorstartDate: "",
      errorendDate: "",
      searchCategory: [],
      searchCategoryCount: 0,
      searchCategoryPage: 0,
      isCategorySearch: "",
      selectedPage: "",
      advPageListOptions: [],
      ExternalAdvPageListOptions: [],
      selectedBanner: "",
      advBannerListOptions: [],
      selectedModalPage: "",
      selectedModalBanner: "",
      image: [],
      uploadImage: "",
      createError: "",
      paginationPage: [],
      HomeArticleData: [],
      sortType: "id",
      sortLabelid: false,
      sortName: true,
      sortStartDate: true,
      sortEndDate: true,
      sortDate: null,
      filterEndDate: null,
      startDateOpen: false,
      endDateOpen: false,
      csvListData: [],
    };
  }

  componentDidMount() {
    this.fetchTableAdlist(null, null, 0, "id", false, null, null);
    this.fetchNewsArticleData();
  }
  componentDidUpdate(prevState, prevProps) {
    const { selectedPage, selectedBanner } = this.state;
    // if (prevState.currentPage !== this.state.currentPage) {
    //     this.fetchTableAdlist(selectedPage, selectedBanner)
    // }
  }
  handleSortStartDate = (date) => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      filterEndDate,
    } = this.state;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        sortType,
        sortData,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        filterEndDate
      );
    }
  };
  handleFilterEndDate = (date) => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      sortDate,
    } = this.state;
    this.setState({
      filterEndDate: date
        ? moment(date).tz(timezone).format("YYYY-MM-DD")
        : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        sortType,
        sortData,
        sortDate,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null
      );
    }
  };
  clearStartDate = () => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      filterEndDate,
    } = this.state;
    this.setState({
      sortDate: null,
      startDateOpen: false,
    });
    this.fetchTableAdlist(
      selectedPage,
      selectedBanner,
      0,
      sortType,
      sortData,
      null,
      filterEndDate
    );
  };
  clearEndDate = () => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      sortDate,
    } = this.state;
    this.setState({
      filterEndDate: null,
      endDateOpen: false,
    });
    this.fetchTableAdlist(
      selectedPage,
      selectedBanner,
      0,
      sortType,
      sortData,
      sortDate,
      null
    );
  };
  fetchTableAdlist = async (
    page,
    position,
    offsetsize,
    type,
    order,
    sortDate,
    filterEndDate
  ) => {
    const { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `ads/getAllAdminAds?page_id=${
          page === null || page == 0 ? "" : page
        }&position_id=${
          position === null ? "" : position
        }&limit=${rowPerPage}&offset=${offsetsize}&key=${type}&order=${
          order ? "ASC" : "DESC"
        }&startDate=${sortDate === null ? "" : sortDate}&endDate=${
          filterEndDate === null ? "" : filterEndDate
        }&timeZone=${timezone}`
      );
      if (status === 200) {
        const pageNumbers = [];
        if (data?.count > 0) {
          for (let i = 1; i <= Math.ceil(data?.count / rowPerPage); i++) {
            pageNumbers.push(i);
          }
        }
        this.fetchAllCsvData(
          page,
          position,
          data?.count,
          type,
          order,
          sortDate,
          filterEndDate
        );
        this.setState({
          isLoading: false,
          advTableListData: data?.data,
          advCount: data?.count,
          selectedPage: page === null ? "" : page,
          selectedBanner: position === null ? "" : position,
          paginationPage: pageNumbers,
          offset: offsetsize,
        });
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({ isLoading: false });
    }
  };
  fetchAllCsvData = async (
    page,
    position,
    count,
    type,
    order,
    sortDate,
    filterEndDate
  ) => {
    try {
      const { status, data } = await axiosInstance.get(
        `ads/getAllAdminAds?page_id=${
          page === null || page == 0 ? "" : page
        }&position_id=${position === null ? "" : position}&limit=${
          count ? count : 20
        }&offset=&key=${type}&order=${order ? "ASC" : "DESC"}&startDate=${
          sortDate === null ? "" : sortDate
        }&endDate=${
          filterEndDate === null ? "" : filterEndDate
        }&timeZone=${timezone}`
      );
      if (status === 200) {
        const csvData = data?.data?.map((item) => {
          return {
            Id: item?.id,
            Title: item?.title,
            PageName: item?.pageName,
            Position: this.facthPostionName(item?.page_id, item?.position_id),
            StartDate: item?.startDate
              ? moment(item?.startDate).tz(timezone).format("DD-MM-YYYY")
              : "-",
            EndDate: item?.endDate
              ? moment(item?.endDate).tz(timezone).format("DD-MM-YYYY")
              : "-",
            Status: item?.status,
            Clicks: item?.AdsMappings?.click ? item?.AdsMappings?.click : 0,
            Impression: item?.AdsMappings?.impression
              ? item?.AdsMappings?.impression
              : 0,
          };
        });
        this.setState({
          csvListData: csvData,
        });
      } else {
      }
    } catch {}
  };

  fetchNewsArticleData = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `v2/news/category?All=0`
      );
      if (status === 200) {
        let Article = [];

        let ArticleObj = data?.result
          // ?.slice(0, data?.result?.length - 1)
          ?.map((item, index) =>
            Article.push({
              id: index + 101,
              adName: "Below" + " " + item?.title,
            })
          );
        const advData = [
          {
            id: 1,
            pageName: "Home Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              {
                id: 2,
                adName: "After Header",
              },
              // {
              //   id: 3,
              //   adName: "Before Sports",
              // },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
              {
                id: 6,
                adName: "News Side Bar",
              },
            ],
          },
          {
            id: 2,
            pageName: "Racing Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              {
                id: 2,
                adName: "Above Horses",
              },
              {
                id: 3,
                adName: "Above Greyhounds",
              },
              {
                id: 4,
                adName: "Above Harness",
              },
              {
                id: 5,
                adName: "Above Ourpartner",
              },
              {
                id: 6,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 3,
            pageName: "Horse Racing Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              {
                id: 2,
                adName: "Above Runner Table",
              },
              {
                id: 3,
                adName: "Above Ourpartner",
              },
              {
                id: 4,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 4,
            pageName: "Greyhound Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              {
                id: 2,
                adName: "Above Runner Table",
              },
              {
                id: 3,
                adName: "Above Ourpartner",
              },
              {
                id: 4,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 5,
            pageName: "Harness Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              {
                id: 2,
                adName: "Above Runner Table",
              },
              {
                id: 3,
                adName: "Above Ourpartner",
              },
              {
                id: 4,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 6,
            pageName: "Cricket Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 7,
            pageName: "Rugby League Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 8,
            pageName: "Rugby Union Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 9,
            pageName: "Site Background",
            pageAdList: [],
          },
          {
            id: 10,
            pageName: "Top header",
            pageAdList: [],
          },
          {
            id: 11,
            pageName: "Main Page header",
            pageAdList: [],
          },
          {
            id: 12,
            pageName: "BasketBall Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 13,
            pageName: "News Page All",
            pageAdList: [
              {
                id: 1,
                adName: "Below Slider",
              },
              {
                id: 2,
                adName: "Below Top Stories",
              },
              ...Article,
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 14,
            pageName: "News Page Category",
            pageAdList: [
              {
                id: 1,
                adName: "Below Slider",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 15,
            pageName: "Single News Page",
            pageAdList: [
              {
                id: 1,
                adName: "Between content",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 16,
            pageName: "American Football Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 17,
            pageName: "Australian Rules Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 18,
            pageName: "Baseball Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 19,
            pageName: "Boxing Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 20,
            pageName: "Ice Hockey Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 21,
            pageName: "MMA Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 22,
            pageName: "Soccer Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 23,
            pageName: "Tennis Page",
            pageAdList: [
              // {
              //   id: 1,
              //   adName: "Page Header",
              // },
              // {
              //   id: 2,
              //   adName: "Above Event list",
              // },
              {
                id: 3,
                adName: "After 1st Event",
              },
              {
                id: 4,
                adName: "Above Ourpartner",
              },
              {
                id: 5,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 24,
            pageName: "Track Profile",
            pageAdList: [
              {
                id: 1,
                adName: "Above list",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 25,
            pageName: "Track Profile - Result",
            pageAdList: [
              {
                id: 1,
                adName: "Above list",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 26,
            pageName: "Track Profile - Records",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 27,
            pageName: "Track Profile - Jockey Stats",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 28,
            pageName: "Track Profile - Trainer Stats",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 29,
            pageName: "Jockey Stats - Profile",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 30,
            pageName: "Jockey Stats - Form",
            pageAdList: [
              {
                id: 1,
                adName: "Above List",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 31,
            pageName: "Jockey Stats - Tracks",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 32,
            pageName: "Jockey Stats - Trainers",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 33,
            pageName: "Trainer Stats - Profile",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 34,
            pageName: "Trainer Stats - Form",
            pageAdList: [
              {
                id: 1,
                adName: "Above List",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 35,
            pageName: "Trainer Stats - Tracks",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 36,
            pageName: "Trainer Stats - Jockeys",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 37,
            pageName: "Trainer Stats - Horses",
            pageAdList: [
              {
                id: 1,
                adName: "Above List",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 38,
            pageName: "Horse Profile",
            pageAdList: [
              {
                id: 1,
                adName: "Above Career stats",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
          {
            id: 39,
            pageName: "Horse Stats",
            pageAdList: [
              {
                id: 1,
                adName: "Above Table",
              },
              {
                id: 2,
                adName: "Above Ourpartner",
              },
              {
                id: 3,
                adName: "Below Ourpartner",
              },
            ],
          },
        ];
        this.setState({ HomeArticleData: advData });
        this.fetchPageList();
      }
    } catch {}
  };

  fetchPageList = () => {
    const { HomeArticleData } = this.state;
    let newdata = [];
    let listdata = HomeArticleData?.map((item) => {
      newdata.push({
        label: item?.pageName,
        value: item?.id,
      });
    });
    const sortedData = newdata?.sort((a, b) => {
      return a?.label.localeCompare(b?.label);
    });
    this.setState({
      advPageListOptions: sortedData,
      ExternalAdvPageListOptions: [
        {
          label: "All Pages",
          value: 0,
        },
        ...sortedData,
      ],
    });
  };
  handleFetchAdBannerList = (value, type) => {
    const {
      sortEndDate,
      sortStartDate,
      sortName,
      sortType,
      sortLabelid,
      HomeArticleData,
      sortDate,
      filterEndDate,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "pageName"
        ? sortName
        : sortType === "startDate"
        ? sortStartDate
        : sortEndDate;
    let adsData = HomeArticleData?.filter((item) => {
      return item?.id == value;
    });
    let newdata = [];
    let listdata = adsData?.[0]?.pageAdList?.map((item) => {
      newdata.push({
        label: item?.adName,
        value: item?.id,
      });
    });
    if (type === "Externalpage") {
      if (!value) {
        this.fetchTableAdlist(
          null,
          null,
          this.state.offset,
          sortType,
          sortData,
          sortDate,
          filterEndDate
        );
      }
      this.setState({
        selectedPage: value,
        advBannerListOptions: newdata,
        selectedBanner: "",
      });
    } else {
      this.setState({
        selectedModalPage: value,
        advBannerListOptions: newdata,
        selectedModalBanner: "",
      });
    }
  };

  facthPostionName = (page_id, position_id) => {
    let { HomeArticleData } = this.state;
    let pageName = HomeArticleData?.filter((item) => {
      return item?.id == page_id;
    });

    let adpostionName = pageName?.[0]?.pageAdList?.filter(
      (item) => item?.id == position_id
    );
    return adpostionName?.[0]?.adName;
  };

  handalValidate = () => {
    let { AdValues, selectedModalBanner, selectedModalPage, script } =
      this.state;

    let flag = true;
    if (AdValues?.title?.trim() === "") {
      flag = false;
      this.setState({
        errorTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTitle: "",
      });
    }
    if (selectedModalPage === "") {
      flag = false;
      this.setState({
        errorPage: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPage: "",
      });
    }
    if (
      selectedModalBanner === "" &&
      !(
        selectedModalPage === 9 ||
        selectedModalPage === 10 ||
        selectedModalPage === 11
      )
    ) {
      flag = false;
      this.setState({
        errorPosition: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPosition: "",
      });
    }
    if (AdValues?.type === "") {
      flag = false;
      this.setState({
        errorType: "This field is mandatory",
      });
    } else {
      this.setState({
        errorType: "",
      });
    }
    // if (AdValues?.type === "script" && script === "") {
    //   flag = false;
    //   this.setState({
    //     errorScript: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorScript: "",
    //   });
    // }
    if (AdValues?.url?.trim() === "") {
      flag = false;
      this.setState({
        errorUrl: "This field is mandatory",
      });
    } else {
      this.setState({
        errorUrl: "",
      });
    }
    if (AdValues?.startDate === "") {
      flag = false;
      this.setState({
        errorstartDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorstartDate: "",
      });
    }
    if (AdValues?.endDate === "") {
      flag = false;
      this.setState({
        errorendDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorendDate: "",
      });
    }

    return flag;
  };
  handleSave = async () => {
    const { HomeArticleData, sortDate, filterEndDate } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });

      const {
        AdValues,
        image,
        script,
        selectedModalBanner,
        selectedModalPage,
        selectedPage,
        selectedBanner,
        offset,
        sortEndDate,
        sortStartDate,
        sortName,
        sortType,
        sortLabelid,
      } = this.state;
      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "pageName"
          ? sortName
          : sortType === "startDate"
          ? sortStartDate
          : sortEndDate;
      let page = HomeArticleData?.filter(
        (item) => item?.id === selectedModalPage
      );
      let payload = {
        title: AdValues?.title,
        type: AdValues?.type,
        url: AdValues?.url,
        startDate: AdValues?.startDate
          ? moment(AdValues?.startDate).tz(timezone).format("YYYY-MM-DD")
          : null,
        endDate: AdValues?.endDate
          ? moment(AdValues?.endDate).tz(timezone).format("YYYY-MM-DD")
          : null,
        pageName: page?.[0]?.pageName,
        position_id:
          selectedModalPage === 9 ||
          selectedModalPage === 10 ||
          selectedModalPage === 11
            ? 0
            : selectedModalBanner,
        page_id: selectedModalPage,
      };
      if (AdValues?.type === "image") {
        if (image?.length > 0) {
          let fileData = await this.setMedia(image[0]);
          if (fileData) {
            payload = {
              ...payload,
              filePath: fileData?.image?.filePath,
            };
            this.setState({
              uploadImage: fileData?.image?.filePath,
            });
          }
        }
      } else {
        payload = {
          ...payload,
          text: script,
        };
      }
      try {
        const { status, data } = await axiosInstance.post(
          `ads/createAds?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
            uploadImage: "",
            image: [],
          });
          this.fetchTableAdlist(
            selectedPage,
            selectedBanner,
            offset,
            sortType,
            sortData,
            sortDate,
            filterEndDate
          );
          this.setActionMessage(
            true,
            "Success",
            data?.success ? data?.message : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };
  handleUpdate = async () => {
    const { HomeArticleData } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });

      const {
        AdValues,
        image,
        script,
        selectedModalBanner,
        selectedModalPage,
        selectedPage,
        selectedBanner,
        uploadImage,
        offset,
        sortEndDate,
        sortStartDate,
        sortName,
        sortType,
        sortLabelid,
        sortDate,
        filterEndDate,
      } = this.state;
      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "pageName"
          ? sortName
          : sortType === "startDate"
          ? sortStartDate
          : sortEndDate;
      let page = HomeArticleData?.filter(
        (item) => item?.id === selectedModalPage
      );
      let payload = {
        title: AdValues?.title,
        type: AdValues?.type,
        url: AdValues?.url,
        startDate: AdValues?.startDate
          ? moment(AdValues?.startDate).tz(timezone).format("YYYY-MM-DD")
          : null,
        endDate: AdValues?.endDate
          ? moment(AdValues?.endDate).tz(timezone).format("YYYY-MM-DD")
          : null,
        pageName: page?.[0]?.pageName,
        position_id:
          selectedModalPage === 9 ||
          selectedModalPage === 10 ||
          selectedModalPage === 11
            ? 0
            : selectedModalBanner,
        page_id: selectedModalPage,
      };
      if (AdValues?.type === "image") {
        if (image?.length > 0) {
          let fileData = await this.setMedia(image[0]);
          if (fileData) {
            payload = {
              ...payload,
              filePath: fileData?.image?.filePath,
            };
            this.setState({
              uploadImage: fileData?.image?.filePath,
            });
          }
        } else {
          payload = {
            ...payload,
            filePath: uploadImage,
          };
        }
      } else {
        payload = {
          ...payload,
          text: script,
        };
      }
      try {
        const { status, data } = await axiosInstance.put(
          `ads/updateAds/${AdValues?.id}?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
            uploadImage: "",
            image: [],
          });
          this.fetchTableAdlist(
            selectedPage,
            selectedBanner,
            offset,
            sortType,
            sortData,
            sortDate,
            filterEndDate
          );
          this.setActionMessage(
            true,
            "Success",
            data?.success ? data?.message : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTitle: "",
      errorPage: "",
      errorPosition: "",
      errorType: "",
      errorUrl: "",
      errorstartDate: "",
      errorendDate: "",
      errorScript: "",
      image: [],
      uploadImage: "",
      createError: "",
      isLoading: false,
    });
  };

  inputModal = (item, type) => () => {
    const { AdValues, image } = this.state;
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.handleFetchAdBannerList(item?.page_id, "Modalpage");
      // this.fetchModalSelectedCategory(
      //     item?.RLCategoryId,
      //     item?.RLCategory?.name
      // );
      this.setState({
        AdValues: {
          ...AdValues,
          title: item?.title,
          type: item?.type,
          startDate: item?.startDate,
          endDate: moment(item?.endDate),
          url: item?.url,
          id: item?.id,
        },
        selectedModalBanner: item?.position_id,
        selectedModalPage: item?.page_id,
        isEditMode: true,
      });
      if (item?.type === "image") {
        this.setState({
          uploadImage: item?.filePath,
        });
      } else {
        this.setState({
          script: item?.text,
        });
      }
    } else {
      this.setState({
        AdValues: {
          title: "",
          type: "",
          id: "",
          url: "",
          startDate: new Date(),
          endDate: new Date(),
        },
        selectedModalPage: "",
        selectedModalBanner: "",
        script: "",
        image: [],
        uploadImage: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const {
        selectedPage,
        selectedBanner,
        currentPage,
        offset,
        sortEndDate,
        sortStartDate,
        sortName,
        sortType,
        sortLabelid,
        sortDate,
        filterEndDate,
      } = this.state;
      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "pageName"
          ? sortName
          : sortType === "startDate"
          ? sortStartDate
          : sortEndDate;
      this.setState({ isLoading: true });
      const { status } = await axiosInstance.delete(
        `ads/deleteAd/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchTableAdlist(
            selectedPage,
            selectedBanner,
            offset,
            sortType,
            sortData,
            sortDate,
            filterEndDate
          );
        });
        // this.setActionMessage(
        //     true,
        //     "Success",
        //     data?.success
        //         ? data?.message
        //         : ""
        // );
      } else {
        // this.setActionMessage(
        //     true,
        //     'Error',
        //     data?.message
        // );
        this.setState({ isLoading: false, isModalOpen: false });
      }
    } catch (err) {
      // this.setActionMessage(
      //     true,
      //     'Error',
      //     err?.response?.data?.message
      // );
      this.setState({ isLoading: false, isModalOpen: false });
    }
  };
  handlePaginationClick = (event, page) => {
    let {
      selectedPage,
      selectedBanner,
      currentPage,
      rowPerPage,
      sortEndDate,
      sortStartDate,
      sortName,
      sortType,
      sortLabelid,
      sortDate,
      filterEndDate,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "pageName"
        ? sortName
        : sortType === "startDate"
        ? sortStartDate
        : sortEndDate;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
    this.fetchTableAdlist(
      selectedPage,
      selectedBanner,
      (Number(page) - 1) * rowPerPage,
      sortType,
      sortData,
      sortDate,
      filterEndDate
    );
  };
  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  handleExternalPageChange = (value) => {
    let {
      sortEndDate,
      sortStartDate,
      sortName,
      sortType,
      sortLabelid,
      sortDate,
      filterEndDate,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "pageName"
        ? sortName
        : sortType === "startDate"
        ? sortStartDate
        : sortEndDate;
    this.handleFetchAdBannerList(value, "Externalpage");
    if (value === 9 || value == 10 || value == 11) {
      this.fetchTableAdlist(
        value,
        "",
        0,
        sortType,
        sortData,
        sortDate,
        filterEndDate
      );
    }
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  sortLabelHandler = (type) => {
    const {
      sortType,
      sortLabelid,
      sortName,
      sortStartDate,
      sortEndDate,
      selectedPage,
      selectedBanner,
      offset,
      sortDate,
      filterEndDate,
    } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        type,
        !sortLabelid,
        sortDate,
        filterEndDate
      );
      this.setState({
        sortLabelid: !sortLabelid,
        sortName: true,
        sortStartDate: true,
        sortEndDate: true,
        currentPage: 1,
      });
    } else if (type === "pageName") {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        type,
        !sortName,
        sortDate,
        filterEndDate
      );
      this.setState({
        sortLabelid: false,
        sortName: !sortName,
        sortStartDate: true,
        sortEndDate: true,
        currentPage: 1,
      });
    } else if (type === "startDate") {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        type,
        !sortStartDate,
        sortDate,
        filterEndDate
      );
      this.setState({
        sortLabelid: false,
        sortName: true,
        sortStartDate: !sortStartDate,
        sortEndDate: true,
        currentPage: 1,
      });
    } else {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        type,
        !sortEndDate,
        sortDate,
        filterEndDate
      );
      this.setState({
        sortLabelid: false,
        sortName: true,
        sortStartDate: true,
        sortEndDate: !sortEndDate,
        currentPage: 1,
      });
    }
  };
  render() {
    var {
      AdvertisingPageList,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      AdValues,
      CategoryPage,
      categoryData,
      advPageListOptions,
      ExternalAdvPageListOptions,
      TournamentList,
      TournamentCount,
      SelectedTournamentList,
      errorTitle,
      errorPage,
      errorPosition,
      errorType,
      errorUrl,
      errorScript,
      errorstartDate,
      errorendDate,
      searchCategory,
      isCategorySearch,
      selectedPage,
      selectedBanner,
      selectedModalPage,
      selectedModalBanner,
      advBannerListOptions,
      image,
      uploadImage,
      advTableListData,
      script,
      createError,
      advCount,
      advPage,
      paginationPage,
      HomeArticleData,
      sortType,
      sortLabelid,
      sortName,
      sortStartDate,
      sortEndDate,
      sortDate,
      filterEndDate,
      csvListData,
    } = this.state;

    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "pageName"
        ? sortName
        : sortType === "startDate"
        ? sortStartDate
        : sortEndDate;

    let FinalTournamentList = selectedPage
      ? SelectedTournamentList
      : TournamentList;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Web Advertisement</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Web Advertisement
                </Typography>
              </Grid>
              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Adv Page"
                  // onMenuScrollToBottom={(e) =>
                  //     this.handleOnScrollBottomCategory(e, "ExternalCategory")
                  // }
                  value={ExternalAdvPageListOptions?.find((item) => {
                    return item?.value == selectedPage;
                  })}
                  onChange={(e) => {
                    return (
                      this.handleExternalPageChange(e.value),
                      this.fetchTableAdlist(
                        e.value,
                        "",
                        0,
                        sortType,
                        sortData,
                        sortDate,
                        filterEndDate
                      )
                    );
                  }}
                  options={ExternalAdvPageListOptions}
                />
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Ad position"
                  isDisabled={
                    !selectedPage ||
                    selectedPage === 9 ||
                    selectedPage === 10 ||
                    selectedPage === 11
                  }
                  // onInputChange={(e) => this.handleCategoryInputChange(0, e)}
                  value={
                    selectedBanner &&
                    advBannerListOptions?.find((item) => {
                      return item?.value == selectedBanner;
                    })
                  }
                  onChange={(e) => {
                    return (
                      this.setState({ selectedBanner: e.value }),
                      this.fetchTableAdlist(
                        selectedPage,
                        e.value,
                        0,
                        sortType,
                        sortData,
                        sortDate,
                        filterEndDate
                      )
                    );
                  }}
                  options={advBannerListOptions}
                />
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    clearable
                    onClear={() => this.clearStartDate()}
                    open={this.state.startDateOpen}
                    onOpen={() => this.setState({ startDateOpen: true })}
                    onClose={() => this.setState({ startDateOpen: false })}
                    autoOk
                    // disableToolbar
                    // variant="inline"
                    format="dd/MM/yyyy"
                    placeholder="Start Date"
                    margin="normal"
                    id="date-picker-inline"
                    inputVariant="outlined"
                    value={sortDate}
                    // onKeyDown={(e) => {
                    //   e.preventDefault();
                    // }}
                    onChange={(e) => this.handleSortStartDate(e)}
                    KeyboardButtonProps={{
                      "aria-label": "change date",
                    }}
                    className="date-picker-fixture advt-datepicker"
                    style={{ margin: "0px 10px 0px 0px" }}
                    slots={{
                      openPickerIcon: TodayIcon,
                    }}
                    slotProps={{
                      field: {
                        id: "date-picker-inline",
                        placeholder: "DD/MM/YYYY",
                      },
                    }}
                  />
                </LocalizationProvider>

                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    clearable
                    onClear={() => this.clearEndDate()}
                    open={this.state.endDateOpen}
                    onOpen={() => this.setState({ endDateOpen: true })}
                    onClose={() => this.setState({ endDateOpen: false })}
                    autoOk
                    // disableToolbar
                    // variant="inline"
                    format="dd/MM/yyyy"
                    placeholder="End Date"
                    margin="normal"
                    id="date-picker-inline"
                    inputVariant="outlined"
                    value={filterEndDate}
                    minDate={sortDate}
                    // onKeyDown={(e) => {
                    //   e.preventDefault();
                    // }}

                    onChange={(e) => this.handleFilterEndDate(e)}
                    KeyboardButtonProps={{
                      "aria-label": "change date",
                    }}
                    className="date-picker-fixture advt-datepicker"
                    style={{ margin: "0px" }}
                    slots={{
                      openPickerIcon: TodayIcon,
                    }}
                    slotProps={{
                      field: {
                        id: "date-picker-inline",
                        placeholder: "DD/MM/YYYY",
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <CSVExport data={csvListData} filename="web_Ads_data.csv" />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && advTableListData?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && advTableListData?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow className="table-rows">
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer", width: "6%" }}
                        >
                          DID{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortLabelid
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell>Title</TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("pageName")}
                          style={{ cursor: "pointer" }}
                        >
                          Page Name{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "pageName"
                                ? sortName
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        <TableCell>Position</TableCell>
                        <TableCell
                          style={{ width: "10%", cursor: "pointer" }}
                          onClick={() => this.sortLabelHandler("startDate")}
                        >
                          Start Date
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "startDate"
                                ? sortStartDate
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          style={{ width: "10%", cursor: "pointer" }}
                          onClick={() => this.sortLabelHandler("endDate")}
                        >
                          End Date{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "endDate"
                                ? sortEndDate
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Clicks</TableCell>
                        <TableCell>Impression</TableCell>
                        <TableCell style={{ width: "15%" }}> Action </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {advTableListData?.length > 0 ? (
                        advTableListData?.map((item, index) => {
                          return (
                            <TableRow
                              className="listTable-Row table-rows"
                              key={index}
                            >
                              <TableCell> {item?.id} </TableCell>
                              <TableCell> {item?.title} </TableCell>
                              <TableCell> {item?.pageName} </TableCell>
                              <TableCell>
                                {this.facthPostionName(
                                  item?.page_id,
                                  item?.position_id
                                )}
                              </TableCell>
                              <TableCell>
                                {/* {item?.startDate
                                ? moment
                                    .utc(item?.startDate)
                                    .format("DD-MM-YYYY")
                                : "-"} */}
                                {item?.startDate
                                  ? moment(item?.startDate)
                                      .tz(timezone)
                                      .format("DD-MM-YYYY")
                                  : "-"}
                              </TableCell>
                              <TableCell>
                                {/* {" "}
                              {item?.endDate
                                ? moment
                                    .utc(item?.endDate)
                                    .format("DD-MM-YYYY")
                                : "-"}{" "} */}
                                {item?.endDate
                                  ? moment(item?.endDate)
                                      .tz(timezone)
                                      .format("DD-MM-YYYY")
                                  : "-"}
                              </TableCell>
                              <TableCell> {item?.status} </TableCell>
                              <TableCell>
                                {" "}
                                {item?.AdsMappings?.click}{" "}
                              </TableCell>
                              <TableCell>
                                {item?.AdsMappings?.impression}
                              </TableCell>
                              <TableCell>
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  className="table-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  className="table-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      {/* {(!selectedPage && !selectedBanner) || (!selectedPage === 9 || !selectedPage === 10) ? ( */}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}

                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                advCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={paginationPage[paginationPage?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />

                            {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                      {/* ) : (
                                              <></>
                                          )} */}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}
            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Advertisement"
                    : "Edit Advertisement"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Title </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Title"
                          value={AdValues?.title}
                          onChange={(e) =>
                            this.setState({
                              AdValues: {
                                ...AdValues,
                                title: e.target.value,
                              },
                            })
                          }
                        />
                        {errorTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Page </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Select Page"
                          value={advPageListOptions?.find((item) => {
                            return item?.value == selectedModalPage;
                          })}
                          onChange={(e) =>
                            this.handleFetchAdBannerList(e.value, "Modalpage")
                          }
                          options={advPageListOptions}
                        />
                        {errorPage ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorPage}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Position </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Select Position"
                          isDisabled={
                            !selectedModalPage ||
                            selectedModalPage === 9 ||
                            selectedModalPage === 10 ||
                            selectedModalPage === 11
                          }
                          value={
                            selectedModalBanner &&
                            advBannerListOptions?.find((item) => {
                              return item?.value == selectedModalBanner;
                            })
                          }
                          onChange={(e) =>
                            this.setState({ selectedModalBanner: e.value })
                          }
                          options={advBannerListOptions}
                        />
                        {errorPosition ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorPosition}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginTop: "15px",
                        }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> URL </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="URL"
                          value={AdValues?.url}
                          onChange={(e) =>
                            this.setState({
                              AdValues: {
                                ...AdValues,
                                url: e.target.value,
                              },
                            })
                          }
                        />
                        {errorUrl ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorUrl}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        className="date-time-picker-wrap"
                        style={{ marginTop: "15px" }}
                      >
                        <label className="modal-label"> Start Date </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          {/* <KeyboardDateTimePicker */}
                          <DatePicker
                            variant="inline"
                            inputVariant="outlined"
                            ampm={false}
                            value={moment(AdValues?.startDate)
                              .tz(timezone)
                              .format("YYYY-MM-DD")}
                            onChange={(e) =>
                              this.setState({
                                AdValues: {
                                  ...AdValues,
                                  startDate: e,
                                },
                              })
                            }
                            autoOk={true}
                            format="yyyy/MM/dd"
                            className="date-time-picker"
                          />
                        </LocalizationProvider>
                        {errorstartDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorstartDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        className="date-time-picker-wrap"
                        style={{ marginTop: "15px" }}
                      >
                        <label className="modal-label"> End Date </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DatePicker
                            variant="inline"
                            inputVariant="outlined"
                            ampm={false}
                            value={moment(AdValues?.endDate)
                              .tz(timezone)
                              .format("YYYY-MM-DD")}
                            onChange={(e) =>
                              this.setState({
                                AdValues: {
                                  ...AdValues,
                                  endDate: e,
                                },
                              })
                            }
                            minDate={AdValues?.startDate}
                            autoOk={true}
                            format="yyyy/MM/dd"
                            className="date-time-picker"
                          />
                        </LocalizationProvider>
                        {errorendDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorendDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        className="radio-wrap ad-radio"
                        style={{ marginTop: "15px" }}
                      >
                        <FormControl component="fieldset">
                          <label className="modal-label"> Ad Type </label>
                          <RadioGroup
                            aria-label="Ad Type"
                            name="Ad Type"
                            className="gender"
                            value={AdValues?.type}
                            onChange={(e) =>
                              this.setState({
                                AdValues: {
                                  ...AdValues,
                                  type: e.target.value,
                                },
                                createError: "",
                              })
                            }
                          >
                            <FormControlLabel
                              value="image"
                              control={
                                <Radio
                                  color="primary"
                                  checked={AdValues?.type === "image"}
                                />
                              }
                              label="image"
                            />
                            <FormControlLabel
                              value="script"
                              control={
                                <Radio
                                  color="primary"
                                  checked={AdValues?.type === "script"}
                                />
                              }
                              label="script"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorType ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorType}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {AdValues?.type === "image" ? (
                        <Grid item xs={12}>
                          <div className="blog-file-upload">
                            <h6>image</h6>
                            <FileUploader
                              onDrop={(image) =>
                                this.handleFileUpload("image", image)
                              }
                            />
                            <div className="logocontainer">
                              {image?.length > 0
                                ? image?.map((file, index) => (
                                    <>
                                      <img
                                        className="auto-width"
                                        key={index}
                                        src={file.preview}
                                        alt="ad"
                                      />
                                    </>
                                  ))
                                : uploadImage &&
                                  uploadImage !== "" && (
                                    <>
                                      <img
                                        className="auto-width"
                                        src={config.mediaUrl + uploadImage}
                                        alt="ad"
                                      />
                                    </>
                                  )}
                            </div>
                          </div>
                        </Grid>
                      ) : AdValues?.type === "script" ? (
                        <Grid item xs={12}>
                          <TextField
                            id="standard-multiline-static"
                            variant="outlined"
                            multiline
                            rows={4}
                            style={{
                              width: "100%",
                              backgroundColor: "#ffffff",
                            }}
                            placeholder="Script"
                            value={script}
                            onChange={(e) =>
                              this.setState({
                                script: e.target.value,
                              })
                            }
                          />
                          {errorScript ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0 0 0" }}
                            >
                              {errorScript}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                      ) : (
                        <></>
                      )}
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                        {createError ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0", width: "300px" }}
                          >
                            {createError}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default AdvertisementSection;
