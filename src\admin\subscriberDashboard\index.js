import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import {
  Box,
  Breadcrumbs,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Link } from "react-router-dom";
import { Loader } from "../../library/common/components";
import {
  BarChart,
  LineChart,
  Line,
  Bar,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import Select from "react-select";
import "./subscriberDashboard.scss";
import moment from "moment";
import ArrowBack from "@mui/icons-material/ArrowBack";
import ArrowForward from "@mui/icons-material/ArrowForward";
import axiosInstance from "../../helpers/Axios";

const barDataOption = [
  {
    label: "Weekly",
    value: 0,
  },

  {
    label: "Monthly",
    value: 1,
  },
  {
    label: "Yearly",
    value: 2,
  },
];

// const initialData = [
//   { date: "2023-08-31", label: "Thursday", userCount: 2 },
//   { date: "2023-09-01", label: "Friday", userCount: 6 },
//   { date: "2023-09-02", label: "Saturday", userCount: 9 },
//   { date: "2023-09-29", label: "Friday", userCount: 5 },
//   { date: "2023-09-28", label: "Thursday", userCount: 2 },
// ];

const SubscriberDashboard = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentWeek, setCurrentWeek] = useState(moment().week());
  const [initialData, setInitialData] = useState([]);
  const [selectedOption, setSelectedOption] = useState("Weekly");
  const [currentStartDate, setCurrentStartDate] = useState(
    moment(Date()).startOf("week").format("YYYY-MM-DD")
  );

  const [filteredData, setFilteredData] = useState([]);
  const [weeklyStartDate, setWeeklyStartDate] = useState("");
  const [monthlyStartDate, setMonthlyStartDate] = useState("");
  const [yearlyStartDate, setYearlyStartDate] = useState("");
  const [weeklyEndDate, setWeeklyEndDate] = useState("");
  const [monthlyEndDate, setMonthlyEndDate] = useState("");
  const [yearlyEndDate, setYearlyEndDate] = useState("");
  const [totalUserCount, setTotalUserCount] = useState({
    DailyNotification: 0,
    TipOfTheDay: 0,
    BestBet: 0,
    FridayNewsletter: 0,
    WednesdayNewsLetter: 0,
  });
  const [currentUserCount, setCurrentUserCount] = useState({});
  const [visibility, setVisibility] = useState({
    DailyNotification: true,
    TipOfTheDay: true,
    BestBet: true,
    WednesdayNewsLetterCount: true,
    // FridayNewsLetterCount: true,
  });

  const fetchChartData = async (startDate, endDate, week, month, day, year) => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        `/emailChart?timezone=Asia/Calcutta&startDate=${startDate}&endDate=${endDate}&week=${week}&month=${month}&day=${day}&year=${year}`
      );

      if (status === 200) {
        const transformedData = data?.result?.map((item) => {
          const obj = { date: item?.date, label: item?.label };
          item.types.forEach((type) => {
            obj[type?.type] = type?.count;
            obj[`${type?.type}_todayCount`] = type?.todayCount;
          });
          return obj;
        });
        const totals = transformedData?.reduce(
          (acc, curr) => {
            acc.DailyNotification += curr?.DailyNotification;
            acc.TipOfTheDay += curr?.TipOfTheDay;
            acc.BestBet += curr?.BestBet;
            acc.Newsletter += curr?.Newsletter;
            return acc;
          },
          {
            DailyNotification: 0,
            TipOfTheDay: 0,
            BestBet: 0,
            Newsletter: 0,
          }
        );
        setCurrentUserCount(totals);
        setTotalUserCount({
          DailyNotification: data?.dailynotificationCount,
          TipOfTheDay: data?.tipOfTheDayCount,
          BestBet: data?.dailyBestBetCount,
          // FridayNewsletter: data?.fridayNewsLetterCount,
          WednesdayNewsLetter: data?.wednesdayNewsLetterCount,
        });
        setInitialData(transformedData);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    let startDate, endDate;
    startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
    setWeeklyStartDate(startDate);
    endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
    setWeeklyEndDate(endDate);
    fetchChartData(startDate, endDate, "", "", true, "");
  }, []);

  const handleSelectOptionChnage = (option) => {
    let startDate, endDate;
    switch (option) {
      case "Weekly":
        startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
        setWeeklyStartDate(startDate);
        setWeeklyEndDate(endDate);
        setMonthlyStartDate("");
        setMonthlyEndDate("");
        setYearlyStartDate("");
        setYearlyEndDate("");
        setSelectedOption(option);
        fetchChartData(startDate, endDate, "", "", true, "");
        setCurrentStartDate(startDate);
        break;
      case "Monthly":
        startDate = moment(Date()).startOf("month").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("month").format("YYYY-MM-DD");
        setWeeklyStartDate("");
        setWeeklyEndDate("");
        let monthYear = moment(startDate).year();
        const monthName = moment(startDate).format("MMMM");
        setMonthlyStartDate(monthName);
        setMonthlyEndDate(monthYear);
        setYearlyStartDate("");
        setYearlyEndDate("");
        setSelectedOption(option);
        fetchChartData(startDate, "", "", "", true, "");
        setCurrentStartDate(startDate);
        break;
      case "Yearly":
        startDate = moment(Date()).startOf("year").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("year").format("YYYY-MM-DD");
        setWeeklyStartDate("");
        setWeeklyEndDate("");
        setMonthlyStartDate("");
        setMonthlyEndDate("");
        let year = moment(startDate).year();
        setYearlyStartDate(year);
        // setYearlyEndDate(year);
        setSelectedOption(option);
        fetchChartData(startDate, "", "", true, "", "");
        setCurrentStartDate(startDate);
        break;
      default:
        startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
        setCurrentStartDate(startDate);
        fetchChartData(startDate, endDate, "", "", true, "");
    }
  };

  const handlePrevious = () => {
    let newStartDate, newEndDate;
    switch (selectedOption) {
      case "Weekly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "weeks")
          .startOf("weeks")
          .format("YYYY-MM-DD");
        setWeeklyStartDate(newStartDate);

        newEndDate = moment(currentStartDate)
          .subtract(1, "weeks")
          .endOf("weeks")
          .format("YYYY-MM-DD");
        setWeeklyEndDate(newEndDate);
        fetchChartData(newStartDate, newEndDate, "", "", true, "");
        setCurrentStartDate(newStartDate);
        break;
      case "Monthly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "months")
          .startOf("isoMonth")
          .format("YYYY-MM-DD");

        const monthName = moment(newStartDate).format("MMMM");
        setMonthlyStartDate(monthName);

        newEndDate = moment(currentStartDate)
          .subtract(1, "months")
          .endOf("isoMonth")
          .format("YYYY-MM-DD");
        let monthYear = moment(newEndDate).year();
        setMonthlyEndDate(monthYear);
        fetchChartData(newStartDate, "", "", "", true, "");
        setCurrentStartDate(newStartDate);
        break;
      case "Yearly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "years")
          .startOf("isoYear")
          .format("YYYY-MM-DD");
        let year = moment(newStartDate).year();
        setYearlyStartDate(year);
        setYearlyEndDate(newEndDate);
        fetchChartData(newStartDate, "", "", true, "", "");
        setCurrentStartDate(newStartDate);
        break;
      default:
        break;
    }
  };

  const isNextDisable = useCallback(() => {
    const conditionToCheck =
      (selectedOption === "Weekly" &&
        currentStartDate ===
          moment(Date()).startOf("week").format("YYYY-MM-DD")) ||
      (selectedOption === "Monthly" &&
        currentStartDate ===
          moment(Date()).startOf("month").format("YYYY-MM-DD")) ||
      (selectedOption === "Yearly" &&
        currentStartDate ===
          moment(Date()).startOf("year").format("YYYY-MM-DD"));
    if (conditionToCheck) {
      return true;
    } else {
      return false;
    }
  }, [selectedOption, currentStartDate]);

  const handleNext = () => {
    if (!isNextDisable()) {
      let newStartDate, newEndDate;

      switch (selectedOption) {
        case "Weekly":
          newStartDate = moment(currentStartDate)
            .add(1, "weeks")
            .startOf("weeks")
            .format("YYYY-MM-DD");

          setWeeklyStartDate(newStartDate);

          newEndDate = moment(currentStartDate)
            .add(1, "weeks")
            .endOf("weeks")
            .format("YYYY-MM-DD");
          setWeeklyEndDate(newEndDate);
          fetchChartData(newStartDate, newEndDate, "", "", true, "");
          setCurrentStartDate(newStartDate);
          break;
        case "Monthly":
          newStartDate = moment(currentStartDate)
            .add(1, "months")
            .endOf("isoMonth")
            .format("YYYY-MM-DD");

          const monthName = moment(newStartDate).format("MMMM");
          setMonthlyStartDate(monthName);
          newEndDate = moment(currentStartDate)
            .subtract(1, "months")
            .endOf("isoMonth")
            .format("YYYY-MM-DD");
          let monthYear = moment(newEndDate).year();
          setMonthlyEndDate(monthYear);
          fetchChartData(newStartDate, "", "", "", true, "");
          setCurrentStartDate(newStartDate);
          break;
        case "Yearly":
          newStartDate = moment(currentStartDate)
            .add(1, "years")
            .startOf("isoYear")
            .format("YYYY-MM-DD");

          let year = moment(newStartDate).year();
          setYearlyStartDate(year);
          fetchChartData(newStartDate, "", "", true, "", "");
          setCurrentStartDate(newStartDate);
          break;
        default:
          break;
      }
    }
  };

  const getTimePeriod = () => {
    const timePeriod =
      selectedOption === "Weekly"
        ? "Week"
        : selectedOption === "Monthly"
        ? "Month"
        : selectedOption === "Yearly"
        ? "Year"
        : "";
    return timePeriod;
  };

  const handleLegendClick = (e) => {
    setVisibility((prev) => ({
      ...prev,
      [e.value]: !prev[e.value],
    }));
  };

  // Custom Legend Renderer
  const renderLegend = (props) => {
    const { payload } = props;

    return (
      <div
        style={{ display: "flex", justifyContent: "center", marginTop: "10px" }}
      >
        {payload.map((entry, index) => (
          <span
            key={`item-${index}`}
            onClick={() => handleLegendClick(entry)}
            style={{
              cursor: "pointer",
              margin: "0 10px",
              opacity: visibility[entry?.value] ? 1 : 0.6,
              display: "inline-flex",
              alignItems: "center",
            }}
          >
            <svg width="10" height="10" style={{ marginRight: "5px" }}>
              <rect width="10" height="10" fill={entry?.color} />
            </svg>
            {entry?.value}
          </span>
        ))}
      </div>
    );
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length > 0) {
      return (
        <div className="custom-tooltip">
          {payload?.map((ele) => {
            return (
              <p className="label" style={{ color: ele.color }}>{`${
                ele.name === "WednesdayNewsLetterCount"
                  ? "Wednesday/FridayNewsLetterCount"
                  : ele.name
              } : ${ele.value} (${ele.payload[`${ele.name}_todayCount`]})`}</p>
            );
          })}
        </div>
      );
    }

    return null;
  };

  return (
    <>
      <Grid container className="page-content adminLogin">
        <Grid item xs={12} className="pageWrapper">
          <Box className="bredcrumn-wrap">
            <Breadcrumbs
              separator="/"
              aria-label="breadcrumb"
              className="breadcrumb"
            >
              <Link underline="hover" color="inherit" to="/dashboard">
                Home
              </Link>
              <Link underline="hover" color="inherit">
                User Management
              </Link>
              <Typography className="active_p">Subscriber Dashboard</Typography>
            </Breadcrumbs>
          </Box>
          <Grid container direction="row" alignItems="center">
            <Grid item xs={4}>
              <Typography variant="h1" align="left">
                Subscriber Dashboard
              </Typography>
            </Grid>
            <Grid item xs={8} className="admin-filter-wrap">
              <Select
                className="React cricket-select external-select"
                classNamePrefix="select"
                placeholder="Select Status"
                options={barDataOption}
                value={barDataOption.find(
                  (option) => option?.label === selectedOption
                )}
                onChange={(option) => {
                  handleSelectOptionChnage(option?.label);
                }}
              />
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Table className="subscriber-dashboared-table">
                <TableBody>
                  <TableRow>
                    <TableCell
                      style={{ textAlign: "center", fontWeight: "700" }}
                    >
                      {" "}
                      Total Subscribed
                    </TableCell>
                  </TableRow>
                  {visibility?.DailyNotification && (
                    <TableRow>
                      <TableCell>
                        <Typography>
                          <span> Total Daily Notification: </span>
                          {totalUserCount?.DailyNotification}
                        </Typography>
                      </TableCell>
                      {/* <TableCell>
                        <Typography>
                          <span>Current Daily Notification: </span>
                          {currentUserCount?.DailyNotification}{" "}
                        </Typography>
                      </TableCell> */}
                    </TableRow>
                  )}
                  {visibility?.TipOfTheDay && (
                    <TableRow>
                      <TableCell>
                        <Typography>
                          <span> Total Tip Of The Day:</span>
                          {totalUserCount?.TipOfTheDay}
                        </Typography>
                      </TableCell>
                      {/* <TableCell>
                        <Typography>
                          <span> Current Tip Of The Day: </span>
                          {currentUserCount?.TipOfTheDay}{" "}
                        </Typography>
                      </TableCell> */}
                    </TableRow>
                  )}
                  {visibility?.BestBet && (
                    <TableRow>
                      <TableCell>
                        <Typography>
                          <span>Total Best Bet:</span>
                          {totalUserCount?.BestBet}
                        </Typography>
                      </TableCell>
                      {/* <TableCell>
                        <Typography>
                          <span>Current Best Bet:</span>
                          {currentUserCount?.BestBet}{" "}
                        </Typography>
                      </TableCell> */}
                    </TableRow>
                  )}
                  {visibility?.WednesdayNewsLetterCount && (
                    <TableRow>
                      <TableCell>
                        <Typography>
                          <span> Total Wednesday/Friday Newsletter:</span>
                          {totalUserCount?.WednesdayNewsLetter}
                        </Typography>
                      </TableCell>
                      {/* <TableCell>
                        <Typography>
                          <span>Current News letter:</span>
                          {currentUserCount?.Newsletter}{" "}
                        </Typography>
                      </TableCell> */}
                    </TableRow>
                  )}
                  {/* {visibility?.FridayNewsLetterCount && (
                    <TableRow>
                      <TableCell>
                        <Typography>
                          <span>Friday Newsletter:</span>
                          {totalUserCount?.FridayNewsletter}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )} */}
                </TableBody>
              </Table>
            </Grid>

            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Typography align="center">
                {weeklyStartDate && weeklyEndDate
                  ? moment(weeklyStartDate).format("DD-MM-YYYY") +
                    " - " +
                    moment(weeklyEndDate).format("DD-MM-YYYY")
                  : ""}
                {monthlyStartDate && monthlyEndDate
                  ? monthlyStartDate + " - " + monthlyEndDate
                  : ""}
                {yearlyStartDate ? yearlyStartDate : ""}
              </Typography>
            </Grid>
          </Grid>
          {isLoading && <Loader />}
          {!isLoading && initialData?.length === 0 && (
            <p className="text-center NoDataPadding">No data available.</p>
          )}
          <Box className="dashboard-box">
            <Box className="line-chart-box">
              {!isLoading && initialData?.length > 0 && (
                <>
                  <Typography variant="h6" align="center">
                    Subscriber Dashboard
                  </Typography>

                  <Box className="mt-22">
                    <ResponsiveContainer width="100%" height={400}>
                      <LineChart data={initialData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey={
                            selectedOption === "Monthly" ? "date" : "label"
                          }
                        />
                        <YAxis />

                        <Tooltip
                          className="custom-tooltip-wrap"
                          content={<CustomTooltip />}
                        />

                        <Legend
                          content={renderLegend}
                          payload={[
                            {
                              value: "DailyNotification",
                              type: "square",
                              color: "#8884d8",
                            },
                            {
                              value: "TipOfTheDay",
                              type: "square",
                              color: "#82ca9d",
                            },
                            {
                              value: "BestBet",
                              type: "square",
                              color: "#ffc658",
                            },
                            {
                              value: "Wednesday/FridayNewsLetterCount",
                              type: "square",
                              color: "#ff7300",
                            },
                            // {
                            //   value: "FridayNewsLetterCount",
                            //   type: "square",
                            //   color: "#1990df",
                            // },
                          ]}
                        />
                        {visibility?.DailyNotification && (
                          <Line
                            type="monotone"
                            dataKey="DailyNotification"
                            stroke="#8884d8"
                          />
                        )}
                        {visibility?.TipOfTheDay && (
                          <Line
                            type="monotone"
                            dataKey="TipOfTheDay"
                            stroke="#82ca9d"
                          />
                        )}
                        {visibility?.BestBet && (
                          <Line
                            type="monotone"
                            dataKey="BestBet"
                            stroke="#ffc658"
                          />
                        )}
                        {visibility?.WednesdayNewsLetterCount && (
                          <Line
                            type="monotone"
                            dataKey="WednesdayNewsLetterCount"
                            stroke="#ff7300"
                          />
                        )}
                        {/* {visibility?.FridayNewsLetterCount && (
                          <Line
                            type="monotone"
                            dataKey="FridayNewsLetterCount"
                            stroke="#1990df"
                          />
                        )} */}
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </>
              )}
              {!isLoading && initialData.length > 0 ? (
                <Box className="arrow-box mt-22 text-center">
                  <Box
                    className="flex mr-30 arrow"
                    onClick={() => handlePrevious()}
                  >
                    <ArrowBack />
                    <Typography>{`Prev ${getTimePeriod()}`}</Typography>
                  </Box>

                  <Box
                    className="flex arrow"
                    onClick={() => handleNext()}
                    style={{ opacity: isNextDisable() ? "0.5" : "1" }}
                  >
                    <Typography>{`Next ${getTimePeriod()}`}</Typography>
                    <ArrowForward />
                  </Box>
                </Box>
              ) : (
                <></>
              )}
            </Box>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default SubscriberDashboard;
