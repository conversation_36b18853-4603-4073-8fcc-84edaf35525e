import { Box, Typography } from "@mui/material";
import EastIcon from "@mui/icons-material/East";
import WestIcon from "@mui/icons-material/West";
import React, { useState, useMemo } from "react";
import Select from "react-select";
import UpPrice from "../../images/upArrowPrice.png";
import DownPrice from "../../images/downArrowPrice.png";
import CardSection from "./cardSection";
import { useMonthSelector } from "./useMonthSelector";
import MyBarChart from "./MyBarChart";

const cardData = [
  {
    title: "Current Paying Subscribers",
    value: 300,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "New Subscribers (last 30 days)",
    value: 100,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "Subscription Renewals",
    value: 100,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "decrease",
  },
  {
    title: "Subscription Cancellations",
    value: 33,
    unit: null,
    currency: null,
    change: 13.3,
    changeType: "increase",
  },
  {
    title: "Payment",
    value: 10284,
    unit: null,
    currency: "USD",
    change: 13.3,
    changeType: "increase",
  },
];

const SOCTabDeatils = ({ selectedYear }) => {
  // Use the custom hook for 5 selectors (Paying, Payment, Renewals, Cancellations)
  const { monthOptions, selectedMonths, setSelectedMonth } = useMonthSelector(
    selectedYear,
    4
  );

  // New Subscribers options
  const newSubscribersOptions = [
    { label: "Last 7 days", value: "last7" },
    { label: "Last 30 days", value: "last30" },
  ];
  const [selectedNewSubscribersOption, setSelectedNewSubscribersOption] =
    useState(newSubscribersOptions[0]);

  // When year changes, select first value for New Subscribers
  React.useEffect(() => {
    setSelectedNewSubscribersOption(newSubscribersOptions[0]);
  }, [selectedYear]);

  // console.log(
  //   "selectedYear",
  //   selectedYear,
  //   selectedMonths[0],
  //   selectedMonths[1],
  //   selectedMonths[2],
  //   selectedMonths[3]
  // );

  const scoreChartData = [
    { name: "Sun", paid: 100, free: 51 },
    { name: "Mon", paid: 100, free: 33 },
    { name: "Tue", paid: 102, free: 51 },
    { name: "Wed", paid: 100, free: 51 },
    { name: "Thu", paid: 73, free: 51 },
    { name: "Fri", paid: 120, free: 51.3 },
    { name: "Sat", paid: 130, free: 58.2 },
  ];

  const multipleBarsConfig = [
    { dataKey: "paid", color: "#4455C7", label: "Paid users" },
    { dataKey: "free", color: "#FDA289", label: "Free trial" },
  ];

  const PaymentChartData = [
    { name: "Sun", payment: 10000 },
    { name: "Mon", payment: 9100 },
    { name: "Tue", payment: 500 },
    { name: "Wed", payment: 21000 },
    { name: "Thu", payment: 5100 },
    { name: "Fri", payment: 1000 },
  ];

  const paymentBarsConfig = [
    { dataKey: "payment", color: "#FC6B43", label: "Payment" },
  ];

  const RenewalsChartData = [
    { name: "Sun", renewal: 50 },
    { name: "Mon", renewal: 10 },
    { name: "Tue", renewal: 18 },
    { name: "Wed", renewal: 32 },
    { name: "Thu", renewal: 5 },
    { name: "Fri", renewal: 11 },
  ];

  const RenewalsBarsConfig = [
    { dataKey: "renewal", color: "#1C9A6C", label: "Renewals" },
  ];

  const CancelledChartData = [
    { name: "Sun", cancelled: 50 },
    { name: "Mon", cancelled: 10 },
    { name: "Tue", cancelled: 18 },
    { name: "Wed", cancelled: 32 },
    { name: "Thu", cancelled: 5 },
    { name: "Fri", cancelled: 11 },
  ];

  const CancelledBarsConfig = [
    { dataKey: "cancelled", color: "#E2662C", label: "Subscription Cancelled" },
  ];

  return (
    <>
      <Box className="details-wrap">
        <Box className="card-section">
          {cardData?.map((item, index, arr) => {
            return (
              <CardSection
                itemData={item}
                itemIndex={index}
                fullData={arr}
                key={index}
              />
            );
          })}
        </Box>
        <Box className="bar-chart-section">
          <Box className="d-flex-ss align-items-center gap-18">
            {/* Current Paying Subscribers */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">
                  Current Paying Subscribers
                </Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Month"
                    options={monthOptions}
                    value={selectedMonths[0]}
                    onChange={(option) => setSelectedMonth(0, option)}
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">500</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={UpPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <Box className="d-flex-ss align-items-center justify-content-between">
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    <WestIcon />
                    Previous Week
                  </Typography>
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    Next Week <EastIcon />
                  </Typography>
                </Box>
                <MyBarChart
                  data={scoreChartData}
                  bars={multipleBarsConfig}
                  xAxisKey="name"
                  barSize={30}
                />
              </Box>
            </Box>
            {/* New Subscribers (last 7 days) */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">
                  New Subscribers (last 7 days)
                </Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Period"
                    options={newSubscribersOptions}
                    value={selectedNewSubscribersOption}
                    onChange={(option) =>
                      setSelectedNewSubscribersOption(option)
                    }
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">100</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={UpPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <MyBarChart
                  data={scoreChartData}
                  bars={multipleBarsConfig}
                  xAxisKey="name"
                   barSize={30}
                />
              </Box>
            </Box>
          </Box>
          {/* Payment */}
          <Box className="bar-wrap-section mt-18 mb-18">
            <Box className="d-flex-ss align-items-center justify-content-between">
              <Typography className="graph-title">Payment</Typography>
              <Box>
                <Select
                  className="React cricket-select external-select w-100"
                  classNamePrefix="select"
                  placeholder="Select Month"
                  options={monthOptions}
                  value={selectedMonths[1]}
                  onChange={(option) => setSelectedMonth(1, option)}
                  isDisabled={!selectedYear}
                  menuPosition="fixed"
                />
              </Box>
            </Box>
            <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
              <Typography className="details-count">10284</Typography>
              <Typography className="value-change-count d-flex-ss align-items-center ">
                <img src={UpPrice} alt="icon" /> 13.33%
              </Typography>
            </Box>
            <Box className="mt-18">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="d-flex-ss align-items-center arrow-text">
                  <WestIcon />
                  Previous Week
                </Typography>
                <Typography className="d-flex-ss align-items-center arrow-text">
                  Next Week <EastIcon />
                </Typography>
              </Box>
              <MyBarChart
                data={PaymentChartData}
                bars={paymentBarsConfig}
                xAxisKey="name"
                 barSize={50}
              />
            </Box>
          </Box>
          <Box className="d-flex-ss align-items-center gap-18">
            {/* Subscription Renewals */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">
                  Subscription Renewals
                </Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Month"
                    options={monthOptions}
                    value={selectedMonths[2]}
                    onChange={(option) => setSelectedMonth(2, option)}
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">100</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={DownPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <Box className="d-flex-ss align-items-center justify-content-between">
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    <WestIcon />
                    Previous Week
                  </Typography>
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    Next Week <EastIcon />
                  </Typography>
                </Box>
                <MyBarChart
                  data={RenewalsChartData}
                  bars={RenewalsBarsConfig}
                  xAxisKey="name"
                   barSize={30}
                />
              </Box>
            </Box>
            {/* Subscription Cancellations */}
            <Box className="w-50 bar-wrap-section">
              <Box className="d-flex-ss align-items-center justify-content-between">
                <Typography className="graph-title">
                  Subscription Cancellations
                </Typography>
                <Box>
                  <Select
                    className="React cricket-select external-select w-100"
                    classNamePrefix="select"
                    placeholder="Select Month"
                    options={monthOptions}
                    value={selectedMonths[3]}
                    onChange={(option) => setSelectedMonth(3, option)}
                    isDisabled={!selectedYear}
                    menuPosition="fixed"
                  />
                </Box>
              </Box>
              <Box className="d-flex-ss align-items-center justify-content-between graph-count-section">
                <Typography className="details-count">33</Typography>
                <Typography className="value-change-count d-flex-ss align-items-center ">
                  <img src={UpPrice} alt="icon" /> 13.33%
                </Typography>
              </Box>
              <Box className="mt-18">
                <Box className="d-flex-ss align-items-center justify-content-between">
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    <WestIcon />
                    Previous Week
                  </Typography>
                  <Typography className="d-flex-ss align-items-center arrow-text">
                    Next Week <EastIcon />
                  </Typography>
                </Box>
                <MyBarChart
                  data={CancelledChartData}
                  bars={CancelledBarsConfig}
                  xAxisKey="name"
                   barSize={30}
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default SOCTabDeatils;
