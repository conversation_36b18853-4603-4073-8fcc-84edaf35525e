import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
// import {
//   MuiPickersUtilsProvider,
//   KeyboardDatePicker,
// } from "@material-ui/pickers";
import DateFnsUtils from "@date-io/date-fns";
import moment from "moment";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../rugbyleague.scss";
class Player extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      playerValues: {
        playerName: "",
        dob: "",
        team: "",
        rapidPlayerId: "",
        CountryId: "",
        id: "",
        CountryName: "",
      },
      countryAll: [],
      countryCount: 0,
      pageCountry: 0,
      searchCountry: [],
      searchCountryCount: 0,
      SearchCountrypage: 0,
      isCountrySearch: "",
      PlayerList: [],
      PlayerCount: 0,
      errorName: "",
      errorDob: "",
      errorTeam: "",
      errorCountry: "",
      externalTeamData: [],
      externalTeamCount: 0,
      selectTeam: "",
      SelectedExternalTeamList: [],
      ExternalTeamPage: 0,
      ModalTeamData: [],
      ModalTeamCount: 0,
      ModalTeamPage: 0,
    };
  }

  componentDidMount() {
    this.fetchAllPlayer();
    this.fetchAllTeam(this.state.ExternalTeamPage);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllPlayer();
    }
  }

  fetchAllCountry = async (page) => {
    const { status, data } = await axiosInstance.get(
      // `${URLS.distance}?size=20&page=${page}`
      `${URLS.country}?limit=20&offset=${page}`
    );

    if (status === 200) {
      let Parray = [...this.state.countryAll];

      let count = data?.result?.count / 20;
      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.country,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state.countryAll, newdata);

      this.setState({
        countryAll: _.uniqBy(filterData, function (e) {
          return e.value;
        }),
        countryCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomCountry = () => {
    let {
      pageCountry,
      isCountrySearch,
      searchCountryCount,
      SearchCountrypage,
      countryCount,
    } = this.state;

    if (
      isCountrySearch !== "" &&
      searchCountryCount !== Math.ceil(SearchCountrypage / 20 + 1)
    ) {
      this.handleCountryInputChange(SearchCountrypage + 20, isCountrySearch);
      this.setState({
        SearchCountrypage: SearchCountrypage + 20,
      });
    } else {
      if (countryCount !== Math.ceil(pageCountry / 20)) {
        this.fetchAllCountry(pageCountry + 20);
        this.setState({
          pageCountry: pageCountry + 20,
        });
      }
    }
  };

  handleCountryInputChange = (page, value) => {
    // if (value.length > 2) {
    axiosInstance
      .get(`${URLS.country}?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          //     let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(this.state.searchCountry, newdata);

          this.setState({
            searchCountry: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchCountryCount: Math.ceil(count),
            isCountrySearch: value,
          });
        }
      });
  };
  fetchSelectedCountry = (CountryId, CountryName) => {
    let seletedCountry = [
      {
        label: CountryName,
        value: CountryId,
      },
    ];

    this.setState({
      countryAll: CountryId ? seletedCountry : this.state.countryAll,
    });
  };
  fetchAllPlayer = async () => {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `rls/player?limit=${rowPerPage}&offset=${offset}`
      );
      if (status === 200) {
        this.setState({
          PlayerList: data?.result?.rows,
          isLoading: false,
          PlayerCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchAllTeam = async (ExternalTeamPage) => {
    const { status, data } = await axiosInstance.get(
      `rls/team?limit=20&offset=${ExternalTeamPage}`
    );
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      newdata = [
        ...newdata,
        {
          label: "All",
          value: 0,
        },
      ];
      let filterData = _.unionBy(this.state?.externalTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        externalTeamData: finalData,
        externalTeamCount: Math.ceil(count),
      });
    }
  };
  handleOnScrollBottomExternalTeam = (e, type) => {
    let {
      externalTeamCount,
      ExternalTeamPage,
      // isWeatherSearch,
      // searchWeatherCount,
      // SearchWeatherPage,
    } = this.state;
    // if (
    //   isWeatherSearch !== "" &&
    //   searchWeatherCount !== Math.ceil(SearchWeatherPage / 10)
    // ) {
    //   this.handleWeatherInputChange(SearchWeatherPage + 20, isWeatherSearch);
    //   this.setState({
    //     SearchWeatherPage: SearchWeatherPage + 20,
    //   });
    // } else {
    if (
      externalTeamCount !==
      (externalTeamCount == 1 ? 1 : Math.ceil(ExternalTeamPage / 20))
      // &&isWeatherSearch == ""
    ) {
      this.fetchAllTeam(ExternalTeamPage + 20);
      this.setState({
        ExternalTeamPage: ExternalTeamPage + 20,
      });
    }
    // }
  };

  handleExternalTeamChange = async (e) => {
    this.setState({
      selectTeam: e.value,
      isLoading: true,
    });
    try {
      const { status, data } = await axiosInstance.get(
        `rls/player/team/${e.value}`
      );
      if (status === 200) {
        let newdata = data?.result?.map((item) => {
          return {
            RLPlayerId: item?.RLPlayerId,
            RLTeam: item?.RLTeam,
            RLTeamId: item?.RLTeamId,
            createdAt: item?.createdAt,
            id: item?.id,
            updatedAt: item?.updatedAt,
            ...item.RLPlayer,
          };
        });
        this.setState({
          SelectedExternalTeamList: newdata,
          isLoading: false,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchModalAllTeam = async (ModalTeamPage) => {
    const { status, data } = await axiosInstance.get(
      `rls/team?limit=20&offset=${ModalTeamPage}`
    );
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        ModalTeamData: finalData,
        ModalTeamCount: Math.ceil(count),
      });
    }
  };
  handleOnScrollBottomModalTeam = (e, type) => {
    let {
      ModalTeamCount,
      ModalTeamPage,
      // isWeatherSearch,
      // searchWeatherCount,
      // SearchWeatherPage,
    } = this.state;
    // if (
    //   isWeatherSearch !== "" &&
    //   searchWeatherCount !== Math.ceil(SearchWeatherPage / 10)
    // ) {
    //   this.handleWeatherInputChange(SearchWeatherPage + 20, isWeatherSearch);
    //   this.setState({
    //     SearchWeatherPage: SearchWeatherPage + 20,
    //   });
    // } else {
    if (
      ModalTeamCount !==
      (ModalTeamCount == 1 ? 1 : Math.ceil(ModalTeamPage / 20))
      // &&isWeatherSearch == ""
    ) {
      this.fetchModalAllTeam(ModalTeamPage + 20);
      this.setState({
        ModalTeamPage: ModalTeamPage + 20,
      });
    }
    // }
  };
  fetchSelectedModalTeam = (TeamId, TeamName) => {
    let seletedTeam = [
      {
        label: TeamId,
        value: TeamName,
      },
    ];

    this.setState({
      ModalTeamData: TeamId ? seletedTeam : this.state.ModalTeamData,
    });
  };
  handalValidate = () => {
    let { playerValues } = this.state;

    let flag = true;
    if (playerValues?.playerName === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (playerValues?.dob === "" || playerValues?.dob === null) {
      flag = false;
      this.setState({
        errorDob: "This field is mandatory",
      });
    } else {
      this.setState({
        errorDob: "",
      });
    }
    if (playerValues?.team === "") {
      flag = false;
      this.setState({
        errorTeam: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTeam: "",
      });
    }
    if (playerValues?.CountryId === "") {
      flag = false;
      this.setState({
        errorCountry: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCountry: "",
      });
    }

    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      const { playerValues } = this.state;
      let teamsId = playerValues?.team?.map((item) => {
        return item?.value;
      });
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        name: playerValues?.playerName,
        rapidPlayerId: playerValues?.rapidPlayerId,
        dob: playerValues?.dob,
        RLTeamIds: teamsId,
        CountryId: playerValues?.CountryId,
      };
      const { status } = await axiosInstance.post(`rls/player`, payload);
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllPlayer();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      const { playerValues } = this.state;
      let teamsId = playerValues?.team?.map((item) => {
        return item?.value;
      });
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        name: playerValues?.playerName,
        rapidPlayerId: playerValues?.rapidPlayerId,
        dob: playerValues?.dob,
        RLTeamIds: teamsId,
        CountryId: playerValues?.CountryId,
      };
      const { status } = await axiosInstance.put(
        `rls/player/${this.state.playerValues?.id}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllPlayer();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Edited Successfully`
        //   );
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorDob: "",
      errorTeam: "",
      errorCountry: "",
    });
  };

  inputModal = (item, type) => () => {
    this.fetchAllCountry(0);
    this.fetchModalAllTeam(this.state.ModalTeamPage);
    this.setState({ isInputModalOpen: true });
    let teams = "";
    if (type === "edit") {
      this.fetchSelectedCountry(item?.CountryId, item?.Country?.country);
      if (!this.state.selectTeam) {
        teams = item?.RLTeamPlayers?.map((obj) => {
          return {
            value: obj?.RLTeamId,
            label: obj?.RLTeam?.name,
          };
        });
      } else {
        teams = {
          value: item?.RLTeam?.id,
          label: item?.RLTeam?.name,
        };
      }
      this.setState({
        playerValues: {
          playerName: item?.name,
          dob: item?.dob,
          team: teams,
          rapidPlayerId: item?.rapidPlayerId,
          CountryId: item?.CountryId,
          id: item?.id,
          CountryName: item?.Country?.country,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        playerValues: {
          playerName: "",
          dob: new Date(),
          team: "",
          rapidPlayerId: "",
          CountryId: "",
          id: "",
          CountryName: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `rls/player/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllPlayer();
        });
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  fetchPlayersTeams = (item) => {
    let teams = item?.RLTeamPlayers?.map((obj) => {
      return obj?.RLTeam?.name;
    });
    return teams?.toString();
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      playerValues,
      countryAll,
      countryCount,
      pageCountry,
      searchCountry,
      searchCountryCount,
      SearchCountrypage,
      isCountrySearch,
      PlayerList,
      PlayerCount,
      errorName,
      errorDob,
      errorTeam,
      errorCountry,
      externalTeamData,
      externalTeamCount,
      selectTeam,
      SelectedExternalTeamList,
      ExternalTeamPage,
      ModalTeamData,
      ModalTeamCount,
      ModalTeamPage,
    } = this.state;
    const pageNumbers = [];
    if (PlayerCount > 0) {
      for (let i = 1; i <= Math.ceil(PlayerCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    let FinalPlayerList = selectTeam ? SelectedExternalTeamList : PlayerList;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {/* {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )} */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Rugby League
                </Link>
                <Typography className="active_p">Player</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Player
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <Select
                  className="React rugby-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Team"
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomExternalTeam(e)
                  }
                  // onInputChange={(e) => this.handleDistanceInputChange(1, e)}
                  value={
                    // isDistanceSearch
                    //   ? searchDistance?.find((item) => {
                    //       return item?.value == distance;
                    //     })
                    //   : distanceAll?.find((item) => {
                    //       return item?.value == distance;
                    //     })
                    externalTeamData?.find((item) => {
                      return item?.value == selectTeam;
                    })
                  }
                  onChange={
                    (e) => this.handleExternalTeamChange(e)
                    // this.setState({
                    //   selectCategory: e.value,
                    // })
                  }
                  options={externalTeamData}
                />
                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  // value={search}
                  onChange={(e) => {
                    // setSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  // onClick={() => fetchAnimal()}
                >
                  Search
                </Button> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && PlayerList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && PlayerList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>rapidPlayerId</TableCell>
                        <TableCell>Player Name</TableCell>
                        {/* <TableCell style={{ width: "25%" }}>
                          variation
                        </TableCell> */}
                        {/* <TableCell style={{ width: "25%" }}>variation</TableCell> */}
                        <TableCell> Country </TableCell>
                        <TableCell>Teams</TableCell>
                        <TableCell>DOB</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {FinalPlayerList?.length > 0 ? (
                        FinalPlayerList?.map((item) => {
                          return (
                            <TableRow className="table-rows listTable-Row">
                              <TableCell> {item?.id} </TableCell>
                              <TableCell>
                                {item?.rapidPlayerId
                                  ? item?.rapidPlayerId
                                  : "-"}
                              </TableCell>
                              <TableCell>{item?.name}</TableCell>
                              <TableCell
                                style={{
                                  textAlign: !item?.Country?.country
                                    ? "center"
                                    : "-",
                                }}
                              >
                                {item?.Country?.country
                                  ? item?.Country?.country
                                  : "-"}
                              </TableCell>
                              <TableCell>
                                {selectTeam
                                  ? item?.RLTeam?.name
                                  : this.fetchPlayersTeams(item)}
                              </TableCell>
                              <TableCell
                                style={{
                                  textAlign: !item?.dob ? "center" : "-",
                                }}
                              >
                                {item?.dob
                                  ? moment(item?.dob).format("DD-MM-YYYY")
                                  : "-"}
                              </TableCell>
                              <TableCell>
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  className="table-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  className="table-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      {!selectTeam ? (
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  PlayerCount / rowPerPage > 1 ? false : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />

                              {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        <></>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Player" : "Edit Player"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="rugby-text"
                      >
                        <label className="modal-label"> Player Name</label>
                        <TextField
                          className="rugby-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Player Name"
                          value={playerValues?.playerName}
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                playerName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid item xs={6} className="rugby-dob-wrap">
                        <label className="modal-label"> DOB </label>
                        {/* <MuiPickersUtilsProvider utils={DateFnsUtils}>
                          <KeyboardDatePicker
                            // disableToolbar
                            variant="inline"
                            format="dd/MM/yyyy"
                            placeholder="DD/MM/YYYY"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={playerValues?.dob}
                            onChange={(e) =>
                              this.setState({
                                playerValues: {
                                  ...playerValues,
                                  dob: e,
                                },
                              })
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            autoOk
                            className="details-runner-picker dob-picker"
                          />
                          {errorDob ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0 0 0" }}
                            >
                              {errorDob}
                            </p>
                          ) : (
                            ""
                          )}
                        </MuiPickersUtilsProvider> */}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="rugby-text"
                      >
                        <label className="modal-label">rapid Player Id</label>
                        <TextField
                          className="rugby-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Player Id"
                          value={playerValues?.rapidPlayerId}
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                rapidPlayerId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Country </label>
                        <Select
                          className="React rugby-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Country"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomCountry(e)
                          }
                          // isSearchable={false}
                          onInputChange={(e) =>
                            this.handleCountryInputChange(0, e)
                          }
                          value={
                            isCountrySearch
                              ? searchCountry?.find((item) => {
                                  return item?.value == playerValues?.CountryId;
                                })
                              : countryAll?.find((item) => {
                                  return item?.value == playerValues?.CountryId;
                                })
                          }
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                CountryId: e.value,
                              },
                            })
                          }
                          options={isCountrySearch ? searchCountry : countryAll}
                        />
                        {errorCountry ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorCountry}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        <label className="modal-label"> Teams </label>
                        <Select
                          className="React rugby-select rugby-multiple-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          isMulti
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomModalTeam(e)
                          }
                          // onInputChange={(e) => this.handleDistanceInputChange(1, e)}
                          // value={
                          // isDistanceSearch
                          //   ? searchDistance?.find((item) => {
                          //       return item?.value == distance;
                          //     })
                          //   : distanceAll?.find((item) => {
                          //       return item?.value == distance;
                          //     })
                          // ModalTeamData?.find((item) => {
                          // return item?.value == playerValues?.team;
                          // })
                          // }\
                          value={playerValues?.team}
                          onChange={(e) =>
                            this.setState({
                              playerValues: {
                                ...playerValues,
                                team: e,
                              },
                            })
                          }
                          options={ModalTeamData}
                        />
                        {errorTeam ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorTeam}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Player;
