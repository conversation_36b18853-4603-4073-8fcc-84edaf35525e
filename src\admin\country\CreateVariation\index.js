import React, { Component, createRef } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
} from "@mui/material";
// import { variationFormModel } from "./form-constant";
// import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
// import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
// import { setValidation } from "../../../helpers/common";
import CancelIcon from "@mui/icons-material/Cancel";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import { Loader } from "../../../library/common/components";

class CreateVariation extends Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
      isEditMode: false,
      variationData: {},
      isInputModalOpen: false,
      isModalOpen: false,
      itemToDelete: null,
      variationToSend: "",
      addInput: "",
      idToSend: "",
    };
  }
  componentDidMount() {
    this.fetchAllVariation();
  }
  async fetchAllVariation() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      //   `${URLS.country}/variation/${this.props?.id}`
      `/countries/country/${this.props?.id}`
    );
    if (status === 200) {
      this.setState({ variationData: data?.result, isLoading: false });
    }
  }
  inputModal = (id, type, variation) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        idToSend: id,
        isEditMode: true,
        variationToSend: variation,
      });
    } else {
      this.setState({ isEditMode: false, addInput: "" });
    }
  };
  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };
  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  handleSave = async () => {
    let payload = {
      variation: this.state?.addInput,
    };

    this.setState({ isLoading: true, isEditMode: false });
    const { status } = await axiosInstance.post(
      `${URLS.country}/variation/${this.props?.id}`,
      payload
    );
    if (status === 200) {
      this.setState({ isLoading: false, isInputModalOpen: false });
      this.fetchAllVariation();
      //   this.setActionMessage(
      //     true,
      //     "Success",
      //     `Country variation Created Successfully`
      //   );
    }
  };
  handleUpdate = async () => {
    let payload = {
      variation: this.state?.variationToSend,
    };
    this.setState({ isLoading: true, isEditMode: true });
    const { status } = await axiosInstance.put(
      `${URLS.country}/variation/${this.state?.idToSend}`,
      payload
    );
    if (status === 200) {
      this.setState({ isLoading: false, isInputModalOpen: false });
      this.fetchAllVariation();
      //   this.setActionMessage(
      //     true,
      //     "Success",
      //     `Country variation Edited Successfully`
      //   );
    }
  };
  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.country}/variation/${this.state?.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllVariation();
        });
        // this.setActionMessage(
        //   true,
        //   "Success",
        //   "Country variation Deleted Successfully!"
        // );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  render() {
    var {
      values,
      messageBox,
      isLoading,
      isEditMode,
      variationData,
      isInputModalOpen,
      isModalOpen,
      variationToSend,
      addInput,
      idToSend,
    } = this.state;

    let variationDatas = variationData?.CountryVariations;

    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          {/* <Button
            className="modal-btn admin-btn-green"
            style={{ marginLeft: "auto", marginBottom: "10px" }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button> */}
          <Button
            variant="contained"
            style={{
              backgroundColor: "#4455C7",
              color: "#fff",
              borderRadius: "8px",
              textTransform: "capitalize",
              padding: "13px 24px 12px",
              marginLeft: "auto",
              marginBottom: "10px",
            }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button>
          <Grid item xs={12}>
            <Paper className="pageWrapper api-provider">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              {isLoading ? (
                <Box className="message">
                  <Loader />
                </Box>
              ) : variationDatas?.length > 0 ? (
                <>
                  {" "}
                  <TableContainer>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow className="table-rows listTable-Row">
                          <TableCell>Id</TableCell>
                          <TableCell>Variation</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {variationDatas?.map((data) => (
                          <TableRow>
                            <TableCell>{data?.id}</TableCell>
                            <TableCell>{data?.variation}</TableCell>
                            <TableCell>
                              <Button
                                style={{ minWidth: "0px" }}
                                onClick={this.inputModal(
                                  data?.id,
                                  "edit",
                                  data?.variation
                                )}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                style={{ minWidth: "0px" }}
                                onClick={this.setItemToDelete(data?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              ) : (
                <Box className="message">No Variation Data Avilable</Box>
              )}
            </Paper>
          </Grid>
        </Grid>
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={this.toggleInputModal}
        >
          {/* {isLoading && <Loader />} */}
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {!isEditMode
                ? "Create  Country variation"
                : "Edit Country variation"}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleInputModal}
            />
            {/* <Form
              values={values}
              model={variationFormModel}
              ref={this.formRef}
              onChange={this.handleChange}
            /> */}
            {/* <TextField
              className="textfield-tracks"
              style={{ width: "100%", color: "#D4D6D8" }}
              variant="outlined"
              color="primary"
              size="small"
              placeholder="variation"
              label="variation "
              value={isEditMode ? variationToSend : addInput}
              onChange={(e) => {
                isEditMode
                  ? this.setState({ variationToSend: e.target.value })
                  : this.setState({ addInput: e.target.value });
              }}
            /> */}
            <Grid item xs={12} className="runnerInfo">
              <Grid
                item
                xs={12}
                className="runnerInfo-text"
                style={{ display: "flex", flexDirection: "column" }}
              >
                <label className="modal-label">Country Variation</label>
                <TextField
                  className="textfield-tracks"
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder="Country variation"
                  value={isEditMode ? variationToSend : addInput}
                  onChange={(e) => {
                    isEditMode
                      ? this.setState({ variationToSend: e.target.value })
                      : this.setState({ addInput: e.target.value });
                  }}
                />
              </Grid>
            </Grid>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={!addInput}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleUpdate}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={!variationToSend}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
        <ShowModal
          isModalOpen={isModalOpen}
          onClose={this.toggleModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={this.deleteItem}
          onCancel={this.toggleModal}
        />
      </>
    );
  }
}

export default CreateVariation;
