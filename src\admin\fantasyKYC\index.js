import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  //   InputAdornment,
  //   IconButton,
} from "@mui/material";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
// import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import { config } from "../../helpers/config";
import Pagination from "@mui/material/Pagination";
// import DeleteIcon from "@mui/icons-material/Delete";
import Select, { components } from "react-select";
// import moment from "moment-timezone";
import "../tippingPremiumPrize/tippingPremiumPrize.scss";
import moment from "moment";

const statusOption = [
  {
    label: "Declined",
    value: "declined",
  },
  {
    label: "Expired",
    value: "expired",
  },
  {
    label: "Pending",
    value: "pending",
  },
  {
    label: "Verified",
    value: "verified",
  },
];

class FantasyKYC extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      FantasyKYCValues: {
        withdrawKYCStatus: null,
      },
      withdrawKYCList: [],
      withdrawKYCCount: 0,
      errorTransactionId: "",
      isSearch: "",
      selectedWithdrawID: "",
      selectedExternalStatus: null,
      errorCreate: "",
    };
  }

  componentDidMount() {
    this.fetchFantasyWithdraw(0);
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchFantasyWithdraw(offset);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchFantasyWithdraw(0);

      this.setState({
        offset: 0,
        currentPage: 1,
      });
    }
  }

  async fetchFantasyWithdraw(page) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/user/identity/list?limit=${rowPerPage}&offset=${page}`
      );
      if (status === 200) {
        this.setState({
          withdrawKYCList: data?.result,
          isLoading: false,
          withdrawKYCCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  //   handalValidate = () => {
  //     let { FantasyKYCValues } = this.state;
  //     let flag = true;
  //     if (
  //       FantasyKYCValues?.transactionId?.trim() === "" ||
  //       FantasyKYCValues?.transactionId === null
  //     ) {
  //       flag = false;
  //       this.setState({
  //         errorTransactionId: "This field is mandatory",
  //       });
  //     } else {
  //       this.setState({
  //         errorTransactionId: "",
  //       });
  //     }

  //     return flag;
  //   };

  handleUpdate = async () => {
    const { FantasyKYCValues, selectedWithdrawID, offset } = this.state;
    // if (this.handalValidate()) {
    this.setState({ isLoading: true, isEditMode: true });
    let payload = {
      verificationStatus: FantasyKYCValues?.withdrawKYCStatus,
      UserId: selectedWithdrawID?.UserId,
      notifyUser: true,
    };

    try {
      const { status, data } = await axiosInstance.put(
        `/user/identity/verification/${selectedWithdrawID?.id}`,
        payload
      );
      if (status === 200) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          errorCreate: "",
          FantasyKYCValues: {
            withdrawKYCStatus: null,
          },
        });
        this.fetchFantasyWithdraw(offset);
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          errorCreate: data?.message,
        });
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setState({
        isLoading: false,
        errorCreate: err?.response?.data?.message,
      });
    }
    // }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTransactionId: "",
      errorCreate: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        isEditMode: true,
        selectedWithdrawID: item,
      });
    } else {
      this.setState({
        FantasyKYCValues: {
          withdrawKYCStatus: null,
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  //   deleteItem = async () => {
  //     const { itemToDelete, offset } = this.state;

  //     try {
  //       const { status, data } = await axiosInstance.delete(
  //         `/fantasy/coins/delete/${itemToDelete}`
  //       );
  //       if (status === 200) {
  //         this.setState({ itemToDelete: null, isModalOpen: false }, () => {
  //           this.fetchFantasyWithdraw(offset);
  //         });
  //         this.setActionMessage(true, "Success", data?.message);
  //       }
  //     } catch (err) {
  //       this.setActionMessage(true, "Error", err?.response?.data?.message);
  //     }
  //   };

  DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        {/* <SelectIndicator /> */}
      </components.DropdownIndicator>
    );
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= rowPerPage) {
        this.setState({
          offset: offset - rowPerPage,
          currentPage: currentPage - 1,
        });
      }
    } else {
      this.setState({
        offset: offset + rowPerPage,
        currentPage: currentPage + 1,
      });
    }
  };

  openPreview = (urls) => {
    const url = config?.mediaUrl + urls;

    window.open(url, "_blank");
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      FantasyKYCValues,
      withdrawKYCList,
      withdrawKYCCount,
      //   errorTransactionId,
      //   isSearch,
      //   selectedExternalStatus,
      errorCreate,
    } = this.state;
    const pageNumbers = [];

    if (withdrawKYCCount > 0) {
      for (let i = 1; i <= Math.ceil(withdrawKYCCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  Fantasy Withdraw KYC
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Fantasy Withdraw KYC
                </Typography>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                {/* <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button> */}
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && withdrawKYCList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && withdrawKYCList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>ID</TableCell>
                        <TableCell>User</TableCell>
                        <TableCell>Email</TableCell>
                        <TableCell>Documents</TableCell>
                        <TableCell>verification Date</TableCell>
                        <TableCell>Score</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>

                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {withdrawKYCList?.map((item, index) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={index}
                          >
                            <TableCell> {item?.id} </TableCell>
                            <TableCell style={{ textTransform: "capitalize" }}>
                              {item?.User?.firstName + item?.User?.lastName}
                            </TableCell>
                            <TableCell>{item?.User?.username}</TableCell>
                            <TableCell>
                              {item?.identificationDocuments?.map(
                                (doc, index) => {
                                  return (
                                    <>
                                      <div
                                        key={index}
                                        onClick={() =>
                                          this.openPreview(doc?.fileName)
                                        }
                                      >
                                        <Typography
                                          style={{
                                            color: "#4455C7",
                                            cursor: "pointer",
                                          }}
                                        >
                                          {doc?.type}
                                        </Typography>
                                      </div>
                                    </>
                                  );
                                }
                              )}
                            </TableCell>
                            <TableCell>
                              {item?.verificationDate
                                ? moment(item?.verificationDate).format(
                                    "DD-MM-YYYY"
                                  )
                                : ""}
                            </TableCell>
                            <TableCell>{item?.requirementScore}</TableCell>
                            <TableCell>{item?.verificationStatus}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn edit-btn"
                              >
                                approved
                              </Button>
                              {/* <Button
                                onClick={this.setItemToDelete(item?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button> */}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                withdrawKYCCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Withdraw KYC"
                    : "Edit Withdraw KYC"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Status</label>
                        <Select
                          className="React teamsport-select event-Tournament-select premium-key-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Status"
                          value={
                            FantasyKYCValues?.withdrawKYCStatus &&
                            statusOption?.find((item) => {
                              return (
                                item?.value ===
                                FantasyKYCValues?.withdrawKYCStatus
                              );
                            })
                          }
                          options={statusOption}
                          onChange={(e) => {
                            this.setState({
                              FantasyKYCValues: {
                                ...FantasyKYCValues,
                                withdrawKYCStatus: e?.value,
                              },
                            });
                          }}
                        />
                      </Grid>

                      <span style={{ fontSize: "12px", color: "red" }}>
                        {errorCreate ? errorCreate : ""}
                      </span>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default FantasyKYC;
