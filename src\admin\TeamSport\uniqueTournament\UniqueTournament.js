import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../teamsport.scss";

class UniqueTournament extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      uniqueTournamentValues: {
        uniqueTournamentName: "",
        rapidId: "",
        id: "",
      },
      uniqueTournamentlist: [],
      uniqueTournamentCount: 0,
      errorRequire: "",
      search: "",
    };
  }

  componentDidMount() {
    this.fetchAllUniqueTournament(0, "");
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllUniqueTournament(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllUniqueTournament(0, "");
      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
      });
    }
  }

  async fetchAllUniqueTournament(page, searchvalue) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    const Cricket = this.props.match.path?.includes("cricket");
    const RugbyLeague = this.props.match.path?.includes("rugbyleague");
    const RugbyUnion = this.props.match.path?.includes("rugbyunion");
    const BasketBall = this.props.match.path?.includes("basketball");
    const AFL = this.props.match.path?.includes("afl");
    const AustralianRules = this.props.match.path?.includes("australianrules");
    const Golf = this.props.match.path?.includes("golf");
    const Tennis = this.props.match.path?.includes("tennis");
    const Baseball = this.props.match.path?.includes("baseball");
    const IceHockey = this.props.match.path?.includes("icehockey");
    const Boxing = this.props.match.path?.includes("boxing");
    const MMA = this.props.match.path?.includes("mma");
    const Soccer = this.props.match.path?.includes("soccer");

    const SportId = Cricket
      ? 4
      : RugbyLeague
      ? 12
      : RugbyUnion
      ? 13
      : BasketBall
      ? 10
      : AFL
      ? 15
      : AustralianRules
      ? 9
      : Golf
      ? 16
      : Tennis
      ? 7
      : Baseball
      ? 11
      : IceHockey
      ? 17
      : Boxing
      ? 6
      : MMA
      ? 5
      : Soccer
      ? 8
      : 14;
    try {
      let passApi = `allsport/unique-tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}&SportId=${SportId}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          uniqueTournamentlist: data?.result?.rows,
          isLoading: false,
          uniqueTournamentCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }
  handalValidate = () => {
    let { uniqueTournamentValues } = this.state;

    let flag = true;
    if (uniqueTournamentValues?.uniqueTournamentName?.trim() === "") {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    const Cricket = this.props.match.path?.includes("cricket");
    const RugbyLeague = this.props.match.path?.includes("rugbyleague");
    const RugbyUnion = this.props.match.path?.includes("rugbyunion");
    const BasketBall = this.props.match.path?.includes("basketball");
    const AFL = this.props.match.path?.includes("afl");
    const AustralianRules = this.props.match.path?.includes("australianrules");
    const Golf = this.props.match.path?.includes("golf");
    const Tennis = this.props.match.path?.includes("tennis");
    const Baseball = this.props.match.path?.includes("baseball");
    const IceHockey = this.props.match.path?.includes("icehockey");
    const Boxing = this.props.match.path?.includes("boxing");
    const MMA = this.props.match.path?.includes("mma");
    const Soccer = this.props.match.path?.includes("soccer");

    const SportId = Cricket
      ? 4
      : RugbyLeague
      ? 12
      : RugbyUnion
      ? 13
      : BasketBall
      ? 10
      : AFL
      ? 15
      : AustralianRules
      ? 9
      : Golf
      ? 16
      : Tennis
      ? 7
      : Baseball
      ? 11
      : IceHockey
      ? 17
      : Boxing
      ? 6
      : MMA
      ? 5
      : Soccer
      ? 8
      : 14;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        name: this.state?.uniqueTournamentValues?.uniqueTournamentName,
        SportId: SportId,
      };
      try {
        const { status, data } = await axiosInstance.post(
          `/allsport/unique-tournament`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllUniqueTournament(this.state.offset, this?.state?.search);
          this.setActionMessage(
            true,
            "Success",
            "Unique Tournament Created Successfully"
          );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", "An error occurred");
      }
    }
  };

  handleUpdate = async () => {
    const Cricket = this.props.match.path?.includes("cricket");
    const RugbyLeague = this.props.match.path?.includes("rugbyleague");
    const RugbyUnion = this.props.match.path?.includes("rugbyunion");
    const BasketBall = this.props.match.path?.includes("basketball");
    const AFL = this.props.match.path?.includes("afl");
    const AustralianRules = this.props.match.path?.includes("australianrules");
    const Golf = this.props.match.path?.includes("golf");
    const Tennis = this.props.match.path?.includes("tennis");
    const Baseball = this.props.match.path?.includes("baseball");
    const IceHockey = this.props.match.path?.includes("icehockey");
    const Boxing = this.props.match.path?.includes("boxing");
    const MMA = this.props.match.path?.includes("mma");
    const Soccer = this.props.match.path?.includes("soccer");
    const SportId = Cricket
      ? 4
      : RugbyLeague
      ? 12
      : RugbyUnion
      ? 13
      : BasketBall
      ? 10
      : AFL
      ? 15
      : AustralianRules
      ? 9
      : Golf
      ? 16
      : Tennis
      ? 7
      : Baseball
      ? 11
      : IceHockey
      ? 17
      : Boxing
      ? 6
      : MMA
      ? 5
      : Soccer
      ? 8
      : 14;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        name: this.state?.uniqueTournamentValues?.uniqueTournamentName,
        SportId: SportId,
      };
      try {
        const { status, data } = await axiosInstance.put(
          `/allsport/unique-tournament/${this.state.uniqueTournamentValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllUniqueTournament(this.state.offset, this?.state?.search);
          this.setActionMessage(
            true,
            "Success",
            "Unique Tournament Updated Successfully"
          );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", "An error occurred");
      }
    }
  };

  //   async fetchAllSportType() {
  //     const { status, data } = await axiosInstance.get(URLS.sportType);
  //     if (status === 200) {
  //       this.setState({ allSportsType: data.result });
  //     }
  //   }

  //   getSportType = (id) => {
  //     let { allSportsType } = this.state;
  //     let sportType = allSportsType
  //       .filter((obj) => obj.id === id)
  //       .map((object) => object.sportType);
  //     return sportType;
  //   };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        uniqueTournamentValues: {
          uniqueTournamentName: item?.name,
          rapidId:
            this.props.match.path?.includes("cricket") ||
            this.props.match.path?.includes("soccer")
              ? item?.Ccd
              : item?.rapidId,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        uniqueTournamentValues: {
          uniqueTournamentName: "",
          rapidId: "",
          id: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const Cricket = this.props.match.path?.includes("cricket");
    const RugbyLeague = this.props.match.path?.includes("rugbyleague");
    const RugbyUnion = this.props.match.path?.includes("rugbyunion");
    const BasketBall = this.props.match.path?.includes("basketball");
    const AFL = this.props.match.path?.includes("afl");
    const AustralianRules = this.props.match.path?.includes("australianrules");
    const Golf = this.props.match.path?.includes("golf");
    const Tennis = this.props.match.path?.includes("tennis");
    const Baseball = this.props.match.path?.includes("baseball");
    const IceHockey = this.props.match.path?.includes("icehockey");
    const Boxing = this.props.match.path?.includes("boxing");
    const MMA = this.props.match.path?.includes("mma");
    const Soccer = this.props.match.path?.includes("soccer");
    const sportsEndPoints = Cricket
      ? "crickets"
      : RugbyLeague
      ? `rls`
      : RugbyUnion
      ? `rls`
      : BasketBall
      ? `nba`
      : AFL
      ? `afl`
      : AustralianRules
      ? `ar`
      : Golf
      ? `golf`
      : Tennis
      ? `tennis`
      : Baseball
      ? `baseball`
      : IceHockey
      ? `icehockey`
      : Boxing
      ? `boxing`
      : MMA
      ? `mma`
      : Soccer
      ? `soccer`
      : `rls`;
    const SportId = Cricket
      ? 4
      : RugbyLeague
      ? 12
      : RugbyUnion
      ? 13
      : BasketBall
      ? 10
      : AFL
      ? 15
      : AustralianRules
      ? 9
      : Golf
      ? 16
      : Tennis
      ? 7
      : Baseball
      ? 11
      : IceHockey
      ? 17
      : Boxing
      ? 6
      : MMA
      ? 5
      : Soccer
      ? 8
      : 14;
    try {
      const passApi = `/allsport/unique-tournament/${this.state.itemToDelete}?SportId=${SportId}`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllUniqueTournament(this.state.offset, this?.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          "Unique Tournament Deleted Successfully"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, uniqueTournamentlist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      uniqueTournamentValues,
      uniqueTournamentlist,
      uniqueTournamentCount,
      errorRequire,
      search,
    } = this.state;
    const pageNumbers = [];

    if (uniqueTournamentCount > 0) {
      for (let i = 1; i <= Math.ceil(uniqueTournamentCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Unique Tournament</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  Unique Tournament
                </Typography>
              </Grid>

              <Grid item xs={8} className="admin-filter-wrap">
                {/* <SelectBox
                  onChange={(e) => this.setState({ sportType: e.target.value })}
                  style={{ width: "60%" }}
                >
                  <option value="">Select Sport Type</option>
                  {allSportsType?.length > 0 &&
                    allSportsType?.map((obj, i) => (
                      <option key={i} value={obj?.id}>
                        {obj?.sportType}
                      </option>
                    ))}
                </SelectBox> */}
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllUniqueTournament(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && uniqueTournamentlist?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && uniqueTournamentlist?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell>DID</TableCell>
                      <TableCell style={{ width: "25%" }}>
                        Unique Tournament Name
                      </TableCell>
                      {/* <TableCell style={{ width: "25%" }}>
                          variation
                        </TableCell> */}
                      {/* <TableCell style={{ width: "25%" }}>variation</TableCell> */}
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {uniqueTournamentlist?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.name}</TableCell>
                          <TableCell>
                            <Button
                              style={{ cursor: "pointer" }}
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              style={{ cursor: "pointer" }}
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                              className={
                                uniqueTournamentlist.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                uniqueTournamentlist.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              uniqueTournamentCount / rowPerPage > 1
                                ? false
                                : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                              className={
                                uniqueTournamentlist.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                uniqueTournamentlist.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?  This Unique tournament might have association with the Tournaments."
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Unique Tournament"
                    : "Edit Unique Tournament"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Unique Tournament
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Unique Tournament"
                          value={uniqueTournamentValues?.uniqueTournamentName}
                          onChange={(e) =>
                            this.setState({
                              uniqueTournamentValues: {
                                ...uniqueTournamentValues,
                                uniqueTournamentName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorRequire ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRequire}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default UniqueTournament;
