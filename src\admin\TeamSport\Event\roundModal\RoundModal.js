import React, { Component, createRef } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
} from "@mui/material";
// import { variationFormModel } from "./form-constant";
import Form from "../../../../library/common/components/Form";
import ButtonComponent from "../../../../library/common/components/Button";
import ActionMessage from "../../../../library/common/components/ActionMessage";
import { URLS } from "../../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../../library/utilities";
import axiosInstance from "../../../../helpers/Axios";
import { setValidation } from "../../../../helpers/common";
import CancelIcon from "@mui/icons-material/Cancel";
import ShowModal from "../../../../components/common/ShowModal/ShowModal";
import { Loader } from "../../../../library/common/components";
import moment from "moment";
import Select from "react-select";
import { ReactComponent as LeftArrow } from "../../../../images/left-arrow.svg";
import { ReactComponent as RightArrow } from "../../../../images/right-arrow.svg";

class RoundModal extends Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
      isEditMode: false,
      isInputModalOpen: false,
      isModalOpen: false,
      itemToDelete: null,
      stepperCount: 0,
    };
  }
  componentDidMount() {}

  handleSave = async () => {
    const { stepperCount } = this.state;
    const passApi = `allsport/round/event`;

    const payload = {
      SportId: this.props?.pathName?.includes("cricket")
        ? 4
        : this.props?.pathName?.includes("rugbyleague")
        ? 12
        : this.props?.pathName?.includes("rugbyunion")
        ? 13
        : this.props?.pathName?.includes("basketball")
        ? 10
        : this.props?.pathName?.includes("afl")
        ? 15
        : this.props?.pathName?.includes("australianrules")
        ? 9
        : this.props?.pathName?.includes("golf")
        ? 16
        : this.props?.pathName?.includes("tennis")
        ? 7
        : this.props?.pathName?.includes("baseball")
        ? 11
        : this.props?.pathName?.includes("icehockey")
        ? 17
        : this.props?.pathName?.includes("boxing")
        ? 6
        : this.props?.pathName?.includes("mma")
        ? 5
        : this.props?.pathName?.includes("soccer")
        ? 8
        : 14,
      round: stepperCount,
      eventIds: this.props.checkBoxValues,
    };
    try {
      const { status, data } = await axiosInstance.put(`${passApi}`, payload);
      if (status === 200) {
        this.props.toggleRoundModal();
        this.props.resetModal();
        this.props.listingFunction();
        this.props.setActionMessage(
          true,
          "Success",
          "Events Added to Round Successfully!"
        );
      } else {
      }
    } catch (err) {}
  };

  deleteItem = async () => {};
  handleStepper = (value) => {
    const { stepperCount } = this.state;
    if (stepperCount >= 0) {
      return value === "left"
        ? stepperCount > 0
          ? this.setState({ stepperCount: stepperCount - 1 })
          : ""
        : stepperCount !== null
        ? this.setState({ stepperCount: stepperCount + 1 })
        : this.setState({ stepperCount: 0 });
    }
  };
  render() {
    var { stepperCount } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            <Grid container>
              <Grid
                item
                xs={12}
                className="stepper-input-wrap"
                style={{ marginBottom: "15px" }}
              >
                <label className="modal-label"> Round </label>
                <Box className="stepper-input" style={{ width: "100%" }}>
                  <Button
                    className="stepper-input__button"
                    onClick={(e) => {
                      e.preventDefault();
                      this.handleStepper("left");
                    }}
                    disabled={stepperCount == 0}
                  >
                    <LeftArrow />
                  </Button>

                  <div className="stepper-input__content">
                    Round {stepperCount ?? ""}
                  </div>

                  <Button
                    className="stepper-input__button"
                    onClick={(e) => {
                      e.preventDefault();
                      this.handleStepper("right");
                    }}
                  >
                    <RightArrow />
                  </Button>
                </Box>
              </Grid>
              <TableContainer>
                <Table
                  className="listTable"
                  aria-label="simple table"
                  style={{ minWidth: "max-content" }}
                >
                  <TableHead className="tableHead-row">
                    <TableRow className="table-rows listTable-Row">
                      <TableCell>Id</TableCell>
                      <TableCell>Event</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {this.props.selectedCheckBoxEvent?.length > 0 ? (
                      this.props.selectedCheckBoxEvent?.map((data) => (
                        <TableRow>
                          <TableCell>{data?.id}</TableCell>
                          <TableCell>{data?.eventName}</TableCell>
                          <TableCell>
                            <Button
                              onClick={() =>
                                this.props.deleteEventFromRound(data?.id)
                              }
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        {" "}
                        <TableCell
                          colSpan={100}
                          style={{ textAlign: "center" }}
                        >
                          No Events Are Selected
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <Grid container>
                <Grid item xs={12}>
                  <div style={{ marginTop: "20px", display: "flex" }}>
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={"Add To Round"}
                      style={{ minWidth: "auto" }}
                      // disabled={stepperCount == 0}
                    />
                  </div>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </>
    );
  }
}

export default RoundModal;
