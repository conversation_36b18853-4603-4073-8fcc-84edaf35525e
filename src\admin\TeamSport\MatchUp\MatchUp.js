import React, { Component } from "react";
import { <PERSON>rid, Typo<PERSON>, <PERSON>, <PERSON>readcrum<PERSON>, Button } from "@mui/material";
import { Link } from "react-router-dom";
import axiosInstance from "../../../helpers/Axios";
import moment from "moment";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import DateFnsUtils from "@date-io/date-fns";
import ActionMessage from "../../../library/common/components/ActionMessage";

class MatchUp extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: null,
      endDate: null,
      isLoading: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      errorStartDate: "",
      errorEndDate: "",
    };
  }
  handleStartDate = (date) => {
    this.setState({ startDate: date });
  };
  handleEndDate = (date) => {
    this.setState({ endDate: date });
  };
  handleValidate = () => {
    let { startDate, endDate } = this.state;
    let flag = true;
    if (!startDate) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    // if (!this.props?.match?.path?.includes("cricket")) {
    //   if (!endDate) {
    //     flag = false;
    //     this.setState({
    //       errorEndDate: "This field is mandatory",
    //       isLoading: false,
    //     });
    //   } else {
    //     this.setState({
    //       errorEndDate: "",
    //     });
    //   }
    // }
    return flag;
  };

  handlePastFixture = async () => {
    if (this.handleValidate()) {
      this.setState({ isLoading: true });
      const { startDate, endDate } = this.state;
      let payload = {
        date: moment(startDate).format("YYYY-MM-DD"),
        sportId: this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("baseball")
          ? 11
          : 17,
      };
      try {
        const passApi = this.props?.match?.path?.includes("afl")
          ? "afl/sync/matchUp"
          : this.props?.match?.path?.includes("baseball")
          ? "baseball/sync/matchUp"
          : this.props?.match?.path?.includes("basketball")
          ? "nba/sync/matchUp"
          : "icehockey/sync/matchUp";
        const { status, data } = await axiosInstance.post(passApi, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            startDate: null,
            endDate: null,
          });
          this.setActionMessage(
            true,
            "Success",
            data?.result?.status
              ? "Future Fixture data import Process started"
              : "Right now Future Fixture process already in progress of last selected date so please try again once last process it completed"
          );
        } else {
          this.setState({
            isLoading: false,
            startDate: null,
            endDate: null,
          });
        }
      } catch (error) {
        this.setState({
          isLoading: false,
          startDate: null,
          endDate: null,
        });
      }
    }
  };
  // setActionMessage = (display = false, type = "", message = "") => {
  //   let setActionMessage = {
  //     display: display,
  //     type: type,
  //     message: message,
  //   };
  //   this.setState({ messageBox: setActionMessage });
  // };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match.path !== this.props.match.path) {
      this.setState({
        startDate: null,
        endDate: null,
        errorStartDate: "",
        errorEndDate: "",
      });
    }
  }
  render() {
    const { startDate, endDate, messageBox, errorStartDate, errorEndDate } =
      this.state;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : "Ice Hockey"}
                </Link>
                <Typography className="active_p">Matchup import</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  Matchup import
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
                className="admin-fixture-wrap"
              >
                <Grid item xs={3}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <Grid container>
                      <DesktopDatePicker
                        autoOk
                        // disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        placeholder={"Select Date"}
                        margin="normal"
                        id="date-picker-inline"
                        inputVariant="outlined"
                        value={
                          startDate
                            ? typeof startDate === "string"
                              ? parseISO(startDate)
                              : startDate
                            : null
                        }
                        onChange={(e) => this.handleStartDate(e)}
                        KeyboardButtonProps={{
                          "aria-label": "change date",
                        }}
                        className="date-picker-fixture"
                      />
                    </Grid>
                  </LocalizationProvider>
                  {errorStartDate ? (
                    <p className="errorText" style={{ margin: "0px 0 0 0" }}>
                      {errorStartDate}
                    </p>
                  ) : (
                    ""
                  )}
                </Grid>
                <div>
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "10px 15px",
                      marginTop: "5px",
                    }}
                    onClick={() => this.handlePastFixture()}
                  >
                    import
                  </Button>
                </div>
              </Grid>
            </Grid>
            {/* <Grid container direction="row" alignItems="center">
              <Grid item xs={12} style={{ textAlign: "end" }}>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "10px 15px",
                    marginTop: "5px",
                  }}
                  // onClick={() => this.handlePastFixture()}
                >
                  Clear
                </Button>
              </Grid>
            </Grid> */}
          </Grid>
        </Grid>
      </>
    );
  }
}

export default MatchUp;
