import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TableHead,
  TableRow,
  TableContainer,
  Table,
  TableCell,
  TableBody,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import moment from "moment-timezone";
import { URLS } from "../../../library/common/constants";
import { Loader } from "../../../library/common/components";
import axiosInstance from "../../../helpers/Axios";
import TabHorses from "../../../images/sport_icons/tab_horse.svg";
import TabGreyhounds from "../../../images/sport_icons/tab_greyhounds.svg";
import TabHarness from "../../../images/sport_icons/tab_harness.svg";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import bet365 from "../../../images/logo/bet365.png";
import Ladbrokes from "../../../images/logo/ladbrokes.png";
import Betstar from "../../../images/logo/betstar.png";
import Bookmaker from "../../../images/logo/bookmaker.png";
import Neds from "../../../images/logo/neds.png";
import Playup from "../../../images/logo/playup_.png";
// import palmerbet from "../../../images/logo/palmerbet.png";
// import vicbet from "../../../images/logo/vicebet.png";
import UniBet from "../../../images/logo/unibet.svg";
// import winningedge from "../../../images/logo/winningedge.png";
// import realbookie from "../../../images/logo/realbookie.png";
import DraftStars from "../../../images/logo/draftstart-thumb.svg";
import TopSport from "../../../images/logo/top-sport-thumb.svg";
import BetFair from "../../../images/logo/betfair-thumb.svg";
import BlueBet from "../../../images/logo/BlueBet.png";
import BoomBet from "../../../images/logo/BoomBet.png";
import SouthernCrossBet from "../../../images/logo/SouthernCrossBet.png";
import Betr from "../../../images/logo/Betr-long.png";
import PointsBet from "../../../images/logo/pointsbet-long.png";
import SportsBet from "../../../images/logo/sportbet-long.jpg";
import TAB from "../../../images/logo/tab-long.jpg";

import { config } from "../../../helpers/config";
import Pagination from "@mui/material/Pagination";
import "./DashboardCollapase.scss";

function DashboardCollapase({
  selectedDate,
  selectedRaceType,
  selectedCountryType,
  isRefetch,
}) {
  const [RaceType, setRaceType] = useState([
    {
      id: 1,
      title: "Horses",
      icon: TabHorses,
    },
    {
      id: 3,
      title: "Greyhounds",
      icon: TabGreyhounds,
    },
    {
      id: 2,
      title: "Harness",
      icon: TabHarness,
    },
  ]);
  let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const TeamSportType = window.location?.pathname;
  const [eventList, seteventList] = useState([]);
  const [isYesterday, setisYesterday] = useState(false);
  const [isTRaceeventLoading, setisisTRaceeventLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [eventCount, setEventCount] = useState(0);
  const [offset, setOffset] = useState(0);
  const [pageHeadingData, setPageHeadingData] = useState([]);
  const [BookkeeperData, setBookKeeperData] = useState([]);

  const pageNumbers = [];
  if (eventCount > 0) {
    for (let i = 1; i <= Math.ceil(eventCount / 20); i++) {
      pageNumbers.push(i);
    }
  }
  const fetchAllEvents = async (page) => {
    setisisTRaceeventLoading(true);
    let date = selectedDate;
    try {
      let passApi = TeamSportType?.includes("cricket")
        ? `/crickets/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("rugbyleague")
        ? `/rls/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}&SportId=12`
        : TeamSportType?.includes("rugbyunion")
        ? `/rls/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}&SportId=13`
        : TeamSportType?.includes("basketball")
        ? `/nba/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("afl")
        ? `/afl/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("australianrules")
        ? `/ar/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("golf")
        ? `/golf/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("tennis")
        ? `/tennis/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("baseball")
        ? `/baseball/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("icehockey")
        ? `/icehockey/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("boxing")
        ? `/boxing/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("mma")
        ? `/mma/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : TeamSportType?.includes("soccer")
        ? `/soccer/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}`
        : `/rls/admin/oddcheck?limit=20&offset=${page}&startDate=${selectedDate}&endDate=${selectedDate}&timezone=${timezone}&SportId=14`;
      const { status, data } = await axiosInstance.get(passApi);

      if (status === 200) {
        seteventList(data?.result?.rows);
        setEventCount(data?.result?.count);
        setisisTRaceeventLoading(false);
      } else {
        setisisTRaceeventLoading(false);
      }
    } catch (error) {
      setisisTRaceeventLoading(false);
    }
  };

  const updateFixture = async (eventId, providerId, statusId) => {
    let params = {
      eventId: eventId,
      providerId: providerId,
      status: statusId,
    };
    try {
      const { status } = await axiosInstance.post(URLS.updateFixtures, params);
      if (status === 200) {
        setTimeout(() => {
          fetchAllEvents(offset);
        }, 500);
      }
    } catch (err) {}
  };

  useEffect(() => {
    fetchAllEvents(offset);
    fetchBookKeeper();
    if (
      selectedDate == moment().utc().subtract(1, "days").format("YYYY-MM-DD")
    ) {
      setisYesterday(true);
    } else {
      setisYesterday(false);
    }
  }, [selectedDate, isRefetch]);

  useEffect(() => {
    fetchTableHeading();
    fetchAllEvents(0);
    setCurrentPage(1);
    setOffset(0);
    seteventList([]);
  }, [TeamSportType]);

  const handleCellColor = (data, BookKeeperId) => {
    let teamSportsBetOffers = TeamSportType?.includes("cricket")
      ? data?.CricketBetOffers
      : TeamSportType?.includes("basketball")
      ? data?.NBABetOffers
      : TeamSportType?.includes("afl")
      ? data?.AFLBetOffers
      : TeamSportType?.includes("australianrules")
      ? data?.ARBetOffers
      : TeamSportType?.includes("golf")
      ? data?.GolfBetOffers
      : TeamSportType?.includes("tennis")
      ? data?.TennisBetOffers
      : TeamSportType?.includes("baseball")
      ? data?.BaseballBetOffers
      : TeamSportType?.includes("icehockey")
      ? data?.IceHockeyBetOffers
      : TeamSportType?.includes("boxing")
      ? data?.BoxingBetOffers
      : TeamSportType?.includes("mma")
      ? data?.MMABetOffers
      : TeamSportType?.includes("soccer")
      ? data?.SoccerBetOffers
      : data?.RLBetOffers;
    let teamSportsOdds = TeamSportType?.includes("cricket")
      ? data?.CricketBetOffers?.[0]?.CricketOdds
      : TeamSportType?.includes("basketball")
      ? data?.NBABetOffers?.[0]?.NBAOdds
      : TeamSportType?.includes("afl")
      ? data?.AFLBetOffers?.[0]?.AFLOdds
      : TeamSportType?.includes("australianrules")
      ? data?.ARBetOffers?.[0]?.AROdds
      : TeamSportType?.includes("golf")
      ? data?.GolfBetOffers?.[0]?.GolfOdds
      : TeamSportType?.includes("tennis")
      ? data?.TennisBetOffers?.[0]?.TennisOdds
      : TeamSportType?.includes("baseball")
      ? data?.BaseballBetOffers?.[0]?.BaseballOdds
      : TeamSportType?.includes("icehockey")
      ? data?.IceHockeyBetOffers?.[0]?.IceHockeyOdds
      : TeamSportType?.includes("boxing")
      ? data?.BoxingBetOffers?.[0]?.BoxingOdds
      : TeamSportType?.includes("mma")
      ? data?.MMABetOffers?.[0]?.MMAOdds
      : TeamSportType?.includes("soccer")
      ? data?.SoccerBetOffers?.[0]?.SoccerOdds
      : data?.RLBetOffers?.[0]?.RLOdds;
    if (teamSportsBetOffers?.length > 0) {
      const raceCell = teamSportsOdds?.filter((obj) => {
        return obj?.BookKeeperId === BookKeeperId;
      });
      if (raceCell?.length > 0) {
        return "fixture";
      } else {
        return "notfixture";
      }
    } else {
      return "notfixture";
    }
  };

  const handleCellColorChange = (race, BookKeeperId) => {
    // const raceCell = race?.providers?.filter((obj) => {
    //   return obj?.providerId === BookKeeperId;
    // });
    // let eventId = raceCell?.[0]?.eventId;
    // if (raceCell?.[0]?.status === 2 || raceCell?.[0]?.status === 3) {
    //   if (raceCell?.[0]?.status === 2) {
    //     updateFixture(eventId, BookKeeperId, 3);
    //   } else {
    //     updateFixture(eventId, BookKeeperId, 2);
    //   }
    // }
  };

  const fetchBookKeeper = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/apiProviders/bookkeeperproviders`
      );
      if (status === 200) {
        setBookKeeperData(data?.result);
      } else {
      }
    } catch (err) {}
  };
  const oddsicon = (BookKeeperId) => {
    let icon = BookkeeperData?.filter(
      (obj) => obj?.BookKeeperId === BookKeeperId
    );
    let iconData = icon?.[0]?.BookKeeper;
    let longlogo = [3, 4, 8, 10, 15];
    return (
      <img
        className="square-bookmaker"
        src={
          iconData?.small_logo?.includes("uploads")
            ? config.mediaUrl + iconData?.small_logo
            : iconData?.small_logo
        }
        alt="Bookkeeper"
      />
    );
  };

  const fetchTableHeading = async () => {
    try {
      let SportId = TeamSportType?.includes("cricket")
        ? 4
        : TeamSportType?.includes("rugbyleague")
        ? 12
        : TeamSportType?.includes("rugbyunion")
        ? 13
        : TeamSportType?.includes("basketball")
        ? 10
        : TeamSportType?.includes("afl")
        ? 15
        : TeamSportType?.includes("australianrules")
        ? 9
        : TeamSportType?.includes("golf")
        ? 16
        : TeamSportType?.includes("tennis")
        ? 7
        : TeamSportType?.includes("baseball")
        ? 11
        : TeamSportType?.includes("icehockey")
        ? 17
        : TeamSportType?.includes("boxing")
        ? 6
        : TeamSportType?.includes("mma")
        ? 5
        : TeamSportType?.includes("soccer")
        ? 8
        : 14;
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookkeeperproviders?SportId=${SportId}`
      );
      if (status === 200) {
        setPageHeadingData(data?.result);
      } else {
      }
    } catch (err) {}
  };

  const handlePaginationClick = (event, page) => {
    setCurrentPage(Number(page));
    setOffset((Number(page) - 1) * 20);
    fetchAllEvents((Number(page) - 1) * 20);
  };
  return (
    <>
      {isTRaceeventLoading ? (
        <Box style={{ paddingTop: "20px" }}>
          <Loader />
        </Box>
      ) : (
        <Box className="fixture-colleps-wrap">
          <Box className="racing-colleps">
            <Box className="accordion_details">
              <TableContainer className="fixture-table-wrap">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell className="rtable-hc1 teamsport-dashboard-head"></TableCell>
                      {pageHeadingData?.map((item) => {
                        return (
                          <>
                            <TableCell className="rt-thead">
                              {oddsicon(item?.BookKeeperId)}
                            </TableCell>
                          </>
                        );
                      })}
                    </TableRow>
                  </TableHead>
                </Table>
              </TableContainer>

              <Box className="fixture-wrape">
                <Typography className="country-title">Events</Typography>
                <TableContainer className="fixture-table-wrap">
                  <Table>
                    <TableBody>
                      <TableRow></TableRow>
                      {!isTRaceeventLoading &&
                        (eventList?.length > 0 ? (
                          eventList?.map((item, index) => {
                            return (
                              <>
                                <TableRow>
                                  <TableCell
                                    component="th"
                                    className="teamsports-dashboard-th"
                                  >
                                    {/* {race?.track?.countryObj?.country_flag?.includes(
                                                "uploads"
                                              ) ? (
                                                <img
                                                  src={
                                                    config.mediaUrl +
                                                    race?.track
                                                      ?.countryObj
                                                      ?.country_flag
                                                  }
                                                  alt="Flag"
                                                  className="flag-icon"
                                                />
                                              ) : (
                                                <img
                                                  src={
                                                    race?.track
                                                      ?.countryObj
                                                      ?.country_flag
                                                  }
                                                  alt="Flag"
                                                  className="flag-icon"
                                                />
                                              )} */}
                                    <Typography variant="h6">
                                      {item?.eventName}
                                    </Typography>
                                  </TableCell>
                                  {pageHeadingData?.map((obj) => {
                                    return (
                                      <>
                                        <TableCell
                                          className={handleCellColor(
                                            item,
                                            obj?.BookKeeperId
                                          )}
                                          onClick={() => {
                                            handleCellColorChange(
                                              item,
                                              obj?.BookKeeperId
                                            );
                                          }}
                                        ></TableCell>
                                      </>
                                    );
                                  })}
                                </TableRow>
                              </>
                            );
                          })
                        ) : (
                          <TableRow
                            style={{
                              border: "transparent",
                              height: "1px",
                            }}
                          >
                            <TableCell
                              style={{
                                textAlign: "center",
                              }}
                              colSpan={21}
                            >
                              No Data Available
                            </TableCell>
                          </TableRow>
                        ))}
                      {eventList?.length > 0 && (
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                              className={
                                categorylist.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                categorylist.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={eventCount / 20 > 1 ? false : true}
                                page={currentPage}
                                onChange={handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />
                              {/* <button
                              className={
                                categorylist.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                categorylist.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </Box>
          </Box>
        </Box>
      )}
    </>
  );
}

export default DashboardCollapase;
