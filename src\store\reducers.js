import { combineReducers } from "redux";
import CommonReducer from "./common/reducers/common.reducers";
import AuthReducer from "../library/common/reducers/AuthReducer";
import RegisterReducer from "../components/register/RegisterReducer";
import MetaReducer from "../library/common/reducers/MetaReducer";
import BlogsReducer from "./common/reducers/blogs.reducers";
import SportsReducer from "./common/reducers/sports.reducers";
import BetsReducer from "./common/reducers/bets.reducers";
import ReviewReducer from "./common/reducers/review.reducers";
import ContactReducer from "./common/reducers/contact.reducers";

const rootReducers = combineReducers({
  common: CommonReducer,
  authReducer: AuthReducer,
  registerReducer: RegisterReducer,
  metaReducer: MetaReducer,
  blogs: BlogsReducer,
  sports: SportsReducer,
  bets: BetsReducer,
  reviewReducer: ReviewReducer,
  contactReducer: ContactReducer,
});

export default rootReducers;
