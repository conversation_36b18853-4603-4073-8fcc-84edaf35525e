import React, { Component } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>readcrumbs,
  Button,
  TextField,
} from "@mui/material";
import { Link } from "react-router-dom";
import axiosInstance from "../../helpers/Axios";
import ActionMessage from "../../library/common/components/ActionMessage";
import Select from "react-select";
import "./socBroadcasr.scss";

const SOCBroadcastOptions = [
  {
    label: "Yes",
    value: "true",
  },
  {
    label: "No",
    value: "false",
  },
];

class SOCBroadcast extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: null,
      endDate: null,
      isLoading: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      broadcastText: "",
      selectedSOCBroadcast: null,
      newsTypeOption: [],
      isProviderLoading: false,
    };
  }

  handleBroadcastSave = async () => {
    this.setState({ isLoading: true });

    try {
      const { status, data } = await axiosInstance.post("/general/settings", {
        broadcast_status: this.state.selectedSOCBroadcast,
        broadcast_message: this.state.broadcastText,
      });
      if (status === 200) {
        this.setState({
          isLoading: false,
        });
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({
          isLoading: false,
        });
        this.setActionMessage(true, "error", data?.message);
      }
    } catch (error) {
      this.setState({
        isLoading: false,
      });
      console.log("objecterror", error);
      this.setActionMessage(true, "error", error?.response?.data?.message);
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  componentDidMount() {
    this.fetchSOCBroadcast();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match.path !== this.props.match.path) {
      this.setState({
        broadcastText: "",
        selectedSOCBroadcast: null,
      });
    }
  }

  fetchSOCBroadcast = async () => {
    this.setState({ isProviderLoading: true });
    try {
      const { status, data } = await axiosInstance.get(`/general/settings`);
      if (status === 200) {
        const broadcastData = data?.result;
        this.setState({
          broadcastText: broadcastData?.broadcast_message
            ? broadcastData?.broadcast_message
            : "",
          isProviderLoading: false,
          selectedSOCBroadcast: broadcastData?.broadcast_status
            ? broadcastData?.broadcast_status
            : null,
        });
      } else {
        this.setState({ isProviderLoading: false });
      }
    } catch (err) {
      this.setState({ isProviderLoading: false });
    }
  };

  handleBroadcastChange = (e) => {
    this.setState({
      selectedSOCBroadcast: e.value,
    });
  };

  render() {
    const { messageBox, selectedSOCBroadcast, broadcastText } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin soc-broadcast-wrap">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>

                <Typography className="active_p">SOC Broadcast</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  SOC Broadcast
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
                className="admin-fixture-wrap"
              ></Grid>
            </Grid>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="txt-field-class"
                  value={broadcastText}
                  onChange={(e) => {
                    this.setState({ broadcastText: e?.target?.value });
                  }}
                />
              </Grid>
              <Grid
                item
                xs={5}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
                className="admin-fixture-wrap"
              >
                <Select
                  className="React cricket-select external-select"
                  classNamePrefix="select"
                  placeholder="Broadcast list"
                  value={SOCBroadcastOptions?.find((item) => {
                    return item?.value == selectedSOCBroadcast;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleBroadcastChange(e)}
                  options={SOCBroadcastOptions}
                />
              </Grid>
              <Grid
                item
                xs={2}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
                className="admin-fixture-wrap"
              >
                <div>
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      marginLeft: "10px",
                    }}
                    onClick={() => this.handleBroadcastSave()}
                  >
                    Save
                  </Button>
                </div>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </>
    );
  }
}

export default SOCBroadcast;
