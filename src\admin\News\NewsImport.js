import React, { Component } from "react";
import { <PERSON>rid, Typo<PERSON>, <PERSON>, <PERSON>readcrumbs, Button } from "@mui/material";
import { Link } from "react-router-dom";
import axiosInstance from "../../helpers/Axios";
import ActionMessage from "../../library/common/components/ActionMessage";
import Select from "react-select";

class NewsImport extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: null,
      endDate: null,
      isLoading: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      errorStartDate: "",
      errorEndDate: "",
      selectedNewsType: null,
      newsTypeOption: [],
      isProviderLoading: false,
    };
  }
  handleStartDate = (date) => {
    this.setState({ startDate: date });
  };
  handleEndDate = (date) => {
    this.setState({ endDate: date });
  };
  //   handleValidate = () => {
  //     let { startDate, endDate } = this.state;
  //     let flag = true;
  //     if (!startDate) {
  //       flag = false;
  //       this.setState({
  //         errorStartDate: "This field is mandatory",
  //         isLoading: false,
  //       });
  //     } else {
  //       this.setState({
  //         errorStartDate: "",
  //       });
  //     }
  //     if (!endDate) {
  //       flag = false;
  //       this.setState({
  //         errorEndDate: "This field is mandatory",
  //         isLoading: false,
  //       });
  //     } else {
  //       this.setState({
  //         errorEndDate: "",
  //       });
  //     }
  //     return flag;
  //   };

  handleNewsImport = async () => {
    // if (this.handleValidate()) {
    this.setState({ isLoading: true });
    //   const { startDate, endDate } = this.state;
    //   let payload = {
    //     date: moment(startDate).format("YYYY-MM-DD"),
    //     endDate: moment(endDate).format("YYYY-MM-DD"),
    //   };
    try {
      const { status, data } = await axiosInstance.post(
        "v2/news/sync/articles",
        {
          ProviderId: this.state.selectedNewsType,
        }
      );
      if (status === 200) {
        this.setState({
          isLoading: false,
          startDate: null,
          endDate: null,
        });
        this.setActionMessage(true, "Success", "News import Process started");
      } else {
        this.setState({
          isLoading: false,
          startDate: null,
          endDate: null,
        });
      }
    } catch (error) {
      this.setState({
        isLoading: false,
        startDate: null,
        endDate: null,
      });
      //   }
    }
  };
  // setActionMessage = (display = false, type = "", message = "") => {
  //   let setActionMessage = {
  //     display: display,
  //     type: type,
  //     message: message,
  //   };
  //   this.setState({ messageBox: setActionMessage });
  // };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  componentDidMount() {
    this.fetchAllNewsProvider();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match.path !== this.props.match.path) {
      this.setState({
        startDate: null,
        endDate: null,
        errorStartDate: "",
        errorEndDate: "",
      });
    }
  }
  handleNewsTypeChange = (e) => {
    this.setState({
      selectedNewsType: e.value,
    });
  };
  fetchAllNewsProvider = async () => {
    this.setState({ isProviderLoading: true });
    try {
      const { status, data } = await axiosInstance.get(`v2/news/provider`);
      if (status === 200) {
        let newdata = [];
        let providerData = data.result?.map((item) => {
          newdata.push({
            label: item?.providerName,
            value: item?.id,
          });
        });

        // let pushData = newdata.push(alldatas);
        // let sortData = newdata?.sort((a, b) => {
        //   return a.value > b.value ? 1 : -1;
        // });
        this.setState({
          newsTypeOption: newdata,
          isProviderLoading: false,
          selectedNewsType: newdata?.[0]?.value,
        });
      } else {
        this.setState({ isProviderLoading: false });
      }
    } catch (err) {
      this.setState({ isProviderLoading: false });
    }
  };
  render() {
    const {
      startDate,
      endDate,
      messageBox,
      errorStartDate,
      errorEndDate,
      newsTypeOption,
      selectedNewsType,
    } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  News
                </Link>
                <Typography className="active_p">News import</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  News import
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
                className="admin-fixture-wrap"
              >
                <Select
                  className="React cricket-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Type"
                  value={newsTypeOption?.find((item) => {
                    return item?.value == selectedNewsType;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleNewsTypeChange(e)}
                  options={newsTypeOption}
                />
                <div>
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      marginLeft: "10px",
                    }}
                    onClick={() => this.handleNewsImport()}
                  >
                    import News
                  </Button>
                </div>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </>
    );
  }
}

export default NewsImport;
