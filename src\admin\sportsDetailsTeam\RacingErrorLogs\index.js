import React from "react";
import {
  Grid,
  Typography,
  // withSty<PERSON>,
  Box,
  Tabs,
  Tab,
  Breadcrumbs,
} from "@mui/material";
// import MuiAccordion from "@mui/material/Accordion";
// import MuiAccordionSummary from "@mui/material/AccordionSummary";
// import MuiAccordionDetails from "@mui/material/AccordionDetails";
// import { ReactSVG } from "react-svg";
// import Pagination from "@mui/lab/Pagination";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
import ButtonComponent from "../../../library/common/components/Button";
import axiosInstance from "../../../helpers/Axios";
import moment from "moment";
import "./errorLogs.scss";
import Compltedtable from "./compltedtable";
import { Link } from "react-router-dom";
import Inprogresstable from "./inprogresstable";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import FailedTable from "./failedErrortable";
import BrowserErrorTable from "./BrowserErrorTable";
import Select from "react-select";

// const Accordion = withStyles({
//   root: {
//     border: "1px solid rgba(0, 0, 0, .125)",
//     boxShadow: "none",
//     "&:not(:last-child)": {
//       borderBottom: 0,
//     },
//     "&:before": {
//       display: "none",
//     },
//     "&$expanded": {
//       margin: "auto",
//     },
//   },
//   expanded: {},
// })(MuiAccordion);
const tabs = [
  {
    id: 0,
    tabsName: "Racing Sync Error",
  },
  {
    id: 1,
    tabsName: "Failed Error",
  },
  {
    id: 2,
    tabsName: "Race Errors (Only for Dev Team)",
  },
  // {
  //   id: 1,
  //   tabsName: "InProgress Sync",
  // },
];

const ExternalCategoryOption = [
  { label: "All", value: null },
  { label: "Api", value: "api" },
  { label: "Scrap", value: "scrap" },
];

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class RaceErrorLogs extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      searchVal: "",
      errlgList: [],
      eventLoader: false,
      selectedDate: null,
      value: 0,
      completed: "",
      inprogerss: "",
      newdata: [],
      inProgerssNewData: [],
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      sportCount: null,
      isErrorLogsDeleteModal: false,
      selectedType: "api",
      errTabValue: 0,
    };
  }

  componentDidMount = () => {
    const searchParams = new URLSearchParams(window.location.search);
    const selectTypeData = searchParams.get("selectType");
    const errorTabData = searchParams.get("errorTab");
    this.setState({
      selectedType: selectTypeData !== null ? selectTypeData : null,
      errTabValue: Number(errorTabData),
      value: Number(errorTabData),
    });
    if (Number(errorTabData) == 0) {
      this.fetchAllErrorLogs(
        0,
        "",
        selectTypeData !== null ? selectTypeData : null
      );
    } else if (Number(errorTabData) == 1) {
      this.fetchFailedLogs(0, "");
    } else {
      this.fetchBrowserLogs(0, "");
    }
  };

  componentDidUpdate = (prevProps, prevState) => {
    if (prevState?.value !== this.state.value) {
      const searchParams = new URLSearchParams(window.location.search);
      const errorTabData = searchParams.get("errorTab");
      this.setState({ errTabValue: errorTabData });
    }
    // if (prevState.offset !== this.state.offset) {
    //   // this.fetchAllErrorLogs();
    //   if(this.state.value ===0 ){
    //     this.fetchAllErrorLogs()
    //   }else if(this.state.value ===1){
    //     this.fetchFailedLogs()
    //   }else{
    //     this.fetchBrowserLogs()
    //   }
    // }
  };

  searchInputHandler = (e) => {
    this.setState({ searchVal: e.target.value });
  };

  // handleDateChange = (date) => {
  //   this.setState({ selectedDate: date });
  // };

  fetchAllErrorLogs = async (offsetValue, searchInput, selectedType) => {
    let { selectedDate, rowPerPage, offset, searchVal } = this.state;
    let date =
      selectedDate === null
        ? moment().format("YYYY-MM-DD")
        : moment(selectedDate).format("YYYY-MM-DD");

    this.setState({ eventLoader: true });
    let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    try {
      const { status, data } = await axiosInstance.get(
        // `/adminNotification/error?date=${date}`
        `/adminNotification/reports?date=${date}&trackOnly=1&timezone=${timezone}&limit=${rowPerPage}&offset=${offsetValue}&matchString=${searchInput}&type=${
          selectedType !== null ? selectedType : ""
        }`
      );

      if (status === 200) {
        this.setState({
          errlgList: data.errors.rows,
          eventLoader: false,
          sportCount: data?.errors?.count,
        });

        // const completedData = data?.errors?.rows?.filter(
        //   (obj) => obj?.status === "completed"
        // );

        const reportMeetingsData = data?.errors?.rows?.map((item) => {
          let datas = {
            ...item,
            ReportMeetings: item?.ReportMeetings?.map((obj) => {
              let errorData = {
                ...obj,
                errors: JSON.parse(obj?.errors)?.length
                  ? JSON.parse(obj?.errors)
                  : [JSON.parse(obj?.errors)],
              };
              return errorData;
            }),
          };
          return datas;
        });
        const inProgressdData = data?.errors?.rows.filter(
          (obj) => obj?.status === "inprogress"
        );
        const inProgressReportMeetingsData = inProgressdData.map((item) => {
          let datas = {
            ...item,
            ReportMeetings: item?.ReportMeetings?.map((obj) => {
              let errorData = {
                ...obj,
                errors: JSON.parse(obj?.errors)?.length
                  ? JSON.parse(obj?.errors)
                  : [JSON.parse(obj?.errors)],
              };
              return errorData;
            }),
          };
          return datas;
        });

        this.setState({
          newdata: reportMeetingsData,
          inProgerssNewData: inProgressReportMeetingsData,
        });
      }
    } catch (error) {
      this.setState({ eventLoader: false });
    }
  };
  fetchFailedLogs = async (offsetValue, searchInput) => {
    let { rowPerPage } = this.state;

    this.setState({ eventLoader: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/adminNotification/failedReport?limit=${rowPerPage}&offset=${offsetValue}&providerId=&search=${searchInput}`
      );

      if (status === 200) {
        this.setState({
          eventLoader: false,
          sportCount: data?.count,
          newdata: data?.result,
        });
      }
    } catch (error) {
      this.setState({ eventLoader: false });
    }
  };
  fetchBrowserLogs = async (offsetValue, searchInput) => {
    let { rowPerPage, offset } = this.state;
    this.setState({ eventLoader: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/adminNotification/browserReport?limit=${rowPerPage}&offset=${offsetValue}&bookkeperId=&search=${searchInput}`
      );

      if (status === 200) {
        this.setState({
          eventLoader: false,
          sportCount: data?.count,
          newdata: data?.result,
        });
      }
    } catch (error) {
      this.setState({ eventLoader: false });
    }
  };
  handleChange = (event, newValue) => {
    this.setState({
      value: newValue,
      offset: 0,
      currentPage: 1,
      searchVal: "",
      selectedType: "api",
      newdata: [],
    });
    if (newValue === 0) {
      this.props.navigate(`/racing/errorLogs?errorTab=0`);
      this.fetchAllErrorLogs(0, "", "api");
    } else if (newValue === 1) {
      this.props.navigate(`/racing/errorLogs?errorTab=1`);
      this.fetchFailedLogs(0, "");
    } else {
      this.props.navigate(`/racing/errorLogs?errorTab=2`);
      this.fetchBrowserLogs(0, "");
    }
  };
  allErrorLogeDelete = () => {
    this.setState({ isErrorLogsDeleteModal: true });
  };
  toggleAllErrorLogeDelete = () => {
    this.setState({ isErrorLogsDeleteModal: false });
  };
  handleErrorDelateAll = async () => {
    this.setState({ eventLoader: true });
    try {
      const apiEndpoint =
        this.state.value === 2
          ? `adminNotification/browserReport`
          : `adminNotification/reports/`;
      let { status } = await axiosInstance.delete(apiEndpoint);

      if (status === 200) {
        this.setState({ eventLoader: false });
        if (this.state.value === 0) {
          this.fetchAllErrorLogs(
            this.state.offset,
            this.state.searchVal,
            this.state.selectedType
          );
        } else if (this.state.value === 1) {
          this.fetchFailedLogs(this.state.offset, this.state.searchVal);
        } else {
          this.fetchBrowserLogs(this.state.offset, this.state.searchVal);
        }
      }
    } catch (err) {
      this.setState({ eventLoader: false });
    }
  };

  handleExternalCategoryChange = (e) => {
    this.setState({
      selectedType: e.value,
      currentPage: 1,
      offset: 0,
    });
    this.props.navigate(
      `/racing/errorLogs?errorTab=0&selectType=${
        e.value !== null ? e.value : ""
      }`
    );
    this.fetchAllErrorLogs(0, this.state.searchVal, e.value);
  };

  render() {
    // const providerList = [
    //   {
    //     id: 1,
    //     name: "PUP",
    //   },
    //   {
    //     id: 2,
    //     name: "VBT",
    //   },
    //   {
    //     id: 3,
    //     name: "Neds",
    //   },
    //   {
    //     id: 4,
    //     name: "Ladbrokes",
    //   },
    //   {
    //     id: 5,
    //     name: "Bookmaker",
    //   },
    //   {
    //     id: 6,
    //     name: "BetStar",
    //   },
    //   {
    //     id: 8,
    //     name: "Bet365",
    //   },
    //   {
    //     id: 9,
    //     name: "PalmerBet",
    //   },
    //   {
    //     id: 10,
    //     name: "Unibet",
    //   },
    //   {
    //     id: 11,
    //     name: "TOP",
    //   },
    // ];

    // const { match } = this.props;
    let {
      // errlgList,
      searchVal,
      eventLoader,
      // selectedDate,
      value,
      newdata,
      sportCount,
      inProgerssNewData,
      currentPage,
      rowPerPage,
      // offset,
      isErrorLogsDeleteModal,
      selectedType,
      errTabValue,
    } = this.state;
    const errLogData = newdata
      ?.map((data) => {
        let datas = {
          ...data,
          ReportMeetings: data?.ReportMeetings?.filter((item) => {
            if (searchVal === "") return item;
            else if (
              item?.name
                .toString()
                .toLowerCase()

                .includes(searchVal.toString().toLowerCase().trim()) ||
              item?.errors?.[0]?.message
                .toString()
                .toLowerCase()
                .includes(searchVal.toString().toLowerCase().trim())
            ) {
              return item;
            }
          }),
        };
        return datas;
      })
      .filter((x) => x?.ReportMeetings?.length > 0);

    return (
      <>
        <Grid container className="page-content adminLogin sports_details">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  racing
                </Link>
                <Typography className="active_p">Error Logs</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                {/* <h1 className="text-left">Racing Error Logs</h1> */}
                <Typography variant="h1" align="left">
                  Racing Error Logs
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
              >
                {this.state.value === 0 && (
                  <Select
                    className="React teamsport-select external-select text-capitalize ourteam-select"
                    classNamePrefix="select"
                    placeholder="All"
                    value={
                      selectedType &&
                      ExternalCategoryOption?.find((item) => {
                        return item?.value == selectedType;
                      })
                    }
                    onChange={(e) => this.handleExternalCategoryChange(e)}
                    options={ExternalCategoryOption}
                  />
                )}
                <input
                  type="text"
                  className="error-search"
                  placeholder="Search"
                  value={searchVal}
                  onChange={(e) => {
                    this.searchInputHandler(e);
                  }}
                  style={{
                    fontSize: "16px",
                    borderRadius: "8px",
                    minHeight: "36px",
                    border: "1px solid #ddd",
                    paddingLeft: "10px",
                    marginLeft: "15px",
                    color: "#000000",
                    width: "25%",
                  }}
                />
                <Box
                  style={{
                    display: "flex",
                    marginLeft: "15px",
                    alignItems: "center",
                  }}
                >
                  <ButtonComponent
                    className="admin-btn-green"
                    color="primary"
                    value="Search"
                    onClick={() => {
                      if (this.state.value === 0) {
                        this.fetchAllErrorLogs(
                          0,
                          this.state.searchVal,
                          this.state.selectedType
                        );
                      } else if (this.state.value === 1) {
                        this.fetchFailedLogs(0, this.state.searchVal);
                      } else {
                        this.fetchBrowserLogs(0, this.state.searchVal);
                      }

                      this.setState({
                        currentPage: 1,
                        offset: 0,
                      });
                    }}
                    style={{ padding: "5px 16px" }}
                  />
                  <ButtonComponent
                    className="admin-btn-green"
                    color="primary"
                    value="Delete All"
                    onClick={() => this.allErrorLogeDelete()}
                    style={{ marginLeft: "15px" }}
                  />
                </Box>
              </Grid>
            </Grid>

            <Box className="sport-tab error-tab">
              <Tabs
                value={value}
                indicatorColor="primary"
                textColor="primary"
                className="racing-tab-detail"
                disableRipple
                disableFocusRipple
              >
                {tabs?.map((item, index) => {
                  return (
                    <>
                      <Tab
                        disableRipple
                        disableFocusRipple
                        label={item?.tabsName}
                        value={item?.id}
                        className={item?.id == errTabValue ? "active" : ""}
                        onChange={(event, newValue) =>
                          this.handleChange(event, item?.id)
                        }
                      />
                    </>
                  );
                })}
              </Tabs>
            </Box>
            {value == 0 ? (
              <Compltedtable
                newdata={newdata}
                sportCount={sportCount}
                currentPage={currentPage}
                rowPerPage={rowPerPage}
                isLoading={eventLoader}
                searchVal={searchVal}
                errLogData={errLogData}
                navigate={this.props?.navigate}
                afterRefresh={() => {
                  this.fetchAllErrorLogs(
                    this.state.offset,
                    this.state.searchVal,
                    this.state.selectedType
                  );
                }}
                currentPagechange={(data, data1) => {
                  this.setState({
                    currentPage: data,
                    offset: data1,
                  });

                  this.fetchAllErrorLogs(
                    data1,
                    this.state.searchVal,
                    this.state.selectedType
                  );
                }}
                selectedType={this.state?.selectedType}
              />
            ) : value == 1 ? (
              // <Inprogresstable
              //   inProgerssNewData={inProgerssNewData}
              //   sportCount={sportCount}
              //   currentPage={currentPage}
              //   rowPerPage={rowPerPage}
              //   currentPagechange={(data, data1) => {
              //     this.setState({
              //       currentPage: data,
              //       offset: data1,
              //     });
              //   }}
              // />
              <FailedTable
                newdata={newdata}
                sportCount={sportCount}
                currentPage={currentPage}
                rowPerPage={rowPerPage}
                isLoading={eventLoader}
                searchVal={searchVal}
                afterRefresh={() =>
                  this.fetchFailedLogs(this.state.offset, this.state.searchVal)
                }
                currentPagechange={(data, data1) => {
                  this.setState({
                    currentPage: data,
                    offset: data1,
                  });
                  this.fetchFailedLogs(data1, this.state.searchVal);
                }}
              />
            ) : (
              <BrowserErrorTable
                newdata={newdata}
                sportCount={sportCount}
                currentPage={currentPage}
                rowPerPage={rowPerPage}
                isLoading={eventLoader}
                searchVal={searchVal}
                afterRefresh={() =>
                  this.fetchBrowserLogs(this.state.offset, this.state.searchVal)
                }
                currentPagechange={(data, data1) => {
                  this.setState({
                    currentPage: data,
                    offset: data1,
                  });
                  this.fetchBrowserLogs(data1, this.state.searchVal);
                }}
              />
            )}
            {/* <Box mt={3} mb={3}>
              {eventLoader ? (
                <Box
                  mt={3}
                  mb={3}
                  width={"100%"}
                  display={"flex"}
                  justifyContent={"center"}
                >
                  <CircularProgress size={18} />
                </Box>
              ) : errLogData?.length > 0 ? (
                <Box>
                  <TableContainer component={Paper}>
                    <Table
                      className="listTable api-provider-listTable"
                      aria-label="simple table"
                    >
                      <TableHead>
                        <TableRow>
                          <TableCell>DID</TableCell>
                          <TableCell>RaceId</TableCell>
                          <TableCell>EventId</TableCell>
                          <TableCell>Status Code</TableCell>
                          <TableCell>Error Type</TableCell>
                          <TableCell>Provider</TableCell>
                          <TableCell>Log</TableCell>
                          <TableCell>Created At</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {errLogData?.map((data, index) => {
                          return (
                            <TableRow key={index}>
                              <TableCell>{data?.id}</TableCell>
                              <TableCell>{data?.raceId}</TableCell>
                              <TableCell>{data?.eventId}</TableCell>
                              <TableCell>{data?.statusCode}</TableCell>
                              <TableCell>{data?.errorType}</TableCell>
                              <TableCell>
                                {
                                  providerList?.filter(
                                    (obj) => obj.id === data?.providerId
                                  )[0]?.name
                                }
                              </TableCell>
                              <TableCell>{data?.log}</TableCell>
                              <TableCell>
                                {moment(data?.createdAt).format(
                                  "DD/MM/YYYY hh:mm A"
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              ) : (
                <Typography align="center">Data Not Available</Typography>
              )}
            </Box> */}
            {/* </Paper> */}
          </Grid>
        </Grid>
        <ShowModal
          isModalOpen={isErrorLogsDeleteModal}
          onClose={this.toggleAllErrorLogeDelete}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={this.handleErrorDelateAll}
          onCancel={this.toggleAllErrorLogeDelete}
        />
      </>
    );
  }
}
export default RaceErrorLogs;
