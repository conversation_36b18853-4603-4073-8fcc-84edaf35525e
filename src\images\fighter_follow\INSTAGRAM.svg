<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17.606" height="17.122" viewBox="0 0 17.606 17.122">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_71" data-name="Rectangle 71" width="17.606" height="17.122" transform="translate(0 0)" stroke="#707070" stroke-width="1"/>
    </clipPath>
  </defs>
  <g id="INSTAGRAM" clip-path="url(#clip-path)">
    <g id="twitter_instagram_wordpress" data-name="twitter instagram wordpress" transform="translate(0 0)">
      <path id="Path_107" data-name="Path 107" d="M654.477-301.523a8.7,8.7,0,0,0-4.289,1.954A8.176,8.176,0,0,0,648-296.743a7.954,7.954,0,0,0-.823,4.1,6.1,6.1,0,0,0,.212,1.682,8.547,8.547,0,0,0,6.3,6.3,6.1,6.1,0,0,0,1.682.212,7.954,7.954,0,0,0,4.1-.823,8.258,8.258,0,0,0,2.825-2.184,8.484,8.484,0,0,0,2.027-5.9,8.013,8.013,0,0,0-.956-3.606,8.308,8.308,0,0,0-1.706-2.275,8.767,8.767,0,0,0-4.283-2.226A11.417,11.417,0,0,0,654.477-301.523Zm2.444.907a8.32,8.32,0,0,1,2.42.8,7.688,7.688,0,0,1,1.434.986.631.631,0,0,1-.242.109,1.369,1.369,0,0,0-.774.659,1.121,1.121,0,0,0-.157.738,3,3,0,0,0,.629,1.724,3.788,3.788,0,0,1,.678,2.238,4.319,4.319,0,0,1-.187,1.361,27.866,27.866,0,0,1-1.18,3.448c-.139-.351-2.7-7.962-2.7-8.022s.115-.085.484-.085c.454,0,.484-.006.52-.145a1.192,1.192,0,0,0,.012-.333l-.018-.2h-4.6l-.018.2a1.19,1.19,0,0,0,.012.333c.036.139.061.145.551.145.424,0,.52.018.569.109.03.054.309.793.611,1.627l.557,1.525-.853,2.553c-.466,1.41-.865,2.517-.883,2.468-.218-.593-2.753-8.131-2.753-8.192s.127-.091.532-.1l.526-.018.018-.224c.036-.442.06-.436-1.4-.417a10.085,10.085,0,0,1-1.307-.03,8.713,8.713,0,0,1,2.039-2.039,8.45,8.45,0,0,1,3.2-1.246A9.622,9.622,0,0,1,656.921-300.615Zm6.044,4.955a7.087,7.087,0,0,1,.448,2.335,7.554,7.554,0,0,1-1.325,4.658,9.873,9.873,0,0,1-2.009,2.009,3.22,3.22,0,0,1-.436.266c-.012-.012.508-1.519,1.162-3.352,1.555-4.417,1.712-4.937,1.821-6.147C662.674-296.429,662.687-296.423,662.965-295.66Zm-12.475,4.6c.968,2.656,1.748,4.84,1.73,4.858a5.306,5.306,0,0,1-1.355-.865,7.679,7.679,0,0,1-2.68-4.519,9.159,9.159,0,0,1-.024-2.729,5.377,5.377,0,0,1,.514-1.633C648.7-295.92,649.516-293.724,650.49-291.062Zm6.613,1.984c.659,1.8,1.174,3.3,1.143,3.328a4.374,4.374,0,0,1-.653.212,5.3,5.3,0,0,1-1.779.163,6.628,6.628,0,0,1-1.67-.109,3.751,3.751,0,0,1-.557-.151,35.5,35.5,0,0,1,1.1-3.382c.641-1.833,1.174-3.34,1.192-3.34S656.443-290.881,657.1-289.078Z" transform="translate(-609.416 301.589)"/>
      <path id="Path_108" data-name="Path 108" d="M27.934-296.55a4.775,4.775,0,0,0-3.569,1.494,4.9,4.9,0,0,0-1.21,3.3c-.073,1.077-.073,5.784,0,6.885a10.269,10.269,0,0,0,.181,1.361,4.348,4.348,0,0,0,2.269,2.874c1.047.514,1.845.6,5.778.6,3.914,0,4.707-.085,5.8-.611a3.261,3.261,0,0,0,1.071-.8c.95-.956,1.246-1.773,1.361-3.727.085-1.373.036-6.661-.067-7.32a6.041,6.041,0,0,0-.678-1.954,4.619,4.619,0,0,0-3.315-2.039C34.988-296.574,29.09-296.623,27.934-296.55Zm7.641,1.773a2.838,2.838,0,0,1,1.434.744,2.865,2.865,0,0,1,.829,1.525,24.614,24.614,0,0,1,.109,4.217c0,4.09-.006,4.132-.363,4.919a3.032,3.032,0,0,1-1.192,1.216c-.774.387-.9.4-5.009.4s-4.235-.012-5.009-.4a3.032,3.032,0,0,1-1.192-1.216c-.363-.786-.363-.829-.363-4.919,0-4.15.006-4.265.4-5.04a3.14,3.14,0,0,1,1.119-1.15,4.172,4.172,0,0,1,1.416-.375c.248-.03,2.021-.042,3.933-.036A26.371,26.371,0,0,1,35.575-294.777Z" transform="translate(-23.1 296.888)"/>
      <path id="Path_109" data-name="Path 109" d="M215.827-248.9a1.331,1.331,0,0,0-.6.889.988.988,0,0,0,1.059,1.047,1.3,1.3,0,0,0,.859-.557,1.109,1.109,0,0,0-.024-1.065A1.051,1.051,0,0,0,215.827-248.9Z" transform="translate(-203.6 252.181)"/>
      <path id="Path_110" data-name="Path 110" d="M93.311-229.869a4.476,4.476,0,0,0-2.571,1.519,4.217,4.217,0,0,0,.3,5.669,4.242,4.242,0,0,0,5.717.266,4.464,4.464,0,0,0,1.428-2.353,5.047,5.047,0,0,0-.1-2.178,4.23,4.23,0,0,0-4.1-2.958C93.7-229.9,93.4-229.882,93.311-229.869Zm1.888,1.785a2.8,2.8,0,0,1,1.277,1.3,1.864,1.864,0,0,1,.194,1.107,1.864,1.864,0,0,1-.194,1.107,2.785,2.785,0,0,1-1.283,1.3,1.791,1.791,0,0,1-1.156.224,1.791,1.791,0,0,1-1.156-.224,2.785,2.785,0,0,1-1.283-1.3,1.864,1.864,0,0,1-.194-1.107,1.832,1.832,0,0,1,.194-1.1,2.8,2.8,0,0,1,1.621-1.458,2.412,2.412,0,0,1,.914-.091A1.892,1.892,0,0,1,95.2-228.085Z" transform="translate(-85.755 234.243)"/>
      <path id="Path_111" data-name="Path 111" d="M974.484-287.9a.987.987,0,0,0-.345.309l-.139.206V-272.6l.139.206c.29.436.109.417,4.477.417h3.914v-6.286h-2l-.012-1.15-.012-1.156h2.015l.03-1.258a6.394,6.394,0,0,1,.133-1.585,2.624,2.624,0,0,1,1.936-1.912,4.131,4.131,0,0,1,1.325-.1c.526.006,1.065.03,1.192.048l.236.042v1.966l-.811.03c-.738.024-.829.042-1.077.188-.442.26-.5.442-.5,1.585v.968l1.168.018,1.168.012-.03.169c-.018.091-.091.6-.157,1.125s-.127.974-.145.986-.466.03-1.01.036l-.992.012-.018,3.013c-.006,2.009.006,3.049.048,3.128.06.115.145.121,2.184.121,2.015,0,2.13-.006,2.36-.127a.71.71,0,0,0,.363-.375c.109-.248.115-.508.115-7.514v-7.266l-.133-.266c-.272-.526.405-.484-7.883-.484C975.168-288.013,974.684-288.007,974.484-287.9Z" transform="translate(-916.471 288.835)"/>
      <path id="Path_112" data-name="Path 112" d="M342.542-279.775a3.81,3.81,0,0,0-2.5,2.353,2.634,2.634,0,0,0-.157,1.216l-.006.811-.466-.036a10.148,10.148,0,0,1-3.527-1.01,9.631,9.631,0,0,1-3.073-2.317,2.93,2.93,0,0,0-.466-.466.84.84,0,0,0-.175.3,3.941,3.941,0,0,0,.018,3.122,4.248,4.248,0,0,0,.986,1.271l.29.23-.315-.036a3.108,3.108,0,0,1-.7-.194,4.527,4.527,0,0,0-.526-.194c-.145-.036-.145-.036-.1.363a3.731,3.731,0,0,0,2.5,3.128c.188.061.3.121.26.145a3.65,3.65,0,0,1-.732.03,3.386,3.386,0,0,0-.7.024c-.061.06.1.442.339.823a3.7,3.7,0,0,0,2.632,1.688l.363.048-.333.218a7.42,7.42,0,0,1-4.277,1.18c-.5-.03-.629-.018-.69.054-.1.115-.121.1.793.563a10.041,10.041,0,0,0,4.326,1.131,10.1,10.1,0,0,0,8.754-4.06,11.186,11.186,0,0,0,2.166-6.371,1.488,1.488,0,0,1,.054-.557,8.319,8.319,0,0,0,1.688-1.785.853.853,0,0,0-.333.091,11.533,11.533,0,0,1-1.47.387l-.212.036.181-.133a4.548,4.548,0,0,0,1.192-1.379,2.249,2.249,0,0,0,.206-.472,1.173,1.173,0,0,0-.333.133,7.178,7.178,0,0,1-1.452.557l-.508.139-.309-.272a4.161,4.161,0,0,0-1.519-.78A4.658,4.658,0,0,0,342.542-279.775Z" transform="translate(-312.51 281.196)"/>
    </g>
  </g>
</svg>
