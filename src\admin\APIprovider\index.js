import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import { Link } from "react-router-dom";
import "./apiprovider.scss";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import RateReviewIcon from "@mui/icons-material/RateReview";
// import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import CreateApiProviderUpdate from "./CreateAPIprovider";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import { config } from "../../helpers/config";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";

class ApiProvider extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      apiprovider: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
    };
  }

  componentDidMount() {
    this.fetchAllApiProvider();
  }

  async fetchAllApiProvider() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(URLS.apiProvider);
    if (status === 200) {
      const dataResult = []
        .concat(data.result)
        .sort((a, b) => (a.displayOrder > b.displayOrder ? 1 : -1));
      this.setState({ apiprovider: dataResult, isLoading: false });
    }
  }

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  navigateToUpdateApiProvider = (id) => () => {
    this.props.navigate(`/apiprovider/apiproviderupdate/${id}`);
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllApiProvider();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.apiProvider}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllApiProvider();
        });
        this.setActionMessage(
          true,
          "Success",
          "Api Provider Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, apiprovider } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < apiprovider.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  render() {
    var {
      apiprovider,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
    } = this.state;

    const pageNumbers = [];
    let currentPageRow = apiprovider;

    if (apiprovider?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = apiprovider.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(apiprovider.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin api-provider-main">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit"></Link>
                <Typography className="active_p">Api Provider</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={10}>
                {/* <h3 className="text-left">Api Provider</h3> */}
                <Typography variant="h1" align="left">
                  Api Provider
                </Typography>
              </Grid>
              <Grid
                item
                xs={2}
                style={{ display: "flex", justifyContent: "end" }}
              >
                {/* <ButtonComponent
                  className="addButton admin-btn-green"
                  onClick={this.inputModal(null, "create")}
                  color="primary"
                  value="Add New"
                /> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && apiprovider.length === 0 && <p>No Data Available</p>}
            {!isLoading && apiprovider.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Logo</TableCell>
                        <TableCell>Provider Name</TableCell>
                        <TableCell>Api Key ID</TableCell>
                        {/* <TableCell>Api access Key</TableCell> */}
                        <TableCell>Status</TableCell>
                        <TableCell>Display Order</TableCell>
                        {/* <TableCell>Response Type</TableCell> */}
                        {/* <TableCell>Date Type</TableCell> */}
                        {/* <TableCell>Api Provider Update</TableCell> */}
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((apiProviderItem, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{apiProviderItem.id}</TableCell>
                          <TableCell>
                            <div className="logocontainer">
                              {apiProviderItem.logo ? (
                                <img
                                  src={config.mediaUrl + apiProviderItem.logo}
                                  alt={apiProviderItem.providerName}
                                />
                              ) : (
                                <div className="imagePlaceholder">Logo</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{apiProviderItem.providerName}</TableCell>
                          <TableCell>{apiProviderItem.apiKeyId}</TableCell>
                          {/* <TableCell>{apiProviderItem.apiAccessKey}</TableCell> */}
                          <TableCell>{apiProviderItem.status}</TableCell>
                          <TableCell>{apiProviderItem.displayOrder}</TableCell>
                          {/* <TableCell>{apiProviderItem.responseType}</TableCell> */}
                          {/* <TableCell>{apiProviderItem.dateType}</TableCell> */}
                          {/* <TableCell>
                            <RateReviewIcon
                              onClick={this.navigateToUpdateApiProvider(
                                apiProviderItem.id
                              )}
                              color="primary"
                              className="cursor iconBtn admin-btn-green"
                            />
                          </TableCell> */}
                          <TableCell>
                            {/* <EditIcon
                              onClick={this.inputModal(
                                apiProviderItem.id,
                                "edit"
                              )}
                              color="primary"
                              className="mr10 cursor iconBtn admin-btn-green"
                            /> */}
                            <Button
                              onClick={this.inputModal(
                                apiProviderItem.id,
                                "edit"
                              )}
                              className="table-btn"
                            >
                              Edit
                            </Button>
                            {/* <DeleteOutlineIcon
                              onClick={this.setItemToDelete(apiProviderItem.id)}
                              color="secondary"
                              className="mr10 cursor iconBtn admin-btn-orange"
                            /> */}
                            {/* <Button
                              onClick={this.setItemToDelete(apiProviderItem.id)}
                              className="table-btn"
                            >
                              Delete
                            </Button> */}
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          {" "}
                          <div className="tablePagination">
                            {/* <button
                              className={
                                apiprovider.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                apiprovider.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                apiprovider.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                apiprovider.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                apiprovider.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Api Provider"
                    : "Edit Api Provider"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateApiProviderUpdate
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllApiProvider={this.afterChangeRefresh}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default ApiProvider;
