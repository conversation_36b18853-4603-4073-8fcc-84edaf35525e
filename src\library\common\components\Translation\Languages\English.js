export const eng = {
    loginWelcomeText: 'Welcome to Book Banquet Room',
    loginText: 'Login',
    emailText: 'Email Id',
    PasswordText: 'Password',
    loginFormError: 'Please check email or password',
    selectVenuesText: 'Select venues',
    showCancelledEventsText: 'Show cancelled events',
    // bookings_buuton_addNew: 'New Booking',
    advancedSearchText: 'Advanced Search',
    bookingRefNoText: 'Booking Ref. No.',
    clearText: 'Clear',
    monthText: 'Month',
    weekText: 'Week',
    dayText: 'Day',
    confirmedText: 'CONFIRMED',
    tentativeText: 'TENTATIVE',
    waitListText: 'WAITLISTED',
    cancelledText: 'CANCELLED',
    cancelRequestText: 'CANCEL REQUEST',

    addOnsListText: 'AddOns List',
    newAddOnsText: 'New AddOns',
    addOnsName: 'AddOn name',
    addOnsType: 'AddOn type',
    addOnsCategory: 'AddOn category',
    addOnsTaxCode: 'Tax code',
    addOnsMaxCount: 'Max count',
    addOnsPrice: 'Price',
    addOnsText: 'addOns',
    addOnsImage: 'AddOn Image *',

    bookingDetailsCancelBooking: 'Cancel Booking',
    bookingDetailsConfirmBooking: 'Confirm Booking',
    bookingDetailsProcessRefund: 'Process Refund',
    bookingDetailsEditBooking: 'Edit Booking',
    bookingDetailsBookingReference: 'Booking Reference',
    bookingDetailsBookingStatus: 'Booking Status',
    bookingDetailsBookingConfirmationBy: 'Booking confirmation by',
    bookingDetailsCancelReason: 'Cancel Reason',
    bookingDetailsPricePerPerson: 'Price Per Person',
    bookingDetailsGuestCharges: 'Guest Charges',
    bookingDetailsHallRental: 'Hall Rental',
    bookingDetailsName: 'Name',
    bookingDetailsType: 'Type',
    bookingDetailsQuantity: 'Quantity',
    bookingDetailsPrice: 'Price',
    bookingDetailsRoomType: 'Room Type',
    bookingDetailsStartDate: 'Start Date',
    bookingDetailsEndDate: 'End Date',
    bookingDetailsNumberOfRooms: 'Number of Rooms',
    bookingDetailsTotalAddonCost: 'Total Addon Cost',
    bookingDetailsTotalAddonTax: 'Total Addon Tax',
    bookingDetailsTotalRoomCost: 'Total Room Cost',
    bookingDetailsTotalRoomTax: 'Total Room Tax',
    bookingDetailsTotalRoomServiceCharges: 'Total Room Service Charges',
    bookingDetailsTotalServiceCharge: 'Total Service Charge',
    bookingDetailsGSTAmount: 'GST Amount',
    bookingDetailsTotalAmount: 'Total Amount',
    customerDetailsTotalAmount: 'Customer Name',
    customerDetailsPhoneNumber: 'Phone Number',
    customerDetailsEmailId: 'Email Id',
    customerDetailsCompanyName: 'Company Name',
    customerDetailsDesignation: 'Designation',
    customerDetailsGST: 'GST',
    customerDetailsAddress: 'Address',
    customerDetailsCity: 'City',
    customerDetailsState: 'State',
    customerDetailsCountry: 'Country',
    customerDetailsPinCode: 'Pin code',
    tabsVenueDetail: 'Venue Detail',
    tabsCustomerDetail: 'Customer Detail',
    tabsBookingDetail: 'Booking Detail',
    tabsPaymentDetail: 'Payment Detail',
    paymentsPaymentOverdue: 'Payment Overdue',
    paymentsAmountDueAsOnDate: 'Amount Due as on Date',
    paymentsTotalPrice: 'Total Price',
    paymentsPaymentDate: 'Payment Date',
    paymentsPaymentLocation: 'Payment Location',
    paymentsPaymentMethod: 'Payment Method',
    paymentsPaymentReference: 'Payment Reference',
    paymentsAmountPaid: 'Amount Paid',
    paymentsRefundAmount: 'Refund Amount',
    paymentsRefundedDate: 'Refunded Date',
    paymentsRefundedAmount: 'Refunded Amount',
    paymentsRefundedReferenceNumber: 'Refunded Reference Number',
    venueDetailsTabsVenueName: 'Venue Name',
    venueDetailsTabsVenueDescription: 'Venue Description',
    venueDetailsTabsEventType: 'Event Type',
    venueDetailsTabsNoOfGuests: 'No. of guests',
    venueDetailsTabsDate: 'Date',

    bookingMessageListTitle: 'Booking Message List',
    bookingMessageType: 'Booking message type',
    bookingMessageUpdateBookingMessage: 'Update Booking Message',
    bookingMessageContentIsEmpty: 'Content is empty',

    CompanyCreditListTitle: 'Company Credit List',
    CompanyCreditNewButton: 'New CompanyCredit',
    CompanyCreditName: 'Company Name',
    CompanyCreditLimit: 'credit Limit',

    venueTitle: 'Venue',
    venueName: 'Name',
    venueAvailability: 'Availability',
    venueLength: 'Length',
    venueHeight: 'Height',
    venueWidth: 'Width',
    venueArea: 'Area',
    venueExcessGuestRate: 'Excess guest rate',
    venueExcessGuest: 'Excess guest',
    venueVenueDescription: 'Venue Description',
    venueListTitle: 'Venue List',
    venueListNewVenue: 'New Venue',
    venueListTaxCode: 'Tax code',
    venueListVenueType: 'Venue type',
    venueListAvailable: 'Available',
    editText: 'Edit',
    updateText: 'Update',
    createText: 'Create',
    // common_button_back: 'Back',
    deleteText: 'Delete',
    confirmDeletedText: 'Are you sure! You want to Delete?',
    closeText: 'Close',

    // newTranslate
    common_help_title: 'Help',
    common_button_back: 'Back',
    common_button_save: 'Save',
    common_button_update: 'Update',
    addOn_button_add: 'New Add On',
    addOn_column_category: 'AddOn Category',
    addOn_column_delete: 'Delete',
    addOn_column_edit: 'Edit',
    addOn_column_maxCount: 'Max Count',
    addOn_column_name: 'AddOn Name',
    addOn_column_price: 'Price',
    addOn_column_taxCode: 'Tax Code',
    addOn_column_type: 'AddOn Type',
    addOn_text_category: 'AddOn category',
    addOn_text_image: 'AddOn Image',
    addOn_text_name: 'AddOn name',
    addOn_text_price: 'Price',
    addOn_text_taxCode: 'Tax code',
    addOn_text_type: 'AddOn type',
    addOn_title_add: 'Add AddOns',
    addOn_title_edit: 'Edit AddOns',
    addOn_title_list: 'AddOns List',
    addOn_title_maxCount: 'Max count',
    bookings_button_cancel: 'Cancel Booking',
    bookings_button_processRefund: 'Process Refund',
    bookings_button_confirm: 'Confirm Booking',
    bookings_button_edit: 'Edit Booking',
    bookings_buuton_addNew: 'Add New Booking',
    bookings_placeholder_selectVenues: 'Select Venues',
    bookings_text_showCancelledEvents: 'Show Cancelled  Events',
    bookingDetails_text_addOns: 'Add Ons',
    bookingDetails_text_bookingRef: 'Booking Reference',
    bookingDetails_text_confirmationBy: 'booking Confirmation By',
    bookingDetails_text_gst: 'GST Amount',
    bookingDetails_text_guestCharges: 'Guest Charges',
    bookingDetails_text_hallRental: 'Hall Rental',
    bookingDetails_text_additional_comment: 'Additional Comment',
    bookingDetails_text_pricePerPerson: 'Price Per Person',
    bookingDetails_text_rooms: 'Rooms',
    bookingDetails_text_status: 'Booking Status',
    bookingDetails_text_total: 'Total Amount',
    bookingDetails_text_totalAddOnCost: 'Total Addon Cost',
    bookingDetails_text_totalAddOnTax: 'Total Addon Tax',
    bookingDetails_text_totalRoomCost: 'Total Room Cost',
    bookingDetails_text_totalRoomServiceCharge: 'Total Room Service Charges',
    bookingDetails_text_totalRoomTax: 'Total Room Tax',
    bookingDetails_text_totalServiceCharges: 'Total Service Charge',
    bookingDetails_text_addOn_name: 'Name',
    bookingDetails_text_addOn_type: 'Type',
    bookingDetails_text_addOn_quantity: 'Quantity',
    bookingDetails_text_addOn_price: 'Price',
    bookingDetails_text_room_type: 'Room Type',
    bookingDetails_text_room_startDate: 'Start date',
    bookingDetails_text_room_endDate: 'End date',
    bookingDetails_text_room_noOfRoom: 'No. of rooms',
    bookingMessage_column_edit: 'Edit',
    bookingMessage_column_type: 'Booking Message Type',
    bookingMessage_title_edit: 'Update Booking Message',
    bookingMessage_title_list: 'Booking Message List',
    changePassword_text_confirmPassword: 'Confirm Password',
    changePassword_text_newPassword: 'New Password',
    changePassword_text_oldPassword: 'Old Password',
    changePassword_title_text: 'Confirm Password',
    changePassword_text_changePassword: 'Change Password',
    companyCredit_button_add: 'Add Company Credit',
    companyCredit_column_companyName: 'Company Name',
    companyCredit_column_creditLimit: 'Credit Limit',
    companyCredit_column_delete: 'Delete',
    companyCredit_column_edit: 'Edit',
    companyCredit_text_companyName: 'Company Name',
    companyCredit_text_creditLimit: 'Credit Limit',
    companyCredit_title_add: 'Add Company Credit',
    companyCredit_title_edit: 'Edit Company Credit',
    companyCredit_title_list: 'Company Credit',
    customerDetails_text_address: 'Address',
    customerDetails_text_city: 'City',
    customerDetails_text_country: 'Country',
    customerDetails_text_name: 'Customer Name',
    customerDetails_text_pinCode: 'Pin Code',
    customerDetails_text_state: 'State',
    customerDetails_title_email: 'Email',
    customerDetails_title_phone: 'Phone Number',

    customermgmt_button_add: 'New Customer',
    customermgmt_button_export: 'Export',
    customermgmt_column_action: 'Action',
    customermgmt_column_email: 'Email',
    customermgmt_column_name: 'Name',
    customermgmt_column_phone: 'Phone Number',
    customermgmt_column_role: 'Role',
    customermgmt_filter_placeholder: 'Filter By Name',
    customermgmt_list_title: 'Customer List',
    customermgmt_text_address: 'Address',
    customermgmt_text_city: 'City',
    customermgmt_text_company: 'Company Name',
    customermgmt_text_country: 'Country',
    customermgmt_text_designation: 'Designation',
    customermgmt_text_email: 'Email',
    customermgmt_text_firstName: 'First Name',
    customermgmt_text_gst: 'GST',
    customermgmt_text_language: 'Language',
    customermgmt_text_lastName: 'Last Name',
    customermgmt_text_phone: 'Phone No',
    customermgmt_text_pinCode: 'Pin Code',
    customermgmt_text_role: 'Role',
    customermgmt_text_state: 'State',
    customermgmt_title_add: 'Add Cusomer',
    customermgmt_title_edit: 'Edit Customer',
    dashboard_title_text: 'Dashboard',
    eventType_button_add: 'New Event Type',
    eventType_column_delete: 'Delete',
    eventType_column_edit: 'Edit',
    eventType_column_type: 'Event Type',
    eventType_text_type: 'Event Type',
    eventType_title_add: 'Add Event Type',
    eventType_title_edit: 'Edit Event Type',
    eventType_title_list: 'Event Type',
    layout_button_add: 'Add Layout',
    layout_column_delete: 'Delete',
    layout_column_edit: 'Edit',
    layout_column_name: 'Name',
    layout_text_image: 'Layout Image',
    layout_text_name: 'Name',
    layout_title_add: 'Add Layout',
    layout_title_edit: 'Edit Layout',
    layout_title_list: 'Layout',
    mailTemplate_column_edit: 'Edit',
    mailTemplate_column_type: 'Mail Template Type',
    mailTemplate_text_subject: 'Subject',
    mailTemplate_title_edit: 'Update Mail Template',
    mailTemplate_title_list: 'Mail Template List',
    menu_text_bookings: 'Bookings',
    menu_text_companyCredit: 'Company Credit',
    menu_text_dashboard: 'Dashboard',
    menu_text_eventType: 'Event Type',
    menu_text_layout: 'Layout',
    menu_text_mailTemplate: 'Mail Template',
    menu_text_masterdata: 'Master Data',
    menu_text_org: 'Organization',
    menu_text_organization: 'Organization',
    menu_text_parentOrg: 'Parent Organization',
    menu_text_pricePackage: 'Price Package',
    menu_text_reports: 'Reports',
    menu_text_room: 'Room',
    menu_text_roomType: 'Room Type',
    menu_text_systemMessage: 'System Message',
    menu_text_taxCode: 'Tax Code',
    menu_text_templateSchedule: 'Template Schedule',
    menu_text_timeSlot: 'Time Slot',
    menu_text_usermgmt: 'User Management',
    menu_text_venue: 'Venue',
    menu_text_venueType: 'VenueType',
    menu_text_venueconfig: 'Venue Configuration',
    menu_text_venuemgmt: 'Venue Management',
    menu_title_addOns: 'Add Ons',
    menu_title_bookingmsg: 'Booking Message',
    menu_title_location: 'Location',
    bookings_button_preview: 'Preview',
    menu_text_budget: "Budget",
    menu_text_roles: "Roles",
    menu_text_customermgmt: "Customers",
    organization_column_edit: 'Edit',
    organization_column_id: 'Id',
    organization_column_name: 'Name',
    organization_text_address: 'Address',
    organization_text_confirmationBy: 'Booking Confirmation By',
    organization_text_contractTemplate: 'Contract Template',
    organization_text_description: 'Description',
    organization_text_dynamicContractTemplate: 'Dynamic Contract Template',
    organization_text_email: 'Email',
    organization_text_gst: 'GST Number',
    organization_text_image: 'Image',
    organization_text_latitude: 'Latitude',
    organization_text_longitude: 'Longitude',
    organization_text_minConfirmPercent: 'Min. Confirm Payment %',
    organization_text_name: 'Name',
    organization_text_newBookingLink: 'New Booking Link',
    organization_text_phone: 'Phone',
    organization_text_roomsAvailable: 'Room Available',
    organization_text_location: 'Location',
    organization_text_serviceChargePercent: 'Service Charge %',
    organization_text_slotOneBeforeDays: 'Payment Slot One Before Days',
    organization_text_slotOnePercent: 'Payment Slot One %',
    organization_text_slotThreeBeforeDays: 'Full Payment Before Days',
    organization_text_slotThreePercent: 'Payment Slot Three %',
    organization_text_slotTwoBeforeDays: 'Payment Slot Two Before Days',
    organization_text_slotTwoPercent: 'Payment Slot Two %',
    organization_text_streetViewExp: 'Show Street View Experience',
    organization_title_list: 'Organization',
    organization_title_update: 'Update Organization',
    parentOrg_column_edit: 'Edit',
    parentOrg_column_id: 'Id',
    parentOrg_column_name: 'Name',
    parentOrg_text_eventType: 'Event Type',
    parentOrg_text_layout: 'Layout',
    parentOrg_text_location: 'Location',
    parentOrg_text_logo: 'Logo',
    parentOrg_text_theme: 'Theme',
    parentOrg_text_timeSlot: 'TimeSlot',
    parentOrg_text_venueType: 'Venue Type',
    parentOrg_title_list: 'Parent Organization',
    parentOrg_title_update: 'Update Parent Organization',
    paymentDetails_button_addNew: 'Add New Payment',
    paymentDetails_text_amountDue: 'Amount Due as on Date',
    paymentDetails_text_date: 'Payment Date',
    paymentDetails_text_location: 'Payment Location',
    paymentDetails_text_paymentOverdue: 'Payment Overdue',
    paymentDetails_text_reference: 'Payment Reference',
    paymentDetails_text_totalPrice: 'Total Price',
    paymentDetails_title_method: 'Payment Method',
    paymentDetails_title_paid: 'Amount Paid',
    pricePackage_button_add: 'New Price Package',
    pricePackage_column_delete: 'Delete',
    pricePackage_column_edit: 'Edit',
    pricePackage_column_name: 'Package Name',
    pricePackage_text_name: 'Package Name',
    pricePackage_title_add: 'Add Price Package',
    pricePackage_title_edit: 'Edit Price Package',
    pricePackage_title_list: 'Price Package',
    reports_button_search: 'Search',
    reports_text_budgets: 'Planned versus Actuals',
    reports_text_cancelled: 'Cancelled',
    reports_text_confirmed: 'Confirmed',
    reports_text_consolidatedBoB: 'Consolidated Business on Books',
    reports_text_endDate: 'End Date',
    reports_text_eventTypeBoB: 'Event Type wise Business on Books',
    reports_text_reserved: 'Reserved',
    reports_text_startDate: 'Start Date',
    reports_text_timeWiseBoB: 'Time wise Business on Books',
    reports_text_timeWiseOccupancy: 'Time wise Venue Occupancy Percentage',
    reports_text_venueBoB: 'Venue wise Business on Books',
    reports_text_waitlisted: 'Waitlisted',
    reports_title_text: 'Reports',
    rolesmgmt_button_add: 'New Role',
    rolesmgmt_column_delete: 'Delete',
    rolesmgmt_column_description: 'Description',
    rolesmgmt_column_edit: 'Edit',
    rolesmgmt_column_name: 'Name',
    rolesmgmt_column_privilege: 'Privilege',
    rolesmgmt_column_privilegeName: 'Privilege Name',
    rolesmgmt_text_name: 'Role Name',
    rolesmgmt_title_add: 'Add Role',
    rolesmgmt_title_edit: 'Edit Role',
    rolesmgmt_title_privilege: 'Privileges',
    rolesmgmt_title_roles: 'Roles List',
    roomType_button_add: 'Add Room Type',
    roomType_column_delete: 'Delete',
    roomType_column_edit: 'Edit',
    roomType_column_type: 'Room Type',
    roomType_text_type: 'Room Type',
    roomType_title_add: 'Add Room Type',
    roomType_title_edit: 'Edit Room Type',
    roomType_title_list: 'Room Type',
    room_button_add: 'Add Room',
    room_column_delete: 'Delete',
    room_column_edit: 'Edit',
    room_column_price: 'Price',
    room_column_taxCode: 'Tax Code',
    room_column_type: 'Room Type',
    room_text_price: 'Price',
    room_text_serviceCharge: 'Room Service Charge',
    room_text_taxCode: 'Tax Code',
    room_text_type: 'Room Type',
    room_title_add: 'Add Room',
    room_title_edit: 'Edit Room',
    room_title_list: 'Room',
    systemMessage_column_edit: 'Edit',
    systemMessage_column_type: 'System Message Type',
    systemMessage_title_edit: 'Update System Message',
    systemMessage_title_list: 'System Message List',
    tabs_text_bookingDetails: 'Booking Details',
    tabs_text_customerDetails: 'Customer Details',
    tabs_text_paymentDetails: 'Payment Details',
    tabs_text_venueDetails: 'Venue Details',
    taxCode_button_add: 'Add Tax Code',
    taxCode_column_delete: 'Delete',
    taxCode_column_edit: 'Edit',
    taxCode_column_name: 'Name',
    taxCode_column_percentage: 'Percentage',
    taxCode_text_name: 'Name',
    taxCode_text_percentage: 'Percentage',
    taxCode_title_add: 'Add Tax Code',
    taxCode_title_edit: 'Edit Tax Code',
    taxCode_title_list: 'Tax Code',
    templateSchedule_column_edit: 'Edit',
    templateSchedule_column_name: 'Number of Days',
    templateSchedule_column_type: 'Type',
    templateSchedule_text_numberOfDays: 'Number of Days',
    templateSchedule_title_edit: 'Update Template Schedule',
    templateSchedule_title_list: 'Template Schedule',
    timeSlot_button_add: 'Add Time Slot',
    timeSlot_column_delete: 'Delete',
    timeSlot_column_edit: 'Edit',
    timeSlot_column_endTime: 'End Time',
    timeSlot_column_name: 'Name',
    timeSlot_column_partOfDay: 'Part Of Day',
    timeSlot_column_startTime: 'Start Time',
    timeSlot_text_endTime: 'End Time',
    timeSlot_text_fullDay: 'Full Day',
    timeSlot_text_multiDay: 'Multi Day',
    timeSlot_text_name: 'Name',
    timeSlot_text_partOfDay: 'Part Of Day',
    timeSlot_text_startTime: 'Start Time',
    timeSlot_title_add: 'Add Time Slot',
    timeSlot_title_edit: 'Edit Time Slot',
    timeSlot_title_list: 'Time Slot',

    updateProfile_text_address: 'Address',
    updateProfile_text_city: 'City',
    updateProfile_text_companyName: 'Company Name',
    updateProfile_text_country: 'Country',
    updateProfile_text_designation: 'Designation',
    updateProfile_text_email: 'Email',
    updateProfile_text_firstName: 'First Name',
    updateProfile_text_gst: 'GST',
    updateProfile_text_language: 'Language',
    updateProfile_text_lastName: 'Last Name',
    updateProfile_text_phoneNumber: 'Phone Number',
    updateProfile_text_pinCode: 'Pin Code',
    updateProfile_text_role: 'Role',
    updateProfile_text_state: 'State',
    updateProfile_title_text: 'User Profile',
    userprofile_button_changePassword: 'Change Password',
    userprofile_button_updateProfile: 'Update Profile',
    userprofile_title_text: 'Welcome to Book Banquet Room',
    usermgmt_button_add: 'New User',
    usermgmt_button_export: 'Export',
    usermgmt_column_action: 'Action',
    usermgmt_column_email: 'Email',
    usermgmt_column_name: 'Name',
    usermgmt_column_phone: 'Phone Number',
    usermgmt_column_role: 'Role',
    usermgmt_filter_placeholder: 'Filter By Name',
    usermgmt_list_title: 'User List',
    usermgmt_text_address: 'Address',
    usermgmt_text_city: 'City',
    usermgmt_text_company: 'Company Name',
    usermgmt_text_country: 'Country',
    usermgmt_text_designation: 'Designation',
    usermgmt_text_email: 'Email',
    usermgmt_text_firstName: 'First Name',
    usermgmt_text_gst: 'GST',
    usermgmt_text_language: 'Language',
    usermgmt_text_lastName: 'Last Name',
    usermgmt_text_phone: 'Phone No',
    usermgmt_text_pinCode: 'Pin Code',
    usermgmt_text_role: 'Role',
    usermgmt_text_state: 'State',
    usermgmt_title_add: 'Add User',
    usermgmt_title_edit: 'Edit User',
    venueConfig_attributes_bookingPolicy: 'Booking Policy',
    venueConfig_attributes_contractTemplate: 'Contract Template',
    venueConfig_attributes_description: 'Description',
    venueConfig_attributes_floorPlan: 'Floor Plan',
    venueConfig_attributes_priceDetails: 'Price Details',
    venueConfig_attributes_priceSummary: 'Price Summary',
    venueConfig_attributes_terms: 'Terms',
    venueConfig_button_new: 'New Venue Configuration',
    venueConfig_column_available: 'Available',
    venueConfig_column_confirmationBy: 'Confirmation By',
    venueConfig_column_delete: 'Delete',
    venueConfig_column_copy: 'Copy',
    venueConfig_column_edit: 'Edit',
    venueConfig_column_eventType: 'Event Type',
    venueConfig_column_tentative: 'Tentative Allowed',
    venueConfig_column_timeSlot: 'Time Slot',
    venueConfig_column_venue: 'Venue',
    venueConfig_general_add: 'Add',
    venueConfig_general_availability: 'Availability',
    venueConfig_general_eventType: 'Event Type',
    venueConfig_general_holdDays: 'Hold Days',
    venueConfig_general_maxCapacity: 'MaxBooking Capacity',
    venueConfig_general_minDays: 'Minimum Days Before Booking',
    venueConfig_general_minRevenue: 'Minimum Revenue',
    venueConfig_general_tentative: 'Tentative Booking Allowed',
    venueConfig_general_unavailabilityEnd: 'Unavailability End Date',
    venueConfig_general_unavailabilityStart: 'Unavailability Start Date',
    venueConfig_general_venue: 'Venue',
    venueConfig_mapping_addons: 'Add Ons',
    venueConfig_mapping_buttonAdd: 'Add',
    venueConfig_mapping_layout: 'Layout Selection',
    venueConfig_mapping_maxCapacity: 'Max Capacity',
    venueConfig_mapping_minCapacity: 'Min Capacity',
    venueConfig_mapping_tableDelete: 'Delete',
    venueConfig_mapping_tableLayouts: 'Layouts',
    venueConfig_mapping_tableMaxCapacity: 'Max. Capacity',
    venueConfig_mapping_tableMinCapacity: 'Min. Capacity',
    venueConfig_mapping_timeslots: 'TimeSlots',
    venueConfig_payment_bookingConfirmation: 'Booking Confirmation',
    venueConfig_payment_minPercent: 'Min Confirmation Percentage',
    venueConfig_payment_serviceChargePercent: 'Service Charge Percentage',
    venueConfig_payment_slotOneBeforeDays: 'Payment Slot One Before Days',
    venueConfig_payment_slotOnePercent: 'Payment Slot One Percentage',
    venueConfig_payment_slotThreeBeforeDays: 'Full Payment Before Days',
    venueConfig_payment_slotThreePercent: 'Payment Slot Three Percentage',
    venueConfig_payment_slotTwoBeforeDays: 'Payment Slot Two Before Days',
    venueConfig_payment_slotTwoPercent: 'Payment Slot Two Percentage',
    venueConfig_price_buttonAddPricePackage: 'Add',
    venueConfig_price_buttonPriceOverride: 'Add',
    venueConfig_price_columnDelete: 'Delete',
    venueConfig_price_columnPricePackage: 'Price Package',
    venueConfig_price_columnPricePerPerson: 'Price Per Person',
    venueConfig_price_endDate: 'End Date',
    venueConfig_price_pricePackage: 'Price Package',
    venueConfig_price_pricePerPerson: 'Price Per Person',
    venueConfig_price_priority: 'Priority',
    venueConfig_price_startDate: 'Start Date',
    venueConfig_price_titlePriceOverride: 'Price Override',
    venueConfig_price_titlePricePackage: 'Price Package',
    venueConfig_price_weekDays: 'Week Days',
    venueConfig_tabs_attributes: 'Localized Attributes',
    venueConfig_tabs_general: 'General',
    venueConfig_tabs_mapping: 'Mapping',
    venueConfig_tabs_payment: 'Payment',
    venueConfig_tabs_price: 'Price',
    venueConfig_title_list: 'Venue Configuration',
    venueDetails_text_date: 'Date',
    venueDetails_text_description: 'Venue Description',
    venueDetails_text_eventType: 'Event Type',
    venueDetails_text_guests: 'No. of Guests',
    venueDetails_text_name: 'Venue Name',
    venueDetails_text_timeSlots: 'Time Slots',
    venueType_button_add: 'New Venue Type',
    venueType_column_delete: 'Delete',
    venueType_column_edit: 'Edit',
    venueType_column_venueType: 'Venue Type',
    venueType_text_venueType: 'Venue Type',
    venueType_title_add: 'Create Venue Type',
    venueType_title_edit: 'Edit Venue Type',
    venueType_title_list: 'Venue Type',

    venue_button_new: 'New Venue',
    venue_column_available: 'Available',
    venue_column_delete: 'Delete',
    venue_column_edit: 'Edit',
    venue_column_excessGuestPercent: 'Excess Guest %',
    venue_column_excessGuestRatePercent: 'Excess Guest Rate %',
    venue_column_name: 'Name',
    venue_column_taxCode: 'Tax Code',
    venue_list_title: 'Venue List1',
    venue_text_area: 'Area',
    venue_text_availability: 'Availability',
    venue_text_description: 'Venue Description',
    venue_text_excessGuestPercent: 'Excess guest % ',
    venue_text_excessGuestRatePercent: 'Excess guest rate %',
    venue_text_height: 'Height',
    venue_text_length: 'Length',
    venue_text_media: 'Venue Media',
    venue_text_name: 'Name',
    venue_text_width: 'Width',
    venue_title_create: 'Create Venue',
    venue_title_edit: 'Edit Venue',
    venue_title_taxCode: 'Tax code',
    venue_title_venueType: 'Venue type',

    budget_button_add: 'New Budget',
    budget_column_delete: 'Delete',
    budget_column_edit: 'Edit',
    budget_column_start_date: 'Start Date',
    budget_column_end_date: 'End Date',
    budget_column_target: 'Target',
    budget_column_name: 'Budget Name',
    budget_text_name: 'Budget Name',
    budget_text_target: 'Target',
    budget_text_start_date: 'Start Date',
    budget_text_end_date: 'End Date',
    budget_title_add: 'Add Budget',
    budget_title_edit: 'Edit Budget',
    budget_title_list: 'Budget',

    location_button_add: 'New Location',
    location_column_delete: 'Delete',
    location_column_edit: 'Edit',
    location_column_name: 'Name',
    location_title_add: 'Add Location',
    location_title_edit: 'Edit Location',
    location_title_list: 'Locations',
    location_text_name: 'Name',
    Dashboard: 'Dashboard',
    common_required_message: 'Fields marked with * are mandatory',
};
