# Stage 1: Build Stage
FROM node:20 AS build

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json ./

# Install dependencies
RUN npm install babel-runtime
RUN npm install

# Copy the entire application code to the container
COPY . .
ARG REACT_APP_API_BASE_URL
ARG REACT_APP_BASE_NAME
ARG REACT_APP_BASE_URL
ARG REACT_APP_WP_BASE_URL
ARG REACT_APP_RELEASE
ARG REACT_APP_API_BASE_URL_FANTASY
ARG REACT_APP_MEDIA_URL

# Set environment variables
ENV REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL}
ENV REACT_APP_BASE_NAME=${REACT_APP_BASE_NAME}
ENV REACT_APP_BASE_URL=${REACT_APP_BASE_URL}
ENV REACT_APP_WP_BASE_URL=${REACT_APP_WP_BASE_URL}
ENV REACT_APP_RELEASE=${REACT_APP_RELEASE}
ENV REACT_APP_API_BASE_URL_FANTASY=${REACT_APP_API_BASE_URL_FANTASY}
ENV REACT_APP_MEDIA_URL=${REACT_APP_MEDIA_URL}
# Build the React app for production
RUN npm run build

# Stage 2: Production Stage
FROM nginx:latest

# Set the working directory for Nginx
WORKDIR /usr/share/nginx/html

# Remove default Nginx static assets
RUN rm -rf ./*

# Copy the build output from the previous stage to Nginx
COPY --from=build /app/build .

# Expose the default Nginx HTTP port


# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]
