import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
  TableSortLabel,
  Chip,
} from "@mui/material";
import {
  LocalizationProvider,
  DesktopDatePicker,
  MobileDateTimePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import TodayIcon from "@mui/icons-material/Today";
import moment from "moment-timezone";
import DateFnsUtils from "@date-io/date-fns";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../src/images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import Select from "react-select";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { ActionMessage, Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import { fetchFromStorage } from "../../library/utilities";
import { identifiers } from "../../library/common/constants";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";
import { Editor } from "react-draft-wysiwyg";
import {
  EditorState,
  convertToRaw,
  ContentState,
  AtomicBlockUtils,
  convertFromHTML,
} from "draft-js";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import _, { max } from "lodash";
import FileUploader from "../../library/common/components/FileUploader";
import { config } from "../../helpers/config";
import { URLS } from "../../library/common/constants";

import "../TeamSport/teamsport.scss";
import "./news.scss";
import ImageUploader from "./ImageUploader";
import he from "he";
import { SortTwoTone } from "@mui/icons-material";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import { GettyEmbed } from "../../helpers/common";
import AsyncSelect from "../../components/common/AsyncSelect";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const newsStatusOption = [
  {
    label: "all",
    value: 0,
  },

  {
    label: "published",
    value: 1,
  },
  {
    label: "draft",
    value: 2,
  },
];
const featuredOption = [
  {
    label: "All",
    value: 0,
  },

  {
    label: "Featured",
    value: 1,
  },
];
const newsModalStatusOption = [
  {
    label: "published",
    value: 1,
  },
  {
    label: "draft",
    value: 2,
  },
];
class NewsArticle extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      isTagLoading: false,
      isRealatedLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      articleValues: {
        articleTitle: "",
        articleSubTitle: "",
        NewsCategoryId: "",
        NewsCategorySportId: "",
        startTime: null,
        body: "",
        tag: [],
        relatedCategory: [],
        relatedArticle: [],
        topStory: "No",
        featuredArticle: "No",
        homePageArticles: "No",
        gettyImages: "No",
        alt: "",
        id: "",
        author: "",
        publisherName: "",
        NewsProviderId: null,
        tagTitle: "",
      },
      articlelist: [],
      articleCount: 0,
      errorTitle: "",
      errorInitialTitle: "",
      errorNewsCategory: "",
      errorStartTime: "",
      errorTag: "",
      errorRelatedCategory: "",
      errorRelatedArticle: "",
      errorTopStory: "",
      errorfeaturedArticle: "",
      errorHomeArticle: "",
      errorAlt: "",
      errorEmbedCode: "",
      search: "",
      categoryCount: 0,
      categoryOffset: 0,
      categorylist: [],
      isCategorySearch: "",
      searchCatogoryCount: 0,
      searchCategoryPage: 0,
      searchCategory: [],
      isCategoryLoading: false,
      editorState: EditorState.createEmpty(),
      tagList: [],
      tagCount: 0,
      tagOffset: 0,
      searchTag: [],
      searchtagCount: 0,
      searchtagOffset: 0,
      isTagSearch: "",
      relatedCategoryList: [],
      relatedCategoryCount: 0,
      relatedCategoryOffset: 0,
      searchRelatedCategory: [],
      searchRelatedCategoryCount: 0,
      searchRelatedCategoryOffset: 0,
      isRelatedCategorySearch: "",
      RelatedArticleList: [],
      image: [],
      authorImage: [],
      uploadImage: "",
      authorUploadImage: "",
      filterDate: null,
      externalCategoryCount: 0,
      externalCategoryOffset: 0,
      externalCategorylist: [],
      isExternalCategorySearch: "",
      externalSearchCatogoryCount: 0,
      externalSearchCategoryPage: 0,
      externalSearchCategory: [],
      isExternalCategoryLoading: false,
      selectCategory: "",
      newsTypeOption: [],
      isProviderLoading: false,
      selectedNewsStatus: null,
      selectedModalNewsStatus: null,
      sortType: "id",
      sortLabelid: false,
      sortTitle: true,
      sortCategory: true,
      sortAuthor: true,
      sortStatus: true,
      sortCreateDate: true,
      sortUpdateDate: true,
      isViewImgModalOpen: false,
      isViewImgData: {},
      isCreateTag: false,
      startDateOpen: false,
      selectedFeaturedOption: null,
      content: "",
      oldDate: null,
      embedCode: "", // Add this new state variable
      selectedTournaments: [],
      tournamentOffset: 0,
      tournamentLimit: 20,
      tournamentSearch: "",
      tournamentOptions: [],
      tournamentHasMore: true,
      tournamentLoading: false,
      // Players state
      playerOptions: [],
      selectedPlayers: [],
      playerOffset: 0,
      playerLimit: 20,
      playerSearch: "",
      playerHasMore: true,
      playerLoading: false,
      // Teams state
      teamOptions: [],
      selectedTeams: [],
      teamOffset: 0,
      teamLimit: 20,
      teamSearch: "",
      teamHasMore: true,
      teamLoading: false,
      // Racing state
      horseOptions: [],
      selectedHorses: [],
      horseOffset: 0,
      horseLimit: 20,
      horseSearch: "",
      horseHasMore: true,
      horseLoading: false,

      jockeyOptions: [],
      selectedJockeys: [],
      jockeyOffset: 0,
      jockeyLimit: 20,
      jockeySearch: "",
      jockeyHasMore: true,
      jockeyLoading: false,

      driverOptions: [],
      selectedDrivers: [],
      driverOffset: 0,
      driverLimit: 20,
      driverSearch: "",
      driverHasMore: true,
      driverLoading: false,

      trainerOptions: [],
      selectedTrainers: [],
      trainerOffset: 0,
      trainerLimit: 20,
      trainerSearch: "",
      trainerHasMore: true,
      trainerLoading: false,

      greyhoundOptions: [],
      selectedGreyhounds: [],
      greyhoundOffset: 0,
      greyhoundLimit: 20,
      greyhoundSearch: "",
      greyhoundHasMore: true,
      greyhoundLoading: false,
    };
    // Define sport type constants
    this.IS_RACING = (sportId) =>
      sportId === 1 || sportId === 2 || sportId === 3;
    this.IS_SPORT = (sportId) => sportId && !this.IS_RACING(sportId);
  }

  componentDidMount() {
    this.fetchAllArticles(
      0,
      "",
      this.state?.filterDate,
      this?.state?.selectCategory,
      null,
      null,
      this.state.sortType,
      false,
      ""
    );
    this.fetchAllExternalCategory(0);
    this.fetchAllNewsProvider();

    // Load initial tournament options when component mounts if sport ID is available
    if (this.state?.articleValues?.NewsCategorySportId) {
      this.fetchAllTournaments(
        "",
        0,
        this.state.tournamentLimit,
        this.state.articleValues.NewsCategorySportId
      );
    }

    // Load initial data when component mounts if sport ID is available
    if (this.state?.articleValues?.NewsCategorySportId) {
      const SPORTSID = this.state.articleValues.NewsCategorySportId;

      if (this.IS_SPORT(SPORTSID)) {
        this.fetchAllTournaments("", 0, this.state.tournamentLimit, SPORTSID);
        this.fetchAllPlayers("", 0, this.state.playerLimit, SPORTSID);
        this.fetchAllTeams("", 0, this.state.teamLimit, SPORTSID);
      } else if (this.IS_RACING(SPORTSID)) {
        if (SPORTSID === 1 || SPORTSID === 2) {
          this.fetchAllHorses("", 0, this.state.horseLimit, SPORTSID);
          this.fetchAllTrainers("", 0, this.state.trainerLimit);
        }
        if (SPORTSID === 1) {
          this.fetchAllJockeys("", 0, this.state.jockeyLimit);
        }
        if (SPORTSID === 2) {
          this.fetchAllDrivers("", 0, this.state.driverLimit);
        }
        if (SPORTSID === 3) {
          this.fetchAllGreyhounds("", 0, this.state.greyhoundLimit, SPORTSID);
          this.fetchAllTrainers("", 0, this.state.trainerLimit);
        }
      }
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      let sortData =
        this?.state?.sortType === "id"
          ? this?.state?.sortLabelid
          : this?.state?.sortType === "title"
          ? this?.state?.sortTitle
          : this?.state?.sortType === "NewsCategoryId"
          ? this?.state?.sortCategory
          : this?.state?.sortType === "authors"
          ? this?.state?.sortAuthor
          : this?.state?.sortType === "status"
          ? this?.state?.sortStatus
          : this?.state?.sortType === "createdAt"
          ? this?.state?.sortCreateDate
          : this?.state?.sortUpdateDate;
      this.fetchAllArticles(
        this.state.offset,
        this.state?.search,
        this.state?.filterDate,
        this?.state?.selectCategory,
        this.state.selectedNewsType,
        this.state.selectedNewsStatus,
        this?.state?.sortType,
        sortData,
        this.state.selectedFeaturedOption
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllArticles(
        0,
        "",
        this.state?.filterDate,
        null,
        null,
        null,
        "id",
        false,
        ""
      );

      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
        filterDate: null,
        selectCategory: "",
      });
    }
    // Clean up scripts when component unmounts or embed code changes
    if (prevState.embedCode !== this.state.embedCode) {
      // Remove any previously added scripts
      const scripts = document.querySelectorAll("script[data-getty-embed]");
      scripts.forEach((script) => script.remove());
    }

    // Load tournaments when sport ID changes
    if (
      prevState?.articleValues?.NewsCategorySportId !==
      this.state?.articleValues?.NewsCategorySportId
    ) {
      if (this.state?.articleValues?.NewsCategorySportId) {
        this.fetchAllTournaments(
          "",
          0,
          this.state.tournamentLimit,
          this.state.articleValues.NewsCategorySportId
        );
      }
    }

    // Load data when sport ID changes
    if (
      prevState?.articleValues?.NewsCategorySportId !==
      this.state?.articleValues?.NewsCategorySportId
    ) {
      if (this.state?.articleValues?.NewsCategorySportId) {
        const SPORTSID = this.state.articleValues.NewsCategorySportId;

        if (this.IS_SPORT(SPORTSID)) {
          this.fetchAllTournaments("", 0, this.state.tournamentLimit, SPORTSID);
          this.fetchAllPlayers("", 0, this.state.playerLimit, SPORTSID);
          this.fetchAllTeams("", 0, this.state.teamLimit, SPORTSID);
        } else if (this.IS_RACING(SPORTSID)) {
          if (SPORTSID === 1 || SPORTSID === 2) {
            this.fetchAllHorses("", 0, this.state.horseLimit, SPORTSID);
            this.fetchAllTrainers("", 0, this.state.trainerLimit);
          }
          if (SPORTSID === 1) {
            this.fetchAllJockeys("", 0, this.state.jockeyLimit);
          }
          if (SPORTSID === 2) {
            this.fetchAllDrivers("", 0, this.state.driverLimit);
          }
          if (SPORTSID === 3) {
            this.fetchAllGreyhounds("", 0, this.state.greyhoundLimit, SPORTSID);
            this.fetchAllTrainers("", 0, this.state.trainerLimit);
          }
        }
      }
    }
  }
  handleEditorChange = (editorState) => {
    const { articleValues } = this.state;
    this.setState({ editorState });
    let string = draftToHtml(convertToRaw(editorState.getCurrentContent()));
    this.setState({
      articleValues: {
        ...articleValues,
        body: string,
      },
    });
  };

  async fetchAllArticles(
    page,
    searchvalue,
    filterDate,
    selectCategory,
    selectedNewsType,
    selectedNewsStatus,
    type,
    order,
    isFeatured
  ) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      let passApi = `v2/news/admin/getallnewsarticles?limit=${rowPerPage}&offset=${page}&search=${searchvalue}&startDate=${
        filterDate === null ? "" : filterDate
      }&NewsCategoryId=${
        selectCategory === 0 ? "" : selectCategory
      }&timeZone=${timezone}&NewsProviderId=${
        selectedNewsType ? selectedNewsType : ""
      }&status=${
        selectedNewsStatus && selectedNewsStatus !== "All"
          ? selectedNewsStatus
          : ""
      }&sort=${type ? type : ""}&orderBy=${order ? "ASC" : "DESC"}&featured=${
        isFeatured === "Featured" ? true : ""
      }`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          articlelist: data?.result?.data,
          isLoading: false,
          articleCount: data?.result?.count,
          offset: page,
          selectedNewsType: selectedNewsType ? selectedNewsType : null,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  async fetchAllTournaments(search = "", offset = 0, limit = 20, SPORTSID) {
    try {
      if (SPORTSID) {
        this.setState({ tournamentLoading: true });

        const { status, data } = await axiosInstance.get(
          `sports/tournament?SportId=${SPORTSID}&limit=${limit}&offset=${offset}&search=${search}`
        );

        if (status === 200 && data) {
          // Transform the data to the format expected by AsyncSelect
          const options = data?.result?.rows?.map((tournament) => ({
            value: tournament.id,
            label: tournament.name,
          }));

          // Check if we have more data to load
          const hasMore = options.length === limit;

          // If this is a new search or first load, replace options
          // Otherwise, append to existing options
          const updatedOptions =
            offset === 0
              ? options
              : [...this.state.tournamentOptions, ...options];

          this.setState({
            tournamentOptions: updatedOptions,
            tournamentHasMore: hasMore,
            tournamentOffset: offset + limit,
            tournamentSearch: search,
          });

          return updatedOptions;
        }

        return this.state.tournamentOptions;
      }
    } catch (error) {
      console.error("Error fetching tournaments:", error);
      return this.state.tournamentOptions;
    } finally {
      this.setState({ tournamentLoading: false });
    }
  }

  async fetchAllPlayers(search = "", offset = 0, limit = 20, SPORTSID) {
    try {
      if (SPORTSID) {
        this.setState({ playerLoading: true });

        const { status, data } = await axiosInstance.get(
          `sports/player?SportId=${SPORTSID}&limit=${limit}&offset=${offset}&search=${search}`
        );

        if (status === 200 && data) {
          const options = data?.result?.rows?.map((player) => ({
            value: player.id,
            label: player.name,
          }));

          const hasMore = options.length === limit;
          const updatedOptions =
            offset === 0 ? options : [...this.state.playerOptions, ...options];

          this.setState({
            playerOptions: updatedOptions,
            playerHasMore: hasMore,
            playerOffset: offset + limit,
            playerSearch: search,
          });

          return updatedOptions;
        }
      }
      return this.state.playerOptions;
    } catch (error) {
      console.error("Error fetching players:", error);
      return this.state.playerOptions;
    } finally {
      this.setState({ playerLoading: false });
    }
  }

  async fetchAllTeams(search = "", offset = 0, limit = 20, SPORTSID) {
    try {
      if (SPORTSID) {
        this.setState({ teamLoading: true });

        const { status, data } = await axiosInstance.get(
          `sports/team?SportId=${SPORTSID}&limit=${limit}&offset=${offset}&search=${search}`
        );

        if (status === 200 && data) {
          const options = data?.result?.rows?.map((team) => ({
            value: team.id,
            label: team.name,
          }));

          const hasMore = options.length === limit;
          const updatedOptions =
            offset === 0 ? options : [...this.state.teamOptions, ...options];

          this.setState({
            teamOptions: updatedOptions,
            teamHasMore: hasMore,
            teamOffset: offset + limit,
            teamSearch: search,
          });

          return updatedOptions;
        }
      }
      return this.state.teamOptions;
    } catch (error) {
      console.error("Error fetching teams:", error);
      return this.state.teamOptions;
    } finally {
      this.setState({ teamLoading: false });
    }
  }

  handalValidate = () => {
    let { articleValues, embedCode } = this.state;

    let flag = true;
    if (articleValues?.articleTitle?.trim() === "") {
      flag = false;
      this.setState({
        errorTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTitle: "",
      });
    }
    // if (articleValues?.articleSubTitle === "") {
    //   flag = false;
    //   this.setState({
    //     errorInitialTitle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorInitialTitle: "",
    //   });
    // }
    if (!articleValues?.startTime) {
      flag = false;
      this.setState({
        errorStartTime: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartTime: "",
      });
    }
    if (!articleValues?.NewsCategoryId) {
      flag = false;
      this.setState({
        errorNewsCategory: "This field is mandatory",
      });
    } else {
      this.setState({
        errorNewsCategory: "",
      });
    }
    if (articleValues?.gettyImages === "Yes") {
      if (embedCode?.trim() === "") {
        flag = false;
        this.setState({
          errorEmbedCode: "This field is mandatory",
        });
      } else {
        this.setState({
          errorEmbedCode: "",
        });
      }
    }
    // if (!articleValues?.tag?.length > 0) {
    //   flag = false;
    //   this.setState({
    //     errorTag: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorTag: "",
    //   });
    // }
    // if (!articleValues?.relatedArticle?.length > 0) {
    //   flag = false;
    //   this.setState({
    //     errorRelatedArticle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorRelatedArticle: "",
    //   });
    // }
    // if (!articleValues?.topStory) {
    //   flag = false;
    //   this.setState({
    //     errorTopStory: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorTopStory: "",
    //   });
    // }
    // if (!articleValues?.featuredArticle) {
    //   flag = false;
    //   this.setState({
    //     errorfeaturedArticle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorfeaturedArticle: "",
    //   });
    // }
    // if (!articleValues?.homePageArticles) {
    //   flag = false;
    //   this.setState({
    //     errorHomeArticle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorHomeArticle: "",
    //   });
    // }
    // if (!articleValues?.alt) {
    //   flag = false;
    //   this.setState({
    //     errorAlt: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorAlt: "",
    //   });
    // }

    return flag;
  };
  handeModleLoading = (status) => {
    this.setState({
      isLoading: status,
    });
  };
  handleSave = async () => {
    const fetchuser = localStorage.getItem("user");
    const userObj = JSON.parse(fetchuser);
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const {
        image,
        uploadImage,
        articleValues,
        authorImage,
        sortType,
        sortLabelid,
        sortTitle,
        sortCreateDate,
        sortCategory,
        sortAuthor,
        sortStatus,
        sortUpdateDate,
        embedCode,
        selectedTournaments,
        selectedPlayers,
        selectedTeams,
        selectedHorses,
        selectedJockeys,
        selectedDrivers,
        selectedTrainers,
        selectedGreyhounds,
      } = this.state;

      // Create tags array with the correct structure
      const tags = [];

      // Add tournament tags
      if (selectedTournaments && selectedTournaments.length > 0) {
        selectedTournaments.forEach((tournament) => {
          tags.push({
            title: tournament.label,
            type: "tournament",
            reference_id: tournament.value,
          });
        });
      }

      // Add team tags
      if (selectedTeams && selectedTeams.length > 0) {
        selectedTeams.forEach((team) => {
          tags.push({
            title: team.label,
            type: "team",
            reference_id: team.value,
          });
        });
      }

      // Add player tags
      if (selectedPlayers && selectedPlayers.length > 0) {
        selectedPlayers.forEach((player) => {
          tags.push({
            title: player.label,
            type: "player",
            reference_id: player.value,
          });
        });
      }

      // Add animal tags (horses and greyhounds)
      if (selectedHorses && selectedHorses.length > 0) {
        selectedHorses.forEach((horse) => {
          tags.push({
            title: horse.label,
            type: "animal",
            reference_id: horse.value,
          });
        });
      }

      if (selectedGreyhounds && selectedGreyhounds.length > 0) {
        selectedGreyhounds.forEach((greyhound) => {
          tags.push({
            title: greyhound.label,
            type: "animal",
            reference_id: greyhound.value,
          });
        });
      }

      // Add jockey tags
      if (selectedJockeys && selectedJockeys.length > 0) {
        selectedJockeys.forEach((jockey) => {
          tags.push({
            title: jockey.label,
            type: "jockey",
            reference_id: jockey.value,
          });
        });
      }

      // Add trainer tags
      if (selectedTrainers && selectedTrainers.length > 0) {
        selectedTrainers.forEach((trainer) => {
          tags.push({
            title: trainer.label,
            type: "trainer",
            reference_id: trainer.value,
          });
        });
      }

      // Add driver tags (as jockey type)
      if (selectedDrivers && selectedDrivers.length > 0) {
        selectedDrivers.forEach((driver) => {
          tags.push({
            title: driver.label,
            type: "jockey",
            reference_id: driver.value,
          });
        });
      }

      if (articleValues?.tag && articleValues?.tag.length > 0) {
        articleValues.tag.forEach((tag) => {
          tags.push({
            title: tag.label,
            type: "tag",
            reference_id: tag.value,
          });
        });
      }

      let tagIds = articleValues?.tag?.map((item) => {
        return item?.value;
      });
      let relatedArticleIds = articleValues?.relatedArticle?.map((item) => {
        return item?.value;
      });
      const relatedCategoryIds = articleValues?.relatedCategory?.map((item) => {
        return item?.value;
      });
      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "title"
          ? sortTitle
          : sortType === "NewsCategoryId"
          ? sortCategory
          : sortType === "authors"
          ? sortAuthor
          : sortType === "status"
          ? sortStatus
          : sortType === "createdAt"
          ? sortCreateDate
          : sortUpdateDate;

      let payload = {
        NewsCategoryId: articleValues?.NewsCategoryId,
        featuredArticles:
          articleValues?.featuredArticle === "Yes" ? true : false,
        topStories: articleValues?.topStory === "Yes" ? true : false,
        homePageArticles:
          articleValues?.homePageArticles === "Yes" ? true : false,
        rapidCreatedAt: articleValues?.startTime
          ? moment(articleValues?.startTime)
              .tz(timezone)
              .format("YYYY-MM-DD HH:mm:ss")
          : null,
        title: articleValues?.articleTitle,
        subTitle: articleValues?.articleSubTitle,
        tags: tags, // Add the new tags array to the payload
        newsCategory: relatedCategoryIds ? relatedCategoryIds : [],
        relatedArticles: relatedArticleIds ? relatedArticleIds : [],
        body: articleValues?.body,
        userId: userObj?.id,
        embeddedImageStatus:
          articleValues?.gettyImages === "Yes" ? true : false,
        embeddedImage: embedCode,
      };

      if (image?.length > 0) {
        let fileData = await this.setMedia(image[0]);

        if (fileData) {
          payload = {
            ...payload,
            mainMedia: {
              gallery: {
                alt: articleValues?.alt,
                url: config.mediaUrl + fileData?.image?.filePath,
              },
            },
          };
          this.setState({
            uploadImage: config.mediaUrl + fileData?.image?.filePath,
          });
        }
      }
      try {
        const { status } = await axiosInstance.post(
          `v2/news/admin/create/articles?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            authorImage: [],
            uploadImage: "",
            embedCode: "",
            authorUploadImage: "",
            isCreateTag: false,
            articleValues: { ...articleValues, tagTitle: "" },
          });
          this.fetchAllArticles(
            this.state.offset,
            this?.state?.search,
            this?.state?.filterDate,
            this?.state?.selectCategory,
            this?.state?.selectedNewsType,
            this?.state?.selectedNewsStatus,
            sortType,
            sortData,
            this.state.selectedFeaturedOption
          );
          this.setActionMessage(
            true,
            "Success",
            `News Article Created Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            authorImage: [],
            uploadImage: "",
            embedCode: "",
            authorUploadImage: "",
            isCreateTag: false,
            articleValues: { ...articleValues, tagTitle: "" },
            content: "",
          });
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          image: [],
          authorImage: [],
          uploadImage: "",
          embedCode: "",
          authorUploadImage: "",
          isCreateTag: false,
          articleValues: { ...articleValues, tagTitle: "" },
        });
      }
    }
  };

  handleUpdate = async () => {
    const {
      articleValues,
      selectedTournaments,
      selectedTeams,
      selectedPlayers,
      selectedHorses,
      selectedJockeys,
      selectedDrivers,
      selectedGreyhounds,
      selectedTrainers,
      selectedCategory,
      selectedModalNewsStatus,
      oldDate,
      embedCode,
      uploadImage,
      authorUploadImage,
    } = this.state;

    const tags = [
      ...selectedTournaments.map((item) => ({
        type: "tournament",
        reference_id: item.value,
        title: item.label,
      })),
      ...selectedTeams.map((item) => ({
        type: "team",
        reference_id: item.value,
        title: item.label,
      })),
      ...selectedPlayers.map((item) => ({
        type: "player",
        reference_id: item.value,
        title: item.label,
      })),
      ...selectedHorses.map((item) => ({
        type: "animal",
        reference_id: item.value,
        title: item.label,
      })),
      ...selectedJockeys.map((item) => ({
        type: "jockey",
        reference_id: item.value,
        title: item.label,
      })),
      ...selectedDrivers.map((item) => ({
        type: "driver",
        reference_id: item.value,
        title: item.label,
      })),
      ...selectedGreyhounds.map((item) => ({
        type: "animal",
        reference_id: item.value,
        title: item.label,
      })),
      ...selectedTrainers.map((item) => ({
        type: "trainer",
        reference_id: item.value,
        title: item.label,
      })),
      ...articleValues?.tag?.map((item) => ({
        title: item.label,
        type: "tag",
        reference_id: item.value,
      })),
    ];

    const payload = {
      title: articleValues.articleTitle,
      subTitle: articleValues.articleSubTitle,
      body: articleValues.body,
      NewsCategoryId: articleValues.NewsCategoryId,
      NewsProviderId: articleValues.NewsProviderId,
      status: selectedModalNewsStatus,
      rapidCreatedAt: oldDate,
      embeddedImage: embedCode,
      embeddedImageStatus: articleValues.gettyImages === "Yes",
      mainMedia: uploadImage
        ? {
            gallery: {
              url: uploadImage,
              alt: articleValues.alt,
            },
          }
        : null,
      publishedBy: authorUploadImage
        ? {
            logo: authorUploadImage,
          }
        : null,
      authors: articleValues.author
        ? [
            {
              name: articleValues.author,
            },
          ]
        : [],
      tags: tags,
      newsCategory:
        articleValues.relatedCategory?.map((item) => item.value) || [],
      relatedArticles:
        articleValues.relatedArticle?.map((item) => item.value) || [],
      featuredArticles: articleValues?.featuredArticle === "Yes" ? true : false,
      topStories: articleValues?.topStory === "Yes" ? true : false,
      homePageArticles:
        articleValues?.homePageArticles === "Yes" ? true : false,
    };

    try {
      const { status } = await axiosInstance.put(
        `v2/news/admin/articles/${articleValues.id}?timeZone=${timezone}`,
        payload
      );

      if (status === 200) {
        this.fetchAllArticles(
          this.state.offset,
          this.state.search,
          this.state.filterDate,
          this.state.selectCategory,
          this.state.selectedNewsType,
          this.state.selectedNewsStatus,
          this.state.sortType,
          this.state.selectedFeaturedOption
        );
        this.setActionMessage(
          true,
          "success",
          "News Article Updated Successfully"
        );
        this.toggleInputModal();
      }
    } catch (error) {
      console.error("Error updating article:", error);
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      articleValues: {
        articleTitle: "",
        articleSubTitle: "",
        id: "",
        NewsCategoryId: "",
        NewsCategorySportId: "",
        startTime: null,
        body: "",
        tag: [],
        relatedCategory: [],
        relatedArticle: [],
        topStory: null,
        featuredArticle: null,
        homePageArticles: null,
        gettyImages: null,
        alt: "",
        author: "",
        publisherName: "",
        tagTitle: "",
      },
      uploadImage: "",
      embedCode: "",
      authorUploadImage: "",
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTitle: "",
      errorInitialTitle: "",
      errorHomeRelation: "",
      errorNewsCategory: "",
      errorStartTime: "",
      errorTag: "",
      errorRelatedArticle: "",
      errorTopStory: "",
      errorfeaturedArticle: "",
      errorHomeArticle: "",
      errorAlt: "",
      errorEmbedCode: "",
      editorState: EditorState.createEmpty(),
      image: [],
      selectedNewsType: null,
      selectedNewsStatus: null,
      authorImage: [],
      selectedModalNewsStatus: null,
      isCreateTag: false,
      content: "",
      // Reset all tag-related states
      selectedTournaments: [],
      selectedTeams: [],
      selectedPlayers: [],
      selectedHorses: [],
      selectedJockeys: [],
      selectedDrivers: [],
      selectedGreyhounds: [],
      selectedTrainers: [],
      // Reset all options lists
      tournamentOptions: [],
      teamOptions: [],
      playerOptions: [],
      horseOptions: [],
      jockeyOptions: [],
      driverOptions: [],
      greyhoundOptions: [],
      trainerOptions: [],
    });
  };

  inputModal = (item, type) => async () => {
    console.log("Opening modal with item:", item);
    this.fetchAllCategory(0);
    this.fetchAllTag(0);

    // Set initial state with sport type if it's a sports article
    if (item?.NewsCategory?.SportId) {
      const sportId = item?.NewsCategory?.SportId;
      const IS_SPORT = sportId === 4 || sportId === 9 || sportId === 12;
      console.log(
        "Initial sport check - SportId:",
        sportId,
        "IS_SPORT:",
        IS_SPORT
      );

      if (IS_SPORT) {
        this.setState(
          {
            isInputModalOpen: true,
            articleValues: {
              ...this.state.articleValues,
              NewsCategorySportId: sportId,
              NewsCategoryId: item?.NewsCategoryId,
              NewsCategory: item?.NewsCategory,
            },
          },
          () => {
            console.log(
              "State after setting sport values:",
              this.state.articleValues
            );
          }
        );
      } else {
        this.setState({
          isInputModalOpen: true,
        });
      }
    } else {
      this.setState({
        isInputModalOpen: true,
      });
    }

    if (type === "edit") {
      console.log(
        "Edit mode - Current state before fetches:",
        this.state.articleValues
      );
      this.fetchRelatedArticle(item?.NewsCategoryId);
      this.fetchAllRelatedCategory(0, item?.NewsCategoryId);
      this.fetchSelectedCategory(item, item?.NewsCategoryId);
      console.log(
        "State after fetchSelectedCategory:",
        this.state.articleValues
      );

      // Check if it's a sports article and fetch initial data
      if (item?.NewsCategory?.SportId) {
        const sportId = item?.NewsCategory?.SportId;
        const IS_SPORT = sportId === 4 || sportId === 9 || sportId === 12;
        console.log(
          "Second sport check - SportId:",
          sportId,
          "IS_SPORT:",
          IS_SPORT
        );

        if (IS_SPORT) {
          // Fetch initial data for sports-related items
          this.fetchAllTeams("", 0, 20, sportId);
          this.fetchAllPlayers("", 0, 20, sportId);
          this.fetchAllTournaments("", 0, 20, sportId);
        }
      }

      if (item?.NewsProviderId === 2 || item?.NewsProviderId === 3) {
        const htmlString = he.decode(String(item?.body));
        if (typeof htmlString === "string") {
          this.setState({
            content: htmlString,
          });
        }
      }

      try {
        const { status, data } = await axiosInstance.get(
          `v2/news/admin/articles/${item?.id}`
        );
        if (status === 200) {
          // Setting Read Only Tags
          console.log("dataDatata", data);

          const newsTagRelations = data?.result?.NewsTagRelations?.map(
            (tag) => {
              return tag?.NewsTag;
            }
          );

          const allNewsTagOptions = newsTagRelations?.map((tag) => {
            return {
              label: tag?.title,
              value: tag?.id,
              type: tag?.type,
            };
          });
          var tournaments = allNewsTagOptions?.filter(
            (item) => item.type === "tournament"
          );
          var teams = allNewsTagOptions?.filter((item) => item.type === "team");
          var players = allNewsTagOptions?.filter(
            (item) => item.type === "player"
          );
          var animals = allNewsTagOptions?.filter(
            (item) => item.type === "animal"
          );
          var jockeys = allNewsTagOptions?.filter(
            (item) => item.type === "jockey"
          );
          var trainers = allNewsTagOptions?.filter(
            (item) => item.type === "trainer"
          );

          // Set state for AsyncSelect components
          // this.setState((prevState) => ({
          //   tournamentOptions: tournaments || [],
          //   teamOptions: teams || [],
          //   playerOptions: players || [],
          //   horseOptions: animals || [],
          //   jockeyOptions: jockeys || [],
          //   trainerOptions: trainers || [],
          // }));

          var selectedTags = data?.result?.NewsTagRelations?.map((obj) => {
            return {
              value: obj?.NewsTag?.id,
              label: obj?.NewsTag?.title,
            };
          });
          var selectedRelatedArticles = data?.result?.NewsRelatedArticles?.map(
            (obj) => {
              return {
                value: obj?.NewsArticle?.id,
                label: obj?.NewsArticle?.title,
              };
            }
          );
          var selectedRelatedCategory =
            await data?.result?.NewsCategoryArticles?.map((obj) => {
              return {
                value: obj?.NewsCategoryId,
                label: obj?.NewsCategory?.initialTitle,
              };
            });
          var topStories =
            data?.result?.NewsTopStories &&
            data?.result?.NewsTopStories?.length > 0;
          var featureArticles =
            data?.result?.NewsFeatureds &&
            data?.result?.NewsFeatureds?.length > 0;
          var homeArticles =
            data?.result?.NewsHomeRelations &&
            data?.result?.NewsHomeRelations?.length > 0;
        }
      } catch (err) {
        console.error("Error fetching article data:", err);
      }

      this.setState((prevState) => ({
        articleValues: {
          ...prevState.articleValues,
          articleTitle: item?.title,
          articleSubTitle: item?.subTitle,
          startTime: item?.rapidCreatedAt,
          body: item?.body,
          tag: selectedTags ? selectedTags : [],
          relatedCategory: selectedRelatedCategory
            ? selectedRelatedCategory
            : [],
          relatedArticle: selectedRelatedArticles
            ? selectedRelatedArticles
            : [],
          topStory: topStories === true ? "Yes" : "No",
          featuredArticle: featureArticles === true ? "Yes" : "No",
          homePageArticles: homeArticles === true ? "Yes" : "No",
          gettyImages: item?.embeddedImageStatus === true ? "Yes" : "No",
          alt: item?.mainMedia?.gallery
            ? item?.mainMedia?.gallery?.alt
            : item?.mainMedia?.[0]?.gallery
            ? item?.mainMedia?.[0]?.gallery.alt
            : "",
          id: item?.id,
          author: item?.authors?.[0]?.name,
          publisherName: item?.publishedBy?.name,
          NewsProviderId: item?.NewsProviderId,
        },
        selectedTournaments: tournaments || [],
        selectedTeams: teams || [],
        selectedPlayers: players || [],
        selectedHorses: animals || [],
        selectedJockeys: jockeys || [],
        selectedTrainers: trainers || [],
        embedCode: item?.embeddedImage,
        uploadImage: item?.mainMedia?.gallery
          ? item?.mainMedia?.gallery?.url
          : item?.mainMedia?.[0]?.gallery
          ? item?.mainMedia?.[0]?.gallery.url
          : "",
        authorUploadImage: item?.publishedBy?.logo,
        isEditMode: true,
        selectedModalNewsStatus: item?.status,
        oldDate: item?.rapidCreatedAt,
      }));
    } else {
      this.setState({
        articleValues: {
          articleTitle: "",
          articleSubTitle: "",
          id: "",
          NewsCategoryId: "",
          startTime: new Date(),
          body: "",
          tag: [],
          relatedCategory: [],
          relatedArticle: [],
          topStory: null,
          featuredArticle: null,
          homePageArticles: null,
          gettyImages: null,
          alt: "",
          author: "",
          publisherName: "",
          NewsProviderId: null,
        },
        uploadImage: "",
        embedCode: "",
        authorUploadImage: "",
        isEditMode: false,
        editorState: EditorState.createEmpty(),
        image: [],
        authorImage: [],
        selectedModalNewsStatus: null,
        content: "",
        // Reset all tag-related states
        selectedTournaments: [],
        selectedTeams: [],
        selectedPlayers: [],
        selectedHorses: [],
        selectedJockeys: [],
        selectedDrivers: [],
        selectedGreyhounds: [],
        selectedTrainers: [],
        // Reset all options lists
        tournamentOptions: [],
        teamOptions: [],
        playerOptions: [],
        horseOptions: [],
        jockeyOptions: [],
        driverOptions: [],
        greyhoundOptions: [],
        trainerOptions: [],
      });
    }
  };

  fetchSelectedCategory = (item, NewsCategoryId) => {
    console.log("fetchSelectedCategory called with:", { item, NewsCategoryId });
    console.log("Current state before update:", this.state.articleValues);

    this.setState(
      {
        selectedCategory: {
          value: item?.NewsCategory?.id,
          label: item?.NewsCategory?.title,
          SportId: item?.NewsCategory?.SportId,
        },
        articleValues: {
          ...this.state.articleValues,
          NewsCategoryId: item?.NewsCategory?.id,
          NewsCategory: item?.NewsCategory,
          NewsCategorySportId: item?.NewsCategory?.SportId,
        },
      },
      () => {
        console.log(
          "State after fetchSelectedCategory update:",
          this.state.articleValues
        );
      }
    );
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const {
      articleValues,
      sortType,
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
    } = this.state;

    // let sortData =
    //   sortType === "id"
    //     ? sortLabelid
    //     : sortType === "sponsorDate"
    //     ? sortLabelstartDate
    //     : sortLabelendDate;
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "title"
        ? sortTitle
        : sortType === "NewsCategoryId"
        ? sortCategory
        : sortType === "authors"
        ? sortAuthor
        : sortType === "status"
        ? sortStatus
        : sortType === "createdAt"
        ? sortCreateDate
        : sortUpdateDate;
    try {
      const passApi = `v2/news/admin/articles/${this.state.itemToDelete}`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllArticles(
            this.state.offset,
            this?.state?.search,
            this.state?.filterDate,
            this?.state?.selectCategory,
            this.state.selectedNewsType,
            this.state.selectedNewsStatus,
            sortType,
            sortData,
            this.state.selectedFeaturedOption
          );
        });
        this.setActionMessage(
          true,
          "Success",
          "News Article Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      offset,
      search,
      filterDate,
      selectCategory,
      selectedNewsType,
      selectedNewsStatus,
      sortType,
      sortLabelid,
      sortTitle,
      sortCreateDate,
      sortCategory,
      sortUpdateDate,
      sortAuthor,
      sortStatus,
    } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });

    // let sortData =
    //   sortType === "id"
    //     ? sortLabelid
    //     : sortType === "title"
    //     ? sortTitle
    //     : sortType === "NewsCategoryId"
    //     ? sortCategory
    //     : sortType === "authors"
    //     ? sortAuthor
    //     : sortType === "status"
    //     ? sortStatus
    //     : sortType === "createdAt"
    //     ? sortCreateDate
    //     : sortUpdateDate;

    // this.fetchAllArticles(
    //   offset,
    //   search,
    //   filterDate,
    //   selectCategory,
    //   selectedNewsType,
    //   selectedNewsStatus,
    //   sortType,
    //   sortData
    // );
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, articlelist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  async fetchAllCategory(page) {
    let { rowPerPage } = this.state;
    this.setState({ isCategoryLoading: true });
    try {
      let passApi = `v2/news/admin/category?isAll=1&limit=20&offset=${page}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = Math.ceil(data?.result?.count / 20);
        // Map the new data
        const newdata = data?.result?.result?.map((item) => ({
          label: item?.initialTitle,
          value: item?.id,
          SportId: item?.SportId,
        }));

        // Merge with existing data
        this.setState((prevState) => {
          const mergedData = _.unionBy(prevState.categorylist, newdata);
          const sortedData = mergedData?.sort((a, b) =>
            a?.label.localeCompare(b?.label)
          );
          const finalData = _.uniqBy(sortedData, "value")?.filter(
            (item) => item?.value !== 0
          );

          return {
            categorylist: finalData,
            isCategoryLoading: false,
            categoryCount: count,
            categoryOffset: page,
          };
        });
      } else {
        this.setState({ isCategoryLoading: false });
      }
    } catch {
      this.setState({ isCategoryLoading: false });
    }
  }

  handleOnScrollBottomCategory = (e) => {
    const { categoryCount, categoryOffset } = this.state;
    console.log("Category scroll debug:", {
      categoryCount,
      categoryOffset,
      currentPage: Math.ceil(categoryOffset / 20),
      hasMore: categoryCount !== Math.ceil(categoryOffset / 20),
    });

    // Check if we have more pages to load
    if (categoryCount !== Math.ceil(categoryOffset / 20)) {
      this.fetchAllCategory(categoryOffset + 20);
    }
  };

  handleCategoryInputChange = _.debounce((categoryOffset, value) => {
    if (value === "") return;
    const passApi = `v2/news/admin/category?isAll=1&limit=20&offset=${categoryOffset}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.result?.count / 20;
        // Directly map the data without using push
        const newdata = data?.result?.result?.map((item) => ({
          label: item?.initialTitle,
          value: item?.id,
          SportId: item?.SportId,
        }));

        let filterData = _.unionBy(this.state?.searchCategory, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchCategory: finalData,
          searchCatogoryCount: Math.ceil(count),
          isCategorySearch: value,
        });
      }
    });
  }, 300);
  fetchSelectedCategory = (item, NewsCategoryId) => {
    console.log("fetchSelectedCategory called with:", { item, NewsCategoryId });
    console.log("Current state before update:", this.state.articleValues);

    this.setState(
      {
        selectedCategory: {
          value: item?.NewsCategory?.id,
          label: item?.NewsCategory?.title,
          SportId: item?.NewsCategory?.SportId,
        },
        articleValues: {
          ...this.state.articleValues,
          NewsCategoryId: item?.NewsCategory?.id,
          NewsCategory: item?.NewsCategory,
          NewsCategorySportId: item?.NewsCategory?.SportId,
        },
      },
      () => {
        console.log(
          "State after fetchSelectedCategory update:",
          this.state.articleValues
        );
      }
    );
  };
  fetchAllTag = async (page) => {
    let { rowPerPage } = this.state;
    this.setState({ isTagLoading: true });
    try {
      let passApi = `v2/news/admin/tags?limit=${rowPerPage}&offset=${page}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let tag = data?.result?.data?.map((item) => {
          newdata.push({
            label: item?.title,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.tagList, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        finalData = finalData?.filter((item) => item?.value !== 0);
        this.setState({
          tagList: finalData,
          isTagLoading: false,
          tagCount: Math.ceil(count),
        });
      } else {
        this.setState({
          isTagLoading: false,
        });
      }
    } catch {
      this.setState({
        isTagLoading: false,
      });
    }
  };
  handleOnScrollBottomModalTeam = (e, type) => {
    let { tagCount, tagOffset, isTagSearch, searchtagCount, searchtagOffset } =
      this.state;
    if (
      isTagSearch !== "" &&
      searchtagCount !== Math.ceil(searchtagOffset / 20 + 1)
    ) {
      this.handleTagInputChange(searchtagOffset + 20, isTagSearch);
      this.setState({
        searchtagOffset: searchtagOffset + 20,
      });
    } else {
      if (
        tagCount !== (tagCount == 1 ? 1 : Math.ceil(tagOffset / 20)) &&
        isTagSearch == ""
      ) {
        this.fetchAllTag(tagOffset + 20);
        this.setState({
          tagOffset: tagOffset + 20,
        });
      }
    }
  };
  handleTagInputChange = _.debounce((tagOffset, value) => {
    // if (value === "") return;
    const passApi = `v2/news/admin/tags?limit=20&offset=${tagOffset}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        data?.data?.result?.data?.forEach((item) => {
          newdata.push({
            label: item?.title,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchTag, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });

        const allData = finalData?.filter((item) => item?.value !== 0);
        this.setState({
          searchTag: allData,
          searchtagCount: Math.ceil(count),
          isTagSearch: value,
        });
      }
    });
  }, 300);
  fetchAllRelatedCategory = async (page, categoryId) => {
    let { rowPerPage } = this.state;
    this.setState({ isRelatedCategoryLoading: true });
    try {
      let passApi = `v2/news/admin/category?isAll=1&limit=20&offset=${page}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let category = data?.result?.result?.map((item) => {
          newdata.push({
            label: item?.initialTitle,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.relatedCategoryList, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        finalData = finalData?.filter((item) => item?.value !== categoryId);
        this.setState({
          relatedCategoryList: finalData,
          isRelatedCategoryLoading: false,
          relatedCategoryCount: Math.ceil(count),
          relatedCategoryOffset: page,
        });
      } else {
        this.setState({
          isRelatedCategoryLoading: false,
        });
      }
    } catch {
      this.setState({
        isRelatedCategoryLoading: false,
      });
    }
  };
  handleOnScrollBottomModalRelatedCategory = (e, type) => {
    let {
      relatedCategoryCount,
      relatedCategoryOffset,
      isRelatedCategorySearch,
      searchRelatedCategoryCount,
      searchRelatedCategoryOffset,
    } = this.state;
    if (
      isRelatedCategorySearch !== "" &&
      searchRelatedCategoryCount !==
        Math.ceil(searchRelatedCategoryOffset / 20 + 1)
    ) {
      this.handleRelatedCategoryInputChange(
        searchRelatedCategoryOffset + 20,
        isRelatedCategorySearch
      );
      this.setState({
        searchRelatedCategoryOffset: searchRelatedCategoryOffset + 20,
      });
    } else {
      if (
        relatedCategoryCount !==
          (relatedCategoryCount == 1
            ? 1
            : Math.ceil(relatedCategoryOffset / 20)) &&
        isRelatedCategorySearch == ""
      ) {
        this.fetchAllRelatedCategory(relatedCategoryOffset + 20);
        this.setState({
          relatedCategoryOffset: relatedCategoryOffset + 20,
        });
      }
    }
  };
  handleRelatedCategoryInputChange = _.debounce(
    (relatedCategoryOffset, value) => {
      if (value === "") return;
      const { articleValues } = this.state;
      const passApi = `v2/news/admin/category?isAll=1&limit=20&offset=${relatedCategoryOffset}&search=${value}`;
      axiosInstance.get(passApi).then((data) => {
        if (data?.status === 200) {
          let count = data?.data?.result?.count / 20;
          let newdata = [];
          data?.data?.result?.result?.forEach((item) => {
            newdata.push({
              label: item?.initialTitle,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(
            this.state?.searchRelatedCategory,
            newdata
          );
          const sortedData = filterData?.sort((a, b) => {
            return a?.label.localeCompare(b?.label);
          });
          let finalData = _.uniqBy(sortedData, function (e) {
            return e.value;
          });
          finalData = finalData?.filter(
            (item) => item?.value !== articleValues?.NewsCategoryId
          );
          this.setState({
            searchRelatedCategory: finalData,
            searchRelatedCategoryCount: Math.ceil(count),
            isRelatedCategorySearch: value,
          });
        }
      });
    },
    300
  );
  fetchRelatedArticle = async (NewsCategoryId) => {
    let { rowPerPage } = this.state;
    this.setState({ isRealatedLoading: true });
    try {
      let passApi = `v2/news/admin/articles?NewsCategoryId=${NewsCategoryId}&admin=true`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let newdata = [];
        let tag = data?.result?.raw?.map((item) => {
          newdata.push({
            label: item?.title,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.RelatedArticleList, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        finalData = finalData?.filter((item) => item?.value !== 0);
        this.setState({
          RelatedArticleList: finalData,
          isRealatedLoading: false,
        });
      } else {
        this.setState({
          isRealatedLoading: false,
        });
      }
    } catch {
      this.setState({
        isRealatedLoading: false,
      });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };
  handleFilterDateChange = (e) => {
    const {
      sortType,
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
    } = this.state;
    this.setState({
      filterDate: e ? moment(e)?.format("YYYY-MM-DD") : null,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "title"
        ? sortTitle
        : sortType === "NewsCategoryId"
        ? sortCategory
        : sortType === "authors"
        ? sortAuthor
        : sortType === "status"
        ? sortStatus
        : sortType === "createdAt"
        ? sortCreateDate
        : sortUpdateDate;
    this.fetchAllArticles(
      0,
      this?.state?.search,
      e ? moment(e)?.format("YYYY-MM-DD") : null,
      this?.state?.selectCategory,
      this.state.selectedNewsType,
      this.state.selectedNewsStatus,
      sortType,
      sortData,
      this.state.selectedFeaturedOption
    );
  };

  async fetchAllExternalCategory(page) {
    let { rowPerPage } = this.state;
    this.setState({ isExternalCategoryLoading: true });
    try {
      let passApi = `v2/news/admin/category?isAll=1&limit=20&offset=${page}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let category = data?.result?.result?.map((item) => {
          newdata.push({
            label: item?.initialTitle,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.externalCategorylist, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Categories",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });

        // finalData = finalData?.filter((item) => item?.value !== 0);
        this.setState({
          externalCategorylist: finalData,
          isExternalCategoryLoading: false,
          externalCategoryCount: Math.ceil(data?.result?.count / 20),
        });
      } else {
        this.setState({
          isExternalCategoryLoading: false,
        });
      }
    } catch {
      this.setState({
        isExternalCategoryLoading: false,
      });
    }
  }
  handleOnScrollBottomExternalCategory = (e, type) => {
    let {
      externalCategoryCount,
      externalCategoryOffset,
      isExternalCategorySearch,
      externalSearchCatogoryCount,
      externalSearchCategoryPage,
    } = this.state;
    if (
      isExternalCategorySearch !== "" &&
      externalSearchCatogoryCount !==
        Math.ceil(externalSearchCategoryPage / 20 + 1)
    ) {
      this.handleExternalCategoryInputChange(
        externalSearchCategoryPage + 20,
        isExternalCategorySearch
      );
      this.setState({
        externalSearchCategoryPage: externalSearchCategoryPage + 20,
      });
    } else {
      if (
        externalCategoryCount !==
          (externalCategoryCount == 1
            ? 1
            : Math.ceil(externalCategoryOffset / 20)) &&
        isExternalCategorySearch == ""
      ) {
        this.fetchAllExternalCategory(externalCategoryOffset + 20);
        this.setState({
          externalCategoryOffset: externalCategoryOffset + 20,
        });
      }
    }
  };
  handleExternalCategoryInputChange = (externalCategoryOffset, value) => {
    const passApi = `v2/news/admin/category?isAll=1&limit=20&offset=${externalCategoryOffset}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let category = data?.data?.result?.result?.map((item) => {
          newdata.push({
            label: item?.initialTitle,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.externalSearchCategory, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          externalSearchCategory: finalData,
          externalSearchCatogoryCount: Math.ceil(count),
          isExternalCategorySearch: value,
        });
      }
    });
  };

  handleExternalCategoryChange = (e) => {
    const {
      sortType,
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
    } = this.state;
    this.setState({
      selectCategory: e.value,
      currentPage: 1,
    });

    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "title"
        ? sortTitle
        : sortType === "NewsCategoryId"
        ? sortCategory
        : sortType === "authors"
        ? sortAuthor
        : sortType === "status"
        ? sortStatus
        : sortType === "createdAt"
        ? sortCreateDate
        : sortUpdateDate;
    this.fetchAllArticles(
      0,
      this.state?.search,
      this.state?.filterDate,
      e?.value,
      this.state.selectedNewsType,
      this.state.selectedNewsStatus,
      sortType,
      sortData,
      this.state.selectedFeaturedOption
    );
  };
  handleImageUpload = (imageUrl) => {
    const { editorState } = this.state;
    const contentState = editorState.getCurrentContent();
    const contentStateWithEntity = contentState.createEntity(
      "IMAGE",
      "IMMUTABLE",
      { src: imageUrl }
    );
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
    const newEditorState = EditorState.set(editorState, {
      currentContent: contentStateWithEntity,
    });
    this.setState({
      editorState: AtomicBlockUtils.insertAtomicBlock(
        newEditorState,
        entityKey,
        " "
      ),
    });
  };
  handleNewsTypeChange = (e) => {
    const {
      sortType,
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
    } = this.state;
    this.setState({
      selectedNewsType: e.value,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "title"
        ? sortTitle
        : sortType === "NewsCategoryId"
        ? sortCategory
        : sortType === "authors"
        ? sortAuthor
        : sortType === "status"
        ? sortStatus
        : sortType === "createdAt"
        ? sortCreateDate
        : sortUpdateDate;
    this.fetchAllArticles(
      0,
      this.state?.search,
      this.state?.filterDate,
      this.state?.selectCategory,
      e?.value,
      this.state.selectedNewsStatus,
      sortType,
      sortData,
      this.state.selectedFeaturedOption
    );
  };
  handleNewsStatusChange = (e) => {
    const {
      sortType,
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
    } = this.state;
    this.setState({
      selectedNewsStatus: e.label,
      currentPage: 1,
    });

    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "title"
        ? sortTitle
        : sortType === "NewsCategoryId"
        ? sortCategory
        : sortType === "authors"
        ? sortAuthor
        : sortType === "status"
        ? sortStatus
        : sortType === "createdAt"
        ? sortCreateDate
        : sortUpdateDate;

    this.fetchAllArticles(
      0,
      this.state?.search,
      this.state?.filterDate,
      this.state?.selectCategory,
      this.state?.selectedNewsType,
      e?.label,
      sortType,
      sortData,
      ""
    );
  };

  handleFeaturedOptionChange = (e) => {
    const {
      sortType,
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
      selectedNewsStatus,
    } = this.state;
    this.setState({
      selectedFeaturedOption: e.label,
      currentPage: 1,
    });

    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "title"
        ? sortTitle
        : sortType === "NewsCategoryId"
        ? sortCategory
        : sortType === "authors"
        ? sortAuthor
        : sortType === "status"
        ? sortStatus
        : sortType === "createdAt"
        ? sortCreateDate
        : sortUpdateDate;

    this.fetchAllArticles(
      0,
      this.state?.search,
      this.state?.filterDate,
      this.state?.selectCategory,
      this.state?.selectedNewsType,
      this?.state?.selectedNewsStatus,
      this.state.sortType,
      sortData,
      e?.label
    );
  };

  // handleAddImage = () => {
  //   this.imageUploaderRef.handleImageUpload();
  //   // Trigger the image upload logic from the ImageUploader component
  //   // Call this.handleImageUpload(imageUrl) when the image upload is successful
  // };
  fetchAllNewsProvider = async () => {
    this.setState({ isProviderLoading: true });
    try {
      const { status, data } = await axiosInstance.get(`v2/news/provider`);
      if (status === 200) {
        let newdata = [];
        let providerData = data.result?.map((item) => {
          newdata.push({
            label: item?.providerName,
            value: item?.id,
          });
        });
        let alldatas = {
          label: "All Provider",
          value: 0,
        };

        let alldata = [alldatas, ...newdata];

        // let pushData = newdata.push(alldatas);
        // let sortData = newdata?.sort((a, b) => {
        //   return a.value > b.value ? 1 : -1;
        // });
        this.setState({
          newsTypeOption: [alldatas, ...newdata]?.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          }),
          isProviderLoading: false,
        });
      } else {
        this.setState({ isProviderLoading: false });
      }
    } catch (err) {
      this.setState({ isProviderLoading: false });
    }
  };
  fetchUrl = (item) => {
    const token = fetchFromStorage(identifiers.token);
    return (
      config.baseUrl.replace("/api", "") +
      `smartinfo/news/${item?.NewsCategoryId}/${item?.id}?token=${token.access_token}`
    );
  };
  decodeHTMLEntities(encodedString) {
    const element = document.createElement("input");
    element.innerHTML = encodedString;
    return element.textContent;
  }

  sortLabelHandler = (type) => {
    const {
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
      offset,
      search,
      filterDate,
      selectCategory,
      selectedNewsType,
      selectedNewsStatus,
    } = this.state;

    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchAllArticles(
        offset,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        type,
        !sortLabelid,
        this.state.selectedFeaturedOption
      );
      this.setState({
        sortLabelid: !sortLabelid,
        sortTitle: true,
        sortCategory: true,
        sortAuthor: true,
        sortStatus: true,
        sortCreateDate: true,
        sortUpdateDate: true,
        currentPage: 1,
      });
    } else if (type === "title") {
      this.fetchAllArticles(
        offset,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        type,
        !sortTitle,
        this.state.selectedFeaturedOption
      );
      this.setState({
        sortLabelid: false,
        sortTitle: !sortTitle,
        sortCategory: true,
        sortAuthor: true,
        sortStatus: true,
        sortCreateDate: true,
        sortUpdateDate: true,
        currentPage: 1,
      });
    } else if (type === "NewsCategoryId") {
      this.fetchAllArticles(
        offset,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        type,
        !sortCategory,
        this.state.selectedFeaturedOption
      );
      this.setState({
        sortLabelid: false,
        sortTitle: true,
        sortCategory: !sortCategory,
        sortAuthor: true,
        sortStatus: true,
        sortCreateDate: true,
        sortUpdateDate: true,
        currentPage: 1,
      });
    } else if (type === "authors") {
      this.fetchAllArticles(
        offset,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        type,
        !sortAuthor,
        this.state.selectedFeaturedOption
      );
      this.setState({
        sortLabelid: false,
        sortTitle: true,
        sortCategory: true,
        sortAuthor: !sortAuthor,
        sortStatus: true,
        sortCreateDate: true,
        sortUpdateDate: true,
        currentPage: 1,
      });
    } else if (type === "status") {
      this.fetchAllArticles(
        offset,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        type,
        !sortStatus,
        this.state.selectedFeaturedOption
      );
      this.setState({
        sortLabelid: false,
        sortTitle: true,
        sortCategory: true,
        sortAuthor: true,
        sortStatus: !sortStatus,
        sortCreateDate: true,
        sortUpdateDate: true,
        currentPage: 1,
      });
    } else if (type === "createdAt") {
      this.fetchAllArticles(
        offset,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        type,
        !sortCreateDate,
        this.state.selectedFeaturedOption
      );
      this.setState({
        sortLabelid: false,
        sortTitle: true,
        sortCategory: true,
        sortAuthor: true,
        sortStatus: true,
        sortCreateDate: !sortCreateDate,
        sortUpdateDate: true,
        currentPage: 1,
      });
    } else {
      this.fetchAllArticles(
        offset,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        type,
        !sortUpdateDate,
        this.state.selectedFeaturedOption
      );
      this.setState({
        sortLabelid: false,
        sortTitle: true,
        sortCategory: true,
        sortAuthor: true,
        sortStatus: true,
        sortCreateDate: true,
        sortUpdateDate: !sortUpdateDate,
        currentPage: 1,
      });
    }
  };

  handleChange = (content) => {
    // this.setState({ content: content });
    const { articleValues } = this.state;
    // this.setState({ content });
    // let string = draftToHtml(convertToRaw(content.getCurrentContent()));
    this.setState({
      articleValues: {
        ...articleValues,
        body: content,
      },
    });
  };

  inputViewImgModal = (data) => {
    this.setState({
      isViewImgModalOpen: true,
      isViewImgData: data,
    });
  };
  toggleViewImgModal = () => {
    this.setState({
      isViewImgModalOpen: false,
      isViewImgData: {},
    });
  };

  handleCreateTag = async () => {
    const { articleValues } = this.state;
    const payload = {
      title: this.state?.articleValues?.tagTitle,
    };
    try {
      const { status, data } = await axiosInstance.post(
        `v2/news/admin/tags`,
        payload
      );
      if (status === 200) {
        this.setActionMessage(true, "Success", data?.message);
        this.setState({ articleValues: { ...articleValues, tagTitle: "" } });
      } else {
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  clearStartDate = () => {
    this.setState({
      filterDate: null,
      startDateOpen: false,
    });
    this.fetchAllArticles(
      0,
      "",
      "",
      this?.state?.selectCategory,
      null,
      null,
      this.state.sortType,
      false,
      this.state.selectedFeaturedOption
    );
  };

  handleKeyDown = (event) => {
    var {
      search,
      filterDate,
      selectCategory,
      selectedNewsType,
      selectedNewsStatus,
      sortType,
    } = this.state;
    if (event.key === "Enter") {
      this.fetchAllArticles(
        0,
        search,
        filterDate,
        selectCategory,
        selectedNewsType,
        selectedNewsStatus,
        sortType,
        false,
        this.state.selectedFeaturedOption
      );
      this.setState({ currentPage: 1 });
    }
  };

  handleEmbedCodeChange = (e) => {
    const embedCode = e;
    this.setState({ embedCode }, () => {
      this.injectScripts();
    });
  };

  injectScripts = () => {
    // Remove any previously injected scripts to avoid duplicates
    document
      .querySelectorAll("script[data-getty-embed]")
      .forEach((script) => script.remove());

    // Extract scripts and inject them dynamically
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = this.state.embedCode;
    const scripts = tempDiv.querySelectorAll("script");

    scripts.forEach((script) => {
      const newScript = document.createElement("script");
      newScript.setAttribute("data-dynamic", "true"); // Mark dynamically added scripts

      if (script.src) {
        newScript.src = script.src;
        newScript.async = true;

        // Add error handling for script loading
        newScript.onerror = () =>
          console.error(`Failed to load script: ${newScript.src}`);
      } else {
        newScript.textContent = script.textContent;
      }
      document.body.appendChild(newScript);
    });
  };

  // Racing API methods
  async fetchAllHorses(search = "", offset = 0, limit = 20, SPORTSID) {
    try {
      if (SPORTSID) {
        this.setState({ horseLoading: true });
        const { status, data } = await axiosInstance.get(
          `animal/all/animals?SportId=${SPORTSID}&limit=${limit}&offset=${offset}&search=${search}`
        );
        if (status === 200 && data) {
          const options = data?.result?.rows?.map((horse) => ({
            value: horse.id,
            label: horse.name,
          }));
          const hasMore = options.length === limit;
          const updatedOptions =
            offset === 0 ? options : [...this.state.horseOptions, ...options];
          this.setState({
            horseOptions: updatedOptions,
            horseHasMore: hasMore,
            horseOffset: offset + limit,
            horseSearch: search,
          });
          return updatedOptions;
        }
      }
      return this.state.horseOptions;
    } catch (error) {
      console.error("Error fetching horses:", error);
      return this.state.horseOptions;
    } finally {
      this.setState({ horseLoading: false });
    }
  }

  async fetchAllJockeys(search = "", offset = 0, limit = 20) {
    try {
      this.setState({ jockeyLoading: true });
      const { status, data } = await axiosInstance.get(
        `race/participant/jockeys/jockey?limit=${limit}&offset=${offset}&search=${search}`
      );
      if (status === 200 && data) {
        const options = data?.result?.rows?.map((jockey) => ({
          value: jockey.id,
          label: jockey.name,
        }));
        const hasMore = options.length === limit;
        const updatedOptions =
          offset === 0 ? options : [...this.state.jockeyOptions, ...options];
        this.setState({
          jockeyOptions: updatedOptions,
          jockeyHasMore: hasMore,
          jockeyOffset: offset + limit,
          jockeySearch: search,
        });
        return updatedOptions;
      }
      return this.state.jockeyOptions;
    } catch (error) {
      console.error("Error fetching jockeys:", error);
      return this.state.jockeyOptions;
    } finally {
      this.setState({ jockeyLoading: false });
    }
  }

  async fetchAllDrivers(search = "", offset = 0, limit = 20, SPORTSID) {
    try {
      this.setState({ driverLoading: true });
      const { status, data } = await axiosInstance.get(
        `race/participant/jockeys/jockey?limit=${limit}&offset=${offset}&search=${search}`
      );
      if (status === 200 && data) {
        const options = data?.result?.rows?.map((driver) => ({
          value: driver.id,
          label: driver.name,
        }));
        const hasMore = options.length === limit;
        const updatedOptions =
          offset === 0 ? options : [...this.state.driverOptions, ...options];
        this.setState({
          driverOptions: updatedOptions,
          driverHasMore: hasMore,
          driverOffset: offset + limit,
          driverSearch: search,
        });
        return updatedOptions;
      }
      return this.state.driverOptions;
    } catch (error) {
      console.error("Error fetching drivers:", error);
      return this.state.driverOptions;
    } finally {
      this.setState({ driverLoading: false });
    }
  }

  async fetchAllTrainers(search = "", offset = 0, limit = 20) {
    try {
      this.setState({ trainerLoading: true });
      const { status, data } = await axiosInstance.get(
        `race/participant/trainers/trainer?limit=${limit}&offset=${offset}&search=${search}`
      );
      if (status === 200 && data) {
        const options = data?.result?.rows?.map((trainer) => ({
          value: trainer.id,
          label: trainer.name,
        }));
        const hasMore = options.length === limit;
        const updatedOptions =
          offset === 0 ? options : [...this.state.trainerOptions, ...options];
        this.setState({
          trainerOptions: updatedOptions,
          trainerHasMore: hasMore,
          trainerOffset: offset + limit,
          trainerSearch: search,
        });
        return updatedOptions;
      }
      return this.state.trainerOptions;
    } catch (error) {
      console.error("Error fetching trainers:", error);
      return this.state.trainerOptions;
    } finally {
      this.setState({ trainerLoading: false });
    }
  }

  async fetchAllGreyhounds(search = "", offset = 0, limit = 20, SPORTSID) {
    try {
      if (SPORTSID) {
        this.setState({ greyhoundLoading: true });
        const { status, data } = await axiosInstance.get(
          `animal/all/animals?SportId=${SPORTSID}&limit=${limit}&offset=${offset}&search=${search}`
        );
        if (status === 200 && data) {
          const options = data?.result?.rows?.map((greyhound) => ({
            value: greyhound.id,
            label: greyhound.name,
          }));
          const hasMore = options.length === limit;
          const updatedOptions =
            offset === 0
              ? options
              : [...this.state.greyhoundOptions, ...options];
          this.setState({
            greyhoundOptions: updatedOptions,
            greyhoundHasMore: hasMore,
            greyhoundOffset: offset + limit,
            greyhoundSearch: search,
          });
          return updatedOptions;
        }
      }
      return this.state.greyhoundOptions;
    } catch (error) {
      console.error("Error fetching greyhounds:", error);
      return this.state.greyhoundOptions;
    } finally {
      this.setState({ greyhoundLoading: false });
    }
  }

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      articleValues,
      articlelist,
      articleCount,
      errorTitle,
      errorInitialTitle,
      errorNewsCategory,
      errorStartTime,
      errorTag,
      errorRelatedCategory,
      errorRelatedArticle,
      errorTopStory,
      errorfeaturedArticle,
      errorHomeArticle,
      errorAlt,
      errorEmbedCode,
      search,
      editorState,
      categorylist,
      isCategoryLoading,
      isTagSearch,
      searchTag,
      tagList,
      RelatedArticleList,
      image,
      uploadImage,
      isTagLoading,
      isRealatedLoading,
      filterDate,
      externalCategorylist,
      selectCategory,
      isExternalCategorySearch,
      externalSearchCategory,
      author,
      authorImage,
      authorUploadImage,
      selectedNewsType,
      selectedNewsStatus,
      newsTypeOption,
      selectedModalNewsStatus,
      sortType,
      sortLabelid,
      sortTitle,
      sortCategory,
      sortAuthor,
      sortStatus,
      sortCreateDate,
      sortUpdateDate,
      isViewImgModalOpen,
      isViewImgData,
      isCreateTag,
      relatedCategoryList,
      searchRelatedCategory,
      isRelatedCategorySearch,
      startDateOpen,
      selectedFeaturedOption,
      content,
      embedCode,
      selectedTournaments,
      tournamentOffset,
      tournamentLimit,
      tournamentSearch,
      tournamentOptions,
      tournamentHasMore,
      tournamentLoading,
      playerOptions,
      selectedPlayers,
      playerOffset,
      playerLimit,
      playerSearch,
      playerHasMore,
      playerLoading,
      teamOptions,
      selectedTeams,
      teamOffset,
      teamLimit,
      teamSearch,
      teamHasMore,
      teamLoading,
      horseOptions,
      selectedHorses,
      horseOffset,
      horseLimit,
      horseSearch,
      horseHasMore,
      horseLoading,
      jockeyOptions,
      selectedJockeys,
      jockeyOffset,
      jockeyLimit,
      jockeySearch,
      jockeyHasMore,
      jockeyLoading,
      driverOptions,
      selectedDrivers,
      driverOffset,
      driverLimit,
      driverSearch,
      driverHasMore,
      driverLoading,
      trainerOptions,
      selectedTrainers,
      trainerOffset,
      trainerLimit,
      trainerSearch,
      trainerHasMore,
      trainerLoading,
      greyhoundOptions,
      selectedGreyhounds,
      greyhoundOffset,
      greyhoundLimit,
      greyhoundSearch,
      greyhoundHasMore,
      greyhoundLoading,
    } = this.state;
    const pageNumbers = [];
    if (articleCount > 0) {
      for (let i = 1; i <= Math.ceil(articleCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    const decodedTitle = this.decodeHTMLEntities(articleValues?.articleTitle);
    const decodedAlt = this.decodeHTMLEntities(articleValues?.alt);

    const SPORTSID = this?.state?.articleValues?.NewsCategorySportId;
    const IS_RACING = this.IS_RACING(SPORTSID);
    const IS_SPORT = this.IS_SPORT(SPORTSID);

    console.log("SPORTSID", SPORTSID);

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && !isInputModalOpen && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  News
                </Link>
                <Typography className="active_p">News Article</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  News Article
                </Typography>
              </Grid>

              <Grid item xs={9} className="admin-filter-wrap">
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="All Categories"
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomExternalCategory(e)
                  }
                  onInputChange={(e) =>
                    this.handleExternalCategoryInputChange(0, e)
                  }
                  value={
                    isExternalCategorySearch
                      ? externalSearchCategory?.find((item) => {
                          return item?.value == selectCategory;
                        })
                      : externalCategorylist?.find((item) => {
                          return item?.value == selectCategory;
                        })
                  }
                  onChange={(e) => this.handleExternalCategoryChange(e)}
                  menuPosition="absolute"
                  options={
                    isExternalCategorySearch
                      ? externalSearchCategory
                      : externalCategorylist
                  }
                />
                <Box className="date-time-picker-wrap filter-date-picker">
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      clearable
                      // onClear={() => this.clearStartDate()}
                      // open={startDateOpen}
                      // onOpen={() => this.setState({ startDateOpen: true })}
                      // onClose={() => this.setState({ startDateOpen: false })}
                      autoOk
                      // disableToolbar
                      // variant="inline"
                      format="yyyy/MM/dd"
                      placeholder="All"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      value={filterDate ? parseISO(filterDate) : null}
                      // onKeyDown={(e) => {
                      //   e.preventDefault();
                      // }}
                      slots={{
                        openPickerIcon: TodayIcon,
                      }}
                      slotProps={{
                        field: {
                          placeholder: "DD/MM/YYYY",
                          clearable: true,
                        },
                      }}
                      onChange={(e) => this.handleFilterDateChange(e)}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      className="date-time-picker date-picker-fixture advt-datepicker"
                      style={{ margin: "0px 10px 0px 0px" }}
                    />
                  </LocalizationProvider>
                </Box>
                <Select
                  className="React cricket-select external-select text-capitalize"
                  classNamePrefix="select"
                  placeholder="Select Type"
                  value={newsTypeOption?.find((item) => {
                    return item?.value == selectedNewsType;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleNewsTypeChange(e)}
                  options={newsTypeOption}
                />
                <Select
                  className="React cricket-select text-capitalize  external-select"
                  classNamePrefix="select"
                  placeholder="Select Status"
                  value={newsStatusOption?.find((item) => {
                    return item?.label == selectedNewsStatus;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleNewsStatusChange(e)}
                  options={newsStatusOption}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    minWidth: "90px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap"
                style={{ marginBottom: "10px" }}
              >
                <Select
                  className="React cricket-select text-capitalize  external-select"
                  classNamePrefix="select"
                  placeholder="Select News"
                  value={featuredOption?.find((item) => {
                    return item?.label == selectedFeaturedOption;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleFeaturedOptionChange(e)}
                  options={featuredOption}
                />

                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllArticles(
                      0,
                      search,
                      filterDate,
                      selectCategory,
                      selectedNewsType,
                      selectedNewsStatus,
                      sortType,
                      false,
                      this.state.selectedFeaturedOption
                    );
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && articlelist?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && articlelist?.length > 0 && (
              <TableContainer component={Paper}>
                <Table
                  className="listTable"
                  aria-label="simple table"
                  style={{ minWidth: "max-content" }}
                >
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell
                        onClick={() => this.sortLabelHandler("id")}
                        style={{ cursor: "pointer" }}
                      >
                        DID
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "id"
                              ? sortLabelid
                                ? "asc"
                                : "desc"
                              : "desc"
                          }
                        />
                      </TableCell>
                      <TableCell>Provider</TableCell>
                      <TableCell>Feature Image</TableCell>
                      <TableCell
                        style={{ cursor: "pointer", width: "200px" }}
                        onClick={() => this.sortLabelHandler("title")}
                      >
                        {" "}
                        Title{" "}
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "title"
                              ? sortTitle
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("NewsCategoryId")}
                        style={{ cursor: "pointer" }}
                      >
                        News Category
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "NewsCategoryId"
                              ? sortCategory
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("authors")}
                        style={{ cursor: "pointer", width: "142px" }}
                      >
                        Author
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "authors"
                              ? sortAuthor
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("status")}
                        style={{ cursor: "pointer" }}
                      >
                        Status
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "status"
                              ? sortStatus
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      {/* <TableCell>Link</TableCell> */}
                      <TableCell
                        onClick={() => this.sortLabelHandler("createdAt")}
                        style={{ cursor: "pointer", width: "106px" }}
                      >
                        {" "}
                        Created Date{" "}
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "createdAt"
                              ? sortCreateDate
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("updatedAt")}
                        style={{ cursor: "pointer", width: "108px" }}
                      >
                        {" "}
                        Updated Date{" "}
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "updatedAt"
                              ? sortUpdateDate
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {articlelist?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>
                            {item?.NewsProvider?.providerName}
                          </TableCell>
                          <TableCell className="feature-img">
                            <Box
                              onClick={() => {
                                this.inputViewImgModal(item);
                              }}
                              style={{
                                cursor: "pointer",
                                // maxWidth: "170px",
                                // maxHeight: "145px",
                              }}
                            >
                              {item?.embeddedImageStatus ? (
                                <GettyEmbed embedCode={item?.embeddedImage} />
                              ) : item?.mainMedia?.gallery ? (
                                <img
                                  className="auto-width"
                                  src={item?.mainMedia?.gallery?.url}
                                  alt="featureImage"
                                />
                              ) : item?.mainMedia?.[0]?.gallery ? (
                                <img
                                  className="auto-width"
                                  src={item?.mainMedia?.[0]?.gallery.url}
                                  alt="featureImage"
                                />
                              ) : (
                                ""
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <a href={this.fetchUrl(item)} target="_blank">
                              <span
                                dangerouslySetInnerHTML={{
                                  __html: item?.title,
                                }}
                                style={{
                                  color: "blue",
                                  textDecoration: "underline",
                                }}
                              ></span>
                            </a>
                            {/* {decodeURIComponent(tilte)} */}
                          </TableCell>
                          <TableCell>
                            {item?.NewsCategory?.initialTitle}
                          </TableCell>
                          <TableCell>
                            {item?.authors?.length > 0
                              ? item?.authors?.[0]?.name
                              : ""}
                          </TableCell>
                          <TableCell>
                            {item?.status ? item?.status : ""}
                          </TableCell>
                          {/* <TableCell>
                          <a href={this.fetchUrl(item)} target="_blank">
                            URL
                          </a>
                        </TableCell> */}
                          <TableCell>
                            {item?.createdAt
                              ? moment(item?.createdAt)
                                  .tz(timezone)
                                  .format("DD/MM/YYYY hh:mm a")
                              : "-"}
                          </TableCell>
                          <TableCell>
                            {item?.updatedAt
                              ? moment(item?.updatedAt)
                                  .tz(timezone)
                                  .format("DD/MM/YYYY hh:mm a")
                              : "-"}
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className={
                                // !item?.userId && item?.NewsProviderId === 1
                                //   ? "table-btn edit-btn disabled-btn"
                                // :
                                "table-btn edit-btn"
                              }
                              // disabled={
                              //   !item?.userId && item?.NewsProviderId === 1
                              // }
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                            className={
                              articlelist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              articlelist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              articleCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                            className={
                              articlelist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              articlelist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="news-modal"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={
                  isLoading ? "paper modal-disable" : "paper modal-show-scroll"
                }
              >
                {isLoading && (
                  <Box className="modal-loader">
                    <Loader />
                  </Box>
                )}
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New News Article"
                    : "Edit News Article"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    <Box
                      style={{
                        position: "fixed",
                        top: "50px",
                        backgroundColor: "transparent",
                        zIndex: "2",
                        width: "100%",
                        maxWidth: "985px",
                      }}
                    >
                      {messageBox?.display && (
                        <ActionMessage
                          message={messageBox?.message}
                          type={messageBox?.type}
                          styleClass={messageBox?.styleClass}
                        />
                      )}
                    </Box>
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Title </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Article Title"
                          value={decodedTitle}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                articleTitle: e.target.value,
                              },
                              errorTitle: e.target.value ? "" : errorTitle,
                            })
                          }
                        />
                        {errorTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Subtitle</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Article Subtitle"
                          value={articleValues?.articleSubTitle}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                articleSubTitle: e.target.value,
                              },
                              errorInitialTitle: e.target.value
                                ? ""
                                : errorInitialTitle,
                            })
                          }
                        />
                        {errorInitialTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorInitialTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label"> News Category </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="News Category"
                          onMenuScrollToBottom={
                            this.handleOnScrollBottomCategory
                          }
                          onInputChange={(e) =>
                            this.handleCategoryInputChange(0, e)
                          }
                          isLoading={isCategoryLoading}
                          value={categorylist?.find((item) => {
                            return item?.value == articleValues?.NewsCategoryId;
                          })}
                          options={categorylist}
                          onChange={(e) => {
                            console.log("Category changed:", e);

                            // Reset all tag states first
                            const resetState = {
                              articleValues: {
                                ...articleValues,
                                NewsCategoryId: e?.value,
                                relatedArticle: [],
                                relatedCategory: [],
                                NewsCategorySportId: e?.SportId || e?.sportId,
                              },
                              // Reset all tag selections
                              selectedTournaments: [],
                              selectedTeams: [],
                              selectedPlayers: [],
                              selectedHorses: [],
                              selectedJockeys: [],
                              selectedDrivers: [],
                              selectedGreyhounds: [],
                              selectedTrainers: [],
                              // Reset options
                              tournamentOptions: [],
                              teamOptions: [],
                              playerOptions: [],
                              horseOptions: [],
                              jockeyOptions: [],
                              driverOptions: [],
                              greyhoundOptions: [],
                              trainerOptions: [],
                              errorNewsCategory: e?.value
                                ? ""
                                : errorNewsCategory,
                            };

                            this.setState(resetState, () => {
                              this.fetchRelatedArticle(e?.value);
                              this.fetchAllRelatedCategory(0, e?.value);

                              // Fetch relevant data based on new sport type
                              const sportId = e?.SportId || e?.sportId;
                              if (sportId) {
                                if (this.IS_RACING(sportId)) {
                                  // Racing category (1, 2, 3)
                                  if (sportId === 1 || sportId === 2) {
                                    // Horse/Harness Racing
                                    this.fetchAllHorses(
                                      "",
                                      0,
                                      this.state.horseLimit,
                                      sportId
                                    );
                                    this.fetchAllTrainers(
                                      "",
                                      0,
                                      this.state.trainerLimit
                                    );
                                    if (sportId === 1) {
                                      this.fetchAllJockeys(
                                        "",
                                        0,
                                        this.state.jockeyLimit
                                      );
                                    } else {
                                      this.fetchAllDrivers(
                                        "",
                                        0,
                                        this.state.driverLimit
                                      );
                                    }
                                  } else if (sportId === 3) {
                                    // Greyhound Racing
                                    this.fetchAllGreyhounds(
                                      "",
                                      0,
                                      this.state.greyhoundLimit,
                                      sportId
                                    );
                                    this.fetchAllTrainers(
                                      "",
                                      0,
                                      this.state.trainerLimit
                                    );
                                  }
                                } else if (this.IS_SPORT(sportId)) {
                                  // Any other sport category
                                  this.fetchAllTournaments(
                                    "",
                                    0,
                                    this.state.tournamentLimit,
                                    sportId
                                  );
                                  this.fetchAllPlayers(
                                    "",
                                    0,
                                    this.state.playerLimit,
                                    sportId
                                  );
                                  this.fetchAllTeams(
                                    "",
                                    0,
                                    this.state.teamLimit,
                                    sportId
                                  );
                                }
                              }
                            });
                          }}
                        />
                        {errorNewsCategory ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorNewsCategory}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        className="date-time-picker-wrap"
                        style={{ marginBottom: "15px" }}
                      >
                        <label className="modal-label"> Publish Date </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <MobileDateTimePicker
                            variant="inline"
                            inputVariant="outlined"
                            ampm={false}
                            value={
                              articleValues?.startTime
                                ? typeof articleValues?.startTime === "string"
                                  ? parseISO(articleValues?.startTime)
                                  : articleValues?.startTime
                                : null
                            }
                            onChange={(e) => {
                              this.setState({
                                articleValues: {
                                  ...articleValues,
                                  startTime: e,
                                },
                              });
                            }}
                            slots={{
                              openPickerIcon: TodayIcon,
                            }}
                            slotProps={{
                              field: {
                                placeholder: "YYYY/MM/DD HH:mm",
                              },
                            }}
                            autoOk={true}
                            format="yyyy/MM/dd HH:mm"
                            className="date-time-picker"
                          />
                        </LocalizationProvider>
                        {errorStartTime ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorStartTime}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={12} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">
                          {" "}
                          Related Category{" "}
                        </label>
                        <Select
                          className="React teamsport-select news-multi-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          isMulti
                          // isLoading={isTagLoading}
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomModalRelatedCategory(e)
                          }
                          onInputChange={(e) =>
                            this.handleRelatedCategoryInputChange(0, e)
                          }
                          value={
                            isRelatedCategorySearch
                              ? searchRelatedCategory?.find((item) => {
                                  return (
                                    item?.value ==
                                    articleValues?.relatedCategory
                                  );
                                })
                              : articleValues?.relatedCategory
                          }
                          options={
                            isRelatedCategorySearch
                              ? searchRelatedCategory
                              : relatedCategoryList
                          }
                          // value={playerValues?.team}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                relatedCategory: e,
                              },
                            })
                          }
                        />
                        {errorRelatedCategory ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorRelatedCategory}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* {isCreateTag && (
                        <Grid
                          item
                          xs={12}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">Tag Title </label>
                          <Box
                            style={{ display: "flex", alignItems: "center" }}
                            className="tag-create"
                          >
                            <TextField
                              className="teamsport-textfield"
                              variant="outlined"
                              color="primary"
                              size="small"
                              placeholder="Tag Title"
                              value={articleValues?.tagTitle}
                              onChange={(e) =>
                                this.setState({
                                  articleValues: {
                                    ...articleValues,
                                    tagTitle: e.target.value,
                                  },
                                })
                              }
                              style={{ width: "87%" }}
                            />
                            <Button
                              variant="contained"
                              style={{
                                borderRadius: "8px",
                                textTransform: "capitalize",
                                padding: "6px 10px",
                                minWidth: "fit-content",
                                marginTop: "3px",
                                marginBottom: "15px",
                                marginLeft: "10px",
                              }}
                              onClick={() => this.handleCreateTag()}
                              disabled={
                                !this.state?.articleValues?.tagTitle ||
                                this.state?.articleValues?.tagTitle.trim() ===
                                  ""
                              }
                              className={
                                !this.state?.articleValues?.tagTitle ||
                                this.state?.articleValues?.tagTitle.trim() ===
                                  ""
                                  ? "create-tag-btn disable-create-tag"
                                  : "create-tag-btn"
                              }
                            >
                              Create tag
                            </Button>
                          </Box>
                        </Grid>
                      )} */}

                      {/* CONDITION BASED TAGS */}
                      {IS_SPORT && (
                        <Grid item xs={12} style={{ marginBottom: "15px" }}>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={4}>
                              <label className="modal-label">Tournaments</label>
                              <AsyncSelect
                                isMulti
                                className="React teamsport-select news-multi-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                loadOptions={async (inputValue, callback) => {
                                  if (
                                    inputValue !== this.state.tournamentSearch
                                  ) {
                                    this.setState({
                                      tournamentOffset: 0,
                                      tournamentOptions: [],
                                      tournamentHasMore: true,
                                    });
                                  }
                                  const options =
                                    await this.fetchAllTournaments(
                                      inputValue,
                                      0,
                                      this.state.tournamentLimit,
                                      this.state.articleValues
                                        .NewsCategorySportId
                                    );
                                  callback(options);
                                }}
                                onMenuScrollToBottom={async () => {
                                  if (
                                    this.state.tournamentHasMore &&
                                    !this.state.tournamentLoading
                                  ) {
                                    const options =
                                      await this.fetchAllTournaments(
                                        this.state.tournamentSearch,
                                        this.state.tournamentOffset,
                                        this.state.tournamentLimit,
                                        this.state.articleValues
                                          .NewsCategorySportId
                                      );
                                    this.setState({
                                      tournamentOptions: options,
                                    });
                                  }
                                }}
                                onChange={(option) => {
                                  this.setState(
                                    {
                                      selectedTournaments: option,
                                      tournamentSearch: "",
                                      tournamentOffset: 0,
                                      tournamentOptions: [],
                                    },
                                    () => {
                                      this.fetchAllTournaments(
                                        "",
                                        0,
                                        this.state.tournamentLimit,
                                        this.state.articleValues
                                          .NewsCategorySportId
                                      );
                                    }
                                  );
                                }}
                                onInputChange={(inputValue, { action }) => {
                                  if (
                                    action === "input-blur" ||
                                    action === "menu-close"
                                  ) {
                                    this.setState(
                                      { tournamentSearch: "" },
                                      () => {
                                        // Fetch original data when search is cleared
                                        this.fetchAllTournaments(
                                          "",
                                          0,
                                          this.state.tournamentLimit,
                                          this.state.articleValues
                                            .NewsCategorySportId
                                        );
                                      }
                                    );
                                  }
                                }}
                                value={this.state.selectedTournaments}
                                placeholder="Select tournaments..."
                                isLoading={this.state.tournamentLoading}
                                cacheOptions={false}
                                defaultOptions={this.state.tournamentOptions}
                              />
                            </Grid>
                            <Grid item xs={12} md={4}>
                              <label className="modal-label">Players</label>
                              <AsyncSelect
                                isMulti
                                className="React teamsport-select news-multi-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                loadOptions={async (inputValue, callback) => {
                                  if (inputValue !== this.state.playerSearch) {
                                    this.setState({
                                      playerOffset: 0,
                                      playerOptions: [],
                                      playerHasMore: true,
                                    });
                                  }
                                  const options = await this.fetchAllPlayers(
                                    inputValue,
                                    0,
                                    this.state.playerLimit,
                                    this.state.articleValues.NewsCategorySportId
                                  );
                                  callback(options);
                                }}
                                onMenuScrollToBottom={async () => {
                                  if (
                                    this.state.playerHasMore &&
                                    !this.state.playerLoading
                                  ) {
                                    const options = await this.fetchAllPlayers(
                                      this.state.playerSearch,
                                      this.state.playerOffset,
                                      this.state.playerLimit,
                                      this.state.articleValues
                                        .NewsCategorySportId
                                    );
                                    this.setState({ playerOptions: options });
                                  }
                                }}
                                onChange={(option) => {
                                  this.setState(
                                    {
                                      selectedPlayers: option,
                                      playerSearch: "",
                                      playerOffset: 0,
                                      playerOptions: [],
                                    },
                                    () => {
                                      this.fetchAllPlayers(
                                        "",
                                        0,
                                        this.state.playerLimit,
                                        this.state.articleValues
                                          .NewsCategorySportId
                                      );
                                    }
                                  );
                                }}
                                onInputChange={(inputValue, { action }) => {
                                  if (
                                    action === "input-blur" ||
                                    action === "menu-close"
                                  ) {
                                    console.log("input-blur");
                                    this.setState({ playerSearch: "" }, () => {
                                      // Fetch original data when search is cleared
                                      this.fetchAllPlayers(
                                        "",
                                        0,
                                        this.state.playerLimit,
                                        this.state.articleValues
                                          .NewsCategorySportId
                                      );
                                    });
                                  }
                                }}
                                value={this.state.selectedPlayers}
                                placeholder="Select players..."
                                isLoading={this.state.playerLoading}
                                cacheOptions={false}
                                defaultOptions={this.state.playerOptions}
                              />
                            </Grid>
                            <Grid item xs={12} md={4}>
                              <label className="modal-label">Teams</label>
                              <AsyncSelect
                                isMulti
                                className="React teamsport-select news-multi-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                loadOptions={async (inputValue, callback) => {
                                  if (inputValue !== this.state.teamSearch) {
                                    this.setState({
                                      teamOffset: 0,
                                      teamOptions: [],
                                      teamHasMore: true,
                                    });
                                  }
                                  const options = await this.fetchAllTeams(
                                    inputValue,
                                    0,
                                    this.state.teamLimit,
                                    this.state.articleValues.NewsCategorySportId
                                  );
                                  callback(options);
                                }}
                                onMenuScrollToBottom={async () => {
                                  if (
                                    this.state.teamHasMore &&
                                    !this.state.teamLoading
                                  ) {
                                    const options = await this.fetchAllTeams(
                                      this.state.teamSearch,
                                      this.state.teamOffset,
                                      this.state.teamLimit,
                                      this.state.articleValues
                                        .NewsCategorySportId
                                    );
                                    this.setState({ teamOptions: options });
                                  }
                                }}
                                onChange={(option) => {
                                  this.setState(
                                    {
                                      selectedTeams: option,
                                      teamSearch: "",
                                      teamOffset: 0,
                                      teamOptions: [],
                                    },
                                    () => {
                                      this.fetchAllTeams(
                                        "",
                                        0,
                                        this.state.teamLimit,
                                        this.state.articleValues
                                          .NewsCategorySportId
                                      );
                                    }
                                  );
                                }}
                                onInputChange={(inputValue, { action }) => {
                                  if (
                                    action === "input-blur" ||
                                    action === "menu-close"
                                  ) {
                                    this.setState({ teamSearch: "" }, () => {
                                      // Fetch original data when search is cleared
                                      this.fetchAllTeams(
                                        "",
                                        0,
                                        this.state.teamLimit,
                                        this.state.articleValues
                                          .NewsCategorySportId
                                      );
                                    });
                                  }
                                  return inputValue;
                                }}
                                value={this.state.selectedTeams}
                                placeholder="Select teams..."
                                isLoading={this.state.teamLoading}
                                cacheOptions
                                defaultOptions={this.state.teamOptions}
                              />
                            </Grid>
                          </Grid>
                          {/* Read Only Tags for Sports */}
                          <Grid
                            container
                            spacing={2}
                            style={{ marginTop: "10px" }}
                          >
                            <Grid item xs={12}>
                              <div className="selected-tags">
                                <div
                                  className="tag-container"
                                  style={{
                                    display: "flex",
                                    flexWrap: "wrap",
                                    gap: "8px",
                                  }}
                                >
                                  {this.state.selectedTournaments?.map(
                                    (tournament) => (
                                      <Chip
                                        key={tournament.value}
                                        label={tournament.label}
                                        variant="outlined"
                                        size="small"
                                      />
                                    )
                                  )}
                                  {this.state.selectedPlayers?.map((player) => (
                                    <Chip
                                      key={player.value}
                                      label={player.label}
                                      variant="outlined"
                                      size="small"
                                    />
                                  ))}
                                  {this.state.selectedTeams?.map((team) => (
                                    <Chip
                                      key={team.value}
                                      label={team.label}
                                      variant="outlined"
                                      size="small"
                                    />
                                  ))}
                                </div>
                              </div>
                            </Grid>
                          </Grid>
                        </Grid>
                      )}

                      {/* Racing Sports Select Boxes */}
                      {IS_RACING && (
                        <Grid item xs={12} style={{ marginBottom: "15px" }}>
                          <Grid container spacing={2}>
                            {/* Horse Racing (SPORTSID 1) and Harness Racing (SPORTSID 2) */}
                            {(this.state?.articleValues?.NewsCategorySportId ===
                              1 ||
                              this.state?.articleValues?.NewsCategorySportId ===
                                2) && (
                              <>
                                <Grid item xs={12} md={4}>
                                  <label className="modal-label">Horses</label>
                                  <AsyncSelect
                                    isMulti
                                    className="React teamsport-select news-multi-select"
                                    classNamePrefix="select"
                                    menuPosition="fixed"
                                    loadOptions={async (
                                      inputValue,
                                      callback
                                    ) => {
                                      if (
                                        inputValue !== this.state.horseSearch
                                      ) {
                                        this.setState({
                                          horseOffset: 0,
                                          horseOptions: [],
                                          horseHasMore: true,
                                        });
                                      }
                                      const options = await this.fetchAllHorses(
                                        inputValue,
                                        this.state.horseOffset,
                                        this.state.horseLimit,
                                        this.state?.articleValues
                                          ?.NewsCategorySportId
                                      );
                                      callback(options);
                                    }}
                                    onInputChange={(inputValue, { action }) => {
                                      if (
                                        action === "input-blur" ||
                                        action === "menu-close"
                                      ) {
                                        this.setState(
                                          { horseSearch: "" },
                                          () => {
                                            // Fetch original data when search is cleared
                                            this.fetchAllHorses(
                                              "",
                                              0,
                                              this.state.horseLimit,
                                              this.state?.articleValues
                                                ?.NewsCategorySportId
                                            );
                                          }
                                        );
                                      }
                                      return inputValue;
                                    }}
                                    onChange={(selectedOptions) => {
                                      this.setState({
                                        selectedHorses: selectedOptions,
                                      });
                                    }}
                                    value={this.state.selectedHorses}
                                    placeholder="Select Horses"
                                    isClearable
                                    isLoading={this.state.horseLoading}
                                    cacheOptions
                                    defaultOptions={this.state.horseOptions}
                                    onMenuScrollToBottom={() => {
                                      if (
                                        this.state.horseHasMore &&
                                        !this.state.horseLoading
                                      ) {
                                        this.setState(
                                          (prevState) => ({
                                            horseOffset:
                                              prevState.horseOffset +
                                              prevState.horseLimit,
                                          }),
                                          () => {
                                            this.fetchAllHorses(
                                              this.state.horseSearch,
                                              this.state.horseOffset,
                                              this.state.horseLimit,
                                              this.state?.articleValues
                                                ?.NewsCategorySportId
                                            );
                                          }
                                        );
                                      }
                                    }}
                                  />
                                </Grid>

                                {/* Jockeys for Horse Racing (SPORTSID 1) */}
                                {this.state?.articleValues
                                  ?.NewsCategorySportId === 1 && (
                                  <Grid item xs={12} md={4}>
                                    <label className="modal-label">
                                      Jockeys
                                    </label>
                                    <AsyncSelect
                                      isMulti
                                      className="React teamsport-select news-multi-select"
                                      classNamePrefix="select"
                                      menuPosition="fixed"
                                      loadOptions={async (
                                        inputValue,
                                        callback
                                      ) => {
                                        if (
                                          inputValue !== this.state.jockeySearch
                                        ) {
                                          this.setState({
                                            jockeyOffset: 0,
                                            jockeyOptions: [],
                                            jockeyHasMore: true,
                                          });
                                        }
                                        const options =
                                          await this.fetchAllJockeys(
                                            inputValue,
                                            this.state.jockeyOffset,
                                            this.state.jockeyLimit
                                          );
                                        callback(options);
                                      }}
                                      onInputChange={(
                                        inputValue,
                                        { action }
                                      ) => {
                                        if (
                                          action === "input-blur" ||
                                          action === "menu-close"
                                        ) {
                                          this.setState(
                                            { jockeySearch: "" },
                                            () => {
                                              // Fetch original data when search is cleared
                                              this.fetchAllJockeys(
                                                "",
                                                0,
                                                this.state.jockeyLimit
                                              );
                                            }
                                          );
                                        }
                                        return inputValue;
                                      }}
                                      onChange={(selectedOptions) => {
                                        this.setState({
                                          selectedJockeys: selectedOptions,
                                        });
                                      }}
                                      value={this.state.selectedJockeys}
                                      placeholder="Select Jockeys"
                                      isClearable
                                      isLoading={this.state.jockeyLoading}
                                      cacheOptions
                                      defaultOptions={this.state.jockeyOptions}
                                      onMenuScrollToBottom={() => {
                                        if (
                                          this.state.jockeyHasMore &&
                                          !this.state.jockeyLoading
                                        ) {
                                          this.setState(
                                            (prevState) => ({
                                              jockeyOffset:
                                                prevState.jockeyOffset +
                                                prevState.jockeyLimit,
                                            }),
                                            () => {
                                              this.fetchAllJockeys(
                                                this.state.jockeySearch,
                                                this.state.jockeyOffset,
                                                this.state.jockeyLimit
                                              );
                                            }
                                          );
                                        }
                                      }}
                                    />
                                  </Grid>
                                )}

                                {/* Drivers for Harness Racing (SPORTSID 2) */}
                                {this.state?.articleValues
                                  ?.NewsCategorySportId === 2 && (
                                  <Grid item xs={12} md={4}>
                                    <label className="modal-label">
                                      Drivers
                                    </label>
                                    <AsyncSelect
                                      isMulti
                                      className="React teamsport-select news-multi-select"
                                      classNamePrefix="select"
                                      menuPosition="fixed"
                                      loadOptions={async (
                                        inputValue,
                                        callback
                                      ) => {
                                        if (
                                          inputValue !== this.state.driverSearch
                                        ) {
                                          this.setState({
                                            driverOffset: 0,
                                            driverOptions: [],
                                            driverHasMore: true,
                                          });
                                        }
                                        const options =
                                          await this.fetchAllDrivers(
                                            inputValue,
                                            this.state.driverOffset,
                                            this.state.driverLimit,
                                            this.state?.articleValues
                                              ?.NewsCategorySportId
                                          );
                                        callback(options);
                                      }}
                                      onInputChange={(
                                        inputValue,
                                        { action }
                                      ) => {
                                        if (
                                          action === "input-blur" ||
                                          action === "menu-close"
                                        ) {
                                          this.setState(
                                            { driverSearch: "" },
                                            () => {
                                              // Fetch original data when search is cleared
                                              this.fetchAllDrivers(
                                                "",
                                                0,
                                                this.state.driverLimit,
                                                this.state?.articleValues
                                                  ?.NewsCategorySportId
                                              );
                                            }
                                          );
                                        }
                                        return inputValue;
                                      }}
                                      onChange={(selectedOptions) => {
                                        this.setState({
                                          selectedDrivers: selectedOptions,
                                        });
                                      }}
                                      value={this.state.selectedDrivers}
                                      placeholder="Select Drivers"
                                      isClearable
                                      isLoading={this.state.driverLoading}
                                      cacheOptions
                                      defaultOptions={this.state.driverOptions}
                                      onMenuScrollToBottom={() => {
                                        if (
                                          this.state.driverHasMore &&
                                          !this.state.driverLoading
                                        ) {
                                          this.setState(
                                            (prevState) => ({
                                              driverOffset:
                                                prevState.driverOffset +
                                                prevState.driverLimit,
                                            }),
                                            () => {
                                              this.fetchAllDrivers(
                                                this.state.driverSearch,
                                                this.state.driverOffset,
                                                this.state.driverLimit,
                                                this.state?.articleValues
                                                  ?.NewsCategorySportId
                                              );
                                            }
                                          );
                                        }
                                      }}
                                    />
                                  </Grid>
                                )}
                              </>
                            )}

                            {/* Greyhound Racing (SPORTSID 3) */}
                            {this.state?.articleValues?.NewsCategorySportId ===
                              3 && (
                              <Grid item xs={12} md={4}>
                                <label className="modal-label">
                                  Greyhounds
                                </label>
                                <AsyncSelect
                                  isMulti
                                  className="React teamsport-select news-multi-select"
                                  classNamePrefix="select"
                                  menuPosition="fixed"
                                  loadOptions={async (inputValue, callback) => {
                                    if (
                                      inputValue !== this.state.greyhoundSearch
                                    ) {
                                      this.setState({
                                        greyhoundOffset: 0,
                                        greyhoundOptions: [],
                                        greyhoundHasMore: true,
                                      });
                                    }
                                    const options =
                                      await this.fetchAllGreyhounds(
                                        inputValue,
                                        this.state.greyhoundOffset,
                                        this.state.greyhoundLimit,
                                        this.state?.articleValues
                                          ?.NewsCategorySportId
                                      );
                                    callback(options);
                                  }}
                                  onInputChange={(inputValue, { action }) => {
                                    if (
                                      action === "input-blur" ||
                                      action === "menu-close"
                                    ) {
                                      this.setState(
                                        { greyhoundSearch: "" },
                                        () => {
                                          // Fetch original data when search is cleared
                                          this.fetchAllGreyhounds(
                                            "",
                                            0,
                                            this.state.greyhoundLimit,
                                            this.state?.articleValues
                                              ?.NewsCategorySportId
                                          );
                                        }
                                      );
                                    }
                                    return inputValue;
                                  }}
                                  onChange={(selectedOptions) => {
                                    this.setState({
                                      selectedGreyhounds: selectedOptions,
                                    });
                                  }}
                                  value={this.state.selectedGreyhounds}
                                  placeholder="Select Greyhounds"
                                  isClearable
                                  isLoading={this.state.greyhoundLoading}
                                  cacheOptions
                                  defaultOptions={this.state.greyhoundOptions}
                                  onMenuScrollToBottom={() => {
                                    if (
                                      this.state.greyhoundHasMore &&
                                      !this.state.greyhoundLoading
                                    ) {
                                      this.setState(
                                        (prevState) => ({
                                          greyhoundOffset:
                                            prevState.greyhoundOffset +
                                            prevState.greyhoundLimit,
                                        }),
                                        () => {
                                          this.fetchAllGreyhounds(
                                            this.state.greyhoundSearch,
                                            this.state.greyhoundOffset,
                                            this.state.greyhoundLimit,
                                            this.state?.articleValues
                                              ?.NewsCategorySportId
                                          );
                                        }
                                      );
                                    }
                                  }}
                                />
                              </Grid>
                            )}

                            {/* Trainers for all racing types */}
                            <Grid item xs={12} md={4}>
                              <label className="modal-label">Trainers</label>
                              <AsyncSelect
                                isMulti
                                className="React teamsport-select news-multi-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                loadOptions={async (inputValue, callback) => {
                                  if (inputValue !== this.state.trainerSearch) {
                                    this.setState({
                                      trainerOffset: 0,
                                      trainerOptions: [],
                                      trainerHasMore: true,
                                    });
                                  }
                                  const options = await this.fetchAllTrainers(
                                    inputValue,
                                    this.state.trainerOffset,
                                    this.state.trainerLimit
                                  );
                                  callback(options);
                                }}
                                onInputChange={(inputValue, { action }) => {
                                  if (
                                    action === "input-blur" ||
                                    action === "menu-close"
                                  ) {
                                    this.setState({ trainerSearch: "" }, () => {
                                      // Fetch original data when search is cleared
                                      this.fetchAllTrainers(
                                        "",
                                        0,
                                        this.state.trainerLimit
                                      );
                                    });
                                  }
                                  return inputValue;
                                }}
                                onChange={(selectedOptions) => {
                                  this.setState({
                                    selectedTrainers: selectedOptions,
                                  });
                                }}
                                value={this.state.selectedTrainers}
                                placeholder="Select Trainers"
                                isClearable
                                isLoading={this.state.trainerLoading}
                                cacheOptions
                                defaultOptions={this.state.trainerOptions}
                                onMenuScrollToBottom={() => {
                                  if (
                                    this.state.trainerHasMore &&
                                    !this.state.trainerLoading
                                  ) {
                                    this.setState(
                                      (prevState) => ({
                                        trainerOffset:
                                          prevState.trainerOffset +
                                          prevState.trainerLimit,
                                      }),
                                      () => {
                                        this.fetchAllTrainers(
                                          this.state.trainerSearch,
                                          this.state.trainerOffset,
                                          this.state.trainerLimit
                                        );
                                      }
                                    );
                                  }
                                }}
                              />
                            </Grid>
                          </Grid>
                          {/* Read Only Tags for Racing */}
                          <Grid
                            container
                            spacing={2}
                            style={{ marginTop: "10px" }}
                          >
                            <Grid item xs={12}>
                              <div className="selected-tags">
                                <div
                                  className="tag-container"
                                  style={{
                                    display: "flex",
                                    flexWrap: "wrap",
                                    gap: "8px",
                                  }}
                                >
                                  {/* Horse Racing (SPORTSID 1) and Harness Racing (SPORTSID 2) */}
                                  {(this.state?.articleValues
                                    ?.NewsCategorySportId === 1 ||
                                    this.state?.articleValues
                                      ?.NewsCategorySportId === 2) && (
                                    <>
                                      {this.state.selectedHorses?.map(
                                        (horse) => (
                                          <Chip
                                            key={horse.value}
                                            label={horse.label}
                                            variant="outlined"
                                            size="small"
                                          />
                                        )
                                      )}
                                      {/* Jockeys for Horse Racing (SPORTSID 1) */}
                                      {this.state?.articleValues
                                        ?.NewsCategorySportId === 1 &&
                                        this.state.selectedJockeys?.map(
                                          (jockey) => (
                                            <Chip
                                              key={jockey.value}
                                              label={jockey.label}
                                              variant="outlined"
                                              size="small"
                                            />
                                          )
                                        )}
                                      {/* Drivers for Harness Racing (SPORTSID 2) */}
                                      {this.state?.articleValues
                                        ?.NewsCategorySportId === 2 &&
                                        this.state.selectedDrivers?.map(
                                          (driver) => (
                                            <Chip
                                              key={driver.value}
                                              label={driver.label}
                                              variant="outlined"
                                              size="small"
                                            />
                                          )
                                        )}
                                    </>
                                  )}
                                  {/* Greyhound Racing (SPORTSID 3) */}
                                  {this.state?.articleValues
                                    ?.NewsCategorySportId === 3 &&
                                    this.state.selectedGreyhounds?.map(
                                      (greyhound) => (
                                        <Chip
                                          key={greyhound.value}
                                          label={greyhound.label}
                                          variant="outlined"
                                          size="small"
                                        />
                                      )
                                    )}
                                  {/* Trainers for all racing types */}
                                  {this.state.selectedTrainers?.map(
                                    (trainer) => (
                                      <Chip
                                        key={trainer.value}
                                        label={trainer.label}
                                        variant="outlined"
                                        size="small"
                                      />
                                    )
                                  )}
                                </div>
                              </div>
                            </Grid>
                          </Grid>
                        </Grid>
                      )}

                      {!SPORTSID && (
                        <Grid item xs={12} style={{ marginBottom: "15px" }}>
                          <Box
                            style={{ display: "flex", alignItems: "center" }}
                          >
                            <label className="modal-label"> Tags </label>
                          </Box>
                          <Select
                            className="React teamsport-select news-multi-select"
                            classNamePrefix="select"
                            menuPosition="fixed"
                            isMulti
                            isLoading={isTagLoading}
                            onMenuScrollToBottom={(e) =>
                              this.handleOnScrollBottomModalTeam(e)
                            }
                            onInputChange={(e) =>
                              this.handleTagInputChange(0, e)
                            }
                            value={articleValues?.tag}
                            options={isTagSearch ? searchTag : tagList}
                            // value={playerValues?.team}
                            onChange={(e) =>
                              this.setState({
                                articleValues: {
                                  ...articleValues,
                                  tag: e,
                                },
                              })
                            }
                          />
                          {errorTag ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0 0 0" }}
                            >
                              {errorTag}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                      )}

                      <Grid item xs={12} style={{ marginBottom: "15px" }}>
                        <label className="modal-label"> Related Article </label>
                        <Select
                          className="React teamsport-select news-multi-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          isMulti
                          isLoading={isRealatedLoading}
                          value={articleValues?.relatedArticle}
                          options={RelatedArticleList}
                          // value={playerValues?.team}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                relatedArticle: e,
                              },
                            })
                          }
                        />
                        {errorRelatedArticle ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorRelatedArticle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={3} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Top Story </label>
                          <RadioGroup
                            aria-label="Top Story"
                            name="Top Story"
                            className="gender"
                            value={articleValues?.topStory}
                            onChange={(e) =>
                              this.setState({
                                articleValues: {
                                  ...articleValues,
                                  topStory: e.target.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="Yes"
                              control={
                                <Radio
                                  color="primary"
                                  checked={articleValues?.topStory === "Yes"}
                                />
                              }
                              label="Yes"
                            />
                            <FormControlLabel
                              value="No"
                              control={
                                <Radio
                                  color="primary"
                                  checked={articleValues?.topStory === "No"}
                                />
                              }
                              label="No"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorTopStory ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorTopStory}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={3} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label">
                            {" "}
                            Featured Article{" "}
                          </label>
                          <RadioGroup
                            aria-label="Featured Article"
                            name="Featured Article"
                            className="gender"
                            value={articleValues?.featuredArticle}
                            onChange={(e) =>
                              this.setState({
                                articleValues: {
                                  ...articleValues,
                                  featuredArticle: e.target.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="Yes"
                              control={
                                <Radio
                                  color="primary"
                                  checked={
                                    articleValues?.featuredArticle === "Yes"
                                  }
                                />
                              }
                              label="Yes"
                            />
                            <FormControlLabel
                              value="No"
                              control={
                                <Radio
                                  color="primary"
                                  checked={
                                    articleValues?.featuredArticle === "No"
                                  }
                                />
                              }
                              label="No"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorfeaturedArticle ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorfeaturedArticle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={3} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Home Article </label>
                          <RadioGroup
                            aria-label="Home Article"
                            name="Home Article"
                            className="gender"
                            value={articleValues?.homePageArticles}
                            onChange={(e) =>
                              this.setState({
                                articleValues: {
                                  ...articleValues,
                                  homePageArticles: e.target.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="Yes"
                              control={
                                <Radio
                                  color="primary"
                                  checked={
                                    articleValues?.homePageArticles === "Yes"
                                  }
                                />
                              }
                              label="Yes"
                            />
                            <FormControlLabel
                              value="No"
                              control={
                                <Radio
                                  color="primary"
                                  checked={
                                    articleValues?.homePageArticles === "No"
                                  }
                                />
                              }
                              label="No"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorHomeArticle ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorHomeArticle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={3} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Getty Images </label>
                          <RadioGroup
                            aria-label="Getty Images"
                            name="Getty Images"
                            className="gender"
                            value={articleValues?.gettyImages}
                            onChange={(e) =>
                              this.setState({
                                articleValues: {
                                  ...articleValues,
                                  gettyImages: e.target.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="Yes"
                              control={
                                <Radio
                                  color="primary"
                                  checked={articleValues?.gettyImages === "Yes"}
                                />
                              }
                              label="Yes"
                            />
                            <FormControlLabel
                              value="No"
                              control={
                                <Radio
                                  color="primary"
                                  checked={articleValues?.gettyImages === "No"}
                                />
                              }
                              label="No"
                            />
                          </RadioGroup>
                        </FormControl>
                      </Grid>
                      <div
                        className="blog-file-upload"
                        style={{
                          width: "97%",
                          marginTop: "10px",
                          marginBottom: "10px",
                        }}
                      >
                        <label className="modal-label"> Feature Image </label>
                        <div
                          style={{
                            display: "flex",
                            gap: "20px",
                            marginBottom: "10px",
                          }}
                        >
                          <div style={{ flex: 1 }}>
                            <FileUploader
                              onDrop={(image) => {
                                this.handleFileUpload("image", image);
                              }}
                              style={{ marginTop: "5px" }}
                            />
                          </div>
                        </div>
                        <div className="logocontainer">
                          {image?.length > 0
                            ? image?.map((file, index) => (
                                <img
                                  className="auto-width"
                                  key={index}
                                  src={file.preview}
                                  alt="feature"
                                />
                              ))
                            : uploadImage &&
                              uploadImage !== "" && (
                                <img
                                  className="auto-width"
                                  src={uploadImage}
                                  alt="feature"
                                />
                              )}
                        </div>
                        <div style={{ marginTop: "10px" }}>
                          <label
                            style={{ display: "block", marginBottom: "5px" }}
                            className="modal-label"
                          >
                            Getty Images Embed Code
                          </label>
                          <TextField
                            className="teamsport-textfield"
                            variant="outlined"
                            color="primary"
                            size="small"
                            placeholder="Paste Getty Images embed code here"
                            multiline
                            rows={3}
                            value={embedCode}
                            onChange={(e) => {
                              this.handleEmbedCodeChange(e?.target?.value);
                              this.setState({
                                errorEmbedCode: e?.target?.value
                                  ? ""
                                  : errorEmbedCode,
                              });
                            }}
                            fullWidth
                          />
                          {errorEmbedCode ? (
                            <p
                              className="errorText"
                              style={{ margin: "5px 0 0 0" }}
                            >
                              {errorEmbedCode}
                            </p>
                          ) : (
                            ""
                          )}
                          {!isEditMode && (
                            <div
                              className="embed-preview"
                              dangerouslySetInnerHTML={{
                                __html: embedCode,
                              }}
                            />
                          )}
                          {embedCode && isEditMode && (
                            <GettyEmbed embedCode={embedCode} />
                          )}
                          {/* {this.state.uploadImage && (
                            <div
                              className="logocontainer"
                              style={{ marginTop: "10px" }}
                            >
                              <img
                                className="auto-width"
                                src={this.state.uploadImage}
                                alt="Getty Images preview"
                              />
                            </div>
                          )} */}
                        </div>
                      </div>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Feature image caption{" "}
                        </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder=" Feature image caption"
                          value={decodedAlt}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                alt: e.target.value,
                              },
                            })
                          }
                        />
                        {errorAlt ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorAlt}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* <Grid
                      item
                      xs={6}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label"> Author name </label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Author name"
                        value={articleValues?.author}
                        onChange={(e) =>
                          this.setState({
                            articleValues: {
                              ...articleValues,
                              author: e.target.value,
                            },
                          })
                        }
                      />
                      {/* {errorAlt ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorAlt}
                        </p>
                      ) : (
                        ""
                      )} /}
                    </Grid> */}
                      {/* <Grid
                      item
                      xs={6}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label"> Publisher name </label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Publisher name"
                        value={articleValues?.publisherName}
                        onChange={(e) =>
                          this.setState({
                            articleValues: {
                              ...articleValues,
                              publisherName: e.target.value,
                            },
                          })
                        }
                      />
                      {/* {errorAlt ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorAlt}
                        </p>
                      ) : (
                        ""
                      )} /}
                    </Grid> */}
                      {isEditMode && (
                        <Grid
                          item
                          xs={6}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text national-select text-capitalize"
                        >
                          <label className="modal-label"> Status </label>
                          <Select
                            className="React teamsport-select text-capitalize "
                            classNamePrefix="select"
                            placeholder="Select Status"
                            value={newsModalStatusOption?.find((item) => {
                              return item?.label == selectedModalNewsStatus;
                            })}
                            //   isLoading={isLoading}
                            onChange={(e) =>
                              this.setState({
                                selectedModalNewsStatus: e?.label,
                              })
                            }
                            options={newsModalStatusOption}
                          />
                          {/* {errorAlt ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorAlt}
                        </p>
                      ) : (
                        ""
                      )} */}
                        </Grid>
                      )}
                      {/* <div
                      className="blog-file-upload"
                      style={{ width: "97%", marginTop: "10px" }}
                    >
                      <label className="modal-label"> Author Image </label>
                      <FileUploader
                        onDrop={(image) =>
                          this.handleFileUpload("authorImage", image)
                        }
                        style={{ marginTop: "5px" }}
                      />
                      <div className="logocontainer">
                        {authorImage?.length > 0
                          ? authorImage?.map((file, index) => (
                              <img
                                className="auto-width"
                                key={index}
                                src={file.preview}
                                alt="player"
                              />
                            ))
                          : authorUploadImage &&
                            authorUploadImage !== "" && (
                              <img
                                className="auto-width"
                                src={
                                  articleValues?.NewsProviderId === 1
                                    ? authorUploadImage?.includes("uploads")
                                      ? authorUploadImage
                                      : `https://www.livescore.com${authorUploadImage}`
                                    : authorUploadImage
                                }
                                alt="player"
                              />
                            )}
                      </div>
                    </div> */}
                    </Grid>
                    {articleValues?.NewsProviderId !== 1 ? (
                      <>
                        {/* <div>
                        <button onClick={this.handleAddImage}>Add Image</button>
                        <Editor
                          editorState={editorState}
                          onEditorStateChange={this.handleEditorChange}
                        />
                        <ImageUploader
                          onImageUpload={(image) =>
                            this.handleImageUpload(image)
                          }
                          handeModleLoading={(status) =>
                            this.handeModleLoading(status)
                          }
                        />
                      </div> */}
                        <div className="suneditor-wrap">
                          {/* <label>123456</label> */}
                          <SunEditor
                            onChange={this.handleChange}
                            setContents={content}
                            setOptions={{
                              buttonList: [
                                ["undo", "redo"],
                                ["font", "fontSize", "formatBlock"],
                                [
                                  "bold",
                                  "underline",
                                  "italic",
                                  "strike",
                                  "subscript",
                                  "superscript",
                                ],
                                ["removeFormat"],
                                ["outdent", "indent"],
                                ["align", "horizontalRule", "list", "table"],
                                ["link", "image", "video"],
                                ["fullScreen", "showBlocks", "codeView"],
                              ],
                            }}
                          />
                        </div>
                      </>
                    ) : (
                      <></>
                    )}

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={isViewImgModalOpen}
              onClose={this.toggleViewImgModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">View Image</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleViewImgModal}
                />
                <Grid item xs={12} className="runnerInfo">
                  <Grid
                    item
                    xs={12}
                    className="runnerInfo-text"
                    style={{ display: "flex", flexDirection: "column" }}
                  >
                    {isViewImgData?.embeddedImageStatus ? (
                      <GettyEmbed embedCode={isViewImgData?.embeddedImage} />
                    ) : (
                      <Box style={{ margin: "0 auto" }}>
                        {isViewImgData?.mainMedia?.gallery ? (
                          <img
                            className="auto-width"
                            src={isViewImgData?.mainMedia?.gallery?.url}
                            alt="featureImage"
                          />
                        ) : isViewImgData?.mainMedia?.[0]?.gallery ? (
                          <img
                            className="auto-width"
                            src={isViewImgData?.mainMedia?.[0]?.gallery.url}
                            alt="featureImage"
                          />
                        ) : (
                          ""
                        )}
                      </Box>
                    )}
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        onClick={this.toggleViewImgModal}
                        // className="mr-lr-30"
                        value="Back"
                        style={{ minWidth: "auto" }}
                      />
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default NewsArticle;
