import React, { createRef } from "react";
import { <PERSON>rid, Button, Box } from "@mui/material";
import { trackFormModel } from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
import { setValidation } from "../../../helpers/common";
import Select from "react-select";
import FileUploader from "../../../library/common/components/FileUploader";
import DeleteIcon from "@mui/icons-material/Delete";
import _ from "lodash";
import { config } from "../../../helpers/config";

let trackFormModelArray = trackFormModel;

const races = [
  { id: 1, name: "Horse Racing" },
  { id: 2, name: "Harness Racing" },
  { id: 3, name: "Greyhound Racing" },
];

class CreateTrack extends React.Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      values: {
        name: "",
        variation: "",
      },
      state: null,
      stateAll: [],
      country: null,
      countryAll: [],
      city: null,
      cityAll: [],
      checkedRace: "",
      error: {},
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
      page: 0,
      count: "",
      pageCountry: 0,
      countCountry: "",
      searchCountry: [],
      searchCountryCount: "",
      SearchCountrypage: 0,
      isCountrySearch: "",
      pageState: 0,
      countState: "",
      searchState: [],
      searchStateCount: "",
      SearchStatepage: 0,
      isStateSearch: "",
      defaultImage: [],
      defaultUploadImage: "",
      fileFlag: false,
    };
  }

  componentDidMount() {
    if (this.props.isEditMode) {
      this.fetchCurrentTrack(this.props.id);
    }
    this.fetchAllCountry(0);
  }

  componentWillUnmount() {
    trackFormModelArray = trackFormModelArray.map((fieldItem) => {
      return { ...fieldItem, errorMessage: "" };
    });
  }

  // fetchAllCountry = async () => {
  //   const { status, data } = await axiosInstance.get(URLS.country);
  //   if (status === 200) {
  //     this.setState({ countryAll: data.result?.rows });
  //   }
  // };
  fetchAllCountry = async (page) => {
    const { status, data } = await axiosInstance.get(
      // `${URLS.distance}?size=20&page=${page}`
      `${URLS.country}?limit=20&offset=${page}`
    );

    if (status === 200) {
      let Parray = [...this.state.countryAll];

      let count = data?.result?.count / 20;
      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.country,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state.countryAll, newdata);

      this.setState({
        countryAll: _.uniqBy(filterData, function (e) {
          return e.value;
        }),
        count: Math.ceil(count),
      });
    }
  };

  // fetchAllState = async (id) => {
  //   const { status, data } = await axiosInstance.get(
  //     URLS.state + `/country/${id}`
  //   );
  //   if (status === 200) {
  //     this.setState({ stateAll: data.result?.rows });
  //   }
  // };
  fetchAllState = async (id, pages) => {
    const { status, data } = await axiosInstance.get(
      URLS.state + `/country/${id}?limit=20&offset=${pages}`
      // `${URLS.distance}/country/${id}?size=20&page=${pageState}`
      // `${URLS.country}?limit=20&offset=${pageState}`
    );

    if (status === 200) {
      let Parray = [...this.state.stateAll];
      let count = data?.result?.count / 20;
      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.state,
          value: item?.id,
        });
      });

      let filterData = _.unionBy(this.state.stateAll, newdata);
      let sortData = filterData?.sort((a, b) => {
        return a.label > b.label ? 1 : -1;
      });
      this.setState({
        stateAll: _.uniqBy(sortData, function (e) {
          return e.value;
        }),
        countState: Math.ceil(count),
      });

      // this.setState({
      //   stateAll: _.unionBy(this.state.stateAll, newdata),
      //   countState: Math.ceil(count),
      // });
    }
  };

  fetchAllCity = async (id) => {
    const { status, data } = await axiosInstance.get(
      URLS.cities + `/state/${id}`
    );
    if (status === 200) {
      let newData = [];
      let track = data?.result?.rows?.map((item) => {
        newData.push({
          label: item?.cityName,
          value: item?.id,
        });
      });
      this.setState({ cityAll: newData });
    }
  };

  fetchCurrentTrack = async (id) => {
    let { rowToPass } = this.props;
    const { status, data } = await axiosInstance.get(URLS.track + `/${id}`);

    if (status === 200) {
      let seletedCountry = [
        {
          label: data.result?.Country?.country,
          value: data.result?.Country?.id,
        },
      ];
      let SelectedState = [
        {
          label: data.result?.State?.state,
          value: data.result?.State?.id,
        },
      ];
      let SelectedCity = [
        {
          label: data.result?.City?.cityName,
          value: data.result?.City?.id,
        },
      ];
      this.setState({
        values: data.result,
        country: data.result?.Country?.id ? data.result?.Country?.id : "",
        countryAll: data.result?.Country?.id
          ? seletedCountry
          : this.state.countryAll,
        stateAll: data.result?.State?.state.id
          ? SelectedState
          : this.state.stateAll,
        state: data?.result?.stateId,
        cityAll: data?.result?.stateId ? SelectedCity : [],
        city: data?.result?.stateId ? data?.result?.cityId : "",
        checkedRace: data?.result?.sportId,
        defaultUploadImage: data?.result?.banner_image,
      });
      // this.fetchAllCountry(0);
      if (rowToPass?.countryId !== null) {
        this.fetchAllState(rowToPass?.countryId, 0);
      }

      if (rowToPass?.stateId !== null) {
        this.fetchAllCity(rowToPass?.stateId);
      }

      // let variationArray = JSON.parse("[" + data?.result?.variation + "]");
      // if (variationArray.length > 0) {
      // this.setState(() => {
      //   return {
      //     values: {
      //       ...this.state.values,
      //       ["variation"]: data?.result?.variation,
      //     },
      //   };
      // });
      // }
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } });
  };

  validate = () => {
    let { name } = this.state.values;
    // let { state, country, city, error } = this.state;
    let flag = true;

    if (name?.trim() === "") {
      flag = false;
      //this.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      //this.setActionMessage(false);
    }

    // if (country === null) {
    //   error.country = "This field id mandatory";
    //   flag = false;
    //   this.setState({ isLoading: false });
    // } else {
    //   error.country = "";
    // }

    // if (state === null) {
    //   error.state = "This field is mandatory";
    //   flag = false;
    //   this.setState({ isLoading: false });
    // } else {
    //   error.state = "";
    // }

    return flag;
  };

  handleSave = async () => {
    const { isEditMode } = this.props;
    const { country, state, city, checkedRace, defaultImage, fileFlag } =
      this.state;
    this.setState({ isLoading: true });
    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode ? `${URLS.track}/${this.props.id}` : `/track`;

      const values = removeErrorFieldsFromValues(form.formData);

      let DataToCreate = {
        name: values?.name,
        variation: values?.variation,
        stateId: parseInt(state),
        countryId: parseInt(country),
        cityId: parseInt(city),
        sportId: checkedRace,
      };

      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          DataToCreate = {
            ...DataToCreate,
            banner_image: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      } else {
        DataToCreate = {
          ...DataToCreate,
          banner_image: null,
        };
        this.setState({
          defaultUploadImage: "",
        });
      }

      if (isEditMode) {
        DataToCreate = {
          ...DataToCreate,
          flag: fileFlag,
        };
      }
      trackFormModelArray = trackFormModelArray?.map((fieldItem) => {
        return setValidation(fieldItem, values);
      });
      if (this.validate()) {
        const { status, data } = await axiosInstance[method](url, DataToCreate);
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          this.props.fetchAllTrack();
          this.setActionMessage(true, "Success", data?.message);
        }
      }
    } catch (err) {
      this.setState({ isLoading: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handleChange = (field, value) => {
    let values = { ...this.state.values, [field]: value };
    this.setState({ values: values });
    trackFormModelArray = trackFormModelArray?.map((fieldItem) => {
      if (field === fieldItem?.field) {
        return setValidation(fieldItem, values);
      } else {
        return fieldItem;
      }
    });
    this.setActionMessage(false);
  };

  // handleSelect = (e) => {
  //   let name = e.target.name;
  //   let value = e.target.value;
  //   if (name === "state") {
  //     this.setState(
  //       { state: value /*error: { ...this.state.error, ["state"]: "" }*/ },
  //       () => this.fetchAllCity(value)
  //     );
  //   }
  //   if (name === "country") {
  //     this.setState(
  //       { country: value /*error: { ...this.state.error, ["country"]: "" }*/ },
  //       () => this.fetchAllState(value)
  //     );
  //   }
  //   if (name === "city") {
  //     this.setState({
  //       city: Number(value),
  //       /*error: { ...this.state.error, ["city"]: "" },*/
  //     });
  //   }
  // };

  handleTrackRaceChange = (e) => {
    let value = e.target.value;
    let isChecked = e.target.checked;
    this.setState({
      checkedRace: value,
    });
  };
  handleOnScrollBottomCountry = () => {
    let {
      // countCountry,
      pageCountry,
      isCountrySearch,
      searchCountryCount,
      SearchCountrypage,
      count,
    } = this.state;

    if (
      isCountrySearch !== "" &&
      searchCountryCount !== Math.ceil(SearchCountrypage / 20 + 1)
    ) {
      this.handleCountryInputChange(SearchCountrypage + 20, isCountrySearch);
      this.setState({
        SearchCountrypage: SearchCountrypage + 20,
      });
    } else {
      if (count !== Math.ceil(pageCountry / 20)) {
        this.fetchAllCountry(pageCountry + 20);
        this.setState({
          pageCountry: pageCountry + 20,
        });
      }
    }
  };
  handleOnScrollBottomState = () => {
    let {
      countState,
      pageState,
      isStateSearch,
      searchStateCount,
      SearchStatepage,
      country,
    } = this.state;
    let { rowToPass } = this.props;

    if (
      isStateSearch !== "" &&
      searchStateCount !== Math.ceil(SearchStatepage / 20)
    ) {
      this.handleStateInputChange(SearchStatepage + 20, isStateSearch);
      this.setState({
        SearchStatepage: SearchStatepage + 20,
      });
    } else {
      if (rowToPass?.countryId !== null) {
        if (countState !== Math.ceil(pageState / 20)) {
          this.fetchAllState(
            country ? country : rowToPass?.countryId,
            pageState + 20
          );
          this.setState({
            pageState: pageState + 20,
          });
        }
      }
    }
  };
  handleCountryInputChange = (page, value) => {
    // if (value.length > 2) {
    axiosInstance
      .get(`${URLS.country}?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          //     let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(this.state.searchCountry, newdata);

          this.setState({
            searchCountry: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchCountryCount: Math.ceil(count),
            isCountrySearch: value,
          });
        }
      });
    // } else {
    //   this.setState({
    //     isCountrySearch: "",
    //     SearchCountrypage: 1,
    //     searchCountry: [],
    //   });
    // }
  };
  handleStateInputChange = (page, value) => {
    const { rowToPass } = this.props;
    // if (value.length > 2) {
    axiosInstance
      .get(
        `${URLS.state}/country/${this.state?.country}?limit=20&offset=${page}&search=${value}`
      )
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;

          let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.state,
              value: item?.id,
            });
          });
          const finalStateData = newdata.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          });
          let filterData = _.unionBy(this.state.searchState, finalStateData);

          this.setState({
            searchState: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchStateCount: Math.ceil(count),
            isStateSearch: value,
          });
        }
      });
    // } else {
    //   this.setState({
    //     isStateSearch: "",
    //     SearchStatepage: 1,
    //     searchState: [],
    //   });
    // }
  };

  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    const { isEditMode } = this.props;
    if (isEditMode) {
      this.setState({ fileFlag: true });
    }
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };

  handleFileUpload = (name, files) => {
    const { isEditMode } = this.props;
    if (isEditMode) {
      this.setState({ fileFlag: true });
    }
    this.setState({ [name]: files });
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  render() {
    var {
      values,
      messageBox,
      isLoading,
      countryAll,
      stateAll,
      cityAll,
      country,
      state,
      city,
      checkedRace,
      isCountrySearch,
      searchCountry,
      searchState,
      isStateSearch,
      defaultImage,
      defaultUploadImage,
      fileFlag,
      // error,
    } = this.state;
    var { isEditMode } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="modal-label">
            {/* <Paper className="pageWrapper api-provider"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}

            <Form
              values={values}
              model={trackFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            />

            <div
              style={{
                // display: "flex",
                marginBottom: "27px",
                justifyContent: "space-between",
                width: "33%",
              }}
            >
              <label className="modal-label">Track Type</label>
              <div>
                {races?.map((obj, i) => (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginTop: "10px",
                    }}
                  >
                    <input
                      type="checkbox"
                      style={{
                        height: "18px",
                        width: "18px",
                        cursor: "pointer",
                        marginRight: "12px",
                      }}
                      id={obj?.name}
                      value={obj?.id}
                      name={obj?.name}
                      checked={checkedRace == obj?.id ? true : false}
                      onChange={(e) => this.handleTrackRaceChange(e)}
                    />
                    <label
                      htmlFor={obj?.name}
                      style={{
                        cursor: "pointer",
                        fontWeight: "500",
                      }}
                    >
                      {obj?.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div className="select-box-track">
              <label className="modal-label">Country</label>

              <Select
                className="React"
                // className="select-box-manual select-box-arrow React"
                classNamePrefix="select"
                onMenuScrollToBottom={(e) =>
                  this.handleOnScrollBottomCountry(e)
                }
                // isSearchable={false}
                onInputChange={(e) => this.handleCountryInputChange(0, e)}
                placeholder="No Country Selected"
                value={
                  isCountrySearch
                    ? searchCountry?.find((item) => {
                        return item?.value == country;
                      })
                    : countryAll?.find((item) => {
                        return item?.value == country;
                      })
                }
                onChange={(e) => {
                  this.setState({
                    country: e.value,
                    stateAll: [],
                    cityAll: [],
                    state: null,
                    city: null,
                  });
                  this.fetchAllState(e.value, 0);
                }}
                options={isCountrySearch ? searchCountry : countryAll}
              />
              {/* {typeof error.country !== "undefined" &&
                  error.country !== "" && (
                    <p style={{ color: "red", margin: "-18px 0px 0px" }}>
                      {error.country}
                    </p>
                  )} */}
              <label className="modal-label">State</label>

              <Select
                className="React"
                // className="select-box-manual select-box-arrow React"
                // isSearchable={false}
                classNamePrefix="select"
                onMenuScrollToBottom={(e) => this.handleOnScrollBottomState(e)}
                onInputChange={(e) => this.handleStateInputChange(0, e)}
                placeholder={
                  country ? "No State Selected" : "Select Country First"
                }
                value={
                  isStateSearch
                    ? searchState?.find((item) => {
                        return item?.value == state;
                      })
                    : state !== null &&
                      stateAll?.find((item) => {
                        return item?.value == state;
                      })
                }
                // value={
                //   state !== null &&
                //   stateAll?.find((item) => {
                //     return item?.value == state;
                //   })
                // }
                onChange={(e) => {
                  this.setState({
                    state: e.value,
                    city: null,
                    cityAll: [],
                  });
                  this.fetchAllCity(e.value);
                }}
                options={isStateSearch ? searchState : stateAll}
              />
              {/* {typeof error.state !== "undefined" && error.state !== "" && (
                  <p style={{ color: "red", margin: "-18px 0px 0px" }}>
                    {error.state}
                  </p>
                )} */}
              <label className="modal-label">City</label>
              <Select
                className="React"
                // className="select-box-manual select-box-arrow React"
                classNamePrefix="select"
                placeholder={state ? "No City  Selected" : "Select State First"}
                onChange={(e) => {
                  this.setState({
                    city: e.value,
                  });
                }}
                value={
                  city !== null &&
                  cityAll?.find((item) => {
                    return item?.value == city;
                  })
                }
                options={cityAll}
              />
              {/* {typeof error.city !== "undefined" && error.city !== "" && (
                  <p style={{ color: "red", margin: "-18px 0px 0px" }}>
                    {error.city}
                  </p>
                )} */}
            </div>

            <div
              className="blog-file-upload"
              style={{ width: "100%", marginTop: "10px" }}
            >
              <label className="modal-label"> Track Image </label>
              <FileUploader
                onDrop={(image) => this.handleFileUpload("defaultImage", image)}
                style={{ marginTop: "5px" }}
              />
              <Box
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <div className="logocontainer">
                  {defaultImage?.length > 0
                    ? defaultImage?.map((file, index) => (
                        <img
                          className="auto-width"
                          key={index}
                          src={file.preview}
                          alt="player"
                        />
                      ))
                    : defaultUploadImage &&
                      defaultUploadImage !== "" && (
                        <img
                          className="auto-width"
                          src={
                            defaultUploadImage?.includes("uploads")
                              ? config.mediaUrl + defaultUploadImage
                              : defaultUploadImage
                          }
                          alt="player"
                        />
                      )}
                </div>
                {(defaultImage?.length > 0 ||
                  (defaultUploadImage && defaultUploadImage !== "")) && (
                  <Box className="delete-icon-wrap">
                    <DeleteIcon
                      className="delete-icon"
                      onClick={() => this.handleFeatureLogoRemove()}
                      style={{ cursor: "pointer" }}
                    />
                  </Box>
                )}
              </Box>
            </div>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ margin: "20px 0px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-purple"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-purple"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                    />
                  )}

                  {/* <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Back"
                  /> */}
                  <Button
                    className="mr-lr-30 admin-btn-outlined"
                    variant="outlined"
                    // color="primary"
                    onClick={this.props.inputModal}
                  >
                    Back
                  </Button>
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateTrack;
