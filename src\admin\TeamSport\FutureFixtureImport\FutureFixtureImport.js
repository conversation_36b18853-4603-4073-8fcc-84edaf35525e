import React, { Component } from "react";
import { <PERSON>rid, Typo<PERSON>, <PERSON>, <PERSON>readcrum<PERSON>, Button } from "@mui/material";
import { Link } from "react-router-dom";
import axiosInstance from "../../../helpers/Axios";
import moment from "moment";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import { InputAdornment, IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import Select from "react-select";
import ActionMessage from "../../../library/common/components/ActionMessage";

class FutureFixtureImport extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: null,
      endDate: null,
      isLoading: false,
      isProviderLoading: false,
      isLeagueLoading: false,
      LeagueOption: [],
      selectedLeague: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      errorStartDate: "",
      errorEndDate: "",
      ProviderOption: [],
      selectedProvider: 0,
    };
  }
  handleStartDate = (date) => {
    this.setState({ startDate: date });
  };
  handleEndDate = (date) => {
    this.setState({ endDate: date });
  };
  handleValidate = () => {
    let { startDate, endDate } = this.state;
    let flag = true;
    if (!startDate) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    if (!endDate) {
      flag = false;
      this.setState({
        errorEndDate: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorEndDate: "",
      });
    }
    return flag;
  };

  fetchProviders = async () => {
    this.setState({
      isProviderLoading: true,
    });
    try {
      let SportId = this.props?.match?.path?.includes("cricket")
        ? 4
        : this.props?.match?.path?.includes("rugbyleague")
        ? 12
        : this.props?.match?.path?.includes("rugbyunion")
        ? 13
        : this.props?.match?.path?.includes("basketball")
        ? 10
        : this.props?.match?.path?.includes("afl")
        ? 15
        : this.props?.match?.path?.includes("australianrules")
        ? 9
        : this.props?.match?.path?.includes("golf")
        ? 16
        : this.props?.match?.path?.includes("tennis")
        ? 7
        : this.props?.match?.path?.includes("baseball")
        ? 11
        : this.props?.match?.path?.includes("icehockey")
        ? 17
        : this.props?.match?.path?.includes("boxing")
        ? 6
        : this.props?.match?.path?.includes("mma")
        ? 5
        : this.props?.match?.path?.includes("soccer")
        ? 8
        : 14;
      const { status, data } = await axiosInstance.get(
        `apiProviders/apiprovidersports?SportId=${SportId}`
      );
      if (status === 200) {
        this.setState({
          isProviderLoading: false,
        });
        let newdata = [];

        const filterData = data?.result?.filter(
          (item) => item?.ApiProviderId !== 16
        );
        let providers = filterData?.map((item) => {
          newdata.push({
            label: item?.ApiProvider?.providerName,
            value: item?.ApiProviderId,
          });
        });
        const sortedData = newdata?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Provider",
          value: 0,
        });
        this.setState({
          ProviderOption: sortedData,
        });
      } else {
        this.setState({
          isProviderLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isProviderLoading: false,
      });
    }
  };

  fetchLeague = async () => {
    this.setState({
      isLeagueLoading: true,
    });
    try {
      const { status, data } = await axiosInstance.get(`/soccer/sync/leagues`);
      if (status === 200) {
        let newdata = [];
        const filterData = data?.data?.leagues;
        let leagues = filterData?.map((item) => {
          newdata.push({
            label:
              item?.name + " " + item?.season + " " + "(" + item?.country + ")",
            value: item?.league_id,
          });
        });
        const sortedData = newdata?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });

        this.setState({
          LeagueOption: sortedData,
          isLeagueLoading: false,
        });
      } else {
        this.setState({
          isLeagueLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isLeagueLoading: false,
      });
    }
  };
  handlePastFixture = async () => {
    if (this.handleValidate()) {
      this.setState({ isLoading: true });
      const { startDate, endDate, selectedProvider, selectedLeague } =
        this.state;
      let payload = {
        date: moment(startDate).format("YYYY-MM-DD"),
        endDate: moment(endDate).format("YYYY-MM-DD"),
        ProviderId: selectedProvider,
        sportId: this.props?.match?.path?.includes("cricket")
          ? 4
          : this.props?.match?.path?.includes("rugbyleague")
          ? 12
          : this.props?.match?.path?.includes("rugbyunion")
          ? 13
          : this.props?.match?.path?.includes("basketball")
          ? 10
          : this.props?.match?.path?.includes("afl")
          ? 15
          : this.props?.match?.path?.includes("australianrules")
          ? 9
          : this.props?.match?.path?.includes("golf")
          ? 16
          : this.props?.match?.path?.includes("tennis")
          ? 7
          : this.props?.match?.path?.includes("baseball")
          ? 11
          : this.props?.match?.path?.includes("icehockey")
          ? 17
          : this.props?.match?.path?.includes("boxing")
          ? 6
          : this.props?.match?.path?.includes("mma")
          ? 5
          : this.props?.match?.path?.includes("soccer")
          ? 8
          : 14,
      };
      if (this.props.match.path.includes("soccer")) {
        payload = {
          ...payload,
          leagueId: Number(selectedLeague),
        };
      }
      try {
        const passApi = this.props?.match?.path?.includes("cricket")
          ? "crickets/sync/fixture/rapid"
          : this.props?.match?.path?.includes("rugbyleague")
          ? "rls/sync/fixture/rapid"
          : this.props?.match?.path?.includes("basketball")
          ? "nba/sync/fixture/rapid"
          : this.props?.match?.path?.includes("afl")
          ? "afl/sync/fixture/rapid"
          : this.props?.match?.path?.includes("australianrules")
          ? "ar/sync/fixture/rapid"
          : this.props?.match?.path?.includes("golf")
          ? "golf/sync/fixture/rapid"
          : this.props?.match?.path?.includes("tennis")
          ? "tennis/sync/fixture/rapid"
          : this.props?.match?.path?.includes("baseball")
          ? "baseball/sync/fixture/rapid"
          : this.props?.match?.path?.includes("icehockey")
          ? "icehockey/sync/fixture/rapid"
          : this.props?.match?.path?.includes("boxing")
          ? "boxing/sync/fixture/rapid"
          : this.props?.match?.path?.includes("mma")
          ? "mma/sync/fixture/rapid"
          : this.props?.match?.path?.includes("soccer")
          ? "soccer/sync/fixture/rapid"
          : "rls/sync/fixture/rapid";
        const { status, data } = await axiosInstance.post(passApi, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            startDate: null,
            endDate: null,
            selectedProvider: 0,
            selectedLeague: null,
          });
          this.setActionMessage(
            true,
            "Success",
            data?.result?.status
              ? "Future Fixture data import Process started"
              : "Right now Future Fixture process already in progress of last selected date so please try again once last process it completed"
          );
        } else {
          this.setState({
            isLoading: false,
            startDate: null,
            endDate: null,
            selectedLeague: null,
          });
        }
      } catch (error) {
        this.setState({
          isLoading: false,
          startDate: null,
          endDate: null,
          selectedLeague: null,
        });
      }
    }
  };
  // setActionMessage = (display = false, type = "", message = "") => {
  //   let setActionMessage = {
  //     display: display,
  //     type: type,
  //     message: message,
  //   };
  //   this.setState({ messageBox: setActionMessage });
  // };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  componentDidMount() {
    this.fetchProviders();
    if (this.props.match.path.includes("soccer")) {
      this.fetchLeague();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match.path !== this.props.match.path) {
      this.setState({
        startDate: null,
        endDate: null,
        errorStartDate: "",
        errorEndDate: "",
        selectedProvider: 0,
        ProviderOption: [],
        selectedLeague: null,
        LeagueOption: [],
        isLeagueLoading: false,
      });
      this.fetchProviders();
      if (this.props.match.path.includes("soccer")) {
        this.fetchLeague();
      }
    }
  }
  render() {
    const {
      startDate,
      endDate,
      messageBox,
      errorStartDate,
      errorEndDate,
      ProviderOption,
      selectedProvider,
      selectedLeague,
      LeagueOption,
      isLeagueLoading,
    } = this.state;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Fixture import</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Fixture import
                </Typography>
              </Grid>
              <Grid
                item
                xs={9}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "flex-start",
                }}
                className="admin-fixture-wrap future-fixture-wrap fixture-import-wrap"
              >
                <Box>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      autoOk
                      // disableToolbar
                      variant="inline"
                      format="dd/MM/yyyy"
                      placeholder="Start Date"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      value={
                        startDate
                          ? typeof startDate === "string"
                            ? parseISO(startDate)
                            : startDate
                          : null
                      }
                      slots={{
                        openPickerIcon: TodayIcon,
                      }}
                      slotProps={{
                        field: {
                          placeholder: "Start Date",
                        },
                      }}
                      onChange={(e) => this.handleStartDate(e)}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      className="date-picker-fixture"
                      style={{ margin: "0px 10px 0px 0px" }}
                    />
                  </LocalizationProvider>
                  {errorStartDate ? (
                    <p
                      className="errorText"
                      style={{ margin: "0px 0 0 0", textAlign: "left" }}
                    >
                      {errorStartDate}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>
                <Box>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      autoOk
                      // disableToolbar
                      variant="inline"
                      format="dd/MM/yyyy"
                      placeholder="End Date"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      value={
                        endDate
                          ? typeof endDate === "string"
                            ? parseISO(endDate)
                            : endDate
                          : null
                      }
                      minDate={
                        startDate
                          ? typeof startDate === "string"
                            ? parseISO(startDate)
                            : startDate
                          : null
                      }
                      // onKeyDown={(e) => {
                      //   e.preventDefault();
                      // }}
                      slots={{
                        openPickerIcon: TodayIcon,
                      }}
                      slotProps={{
                        field: {
                          placeholder: "End Date",
                        },
                      }}
                      onChange={(e) => this.handleEndDate(e)}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      className="date-picker-fixture"
                      style={{ margin: "0px 10px 0px 0px" }}
                    />
                  </LocalizationProvider>
                  {errorEndDate ? (
                    <p
                      className="errorText"
                      style={{ margin: "0px 0 0 0", textAlign: "left" }}
                    >
                      {errorEndDate}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>
                <Select
                  className="React teamsport-select  external-select"
                  classNamePrefix="select"
                  placeholder="Select Provider"
                  value={ProviderOption?.find((item) => {
                    return item?.value == selectedProvider;
                  })}
                  onChange={(e) =>
                    this.setState({
                      selectedProvider: e.value,
                    })
                  }
                  isLoading={this.state.isProviderLoading}
                  options={ProviderOption}
                  style={{ margin: "0px 10px 0px 0px" }}
                />
                {this.props.match.path.includes("soccer") && (
                  <Select
                    className="React teamsport-select  external-select"
                    classNamePrefix="select"
                    placeholder="Select League"
                    value={LeagueOption?.find((item) => {
                      return item?.value == selectedLeague;
                    })}
                    onChange={(e) =>
                      this.setState({
                        selectedLeague: e.value,
                      })
                    }
                    isLoading={isLeagueLoading}
                    options={LeagueOption}
                    style={{ margin: "0px 10px 0px 0px" }}
                  />
                )}
                <div>
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "8px 15px",
                      minWidth: "130px",
                    }}
                    onClick={() => this.handlePastFixture()}
                  >
                    import fixture
                  </Button>
                </div>
              </Grid>
            </Grid>
            {/* <Grid container direction="row" alignItems="center">
            <Grid item xs={12} style={{ textAlign: "end" }}>
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "10px 15px",
                  marginTop: "5px",
                }}
                // onClick={() => this.handlePastFixture()}
              >
                Clear
              </Button>
            </Grid>
          </Grid> */}
          </Grid>
        </Grid>
      </>
    );
  }
}

export default FutureFixtureImport;
