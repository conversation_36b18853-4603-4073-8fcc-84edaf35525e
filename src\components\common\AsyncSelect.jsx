import React from 'react';
import AsyncSelect from 'react-select/async';

// Static options for demonstration


// Simulated API delay


const CustomAsyncSelect = ({
    isMulti = false,
    placeholder = 'Select...',
    loadOptions: customLoadOptions,
    onChange,
    value,
    ...props
}) => {
    // Default loadOptions function that simulates API call


    return (
        <AsyncSelect
            isMulti={isMulti}
            placeholder={placeholder}
            loadOptions={customLoadOptions || []}
            onChange={onChange}
            value={value}
            cacheOptions
            defaultOptions
            {...props}
        />
    );
};

export default CustomAsyncSelect; 