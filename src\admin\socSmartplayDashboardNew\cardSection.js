import React from "react";
import { Box, Typography } from "@mui/material";
import Coins from "../../images/coins.png";
import UpPrice from "../../images/upArrowPrice.png";
import DownPrice from "../../images/downArrowPrice.png";

const CardSection = ({ itemData, itemIndex, fullData }) => {
  return (
    <Box
      className="card-wrap"
      style={{ width: `${100 / fullData.length}%` }}
      key={itemIndex}
    >
      <Typography className="card-title">{itemData?.title}</Typography>
      <Box className="card-title-details">
        <Box className="count-section">
          {itemData?.unit === "coin" && (
            <Box className="coins-wrap">
              <img src={Coins} alt="coins" />
            </Box>
          )}
          <p className="count-value">
            {itemData?.currency && "$"}
            {itemData?.value}
          </p>
        </Box>
        <Box>
          <Typography
            className="value-change"
            style={{
              color:
                itemData?.changeType === "increase" ? "#1C9A6C" : "#D84727",
            }}
          >
            <img
              src={itemData?.changeType === "increase" ? UpPrice : DownPrice}
              alt="icon"
            />{" "}
            {itemData?.change}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default CardSection;
