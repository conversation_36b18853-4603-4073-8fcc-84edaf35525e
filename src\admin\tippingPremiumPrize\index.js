import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select, { components } from "react-select";
import moment from "moment-timezone";
import "./tippingPremiumPrize.scss";
import _, { values } from "lodash";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const keyOption = [
  {
    label: "Tipping Fees",
    value: "TippingFees",
  },
];

const statusOption = [
  {
    label: "Active",
    value: "active",
  },
  {
    label: "Deleted",
    value: "deleted",
  },
];

const externalStatusOption = [
  {
    label: "All",
    value: null,
  },
  {
    label: "Active",
    value: "active",
  },
  {
    label: "Deleted",
    value: "deleted",
  },
];

class PremiumTippingComps extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      tippingPrizeValues: {
        selectedName: "",
        selectedPrize: "",
        selectedKey: "TippingFees",
        selectedStatus: "active",
      },
      TippingPrizeList: [],
      TippingPrizeCount: 0,
      errorName: "",
      errorPrize: "",
      isSearch: "",
      selectedTippingPrizeID: "",
      sports: [],
      selectedExternalStatus: null,
      OrgAll: [],
      selectedOrg: null,
      OrgApiCount: 0,
      isOrgSearch: "",
      countOrg: 0,
      searchOrg: [],
      searchOrgCount: 0,
      SearchOrgpage: 0,
      pageOrg: 0,
      errorCreate: "",
    };
  }

  componentDidMount() {
    this.fetchTippingPrize(0, "", null);
    this.fetchSportData();
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset, isSearch, selectedExternalStatus, selectedOrg } =
      this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchTippingPrize(offset, isSearch, selectedExternalStatus);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchTippingPrize(0, "", null);
      this.setState({
        offset: 0,
        currentPage: 1,
        isSearch: "",
        selectedExternalStatus: null,
        selectedOrg: null,
      });
    }
  }

  fetchSportData = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/sports/sport/?sportTypeId=${2}`
      );
      if (status === 200) {
        const sportsdata = data?.result.map((s) => ({
          ...s,
          label: s?.sportName,
          value: s?.id,
        }));

        const sdata = _.orderBy(sportsdata, ["label"], ["asc"])?.filter(
          (item) => [9, 4, 12]?.includes(item?.id)
        );
        let alldatas = sdata?.unshift({
          label: "All Sports",
          value: 0,
        });

        this.setState({
          sports: sdata,
          selectedExternalStatus: sdata?.[0]?.id,
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  fetchOrgData = async (page, sID, OrgAll) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/tournament?SportId=${sID}&offset=${page}&limit=${20}`
      );
      if (status === 200) {
        const newdata = data?.result?.rows.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));

        const filterData = _.unionBy(OrgAll, newdata)?.sort((a, b) =>
          a?.label.localeCompare(b?.label)
        );
        const unique = _.uniqBy(filterData, "value");
        let alldatas = unique?.unshift({
          label: "All Tournaments",
          value: 0,
        });

        this.setState((prevState) => ({
          OrgApiCount: prevState.OrgApiCount + 1,
          countOrg: Math.ceil(data?.result?.count / 20),
          OrgAll: unique,
          selectedOrg: unique?.[0]?.value,
        }));

        this.fetchPublicComp(sID, unique?.[0]?.value, 1, []);
      }
    } catch (err) {
      console.error(err);
    }
  };

  handleOrgInputChange = async (page, value, sid) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/tournament?SportId=${sid}&limit=${20}&offset=${page}&search=${value}`
      );
      if (status === 200) {
        const response = data?.result?.rows;
        const newdata = response.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));

        const mergeData = _.unionBy(this.state.searchOrg, newdata);
        const filterData = _.uniqBy(mergeData, "value");

        this.setState({
          searchOrg: filterData,
          isOrgSearch: value,
          searchOrgCount: Math.ceil(data?.result?.count / 20),
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  handleInputChangeOrg = (newValue, actionMeta) => {
    if (actionMeta.action === "input-change") {
      this.handleOrgInputChange(0, newValue, this.state.selectedExternalStatus);
    }
  };

  handleOnScrollBottomOrg = () => {
    if (
      this.state.isOrgSearch !== "" &&
      this.state.searchOrgCount !== Math.ceil(this.state.SearchOrgpage / 20)
    ) {
      this.handleOrgInputChange(
        this.state.SearchOrgpage + 20,
        this.state.isOrgSearch,
        this.state.selectedExternalStatus
      );

      this.setState((prevState) => ({
        SearchOrgpage: prevState.SearchOrgpage + 20,
      }));
    } else {
      if (
        this.state.countOrg !== 0 &&
        this.state.countOrg !== Math.ceil(this.state.pageOrg / 20 + 1)
      ) {
        this.fetchOrgData(
          this.state.pageOrg + 20,
          this.state.selectedExternalStatus,
          this.state.OrgAll
        );
        this.setState((prevState) => ({ pageOrg: prevState.pageOrg + 20 }));
      }
    }
  };

  async fetchTippingPrize(page, search, statusType) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/global/settings?limit=${rowPerPage}&offset=${page}&search=${search}&status=${
          statusType ? statusType : ""
        }`
      );
      if (status === 200) {
        this.setState({
          TippingPrizeList: data?.result,
          isLoading: false,
          TippingPrizeCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { tippingPrizeValues } = this.state;
    let flag = true;
    if (
      tippingPrizeValues?.selectedName?.trim() === "" ||
      tippingPrizeValues?.selectedName === null
    ) {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }

    if (
      tippingPrizeValues?.selectedPrize?.trim() === "" ||
      tippingPrizeValues?.selectedPrize === null
    ) {
      flag = false;
      this.setState({
        errorPrize: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPrize: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      const {
        tippingPrizeValues,
        offset,
        isSearch,
        selectedExternalStatus,
        selectedOrg,
      } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        name: tippingPrizeValues?.selectedName,
        value: tippingPrizeValues?.selectedPrize?.toString(),
        key: tippingPrizeValues?.selectedKey,
        status: tippingPrizeValues?.selectedStatus,
      };

      try {
        const { status, data } = await axiosInstance.post(
          `/global/settings`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
          });
          this.fetchTippingPrize(offset, isSearch, selectedExternalStatus);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
        });

        // this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const {
      tippingPrizeValues,
      selectedTippingPrizeID,
      offset,
      isSearch,
      selectedExternalStatus,
      selectedOrg,
    } = this.state;

    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        name: tippingPrizeValues?.selectedName,
        value: tippingPrizeValues?.selectedPrize?.toString(),
        status: tippingPrizeValues?.selectedStatus,
      };

      try {
        const { status, data } = await axiosInstance.put(
          `/global/settings/${selectedTippingPrizeID}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
          });
          this.fetchTippingPrize(offset, isSearch, selectedExternalStatus);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorPrize: "",
      errorCreate: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        tippingPrizeValues: {
          selectedPrize: item?.value,
          selectedName: item?.name,
          selectedKey: item?.key,
          selectedStatus: item?.status,
        },
        isEditMode: true,
        selectedTippingPrizeID: item?.id,
      });
    } else {
      this.setState({
        tippingPrizeValues: {
          selectedName: "",
          selectedPrize: "",
          selectedKey: "TippingFees",
          selectedStatus: "active",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const {
      itemToDelete,
      offset,
      isSearch,
      selectedExternalStatus,
      selectedOrg,
    } = this.state;

    try {
      const { status, data } = await axiosInstance.delete(
        `/global/settings/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchTippingPrize(offset, isSearch, selectedExternalStatus);
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, TippingPrizeList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= rowPerPage) {
        this.setState({
          offset: offset - rowPerPage,
          currentPage: currentPage - 1,
        });
      }
    } else {
      this.setState({
        offset: offset + rowPerPage,
        currentPage: currentPage + 1,
      });
    }
  };

  handleClearClick = () => {
    const { selectedExternalStatus, selectedOrg } = this.state;
    this.fetchTippingPrize(0, "", selectedExternalStatus);
    this.setState({
      offset: 0,
      currentPage: 1,
      isSearch: "",
    });
  };

  DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        {/* <SelectIndicator /> */}
      </components.DropdownIndicator>
    );
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      tippingPrizeValues,
      TippingPrizeList,
      TippingPrizeCount,
      errorName,
      errorPrize,
      isSearch,
      sports,
      selectedExternalStatus,
      OrgAll,
      selectedOrg,
      OrgApiCount,
      isOrgSearch,
      countOrg,
      searchOrg,
      searchOrgCount,
      SearchOrgpage,
      pageOrg,
      errorCreate,
    } = this.state;
    const pageNumbers = [];

    if (TippingPrizeCount > 0) {
      for (let i = 1; i <= Math.ceil(TippingPrizeCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Premium Comp fees</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Premium Comp fees
                </Typography>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                <Select
                  className="React teamsport-select external-select sort-select"
                  value={
                    selectedExternalStatus &&
                    externalStatusOption?.find(
                      (item) => item?.value === selectedExternalStatus
                    )
                  }
                  onChange={(e) => {
                    this.setState({
                      selectedExternalStatus: e?.value === 0 ? "" : e?.value,
                      currentPage: 1,
                      offset: 0,
                    });

                    this.fetchTippingPrize(0, isSearch, e?.value);
                  }}
                  options={externalStatusOption}
                  classNamePrefix="select"
                  placeholder="Status"
                />

                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() =>
                    this.fetchTippingPrize(
                      offset,
                      isSearch,
                      selectedExternalStatus
                    )
                  }
                >
                  Search
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TippingPrizeList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TippingPrizeList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell style={{ cursor: "pointer" }}>ID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Key</TableCell>
                      <TableCell>fees</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {TippingPrizeList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.name}</TableCell>
                          <TableCell>{item?.key}</TableCell>
                          <TableCell>{item?.value}</TableCell>
                          <TableCell style={{ textTransform: "capitalize" }}>
                            {item?.status}
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              TippingPrizeCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Tipping Prize"
                    : "Edit Tipping Prize"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Name</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Name"
                          value={tippingPrizeValues?.selectedName}
                          onChange={(e) =>
                            this.setState({
                              tippingPrizeValues: {
                                ...tippingPrizeValues,
                                selectedName: e?.target?.value,
                              },
                              errorName: e?.target?.value ? "" : errorName,
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Key</label>
                        <Select
                          className="React teamsport-select event-Tournament-select premium-key-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Key"
                          value={
                            tippingPrizeValues?.selectedKey &&
                            keyOption?.find((item) => {
                              return (
                                item?.value == tippingPrizeValues?.selectedKey
                              );
                            })
                          }
                          isDisabled={isEditMode}
                          options={keyOption}
                          onChange={(e) => {
                            this.setState({
                              tippingPrizeValues: {
                                ...tippingPrizeValues,
                                selectedKey: e?.value,
                              },
                            });
                          }}
                        />
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">fees</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Prize"
                          value={tippingPrizeValues?.selectedPrize}
                          onChange={(e) =>
                            this.setState({
                              tippingPrizeValues: {
                                ...tippingPrizeValues,
                                selectedPrize: e?.target?.value,
                              },
                              errorPrize: e?.target?.value ? "" : errorPrize,
                            })
                          }
                        />
                        {errorPrize ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorPrize}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Status</label>
                        <Select
                          className="React teamsport-select event-Tournament-select premium-key-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Status"
                          value={
                            tippingPrizeValues?.selectedStatus &&
                            statusOption?.find((item) => {
                              return (
                                item?.value ==
                                tippingPrizeValues?.selectedStatus
                              );
                            })
                          }
                          options={statusOption}
                          onChange={(e) => {
                            this.setState({
                              tippingPrizeValues: {
                                ...tippingPrizeValues,
                                selectedStatus: e?.value,
                              },
                            });
                          }}
                        />
                      </Grid>
                      <span style={{ fontSize: "12px", color: "red" }}>
                        {errorCreate ? errorCreate : ""}
                      </span>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default PremiumTippingComps;
