@import "../../assets/scss/variables";

.news-modal {
  .rdw-editor-wrapper {
    width: 97%;
  }

  .rdw-editor-main {
    background-color: $Color-White;
    min-height: 200px;
    max-height: 600px;
  }

  .DraftEditor-editorContainer {
    padding: 10px;
  }

  .news-multi-select {
    width: 97.5%;
  }
}

.txt-field-class {
  background: $Color-White;
  width: 282px;
}

.feature-img {
  img {
    max-width: 75px;
    max-height: 50px;
  }

  .getty-img-container {
    pointer-events: none;
  }
}

.modal-disable {
  .modal-loader {
    position: absolute;
    z-index: 3;
    height: 100%;
    width: 100%;
    left: 0;
    right: 0px;

    .spinner {
      position: absolute;
      right: 50%;
      top: 25%;
    }
  }

  position: relative;

  &::before {
    content: "";
    position: absolute;
    background-color: #0003;
    opacity: 0.7;
    width: 100%;
    height: 100%;
    z-index: 2;
    left: 0px;
    top: 0px;
  }
}

.text-capitalize {
  text-transform: capitalize;
}

.tag-create {
  .create-tag-btn {
    background-color: $purplue;
    color: $Color-White;

    &:hover {
      background-color: $purplue;
      color: $Color-White;
    }
  }
}

.suneditor-wrap {
  .sun-editor {
    .sun-editor-editable {
      min-height: 400px !important;
    }
  }
}

.embed-preview {
  iframe {
    position: absolute !important;
  }
}