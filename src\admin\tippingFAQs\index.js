import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select from "react-select";
import moment from "moment-timezone";
import "./tippingFAQs.scss";
import _ from "lodash";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class TippingFAQs extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      tippingFAQValues: {
        selectedQuestion: "",
        selectedAnswer: "",
      },
      TippingFAQList: [],
      TippingFAQCount: 0,
      errorSelectQuestion: "",
      errorSelectAnswer: "",
      isSearch: "",
      selectedTippingFAQID: "",
    };
  }

  componentDidMount() {
    this.fetchTippingFAQs(0, "");
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset, isSearch } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchTippingFAQs(offset, isSearch);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchTippingFAQs(offset, "");
      this.setState({
        offset: 0,
        currentPage: 1,
        isSearch: "",
      });
    }
  }

  async fetchTippingFAQs(page, search) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/tippingFaqs/faqs?limit=${rowPerPage}&offset=${page}&search=${search}`
      );
      if (status === 200) {
        this.setState({
          TippingFAQList: data?.data,
          isLoading: false,
          TippingFAQCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { tippingFAQValues } = this.state;
    let flag = true;
    if (tippingFAQValues?.selectedQuestion?.trim() === "") {
      flag = false;
      this.setState({
        errorSelectQuestion: "This field is mandatory",
      });
    } else {
      this.setState({
        errorSelectQuestion: "",
      });
    }

    if (tippingFAQValues?.selectedAnswer?.trim() === "") {
      flag = false;
      this.setState({
        errorSelectAnswer: "This field is mandatory",
      });
    } else {
      this.setState({
        errorSelectAnswer: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      const { tippingFAQValues, offset, isSearch } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        questions: tippingFAQValues?.selectedQuestion?.trim(),
        answer: tippingFAQValues?.selectedAnswer?.trim(),
      };

      try {
        const { status, data } = await axiosInstance.post(
          `/tippingFaqs/faqs`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchTippingFAQs(offset, isSearch);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const { tippingFAQValues, selectedTippingFAQID, offset, isSearch } =
      this.state;

    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        questions: tippingFAQValues?.selectedQuestion?.trim(),
        answer: tippingFAQValues?.selectedAnswer?.trim(),
      };

      try {
        const { status, data } = await axiosInstance.put(
          `/tippingFaqs/faqs/${selectedTippingFAQID}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchTippingFAQs(offset, isSearch);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorSelectQuestion: "",
      errorSelectAnswer: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        tippingFAQValues: {
          selectedAnswer: item?.answer,
          selectedQuestion: item?.questions,
        },
        isEditMode: true,
        selectedTippingFAQID: item?.id,
      });
    } else {
      this.setState({
        tippingFAQValues: {
          selectedQuestion: "",
          selectedAnswer: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { itemToDelete, offset, isSearch } = this.state;

    try {
      const { status, data } = await axiosInstance.delete(
        `/tippingFaqs/faqs/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchTippingFAQs(offset, isSearch);
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, TippingFAQList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handleClearClick = () => {
    this.setState({ isSearch: "" });
    this.fetchTippingFAQs(this.state?.offset, "");
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      tippingFAQValues,
      TippingFAQList,
      TippingFAQCount,
      errorSelectQuestion,
      errorSelectAnswer,
      isSearch,
    } = this.state;
    const pageNumbers = [];

    if (TippingFAQCount > 0) {
      for (let i = 1; i <= Math.ceil(TippingFAQCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Tipping FAQs</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Typography variant="h1" align="left">
                  Tipping FAQs
                </Typography>
              </Grid>

              <Grid
                item
                xs={7}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => this.fetchTippingFAQs(offset, isSearch)}
                >
                  Search
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TippingFAQList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TippingFAQList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell style={{ cursor: "pointer" }}>ID</TableCell>
                      <TableCell>Questions</TableCell>
                      <TableCell>Answer</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {TippingFAQList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell style={{ width: "40%" }}>
                            {item?.questions}
                          </TableCell>
                          <TableCell style={{ width: "40%" }}>
                            {" "}
                            {item?.answer}
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              TippingFAQCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Tipping FAQs"
                    : "Edit Tipping FAQs"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">FAQs Questions</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="textarea"
                          multiline
                          maxRows={3}
                          color="primary"
                          size="small"
                          placeholder="FAQs Question"
                          value={tippingFAQValues?.selectedQuestion}
                          onChange={(e) =>
                            this.setState({
                              tippingFAQValues: {
                                ...tippingFAQValues,
                                selectedQuestion: e?.target?.value,
                              },
                              errorSelectQuestion: e?.target?.value
                                ? ""
                                : errorSelectQuestion,
                            })
                          }
                        />
                        {errorSelectQuestion ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorSelectQuestion}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">FAQs Answer</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="textarea"
                          multiline
                          maxRows={3}
                          color="primary"
                          size="small"
                          placeholder="FAQs Answer"
                          value={tippingFAQValues?.selectedAnswer}
                          onChange={(e) =>
                            this.setState({
                              tippingFAQValues: {
                                ...tippingFAQValues,
                                selectedAnswer: e?.target?.value,
                              },
                              errorSelectAnswer: e?.target?.value
                                ? ""
                                : errorSelectAnswer,
                            })
                          }
                        />
                        {errorSelectAnswer ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorSelectAnswer}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default TippingFAQs;
