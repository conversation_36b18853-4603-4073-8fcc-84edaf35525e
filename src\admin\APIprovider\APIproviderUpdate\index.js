import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Button,
  Box,
  Breadcrumbs,
  Typography,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import { Link } from "react-router-dom";
import { MdKeyboardBackspace } from "react-icons/md";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import RateReviewIcon from "@mui/icons-material/RateReview";
// import ButtonComponent from "../../../library/common/components/Button";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import CreateApiProviderUpdate from "./CreateAPIproviderUpdate";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";

class ApiProviderUpdate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentProviderData: {},
      apiproviderupdate: [],
      allApiProvider: [],
      dbtable: [],
      allSportsType: [],
      allApiKeyIdentifire: [],
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
    };
  }

  componentDidMount() {
    this.fetchAllApiProviderUpdate();
    this.fetchAllDBTable();
    this.fetchAllSportType();
    this.fetchAllApiKeyIdentifire();
  }

  async fetchAllApiProviderUpdate() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.apiProviderUpdate + `/providerId/${this.props.match.params.id}`
    );
    if (status === 200) {
      this.setState({ apiproviderupdate: data.result, isLoading: false });
      this.fetchAllApiProvider();
    }
  }

  async fetchAllDBTable() {
    const { status, data } = await axiosInstance.get(URLS.dbtable);
    if (status === 200) {
      this.setState({ dbtable: data.tables });
    }
  }

  async fetchAllApiProvider() {
    const { id } = this.props.match.params;
    await axiosInstance.get(`${URLS.apiProvider}/${id}`).then((res) => {
      this.setState({ currentProviderData: res?.data?.result[0] });
    });
    const { status, data } = await axiosInstance.get(URLS.apiProvider);
    if (status === 200) {
      this.setState({ allApiProvider: data.result });
    }
  }

  async fetchAllSportType() {
    const { status, data } = await axiosInstance.get(URLS.sportType);
    if (status === 200) {
      this.setState({ allSportsType: data.result });
    }
  }

  async fetchAllApiKeyIdentifire() {
    const { status, data } = await axiosInstance.get(URLS.apiKeyIdentifire);
    if (status === 200) {
      this.setState({ allApiKeyIdentifire: data.result });
    }
  }

  getSportType = (id) => {
    let { allSportsType } = this.state;
    let sportType = allSportsType
      .filter((obj) => obj.id == id)
      .map((object) => object.sportType);
    return sportType;
  };

  getIdentifire = (id) => {
    let { allApiKeyIdentifire } = this.state;
    let key = allApiKeyIdentifire
      .filter((obj) => obj.id == id)
      .map((object) => object.key);
    return key;
  };

  getApiProvider = (id) => {
    let { allApiProvider } = this.state;
    let apiProvider = allApiProvider
      .filter((obj) => obj.id === id)
      .map((object) => object.providerName);
    return apiProvider;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.apiProviderUpdate}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllApiProviderUpdate();
        });
        this.setActionMessage(
          true,
          "Success",
          "Api Provider Update Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  afterChangeRefresh = () => {
    this.fetchAllApiProviderUpdate();
  };

  backToNavigatePage = () => {
    this.props.navigate(`/apiprovider`);
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, apiproviderupdate } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < apiproviderupdate.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  navigateToUpdateApiProviderFieldMapping = (id) => () => {
    let providerid = this.props.match.params?.id;
    this.props.navigate(`/apiprovider/apifieldmapping/${providerid}/${id}`);
  };

  render() {
    var {
      apiproviderupdate,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      currentProviderData,
      allSportsType,
      allApiKeyIdentifire,
    } = this.state;
    const pageNumbers = [];
    let currentPageRow = apiproviderupdate;

    if (apiproviderupdate?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = apiproviderupdate.slice(
        indexOfFirstTodo,
        indexOfLastTodo
      );

      for (
        let i = 1;
        i <= Math.ceil(apiproviderupdate.length / rowPerPage);
        i++
      ) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit"></Link>
                <Link underline="hover" color="inherit" to="/apiprovider">
                  Api Provider
                </Link>
                <Typography className="active_p">
                  Api Provider Update
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row">
              <Grid item xs={10}>
                <Button
                  className="admin-btn-back"
                  onClick={this.backToNavigatePage}
                >
                  <MdKeyboardBackspace />
                </Button>
                {/* <h3 className="text-left admin-page-heading">
                  Api Provider Update
                </h3> */}
                <Typography variant="h1" align="left">
                  Api Provider Update
                </Typography>
              </Grid>
              <Grid item xs={2} style={{ textAlign: "end" }}>
                {/* <ButtonComponent
                  className="addButton admin-btn-green"
                  onClick={this.inputModal(null, "create")}
                  color="primary"
                  value="Add New"
                /> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Grid item xs={12} lg={12} md={12}>
                <p className="text-left">
                  <strong>Provider: </strong>{" "}
                  {currentProviderData?.providerName}
                  <br />
                </p>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && apiproviderupdate.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && apiproviderupdate.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Api Url</TableCell>
                        <TableCell>Sport Type</TableCell>
                        <TableCell>Parameter Type</TableCell>
                        <TableCell>Parameter Name Value</TableCell>
                        <TableCell>Parameter Value</TableCell>
                        <TableCell>Parameter Key</TableCell>
                        <TableCell>Parameter Formate</TableCell>
                        <TableCell>Level</TableCell>
                        <TableCell>Parent Table Id</TableCell>
                        {/* <TableCell>Api Provider</TableCell> */}
                        <TableCell>Table name to update</TableCell>
                        <TableCell>Second to update</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Provider Field Mapping</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((api, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{api.id}</TableCell>

                          {/* <TableCell>
                              {this.getApiProvider(api.apiProviderId)}
                            </TableCell> */}
                          <TableCell>{api.apiURL}</TableCell>
                          <TableCell>
                            {this.getSportType(api.sportTypeId)}
                          </TableCell>
                          <TableCell>{api.parameterType}</TableCell>
                          <TableCell>{api.parameterNameValue}</TableCell>
                          <TableCell>{api.parameterValue}</TableCell>
                          <TableCell>
                            {this.getIdentifire(api.parameterKey)}
                          </TableCell>
                          <TableCell>{api.parameterFormate}</TableCell>
                          <TableCell>{api.level}</TableCell>
                          <TableCell>{api.parentTableId}</TableCell>
                          <TableCell>{api.tableNameToUpdate}</TableCell>
                          <TableCell>{api.secondToUpdate}</TableCell>
                          <TableCell>{api.status}</TableCell>
                          <TableCell>
                            <RateReviewIcon
                              onClick={this.navigateToUpdateApiProviderFieldMapping(
                                api?.id
                              )}
                              color="primary"
                              className="cursor iconBtn admin-btn-green"
                            />
                          </TableCell>
                          <TableCell>
                            {/* <EditIcon
                              onClick={this.inputModal(api.id, "edit")}
                              color="primary"
                              className="mr10 cursor iconBtn admin-btn-green"
                            /> */}
                            <Button
                              onClick={this.inputModal(api.id, "edit")}
                              className="table-btn"
                            >
                              Edit
                            </Button>
                            {/* <DeleteOutlineIcon
                              onClick={this.setItemToDelete(api.id)}
                              color="secondary"
                              className="cursor iconBtn admin-btn-orange"
                            /> */}
                            <Button
                              onClick={this.setItemToDelete(api.id)}
                              className="table-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          {" "}
                          <div className="tablePagination">
                            {/* <button
                              className={
                                apiproviderupdate.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                apiproviderupdate.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                apiproviderupdate.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                apiproviderupdate.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                apiproviderupdate.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div className={"paper"} style={{ position: "relative" }}>
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Api Provider Update"
                    : "Edit Api Provider Update"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateApiProviderUpdate
                  type={this.state.type}
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  apiProviderId={this.props.match.params.id}
                  isEditMode={isEditMode}
                  fetchAllApiProviderUpdate={this.afterChangeRefresh}
                  dbtable={this.state.dbtable}
                  allSportsType={allSportsType}
                  allApiKeyIdentifire={allApiKeyIdentifire}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default ApiProviderUpdate;
