import React, { useState } from "react";
import {
  Box,
  Tabs,
  Tab,
  Grid,
  Checkbox,
  Breadcrumbs,
  Typography,
  Modal,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  Button,
} from "@mui/material";
import {
  Link,
  // useNavigate
} from "react-router-dom";
import moment from "moment";
import axiosInstance from "../../helpers/Axios";
import ActionMessage from "../../library/common/components/ActionMessage";
import ButtonComponent from "../../library/common/components/Button";
import CancelIcon from "@mui/icons-material/Cancel";
import { Loader } from "../../library/common/components";
import Horse from "../../images/dark_horse.svg";
import Greys from "../../images/dar-greyhound.svg";
import Harnes from "../../images/dark_harness.svg";
// import TabHorses from "../../images/sport_icons/tab_horse.svg";
// import TabGreyhounds from "../../images/sport_icons/tab_greyhounds.svg";
// import TabHarness from "../../images/sport_icons/tab_harness.svg";
import { ReactComponent as Unchecked } from "../../images/smartb-uncheck.svg";
import { ReactComponent as Checked } from "../../images/smartb-check.svg";
// import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import "./Dashboard.scss";
import DashboardCollapase from "./DashboardCollapase";
import DateFnsUtils from "@date-io/date-fns";
import Select from "react-select";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";

const sportIdOptions = [
  { label: "Horse Racing", value: "1" },
  { label: "Harness Racing", value: "2" },
  { label: "Greyhound Racing", value: "3" },
];
const day = [
  {
    id: 0,
    name: "Yesterday",
    date: moment().utc().subtract(1, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().subtract(1, "days").format("dddd"),
  },
  {
    id: 1,
    name: "Today",
    date: moment().utc().format("YYYY-MM-DD"),
    dayName: moment().utc().format("dddd"),
  },
  {
    id: 2,
    name: "Tomorrow",
    date: moment().utc().add(1, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(1, "days").format("dddd"),
  },
  {
    id: 3,
    name: "Wednesday",
    date: moment().utc().add(2, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(2, "days").format("dddd"),
  },
  {
    id: 4,
    name: "Thursday",
    date: moment().utc().add(3, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(3, "days").format("dddd"),
  },
  {
    id: 5,
    name: "Friday",
    date: moment().utc().add(4, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(4, "days").format("dddd"),
  },
  {
    id: 6,
    name: "Saturday",
    date: moment().utc().add(5, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(5, "days").format("dddd"),
  },
  {
    id: 7,
    name: "Futures",
    // dayName: "Futures",
    date: moment().utc().add(1, "days").format("YYYY-MM-DD"),
  },
  {
    id: 8,
    name: "Archive",
    // dayName: "Archive",
    date: moment().utc().subtract(1, "days").format("YYYY-MM-DD"),
  },
];

const Dashboard = () => {
  const [filterRaceType, setfilterRaceType] = useState([
    {
      id: 1,
      Racevalue: "Horse Racing",
      isRaceChecked: true,
      icon: (
        <span>
          <img src={Horse} alt="Horse" />
        </span>
      ),
    },
    {
      id: 3,
      Racevalue: "Greyhound Racing",
      isRaceChecked: true,
      icon: (
        <span>
          <img src={Greys} alt="Greys" />
        </span>
      ),
    },
    {
      id: 2,
      Racevalue: "Harness Racing",
      isRaceChecked: true,
      icon: (
        <span>
          <img src={Harnes} alt="Harnes" />
        </span>
      ),
    },
  ]);
  const [filterCountry, setfilterCountry] = useState([
    {
      id: 1,
      name: "Aus/NZ",
      value: "Aus/NZ",
      isChecked: true,
    },
    {
      id: 2,
      name: "Int'l",
      value: "Intl",
      isChecked: true,
    },
  ]);
  // const navigate = useNavigate();
  const [value, setValue] = useState(1);
  const [apimessage, setApiMessage] = useState(1);
  const [selectedRaceType, setselectedRaceType] = useState([1, 2, 3]);
  const [selectedCountryType, setselectedCountryType] = useState([
    "Aus/NZ",
    "Intl",
  ]);
  const [providersDetails, setProvidersDetails] = useState([]);
  const [selectedDate, setselectedDate] = useState(
    moment().format("YYYY-MM-DD")
  );
  const [isRefetch, setIsRefetch] = useState(false);
  const [isLoading, setisLoading] = useState(false);
  const [bookmakerModalOpen, setBookmakerModalOpen] = useState(false);
  const [seletedProvider, setseletedProvider] = useState("");
  const [betProviderModal, setbetProviderModal] = useState(false);
  const [sportId, setsportId] = useState("");

  const handleDateChange = (date) => {
    setselectedDate(moment(date).format("YYYY-MM-DD"));
  };
  // Change Day Tab
  const handleChange = (event, value) => {
    setValue(value);
    let SelectDate = day.filter((item) => {
      return item.id === value;
    });
    setselectedDate(
      SelectDate?.map((item) => {
        return item?.date;
      })?.[0]
    );
  };

  // Change Racing Filter

  const ChangeRaceFilter = (event, item) => {
    const flag = event.target.checked;

    if (flag) {
      const newItem = [...selectedRaceType, item];
      setselectedRaceType(newItem);
    } else {
      if (selectedRaceType?.length >= 2) {
        const removeItem = selectedRaceType?.filter((eItem) => eItem !== item);
        setselectedRaceType(removeItem);
      }
    }
  };

  const ChangeCountryFilter = (event, item) => {
    const flag = event.target.checked;
    if (flag) {
      const newItem = [...selectedCountryType, item];
      setselectedCountryType(newItem);
    } else {
      if (selectedCountryType.length >= 2) {
        const removeItem = selectedCountryType.filter(
          (eItem) => eItem !== item
        );
        setselectedCountryType(removeItem);
      }
    }
  };
  const setActionMessage = (display = false, type = "", message = "") => {
    let setActionMessage = {
      display: display,
      type: type,
      message: message,
    };
    setApiMessage(setActionMessage);
  };
  const handlebet365 = () => {
    setbetProviderModal(true);
  };
  const togglebet365 = () => {
    setbetProviderModal(false);
  };

  const reFetch = async () => {
    setisLoading(true);
    let params = {
      providerId: seletedProvider,
    };

    try {
      const { status } = await axiosInstance.post(`sync/fixture`, params);
      if (status === 200) {
        setActionMessage(true, "Success", "sync process start");
        setisLoading(false);
        setIsRefetch(true);
      }
    } catch (err) { }
  };
  const handleSave = async () => {
    setisLoading(true);
    let params = {
      providerId: "6",
      sportId: sportId,
    };

    try {
      const { status } = await axiosInstance.post(`sync/fixture`, params);
      if (status === 200) {
        setActionMessage(true, "Success", "sync process start");
        setisLoading(false);
        setIsRefetch(true);
        setbetProviderModal(false);
      }
    } catch (err) { }
  };
  const bookMakerModel = () => {
    setBookmakerModalOpen(!bookmakerModalOpen);
    fetchProviders();
  };
  const fetchProviders = async () => {
    setisLoading(true);

    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/apiprovidersports?SportId=1,2,3`
      );
      if (status === 200) {
        setProvidersDetails(data?.result);
        setisLoading(false);
      }
    } catch (err) {
      setisLoading(false);
    }
  };

  const togglebookmakerModal = () => {
    setBookmakerModalOpen(!bookmakerModalOpen);
    setseletedProvider("");
  };
  const handleChangeProvider = (e) => {
    if (e.target.checked) {
      setseletedProvider(e.target.value);
    } else {
      setseletedProvider("");
    }
  };

  const Today = moment().utc().format("YYYY-MM-DD");
  const Tommorow = moment().utc().add(1, "days").format("YYYY-MM-DD");
  const yesterDay = moment().utc().subtract(1, "days").format("YYYY-MM-DD");
  return (
    <>
      <Grid container className="page-content adminLogin">
        <Grid item xs={12}>
          <Box className="pageWrapper">
            {apimessage.display && (
              <ActionMessage
                message={apimessage.message}
                type={apimessage.type}
                styleClass={apimessage.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">racing</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="space-around">
              <Grid item xs={10}>
                <h1 className="text-left">RACING </h1>
              </Grid>
              <Grid item xs={2}>
                <ButtonComponent
                  className="addButton admin-btn-green btn"
                  onClick={() => {
                    bookMakerModel();
                  }}
                  color="primary"
                  value="Refetch"
                />
              </Grid>
            </Grid>

            <Box className="sport-tab">
              <Tabs
                value={value}
                indicatorColor="primary"
                textColor="primary"
                className="racing-tab-detail"
                disableRipple
                disableFocusRipple
              >
                {day?.map((item, index) => {
                  return (
                    <>
                      <Tab
                        disableRipple
                        disableFocusRipple
                        //   label={item?.dayName}
                        label={
                          item?.date == Today
                            ? "Today"
                            : item?.name == "Archive"
                              ? "Archive"
                              : item?.name == "Futures"
                                ? "Futures"
                                : item?.date == Tommorow
                                  ? "Tommorow"
                                  : item?.date == yesterDay
                                    ? "Yesterday"
                                    : item?.dayName
                        }
                        value={item?.id}
                        className={item?.id == value ? "active" : ""}
                        onChange={(event, newValue) =>
                          handleChange(event, item?.id)
                        }
                      />
                    </>
                  );
                })}
              </Tabs>
              <Box className="Filteritemlist-wrap">
                <ul className="Filteritemlist-racing">
                  {filterRaceType?.length > 0 &&
                    filterRaceType?.map((filter, i) => (
                      <li>
                        <label>
                          <Checkbox
                            disableRipple
                            disableFocusRipple
                            disableTouchRipple
                            className="filter-racing"
                            icon={<Unchecked />}
                            checkedIcon={<Checked />}
                            name="filter"
                            value={filter?.id}
                            onChange={(event) => {
                              ChangeRaceFilter(event, filter?.id);
                            }}
                            checked={selectedRaceType?.includes(filter?.id)}
                          />
                          {filter?.icon}
                        </label>
                      </li>
                    ))}
                </ul>

                <ul className="Filteritemlist-racing">
                  {filterCountry?.length > 0 &&
                    filterCountry?.map((countryItem, i) => (
                      <li>
                        <label>
                          <Checkbox
                            disableRipple
                            disableFocusRipple
                            disableTouchRipple
                            className="filter-country"
                            icon={<Unchecked />}
                            checkedIcon={<Checked />}
                            name="filtercountry"
                            value={countryItem?.value}
                            onChange={(event) => {
                              ChangeCountryFilter(event, countryItem?.value);
                            }}
                            checked={selectedCountryType.includes(
                              countryItem?.value
                            )}
                          />

                          {countryItem?.name}
                        </label>
                      </li>
                    ))}
                </ul>
                {value == 8 || value == 7 ? (
                  <Box className="Filteritemlist-datepicker">
                    <LocalizationProvider dateAdapter={AdapterDateFns}>

                      <Grid container style={{ justifyContent: "end" }}>
                        <DesktopDatePicker
                          autoOk
                          disableToolbar
                          variant="inline"
                          format="dd/MM/yyyy"
                          placeholder="DD/MM//YYYY"
                          margin="normal"
                          id="date-picker-inline"
                          inputVariant="outlined"
                          value={selectedDate ? parseISO(selectedDate) : null}
                          onChange={(e) => handleDateChange(e)}
                          KeyboardButtonProps={{
                            "aria-label": "change date",
                          }}
                          disableFuture={value == 8}
                          disablePast={value == 7}
                          // style={{ marginRight: 5 }}
                          className="details-search-picker"

                        />
                      </Grid>
                    </LocalizationProvider>
                  </Box>
                ) : (
                  ""
                )}
              </Box>
            </Box>
            <DashboardCollapase
              selectedDate={selectedDate}
              selectedRaceType={selectedRaceType}
              selectedCountryType={selectedCountryType}
              isRefetch={isRefetch}
            />
            <Box className="fixture-info">
              <ul>
                <li>
                  <span className="sqare fixture"></span> Found Fixture
                </li>
                <li>
                  <span className="sqare notfixture"> </span> Can't Find Fixture
                </li>
                <li>
                  <span className="sqare ignore"> </span> Marked by User as
                  Ignore
                </li>
              </ul>
            </Box>
          </Box>
        </Grid>
      </Grid>
      <Modal
        className="modal modal-input"
        open={bookmakerModalOpen}
        onClose={togglebookmakerModal}
      >
        <Box
          className={"paper modal-show-scroll"}
          style={{ position: "relative" }}
        >
          <Grid container>
            <Grid item xs={12}>
              <h3 className="text-center">Providers</h3>
            </Grid>
          </Grid>
          <Grid
            item
            xs={12}
            style={{ display: "flex", justifyContent: "flex-end" }}
          >
            <Button
              variant="contained"
              disabled={seletedProvider == "" ? true : false}
              className="fetch-btn"
              onClick={() => {
                seletedProvider == 6 ? handlebet365() : reFetch();
                togglebookmakerModal();
              }}
            >
              Refetch
            </Button>
          </Grid>
          <CancelIcon
            className="admin-close-icon"
            onClick={togglebookmakerModal}
          />

          {isLoading && (
            <Box style={{ display: "flex", justifyContent: "center" }}>
              <Loader />
            </Box>
          )}
          {!isLoading && providersDetails.length === 0 && (
            <p>No Data Available</p>
          )}
          {!isLoading && providersDetails.length > 0 && (
            <>
              <TableContainer component={Paper}>
                <Table
                  className="listTable api-provider-listTable"
                  aria-label="simple table"
                >
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell></TableCell>
                      <TableCell>Provider Id</TableCell>
                      <TableCell>API Provider</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {providersDetails?.map((item, i) => (
                      <TableRow key={i}>
                        <TableCell style={{ width: "20px" }}>
                          <Checkbox
                            className="mz-checkbox"
                            checked={
                              item?.ApiProviderId == Number(seletedProvider)
                            }
                            value={item?.ApiProviderId}
                            onChange={(e) => handleChangeProvider(e)}
                          />
                        </TableCell>
                        <TableCell>{item.ApiProviderId}</TableCell>
                        <TableCell>{item.ApiProvider?.providerName}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}
        </Box>
      </Modal>
      <Modal
        className="modal modal-input"
        open={betProviderModal}
        onClose={togglebet365}
      >
        <div
          className={"paper modal-show-scroll"}
          style={{ position: "relative" }}
        >
          <h3 className="text-center modal-head">bet365</h3>
          <CancelIcon className="admin-close-icon" onClick={togglebet365} />
          <Box>
            <Grid item xs={12}>
              <Box className="select-box">
                <label className="modal-label">Sport Id</label>
                <Select
                  className="React "
                  classNamePrefix="select"
                  menuPosition="fixed"
                  // menuShouldBlockScroll
                  // value={seletedValue}
                  value={
                    // StatusOptions &&
                    sportIdOptions?.find((op) => {
                      return op?.value === sportId;
                    })
                  }
                  onChange={(e) => setsportId(e.value)}
                  // options={StatusOptions && StatusOptions}
                  options={sportIdOptions}
                />
              </Box>
            </Grid>
            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    className="mt-3 admin-btn-green"
                    onClick={handleSave}
                    color="secondary"
                    value={!isLoading ? "Save" : "loading..."}
                    disabled={!sportId}
                    style={{ minWidth: "auto" }}
                  />

                  <Button
                    className="mr-lr-30 outlined"
                    variant="outlined"
                    // color="primary"
                    onClick={togglebet365}
                    style={{ minWidth: "auto" }}
                  >
                    Back
                  </Button>
                </div>
              </Grid>
            </Grid>
          </Box>
        </div>
      </Modal>
    </>
  );
};

export default Dashboard;
