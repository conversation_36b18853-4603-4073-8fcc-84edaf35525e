import React, { createRef } from "react";
import { Grid, TextField } from "@mui/material";
import { ClientsFormModel } from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
// import LocationSearchInput from "../../../components/common/LocationSearchInput/LocationSearchInput";
import { setValidation } from "../../../helpers/common";

let ClientsFormModelArray = ClientsFormModel;

class CreateClient extends React.Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      values: {
        name: "",
        contact_name: "",
        email: "",
        website: "",
        phone_number: "",
      },
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
    };
  }

  componentDidMount() {
    if (this.props.isEditMode) {
      this.fetchCurrentClient(this.props.id);
    }
  }
  componentWillUnmount() {
    ClientsFormModelArray = ClientsFormModelArray.map((fieldItem) => {
      return { ...fieldItem, errorMessage: "" };
    });
  }
  fetchCurrentClient = async (id) => {
    const { status, data } = await axiosInstance.get(`/campaign/client/${id}`);
    if (status === 200) {
      this.setState({ values: data?.result });
      this.setState(() => {
        return {
          values: {
            ...this.state.values,
            // ["isVarify"]: data.isVarify === true ? true : "false",
          },
        };
      });
    }
  };

  // setActionMessage = (display = false, type = "", message = "") => {
  //   this.setState({ messageBox: { display, type, message } });
  // };

  validate = () => {
    this.props.setActionMessage();
    let { clientName, contactName, email, website, phone_number } =
      this.state.values;
    let flag = true;

    const emailVAl =
      /^(([^<>()\[\]\\\.,;:\s@]+(\.[^<>()\[\]\\\.,;:\s@]+)*)|(.+))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    const mobileVAL = /^\d{10,12}$/;

    const emailError = emailVAl.test(email);
    const numberError = mobileVAL.test(phone_number);

    if (
      clientName === "" ||
      contactName === "" ||
      email === "" ||
      emailError === false ||
      numberError === false ||
      website === "" ||
      phone_number === ""
    ) {
      flag = false;
      this.setState({ isLoading: false });
    } else {
      flag = true;
    }

    return flag;
  };

  handleSave = async () => {
    const { isEditMode, offset, sortType, sortLabelid, sortName } = this.props;
    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode
        ? `/campaign/client/${this.props.id}`
        : `/campaign/client`;

      const values = removeErrorFieldsFromValues(form.formData);
      ClientsFormModelArray = ClientsFormModelArray?.map((fieldItem) => {
        return setValidation(fieldItem, values);
      });

      if (this.validate()) {
        this.setState({ isLoading: true });
        const { status } = await axiosInstance[method](url, values);
        let sortData = sortType === "id" ? sortLabelid : sortName;
        if (status === 200) {
          this.props.inputModal();
          this.props.fetchAllUsers(offset, sortType, sortData);
          this.props.setActionMessage(
            true,
            "Success",
            `Client ${isEditMode ? "Edited" : "Created"} Successfully`
          );
        }
        this.setState({ isLoading: false });
      }
    } catch (err) {
      this.props.setActionMessage(
        true,
        "Error",
        `An error occurred while ${isEditMode ? "editing" : "creating"} Client!`
      );
      this.setState({ isLoading: false });
    }
  };

  handleChange = (field, value) => {
    let values = { ...this.state.values, [field]: value };
    this.setState({ values: values });
    ClientsFormModelArray = ClientsFormModelArray?.map((fieldItem) => {
      if (field === fieldItem?.field) {
        return setValidation(fieldItem, values);
      } else {
        return fieldItem;
      }
    });
    this.props.setActionMessage(false);
  };

  render() {
    var { values, messageBox, isLoading } = this.state;
    var { isEditMode } = this.props;

    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            {/* <Paper className="pageWrapper api-provider"> */}
            {/* {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )} */}

            <Form
              values={values}
              model={ClientsFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            />

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-purple"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30 admin-btn-outlined"
                    variant="outlined"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateClient;
