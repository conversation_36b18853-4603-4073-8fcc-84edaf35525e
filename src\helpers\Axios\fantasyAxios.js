import Axios from "axios";
import { errorHandler, fetchFromStorage } from "../../library/utilities";
import { identifiers } from "../../library/common/constants";
import { config } from "../config";
import { EventEmitter } from "../../services/event";

const fantasyAxiosInstance = Axios.create({
  baseURL: config.fantasyApiUrl,
  headers: {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": "true",
  },
});

const isMaintenancePage = (data) => {
  EventEmitter.dispatch("onmaintenance", data);
};

fantasyAxiosInstance.interceptors.request.use((config) => {
  const token = fetchFromStorage(identifiers.token);
  const clonedConfig = config;

  if (token) {
    clonedConfig.headers = {
      Authorization: `Bearer ${token.access_token}`,
      ...config.headers,
    };
  }

  return clonedConfig;
});

fantasyAxiosInstance.interceptors.response.use(
  (config) => {
    updateHeaderResponse(config);
    return config;
  },
  (error) => {
    errorHandler(error);
    updateHeaderResponse(error?.response);
    return Promise.reject(error);
  }
);
let maintenanceModeEmitted = false;
const updateHeaderResponse = (response) => {
  const maintenance = response?.headers?.["x-maintenance"];
  if (!maintenanceModeEmitted) {
    isMaintenancePage(maintenance);
    maintenanceModeEmitted = true;
  }
};
export default fantasyAxiosInstance;
