import { Validators } from "../../../library/utilities/Validators";

export const eventsFormModel = [
  // {
  //   label: "Sport*",
  //   value: "",
  //   type: "dropdown",
  //   placeholder: "Sport",
  //   field: "sportId",
  //   validators: [{ check: Validators.required }],
  //   required: true,
  //   className: "6",
  //   options: [],
  // },
  // {
  //   label: 'League*',
  //   value: '',
  //   type: 'dropdown',
  //   placeholder: 'League',
  //   field: 'leagueId',
  //   validators: [{ check: Validators.required }],
  //   required: true,
  //   className: '6',
  //   options: [],
  // },
  // {
  //   label: "Api Event Id*",
  //   value: "",
  //   type: "text",
  //   placeholder: "Api Event Id",
  //   field: "apiEventId",
  //   validators: [{ check: Validators.required }],
  //   required: true,
  //   className: "6",
  // },
  {
    label: "Event Name*",
    value: "",
    type: "text",
    placeholder: "Event Name",
    field: "eventName",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
  },
  {
    label: "Description",
    value: "",
    type: "text",
    placeholder: "Description",
    field: "description",
    required: false,
    className: "6",
  },
  // {
  //   label: "Category",
  //   value: "",
  //   type: "dropdown",
  //   placeholder: "Category",
  //   field: "categoryId",
  //   validators: [
  //     { check: Validators.required},
  //   ],
  //   required: true,
  //   className: "6",
  //   options: [
  //     { value: "", label: "No Category Selected" },

  //   ],
  // },
  {
    label: "Track",
    value: "",
    type: "dropdown",
    placeholder: "Location",
    field: "trackId",
    required: false,
    className: "6",
    options: [],
  },
  {
    label: "Location",
    value: "",
    type: "dropdown",
    placeholder: "Location",
    field: "locationId",
    required: false,
    className: "6",
    options: [],
  },
  // {
  //   label: 'Weather',
  //   value: '',
  //   type: 'dropdown',
  //   placeholder: 'Weather',
  //   field: 'weather',
  //   required: false,
  //   className: '6',
  //   options: [],
  // },
  {
    label: "Comment",
    value: "",
    type: "text",
    placeholder: "Comment",
    field: "comment",
    required: false,
    className: "6",
  },
  // {
  //   label: "Variation",
  //   value: "",
  //   type: "text",
  //   placeholder: "Variation",
  //   field: "variation",
  //   required: false,
  //   className: "6",
  // },
];

export const eventsPlaceFormModel = [
  // {
  //   label: 'Sport*',
  //   value: '',
  //   type: 'dropdown',
  //   placeholder: 'Sport',
  //   field: 'sportId',
  //   validators: [{ check: Validators.required }],
  //   required: true,
  //   className: '6',
  //   options: [],
  // },
  {
    label: "Event Name*",
    value: "",
    type: "text",
    placeholder: "Event Name",
    field: "eventName",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
  },
  {
    label: "Description",
    value: "",
    type: "text",
    placeholder: "Description",
    field: "description",
    required: false,
    className: "6",
  },
  {
    label: "Comment",
    value: "",
    type: "text",
    placeholder: "Comment",
    field: "comment",
    required: false,
    className: "6",
  },
  // {
  //   label: 'Variation',
  //   value: '',
  //   type: 'text',
  //   placeholder: 'Variation',
  //   field: 'variation',
  //   required: false,
  //   className: '6',
  // },
  {
    label: "Location",
    value: "",
    type: "dropdown",
    placeholder: "Location",
    field: "locationId",
    required: false,
    className: "6",
    options: [],
  },
  {
    label: "League*",
    value: "",
    type: "dropdown",
    placeholder: "League",
    field: "leagueId",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
    options: [],
  },
  {
    label: "Round",
    value: "",
    type: "text",
    placeholder: "round",
    field: "round",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
  },
];
