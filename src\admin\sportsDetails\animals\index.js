import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHead,
  Modal,
  Button,
  InputAdornment,
  Breadcrumbs,
  IconButton,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import Pagination from "@mui/material/Pagination";
// import { ReactSVG } from "react-svg";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
import "./animal.scss";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../../library/common/constants";
import { Loader } from "../../../library/common/components";
import CreateAnimal from "../../animal/CreateAnimal";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import SearchIcons from "../../../images/searchIcon.svg";
import { Link } from "react-router-dom";
import _ from "lodash";
import CreateVariation from "./createVariation";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";

const Index = (props) => {
  const [search, setSearch] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isInputModalOpen, setIsInputModalOpen] = useState(false);
  const [idToSend, setIdToSend] = useState(null);
  const [data, setData] = useState([]);
  const [allGender, setAllGender] = useState([]);
  const [allColour, setAllColour] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [sportCount, setSportCount] = useState(null);
  const [rowPerPage] = useState(20); //setRowPerPage
  const [offset, setOffset] = useState(0);
  const [pageNumbers] = useState([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isVariationModalOpen, setisVariationModalOpen] = useState(false);
  const [isId, setisId] = useState("");
  const [isName, setisName] = useState("");

  // Merge States
  const [isMergeModalOpen, setisMergeModalOpen] = useState(false);
  const [searchTrack, setsearchTrack] = useState([]);
  const [searchModalTrack, setsearchModalTrack] = useState([]);
  const [ModalTrackData, setModalTrackData] = useState([]);
  const [searchModalChildTrack, setsearchModalChildTrack] = useState([]);
  const [ModalChildTrackData, setModalChildTrackData] = useState([]);
  const [childTrack, setchildTrack] = useState([]);
  const [searchTrackCount, setsearchTrackCount] = useState(0);
  const [searchTrackPage, setsearchTrackPage] = useState(0);
  const [ModalTrackCount, setModalTrackCount] = useState(0);
  const [searchModalTrackCount, setsearchModalTrackCount] = useState(0);
  const [searchModalTrackPage, setsearchModalTrackPage] = useState(0);
  const [ModalTrackPage, setModalTrackPage] = useState(0);
  const [ModalChildTrackCount, setModalChildTrackCount] = useState(0);
  const [searchModalChildTrackCount, setsearchModalChildTrackCount] =
    useState(0);
  const [searchModalChildTrackPage, setsearchModalChildTrackPage] = useState(0);
  const [ModalChildTrackPage, setModalChildTrackPage] = useState(0);
  const [isTrackSearch, setisTrackSearch] = useState("");
  const [isModalTrackSearch, setisModalTrackSearch] = useState("");
  const [isModalChildTrackSearch, setisModalChildTrackSearch] = useState("");
  const [ModalChildTrackSearch, setModalChildTrackSearch] = useState("");
  const [ModalParentTrackSearch, setModalParentTrackSearch] = useState("");
  const [parentTrack, setparentTrack] = useState("");
  const [createError, setcreateError] = useState("");
  const [isDeleteLoading, setisDeleteLoading] = useState("");
  const [rowToPass, setrowToPass] = useState({});
  const [messageBox, setmessageBox] = useState({
    display: false,
    type: "",
    message: "",
  });

  // let currentPageRow = data;

  useEffect(() => {
    // fetchAllGender();
    // fetchAllColour();
    setSearch("");
    if (offset === 0) {
      fetchAnimal(true);
    } else {
      setOffset(0);
      setCurrentPage(1);
    }

    // eslint-disable-next-line
  }, [props.match]);

  useEffect(() => {
    fetchAnimal();
    // eslint-disable-next-line
  }, [offset]);

  const fetchAnimal = async (type, searchInput) => {
    let searchValue = type ? "" : search;
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        `animal/animaltype/${
          props.match.params.typeid
        }?limit=${rowPerPage}&offset=${
          searchInput ? 0 : offset
        }&matchString=${searchValue}`
      );
      if (status === 200) {
        setData(data?.result?.rows);
        setIsLoading(false);
        setSportCount(data?.result?.count);
        // setSearch("");
      }
    } catch (err) {
      // console.log(err);
      setIsLoading(false);
    }
  };

  const setActionMessage = (display = false, type = "", message = "") => {
    //  this.setState({ messageBox: { display, type, message } }, () =>
    //    setTimeout(
    //      () =>
    //        this.setState({
    //          messageBox: { display: false, type: "", message: "" },
    //        }),
    //      3000
    //    )
    //  );
    setmessageBox(
      {
        display,
        type,
        message,
      },
      () =>
        setTimeout(
          () => setmessageBox({ display: false, type: "", message: "" }),
          3000
        )
    );
  };

  // const fetchAllGender = async () => {
  //   const { status, data } = await axiosInstance.get(URLS.animalGender);
  //   if (status === 200) {
  //     setAllGender(data.result);
  //   }
  // };

  // const fetchAllColour = async () => {
  //   const { status, data } = await axiosInstance.get(URLS.animalColour);
  //   if (status === 200) {
  //     setAllColour(data.result);
  //   }
  // };

  const afterChangeRefresh = () => {
    fetchAnimal();
  };

  const toggleInputModal = () => {
    setIsInputModalOpen(!isInputModalOpen);
  };

  const inputModal = (id, type) => {
    setIsInputModalOpen(true);
    setIdToSend(id);
    setIsEditMode(type);
  };

  const handleAnimalDelete = async () => {
    try {
      const { status } = await axiosInstance.delete(`animal/${itemToDelete}`);
      if (status === 200) {
        afterChangeRefresh();
        toggleDeleteModal();
      }
    } catch (err) {}
  };

  const setItemDelete = (id) => {
    setItemToDelete(id);
    setIsDeleteModalOpen(true);
  };

  const toggleDeleteModal = () => {
    setItemToDelete(null);
    setIsDeleteModalOpen(!isDeleteModalOpen);
  };

  const mergeModal = (item) => {
    setisMergeModalOpen(true);
    fetchModalParentAnimal(0, "");
    fetchModalChildAnimal(0, "");
    let Tracks = "";
    Tracks = {
      value: item?.id,
      label: item?.name,
    };
    setparentTrack(Tracks);
    setchildTrack([]);
    // this.setState({
    //   parentTrack: Tracks,
    //   childTrack: [],
    // });
  };

  const toggleMergeModal = () => {
    setisMergeModalOpen(false);
    setcreateError("");
    setparentTrack("");
    setchildTrack("");
  };

  const fetchModalParentAnimal = async (ModalTrackPage, searchvalue) => {
    // const passApi = `track?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${searchvalue}`;
    const passApi = `animal/animaltype/${props.match.params.typeid}?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${searchvalue}`;

    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(ModalTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      setModalTrackCount(Math.ceil(count));
      setModalTrackData(finalData);
    }
  };

  const handleOnScrollBottomModalParentAnimal = (e, type) => {
    if (
      isModalTrackSearch !== "" &&
      searchModalTrackCount !== Math.ceil(searchModalTrackPage / 20 + 1)
    ) {
      handleModalParentAnimalInputChange(
        searchModalTrackPage + 20,
        isModalTrackSearch
      );
      //  this.setState({
      //    searchModalTrackPage: searchModalTrackPage + 20,
      //  });
      setsearchModalChildTrackPage(searchModalTrackPage + 20);
    } else {
      if (
        ModalTrackCount !==
          (ModalTrackCount == 1 ? 1 : Math.ceil(ModalTrackPage / 20)) &&
        isModalTrackSearch == ""
      ) {
        fetchModalParentAnimal(ModalTrackPage + 20, isModalTrackSearch);
        //  this.setState({
        //    ModalTrackPage:,
        //  });
        setModalTrackPage(ModalTrackPage + 20);
      }
    }
  };
  const handleModalParentAnimalInputChange = (ModalTrackPage, value) => {
    const passApi = `animal/animaltype/${props.match.params.typeid}?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${value}`;

    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        setsearchModalTrack(finalData);
        setsearchModalTrackCount(Math.ceil(count));
        setisModalTrackSearch(value);
      }
    });
  };

  const fetchModalChildAnimal = async (ModalTrackPage, searchvalue) => {
    const passApi = `animal/animaltype/${props.match.params.typeid}?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${searchvalue}`;

    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(ModalChildTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      }).filter((Track) => Track?.value !== parentTrack?.value);

      setModalChildTrackCount(Math.ceil(count));
      // const data1 = {
      //   label: "AAAAA",
      //   value: 10011,
      // };
      // const data2 = {
      //   label: "AAAAB",
      //   value: 10012,
      // };
      // finalData?.push(data1, data2);
      setModalChildTrackData(finalData);
    }
  };

  const handleOnScrollBottomModalChildAnimal = (e, type) => {
    if (
      isModalChildTrackSearch !== "" &&
      searchModalChildTrackCount !==
        Math.ceil(searchModalChildTrackPage / 20 + 1)
    ) {
      handleModalChildAnimalInputChange(
        searchModalChildTrackPage + 20,
        isModalChildTrackSearch
      );
      setsearchModalChildTrackPage(searchModalChildTrackPage + 20);
    } else {
      if (
        ModalChildTrackCount !==
          (ModalChildTrackCount == 1
            ? 1
            : Math.ceil(ModalChildTrackPage / 20)) &&
        isModalChildTrackSearch == ""
      ) {
        fetchModalChildAnimal(
          ModalChildTrackPage + 20,
          isModalChildTrackSearch
        );
        setModalChildTrackPage(ModalChildTrackPage + 20);
      }
    }
  };
  const handleModalChildAnimalInputChange = (ModalTrackPage, value) => {
    const passApi = `animal/animaltype/${props.match.params.typeid}?limit=${rowPerPage}&offset=${ModalTrackPage}&matchString=${value}`;

    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        })?.filter((Track) => Track?.value !== parentTrack?.value);

        setsearchModalChildTrack(finalData);
        setsearchModalChildTrackCount(Math.ceil(count));
        setisModalChildTrackSearch(value);
      }
    });
  };

  const handalMergeAnimal = async () => {
    setIsLoading(true);
    let payload = {
      parentAnimalId: parentTrack?.value,
      childAnimalId: childTrack?.map((item) => item?.value),
    };
    try {
      const passApi = `animal/merge/animal`;
      const { status, data } = await axiosInstance.post(passApi, payload);
      if (status === 200) {
        setActionMessage(true, "Success", data?.message);
        setIsLoading(false);
        setisMergeModalOpen(false);
        fetchAnimal();
      } else {
        setActionMessage(true, "Error", data?.message);
        setIsLoading(false);
        setcreateError(data?.message);
      }
    } catch (err) {
      setActionMessage(true, "Error", err?.response?.data?.message);
      setIsLoading(false);
      setcreateError(err?.response?.data?.message);
    }
  };

  const handleTrackDelete = async () => {
    setisDeleteLoading("trackDelete");
    setIsDeleteModalOpen(false);

    try {
      const { status } = await axiosInstance.delete(`track/${itemToDelete}`);
      if (status === 200) {
        afterChangeRefresh();
        setisDeleteLoading("");
        setItemToDelete(null);
      }
    } catch (err) {
      // console.log(err);
    }
  };

  // const handlePaginationButtonClick = (navDirection) => {
  //   if (navDirection === "prev") {
  //     if (offset >= 20) {
  //       setOffset(offset - 20);
  //       setCurrentPage(currentPage - 1);
  //     }
  //   } else {
  //     setOffset(offset + 20);
  //     setCurrentPage(currentPage + 1);
  //   }
  // };

  const handlePaginationClick = (event, page) => {
    setCurrentPage(Number(page));
    setOffset((Number(page) - 1) * rowPerPage);
  };

  if (sportCount > 0) {
    // const indexOfLastTodo = currentPage * rowPerPage;
    // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    // currentPageRow = data.slice(indexOfFirstTodo, indexOfLastTodo);

    for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
      pageNumbers.push(i);
    }
  }
  const inputVariationModal = (id, name) => {
    setisVariationModalOpen(true);
    setisId(id);
    setisName(name);
  };
  const toggleVariationModal = () => {
    setisVariationModalOpen(false);
  };

  const handleClearClick = () => {
    setSearch("");
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      fetchAnimal(null, "searchInput");
      setOffset(0);
      setCurrentPage(1);
    }
  };

  return (
    <Grid container>
      <Grid item xs={12} className="pageWrapper">
        {/* <Paper className="pageWrapper"> */}
        <Box className="bredcrumn-wrap">
          <Breadcrumbs
            separator="/"
            aria-label="breadcrumb"
            className="breadcrumb"
          >
            <Link underline="hover" color="inherit" to="/dashboard">
              Home
            </Link>
            <Link underline="hover" color="inherit">
              racing
            </Link>
            <Link underline="hover" color="inherit">
              {props.match.params.name}
            </Link>
            <Typography className="active_p">
              {props.match.params.animalname}
            </Typography>
          </Breadcrumbs>
        </Box>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          {/* <Typography variant="h5" style={{ margin: "0" }}>
            {props.match.params.animalname}
          </Typography> */}
          <Typography variant="h1" align="left">
            {props.match.params.animalname}
          </Typography>
          <div>
            <TextField
              className={`txt-field-class`}
              // label="Search"
              placeholder="Search "
              size="small"
              variant="outlined"
              onKeyDown={(e) => handleKeyDown(e)}
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
              sx={{ margin: "8px" }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <img src={SearchIcons} alt="icon" />
                    {/* <SearchIcon /> */}
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    {search && (
                      <IconButton
                        onClick={() => handleClearClick()}
                        edge="end"
                        style={{ minWidth: "unset" }}
                        size="large"
                      >
                        <CancelIcon />
                      </IconButton>
                    )}
                  </InputAdornment>
                ),
              }}
            />
            <Button
              variant="contained"
              sx={{ margin: "8px" }}
              style={{
                backgroundColor: "#4455c7",
                color: "#fff",
                borderRadius: "8px",
                textTransform: "capitalize",
              }}
              onClick={() => {
                fetchAnimal(null, "searchInput");
                setOffset(0);
                setCurrentPage(1);
              }}
            >
              Search
            </Button>
            <Button
              variant="contained"
              sx={{ margin: "8px" }}
              style={{
                backgroundColor: "#4455c7",
                color: "#fff",
                borderRadius: "8px",
                textTransform: "capitalize",
              }}
              onClick={() => inputModal(null, false)}
            >
              Add {props.match.params.animalname}
            </Button>
          </div>
        </Box>
        {isLoading && <Loader />}
        {!isLoading && data.length === 0 && <p>No Data Available</p>}
        {!isLoading && data.length > 0 && (
          <TableContainer component={Paper}>
            <Table className="listTable" aria-label="simple table">
              <TableHead>
                <TableRow className="tableHead-row">
                  <TableCell>DID</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Age</TableCell>
                  {/* <TableCell>Colour</TableCell>
                  <TableCell>Gender</TableCell> */}
                  <TableCell>Variation</TableCell>
                  <TableCell>Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody className="table_body">
                <TableRow className="table_row">
                  <TableCell
                    colSpan={100}
                    className="table-seprator"
                  ></TableCell>
                </TableRow>
                {data
                  /* .filter((data) => {
                      if (search === "") return data;
                      else if (
                        data.name
                          .toString()
                          .toLowerCase()
                          .includes(search.toString().toLowerCase())
                      ) {
                        return data;
                      }
                      return "";
                    }) */
                  /* .slice(
                      currentPage * rowPerPage - rowPerPage,
                      currentPage * rowPerPage
                    ) */
                  .map((row, index) => (
                    <TableRow key={index} className="table-rows listTable-Row">
                      <TableCell>{row?.id}</TableCell>
                      <TableCell>{row?.name}</TableCell>
                      <TableCell>{row?.age}</TableCell>
                      {/* <TableCell>{row?.colour}</TableCell>
                      <TableCell>{row?.gender}</TableCell> */}
                      <TableCell>
                        <Button
                          variant="contained"
                          style={{
                            backgroundColor: "#4455C7",
                            color: "#fff",
                            borderRadius: "8px",
                            textTransform: "capitalize",
                            // padding: "13px 24px 12px",
                            marginLeft: "15px",
                          }}
                          onClick={() => {
                            inputVariationModal(row?.id, row?.name);
                          }}
                        >
                          Add/Edit variation
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Button
                          className="table-btn edit-btn"
                          onClick={() => {
                            inputModal(row?.id, true);
                          }}
                          style={{ cursor: "pointer" }}
                        >
                          Edit
                        </Button>
                        <Button
                          className="table-btn delete-btn"
                          style={{ cursor: "pointer" }}
                          onClick={() => {
                            setItemDelete(row?.id);
                          }}
                        >
                          Delete
                        </Button>
                        <Button
                          className="table-btn"
                          style={{
                            cursor: "pointer",
                            backgroundColor: "#4455C7",
                            color: "#fff",
                            borderRadius: "8px",
                            textTransform: "uppercase",
                          }}
                          onClick={() => mergeModal(row)}
                        >
                          Merge
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                <TableRow>
                  <TableCell colSpan={100} className="pagination">
                    {data?.length > 0 && (
                      <div className="tablePagination animal-pagination">
                        {/* <button
                          className={
                            offset >= 20
                              ? "btn-navigation"
                              : "btn-navigation-disabled"
                          }
                          disabled={offset >= 20 ? false : true}
                          onClick={() => handlePaginationButtonClick("prev")}
                        >
                          <ReactSVG src={arrowLeft} />
                        </button> */}
                        <Pagination
                          hideNextButton
                          hidePrevButton
                          disabled={sportCount > 0 ? false : true}
                          page={currentPage}
                          onChange={handlePaginationClick}
                          count={pageNumbers[pageNumbers?.length - 1]}
                          siblingCount={2}
                          boundaryCount={1}
                          size="small"
                        />
                        {/* <button
                          className={
                            rowPerPage + offset < sportCount
                              ? "btn-navigation"
                              : "btn-navigation-disabled"
                          }
                          disabled={
                            rowPerPage + offset < sportCount ? false : true
                          }
                          onClick={() => handlePaginationButtonClick("next")}
                        >
                          <ReactSVG src={arrowRight} />
                        </button> */}
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        )}
        {/* </Paper> */}

        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={toggleInputModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {isEditMode
                ? `Edit ${props.match.params.animalname}`
                : `Create ${props.match.params.animalname}`}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={toggleInputModal}
            />
            <CreateAnimal
              inputModal={toggleInputModal}
              id={idToSend}
              isEditMode={isEditMode}
              fetchAllAniaml={afterChangeRefresh}
              allGender={allGender}
              allColour={allColour}
              animalTypeId={props.match.params.typeid}
            />
          </div>
        </Modal>
        <Modal
          className="modal modal-input"
          open={isVariationModalOpen}
          onClose={toggleVariationModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">
              {props.match.params.animalname} Variation Details({isName})
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={toggleVariationModal}
            />
            <CreateVariation
              inputModal={toggleVariationModal}
              id={isId}
              fetchAllAniaml={afterChangeRefresh}
              variationData={data?.filter((item) => isId == item.id)}
              animalname={props.match.params.animalname}
            />
          </div>
        </Modal>

        <Modal
          className="modal modal-input"
          open={isMergeModalOpen}
          onClose={() => toggleMergeModal()}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">Merge Animal</h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={() => toggleMergeModal()}
            />
            <Grid container className="page-content adminLogin text-left">
              <Grid item xs={12}>
                <label className="modal-label"> Parent Animal </label>
                <Select
                  className="React teamsport-select teamsport-multiple-select mb15 merge-select"
                  classNamePrefix="select"
                  menuPosition="fixed"
                  onMenuScrollToBottom={(e) =>
                    handleOnScrollBottomModalParentAnimal(e)
                  }
                  onInputChange={(e) =>
                    handleModalParentAnimalInputChange(0, e)
                  }
                  value={
                    isModalTrackSearch
                      ? searchModalTrack?.find((item) => {
                          return item?.value == parentTrack;
                        })
                      : parentTrack
                  }
                  options={
                    isModalTrackSearch ? searchModalTrack : ModalTrackData
                  }
                  onChange={(e) => setparentTrack(e)}
                />
              </Grid>
              <Grid item xs={12}>
                <label className="modal-label"> Child Animals </label>
                <Select
                  className="React teamsport-select teamsport-multiple-select merge-select"
                  classNamePrefix="select"
                  menuPosition="fixed"
                  isMulti
                  onMenuScrollToBottom={(e) =>
                    handleOnScrollBottomModalChildAnimal(e)
                  }
                  onInputChange={(e) => handleModalChildAnimalInputChange(0, e)}
                  value={
                    isModalChildTrackSearch !== ""
                      ? searchModalChildTrack?.find((item) => {
                          return item?.value === childTrack;
                        })
                      : childTrack
                  }
                  options={
                    isModalChildTrackSearch !== ""
                      ? searchModalChildTrack
                      : ModalChildTrackData
                  }
                  onChange={(e) => setchildTrack(e)}
                />
              </Grid>
            </Grid>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    className="mt-3 admin-btn-green"
                    onClick={handalMergeAnimal}
                    color="primary"
                    value={!isLoading ? "Merge" : "Loading..."}
                    disabled={isLoading}
                  />
                </div>
                {createError ? (
                  <p
                    className="errorText"
                    style={{ margin: "0px 0 0 0", width: "300px" }}
                  >
                    {createError}
                  </p>
                ) : (
                  ""
                )}
              </Grid>
            </Grid>
          </div>
        </Modal>

        <ShowModal
          isModalOpen={isDeleteModalOpen}
          onClose={toggleDeleteModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={handleAnimalDelete}
          onCancel={toggleDeleteModal}
        />
      </Grid>
    </Grid>
  );
};

export default Index;
