import React, { useCallback, useEffect, useState } from "react";
import {
  Box,
  Breadcrumbs,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  Tabs,
  Tab,
  Paper,
} from "@mui/material";
import { Link } from "react-router-dom";
import { Loader } from "../../library/common/components";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import Select from "react-select";
import "./socSmartPlayDashboardNew.scss";
import moment from "moment";
import ArrowBack from "@mui/icons-material/ArrowBack";
import ArrowForward from "@mui/icons-material/ArrowForward";
import axiosInstance from "../../helpers/Axios";
import SOCTabDeatils from "./socTabDeatils";
import SmartPlayTabDetails from "./smartPlayTabDetails";

const SOCSmartplayDashboardNew = () => {
  const [activeTab, setActiveTab] = useState(0); // 0 for SOC, 1 for SmartPlay

  // Add year options and selected year state
  const [yearOptions, setYearOptions] = useState([]);
  const [selectedYear, setSelectedYear] = useState(moment().year());

  useEffect(() => {
    const currentYear = moment().year();
    const options = Array.from({ length: 3 }, (_, i) => {
      const year = currentYear - i;
      return { label: `Year ${year}`, value: year };
    });
    setYearOptions(options);
    setSelectedYear(currentYear);
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setSelectedYear(moment().year());
  };

  return (
    <>
      <Grid
        container
        className="page-content adminLogin soc-smartplay-dashboard-wrap"
      >
        <Grid item xs={12} className="pageWrapper">
          <Box className="bredcrumn-wrap">
            <Breadcrumbs
              separator="/"
              aria-label="breadcrumb"
              className="breadcrumb"
            >
              <Link underline="hover" color="inherit" to="/dashboard">
                Home
              </Link>
              <Link underline="hover" color="inherit">
                User Management
              </Link>
              <Typography className="active_p">
                SOC SmartPlay Dashboard{" "}
              </Typography>
            </Breadcrumbs>
          </Box>
          <Grid container direction="row" alignItems="center">
            <Grid item xs={12}>
              <Typography variant="h1" align="left">
                SOC SmartPlay Dashboard
              </Typography>
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "20px" }}
              justifyContent={"flex-start"}
            >
              <Box className="header-top-filter">
                <Box className="header-tab-section">
                  <Tabs
                    value={activeTab}
                    onChange={handleTabChange}
                    aria-label="dashboard tabs"
                    centered
                    sx={{
                      "& .MuiTabs-indicator": {
                        backgroundColor: "#FC4714",
                        height: "4px",
                      },
                      "& .MuiTab-root": {
                        padding: "0px 12px",
                      },
                    }}
                  >
                    <Tab label="SOC" />
                    <Tab label="SmartPlay" />
                  </Tabs>
                </Box>
                <Box className="year-filter">
                  <Select
                    className="React cricket-select external-select"
                    classNamePrefix="select"
                    placeholder="Select Year"
                    options={yearOptions}
                    value={yearOptions?.find(
                      (option) => option?.value === selectedYear
                    )}
                    onChange={(option) => setSelectedYear(option?.value)}
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>
          <Box className="tab-deatils-section">
            {activeTab === 0 && <SOCTabDeatils selectedYear={selectedYear} />}
            {activeTab === 1 && (
              <SmartPlayTabDetails selectedYear={selectedYear} />
            )}
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default SOCSmartplayDashboardNew;
