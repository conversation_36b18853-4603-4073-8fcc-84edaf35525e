import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Typography,
  Breadcrumbs,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import RateReviewIcon from "@mui/icons-material/RateReview";
// import ButtonComponent from "../../library/common/components/Button";
import { Editor } from "react-draft-wysiwyg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import { Loader, ActionMessage } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import { config } from "../../helpers/config";
import Pagination from "@mui/material/Pagination";
import SearchIcons from "../../images/searchIcon.svg";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../library/common/components/Button";
import FileUploader from "../../library/common/components/FileUploader";

class SponsorsLogo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      sponsorsLogo: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      isCMSModalOpen: false,
      isReviewModalOpen: false,
      isAddToCMSInfoMode: false,
      isDetailsAvailable: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      labelValues: {
        labelName: "",
        id: "",
      },
      uploadLogo: "",
      logo: [],
      uploadImage: "",
      authorUploadImage: "",

      image: [],
      authorImage: [],
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      SponsorsLogoCount: 0,
      bookKeeperId: "",
      errorName: "",
      uploadError: "",
      createError: "",
      search: "",
    };
  }

  componentDidMount() {
    this.fetchAllSponsorsLogo(0, "");
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllSponsorsLogo(this.state.offset, this.state?.search);
    }
  }

  async fetchAllSponsorsLogo(page, searchvalue) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `advertiselogo/advertiselogo?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
      );
      if (status === 200) {
        this.setState({
          sponsorsLogo: data?.result,
          isLoading: false,
          SponsorsLogoCount: data?.count,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { labelValues, logo } = this.state;

    let flag = true;
    if (labelValues?.labelName?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (logo?.length === 0) {
      flag = false;
      this.setState({
        uploadError: "This field is mandatory",
      });
    } else {
      this.setState({
        uploadError: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    const { logo } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });

      let payload = {
        name: this.state?.labelValues?.labelName.trim(),
      };

      if (logo?.length > 0) {
        let fileData = await this.setMedia(logo[0]);
        if (fileData) {
          payload = {
            ...payload,
            logo: fileData?.image?.filePath,
          };
          this.setState({
            uploadLogo: fileData?.image?.filePath,
          });
        }
      }
      let passApi = "/advertiselogo/advertiselogo";
      try {
        const { status, data } = await axiosInstance.post(
          `${passApi}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            logo: [],
            uploadLogo: "",
          });
          this.fetchAllSponsorsLogo(this.state.offset, this.state?.search);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };
  handleUpdate = async () => {
    const { logo } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });

      let payload = {
        name: this.state?.labelValues?.labelName.trim(),
      };
      if (logo?.length > 0) {
        let fileData = await this.setMedia(logo[0]);
        if (fileData) {
          payload = {
            ...payload,
            logo: fileData?.image?.filePath,
          };
          this.setState({
            uploadLogo: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          logo: this.state.uploadLogo,
        };
      }
      let passApi = "/advertiselogo/advertiselogo";
      try {
        const { status, data } = await axiosInstance.put(
          `${passApi}/${this.state.labelValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            logo: [],
            uploadLogo: "",
          });
          this.fetchAllSponsorsLogo(this.state.offset, this.state?.search);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      uploadError: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        labelValues: {
          labelName: item?.name,
          id: item?.id,
        },
        uploadLogo: item?.logo,
        isEditMode: true,
      });
    } else {
      this.setState({
        labelValues: {
          labelName: "",
        },
        logo: [],
        uploadLogo: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllSponsorsLogo();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `advertiselogo/advertiselogo/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllSponsorsLogo(this.state.offset, this.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          "Bookkeeper Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handeModleLoading = (status) => {
    this.setState({
      isLoading: status,
    });
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllSponsorsLogo(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      sponsorsLogo,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      labelValues,
      editorState,
      bookKeeperId,
      isDetailsAvailable,
      createError,
      errorName,
      uploadError,
      logo,
      uploadLogo,
      SponsorsLogoCount,
      search,
    } = this.state;

    const pageNumbers = [];

    if (SponsorsLogoCount > 0) {
      for (let i = 1; i <= Math.ceil(SponsorsLogoCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper > */}
            {messageBox?.display && (
              <ActionMessage
                message={messageBox?.message}
                type={messageBox?.type}
                styleClass={messageBox?.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Sponsors Logo</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                {/* <h3 className="text-left">Sponsors Logo</h3> */}
                <Typography variant="h1" align="left">
                  Sponsors Logo
                </Typography>
              </Grid>
              {/* <Grid item xs={2} style={{ textAlign: "end" }}>
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "6px 10px",
                  marginTop: "5px",
                }}
                onClick={this.inputModal(null, "create")}
              >
                Add New
              </Button>
            </Grid> */}
              <Grid item xs={9} className="admin-filter-wrap">
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllSponsorsLogo(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && sponsorsLogo?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && sponsorsLogo?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Sponsors Logo</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {sponsorsLogo?.map((bookkeeper, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{bookkeeper?.id}</TableCell>
                          <TableCell>
                            <Box>{bookkeeper?.name}</Box>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.logo ? (
                                <img
                                  src={config.mediaUrl + bookkeeper?.logo}
                                  alt={bookkeeper?.name}
                                />
                              ) : (
                                <div className="imagePlaceholder"> -</div>
                              )}
                            </div>
                          </TableCell>

                          <TableCell>
                            <Box style={{ display: "flex" }}>
                              <Button
                                onClick={this.inputModal(bookkeeper, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>

                              <Button
                                onClick={() =>
                                  this.setItemToDelete(bookkeeper.id)
                                }
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                SponsorsLogoCount / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Sponsors Logo"
                    : "Edit Sponsors Logo"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleInputModal()}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">Sponsor's Name</label>
                      <TextField
                        className="teamsport-textfield eventname"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Sponsors Name"
                        value={labelValues?.labelName}
                        onChange={(e) =>
                          this.setState({
                            labelValues: {
                              ...labelValues,
                              labelName: e.target.value,
                            },
                          })
                        }
                      />
                      {errorName ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorName}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>

                    <div className="blog-file-upload">
                      <h6 className="modal-label">Sponsor's Logo</h6>
                      <FileUploader
                        onDrop={(logo) => this.handleFileUpload("logo", logo)}
                      />
                      <div className="logocontainer">
                        {logo?.length > 0
                          ? logo?.map((file, index) => (
                              <img
                                className="auto-width"
                                key={index}
                                src={file.preview}
                                alt="file"
                              />
                            ))
                          : uploadLogo &&
                            uploadLogo !== "" && (
                              <img
                                className="auto-width"
                                src={config.mediaUrl + uploadLogo}
                                alt="file"
                              />
                            )}
                      </div>
                      {uploadError ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {uploadError}
                        </p>
                      ) : (
                        ""
                      )}
                    </div>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn"
                            value="Back"
                          />
                        </div>
                        {/* {createError ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0", width: "300px" }}
                        >
                          {createError}
                        </p>
                      ) : (
                        ""
                      )} */}
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default SponsorsLogo;
