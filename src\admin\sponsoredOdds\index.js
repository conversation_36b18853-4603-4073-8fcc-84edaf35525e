import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select from "react-select";
import moment from "moment-timezone";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import { IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import TableSortLabel from "@mui/material/TableSortLabel";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "./sponsoredOdds.scss";
import CSVExport from "../csvExport/CSVExport";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class SponsoredOdds extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      sponsoredValues: {
        id: "",
        startDate: null,
        endDate: null,
        selectedprovider: [],
        selectedSportId: [],
        selectprovideredit: "",
      },
      sponsoredList: [],
      providersDetails: [],
      sponsoredCount: 0,
      errorRequire: "",
      errorStartDate: "",
      errorEndDate: "",
      errorselectProvider: "",
      errorselectProvideredit: "",
      sortLabelid: false,
      sortLabelstartDate: true,
      sortLabelendDate: true,
      sortType: "id",
      sortvalue: true,
      allSportsType: [],
      sportsOption: [],
      errorselectSportId: "",
      SelectedSport: "",
      AllSportsOption: [],
      sortDate: null,
      filterEndDate: null,
      startDateOpen: false,
      endDateOpen: false,
    };
  }

  componentDidMount() {
    this.fetchAllSponsoredOdds(0, "id", false, "", null, null);
    this.fetchProviders();
    // this.fetchAllSportType();
    this.fetchAllSports();
  }
  componentDidUpdate(prevProps, prevState) {
    const {
      sortLabelendDate,
      sortLabelstartDate,
      sortType,
      sortLabelid,
      sortDate,
      filterEndDate,
    } = this.state;
    if (prevState.offset !== this.state.offset) {
      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "sponsorDate"
          ? sortLabelstartDate
          : sortLabelendDate;
      this.fetchAllSponsoredOdds(
        this.state.offset,
        this.state.sortType,
        sortData,
        this.state.SelectedSport,
        sortDate,
        filterEndDate
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "sponsorDate"
          ? sortLabelstartDate
          : sortLabelendDate;
      this.fetchAllSponsoredOdds(
        0,
        this.state.sortType,
        sortData,
        "",
        sortDate,
        filterEndDate
      );
      this.setState({
        offset: 0,
        currentPage: 1,
        SelectedSport: "",
      });
    }
  }
  handleSortStartDate = (date) => {
    const {
      offset,
      sortType,
      SelectedSport,
      filterEndDate,
      sortLabelendDate,
      sortLabelstartDate,
      sortLabelid,
    } = this.state;

    const sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "sponsorDate"
        ? sortLabelstartDate
        : sortLabelendDate;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAllSponsoredOdds(
        offset,
        sortType,
        sortData,
        SelectedSport,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        filterEndDate
      );
    }
  };
  handleFilterEndDate = (date) => {
    const {
      offset,
      sortType,
      SelectedSport,
      sortDate,
      sortLabelendDate,
      sortLabelstartDate,
      sortLabelid,
    } = this.state;
    const sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "sponsorDate"
        ? sortLabelstartDate
        : sortLabelendDate;
    this.setState({
      filterEndDate: date
        ? moment(date).tz(timezone).format("YYYY-MM-DD")
        : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAllSponsoredOdds(
        offset,
        sortType,
        sortData,
        SelectedSport,
        sortDate,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null
      );
    }
  };

  clearStartDate = () => {
    const {
      offset,
      sortType,
      SelectedSport,
      filterEndDate,
      sortLabelendDate,
      sortLabelstartDate,
      sortLabelid,
    } = this.state;
    const sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "sponsorDate"
        ? sortLabelstartDate
        : sortLabelendDate;
    this.setState({
      sortDate: null,
      startDateOpen: false,
    });
    this.fetchAllSponsoredOdds(
      offset,
      sortType,
      sortData,
      SelectedSport,
      null,
      filterEndDate
    );
  };
  clearEndDate = () => {
    const {
      offset,
      sortType,
      SelectedSport,
      sortDate,
      sortLabelendDate,
      sortLabelstartDate,
      sortLabelid,
    } = this.state;
    const sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "sponsorDate"
        ? sortLabelstartDate
        : sortLabelendDate;
    this.setState({
      filterEndDate: null,
      endDateOpen: false,
    });
    this.fetchAllSponsoredOdds(
      offset,
      sortType,
      sortData,
      SelectedSport,
      sortDate,
      null
    );
  };
  fetchProviders = async () => {
    this.setState({ isLoading: true });

    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeepers/`
      );
      if (status === 200) {
        let activeProvide = data?.result?.filter((item) => {
          return item?.status === "active";
        });
        let newdata = [];
        let providerData = activeProvide?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        this.setState({
          isLoading: false,
          providersDetails: newdata?.sort((a, b) => {
            return a.label > b.label ? 1 : -1;
          }),
        });
      }
    } catch (err) {
      this.setState({ isLoading: false });
    }
  };

  async fetchAllSponsoredOdds(
    page,
    type,
    order,
    sportId,
    sortDate,
    filterEndDate
  ) {
    let { rowPerPage, SelectedSport } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/sponsors/get?limit=${rowPerPage}&offset=${page}&key=${type}&order=${
          order ? "ASC" : "DESC"
        }&SportId=${sportId === 0 ? "" : sportId}&startDate=${
          sortDate === null ? "" : sortDate
        }&endDate=${
          filterEndDate === null ? "" : filterEndDate
        }&timeZone=${timezone}`
      );
      if (status === 200) {
        this.fetchAllCsvData(
          data?.result?.count,
          type,
          order,
          sportId,
          sortDate,
          filterEndDate
        );
        this.setState({
          sponsoredList: data?.result?.rows,
          isLoading: false,
          sponsoredCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }
  fetchAllCsvData = async (
    count,
    type,
    order,
    sportId,
    sortDate,
    filterEndDate
  ) => {
    let { SelectedSport } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/sponsors/get?limit=${count ? count : 20}&offset=&key=${type}&order=${
          order ? "ASC" : "DESC"
        }&SportId=${sportId === 0 ? "" : sportId}&startDate=${
          sortDate === null ? "" : sortDate
        }&endDate=${
          filterEndDate === null ? "" : filterEndDate
        }&timeZone=${timezone}`
      );
      if (status === 200) {
        const csvData = data?.result?.rows?.map((item) => {
          return {
            Id: item?.id,
            ProviderName: item?.BookKeeper?.name,
            SportName: JSON.stringify(
              item?.SponsorSports?.map((obj) => {
                return obj?.Sport?.sportName;
              }).toString()
            ),
            StartDate: item?.sponsorDate
              ? moment(item?.sponsorDate).tz(timezone).format("YYYY/MM/DD")
              : "-",
            EndDate: item?.sponsorEndDate
              ? moment(item?.sponsorEndDate).tz(timezone).format("YYYY/MM/DD")
              : "-",
            Status: item?.status,
          };
        });
        this.setState({
          csvListData: csvData,
        });
      } else {
      }
    } catch {}
  };
  handalValidate = () => {
    let { sponsoredValues, isEditMode } = this.state;

    let flag = true;
    if (
      sponsoredValues?.startDate === "" ||
      sponsoredValues?.startDate === null
    ) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    if (sponsoredValues?.endDate === "" || sponsoredValues?.endDate === null) {
      flag = false;
      this.setState({
        errorEndDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorEndDate: "",
      });
    }
    if (isEditMode === true) {
      if (sponsoredValues?.selectprovideredit === "") {
        flag = false;
        this.setState({
          errorselectProvideredit: "This field is mandatory",
        });
      } else {
        this.setState({
          errorselectProvideredit: "",
        });
      }
    } else {
      if (sponsoredValues?.selectedprovider === undefined) {
        flag = false;
        this.setState({
          errorselectProvider: "This field is mandatory",
        });
      } else {
        this.setState({
          errorselectProvider: "",
        });
      }
    }
    if (sponsoredValues?.selectedprovider?.length === 0) {
      flag = false;
      this.setState({
        errorselectSportId: "This field is mandatory",
      });
    } else {
      this.setState({
        errorselectSportId: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    const {
      sortLabelendDate,
      sortLabelstartDate,
      sortType,
      sortLabelid,
      sortDate,
      filterEndDate,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "sponsorDate"
        ? sortLabelstartDate
        : sortLabelendDate;
    if (this.handalValidate()) {
      const { sponsoredValues } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      const providerId = sponsoredValues?.selectedprovider?.map((item) => {
        return item?.value;
      });
      const sportId = sponsoredValues?.selectedSportId?.map((item) => {
        return item?.value;
      });

      const payload = {
        sponsorDate: moment(sponsoredValues?.startDate)
          .tz(timezone)
          .format("YYYY-MM-DD"),
        sponsorEndDate: moment(sponsoredValues?.endDate)
          .tz(timezone)
          .format("YYYY-MM-DD"),
        bookKeepersId: providerId,
        SportId: sportId,
      };

      try {
        const { status, data } = await axiosInstance.post(
          `/sponsors/create?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllSponsoredOdds(
            this.state.offset,
            sortType,
            sortData,
            this.state.SelectedSport,
            sortDate,
            filterEndDate
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const {
      sortLabelendDate,
      sortLabelstartDate,
      sortType,
      sortLabelid,
      sponsoredValues,
      sortDate,
      filterEndDate,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "sponsorDate"
        ? sortLabelstartDate
        : sortLabelendDate;

    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const sportId = sponsoredValues?.selectedSportId?.map((item) => {
        return item?.value;
      });
      const payload = {
        sponsorDate: moment(this.state?.sponsoredValues?.startDate)
          .tz(timezone)
          .format("YYYY-MM-DD"),
        sponsorEndDate: moment(this.state?.sponsoredValues?.endDate)
          .tz(timezone)
          .format("YYYY-MM-DD"),
        SportId: sportId,
        bookKeepersId: this.state?.sponsoredValues?.selectprovideredit,
      };

      try {
        const { status, data } = await axiosInstance.put(
          `/sponsors/update/${this.state.sponsoredValues?.id}?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllSponsoredOdds(
            this.state.offset,
            sortType,
            sortData,
            this.state.SelectedSport,
            sortDate,
            filterEndDate
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Sport",
        value: 0,
      };

      let alldata = [alldatas, ...newdata];

      // let pushData = newdata.push(alldatas);
      // let sortData = newdata?.sort((a, b) => {
      //   return a.value > b.value ? 1 : -1;
      // });
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        isLoading: false,
        sportsOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
      });
    }
  }

  // async fetchAllSportType() {
  //   const { status, data } = await axiosInstance.get(URLS.sportType);
  //   if (status === 200) {
  //     this.setState({ allSportsType: data.result });
  //   }
  // }

  //   getSportType = (id) => {
  //     let { allSportsType } = this.state;
  //     let sportType = allSportsType
  //       .filter((obj) => obj.id === id)
  //       .map((object) => object.sportType);
  //     return sportType;
  //   };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorStartDate: "",
      errorEndDate: "",
      errorselectProvider: "",
      errorselectProvideredit: "",
      errorselectSportId: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    let sports = "";
    if (type === "edit") {
      sports = item?.SponsorSports?.map((obj) => {
        return {
          value: obj?.Sport?.id,
          label: obj?.Sport?.sportName,
        };
      });
      this.setState({
        sponsoredValues: {
          startDate: item?.sponsorDate,
          endDate: item?.sponsorEndDate,
          selectprovideredit: item?.bookKeepersId,
          selectedSportId: sports,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        sponsoredValues: {
          startDate: new Date(),
          endDate: new Date(),
          selectprovider: "",
          id: "",
          selectedSportId: [],
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const {
      sortLabelendDate,
      sortLabelstartDate,
      sortType,
      sortLabelid,
      sortDate,
      filterEndDate,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortLabelid
        : sortType === "sponsorDate"
        ? sortLabelstartDate
        : sortLabelendDate;
    try {
      const { status, data } = await axiosInstance.delete(
        `/sponsors/delete/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllSponsoredOdds(
            this.state.offset,
            sortType,
            sortData,
            this.state.SelectedSport,
            sortDate,
            filterEndDate
          );
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, sponsoredList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  sortLabelHandler = (type) => {
    const { sortDate, filterEndDate } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchAllSponsoredOdds(
        0,
        type,
        !this.state.sortLabelid,
        this.state.SelectedSport,
        sortDate,
        filterEndDate
      );
      this.setState({
        sortLabelid: !this.state.sortLabelid,
        sortLabelstartDate: true,
        sortLabelendDate: true,
        currentPage: 1,
      });
    } else if (type === "sponsorDate") {
      this.fetchAllSponsoredOdds(
        0,
        type,
        !this.state.sortLabelstartDate,
        this.state.SelectedSport,
        sortDate,
        filterEndDate
      );
      this.setState({
        sortLabelstartDate: !this.state.sortLabelstartDate,
        sortLabelid: false,
        sortLabelendDate: true,
        currentPage: 1,
      });
    } else {
      this.fetchAllSponsoredOdds(
        0,
        type,
        !this.state.sortLabelendDate,
        this.state.SelectedSport,
        sortDate,
        filterEndDate
      );
      this.setState({
        sortLabelendDate: !this.state.sortLabelendDate,
        sortLabelid: false,
        sortLabelstartDate: true,
        currentPage: 1,
      });
    }
  };

  handlesportchange = async (e) => {
    const { sortDate, filterEndDate } = this.state;
    this.setState({
      SelectedSport: e.value,
      isLoading: true,
    });
    this.fetchAllSponsoredOdds(
      0,
      "id",
      false,
      e.value,
      sortDate,
      filterEndDate
    );
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      sponsoredValues,
      sponsoredList,
      sponsoredCount,
      errorRequire,
      startDate,
      errorEndDate,
      providersDetails,
      errorStartDate,
      errorselectProvider,
      errorselectProvideredit,
      sortLabelid,
      sortLabelstartDate,
      sortLabelendDate,
      sortvalue,
      sortType,
      allSportsType,
      sportsOption,
      errorselectSportId,
      SelectedSport,
      AllSportsOption,
      sortDate,
      filterEndDate,
      csvListData,
    } = this.state;
    const pageNumbers = [];
    const providerId = sponsoredValues?.selectedprovider?.map((item) => {
      return item?.value;
    });

    const sportId = sponsoredValues?.selectedSportId?.map((item) => {
      return item?.value;
    });

    if (sponsoredCount > 0) {
      for (let i = 1; i <= Math.ceil(sponsoredCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Racing
                </Link>
                <Typography className="active_p">Sponsored Odds</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Typography variant="h1" align="left">
                  Sponsored Odds Management
                </Typography>
              </Grid>

              <Grid
                item
                xs={7}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <Select
                  className="React cricket-select sponsored-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Sport"
                  value={AllSportsOption?.find((item) => {
                    return item?.value == SelectedSport;
                  })}
                  isLoading={isLoading}
                  onChange={(e) => this.handlesportchange(e)}
                  options={AllSportsOption}
                />

                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DesktopDatePicker
                    clearable
                    // onClear={() => this.clearStartDate()}
                    // open={this.state.startDateOpen}
                    // onOpen={() => this.setState({ startDateOpen: true })}
                    // onClose={() => this.setState({ startDateOpen: false })}
                    autoOk
                    // disableToolbar
                    // variant="inline"
                    format="dd/MM/yyyy"
                    placeholder="Start Date"
                    margin="normal"
                    id="date-picker-inline"
                    inputVariant="outlined"
                    value={
                      sortDate
                        ? typeof sortDate === "string"
                          ? parseISO(sortDate)
                          : sortDate
                        : null
                    }
                    // onKeyDown={(e) => {
                    //   e.preventDefault();
                    // }}
                    slots={{
                      openPickerIcon: TodayIcon,
                    }}
                    slotProps={{
                      field: {
                        placeholder: "DD/MM/YYYY",
                        clearable: true,
                        // onClear: () => this.clearStartDate(),
                      },
                    }}
                    onChange={(e) => this.handleSortStartDate(e)}
                    KeyboardButtonProps={{
                      "aria-label": "change date",
                    }}
                    className="date-picker-fixture advt-datepicker"
                    style={{ margin: "0px 10px 0px 0px" }}
                  />
                </LocalizationProvider>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DesktopDatePicker
                    clearable
                    // onClear={() => this.clearEndDate()}
                    // open={this.state.endDateOpen}
                    // onOpen={() => this.setState({ endDateOpen: true })}
                    // onClose={() => this.setState({ endDateOpen: false })}
                    autoOk
                    // disableToolbar
                    // variant="inline"
                    format="dd/MM/yyyy"
                    placeholder="End Date"
                    margin="normal"
                    id="date-picker-inline"
                    inputVariant="outlined"
                    value={
                      filterEndDate
                        ? typeof filterEndDate === "string"
                          ? parseISO(filterEndDate)
                          : filterEndDate
                        : null
                    }
                    minDate={sortDate ? parseISO(sortDate) : null}
                    // onKeyDown={(e) => {
                    //   e.preventDefault();
                    // }}
                    slots={{
                      openPickerIcon: TodayIcon,
                    }}
                    slotProps={{
                      field: {
                        placeholder: "DD/MM/YYYY",
                        clearable: true,
                        // onClear: () => this.clearEndDate(),
                      },
                    }}
                    onChange={(e) => this.handleFilterEndDate(e)}
                    KeyboardButtonProps={{
                      "aria-label": "change date",
                    }}
                    className="date-picker-fixture advt-datepicker"
                    style={{ margin: "0px" }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <CSVExport
                  data={csvListData}
                  filename="sponsored_odds_data.csv"
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && sponsoredList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && sponsoredList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell
                        onClick={() => this.sortLabelHandler("id")}
                        style={{ cursor: "pointer" }}
                      >
                        DID
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "id"
                              ? sortLabelid
                                ? "asc"
                                : "desc"
                              : "desc"
                          }
                        />
                      </TableCell>
                      <TableCell>Provider Id</TableCell>
                      <TableCell>providerName </TableCell>
                      <TableCell>Sport Name</TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("sponsorDate")}
                        style={{ cursor: "pointer", width: "10%" }}
                      >
                        Start Date{" "}
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "sponsorDate"
                              ? sortLabelstartDate
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("sponsorEndDate")}
                        style={{ cursor: "pointer" }}
                      >
                        End Date{" "}
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "sponsorEndDate"
                              ? sortLabelendDate
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell>status</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {sponsoredList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.apiProviderId}</TableCell>
                          <TableCell>{item?.BookKeeper?.name}</TableCell>
                          <TableCell>
                            {item?.SponsorSports?.map((obj) => {
                              return obj?.Sport?.sportName;
                            }).toString()}
                          </TableCell>
                          <TableCell>
                            {moment(item?.sponsorDate)
                              .tz(timezone)
                              .format("YYYY/MM/DD")}
                          </TableCell>
                          <TableCell>
                            {moment(item?.sponsorEndDate)
                              .tz(timezone)
                              .format("YYYY/MM/DD")}
                          </TableCell>
                          <TableCell>{item?.status}</TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                            className={
                              sponsoredList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sponsoredList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              sponsoredCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                            className={
                              sponsoredList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sponsoredList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Sponsor" : "Edit Sponsor"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Start Date</label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            // disableToolbar
                            variant="inline"
                            format="yyyy/MM/dd"
                            placeholder="Start Date"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              sponsoredValues?.startDate
                                ? typeof sponsoredValues?.startDate === "string"
                                  ? parseISO(
                                      moment(sponsoredValues?.startDate)
                                        .tz(timezone)
                                        .format("YYYY-MM-DD")
                                    )
                                  : sponsoredValues?.startDate
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                sponsoredValues: {
                                  ...sponsoredValues,
                                  startDate: e,
                                },
                              })
                            }
                            slots={{
                              openPickerIcon: TodayIcon,
                            }}
                            slotProps={{
                              field: {
                                placeholder: "YYYY/MM/DD",
                              },
                            }}
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal"
                          />
                        </LocalizationProvider>
                        {errorStartDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorStartDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">End Date</label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            // disableToolbar
                            variant="inline"
                            format="yyyy/MM/dd"
                            placeholder="End Date"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              sponsoredValues?.endDate
                                ? typeof sponsoredValues?.endDate === "string"
                                  ? parseISO(
                                      moment(sponsoredValues?.endDate)
                                        .tz(timezone)
                                        .format("YYYY-MM-DD")
                                    )
                                  : sponsoredValues?.endDate
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                sponsoredValues: {
                                  ...sponsoredValues,
                                  endDate: e,
                                },
                              })
                            }
                            // onError={(newError) =>

                            // }
                            minDate={
                              sponsoredValues?.startDate
                                ? typeof sponsoredValues?.startDate === "string"
                                  ? parseISO(sponsoredValues?.startDate)
                                  : sponsoredValues?.startDate
                                : null
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal"
                          />
                        </LocalizationProvider>
                        {errorEndDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorEndDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Provider Id</label>
                        {isEditMode === true ? (
                          <>
                            <Select
                              className="React cricket-select sponsored-select-modal"
                              classNamePrefix="select"
                              placeholder="provider id"
                              menuPosition="fixed"
                              value={providersDetails?.find((item) => {
                                return (
                                  item?.value ==
                                  sponsoredValues?.selectprovideredit
                                );
                              })}
                              isLoading={isLoading}
                              onChange={(e) =>
                                this.setState({
                                  sponsoredValues: {
                                    ...sponsoredValues,
                                    selectprovideredit: e.value,
                                  },
                                })
                              }
                              options={providersDetails}
                            />
                            {errorselectProvideredit ? (
                              <p
                                className="errorText"
                                style={{ margin: "0 0 0 0" }}
                              >
                                {errorselectProvideredit}
                              </p>
                            ) : (
                              ""
                            )}
                          </>
                        ) : (
                          <>
                            <Select
                              className="React cricket-select sponsored-select-modal"
                              classNamePrefix="select"
                              placeholder="provider id"
                              menuPosition="fixed"
                              isMulti
                              value={providersDetails?.find((item) => {
                                return (
                                  item?.value ==
                                  sponsoredValues?.selectedprovider
                                );
                              })}
                              isLoading={isLoading}
                              onChange={(e) =>
                                this.setState({
                                  sponsoredValues: {
                                    ...sponsoredValues,
                                    selectedprovider: e,
                                  },
                                })
                              }
                              options={providersDetails}
                              isOptionDisabled={() =>
                                sponsoredValues?.selectedprovider?.length >= 2
                              }
                            />
                            {errorselectProvider ? (
                              <p
                                className="errorText"
                                style={{ margin: "0 0 0 0" }}
                              >
                                {errorselectProvider}
                              </p>
                            ) : (
                              ""
                            )}
                          </>
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Sport Id</label>
                        <Select
                          className="React cricket-select sponsored-select-modal"
                          classNamePrefix="select"
                          placeholder="Sport id"
                          menuPosition="fixed"
                          isMulti
                          value={sponsoredValues?.selectedSportId}
                          isLoading={isLoading}
                          onChange={(e) =>
                            this.setState({
                              sponsoredValues: {
                                ...sponsoredValues,
                                selectedSportId: e,
                              },
                            })
                          }
                          options={sportsOption}
                          // isOptionDisabled={() =>
                          //   sponsoredValues?.selectedprovider?.length >= 2
                          // }
                        />
                        {errorselectSportId ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorselectSportId}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default SponsoredOdds;
