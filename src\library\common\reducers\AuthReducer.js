import { storeConstants } from "../constants/index";

const initialState = {
  isLoggedIn: false,
  token: null,
  editData: [],
  user: null,
};

const AuthReducer = (state = initialState, action) => {
  switch (action.type) {
    case storeConstants.SET_AUTHENTICATION:
      return {
        ...state,
        token: action.payload.token,
        user: action.payload.user,
        isLoggedIn: true,
      };
    case storeConstants.UPDATE_ROW:
      return {
        ...state,
        editData: action.payload,
      };
    case storeConstants.LOGOUT:
      return initialState;
    default:
      return state;
  }
};

export default AuthReducer;
