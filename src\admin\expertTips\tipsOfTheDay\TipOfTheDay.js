import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  TableSortLabel,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
// import "../teamsport.scss";
import "../expertTips.scss";
import moment from "moment-timezone";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import { IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import { identifiers } from "../../../library/common/constants";
import _ from "lodash";
import { MdKeyboardBackspace } from "react-icons/md";
import { fetchFromStorage } from "../../../library/utilities";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class TipsOfTheDay extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isQueryModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      deleteItemData: {},
      isEditMode: false,
      isTipLoader: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      startDate: null,
      tipName: null,
      //   expertTipsValues: {
      //     // startDate: moment(Date()).format("YYYY-MM-DD"),
      //     tipName: "",
      //   },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      selectedTipId: null,
      ExpertTipsList: [],
      TipsListByDate: [],
      isTipOfTheday: null,
      tipOfTheDayData: {},
      ExpertTipsCount: 0,
      providersDetails: [],
      selectedProvider: 0,
      serachValue: "",
      SelectedSport: null,
      AllSportsOption: [],
      SelectedUsers: null,
      AllUserOption: [],
      sortDate: null,
      startDateOpen: false,
      selectDateOpen: false,
      selectedModalSport: null,
      TrackData: [],
      TrackAllRaceData: [],
      TrackRaceData: [],
      BetRunnerData: [],
      EachWayRunnerData: [],
      LayRunnerData: [],
      selectedValues: {},
      SelectedId: "",
      errorStartDate: "",
      errorTipName: "",
      errorWpCreds: "",
      tipsModalDetailsOpen: false,
      tipsModalDetails: {},
      tipsModalDetailsIsLoading: false,
      sortType: "id",
      sortId: false,
      sortEventName: true,
      sortEventDate: true,
      sortSportType: true,
      sortUser: true,
    };
  }

  // handleChange = (raceId, selectedOptions) => {
  //   this.setState((prevState) => ({
  //     selectedValues: {
  //       ...prevState?.selectedValues,
  //       [raceId]: selectedOptions?.map((option) => option?.value),
  //     },
  //   }));
  // };

  handleChange = (raceId, selectedOptions) => {
    this.setState((prevState) => {
      const updatedSelectedValues = { ...prevState?.selectedValues };
      if (selectedOptions && selectedOptions?.length > 0) {
        updatedSelectedValues[raceId] = selectedOptions?.map(
          (option) => option?.value
        );
      } else {
        delete updatedSelectedValues[raceId];
      }
      return {
        selectedValues: updatedSelectedValues,
      };
    });
  };
  // moment(Date()).format("YYYY-MM-DD")
  componentDidMount() {
    this.fetchAllEvent(
      0,
      this.state?.sortDate,
      "",
      this.state?.sortType,
      false,
      true
    );
    this.handleWpError();
  }

  componentDidUpdate(prevProps, prevState) {
    let {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      search,
    } = this.state;
    if (prevState.offset !== offset) {
      this.fetchAllEvent(offset, sortDate, serachValue, sortType, false, true);
    }
    if (prevProps?.match?.path !== this.props?.match?.path) {
      this.fetchAllEventForCreate(moment(Date()).format("YYYY-MM-DD"));
      this.setState({
        offset: 0,
        currentPage: 1,
      });
    }
  }
  handleWpError = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/expertTips/errorHandled`
      );
      if (status === 200) {
        this.setState({
          errorWpCreds: "",
        });
      } else {
      }
    } catch (err) {
      this.setState({
        errorWpCreds: err?.response?.data?.message,
      });
      this.setActionMessage(
        true,
        "Error",
        err?.response?.data?.message,
        "wordpresserror"
      );
    }
  };
  fetchAllEvent = async (page, date, search, type, order, isTipDay) => {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = `/expertTips/getAllTips?tipsOfTheDays=${isTipDay}&limit=${rowPerPage}&offset=${page}&timezone=${timezone}&date=${
        date ? date : ""
      }&search=${search}&orderBy=${type ? type : ""}&sort=${
        order ? "ASC" : "DESC"
      }`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          ExpertTipsList: data?.result?.rows,
          isLoading: false,
          ExpertTipsCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchAllEventForCreate = async (date) => {
    let { rowPerPage, expertTipsValues } = this.state;
    this.setState({
      TipsListByDate: [],
      tipName: null,
    });
    this.setState({ isTipLoader: true });
    try {
      const passApi = `/expertTips/getDropDown/tips?timezone=${timezone}&date=${
        date ? date : ""
      }`;
      // const passApi = `/expertTips/getAllTips?limit=${rowPerPage}&offset=${page}&timezone=${timezone}&date=${
      //   date ? date : ""
      // }`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let newdata = [];
        let track = data?.result?.map((item) => {
          newdata.push({
            label: item?.Event?.eventName,
            // +
            // " - " +
            // (item?.User?.firstName + " " + item?.User?.lastName)
            value: item?.id,
            isTipOfTheDay: item?.isTipOfTheDay,
          });
        });
        const hasTipOfTheDay = data?.result?.some(
          (item) => item.isTipOfTheDay === true
        );
        const tipOfTheDayData = data?.result?.filter(
          (item) => item.isTipOfTheDay === true
        );
        this.setState({
          isTipLoader: false,
          TipsListByDate: newdata,
          isTipOfTheday: hasTipOfTheDay,
          tipOfTheDayData: tipOfTheDayData,
        });
      } else {
        this.setState({
          isTipLoader: false,
        });
      }
    } catch {
      this.setState({
        isTipLoader: false,
      });
    }
  };

  fetchSingleEvent = async (id) => {
    this.setState({ tipsModalDetailsIsLoading: true });
    try {
      const passApi = `/expertTips/admin/get/${id}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          tipsModalDetailsIsLoading: false,
        });
        let newdata = [];
        let TrackAllRaceObject = (data?.result?.races || []).reduce(
          (acc, item) => {
            const key = item?.RaceId;
            const value = item?.runners?.map((option) => option?.id) || [];
            if (key !== undefined) {
              acc[key] = value;
            }
            return acc;
          },
          {}
        );

        this.setState({
          selectedValues: TrackAllRaceObject,
          tipsModalDetails: data?.result,
        });
      } else {
        this.setState({
          isLoading: false,
          tipsModalDetailsIsLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
        tipsModalDetailsIsLoading: false,
      });
    }
  };

  handleValidate = () => {
    let { startDate, tipName } = this.state;
    let flag = true;
    if (startDate === "" || startDate === null) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    if (tipName?.trim() === "" || tipName === null) {
      flag = false;
      this.setState({
        errorTipName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTipName: "",
      });
    }
    return flag;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };
  toggleQueryModal = () => {
    this.setState({
      isQueryModalOpen: !this.state.isQueryModalOpen,
      // itemToDelete: null,
    });
  };

  inputModal = (item, type) => () => {
    this.setState({
      isInputModalOpen: true,
    });
    if (type === "edit") {
      // this.fetchSingleEvent(item?.id);
      this.fetchAllEventForCreate(
        moment(item?.Event?.eventDate).format("YYYY-MM-DD")
      );
      this.setState({
        isEditMode: true,
        startDate: moment(item?.Event?.eventDate).format("YYYY-MM-DD"),
        tipName: item?.Event?.eventName,
        // +
        // " - " +
        // (item?.User?.firstName + " " + item?.User?.lastName)
        selectedTipId: item?.id,
      });
    } else {
      this.setState({
        isEditMode: false,
        startDate: null,
        tipName: null,
      });
    }
  };

  tipsDetailsModalOpen = (item) => {
    this.setState({ tipsModalDetailsOpen: true });
    this.fetchSingleEvent(item?.id);
  };

  toggleTipsDetailsModalOpen = () => {
    this.setState({ tipsModalDetailsOpen: false, tipsModalDetails: {} });
  };

  setActionMessage = (display = false, type = "", message = "", isWperror) => {
    const clearMessageBox = () =>
      this.setState({
        messageBox: { display: false, type: "", message: "" },
      });
    this.setState(
      {
        messageBox: { display, type, message },
      },
      isWperror
        ? () => {}
        : () => {
            setTimeout(clearMessageBox, 3000);
          }
    );
  };

  setItemToDelete = (item) => () => {
    this.setState({
      itemToDelete: item?.id,
      isModalOpen: true,
      deleteItemData: item,
    });
  };
  setQueryModalOpen = (id) => () => {
    this.setState({ isQueryModalOpen: true });
  };

  deleteItem = async () => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      SelectedUsers,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      selectedTipId,
      startDate,
      itemToDelete,
      deleteItemData,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    try {
      const passApi = `/expertTips/mark/tipOfTheDay?tipId=${itemToDelete}&date=${moment(
        deleteItemData?.Event?.eventDate
      ).format("YYYY-MM-DD")}&timezone=${timezone}&isMark=false`;
      const { status, data } = await axiosInstance.put(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvent(
            offset,
            sortDate,
            serachValue,
            sortType,
            sortData,
            true
          );
        });
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, ExpertTipsList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  clearSortDate = () => {
    const {
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
    } = this.state;
    this.setState({
      sortDate: null,
      startDateOpen: false,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(0, null, serachValue, sortType, sortData, true);
  };
  clearSelectDate = () => {
    this.setState({
      startDate: null,
      selectDateOpen: false,
      tipName: null,
    });
  };

  handleSortStartDate = (date) => {
    const {
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAllEvent(
        0,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        serachValue,
        sortType,
        sortData,
        true
      );
    }
  };

  handleSelectedStartDate = (date) => {
    this.setState({
      startDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
      tipName: null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAllEventForCreate(
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null
      );
    }
  };

  handleSave = async () => {
    const {
      startDate,
      offset,
      sortDate,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      selectedTipId,
    } = this.state;
    if (this.handleValidate()) {
      this.setState({
        isLoading: true,
        isEditMode: false,
        isQueryModalOpen: false,
      });
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      try {
        const { status, data } = await axiosInstance.put(
          `/expertTips/mark/tipOfTheDay?tipId=${selectedTipId}&date=${startDate}&timezone=${timezone}&isMark=true`
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEvent(
            offset,
            sortDate,
            serachValue,
            sortType,
            sortData,
            true
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const {
      expertTipsValues,
      selectedModalSport,
      TrackAllRaceData,
      selectedValues,
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      selectedTipId,
      startDate,
    } = this.state;
    if (this.handleValidate()) {
      this.setState({
        isLoading: true,
        isEditMode: true,
        isQueryModalOpen: false,
      });
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      try {
        const { status, data } = await axiosInstance.put(
          `/expertTips/mark/tipOfTheDay?tipId=${selectedTipId}&date=${startDate}&timezone=${timezone}&isMark=true`
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEvent(
            offset,
            sortDate,
            serachValue,
            sortType,
            sortData,
            true
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleClearClick = () => {
    const { sortDate, sortType } = this.state;
    this.setState({ serachValue: "", currentPage: 1 });
    this.fetchAllEvent(0, sortDate, "", sortType, false, true);
  };

  sortLabelHandler = (type) => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchAllEvent(offset, sortDate, serachValue, type, !sortId, true);
      this.setState({
        sortId: !sortId,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "eventName") {
      this.fetchAllEvent(
        offset,
        sortDate,
        serachValue,
        type,
        !sortEventName,
        true
      );
      this.setState({
        sortId: false,
        sortEventName: !sortEventName,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "eventDate") {
      this.fetchAllEvent(
        offset,
        sortDate,
        serachValue,
        type,
        !sortEventDate,
        true
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: !sortEventDate,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "SportId") {
      this.fetchAllEvent(
        offset,
        sortDate,
        serachValue,
        type,
        !sortSportType,
        true
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: !sortSportType,
        sortUser: true,
        currentPage: 1,
      });
    } else {
      this.fetchAllEvent(offset, sortDate, serachValue, type, !sortUser, true);
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: !sortUser,
        currentPage: 1,
      });
    }
  };

  backToNavigatePage = () => {
    this.props.navigate(`/expert-tips`);
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorDate: "",
      errorName: "",
      isLoading: false,
      errorStartDate: "",
      errorTipName: "",
    });
  };

  handleKeyDown = (event) => {
    var { sortDate, serachValue, sortType } = this.state;
    if (event.key === "Enter") {
      this.fetchAllEvent(0, sortDate, serachValue, sortType, false, true);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      isQueryModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      ExpertTipsList,
      TipsListByDate,
      ExpertTipsCount,
      serachValue,
      sortDate,
      startDateOpen,
      startDate,
      tipName,
      errorStartDate,
      errorTipName,
      tipsModalDetailsOpen,
      tipsModalDetails,
      tipsModalDetailsIsLoading,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      tipOfTheDayData,
      selectedTipId,
      isTipLoader,
      selectDateOpen,
      isTipOfTheday,
    } = this.state;
    const pageNumbers = [];
    if (ExpertTipsCount > 0) {
      for (let i = 1; i <= Math.ceil(ExpertTipsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    const user = fetchFromStorage(identifiers.user);

    return (
      <>
        {/* {!isInputModalOpen ? ( */}
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox?.display && (
              <ActionMessage
                message={messageBox?.message}
                type={messageBox?.type}
                styleClass={messageBox?.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit" to="/expert-tips">
                  Expert Tips
                </Link>

                <Typography className="active_p">Tip Of The Day</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    columnGap: "20px",
                  }}
                >
                  <Button
                    className="admin-btn-margin admin-btn-back"
                    onClick={this.backToNavigatePage}
                  >
                    <MdKeyboardBackspace />
                  </Button>
                  <Typography variant="h1" align="left">
                    Tip Of The Day
                  </Typography>
                </Box>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap"
              >
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DesktopDatePicker
                    // open={startDateOpen}
                    clearable
                    // onClear={() => this.clearSortDate()}
                    // onOpen={() => this.setState({ startDateOpen: true })}
                    // onClose={() => this.setState({ startDateOpen: false })}
                    autoOk
                    // disableToolbar
                    // variant="inline"
                    format="dd/MM/yyyy"
                    placeholder="Event Date"
                    margin="normal"
                    id="date-picker-inline"
                    inputVariant="outlined"
                    value={sortDate ? parseISO(sortDate) : null}
                    slots={{
                      openPickerIcon: TodayIcon,
                    }}
                    slotProps={{
                      field: {
                        placeholder: "DD/MM/YYYY",
                        clearable: true,
                        // onClear: () => this.clearSortDate(),
                      },
                    }}
                    onChange={(e) => this.handleSortStartDate(e)}
                    KeyboardButtonProps={{
                      "aria-label": "change date",
                    }}
                    className="date-picker-fixture expert-tips-date-picker"
                    style={{ margin: "0px 10px 0px 0px", width: "24%" }}
                  />
                </LocalizationProvider>
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="event-search"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={serachValue}
                  onChange={(e) => {
                    this.setState({ serachValue: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {serachValue && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllEvent(
                      0,
                      sortDate,
                      serachValue,
                      sortType,
                      false,
                      true
                    );
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  className={
                    Boolean(this.state.errorWpCreds) ? "disabled-btn" : ""
                  }
                  onClick={this.inputModal(null, "create")}
                  disabled={Boolean(this.state.errorWpCreds)}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && ExpertTipsList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && ExpertTipsList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table
                  className="listTable market-error-table"
                  aria-label="simple table"
                >
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell
                        onClick={() => this.sortLabelHandler("id")}
                        style={{ cursor: "pointer" }}
                      >
                        DID
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "id"
                              ? sortId
                                ? "asc"
                                : "desc"
                              : "desc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("eventName")}
                        style={{ cursor: "pointer" }}
                      >
                        Event Name
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "eventName"
                              ? sortEventName
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("eventDate")}
                        style={{ cursor: "pointer" }}
                      >
                        Event Date
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "eventDate"
                              ? sortEventDate
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      <TableCell
                        onClick={() => this.sortLabelHandler("SportId")}
                        style={{ cursor: "pointer" }}
                      >
                        Sport Type
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "SportId"
                              ? sortSportType
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell>
                      {user?.role !== "expertTip" ? (
                        <TableCell
                          onClick={() => this.sortLabelHandler("UserId")}
                          style={{ cursor: "pointer" }}
                        >
                          User
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "UserId"
                                ? sortUser
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                      ) : (
                        <></>
                      )}
                      <TableCell>Tips Details</TableCell>
                      <TableCell style={{ width: "15%" }}>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {ExpertTipsList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell>{item?.id} </TableCell>
                          <TableCell>{item?.Event?.eventName}</TableCell>
                          <TableCell>
                            {moment(item?.Event?.eventDate).format(
                              "DD/MM/YYYY hh:mm:ss a"
                            )}
                          </TableCell>
                          <TableCell>{item?.Sport?.sportName}</TableCell>
                          {user?.role !== "expertTip" ? (
                            <TableCell>
                              {item?.User?.firstName +
                                " " +
                                item?.User?.lastName}
                            </TableCell>
                          ) : (
                            <></>
                          )}
                          <TableCell>
                            <Button
                              className="table-btn"
                              variant="contained"
                              style={{
                                fontSize: "14px",
                                backgroundColor: "#4455C7",
                                color: "#fff",
                                fontWeight: "400",
                                textTransform: "none",
                                width: "max-content",
                              }}
                              onClick={() => {
                                this.tipsDetailsModalOpen(item);
                              }}
                            >
                              Tips Details
                            </Button>
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className={`table-btn edit-btn ${
                                moment(item?.Event?.eventDate).isBefore(
                                  moment(),
                                  "day" || Boolean(this.state.errorWpCreds)
                                )
                                  ? "disabled-btn"
                                  : ""
                              }`}
                              disabled={
                                moment(item?.Event?.eventDate).isBefore(
                                  moment(),
                                  "day"
                                ) || Boolean(this.state.errorWpCreds)
                              }
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item)}
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className={
                                Boolean(this.state.errorWpCreds)
                                  ? "disabled-btn table-btn delete-btn"
                                  : "table-btn delete-btn"
                              }
                              disabled={Boolean(this.state.errorWpCreds)}
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              ExpertTipsCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />
            <ShowModal
              isModalOpen={isQueryModalOpen}
              onClose={this.toggleQueryModal}
              Content={`There is already a Tip Of The Day (${
                tipOfTheDayData[0]?.Event?.eventName +
                " - " +
                (tipOfTheDayData[0]?.User?.firstName +
                  " " +
                  tipOfTheDayData[0]?.User?.lastName)
              }) for this date! Do you want to change?`}
              onOkayLabel="Yes"
              onOkay={isEditMode ? this.handleUpdate : this.handleSave}
              onCancel={this.toggleQueryModal}
            />
          </Grid>
        </Grid>
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={this.toggleInputModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {!isEditMode ? "Create Tip Of The Day" : "Edit Tip Of The Day"}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleInputModal}
            />
            <Grid container className="page-content adminLogin text-left">
              <Grid item xs={12} className="pageWrapper api-provider">
                <Grid container>
                  <Grid
                    item
                    xs={12}
                    className="teamsport-textfield date-time-picker-wrap teamsport-text"
                    style={{
                      marginTop: "15px",
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <label className="modal-label">
                      {" "}
                      Select Date <span className="color-red">*</span>{" "}
                    </label>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DesktopDatePicker
                        // open={selectDateOpen}
                        clearable
                        // onClear={() => this.clearSelectDate()}
                        // onOpen={() => this.setState({ selectDateOpen: true })}
                        // onClose={() => this.setState({ selectDateOpen: false })}
                        autoOk
                        // disableToolbar
                        // variant="inline"
                        placeholder="YYYY/MM/DD"
                        format="yyyy/MM/dd"
                        margin="normal"
                        id="date-picker-inline"
                        inputVariant="outlined"
                        slots={{
                          openPickerIcon: TodayIcon,
                        }}
                        slotProps={{
                          field: {
                            placeholder: "YYYY/MM/DD",
                            clearable: true,
                            // onClear: () => this.clearSelectDate(),
                          },
                        }}
                        value={startDate ? parseISO(startDate) : null}
                        onChange={(e) => {
                          this.handleSelectedStartDate(e);
                        }}
                        KeyboardButtonProps={{
                          "aria-label": "change date",
                        }}
                        className="date-picker-fixture expert-tips-date-picker date-time-picker"
                        disablePast={true}
                        // style={{ margin: "0px 10px 0px 0px", width: "24%" }}
                      />
                    </LocalizationProvider>
                    {errorStartDate ? (
                      <p className="errorText" style={{ margin: "4px 0 0 0" }}>
                        {errorStartDate}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    className="teamsport-textfield national-select teamsport-text"
                    style={{
                      marginTop: "15px",
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <label className="modal-label">
                      {" "}
                      Tip Name <span className="color-red">*</span>
                    </label>
                    <Select
                      className="React teamsport-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      placeholder="Select Tip"
                      isDisabled={
                        TipsListByDate?.length === 0 ||
                        // TipsListByDate?.length === 1 ||
                        startDate === null
                      }
                      //   onMenuScrollToBottom={(e) =>
                      //     this.handleOnScrollBottomClient(e)
                      //   }
                      //   onInputChange={(e) => this.handleClientInputChange(0, e)}
                      //   isLoading={isClientLoading}
                      //   value={
                      //     campaingValues?.clientId &&
                      //     (isClientSearch
                      //       ? searchClientData?.find((item) => {
                      //           return item?.value == campaingValues?.clientId;
                      //         })
                      //       : clientOptionsData?.find((item) => {
                      //           return item?.value == campaingValues?.clientId;
                      //         }))
                      //   }
                      onChange={(e) =>
                        this.setState({
                          tipName: e?.label,
                          selectedTipId: e?.value,
                        })
                      }
                      value={
                        tipName &&
                        TipsListByDate &&
                        TipsListByDate?.length > 0 &&
                        TipsListByDate?.find((item) => {
                          return item?.label == tipName;
                        })
                      }
                      options={
                        TipsListByDate &&
                        TipsListByDate?.length > 0 &&
                        TipsListByDate
                      }
                    />
                    {errorTipName ? (
                      <p className="errorText" style={{ margin: "4px 0 0 0" }}>
                        {errorTipName}
                      </p>
                    ) : (
                      ""
                    )}
                    {!isTipLoader &&
                    startDate &&
                    TipsListByDate &&
                    TipsListByDate?.length === 0 ? (
                      <p className="errorText" style={{ margin: "4px 0 0 0" }}>
                        No data available. Select another date!
                      </p>
                    ) : (
                      ""
                    )}
                    {!isEditMode &&
                    startDate &&
                    TipsListByDate &&
                    !tipName &&
                    TipsListByDate?.length === 1 &&
                    TipsListByDate?.[0]?.isTipOfTheDay === true ? (
                      <p
                        className="errorText color-yellow"
                        style={{ margin: "4px 0 0 0" }}
                      >
                        Available Tip is already the Tip Of The Day! Choose
                        another Date!
                      </p>
                    ) : (
                      ""
                    )}
                    {startDate &&
                    tipName &&
                    tipOfTheDayData &&
                    // tipOfTheDayData?.length > 1 &&
                    tipOfTheDayData?.[0]?.id === selectedTipId ? (
                      <p
                        className="errorText color-yellow"
                        style={{ margin: "4px 0 0 0" }}
                      >
                        This Tip is already a Tip Of The Day! Kindly choose
                        another Tip to change.
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      {!isEditMode ? (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          // onClick={this.handleSave}
                          onClick={
                            isTipOfTheday === true
                              ? this.setQueryModalOpen()
                              : this.handleSave
                          }
                          color="primary"
                          value={!isLoading ? "Save" : "Loading..."}
                          disabled={
                            isLoading ||
                            tipOfTheDayData?.[0]?.id === selectedTipId
                          }
                          // disabled={isLoading || TipsListByDate?.length === 1}
                        />
                      ) : (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          // onClick={this.handleUpdate}
                          onClick={
                            isTipOfTheday === true
                              ? this.setQueryModalOpen()
                              : this.handleUpdate
                          }
                          color="secondary"
                          value={!isLoading ? "Update" : "Loading..."}
                          disabled={
                            isLoading ||
                            tipOfTheDayData?.[0]?.id === selectedTipId
                          }
                          // disabled={isLoading || TipsListByDate?.length === 1}
                        />
                      )}

                      <ButtonComponent
                        onClick={this.toggleInputModal}
                        className="mr-lr-30"
                        value="Back"
                      />
                    </div>
                    {/* {createError ? (
                    <p
                      className="errorText"
                      style={{ margin: "0px 0 0 0", width: "300px" }}
                    >
                      {createError}
                    </p>
                  ) : (
                    ""
                  )} */}
                  </Grid>
                </Grid>
                {/* </Paper> */}
              </Grid>
            </Grid>
          </div>
        </Modal>
        <Modal
          className="modal modal-input tips-modal-details"
          open={tipsModalDetailsOpen}
          onClose={this.toggleTipsDetailsModalOpen}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">
              {tipsModalDetails?.tips?.Event?.eventName} Tips Details
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleTipsDetailsModalOpen}
            />
            {tipsModalDetailsIsLoading ? (
              <Box className="modal-loader">
                <Loader />
              </Box>
            ) : (
              <Box className="tips-details">
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Key Comments :
                  </Typography>
                  <Typography
                    className="details-para"
                    dangerouslySetInnerHTML={{
                      __html: tipsModalDetails?.tips?.keyComment,
                    }}
                  ></Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Bet Comments :
                  </Typography>
                  <Typography
                    className="details-para"
                    dangerouslySetInnerHTML={{
                      __html: tipsModalDetails?.tips?.betComment,
                    }}
                  ></Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.betPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">Bet Race :</Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.betRace?.raceNumber &&
                    tipsModalDetails?.tips?.betRace?.raceName
                      ? "R" +
                        tipsModalDetails?.tips?.betRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.betRace?.raceName
                      : ""}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.betParticipant?.runnerNumber &&
                    tipsModalDetails?.tips?.betParticipant?.animal?.name &&
                    tipsModalDetails?.tips?.betParticipant?.barrierNumber
                      ? tipsModalDetails?.tips?.betParticipant?.runnerNumber +
                        "." +
                        " " +
                        tipsModalDetails?.tips?.betParticipant?.animal?.name +
                        " " +
                        "(" +
                        tipsModalDetails?.tips?.betParticipant?.barrierNumber +
                        ")"
                      : ""}
                  </Typography>
                </Box>

                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Each Way Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.wayComment}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Each Way Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.wayPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Each Way Race :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.wayRace?.raceNumber &&
                    tipsModalDetails?.tips?.wayRace?.raceName
                      ? "R" +
                        tipsModalDetails?.tips?.wayRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.wayRace?.raceName
                      : ""}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Each Way Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.wayParticipant?.runnerNumber &&
                    tipsModalDetails?.tips?.wayParticipant?.animal?.name &&
                    tipsModalDetails?.tips?.wayParticipant?.barrierNumber
                      ? tipsModalDetails?.tips?.wayParticipant?.runnerNumber +
                        "." +
                        " " +
                        tipsModalDetails?.tips?.wayParticipant?.animal?.name +
                        " " +
                        "(" +
                        tipsModalDetails?.tips?.wayParticipant?.barrierNumber +
                        ")"
                      : ""}
                  </Typography>
                </Box>

                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Lay Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.layComment}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Lay Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.layPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Lay Race :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.layRace?.raceNumber &&
                    tipsModalDetails?.tips?.layRace?.raceName
                      ? "R" +
                        tipsModalDetails?.tips?.layRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.layRace?.raceName
                      : ""}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Lay Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.layParticipant?.runnerNumber &&
                    tipsModalDetails?.tips?.layParticipant?.animal?.name &&
                    tipsModalDetails?.tips?.layParticipant?.barrierNumber
                      ? tipsModalDetails?.tips?.layParticipant?.runnerNumber +
                        "." +
                        " " +
                        tipsModalDetails?.tips?.layParticipant?.animal?.name +
                        " " +
                        "(" +
                        tipsModalDetails?.tips?.layParticipant?.barrierNumber +
                        ")"
                      : ""}
                  </Typography>
                </Box>

                {tipsModalDetails?.races?.map((item, index) => {
                  return (
                    <>
                      <Box
                        className="d-flex align-item-baseline col-35 mb-18 details"
                        key={index}
                      >
                        <Typography className="detsils-header">
                          {"Race " + item?.raceNumber}:
                        </Typography>
                        <Box className="w-60">
                          {item?.runners?.map((obj) => (
                            <Typography className="details-para">
                              {obj?.runnerNumber}
                              {"."} {obj?.animal?.name} ({obj?.barrierNumber})
                            </Typography>
                          ))}
                        </Box>
                      </Box>
                    </>
                  );
                })}
              </Box>
            )}
            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    onClick={this.toggleTipsDetailsModalOpen}
                    // className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
      </>
    );
  }
}
export default TipsOfTheDay;
