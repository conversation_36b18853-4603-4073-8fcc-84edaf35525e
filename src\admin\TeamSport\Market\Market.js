import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import Select from "react-select";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";

import "../teamsport.scss";
import CreateMarketVariation from "./createMarketVariation/CreateMarketVariation";
const IsTeamData = [
  {
    label: "Yes",
    value: true,
  },
  {
    label: "No",
    value: false,
  },
];
const MaketTypeOption = [
  {
    label: "team",
    value: 1,
  },
  {
    label: "label",
    value: 2,
  },
  {
    label: "player",
    value: 3,
  },
];
class Market extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      marketValues: {
        name: "",
        displayName: "",
        id: "",
        type: "",
        isPrimary: "",
      },
      Marketlist: [],
      MarketCount: 0,
      errorName: "",
      errorDisplayName: "",
      errorIsPrimary: "",
      errorMarketType: "",
      isVariationModalOpen: false,
      search: "",
    };
  }

  componentDidMount() {
    this.fetchAllMarket(0, "");
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllMarket(this.state.offset, this.state.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllMarket(0, "");
      this.setState({
        offset: 0,
        currentPage: 1,
        Marketlist: [],
        search: "",
      });
    }
  }

  async fetchAllMarket(page, searchvalue) {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/market?limit=${rowPerPage}&offset=${page}&SportId=12&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/market?limit=${rowPerPage}&offset=${page}&SportId=13&search=${searchvalue}`
        : this.props.match.path?.includes("basketball")
        ? `nba/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("afl")
        ? `afl/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("golf")
        ? `golf/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("mma")
        ? `mma/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/market?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : `rls/market?limit=${rowPerPage}&offset=${page}&SportId=14&search=${searchvalue}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          Marketlist: data?.result?.rows,
          isLoading: false,
          MarketCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }
  handalValidate = () => {
    let { marketValues } = this.state;

    let flag = true;
    if (!marketValues?.name || marketValues?.name?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (
      !marketValues?.displayName ||
      marketValues?.displayName?.trim() === ""
    ) {
      flag = false;
      this.setState({
        errorDisplayName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorDisplayName: "",
      });
    }
    // if (marketValues?.isTeam === "") {
    //   flag = false;
    //   this.setState({
    //     errorIsTeam: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorIsTeam: "",
    //   });
    // }
    if (marketValues?.isPrimary === "") {
      flag = false;
      this.setState({
        errorIsPrimary: "This field is mandatory",
      });
    } else {
      this.setState({
        errorIsPrimary: "",
      });
    }
    if (marketValues?.type === "") {
      flag = false;
      this.setState({
        errorMarketType: "This field is mandatory",
      });
    } else {
      this.setState({
        errorMarketType: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        name: this.state?.marketValues?.name.trim(),
        displayName: this.state?.marketValues?.displayName,
        isPrimary: this.state?.marketValues?.isPrimary,
        type: this.state?.marketValues?.type,
      };
      if (
        this.props.match.path?.includes("cricket") ||
        this.props.match.path?.includes("basketball") ||
        this.props.match.path?.includes("afl") ||
        this.props.match.path?.includes("australianrules") ||
        this.props.match.path?.includes("golf") ||
        this.props.match.path?.includes("tennis") ||
        this.props.match.path?.includes("baseball") ||
        this.props.match.path?.includes("icehockey") ||
        this.props.match.path?.includes("boxing") ||
        this.props.match.path?.includes("mma") ||
        this.props.match.path?.includes("soccer")
      ) {
        payload = {
          ...payload,
        };
      } else {
        payload = {
          ...payload,
          SportId: this.props.match.path?.includes("rugbyleague")
            ? 12
            : this.props.match.path?.includes("rugbyunion")
            ? 13
            : 14,
        };
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";
      try {
        const { status } = await axiosInstance.post(
          `${passApi}/market`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllMarket(this.state.offset, this.state.search);
          //   this.setActionMessage(
          //     true,
          //     "Success",
          //     `Country variation Created Successfully`
          //   );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        name: this.state?.marketValues?.name.trim(),
        displayName: this.state?.marketValues?.displayName,
        isPrimary: this.state?.marketValues?.isPrimary,
        type: this.state?.marketValues?.type,
      };
      if (
        this.props.match.path?.includes("cricket") ||
        this.props.match.path?.includes("basketball") ||
        this.props.match.path?.includes("afl") ||
        this.props.match.path?.includes("australianrules") ||
        this.props.match.path?.includes("golf") ||
        this.props.match.path?.includes("tennis") ||
        this.props.match.path?.includes("baseball") ||
        this.props.match.path?.includes("icehockey") ||
        this.props.match.path?.includes("boxing") ||
        this.props.match.path?.includes("mma") ||
        this.props.match.path?.includes("soccer")
      ) {
        payload = {
          ...payload,
        };
      } else {
        payload = {
          ...payload,
          SportId: this.props.match.path?.includes("rugbyleague")
            ? 12
            : this.props.match.path?.includes("rugbyunion")
            ? 13
            : 14,
        };
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";
      try {
        const { status } = await axiosInstance.put(
          `${passApi}/market/${this.state.marketValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllMarket(this.state.offset, this.state.search);
          //   this.setActionMessage(
          //     true,
          //     "Success",
          //     `Country variation Edited Successfully`
          //   );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (error) {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      marketValues: {
        name: "",
        displayName: "",
        id: "",
        type: "",
        isPrimary: "",
      },
      errorName: "",
      errorDisplayName: "",
      errorIsPrimary: "",
      errorMarketType: "",
    });
  };
  inputVariationModal = (item) => {
    this.setState({
      isVariationModalOpen: true,
      marketValues: {
        name: item?.name,
        id: item?.id,
      },
    });
    // setisId(id);
    // setisName(name);
  };
  toggleVariationModal = () => {
    this.setState({
      isVariationModalOpen: false,
      marketValues: {
        name: "",
        id: "",
      },
    });
  };
  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        marketValues: {
          name: item?.name,
          displayName: item?.displayName,
          id: item?.id,
          isPrimary: item?.isPrimary,
          type: item?.type,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        marketValues: {
          name: "",
          displayName: "",
          id: "",
          isPrimary: "",
          type: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/market/${this.state.itemToDelete}?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/market/${this.state.itemToDelete}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("afl")
        ? `afl/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("golf")
        ? `golf/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("mma")
        ? `mma/market/${this.state.itemToDelete}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/market/${this.state.itemToDelete}`
        : `rls/market/${this.state.itemToDelete}?SportId=14`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllMarket(this.state.offset, this.state.search);
        });
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, Marketlist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllMarket(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      marketValues,
      Marketlist,
      MarketCount,
      errorName,
      errorDisplayName,
      errorMarketType,
      isVariationModalOpen,
      search,
      errorIsPrimary,
    } = this.state;
    const pageNumbers = [];
    // sportType !== "" &&
    //   (Marketlist = Marketlist.filter((obj) => obj.sportTypeId == sportType));

    // let currentPageRow = Marketlist;

    // if (Marketlist?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = Marketlist.slice(indexOfFirstTodo, indexOfLastTodo);
    if (MarketCount > 0) {
      for (let i = 1; i <= Math.ceil(MarketCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {/* {messageBox.display && (
            <ActionMessage
              message={messageBox.message}
              type={messageBox.type}
              styleClass={messageBox.styleClass}
            />
          )} */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules "
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Market</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  Market
                </Typography>
              </Grid>

              <Grid item xs={8} className="admin-filter-wrap">
                {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "60%" }}
              >
                <option value="">Select Sport Type</option>
                {allSportsType?.length > 0 &&
                  allSportsType?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.sportType}
                    </option>
                  ))}
              </SelectBox> */}
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllMarket(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && Marketlist?.length === 0 && <p>No Data Available</p>}

            {!isLoading && Marketlist?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell>DID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Display Name</TableCell>
                      {/* <TableCell style={{ width: "10%" }}>Is Team</TableCell> */}
                      <TableCell style={{ width: "10%" }}>Is Primary</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell style={{ width: "25%" }}>variation</TableCell>
                      <TableCell style={{ width: "14%" }}>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {Marketlist?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.name}</TableCell>
                          <TableCell>{item?.displayName}</TableCell>
                          {/* <TableCell>{item?.isTeam ? "Yes" : "No"}</TableCell> */}
                          <TableCell>
                            {item?.isPrimary ? "Yes" : "No"}
                          </TableCell>
                          <TableCell>{item?.type}</TableCell>
                          <TableCell>
                            <Button
                              variant="contained"
                              style={{
                                backgroundColor: "#4455C7",
                                color: "#fff",
                                borderRadius: "8px",
                                textTransform: "capitalize",
                                // padding: "13px 24px 12px",
                                marginLeft: "15px",
                              }}
                              onClick={() => {
                                this.inputVariationModal(item);
                              }}
                            >
                              Add/Edit variation
                            </Button>
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              style={{ cursor: "pointer", minWidth: "0px" }}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                            className={
                              Marketlist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              Marketlist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              MarketCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                            className={
                              Marketlist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              Marketlist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Market" : "Edit Market"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Name </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Name"
                          value={marketValues?.name}
                          onChange={(e) =>
                            this.setState({
                              marketValues: {
                                ...marketValues,
                                name: e.target.value,
                              },
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Display Name </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Display Name"
                          value={marketValues?.displayName}
                          onChange={(e) =>
                            this.setState({
                              marketValues: {
                                ...marketValues,
                                displayName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorDisplayName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorDisplayName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* <Grid item xs={6} className="national-select">
                      <label className="modal-label"> Is Team </label>
                      <Select
                        className="React teamsport-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        value={IsTeamData?.find((item) => {
                          return item?.value === marketValues?.isTeam;
                        })}
                        onChange={(e) =>
                          this.setState({
                            marketValues: {
                              ...marketValues,
                              isTeam: e.value,
                            },
                          })
                        }
                        options={IsTeamData}
                      />
                      {errorIsTeam ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorIsTeam}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid> */}
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Is Primary </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={IsTeamData?.find((item) => {
                            return item?.value === marketValues?.isPrimary;
                          })}
                          onChange={(e) =>
                            this.setState({
                              marketValues: {
                                ...marketValues,
                                isPrimary: e.value,
                              },
                            })
                          }
                          options={IsTeamData}
                        />
                        {errorIsPrimary ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorIsPrimary}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Type </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={MaketTypeOption?.find((item) => {
                            return item?.label == marketValues?.type;
                          })}
                          onChange={(e) =>
                            this.setState({
                              marketValues: {
                                ...marketValues,
                                type: e.label,
                              },
                            })
                          }
                          options={MaketTypeOption}
                        />
                        {errorMarketType ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorMarketType}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isVariationModalOpen}
              onClose={this.toggleVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Variation Details
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleVariationModal}
                />
                <CreateMarketVariation
                  inputModal={this.toggleVariationModal}
                  marketValues={marketValues}
                  pathName={this.props?.match?.path}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Market;
