.expert-tips-date-picker {
  .MuiOutlinedInput-adornedEnd .MuiOutlinedInput-input {
    padding: 11px 14px !important;
  }
}

.cg-15 {
  column-gap: 15px;
}

.user-select-wrap {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 12px;
}

.expert-tips-modal {
  // width: 720px !important;

  .MuiGrid-container {
    background-color: #ffffff !important;
    padding: 20px;
    border-radius: 16px;
  }

  .modal-label {
    display: block;
    text-align: left;
    width: 100%;
  }

  .date-time-picker {
    width: 100% !important;

    .MuiIconButton-root {
      min-width: 0px !important;
    }
  }

  .race-all-runner {
    display: grid;
    grid-template-columns: 50% 50%;
    column-gap: 20px;
    width: 98.5%;
  }

  .admin-btn-green {
    min-width: 100% !important;
  }

  .back-btn {
    min-width: 100%;
  }

  .teamsport-select {
    width: 100% !important;
  }

  .teamsport-text {
    .teamsport-textfield {
      width: 100%;

      .MuiInputBase-multiline {
        padding: 10.5px;
      }

      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        display: none;
      }
      .Mui-disabled {
        color: black;

        background-color: hsl(0, 0%, 95%) !important ;
        border-color: hsl(0, 0%, 90%);
        border-radius: 6px;
        opacity: 1;
        input {
          background-color: #f2f2f2;
        }
        input::placeholder {
          color: #383838;
        }
        fieldset {
          border: none;
        }
      }
      .MuiFormHelperText-root {
        color: red;
        font-size: 14px;
        margin-left: 0px;
      }
    }
  }

  .race-runner-wrap {
    display: flex;
    align-items: baseline;
    width: 100%;
    column-gap: 20px;
  }

  .errorText {
    text-align: start;
    font-size: 14px;
    margin: 4px 0px 0px 4px;
  }

  .color-red {
    color: red;
  }
}

.color-red {
  color: red;
}

.color-yellow {
  color: #ffa600 !important;
}

.mb-8 {
  margin-bottom: 8px;
}

.tips-modal-details {
  width: 720px !important;

  .modal-loader {
    text-align: center;
  }

  .d-flex {
    display: flex;
    flex-direction: row;
  }

  .align-item-baseline {
    align-items: baseline;
  }

  .col-35 {
    column-gap: 35px;
  }

  .mb-18 {
    margin-bottom: 18px;
  }

  .w-60 {
    width: 60%;
  }

  .details {
    .detsils-header {
      font-size: 16px;
      line-height: 20px;
      font-family: "Inter", sans-serif;
      font-weight: 700;
      width: 40%;
    }

    .details-para {
      width: 60%;
      a {
        color: #004cff;
        text-decoration: underline;
      }
    }
  }
}
.featured-race-editor {
  margin-bottom: 15px;
  .sun-editor-editable {
    min-height: 250px !important;
    a {
      color: #004cff !important;
    }
  }
}

.runner-comments-editor {
  text-align: left;
  .sun-editor-editable {
    min-height: 80px !important;
  }
  .se-list-layer {
    z-index: 6;
  }
}
.news-letter-editor {
  .sun-editor-editable {
    a {
      color: #004cff;
      text-decoration: underline;
    }
    span[style~="color:"] a {
      color: #004cff;
    }
  }
}
.weekly-preview {
  a {
    color: #004cff;
    text-decoration: underline;
  }
  span[style~="color:"] a {
    color: #004cff;
  }
}
