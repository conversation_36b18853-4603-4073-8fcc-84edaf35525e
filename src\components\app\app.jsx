import React, { Component } from "react";
import moment from "moment";
import {
  <PERSON><PERSON>erRouter,
  // HashRouter as Router,
} from "react-router-dom";
import "./app.css";
import { EventEmitter } from "../../services/event";
import { connect } from "react-redux";
import { config } from "../../helpers/config";
import { getAuth } from "../../library/common/actions";
import AppRoutes from "./app.routes";
// import { fetchFromStorage } from "../../library/utilities";
// import Routes from "../../admin/Module/Routes";
import AppMeta from "../AppMeta";
import { setMetaTags } from "../../library/common/actions/MetaActions";

class App extends Component {
  constructor(props) {
    super(props);
    this.state = {
      login: false,
      loginuser: false,
      classList: [],
    };
    this.logout = this.logout.bind(this);
    this.checkLogin = this.checkLogin.bind(this);
    EventEmitter.subscribe("updateAppClassList", this.updateClassList);
    EventEmitter.subscribe("removeAppClassList", this.removeClassList);
    EventEmitter.subscribe("checkLogin", this.checkuserIsLoggedIn);
    EventEmitter.subscribe("setPageMeta", this.setPageMeta);
  }

  componentDidMount() {
    let sessionClockTime = localStorage.getItem("sessionClock");
    sessionClockTime =
      sessionClockTime && sessionClockTime !== null
        ? JSON.parse(localStorage.getItem("sessionClock"))
        : sessionClockTime;

    if (sessionClockTime === null || sessionClockTime === "undefined") {
      localStorage.setItem(
        "sessionClock",
        JSON.stringify({
          sessionStartTime: moment().valueOf(),
        })
      );
      EventEmitter.dispatch("getInitSessionTime");
    }
    this.checkuserIsLoggedIn();
    this.props.getAuth();
    // if (!window.location.href.includes("admin")) {
    // EventEmitter.dispatch("getInitialUpcommingRace");
    // }
    // try {
    //   setInterval(async () => {
    //     await EventEmitter.dispatch("getInitialUpcommingRace");
    //   }, 60000);
    // } catch (e) {
    //   console.log(e);
    // }
    this.handleMaintenanceUpdated = (data) => {
      if (data === "true") {
        localStorage.setItem(
          "previous_page",
          window.location.pathname.includes("/maintenance.html")
            ? "/"
            : window.location.pathname
        );
        setTimeout(() => {
          window.location.href = "/admin/maintenance.html";
        }, 0);
      }
    };

    EventEmitter.subscribe("onmaintenance", this.handleMaintenanceUpdated);
  }
  componentWillUnmount() {
    EventEmitter.unsubscribe("onmaintenance", this.handleMaintenanceUpdated);
  }
  checkuserIsLoggedIn() {
    let { classList } = this.state;
    classList.push("App");
    this.setState({ classList: classList });

    /*var loginuser = localStorage.getItem("user")
      ? JSON.parse(localStorage.getItem("user"))
      : localStorage.getItem("user");*/
    var loginuser = localStorage.getItem("user");
    if (/*localStorage.getItem("user")  || */ localStorage.getItem("user")) {
      this.setState({
        login: true,
        loginuser: loginuser,
      });
    } else {
      this.setState({
        loginuser: false,
      });
      // this.logout();
    }
  }
  updateClassList = (classString) => {
    let { classList } = this.state;
    classList.push(classString);
    this.setState({ classList: classList });
  };
  removeClassList = (classString) => {
    let { classList } = this.state;
    var index = classList.indexOf(classString);
    if (index !== -1) {
      classList.splice(index, 1);
      this.setState({ classList: classList });
    }
  };

  checkLogin(data) {
    if (
      Boolean(data.terms) === true &&
      data.username === "<EMAIL>" &&
      data.password === "123456"
    ) {
      localStorage.setItem("loginuser", JSON.stringify(data.username));
      this.setState({ login: true });
      setTimeout(function () {
        this.logout();
      }, 1000 * 60 * 60);
    } else if (data.username === "admin" && data.password === "123456") {
      localStorage.setItem("loginuser", JSON.stringify(data.username));
      this.setState({ login: true });
      setTimeout(function () {
        this.logout();
      }, 1000 * 60 * 60);
    } else {
      this.setState({ login: false });
    }
  }

  logout() {
    localStorage.removeItem("user");
    localStorage.removeItem("user");
    localStorage.removeItem("auth");
    EventEmitter.dispatch("removeAppClassList", "loggedin");
    this.setState({ login: false, loginuser: "" });
  }

  setPageMeta = (tags) => {
    if (tags) this.props.setMetaTags(tags);
  };

  render() {
    // let user = fetchFromStorage("user");
    var { classList, loginuser } = this.state;
    var { isLoggedIn, tags } = this.props;

    if (isLoggedIn) classList.push("loggedin");
    else {
      var index = classList.indexOf("loggedin");
      if (index !== -1) {
        classList.splice(index, 1);
      }
    }

    const uniqueClass = [];
    classList.map((cl) => {
      if (uniqueClass.indexOf(cl) === -1) {
        uniqueClass.push(cl);
      }
      return cl;
    });

    return (
      <div className={uniqueClass.join(" ")}>
        {tags.map((tag, index) => (
          <AppMeta
            title={tag.title}
            description={tag.description}
            key={index}
          />
        ))}
        {/* basename={config.baseUrl} */}
        <BrowserRouter basename={config.basename}>
          <AppRoutes
            loginuser={
              loginuser
                ? typeof loginuser === "string"
                  ? JSON.parse(loginuser)
                  : loginuser
                : false
            }
            isLoggedIn={isLoggedIn}
            login={isLoggedIn}
            checkLogin={this.checkLogin}
            logout={this.logout}
            {...this.props}
          />
        </BrowserRouter>
      </div>
    );
  }
}

const mapStateToProps = ({ authReducer, metaReducer }) => {
  return {
    isLoggedIn: authReducer.isLoggedIn,
    tags: metaReducer.tags,
  };
};

export default connect(mapStateToProps, { getAuth, setMetaTags })(App);
