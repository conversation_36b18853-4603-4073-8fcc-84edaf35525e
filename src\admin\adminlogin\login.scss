.loginWrapper {
  .leftSide {
    text-align: left;
    height: 100vh;
    display: flex;
    align-items: center;
    background-color: #303f9f;
    justify-content: center;
  }

  .input-field {
    border-radius: 8px;
    min-height: 45px;
  }

  .MuiCardContent-root {
    padding: 0;

    &:last-child {
      padding-bottom: 0;
    }
  }

  .MuiContainer-maxWidthLg {
    padding: 0;
  }

  .right-side {
    text-align: left;
    height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loginBtn {
    min-width: 150px;
    padding: 10px;
    background-color: #4455c7;
    border-radius: 8px;
  }
}

.spinner {
  margin-left: 20px;
  svg {
    color: #4455c7;
  }
}

.mb-30 {
  margin-bottom: 30px;
}

.welcomeText {
  color: #fff;
  font-size: 50px;
  text-align: center;
}

.mainLayout {
  background-color: #e4e5e6;
}

.btnWrapper {
  margin-top: 20px;

  button {
    margin-right: 20px;
    min-width: 80px;
  }
}

.pageInfo {
  padding: 20px 0px;
}

.vp10 {
  padding: 10px 0;
}

.addNew {
  .input-field {
    padding-right: 20px;

    // input {
    //   margin-top: 10px;
    // }
  }
}

.saveAvailable {
  position: relative;
  top: 30px;

  button {
    height: 42px;
  }
}

.mr10 {
  margin-right: 10px;
}

.cursor {
  cursor: pointer;
}

.listTable {
  thead {
    background-color: #ffffff;

    th {
      font-weight: bold;
    }
  }

  .iconBtn {
    cursor: pointer;
    width: 20px;
    margin-right: 15px;
    height: 20px;
    padding: 8px;
    background: #f9f8f8;
    text-align: center;
    border-radius: 50px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
    -webkit-transition: box-shadow 0.3s linear;
    transition: box-shadow 0.3s linear;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    }
  }
}

td.MuiTableCell-root.MuiTableCell-body {
  padding: 6px 15px;
}

.errorText {
  color: red;
}
