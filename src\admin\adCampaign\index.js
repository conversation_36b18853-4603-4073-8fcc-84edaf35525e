import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
import { Link } from "react-router-dom";
import moment from "moment-timezone";
import SearchIcons from "../../images/searchIcon.svg";
import Select from "react-select";
import ButtonComponent from "../../library/common/components/Button";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import _ from "lodash";
import DateFnsUtils from "@date-io/date-fns";
import TodayIcon from "@mui/icons-material/Today";
import TableSortLabel from "@mui/material/TableSortLabel";
import { parseISO } from "date-fns";

const typeOptions1 = [
  {
    label: "All",
    value: 1,
  },
  {
    label: "Web",
    value: 2,
  },
  {
    label: "App",
    value: 3,
  },
  {
    label: "Both",
    value: 4,
  },
];
const typeOptions = [
  {
    label: "web",
    value: 1,
  },

  {
    label: "app",
    value: 2,
  },
  {
    label: "both",
    value: 3,
  },
];
const statusOptions = [
  {
    label: "Scheduled",
    value: 1,
  },

  {
    label: "Published",
    value: 2,
  },
  {
    label: "Completed",
    value: 3,
  },
];

const types = [
  { id: 1, name: "web" },
  { id: 2, name: "app" },
];

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

export default class AdCampaign extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      campaignData: [],
      campaignCount: 0,
      isInputModalOpen: false,
      isActivityModalOpen: false,
      isDetailsModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      userId: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      messageBoxModal: {
        display: false,
        type: "",
        message: "",
      },
      campaingValues: {
        id: "",
        title: "",
        startDate: moment(Date()).format("YYYY-MM-DD"),
        endDate: moment(Date()).format("YYYY-MM-DD"),
        type: "",
        url: "",
        // page_id: [],
        // meadiaDetailId: 0,
        clientId: null,
      },
      errorTitle: "",
      errorPage: "",
      errorMedia: "",
      errorType: "",
      errorstartDate: "",
      errorendDate: "",
      errorClient: "",
      errorUrl: "",
      search: "",
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      paginationPage: [],
      csvListData: [],
      clientDetail: [],
      sortStartDate: true,
      sortEndDate: true,
      sortDate: null,
      filterEndDate: null,
      startDateOpen: false,
      endDateOpen: false,
      sortType: "id",
      sortLabelid: false,
      sortName: true,
      sportsOption: [],
      mediaOptions: [],
      clientOptionsData: [],
      clientCount: 0,
      clientPage: 0,
      isClientSearch: "",
      searchClientCount: 0,
      searchClientPage: 0,
      searchClientData: [],
      isClientLoading: false,
      dataFilter: "",
      editId: null,
      statusFilter: "",
      sortLabelstartDate: true,
      sortLabelendDate: true,
      checkedType: [],
    };
  }

  componentDidMount() {
    this.fetchAdCampaignlist(0, "id", false, null, null, "", "");
    this.fetchAllSports();
    // this.fetchAllMedia();
    this.fetchClientOptions(0);
  }

  handleCampaignStatusChange = (e) => {
    this.setState({
      statusFilter: e?.label,
      currentPage: 1,
    });
    this.fetchAdCampaignlist(0, "id", false, null, null, "", e?.label);
  };

  inputModal = (item, type) => () => {
    const { campaingValues } = this.state;

    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchCurrentCampaign(item?.id);
      this.setState({
        editId: item?.id,
      });
    } else {
      this.setState({
        campaingValues: {
          title: "",
          startDate: moment(Date()).format("YYYY-MM-DD"),
          endDate: moment(Date()).format("YYYY-MM-DD"),
          type: "",
          clientId: null,
          url: "",
          // page_id: [],
          // meadiaDetailId: 0,
        },
        isEditMode: false,
        checkedType: [],
        editId: "",
      });
    }
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTitle: "",
      errorClient: "",
      errorUrl: "",
      errorType: "",
      errorendDate: "",
      errorstartDate: "",
    });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setActionMessageModal = (display = false, type = "", message = "") => {
    this.setState({ messageBoxModal: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBoxModal: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  goToCampaignInfo = (id, ClientId) => () => {
    this.props.navigate(`/campaigninfo/${id}`, { state: { ClientId } });
  };

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Sport",
        value: 0,
      };

      let alldata = [alldatas, ...newdata];

      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        isLoading: false,
        sportsOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
      });
    }
  }
  // async fetchAllMedia() {
  //   this.setState({ isLoading: true });
  //   const { status, data } = await axiosInstance.get(
  //     `/campaign/mediadetails/findAll`
  //   );
  //   if (status === 200) {
  //     let newdata = [];
  //     let sportData = data.result?.map((item) => {
  //       newdata.push({
  //         label: item?.name,
  //         value: item?.id,
  //       });
  //     });
  //     this.setState({
  //       mediaOptions: newdata,
  //       isLoading: false,
  //     });
  //   }
  // }

  fetchClientOptions = async (Page) => {
    const { rowPerPage, search } = this.state;
    this.setState({ isClientLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/campaign/client?limit=20&offset=${Page}`
      );
      if (status === 200) {
        let newdata = [];
        let count = data?.count / 20;
        let optionData = data?.result?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(this.state?.clientOptionsData, newdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          clientOptionsData: finalData,
          clientCount: Math.ceil(count),
          isClientLoading: false,
        });
      } else {
        this.setState({ isClientLoading: false });
      }
    } catch {
      this.setState({ isClientLoading: false });
    }
  };
  handleOnScrollBottomClient = (e, type) => {
    let {
      clientCount,
      clientPage,
      isClientSearch,
      searchClientCount,
      searchClientPage,
    } = this.state;
    if (
      isClientSearch !== "" &&
      searchClientCount !== Math.ceil(searchClientPage / 20 + 1)
    ) {
      this.handleClientInputChange(searchClientPage + 20, isClientSearch);
      this.setState({
        searchClientPage: searchClientPage + 20,
      });
    } else {
      if (clientCount !== Math.ceil(clientPage / 20) && isClientSearch == "") {
        this.fetchClientOptions(clientPage + 20);
        this.setState({
          clientPage: clientPage + 20,
        });
      }
    }
  };
  handleClientInputChange = (clientPage, value) => {
    const passApi = `/campaign/client?limit=20&offset=${clientPage}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(this.state?.searchClientData, newdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchClientData: finalData,
          searchClientCount: Math.ceil(count),
          isClientSearch: value,
        });
      }
    });
  };
  fetchAdCampaignlist = async (
    offsetsize,
    type,
    order,
    sortDate,
    filterEndDate,
    filterType,
    campStatus
  ) => {
    const { rowPerPage, search } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/campaign/campaignAdmin?limit=${rowPerPage}&offset=${offsetsize}&key=${
          type ? type : ""
        }&order=${order ? "ASC" : "DESC"}&startDate=${
          sortDate === null ? "" : sortDate
        }&endDate=${
          filterEndDate === null ? "" : filterEndDate
        }&timeZone=${timezone}&search=${search}&type=${
          filterType ? filterType : ""
        }&status=${campStatus}`
      );
      if (status === 200) {
        const pageNumbers = [];
        if (data?.count > 0) {
          for (let i = 1; i <= Math.ceil(data?.count / rowPerPage); i++) {
            pageNumbers.push(i);
          }
        }

        this.setState({
          isLoading: false,
          campaignData: data?.result,
          campaignCount: data?.count,
          paginationPage: pageNumbers,
          offset: offsetsize,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({ isLoading: false });
    }
  };

  deleteItem = async () => {
    this.setState({ isModalOpen: false });
    const { offset, type, order, sortDate, filterEndDate, statusFilter } =
      this.state;
    try {
      const { status, data } = await axiosInstance.delete(
        `/campaign/campaign/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAdCampaignlist(
            offset,
            type,
            order,
            sortDate,
            filterEndDate,
            null,
            statusFilter
          );
        });
        this.setActionMessage(
          true,
          "Success",
          "Ad Campaign Deleted Successfully!"
        );
      } else {
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ isLoading: false, createError: data?.message });
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", err?.message);
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({
        isLoading: false,
        createError: err?.response?.data?.message,
      });
    }
  };

  handleValidate = () => {
    let {
      campaingValues,
      checkedType,
      selectedModalBanner,
      selectedModalPage,
      script,
    } = this.state;
    let flag = true;
    if (
      campaingValues?.title?.trim() === "" ||
      campaingValues?.title === null
    ) {
      flag = false;
      this.setState({
        errorTitle: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorTitle: "",
      });
    }
    // if (checkedType?.length === 0) {
    //   flag = false;
    //   this.setState({
    //     errorType: "This field is mandatory",
    //   });
    //   // return flag;
    // } else {
    if (campaingValues?.type === "" || campaingValues?.type === null) {
      flag = false;
      this.setState({
        errorType: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorType: "",
      });
    }
    if (campaingValues?.clientId == [] || campaingValues?.clientId === null) {
      flag = false;
      this.setState({
        errorClient: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorClient: "",
      });
    }
    if (
      campaingValues?.startDate === "" ||
      campaingValues?.startDate === null
    ) {
      flag = false;
      this.setState({
        errorstartDate: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorstartDate: "",
      });
    }
    if (campaingValues?.endDate === "" || campaingValues?.endDate === null) {
      flag = false;
      this.setState({
        errorendDate: "This field is mandatory",
      });
      // return flag;
    } else {
      if (this.state.errorendDate) {
        flag = false;
      } else {
        this.setState({
          errorendDate: "",
        });
      }
    }
    if (campaingValues?.url?.trim() === "" || campaingValues?.url === null) {
      flag = false;
      this.setState({
        errorUrl: "This field is mandatory",
      });
      // return flag;
    } else {
      this.setState({
        errorUrl: "",
      });
    }

    return flag;
  };

  fetchCurrentCampaign = async (id) => {
    const { status, data } = await axiosInstance.get(
      `/campaign/campaign/${id}`
    );
    if (status === 200) {
      let receivedType = data?.result?.type;
      if (receivedType === "both") {
        this.setState({
          checkedType: ["web", "app"],
        });
      } else {
        this.setState({
          checkedType: [receivedType],
        });
      }
      this.setState({
        campaingValues: {
          id: data?.result?.id,
          type: data?.result?.type,
          clientId: data?.result?.ClientId,
          title: data?.result?.title,
          startDate: data?.result?.startDate,
          endDate: data?.result?.endDate,
          url: data?.result?.url,
        },
        // checkedType: [data?.result?.type],
        isEditMode: true,
      });
    }
  };

  handleSave = async () => {
    const { HomeArticleData, sortDate, filterEndDate } = this.state;
    if (this.handleValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const {
        campaingValues,
        image,
        script,
        offset,
        sortEndDate,
        sortStartDate,
        sortName,
        sortType,
        sortLabelid,
        selectedPage,
        statusFilter,
        checkedType,
      } = this.state;

      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "pageName"
          ? sortName
          : sortType === "startDate"
          ? sortStartDate
          : sortEndDate;

      // let payload = {
      //   title: campaingValues?.title,
      //   type: checkedType,
      //   startDate: moment(campaingValues?.startDate).format("YYYY-MM-DD"),
      //   endDate: moment(campaingValues?.endDate).format("YYYY-MM-DD"),
      //   clientId: campaingValues?.clientId,
      // };
      let payload = {
        title: campaingValues?.title,
        type: campaingValues?.type,
        startDate: moment(campaingValues?.startDate).format("YYYY-MM-DD"),
        endDate: moment(campaingValues?.endDate).format("YYYY-MM-DD"),
        clientId: campaingValues?.clientId,
        url: campaingValues?.url,
      };
      try {
        const { status, data } = await axiosInstance.post(
          `/campaign/campaign?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
          });
          this.fetchAdCampaignlist(
            offset,
            sortType,
            sortData,
            sortDate,
            filterEndDate,
            null,
            statusFilter
          );
          this.setActionMessage(
            true,
            "Success",
            `Ad Campaign Created Successfully`
          );
        } else {
          // this.setActionMessage(true, "Error", data?.message);
          this.setActionMessageModal(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessageModal(true, "Error", err?.response?.data?.message);
        // this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  handleUpdate = async () => {
    const { HomeArticleData, sortDate, filterEndDate } = this.state;
    if (this.handleValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const {
        campaingValues,
        image,
        script,
        uploadImage,
        offset,
        sortEndDate,
        sortStartDate,
        sortName,
        sortType,
        sortLabelid,
        editId,
        dataFilter,
        statusFilter,
        checkedType,
      } = this.state;
      let sortData =
        sortType === "id"
          ? sortLabelid
          : sortType === "pageName"
          ? sortName
          : sortType === "startDate"
          ? sortStartDate
          : sortEndDate;
      // let payload = {
      //   title: campaingValues?.title,
      //   type: checkedType,
      //   startDate: moment(campaingValues?.startDate).format("YYYY-MM-DD"),
      //   endDate: moment(campaingValues?.endDate).format("YYYY-MM-DD"),
      //   clientId: campaingValues?.clientId,
      // };
      let payload = {
        title: campaingValues?.title,
        type: campaingValues?.type,
        startDate: moment(campaingValues?.startDate).format("YYYY-MM-DD"),
        endDate: moment(campaingValues?.endDate).format("YYYY-MM-DD"),
        clientId: campaingValues?.clientId,
        url: campaingValues?.url,
      };
      try {
        const { status, data } = await axiosInstance.put(
          `/campaign/campaign/${editId}?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
          });
          this.fetchAdCampaignlist(
            offset,
            sortType,
            sortData,
            sortDate,
            filterEndDate,
            null,
            statusFilter
          );
          this.setActionMessage(
            true,
            "Success",
            `Ad Campaign Edited Successfully`
          );
        } else {
          this.setActionMessageModal(true, "Error", data?.message);
          // this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        // this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setActionMessageModal(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  handleSortStartDate = (date) => {
    const { offset, filterEndDate, type, order, dataFilter, statusFilter } =
      this.state;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAdCampaignlist(
        offset,
        type,
        order,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        filterEndDate,
        dataFilter,
        statusFilter
      );
    }
  };
  handleFilterEndDate = (date) => {
    const { offset, sortDate, type, order, dataFilter, statusFilter } =
      this.state;
    this.setState({
      filterEndDate: date
        ? moment(date).tz(timezone).format("YYYY-MM-DD")
        : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAdCampaignlist(
        offset,
        type,
        order,
        sortDate,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        dataFilter,
        statusFilter
      );
    }
  };
  clearStartDate = () => {
    const {
      offset,
      type,
      order,
      sortDate,
      filterEndDate,
      dataFilter,
      statusFilter,
    } = this.state;
    this.setState({
      sortDate: null,
      startDateOpen: false,
    });
    this.fetchAdCampaignlist(
      offset,
      type,
      order,
      null,
      filterEndDate,
      dataFilter,
      statusFilter
    );
  };
  clearEndDate = () => {
    const {
      offset,
      type,
      order,
      sortDate,
      filterEndDate,
      dataFilter,
      statusFilter,
    } = this.state;
    this.setState({
      filterEndDate: null,
      endDateOpen: false,
    });
    this.fetchAdCampaignlist(
      offset,
      type,
      order,
      sortDate,
      null,
      dataFilter,
      statusFilter
    );
  };

  sortLabelHandler = (type) => {
    const {
      sortType,
      sortLabelid,
      sortName,
      sortStartDate,
      sortEndDate,
      selectedPage,
      selectedBanner,
      offset,
      sortDate,
      filterEndDate,
      dataFilter,
      statusFilter,
    } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchAdCampaignlist(
        0,
        type,
        !sortLabelid,
        sortDate,
        filterEndDate,
        dataFilter,
        statusFilter
      );
      this.setState({
        sortLabelid: !sortLabelid,
        sortName: true,
        sortStartDate: true,
        sortEndDate: true,
        currentPage: 1,
      });
    }
    // else if (type === "name") {
    //   this.fetchAdCampaignlist(
    //     0,
    //     type,
    //     !sortName,
    //     sortDate,
    //     filterEndDate,
    //     dataFilter,
    //     statusFilter
    //   );
    //   this.setState({
    //     sortLabelid: false,
    //     sortName: !sortName,
    //     sortStartDate: true,
    //     sortEndDate: true,
    //     currentPage: 1,
    //   });
    // }
    else if (type === "startDate") {
      this.fetchAdCampaignlist(
        0,
        type,
        !sortStartDate,
        sortDate,
        filterEndDate,
        dataFilter,
        statusFilter
      );
      this.setState({
        sortLabelid: false,
        sortName: true,
        sortStartDate: !sortStartDate,
        sortEndDate: true,
        currentPage: 1,
      });
    } else {
      this.fetchAdCampaignlist(
        0,
        type,
        !sortEndDate,
        sortDate,
        filterEndDate,
        dataFilter,
        statusFilter
      );
      this.setState({
        sortLabelid: false,
        sortName: true,
        sortStartDate: true,
        sortEndDate: !sortEndDate,
        currentPage: 1,
      });
    }
  };

  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      selectedPage,
      type,
      sortEndDate,
      sortDate,
      filterEndDate,
      dataFilter,
      statusFilter,
    } = this.state;
    const offsetNew = (Number(page) - 1) * rowPerPage;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
    this.fetchAdCampaignlist(
      (Number(page) - 1) * rowPerPage,
      type,
      !sortEndDate,
      sortDate,
      filterEndDate,
      dataFilter === "All" ? "" : dataFilter,
      statusFilter
    );
  };

  handleCampaignTypeChange = (e) => {
    const { type, order, sortDate, filterEndDate, statusFilter } = this.state;
    this.setState({
      dataFilter: e?.label,
      currentPage: 1,
    });
    this.fetchAdCampaignlist(
      0,
      type,
      order,
      sortDate,
      filterEndDate,
      e?.label === "All" ? "" : e?.label,
      statusFilter
    );
  };

  // handleTypeChange = (e) => {
  //   const value = e.target.value;
  //   const isChecked = e.target.checked;
  //   let { checkedType } = this.state;
  //   // {checkedType.join(', ')}
  //   if (isChecked) {
  //     // Map numeric value to string and add to the array if it's not already present
  //     if (!checkedType.includes(types.find((type) => type.id === value).name)) {
  //       checkedType = [
  //         ...checkedType,
  //         types.find((type) => type.id === value).name,
  //       ];
  //     }
  //   } else {
  //     // Remove the string from the array if it's present
  //     checkedType = checkedType.filter(
  //       (type) => type !== types.find((t) => t.id === value).name
  //     );
  //   }

  //   this.setState({
  //     checkedType,
  //   });
  // };

  handleTypeChange = (e) => {
    const name = e.target.value;
    const isChecked = e.target.checked;
    let { checkedType } = this.state;

    if (isChecked) {
      // Add the name to the array if it's not already present
      if (!checkedType.includes(name)) {
        checkedType = [...checkedType, name];
      }
    } else {
      // Remove the name from the array if it's present
      checkedType = checkedType.filter((type) => type !== name);
    }

    this.setState({
      checkedType,
    });
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { type, order, sortDate, filterEndDate, dataFilter, statusFilter } =
      this.state;
    if (event.key === "Enter") {
      this.fetchAdCampaignlist(
        0,
        type,
        order,
        sortDate,
        filterEndDate,
        dataFilter,
        statusFilter
      );
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      campaignData,
      isModalOpen,
      rowPerPage,
      currentPage,
      messageBox,
      isLoading,
      paginationPage,
      isInputModalOpen,
      isEditMode,
      campaingValues,
      dataFilter,
      offset,
      userId,
      search,
      errorTitle,
      errorPage,
      errorMedia,
      errorType,
      errorstartDate,
      errorendDate,
      sortType,
      sortLabelid,
      errorUrl,
      sportsOption,
      mediaOptions,
      clientOptionsData,
      campaignCount,
      errorClient,
      type,
      order,
      sortDate,
      filterEndDate,
      startDateOpen,
      endDateOpen,
      isClientSearch,
      searchClientData,
      clientOptionsData,
      isClientLoading,
      statusFilter,
      sortLabelstartDate,
      sortLabelendDate,
      checkedType,
      messageBoxModal,
    } = this.state;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Campaign
                </Link>
                <Typography className="active_p">Ad Campaign</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Ad Campaign
                </Typography>
              </Grid>
              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DesktopDatePicker
                    clearable
                    // onClear={() => this.clearStartDate()}
                    // open={startDateOpen}
                    // onOpen={() => this.setState({ startDateOpen: true })}
                    // onClose={() => this.setState({ startDateOpen: false })}
                    autoOk
                    // disableToolbar
                    // variant="inline"
                    format="dd/MM/yyyy"
                    placeholder="Start Date"
                    margin="normal"
                    id="date-picker-inline"
                    inputVariant="outlined"
                    value={sortDate ? parseISO(sortDate) : null}
                    slots={{
                      openPickerIcon: TodayIcon,
                    }}
                    slotProps={{
                      field: {
                        placeholder: "DD/MM/YYYY",
                      },
                    }}
                    onChange={(e) => this.handleSortStartDate(e)}
                    KeyboardButtonProps={{
                      "aria-label": "change date",
                    }}
                    className="date-picker-fixture advt-datepicker"
                    style={{ margin: "0px 10px 0px 0px" }}
                  />
                </LocalizationProvider>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DesktopDatePicker
                    clearable
                    // onClear={() => this.clearEndDate()}
                    // open={endDateOpen}
                    // onOpen={() => this.setState({ endDateOpen: true })}
                    // onClose={() => this.setState({ endDateOpen: false })}
                    autoOk
                    // disableToolbar
                    // variant="inline"
                    format="dd/MM/yyyy"
                    placeholder="End Date"
                    margin="normal"
                    id="date-picker-inline"
                    inputVariant="outlined"
                    value={filterEndDate ? parseISO(filterEndDate) : null}
                    minDate={
                      sortDate
                        ? typeof sortDate === "string"
                          ? parseISO(sortDate)
                          : sortDate
                        : null
                    }
                    slots={{
                      openPickerIcon: TodayIcon,
                    }}
                    slotProps={{
                      field: {
                        placeholder: "DD/MM/YYYY",
                      },
                    }}
                    onChange={(e) => this.handleFilterEndDate(e)}
                    KeyboardButtonProps={{
                      "aria-label": "change date",
                    }}
                    className="date-picker-fixture advt-datepicker"
                    style={{ margin: "0px" }}
                  />
                </LocalizationProvider>
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    // this.fetchAllTeam(0);
                    this.fetchAdCampaignlist(
                      0,
                      type,
                      order,
                      sortDate,
                      filterEndDate,
                      dataFilter,
                      statusFilter
                    );
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <Select
                  className="React cricket-select  external-select"
                  classNamePrefix="select"
                  placeholder="Select Status"
                  value={statusOptions?.find((item) => {
                    return item?.label == statusFilter;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleCampaignStatusChange(e)}
                  options={statusOptions}
                />
                <Select
                  className="React cricket-select  external-select"
                  classNamePrefix="select"
                  placeholder="Select Type"
                  value={typeOptions1?.find((item) => {
                    return item?.label == dataFilter;
                  })}
                  // isLoading={isLoading}
                  onChange={(e) => this.handleCampaignTypeChange(e)}
                  options={typeOptions1}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && campaignData.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && campaignData.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable"
                    aria-label="simple table"
                    style={{ minWidth: "max-content" }}
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer" }}
                        >
                          DID{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortLabelid
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell style={{ width: "108px" }}>Title</TableCell>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("name")}
                          style={{ width: "84px " }}
                        >
                          Client{" "}
                          {/* <TableSortLabel
                          active={true}
                          direction={
                            sortType === "client"
                              ? sortLabelid
                                ? "asc"
                                : "desc"
                              : "desc"
                          }
                        /> */}
                        </TableCell>
                        <TableCell style={{ width: "108px" }}>
                          Page Name{" "}
                        </TableCell>
                        <TableCell
                        // style={{ width: "11%" }}
                        >
                          URL{" "}
                        </TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell
                          style={{
                            cursor: "pointer",
                            // width: "10%"
                          }}
                          onClick={() => this.sortLabelHandler("startDate")}
                        >
                          Start Date{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "startDate"
                                ? sortLabelstartDate
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          style={{ cursor: "pointer" }}
                          onClick={() => this.sortLabelHandler("endDate")}
                        >
                          End Date{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "endDate"
                                ? sortLabelendDate
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell
                        // style={{ width: "26%" }}
                        >
                          Action
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {campaignData?.length > 0 ? (
                        campaignData?.map((campaign, i) => (
                          <TableRow
                            key={i}
                            className="table-rows listTable-Row"
                          >
                            <TableCell>{campaign?.id}</TableCell>
                            <TableCell>{campaign?.title}</TableCell>
                            <TableCell>{campaign?.Client?.name}</TableCell>
                            <TableCell>
                              {campaign?.CampaignMappings?.map((item) => {
                                return item?.page_name;
                              }).join(", ")}
                            </TableCell>
                            <TableCell>{campaign?.url}</TableCell>
                            <TableCell>{campaign?.status}</TableCell>
                            <TableCell>
                              {moment(campaign?.startDate).format("DD-MM-YYYY")}
                            </TableCell>
                            <TableCell>
                              {moment(campaign?.endDate).format("DD-MM-YYYY")}
                            </TableCell>
                            <TableCell>{campaign?.type}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(campaign, "edit")}
                                className={
                                  campaign?.status === "completed"
                                    ? "table-btn edit-btn disabled-btn"
                                    : "table-btn edit-btn"
                                }
                                disabled={campaign?.status === "completed"}
                              >
                                Edit
                              </Button>

                              <Button
                                onClick={this.setItemToDelete(campaign?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                              <Button
                                onClick={this.goToCampaignInfo(
                                  campaign?.id,
                                  campaign?.ClientId
                                )}
                                // className={
                                //   campaign?.status === "completed"
                                //     ? "table-btn info-btn disabled-btn"
                                //     : "table-btn info-btn"
                                // }
                                className="table-btn info-btn"
                              >
                                Campaign Info
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                campaignCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={paginationPage[paginationPage?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Campaign" : "Edit Campaign"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <Grid container>
                      {messageBoxModal.display && (
                        <ActionMessage
                          message={messageBoxModal.message}
                          type={messageBoxModal.type}
                          styleClass={messageBoxModal.styleClass}
                        />
                      )}
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Title </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Title"
                          value={campaingValues?.title}
                          onChange={(e) =>
                            this.setState({
                              campaingValues: {
                                ...campaingValues,
                                title: e.target.value,
                              },
                            })
                          }
                        />
                        {errorTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Website URL </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="URL"
                          value={campaingValues?.url}
                          onChange={(e) =>
                            this.setState({
                              campaingValues: {
                                ...campaingValues,
                                url: e.target.value,
                              },
                            })
                          }
                        />
                        {errorUrl ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorUrl}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        className="teamsport-textfield national-select teamsport-text"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label"> Client </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Select Client"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomClient(e)
                          }
                          onInputChange={(e) =>
                            this.handleClientInputChange(0, e)
                          }
                          isLoading={isClientLoading}
                          value={
                            campaingValues?.clientId &&
                            (isClientSearch
                              ? searchClientData?.find((item) => {
                                  return (
                                    item?.value == campaingValues?.clientId
                                  );
                                })
                              : clientOptionsData?.find((item) => {
                                  return (
                                    item?.value == campaingValues?.clientId
                                  );
                                }))
                          }
                          // onChange={(e) => this.handleSelectCategoryChange(e)}
                          options={
                            isClientSearch
                              ? searchClientData
                              : clientOptionsData
                          }
                          // value={clientOptionsData?.find((item) => {
                          //   return item?.value == campaingValues?.clientId;
                          // })}
                          onChange={(option) => {
                            this.setState({
                              campaingValues: {
                                ...campaingValues,
                                clientId: option?.value,
                              },
                            });
                          }}
                          // options={clientOptionsData}
                        />
                        {errorClient ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorClient}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        className="teamsport-textfield national-select teamsport-text"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label"> Type </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Select Type"
                          value={typeOptions?.find((item) => {
                            return item?.label == campaingValues?.type;
                          })}
                          onChange={(option) => {
                            this.setState({
                              campaingValues: {
                                ...campaingValues,
                                type: option?.label,
                              },
                            });
                          }}
                          options={typeOptions}
                        />
                        {errorType ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorType}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      {/* <Grid
                      item
                      xs={6}
                      className="national-select teamsport-text"
                      style={{
                        marginTop: "15px",
                      }}
                    >
                      <div
                        style={{
                          marginBottom: "27px",
                          width: "33%",
                        }}
                      >
                        <label className="modal-label">Type</label>
                        <div>
                          {types?.map((obj, i) => (
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                marginTop: "10px",
                              }}
                              key={obj.id}
                            >
                              <input
                                type="checkbox"
                                style={{
                                  height: "18px",
                                  width: "18px",
                                  cursor: "pointer",
                                  marginRight: "12px",
                                }}
                                id={obj.name}
                                value={obj.name}
                                name={obj.name}
                                checked={checkedType.includes(obj.name)}
                                onChange={(e) =>
                                  this.handleTypeChange(e)
                                }
                              />
                              <label
                                htmlFor={obj.name}
                                style={{
                                  cursor: "pointer",
                                  fontWeight: "500",
                                  textTransform: "capitalize",
                                }}
                              >
                                {obj.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                      {errorType ? (
                        <p
                          className="errorText"
                          style={{ margin: "4px 0 0 0" }}
                        >
                          {errorType}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid> */}

                      <Grid
                        item
                        xs={6}
                        className="teamsport-textfield date-time-picker-wrap teamsport-text"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label"> Start Date </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            variant="inline"
                            inputVariant="outlined"
                            ampm={false}
                            value={
                              campaingValues?.startDate
                                ? parseISO(
                                    moment(campaingValues?.startDate)
                                      .tz(timezone)
                                      .format("YYYY-MM-DD")
                                  )
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                campaingValues: {
                                  ...campaingValues,
                                  startDate: e,
                                },
                              })
                            }
                            autoOk={true}
                            format="yyyy/MM/dd"
                            className="date-time-picker"
                          />
                        </LocalizationProvider>
                        {errorstartDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorstartDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    ></Grid> */}
                      <Grid
                        item
                        xs={6}
                        className="date-time-picker-wrap teamsport-text teamsport-textfield"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label"> End Date </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            variant="inline"
                            inputVariant="outlined"
                            ampm={false}
                            value={
                              campaingValues?.endDate
                                ? parseISO(
                                    moment(campaingValues?.endDate)
                                      .tz(timezone)
                                      .format("YYYY-MM-DD")
                                  )
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                campaingValues: {
                                  ...campaingValues,
                                  endDate: e,
                                },
                              })
                            }
                            onError={(newError) => {
                              this.setState({
                                errorendDate:
                                  newError === "minDate"
                                    ? "end date should be more than start date"
                                    : "",
                              });
                            }}
                            minDate={campaingValues?.startDate}
                            autoOk={true}
                            format="yyyy/MM/dd"
                            className="date-time-picker"
                          />
                        </LocalizationProvider>
                        {errorendDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorendDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
