import React from "react";
import CreateStates from "./CreateStates";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Button,
  TextField,
  Typography,
  InputAdornment,
  Box,
  Breadcrumbs,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import { Link } from "react-router-dom";
import { MdKeyboardBackspace } from "react-icons/md";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
import RateReviewIcon from "@mui/icons-material/RateReview";
import SearchIcons from "../../images/searchIcon.svg";
import CreateVariation from "./CreateVariation";

class States extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      states: [],
      allCountry: [],
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // searchInput: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      isVariationModalOpen: false,
      stateToSend: null,
      offset: 0,
      sportCount: null,
      search: "",
    };
  }

  componentDidMount() {
    this.fetchAllStates();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match !== this.props.match) {
      this.fetchAllStates();
    }
    if (prevState.offset !== this.state.offset) {
      this.fetchAllStates();
    }
  }

  async fetchAllStates() {
    let { rowPerPage, offset, search } = this.state;
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.state +
        `/country/${this.props.match.params.id}?limit=${rowPerPage}&offset=${offset}&search=${search}`
    );
    if (status === 200) {
      this.setState({
        states: data?.result?.rows,
        isLoading: false,
        sportCount: data?.result?.count,
      });
      // this.fetchAllCountry();
    }
  }

  // async fetchAllCountry() {
  //   const { status, data } = await axiosInstance.get(URLS.country);
  //   if (status === 200) {
  //     this.setState({ allCountry: data?.result?.rows });
  //   }
  // }

  getCountry = (id) => {
    let { allCountry } = this.state;
    let country = allCountry
      .filter((obj) => obj.id === id)
      .map((object) => object.country);
    return country;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllStates();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.state}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllStates();
        });
        this.setActionMessage(true, "Success", "State Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  backToNavigatePage = () => {
    this.props.navigate(`/countries`);
  };

  navigateToPlayers = (id, state) => () => {
    let countryid = this.props.match.params.id;
    let countryname = this.props.match.params.countryname;
    this.props.navigate(
      `/countries/cities/${countryid}/${id}/${countryname}/${state}`
    );
  };

  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      // offset
    } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  // handlePaginationButtonClick = (navDirection) => {
  //   let { currentPage, rowPerPage, states } = this.state;
  //   if (navDirection === "prev") {
  //     if (currentPage > 1) {
  //       this.setState({ currentPage: currentPage - 1 });
  //     }
  //   } else {
  //     if (currentPage < states?.length / rowPerPage)
  //       this.setState({ currentPage: currentPage + 1 });
  //   }
  // };
  handlePaginationButtonClick = (navDirection) => {
    let {
      currentPage,
      //  rowPerPage,
      offset,
    } = this.state;
    // if (navDirection === "prev") {
    //   if (currentPage > 1) {
    //     this.setState({ currentPage: currentPage - 1 });
    //   }
    // } else {
    //   if (currentPage < tracksDetails.length / rowPerPage)
    //     this.setState({ currentPage: currentPage + 1 });
    // }

    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  inputVariationModal = (id, state) => () => {
    this.setState({
      isVariationModalOpen: true,
      idToSend: id,
      stateToSend: state,
    });
  };
  toggleVariationModal = () => {
    this.setState({ isVariationModalOpen: !this.state.isVariationModalOpen });
  };
  // countryName = this.props?.history?.location?.search;

  render() {
    var {
      states,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      // searchInput,
      stateToSend,
      isVariationModalOpen,
      // idToSend,
      // offset,
      sportCount,
      search,
    } = this.state;
    const pageNumbers = [];
    if (sportCount > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = tracksDetails.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    // searchInput !== "" &&
    //   (states = states?.filter(
    //     (obj) =>
    //       obj?.state
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase()) ||
    //       obj?.variation
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase())
    //   ));

    let currentPageRow = states;

    // if (this.states?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = this.states.slice(indexOfFirstTodo, indexOfLastTodo);

    //   for (let i = 1; i <= Math.ceil(this.states.length / rowPerPage); i++) {
    //     pageNumbers.push(i);
    //   }
    // }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Master Data
                </Link>
                <Link underline="hover" color="inherit" to="/countries">
                  Country
                </Link>
                <Typography className="active_p">State</Typography>
              </Breadcrumbs>
            </Box>
            <Grid
              container
              direction="row"
              alignItems="center"
              style={{ marginBottom: "10px" }}
            >
              <Grid item xs={4}>
                <Button
                  className="admin-btn-back"
                  onClick={this.backToNavigatePage}
                >
                  <MdKeyboardBackspace />
                </Button>
                {/* <h3
                  className="text-left admin-page-heading"
                  style={{ margin: "5px 0px 0px 5px" }}
                >
                  States
                  {` ( ${this.getCountry(
                    Number(this.props.match.params.id)
                  )} )`}
                </h3> */}
                <Typography variant="h1" align="left">
                  States({this.props.match.params?.countryname})
                  {/* {` ( ${this.getCountry(
                    Number(this.props.match.params.id)
                  )} )`} */}
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{ display: "flex", justifyContent: "flex-end" }}
              >
                <TextField
                  className="textfield-tracks search-track"
                  style={{ width: "478px", color: "#D4D6D8" }}
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder="Search State"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                        {/* <SearchIcon /> */}
                      </InputAdornment>
                    ),
                  }}
                  // label="Search"
                  value={search}
                  onChange={(e) =>
                    this.setState({
                      ...this.state.search,
                      search: e.target.value,
                    })
                  }
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "10px 15px",
                    marginLeft: "10px",
                  }}
                  onClick={(e) => this.fetchAllStates(e)}
                >
                  Search
                </Button>
                {/* &nbsp;&nbsp;&nbsp; */}
                <div>
                  {/* <ButtonComponent
                    className="admin-btn-green"
                    onClick={this.inputModal(null, "create")}
                    color="primary"
                    value="Add New"
                  /> */}
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "11px 15px",
                      // marginTop: "5px",
                      marginLeft: "10px",
                    }}
                    onClick={this.inputModal(null, "create")}
                  >
                    Add New
                  </Button>
                </div>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && states?.length === 0 && <p>No Data Available</p>}
            {!isLoading && states?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Country</TableCell>
                        <TableCell>States</TableCell>
                        <TableCell>State Code</TableCell>
                        <TableCell>Variation</TableCell>
                        <TableCell>Update Required</TableCell>
                        <TableCell>Cities</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((state, i) => {
                        return (
                          <TableRow
                            key={i}
                            className="table-rows listTable-Row"
                          >
                            <TableCell>{state?.id}</TableCell>
                            <TableCell>
                              {/* {this.getCountry(state.countryId)} */}
                              {this.props.match.params?.countryname}
                            </TableCell>
                            <TableCell>{state?.state}</TableCell>
                            <TableCell>{state?.stateCode}</TableCell>
                            <TableCell>
                              {/* {variation !== ""
                                ? variation?.name && variation?.name
                                : ""} */}
                              {/* {state?.variation} */}
                              {/* <Button
                                onClick={this.inputVariationModal(
                                  state.id,
                                  // "edit",
                                  state.state
                                )}
                                // className="table-btn"
                                className="table-btn admin-btn-green"
                              >
                                View/Add variation
                              </Button> */}
                              <Button
                                variant="contained"
                                style={{
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  borderRadius: "8px",
                                  textTransform: "capitalize",
                                  padding: "8px 10px",
                                }}
                                onClick={this.inputVariationModal(
                                  state.id,
                                  // "edit",
                                  state.state
                                )}
                              >
                                Add/Edit variation
                              </Button>
                            </TableCell>
                            <TableCell>
                              {state.updateRequired === true ? "yes" : "no"}
                            </TableCell>
                            <TableCell>
                              <RateReviewIcon
                                onClick={this.navigateToPlayers(
                                  state.id,
                                  state?.state
                                )}
                                color="primary"
                                className="cursor iconBtn admin-btn-green"
                              />
                            </TableCell>
                            <TableCell>
                              {/* <EditIcon
                                onClick={this.inputModal(state.id, "edit")}
                                color="primary"
                                className="mr10 cursor iconBtn admin-btn-green"
                              /> */}
                              <Button
                                onClick={this.inputModal(state?.id, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              {/* <DeleteOutlineIcon
                                onClick={this.setItemToDelete(state.id)}
                                color="secondary"
                                className="cursor iconBtn admin-btn-orange"
                              /> */}
                              <Button
                                onClick={this.setItemToDelete(state?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                                className={
                                  states.length / rowPerPage > 1
                                    ? "btn-navigation"
                                    : "btn-navigation-disabled"
                                }
                                disabled={
                                  states.length / rowPerPage > 1 ? false : true
                                }
                                onClick={() =>
                                  this.handlePaginationButtonClick("prev")
                                }
                              >
                                <ReactSVG src={arrowLeft} />
                              </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={sportCount > 0 ? false : true}
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                                className={
                                  states?.length / rowPerPage > 1
                                    ? "btn-navigation"
                                    : "btn-navigation-disabled"
                                }
                                disabled={
                                  states?.length / rowPerPage > 1 ? false : true
                                }
                                onClick={() =>
                                  this.handlePaginationButtonClick("next")
                                }
                              >
                                <ReactSVG src={arrowRight} />
                              </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New States" : "Edit States"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateStates
                  type={this.state.type}
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  countryId={this.props.match.params?.id}
                  isEditMode={isEditMode}
                  fetchAllStates={this.afterChangeRefresh}
                />
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isVariationModalOpen}
              onClose={this.toggleVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {/* {!isEditMode ? "Create New Country" : "Edit Country"} */}
                  Variation Details
                  <span className="modal-country-name">{stateToSend}</span>
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleVariationModal}
                />
                <CreateVariation
                  inputModal={this.toggleVariationModal}
                  id={this.state?.idToSend}
                  // variation={variationToSend}
                  isEditMode={isEditMode}
                  fetchAllCountry={this.afterChangeRefresh}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default States;
