import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TableHead,
  TableRow,
  TableContainer,
  Table,
  TableCell,
  TableBody,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import moment from "moment";
// import { URLS } from "../../../library/common/constants";
import { Loader } from "../../../library/common/components";
import axiosInstance from "../../../helpers/Axios";
import TabHorses from "../../../images/sport_icons/tab_horse.svg";
import TabGreyhounds from "../../../images/sport_icons/tab_greyhounds.svg";
import TabHarness from "../../../images/sport_icons/tab_harness.svg";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import Bet365 from "../../../images/logo/bet365.png";
import LadBrokes from "../../../images/logo/ladbrokes.png";
import BetStar from "../../../images/logo/betstar.png";
import BookMaker from "../../../images/logo/bookmaker.png";
import Neds from "../../../images/logo/neds.png";
import PlayUp from "../../../images/logo/playup_.png";
// import palmerbet from "../../../images/logo/palmerbet.png";
// import vicbet from "../../../images/logo/vicebet.png";
import UniBet from "../../../images/logo/unibet.svg";
// import winningedge from "../../../images/logo/winningedge.png";
// import realbookie from "../../../images/logo/realbookie.png";
import Draftstars from "../../../images/logo/draftstart-thumb.svg";
import TopSport from "../../../images/logo/top-sport-thumb.svg";
import betFair from "../../../images/logo/betfair-thumb.svg";
import BlueBet from "../../../images/logo/BlueBet.png";
import BoomBet from "../../../images/logo/BoomBet.png";
import SouthernCrossBet from "../../../images/logo/SouthernCrossBet.png";
import PuntOnDogs from "../../../images/logo/Puntondogs.webp";
import BetRight from "../../../images/logo/BETRIGHT-icon.webp";
import EliteBet from "../../../images/logo/ELITEBET-icon.webp";
import GetSetBet from "../../../images/logo/GSB-icon.webp";
import { config } from "../../../helpers/config";

function OddsCollapase({
  selectedDate,
  selectedRaceType,
  selectedCountryType,
  isRefetch,
}) {
  const history = useNavigate();
  const [RaceType, setRaceType] = useState([
    {
      id: 1,
      title: "Horses",
      icon: TabHorses,
    },
    {
      id: 3,
      title: "Greyhounds",
      icon: TabGreyhounds,
    },
    {
      id: 2,
      title: "Harness",
      icon: TabHarness,
    },
  ]);
  const [eventList, seteventList] = useState([]);
  const [hourseeventList, sethourseeventList] = useState([]);
  const [greyhoundsList, setgreyhoundsList] = useState([]);
  const [harnessList, setharnessList] = useState([]);
  const [isYesterday, setisYesterday] = useState(false);
  const [isTRaceeventLoading, setisisTRaceeventLoading] = useState(false);
  const [pageHeadingData, setPageHeadingData] = useState([]);
  const [BookkeeperData, setBookKeeperData] = useState([]);
  let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const fetchAllEvents = async () => {
    setisisTRaceeventLoading(true);
    let date = selectedDate;
    try {
      const { status, data } = await axiosInstance.get(
        `/adminNotification/missed?date=${date}&timezone=${timezone}`
      );

      if (status === 200) {
        // seteventList(data.results);
        seteventList(data?.events);
        // Filter RaceType Hourse / GreyHounds / Harness
        let HourseEvent = data?.events?.filter((item) => {
          return item?.track?.sportId === 1;
        });
        let greyhoundsEvent = data?.events?.filter((item) => {
          return item?.track?.sportId === 3;
        });
        let harnessEvent = data?.events?.filter((item) => {
          return item?.track?.sportId === 2;
        });
        sethourseeventList(HourseEvent);
        setgreyhoundsList(greyhoundsEvent);
        setharnessList(harnessEvent);
        setisisTRaceeventLoading(false);
      }
    } catch (error) {
      setisisTRaceeventLoading(false);
    }
  };

  // const updateFixture = async (eventId, providerId, statusId) => {
  //   let params = {
  //     eventId: eventId,
  //     providerId: providerId,
  //     status: statusId,
  //   };
  //   try {
  //     const { status, data } = await axiosInstance.post(
  //       URLS.updateFixtures,
  //       params
  //     );
  //     if (status === 200) {
  //       setTimeout(() => {
  //         fetchAllEvents();
  //       }, 500);
  //     }
  //   } catch (err) {}
  // };

  //  Filter For Horse Aus/Nz and Intl Racing
  let newHorseEventList = {
    ausData: hourseeventList?.filter((item) => {
      return item?.track?.countryId == 13 || item?.track?.countryId == 157;
    }),
    intlData: hourseeventList?.filter((item) => {
      return item?.track?.countryId != 13 && item?.track?.countryId != 157;
    }),
  };

  //  Filter For GreyhoundsRaceData Aus/Nz and Intl Racing
  let newGreyhoundsEventList = {
    ausData: greyhoundsList?.filter((item) => {
      return item?.track?.countryId == 13 || item?.track?.countryId == 157;
    }),
    intlData: greyhoundsList?.filter((item) => {
      return item?.track?.countryId != 13 && item?.track?.countryId != 157;
    }),
  };

  //  Filter For HarnessRaceData Aus/Nz and Intl Racing
  let newHarnessEventList = {
    ausData: harnessList?.filter((item) => {
      return item?.track?.countryId == 13 || item?.track?.countryId == 157;
    }),
    intlData: harnessList?.filter((item) => {
      return item?.track?.countryId != 13 && item?.track?.countryId != 157;
    }),
  };

  // push All Racing Filterd Data in RaceType Object
  RaceType[0]["RaceData"] = newHorseEventList;
  RaceType[1]["RaceData"] = newGreyhoundsEventList;
  RaceType[2]["RaceData"] = newHarnessEventList;

  useEffect(() => {
    fetchAllEvents();
    fetchTableHeading();
    fetchBookKeeper();
    if (
      selectedDate == moment().utc().subtract(1, "days").format("YYYY-MM-DD")
    ) {
      setisYesterday(true);
    } else {
      setisYesterday(false);
    }
  }, [selectedDate, isRefetch]);

  const handleNavigate = (eventId) => {
    history(`/racing/odds/${eventId}`);
  };
  const handleCellColor = (item, apiProviderId) => {
    const raceCell = item?.bookKeepers?.filter((obj) => {
      return obj?.apiProviderId === apiProviderId;
    });

    // const raceCell2 = raceCell?.filter((obj) => {
    //   if (obj?.isOdd === true) {
    //     return true;
    //   } else return false;
    // });

    return raceCell?.[0]?.oddStatus === 1
      ? "fixture"
      : raceCell?.[0]?.oddStatus === 2
      ? "partialfixture"
      : "notfixture";

    // return raceCell2?.length > 0 ? "fixture" : "notfixture";
  };
  const handleIsLiveOdds = (item, apiProviderId) => {
    const raceCell = item?.bookKeepers?.filter((obj) => {
      return obj?.apiProviderId === apiProviderId;
    });
    return raceCell?.[0]?.isLive ? "live" : "";
  };

  const fetchTableHeading = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookkeeperproviders?SportId=1,2,3`
      );
      if (status === 200) {
        setPageHeadingData(data?.result);
      } else {
      }
    } catch (err) {}
  };

  const fetchBookKeeper = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/apiProviders/bookkeeperproviders`
      );
      if (status === 200) {
        setBookKeeperData(data?.result);
      } else {
      }
    } catch (err) {}
  };
  const oddsicon = (BookKeeperId) => {
    let icon = BookkeeperData?.filter(
      (obj) => obj?.BookKeeperId === BookKeeperId
    );
    let iconData = icon?.[0]?.BookKeeper;
    let longlogo = [3, 4, 8, 10, 15];
    return (
      <img
        className="square-bookmaker"
        src={
          iconData?.small_logo?.includes("uploads")
            ? config.mediaUrl + iconData?.small_logo
            : iconData?.small_logo
        }
        alt="Bookkeeper"
      />
    );
  };
  return (
    <>
      {isTRaceeventLoading ? (
        <Box style={{ paddingTop: "20px" }}>
          <Loader />
        </Box>
      ) : (
        <Box className="fixture-colleps-wrap">
          {RaceType?.map((item) => {
            return (
              <>
                {selectedRaceType?.includes(item?.id) ? (
                  <Box className="racing-colleps">
                    {(item?.RaceData?.ausData?.length > 0 &&
                      selectedCountryType?.includes("Aus/NZ")) ||
                    (item?.RaceData?.intlData?.length > 0 &&
                      selectedCountryType?.includes("Intl")) ? (
                      <Accordion defaultExpanded className="colleps-accordion">
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls="panel1a-content"
                          id="panel1a-header"
                        >
                          <img src={item?.icon} alt="icon" />
                          <Typography>{item?.title}</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Box className="accordion_details event">
                            <TableContainer className="fixture-table-wrap">
                              <Table>
                                <TableHead>
                                  <TableRow>
                                    <TableCell className="rtable-hc1"></TableCell>
                                    {pageHeadingData?.map((id) => (
                                      <TableCell className="rt-thead">
                                        {oddsicon(id?.BookKeeperId)}
                                      </TableCell>
                                    ))}
                                  </TableRow>
                                </TableHead>
                              </Table>
                            </TableContainer>
                            {item?.RaceData?.ausData?.length > 0 &&
                            selectedCountryType?.includes("Aus/NZ") ? (
                              <Box className="fixture-wrape">
                                <Typography className="country-title">
                                  Australia
                                </Typography>
                                <TableContainer className="fixture-table-wrap">
                                  <Table>
                                    <TableBody>
                                      <TableRow></TableRow>
                                      {!isTRaceeventLoading &&
                                        (item?.RaceData?.ausData?.length > 0 ? (
                                          item?.RaceData?.ausData?.map(
                                            (race, index) => {
                                              return (
                                                <>
                                                  <TableRow>
                                                    <TableCell
                                                      component="th"
                                                      onClick={() => {
                                                        handleNavigate(
                                                          race?.id
                                                        );
                                                      }}
                                                    >
                                                      {race?.track?.countryObj?.country_flag?.includes(
                                                        "uploads"
                                                      ) ? (
                                                        <img
                                                          src={
                                                            config?.mediaUrl +
                                                            race?.track
                                                              ?.countryObj
                                                              ?.country_flag
                                                          }
                                                          alt="Flag"
                                                          className="flag-icon"
                                                        />
                                                      ) : (
                                                        <img
                                                          src={
                                                            race?.track
                                                              ?.countryObj
                                                              ?.country_flag
                                                          }
                                                          alt="Flag"
                                                          className="flag-icon"
                                                        />
                                                      )}
                                                      <Typography variant="h6">
                                                        {race?.eventName}
                                                      </Typography>
                                                    </TableCell>
                                                    {/* {race?.providers?.map(
                                                      (providerItem) => {
                                                        return (
                                                          <>
                                                            <TableCell
                                                              className={
                                                                providerItem?.status ==
                                                                1
                                                                  ? "fixture"
                                                                  : providerItem?.status ==
                                                                    2
                                                                  ? "notfixture"
                                                                  : providerItem?.status ==
                                                                    3
                                                                  ? "ignore"
                                                                  : ""
                                                              }
                                                              onClick={() => {
                                                                if (
                                                                  providerItem?.status ==
                                                                    2 ||
                                                                  providerItem?.status ==
                                                                    3
                                                                ) {
                                                                  updateFixture(
                                                                    providerItem?.eventId,
                                                                    providerItem?.providerId,
                                                                    providerItem?.status ==
                                                                      2
                                                                      ? 3
                                                                      : 2
                                                                  );
                                                                }
                                                              }}
                                                            ></TableCell>
                                                          </>
                                                        );
                                                      }
                                                    )} */}
                                                    {pageHeadingData?.map(
                                                      (id) => (
                                                        <TableCell
                                                          className={`${handleCellColor(
                                                            race,
                                                            id?.ApiProviderId
                                                          )} ${handleIsLiveOdds(
                                                            race,
                                                            id?.ApiProviderId
                                                          )}`}
                                                        ></TableCell>
                                                      )
                                                    )}
                                                  </TableRow>
                                                </>
                                              );
                                            }
                                          )
                                        ) : (
                                          <TableRow
                                            style={{
                                              border: "transparent",
                                              height: "1px",
                                            }}
                                          >
                                            <TableCell
                                              style={{
                                                textAlign: "center",
                                              }}
                                              colSpan={21}
                                            >
                                              No Race Available
                                            </TableCell>
                                          </TableRow>
                                        ))}
                                    </TableBody>
                                  </Table>
                                </TableContainer>
                              </Box>
                            ) : (
                              ""
                            )}
                            {item?.RaceData?.intlData?.length > 0 &&
                            selectedCountryType?.includes("Intl") ? (
                              <Box className="fixture-wrape">
                                <Typography className="country-title">
                                  International
                                </Typography>
                                <TableContainer className="fixture-table-wrap">
                                  <Table>
                                    <TableBody>
                                      <TableRow></TableRow>
                                      {!isTRaceeventLoading &&
                                        (item?.RaceData?.intlData?.length >
                                        0 ? (
                                          item?.RaceData?.intlData?.map(
                                            (race, index) => {
                                              return (
                                                <>
                                                  <TableRow>
                                                    <TableCell
                                                      component="th"
                                                      onClick={() => {
                                                        handleNavigate(
                                                          race?.id
                                                        );
                                                      }}
                                                    >
                                                      {race?.track?.countryObj?.country_flag?.includes(
                                                        "uploads"
                                                      ) ? (
                                                        <img
                                                          src={
                                                            config?.mediaUrl +
                                                            race?.track
                                                              ?.countryObj
                                                              ?.country_flag
                                                          }
                                                          alt="Flag"
                                                          className="flag-icon"
                                                        />
                                                      ) : (
                                                        <img
                                                          src={
                                                            race?.track
                                                              ?.countryObj
                                                              ?.country_flag
                                                          }
                                                          alt="Flag"
                                                          className="flag-icon"
                                                        />
                                                      )}
                                                      <Typography variant="h6">
                                                        {race?.eventName}
                                                      </Typography>
                                                    </TableCell>
                                                    {pageHeadingData?.map(
                                                      (id) => (
                                                        <TableCell
                                                          className={`${handleCellColor(
                                                            race,
                                                            id?.ApiProviderId
                                                          )} ${handleIsLiveOdds(
                                                            race,
                                                            id?.ApiProviderId
                                                          )}`}
                                                        ></TableCell>
                                                      )
                                                    )}
                                                  </TableRow>
                                                </>
                                              );
                                            }
                                          )
                                        ) : (
                                          <TableRow
                                            style={{
                                              border: "transparent",
                                              height: "1px",
                                            }}
                                          >
                                            <TableCell
                                              style={{
                                                textAlign: "center",
                                              }}
                                              colSpan={21}
                                            >
                                              No Event Available
                                            </TableCell>
                                          </TableRow>
                                        ))}
                                    </TableBody>
                                  </Table>
                                </TableContainer>
                              </Box>
                            ) : (
                              ""
                            )}
                          </Box>
                        </AccordionDetails>
                      </Accordion>
                    ) : (
                      ""
                    )}
                  </Box>
                ) : (
                  ""
                )}
              </>
            );
          })}
        </Box>
      )}
    </>
  );
}

export default OddsCollapase;
