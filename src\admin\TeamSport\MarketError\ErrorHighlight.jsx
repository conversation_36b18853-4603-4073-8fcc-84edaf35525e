import React from "react";

class TableCellWithBTags extends React.Component {
  constructor(props) {
    super(props);
    this.contentRef = React.createRef();
  }

  componentDidMount() {
    this.attachEventListeners();
  }

  componentWillUnmount() {
    this.removeEventListeners();
  }

  attachEventListeners() {
    const bTags = this.contentRef.current.querySelectorAll("div.danger");

    for (let i = 0; i < bTags.length; i++) {
      bTags[i].addEventListener("click", this.handleBTagClick);
    }
  }

  removeEventListeners() {
    const bTags = this.contentRef.current.querySelectorAll("div.danger");
    for (let i = 0; i < bTags.length; i++) {
      bTags[i].removeEventListener("click", this.handleBTagClick);
    }
  }

  handleBTagClick = (event) => {
    const bTagContent = event.target.innerText;
    this.copyToClipboard(bTagContent);
  };

  copyToClipboard = (text) => {
    const tempInput = document.createElement("input");
    tempInput.value = text;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);
    alert("Copied to clipboard: " + text);
  };

  render() {
    return (
      <td
        ref={this.contentRef}
      // dangerouslySetInnerHTML={{
      //   __html: this.props.apiContent.replace(/\n/g, "<br /><br />"),
      // }}
      >
        {/* {this.props.apiContent.split("\n").map((paragraph, index) => (
          <React.Fragment key={index}>
            {index > 0 && <br />}
            {paragraph}
          </React.Fragment>
        ))} */}
        {this.props.apiContent}
      </td>
    );
  }
}

export default TableCellWithBTags;
