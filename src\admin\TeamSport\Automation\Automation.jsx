import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../../helpers/Axios";
import moment from "moment-timezone";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from '@mui/material/Pagination';
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

// import "../cricket.scss";
const CronJobOption = [
  {
    label: "active",
    value: 0,
  },
  {
    label: "completed",
    value: 1,
  },
];
class Automation extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      CronJobsList: [],
      CronJobType: "active",
      providersDetails: [],
    };
  }

  componentDidMount() {
    this.fetchAllCronJobs();
    this.fetchProviders();
  }
  //   componentDidUpdate(prevProps, prevState) {
  //     if (prevState.CronJobType !== this.state.CronJobType) {
  //       this.fetchAllCronJobs();
  //     }
  //     if (prevProps.match.path !== this.props.match.path) {
  //       this.fetchAllCronJobs();
  //       this.setState({
  //         CronJobType: "active",
  //       });
  //     }
  //   }
  componentDidUpdate(prevProps, prevState) {
    if (
      prevState.CronJobType !== this.state.CronJobType ||
      prevProps.match.path !== this.props.match.path
    ) {
      if (prevProps.match.path !== this.props.match.path) {
        this.setState(
          {
            CronJobType: "active",
          },
          () => {
            this.fetchAllCronJobs();
          }
        );
      } else if (this.state.CronJobType !== prevState.CronJobType) {
        this.fetchAllCronJobs();
      }
    }
  }
  async fetchAllCronJobs() {
    let { CronJobType } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `sync/queue?queueName=CricketOddChildSync&type=${CronJobType}`
        : this.props.match.path?.includes("rugbyleague")
          ? `sync/queue?queueName=RLOddChildSync&type=${CronJobType}`
          : this.props.match.path?.includes("rugbyunion")
            ? `sync/queue?queueName=RLOddChildSync&type=${CronJobType}`
            : this.props.match.path?.includes("basketball")
              ? `sync/queue?queueName=NBAOddChildSync&type=${CronJobType}`
              : this.props.match.path?.includes("afl")
                ? `sync/queue?queueName=AFLOddChildSync&type=${CronJobType}`
                : this.props.match.path?.includes("australianrules")
                  ? `sync/queue?queueName=AROddChildSync&type=${CronJobType}`
                  : this.props.match.path?.includes("golf")
                    ? `sync/queue?queueName=GolfOddChildSync&type=${CronJobType}`
                    : this.props.match.path?.includes("tennis")
                      ? `sync/queue?queueName=TennisOddChildSync&type=${CronJobType}`
                      : this.props.match.path?.includes("baseball")
                        ? `sync/queue?queueName=BaseballOddChildSync&type=${CronJobType}`
                        : this.props.match.path?.includes("icehockey")
                          ? `sync/queue?queueName=IceHockeyOddChildSync&type=${CronJobType}`
                          : this.props.match.path?.includes("boxing")
                            ? `sync/queue?queueName=BoxingOddChildSync&type=${CronJobType}`
                            : this.props.match.path?.includes("mma")
                              ? `sync/queue?queueName=MMAOddChildSync&type=${CronJobType}`
                              : this.props.match.path?.includes("soccer")
                                ? `sync/queue?queueName=SoccerOddChildSync&type=${CronJobType}`
                                : `sync/queue?queueName=RLOddChildSync&type=${CronJobType}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        // let fullData = []
        // let crondatas = data?.jobs?.map((item) => {
        //     let racecrondata = item?.data?.map((obj) => {

        //         let racename = axiosInstance.get(
        //             `events/runner/${obj?.raceId}`
        //         ).then((res) => {

        //             if (res?.status === 200) {
        //                 obj.raceName = res?.data?.data?.race?.raceName;

        //             } else {
        //                 return ""
        //             }
        //         }).catch(

        //         )
        //     })
        //     fullData.push(item)
        // })
        // setTimeout(() => {
        this.setState({
          // CronJobsList: fullData,
          CronJobsList: data?.jobs,
          isLoading: false,
        });
        // }, 4000);
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }
  fetchRaceName = (raceId) => {
    // let racename = axiosInstance.get(
    //     `events/runner/${raceId}`
    // ).then((res) => {
    //     if (res?.status === 200) {
    //         return res?.data?.data?.race?.raceName;
    //     } else {
    //         return ""
    //     }
    // }).catch(
    // )
  };
  bookmakerName = (bookmakerId) => {
    let { providersDetails } = this.state;
    let provider = providersDetails?.filter((item) => {
      return item?.apiProviderId === bookmakerId;
    });
    return provider?.[0]?.name;
  };
  fetchProviders = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeepers/`
      );
      if (status === 200) {
        let activeProvider = data?.result?.filter((item) => {
          return item?.status === "active";
        });
        this.setState({
          providersDetails: activeProvider,
        });
      }
    } catch (err) { }
  };
  render() {
    var { CronJobsList, CronJobType, isLoading } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {/* {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )} */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                      ? "Rugby League"
                      : this.props.match.path?.includes("rugbyunion")
                        ? "Rugby Union"
                        : this.props.match.path?.includes("basketball")
                          ? "Basketball"
                          : this.props.match.path?.includes("afl")
                            ? "American Football"
                            : this.props.match.path?.includes("australianrules")
                              ? "Australian Rules"
                              : this.props.match.path?.includes("golf")
                                ? "Golf"
                                : this.props.match.path?.includes("tennis")
                                  ? "Tennis"
                                  : this.props.match.path?.includes("baseball")
                                    ? "Baseball"
                                    : this.props.match.path?.includes("icehockey")
                                      ? "Ice Hockey"
                                      : this.props.match.path?.includes("boxing")
                                        ? "Boxing"
                                        : this.props.match.path?.includes("mma")
                                          ? "Mixed Martial Arts"
                                          : this.props.match.path?.includes("soccer")
                                            ? "Soccer"
                                            : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Automation</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Automation
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                  onChange={(e) => this.setState({ sportType: e.target.value })}
                  style={{ width: "20%", marginTop: "0px" }}
                >
                  <option value="">Select Category</option>
                  {CategoryData?.length > 0 &&
                    CategoryData?.map((obj, i) => (
                      <option key={i} value={obj?.id}>
                        {obj?.categoryName}
                      </option>
                    ))}
                </SelectBox> */}
                <Select
                  className="React cricket-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Cronjob Type"
                  value={CronJobOption?.find((item) => {
                    return item?.label == CronJobType;
                  })}
                  onChange={(e) =>
                    this.setState({
                      CronJobType: e.label,
                    })
                  }
                  options={CronJobOption}
                />
                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  // value={search}
                  onChange={(e) => {
                    // setSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  // onClick={() => fetchAnimal()}
                >
                  Search
                </Button> */}
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && CronJobsList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && CronJobsList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>CronJob Name</TableCell>
                        <TableCell>Race Id</TableCell>
                        <TableCell> Provider </TableCell>
                        {/* <TableCell> Race Name </TableCell> */}
                        <TableCell> Date </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {CronJobsList?.length > 0 ? (
                        CronJobsList?.map((item) => {
                          return item?.data?.data?.map((obj, index) => {
                            return (
                              <TableRow className="listTable-Row" key={index}>
                                <TableCell> {item?.id} </TableCell>
                                <TableCell>{item?.name}</TableCell>
                                <TableCell>{obj?.eventId}</TableCell>
                                <TableCell>
                                  {obj?.ApiProviderId
                                    ? this.bookmakerName(obj?.ApiProviderId)
                                    : ""}
                                </TableCell>
                                {/* <TableCell>{obj?.raceName}</TableCell> */}
                                <TableCell>
                                  {item?.timestamp
                                    ? moment(item?.timestamp).format(
                                      "DD-MM-YYYY hh:mm:ss a"
                                    )
                                    : ""}
                                </TableCell>
                              </TableRow>
                            );
                          });
                          //
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Automation;
