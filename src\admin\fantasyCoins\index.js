import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  //   InputAdornment,
  //   IconButton,
} from "@mui/material";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
// import SearchIcons from "../../images/searchIcon.svg";
import fantasyAxiosInstance from "../../helpers/Axios/fantasyAxios";
import { identifiers, URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import { config } from "../../helpers/config";
import FileUploader from "../../library/common/components/FileUploader";
import Pagination from "@mui/material/Pagination";
// import DeleteIcon from "@mui/icons-material/Delete";
// import Select, { components } from "react-select";
// import moment from "moment-timezone";
import "../tippingPremiumPrize/tippingPremiumPrize.scss";
// import _, { values } from "lodash";
import axios from "axios";
import { fetchFromStorage } from "../../library/utilities";

// let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

// const externalStatusOption = [
//   {
//     label: "All",
//     value: null,
//   },
//   {
//     label: "Active",
//     value: "active",
//   },
//   {
//     label: "Deleted",
//     value: "deleted",
//   },
// ];

class FantasyCoins extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      fantasyCoinsValues: {
        coins: "",
        coinPrice: "",
        coinName: "",
        coinImage: "",
        subTitle:"",
      },
      FantasyCoinsList: [],
      FantasyCoinsCount: 0,
      errorCoins: "",
      errorCoinPrice: "",
      errorCoinName: "",
      isSearch: "",
      selectedCoinsID: "",
      selectedExternalStatus: null,
      errorCreate: "",
      defaultImage: [],
      defaultUploadImage: "",
    };
  }

  componentDidMount() {
    // this.fetchTestimonial(0, "", null);
    this.fetchFantasyCoins(0);
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchFantasyCoins(offset);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchFantasyCoins(0);

      this.setState({
        offset: 0,
        currentPage: 1,
      });
    }
  }

  async fetchFantasyCoins(page) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await fantasyAxiosInstance.get(
        `/coins/get?limit=${rowPerPage}&offset=${page}`
      );
      if (status === 200) {
        this.setState({
          FantasyCoinsList: data?.result,
          isLoading: false,
          FantasyCoinsCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { fantasyCoinsValues } = this.state;
    let flag = true;
    if (fantasyCoinsValues?.coins === null) {
      flag = false;
      this.setState({
        errorCoins: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCoins: "",
      });
    }

    if (fantasyCoinsValues?.coinPrice === null) {
      flag = false;
      this.setState({
        errorCoinPrice: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCoinPrice: "",
      });
    }

    if (
      fantasyCoinsValues?.coinName?.trim() === "" ||
      fantasyCoinsValues?.coinName === null
    ) {
      flag = false;
      this.setState({
        errorCoinName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCoinName: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      const { fantasyCoinsValues, defaultImage, offset } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        coins: fantasyCoinsValues?.coins,
        coinPrice: fantasyCoinsValues?.coinPrice,
        coinName: fantasyCoinsValues?.coinName,
        subTitle:fantasyCoinsValues?.subTitle,
      };
      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            coinImage: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      }

      try {
        const { status, data } = await fantasyAxiosInstance.post(
          `coins/create `,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.fetchFantasyCoins(offset);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
          defaultImage: [],
          defaultUploadImage: "",
        });

        // this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const { fantasyCoinsValues, selectedCoinsID, defaultImage, offset } =
      this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        coins: fantasyCoinsValues?.coins,
        coinPrice: fantasyCoinsValues?.coinPrice,
        coinName: fantasyCoinsValues?.coinName,
        subTitle:fantasyCoinsValues?.subTitle,
      };

      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            coinImage: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          coinImage: fantasyCoinsValues?.coinImage,
        };
      }

      try {
        const { status, data } = await fantasyAxiosInstance.put(
          `/coins/update/${selectedCoinsID}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
          });
          this.fetchFantasyCoins(offset);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorCoins: "",
      errorCoinPrice: "",
      errorCoinName: "",
      errorCreate: "",
      defaultImage: [],
      defaultUploadImage: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        fantasyCoinsValues: {
          coins: item?.coins,
          coinPrice: item?.coinPrice,
          coinName: item?.coinName,
          coinImage: item?.coinImage,
          subTitle:item?.subTitle,
        },
        defaultUploadImage: item?.coinImage,
        isEditMode: true,
        selectedCoinsID: item?.id,
      });
    } else {
      this.setState({
        fantasyCoinsValues: {
          coins: "",
          coinPrice: "",
          coinName: "",
          coinImage: "",
          subTitle:"",
        },
        isEditMode: false,
      });
    }
  };

  //   handleFeatureLogoRemove = () => {
  //     const { defaultImage, defaultUploadImage } = this.state;
  //     {
  //       defaultImage?.length > 0
  //         ? this.setState({ defaultImage: [] })
  //         : defaultUploadImage &&
  //           defaultUploadImage !== "" &&
  //           this.setState({
  //             defaultUploadImage: "",
  //           });
  //     }
  //   };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };

  setMedia = async (files) => {
    const token = fetchFromStorage(identifiers.token);
    const formData = new FormData();
    formData.append("image", files);

    // changes URL after functionality implemented

    if (files !== undefined) {
      const { status, data } = await axios.post(
        `${config?.baseUrl}media`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token.access_token}`,
          },
        }
      );

      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }

    return false;
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { itemToDelete, offset } = this.state;

    try {
      const { status, data } = await fantasyAxiosInstance.delete(
        `/coins/delete/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchFantasyCoins(offset);
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= rowPerPage) {
        this.setState({
          offset: offset - rowPerPage,
          currentPage: currentPage - 1,
        });
      }
    } else {
      this.setState({
        offset: offset + rowPerPage,
        currentPage: currentPage + 1,
      });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      fantasyCoinsValues,
      FantasyCoinsList,
      FantasyCoinsCount,
      errorCoins,
      errorCoinPrice,
      errorCoinName,
      //   isSearch,
      //   selectedExternalStatus,
      errorCreate,
      defaultImage,
      defaultUploadImage,
    } = this.state;
    const pageNumbers = [];

    if (FantasyCoinsCount > 0) {
      for (let i = 1; i <= Math.ceil(FantasyCoinsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Fantasy Coins</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Fantasy Coins
                </Typography>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                {/* <Select
                  className="React teamsport-select external-select sort-select"
                  value={
                    selectedExternalStatus &&
                    externalStatusOption?.find(
                      (item) => item?.value === selectedExternalStatus
                    )
                  }
                  onChange={(e) => {
                    this.setState({
                      selectedExternalStatus: e?.value === 0 ? "" : e?.value,
                      currentPage: 1,
                      offset: 0,
                    });

                    this.fetchTestimonial(0, isSearch, e?.value);
                  }}
                  options={externalStatusOption}
                  classNamePrefix="select"
                  placeholder="Status"
                />

                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() =>
                    this.fetchTestimonial(
                      offset,
                      isSearch,
                      selectedExternalStatus
                    )
                  }
                >
                  Search
                </Button> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              {/* <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: this.state.isSortChange
                      ? "#4455c7"
                      : "rgba(0, 0, 0, 0.12)",
                    color: this.state.isSortChange
                      ? "#fff"
                      : "rgba(0, 0, 0, 0.26)",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  disabled={!this.state.isSortChange}
                  onClick={() => {
                    this.handleOrderChange();
                  }}
                >
                  Save Order
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid> */}
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && FantasyCoinsList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && FantasyCoinsList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>ID</TableCell>
                        <TableCell>Coins Image</TableCell>
                        <TableCell>Coin Name</TableCell>
                        <TableCell>Coins</TableCell>
                        <TableCell>Coin Price</TableCell>
                        <TableCell>subTitle</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>

                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {FantasyCoinsList?.map((item, index) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={index}
                          >
                            <TableCell> {item?.id} </TableCell>
                            <TableCell style={{ textTransform: "capitalize" }}>
                              {item?.coinImage ? (
                                <img
                                  src={
                                    item?.coinImage?.includes("uploads")
                                      ? config.mediaUrl + item?.coinImage
                                      : item?.coinImage
                                  }
                                  alt="icon"
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  style={{ textAlign: "center" }}
                                >
                                  -
                                </div>
                              )}
                            </TableCell>

                            <TableCell>{item?.coinName}</TableCell>
                            <TableCell>{item?.coins}</TableCell>
                            <TableCell>{item?.coinPrice}</TableCell>
                            <TableCell>{item?.subTitle ? item?.subTitle : ""}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                FantasyCoinsCount / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Fantasy Coins"
                    : "Edit Fantasy Coins"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Coins</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Coins"
                          value={fantasyCoinsValues?.coins}
                          onChange={(e) =>
                            this.setState({
                              fantasyCoinsValues: {
                                ...fantasyCoinsValues,
                                coins: e?.target?.value,
                              },
                              errorCoins: e?.target?.value ? "" : errorCoins,
                            })
                          }
                        />
                        {errorCoins ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorCoins}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Coins Price</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Coins Price"
                          value={fantasyCoinsValues?.coinPrice}
                          onChange={(e) =>
                            this.setState({
                              fantasyCoinsValues: {
                                ...fantasyCoinsValues,
                                coinPrice: e?.target?.value,
                              },
                              errorCoinPrice: e?.target?.value
                                ? ""
                                : errorCoinPrice,
                            })
                          }
                        />
                        {errorCoinPrice ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorCoinPrice}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Coin Name</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Coin Name"
                          value={fantasyCoinsValues?.coinName}
                          onChange={(e) =>
                            this.setState({
                              fantasyCoinsValues: {
                                ...fantasyCoinsValues,
                                coinName: e?.target?.value,
                              },
                              errorCoinName: e?.target?.value
                                ? ""
                                : errorCoinName,
                            })
                          }
                        />
                        {errorCoinName ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorCoinName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">SubTitle</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Coin Name"
                          value={fantasyCoinsValues?.subTitle}
                          onChange={(e) =>
                            this.setState({
                              fantasyCoinsValues: {
                                ...fantasyCoinsValues,
                                subTitle: e?.target?.value,
                              },
                            
                            })
                          }
                        />
                       
                      </Grid>

                      <div
                        className="blog-file-upload"
                        style={{ width: "100%", marginTop: "5px" }}
                      >
                        <label className="modal-label">
                          Fantasy Coins Image{" "}
                        </label>
                        <Box style={{ marginTop: "5px" }}>
                          <FileUploader
                            onDrop={(image) =>
                              this.handleFileUpload("defaultImage", image)
                            }
                          />
                        </Box>
                        <Box
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <div className="logocontainer">
                            {defaultImage?.length > 0
                              ? defaultImage?.map((file, index) => (
                                  <img
                                    className="auto-width"
                                    key={index}
                                    src={file.preview}
                                    alt="icon"
                                  />
                                ))
                              : defaultUploadImage &&
                                defaultUploadImage !== "" && (
                                  <img
                                    className="auto-width"
                                    src={
                                      defaultUploadImage?.includes("uploads")
                                        ? config.mediaUrl + defaultUploadImage
                                        : defaultUploadImage
                                    }
                                    alt="icon"
                                  />
                                )}
                          </div>
                          {/*} {(defaultImage?.length > 0 ||
                            (defaultUploadImage &&
                              defaultUploadImage !== "")) && (
                            <Box className="delete-icon-wrap">
                              <DeleteIcon
                                className="delete-icon"
                                onClick={() => this.handleFeatureLogoRemove()}
                                style={{ cursor: "pointer" }}
                              />
                            </Box>
                          )} */}
                        </Box>
                      </div>
                      <span style={{ fontSize: "12px", color: "red" }}>
                        {errorCreate ? errorCreate : ""}
                      </span>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default FantasyCoins;
