import React, { Component } from "react";
import { Grid, TextField, Box } from "@mui/material";
import "./editRunnerExtraInfo.scss";
import ButtonComponent from "../../../library/common/components/Button";
import axiosInstance from "../../../helpers/Axios";

class EditRunnerExtraInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      comment: "",
      sire: "",
      dam: "",
      type: "",
      age: "",
      prizeMoney: "",
      overallStarts: "",
      overallWins: "",
      overallSeconds: "",
      overallThirds: "",
      last6: "",
      trackStarts: "",
      trackWins: "",
      trackSeconds: "",
      trackThirds: "",
      distanceStarts: "",
      distanceWins: "",
      distanceSeconds: "",
      distanceThirds: "",
      trkDstStarts: "",
      trkDstWins: "",
      trkDstSeconds: "",
      trkDstThirds: "",
      firstUpStarts: "",
      firstUpWins: "",
      firstUpSeconds: "",
      firstUpThirds: "",
      secondUpStarts: "",
      secondUpWins: "",
      secondUpSeconds: "",
      secondUpThirds: "",
      firmStarts: "",
      firmWins: "",
      firmSeconds: "",
      firmThirds: "",
      goodStarts: "",
      goodWins: "",
      goodSeconds: "",
      goodThirds: "",
      softStarts: "",
      softWins: "",
      softSeconds: "",
      softThirds: "",
      heavyStarts: "",
      heavyWins: "",
      heavySeconds: "",
      heavyThirds: "",
      syntheticStarts: "",
      syntheticWins: "",
      syntheticSeconds: "",
      syntheticThirds: "",
    };
  }

  componentDidUpdate(prevProps) {
    const { runnerExtraInfoData } = this.props;
    //Typical usage, don't forget to compare the props
    if (this.props?.runnerExtraInfoData !== prevProps.runnerExtraInfoData) {
      this.setState({
        comment: runnerExtraInfoData?.entrant_comment,
        sire: runnerExtraInfoData?.sire?.name,
        dam: runnerExtraInfoData?.dam?.name,
        type: runnerExtraInfoData?.runner_info?.colour,
        age: runnerExtraInfoData?.runner_info?.age,
        prizeMoney: runnerExtraInfoData?.runner_info?.prize_money,
        overallStarts:
          runnerExtraInfoData?.past_runner_performances?.overall?.starts,
        overallWins:
          runnerExtraInfoData?.past_runner_performances?.overall?.wins,
        overallSeconds:
          runnerExtraInfoData?.past_runner_performances?.overall?.seconds,
        overallThirds:
          runnerExtraInfoData?.past_runner_performances?.overall?.thirds,
        last6: runnerExtraInfoData?.runner_info?.last_starts?.slice(
          runnerExtraInfoData?.runner_info?.last_starts?.length - 6
        ),
        trackStarts:
          runnerExtraInfoData?.past_runner_performances?.track?.starts,
        trackWins: runnerExtraInfoData?.past_runner_performances?.track?.wins,
        trackSeconds:
          runnerExtraInfoData?.past_runner_performances?.track?.seconds,
        trackThirds:
          runnerExtraInfoData?.past_runner_performances?.track?.thirds,
        distanceStarts:
          runnerExtraInfoData?.past_runner_performances?.distance?.starts,
        distanceWins:
          runnerExtraInfoData?.past_runner_performances?.distance?.wins,
        distanceSeconds:
          runnerExtraInfoData?.past_runner_performances?.distance?.seconds,
        distanceThirds:
          runnerExtraInfoData?.past_runner_performances?.distance?.thirds,
        trkDstStarts:
          runnerExtraInfoData?.past_runner_performances?.track_distance?.starts,
        trkDstWins:
          runnerExtraInfoData?.past_runner_performances?.track_distance?.wins,
        trkDstSeconds:
          runnerExtraInfoData?.past_runner_performances?.track_distance
            ?.seconds,
        trkDstThirds:
          runnerExtraInfoData?.past_runner_performances?.track_distance?.thirds,
        firstUpStarts:
          runnerExtraInfoData?.past_runner_performances?.first_up?.starts,
        firstUpWins:
          runnerExtraInfoData?.past_runner_performances?.first_up?.wins,
        firstUpSeconds:
          runnerExtraInfoData?.past_runner_performances?.first_up?.seconds,
        firstUpThirds:
          runnerExtraInfoData?.past_runner_performances?.first_up?.thirds,
        secondUpStarts:
          runnerExtraInfoData?.past_runner_performances?.second_up?.starts,
        secondUpWins:
          runnerExtraInfoData?.past_runner_performances?.second_up?.wins,
        secondUpSeconds:
          runnerExtraInfoData?.past_runner_performances?.second_up?.seconds,
        secondUpThirds:
          runnerExtraInfoData?.past_runner_performances?.second_up?.thirds,
        firmStarts: runnerExtraInfoData?.past_runner_performances?.firm?.starts,
        firmWins: runnerExtraInfoData?.past_runner_performances?.firm?.wins,
        firmSeconds:
          runnerExtraInfoData?.past_runner_performances?.firm?.seconds,
        firmThirds: runnerExtraInfoData?.past_runner_performances?.firm?.thirds,
        goodStarts: runnerExtraInfoData?.past_runner_performances?.good?.starts,
        goodWins: runnerExtraInfoData?.past_runner_performances?.good?.wins,
        goodSeconds:
          runnerExtraInfoData?.past_runner_performances?.good?.seconds,
        goodThirds: runnerExtraInfoData?.past_runner_performances?.good?.thirds,
        softStarts: runnerExtraInfoData?.past_runner_performances?.soft?.starts,
        softWins: runnerExtraInfoData?.past_runner_performances?.soft?.wins,
        softSeconds:
          runnerExtraInfoData?.past_runner_performances?.soft?.seconds,
        softThirds: runnerExtraInfoData?.past_runner_performances?.soft?.thirds,
        heavyStarts:
          runnerExtraInfoData?.past_runner_performances?.heavy?.starts,
        heavyWins: runnerExtraInfoData?.past_runner_performances?.heavy?.wins,
        heavySeconds:
          runnerExtraInfoData?.past_runner_performances?.heavy?.seconds,
        heavyThirds:
          runnerExtraInfoData?.past_runner_performances?.heavy?.thirds,
        syntheticStarts:
          runnerExtraInfoData?.past_runner_performances?.synthetic?.starts,
        syntheticWins:
          runnerExtraInfoData?.past_runner_performances?.synthetic?.wins,
        syntheticSeconds:
          runnerExtraInfoData?.past_runner_performances?.synthetic?.seconds,
        syntheticThirds:
          runnerExtraInfoData?.past_runner_performances?.synthetic?.thirds,
      });
    }
  }
  handleSave = async () => {
    const { runnerExtraInfoData, runnerInfoData, previousRuns } = this.props;
    const {
      overallStarts,
      overallWins,
      overallSeconds,
      overallThirds,
      trackStarts,
      trackWins,
      trackSeconds,
      trackThirds,
      distanceStarts,
      distanceWins,
      distanceSeconds,
      distanceThirds,
      trkDstStarts,
      trkDstWins,
      trkDstSeconds,
      trkDstThirds,
      firstUpStarts,
      firstUpWins,
      firstUpSeconds,
      firstUpThirds,
      secondUpStarts,
      secondUpWins,
      secondUpSeconds,
      secondUpThirds,
      firmStarts,
      firmWins,
      firmSeconds,
      firmThirds,
      goodStarts,
      goodWins,
      goodSeconds,
      goodThirds,
      softStarts,
      softWins,
      softSeconds,
      softThirds,
      heavyStarts,
      heavyWins,
      heavySeconds,
      heavyThirds,
      syntheticStarts,
      syntheticWins,
      syntheticSeconds,
      syntheticThirds,
      age,
      type,
      prizeMoney,
      last6,
      dam,
      comment,
      sire,
    } = this.state;
    // this.setState({ isLoading: true });
    const info = {
      dam: { name: dam ? dam : "" },
      entrant_comment: comment ? comment : "",
      entrant_comment_alternative: comment ? comment : "",
      entrant_id: runnerExtraInfoData?.entrant_id,
      owners_info: { name: runnerExtraInfoData?.owners_info?.names },
      past_runner_performances: {
        overall: {
          starts: overallStarts ? overallStarts : "",
          wins: overallWins ? overallWins : "",
          seconds: overallSeconds ? overallSeconds : "",
          thirds: overallThirds ? overallThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.overall?.placings,
          // runnerExtraInfoData?.past_runner_performances?.overall?.placings,
        },
        track: {
          starts: trackStarts ? trackStarts : "",
          wins: trackWins ? trackWins : "",
          seconds: trackSeconds ? trackSeconds : "",
          thirds: trackThirds ? trackThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.track?.placings,
        },
        distance: {
          starts: distanceStarts ? distanceStarts : "",
          wins: distanceWins ? distanceWins : "",
          seconds: distanceSeconds ? distanceSeconds : "",
          thirds: distanceThirds ? distanceThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.distance?.placings,
        },
        track_distance: {
          starts: trkDstStarts ? trkDstStarts : "",
          wins: trkDstWins ? trkDstWins : "",
          seconds: trkDstSeconds ? trkDstSeconds : "",
          thirds: trkDstThirds ? trkDstThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.track_distance
              ?.placings,
        },
        first_up: {
          starts: firstUpStarts ? firstUpStarts : "",
          wins: firstUpWins ? firstUpWins : "",
          seconds: firstUpSeconds ? firstUpSeconds : "",
          thirds: firstUpThirds ? firstUpThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.first_up?.placings,
        },
        second_up: {
          starts: secondUpStarts ? secondUpStarts : "",
          wins: secondUpWins ? secondUpWins : "",
          seconds: secondUpSeconds ? secondUpSeconds : "",
          thirds: secondUpThirds ? secondUpThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.second_up?.placings,
        },
        firm: {
          starts: firmStarts ? firmStarts : "",
          wins: firmWins ? firmWins : "",
          seconds: firmSeconds ? firmSeconds : "",
          thirds: firmThirds ? firmThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.firm?.placings,
        },
        good: {
          starts: goodStarts ? goodStarts : "",
          wins: goodWins ? goodWins : "",
          seconds: goodSeconds ? goodSeconds : "",
          thirds: goodThirds ? goodThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.good?.placings,
        },
        soft: {
          starts: softStarts ? softStarts : "",
          wins: softWins ? softWins : "",
          seconds: softSeconds ? softSeconds : "",
          thirds: softThirds ? softThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.soft?.placings,
        },
        heavy: {
          starts: heavyStarts ? heavyStarts : "",
          wins: heavyWins ? heavyWins : "",
          seconds: heavySeconds ? heavySeconds : "",
          thirds: heavyThirds ? heavyThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.heavy?.placings,
        },
        synthetic: {
          starts: syntheticStarts ? syntheticStarts : "",
          wins: syntheticWins ? syntheticWins : "",
          seconds: syntheticSeconds ? syntheticSeconds : "",
          thirds: syntheticThirds ? syntheticThirds : "",
          placings:
            runnerExtraInfoData?.past_runner_performances?.synthetic?.placings,
        },
        dead: {
          // starts: "",
          // wins: "",
          // seconds: "",
          // thirds: "",
          // placings: "",
        },
        slow: {
          // starts: "",
          // wins: "",
          // seconds: "",
          // thirds: "",
          // placings: "",
        },
      },
      previous_runs: previousRuns ? previousRuns : "",
      runner_info: {
        name: runnerExtraInfoData?.runner_info?.name,
        barrier: runnerExtraInfoData?.runner_info?.barrier,
        status: runnerExtraInfoData?.runner_info?.status,
        age: age ? age : "",
        colour: type ? type : "",
        prize_money: prizeMoney ? prizeMoney : "",
        best_time: runnerExtraInfoData?.runner_info?.best_time,
        last_starts: last6 ? last6 : "",
        training_location: runnerExtraInfoData?.runner_info?.training_location,
        sex: runnerExtraInfoData?.runner_info?.sex,
      },
      sire: { name: sire ? sire : "" },
      trainer_info: {
        name: runnerExtraInfoData?.trainer_info?.name,
        location: runnerExtraInfoData?.trainer_info?.location,
      },
    };
    const newInfo = JSON.stringify(info);

    let payload = {
      RacingParticipantId: runnerInfoData?.RacingParticipantId,
      raceId: this.props?.runnerInfoData?.raceId,
      info: newInfo,
    };

    try {
      const method = "put";
      const url = `/events/runnerExtraInfo/${this.props?.id}`;

      const { status } = await axiosInstance[method](url, payload);
      if (status === 200) {
        this.setState({ isLoading: false });
        this.props.inputModal();
        this.props.fetchAllRunnersInfo();
      }
    } catch (err) { }
  };
  render() {
    const {
      isLoading,
      comment,
      sire,
      dam,
      type,
      age,
      prizeMoney,
      overallStarts,
      overallWins,
      overallSeconds,
      overallThirds,
      last6,
      trackStarts,
      trackWins,
      trackSeconds,
      trackThirds,
      distanceStarts,
      distanceWins,
      distanceSeconds,
      distanceThirds,
      trkDstStarts,
      trkDstWins,
      trkDstSeconds,
      trkDstThirds,
      firstUpStarts,
      firstUpWins,
      firstUpSeconds,
      firstUpThirds,
      secondUpStarts,
      secondUpWins,
      secondUpSeconds,
      secondUpThirds,
      firmStarts,
      firmWins,
      firmSeconds,
      firmThirds,
      goodStarts,
      goodWins,
      goodSeconds,
      goodThirds,
      softStarts,
      softWins,
      softSeconds,
      softThirds,
      heavyStarts,
      heavyWins,
      heavySeconds,
      heavyThirds,
      syntheticStarts,
      syntheticWins,
      syntheticSeconds,
      syntheticThirds,
    } = this.state;
    // const { runnerExtraInfoData, runnerInfoData } = this.props;

    return (
      <>
        <Box className="runnerInfo-contanier">
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Runner Comments</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="Runner Comments"
                value={comment}
                onChange={(e) => this.setState({ comment: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Sire</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="Sire"
                value={sire}
                onChange={(e) => this.setState({ sire: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Dam</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="Dam"
                value={dam}
                onChange={(e) => this.setState({ dam: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Type</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="Type"
                value={type}
                onChange={(e) => this.setState({ type: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Age</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="Age"
                value={age}
                onChange={(e) => this.setState({ age: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Prize Money</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="Prize Money"
                value={prizeMoney}
                onChange={(e) => this.setState({ prizeMoney: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Overall</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={overallStarts}
                onChange={(e) =>
                  this.setState({ overallStarts: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={overallWins}
                onChange={(e) => this.setState({ overallWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={overallSeconds}
                onChange={(e) =>
                  this.setState({ overallSeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={overallThirds}
                onChange={(e) =>
                  this.setState({ overallThirds: e.target.value })
                }
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Last 6</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="Last 6"
                value={last6}
                onChange={(e) => this.setState({ last6: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Track</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={trackStarts}
                onChange={(e) => this.setState({ trackStarts: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={trackWins}
                onChange={(e) => this.setState({ trackWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={trackSeconds}
                onChange={(e) =>
                  this.setState({ trackSeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={trackThirds}
                onChange={(e) => this.setState({ trackThirds: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Distance</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={distanceStarts}
                onChange={(e) =>
                  this.setState({ distanceStarts: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={distanceWins}
                onChange={(e) =>
                  this.setState({ distanceWins: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={distanceSeconds}
                onChange={(e) =>
                  this.setState({ distanceSeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={distanceThirds}
                onChange={(e) =>
                  this.setState({ distanceThirds: e.target.value })
                }
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Trk/Dst</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={trkDstStarts}
                onChange={(e) =>
                  this.setState({ trkDstStarts: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={trkDstWins}
                onChange={(e) => this.setState({ trkDstWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={trkDstSeconds}
                onChange={(e) =>
                  this.setState({ trkDstSeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={trkDstThirds}
                onChange={(e) =>
                  this.setState({ trkDstThirds: e.target.value })
                }
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">1st Up</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={firstUpStarts}
                onChange={(e) =>
                  this.setState({ firstUpStarts: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={firstUpWins}
                onChange={(e) => this.setState({ firstUpWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={firstUpSeconds}
                onChange={(e) =>
                  this.setState({ firstUpSeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={firstUpThirds}
                onChange={(e) =>
                  this.setState({ firstUpThirds: e.target.value })
                }
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">2nd Up</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={secondUpStarts}
                onChange={(e) =>
                  this.setState({ secondUpStarts: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={secondUpWins}
                onChange={(e) =>
                  this.setState({ secondUpWins: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={secondUpSeconds}
                onChange={(e) =>
                  this.setState({ secondUpSeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={secondUpThirds}
                onChange={(e) =>
                  this.setState({ secondUpThirds: e.target.value })
                }
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Firm</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={firmStarts}
                onChange={(e) => this.setState({ firmStarts: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={firmWins}
                onChange={(e) => this.setState({ firmWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={firmSeconds}
                onChange={(e) => this.setState({ firmSeconds: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={firmThirds}
                onChange={(e) => this.setState({ firmThirds: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Good</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={goodStarts}
                onChange={(e) => this.setState({ goodStarts: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={goodWins}
                onChange={(e) => this.setState({ goodWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={goodSeconds}
                onChange={(e) => this.setState({ goodSeconds: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={goodThirds}
                onChange={(e) => this.setState({ goodThirds: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Soft</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={softStarts}
                onChange={(e) => this.setState({ softStarts: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={softWins}
                onChange={(e) => this.setState({ softWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={softSeconds}
                onChange={(e) => this.setState({ softSeconds: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={softThirds}
                onChange={(e) => this.setState({ softThirds: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Heavy</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={heavyStarts}
                onChange={(e) => this.setState({ heavyStarts: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={heavyWins}
                onChange={(e) => this.setState({ heavyWins: e.target.value })}
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={heavySeconds}
                onChange={(e) =>
                  this.setState({ heavySeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={heavyThirds}
                onChange={(e) => this.setState({ heavyThirds: e.target.value })}
              />
            </Grid>
          </Grid>
          <Grid item xs={12} className="runnerInfo">
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label">Synthetic</label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="starts"
                value={syntheticStarts}
                onChange={(e) =>
                  this.setState({ syntheticStarts: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="wins"
                value={syntheticWins}
                onChange={(e) =>
                  this.setState({ syntheticWins: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="second"
                value={syntheticSeconds}
                onChange={(e) =>
                  this.setState({ syntheticSeconds: e.target.value })
                }
              />
            </Grid>
            <Grid item xs={6} className="runnerInfo-text">
              <label className="modal-label mb-20"></label>
              <TextField
                className="textfield-tracks"
                variant="outlined"
                color="primary"
                size="small"
                placeholder="thirds"
                value={syntheticThirds}
                onChange={(e) =>
                  this.setState({ syntheticThirds: e.target.value })
                }
              />
            </Grid>
          </Grid>
          <Grid container>
            <Grid item xs={3}>
              <div style={{ margin: "20px 0px", display: "flex" }}>
                <ButtonComponent
                  className="mt-3 admin-btn-green"
                  onClick={this.handleSave}
                  color="primary"
                  value={!isLoading ? "Update" : "Loading..."}
                  disabled={isLoading}
                  style={{ minWidth: "auto" }}
                />

                <ButtonComponent
                  onClick={this.props?.inputModal}
                  className="mr-lr-30"
                  value="Back"
                  style={{ minWidth: "auto" }}
                />
              </div>
            </Grid>
          </Grid>
        </Box>
      </>
    );
  }
}

export default EditRunnerExtraInfo;
