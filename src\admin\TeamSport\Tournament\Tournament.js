import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
  Checkbox,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import DateFnsUtils from "@date-io/date-fns";
import moment from "moment-timezone";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../teamsport.scss";
import CreateTournamentVariation from "./createTournamentVariation/CreateTournamentVariation";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";
import { URLS } from "../../../library/common/constants";
import { ReactComponent as Unchecked } from "../../../images/uncheck-star.svg";
import { ReactComponent as Checked } from "../../../images/check-star.svg";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const TournamentTypeOption = [
  { label: "International", value: "international" },
  { label: "League", value: "league" },
  { label: "Domestic", value: "domestic" },
  { label: "Women", value: "women" },
];
const TournamentFormatOptaion = [
  { label: "T20", value: "T20" },
  { label: "T10", value: "T10" },
  { label: "Test", value: "Test" },
  { label: "Odi", value: "Odi" },
  { label: "HUN", value: "HUN" },
];
class Tournament extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      TournamentValues: {
        TournamentName: "",
        rapidTournamentId: "",
        RLCategoryId: "",
        CricketCategoryId: "",
        NBACategoryId: "",
        AFLCategoryId: "",
        ARCategoryId: "",
        GolfCategoryId: "",
        TennisCategoryId: "",
        BaseballCategoryId: "",
        IceHockeyCategoryId: "",
        BoxingCategoryId: "",
        MMACategoryId: "",
        SoccerCategoryId: "",
        id: "",
        SportKey: "",
        gender: "",
      },
      selectCategory: "",
      CategoryPage: 0,
      categoryData: [],
      externalCategoryData: [],
      categoryCount: 0,
      TournamentCount: 0,
      TournamentList: [],
      SelectedTournamentList: [],
      errorRequire: "",
      errorCategory: "",
      searchCategory: [],
      searchCategoryCount: 0,
      searchCategoryPage: 0,
      isCategorySearch: "",
      search: "",
      isVariationModalOpen: false,
      errorGender: "",
      uploadLogo: "",
      flag: [],
      uniqueTournamentCount: 0,
      uniqueTournamentPage: 0,
      isuniqueTournamentSearch: "",
      searchuniqueTournamentCount: 0,
      searchuniqueTournamentPage: 0,
      searchuniqueTournamentData: [],
      uniqueTournament: 0,
      uniqueTournamentData: [],
      isuniqueTournamentLoading: false,
      tournamentStartDate: null,
      tournamentEndDate: null,
      tournamentType: null,
      tournamentFormat: null,
      errorTournamentType: "",
      errorTournamentFormat: "",
      checkBoxValues: [],
    };
  }

  componentDidMount() {
    this.fetchAllCategory(0, "ExternalCategory");
    this.fetchAllTournament(0, "");
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllTournament(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllTournament(0, "");
      this.fetchAllCategory(0, "ExternalCategory");
      this.setState({
        offset: 0,
        selectCategory: "",
        currentPage: 1,
        externalCategoryData: [],
        CategoryPage: 0,
        search: "",
      });
    }
  }
  async fetchAllTournament(page, searchvalue) {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/tournament?limit=${rowPerPage}&offset=${page}&SportId=12&search=${searchvalue}`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/tournament?limit=${rowPerPage}&offset=${page}&SportId=13&search=${searchvalue}`
        : this.props.match.path?.includes("basketball")
        ? `nba/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("afl")
        ? `afl/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("golf")
        ? `golf/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("mma")
        ? `mma/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/tournament?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
        : `rls/tournament?limit=${rowPerPage}&offset=${page}&SportId=14`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          TournamentList: data?.result?.rows,
          isLoading: false,
          TournamentCount: data?.result?.count,
        });
        let newdata = [];
        const filteredData = data?.result?.rows?.filter(
          (item) => item?.isFeatured === true
        );
        let categories = filteredData?.map((item) => {
          newdata.push(item?.id);
        });
        this.setState({ checkBoxValues: newdata });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  async fetchAllCategory(CategoryPage, type) {
    let { rowPerPage, offset } = this.state;
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/category?limit=20&offset=${CategoryPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/category?limit=20&offset=${CategoryPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/category?limit=20&offset=${CategoryPage}`
      : `rls/category?limit=20&offset=${CategoryPage}&SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let categories = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        if (type === "ExternalCategory") {
          let filterdata = newdata?.filter((item) => item?.value !== 0);
          let mergeData = _.unionBy(
            this.state?.externalCategoryData,
            filterdata
          );
          const sortedData = mergeData?.sort((a, b) => {
            return a?.label.localeCompare(b?.label);
          });
          let alldatas = sortedData?.unshift({
            label: "All Categories",
            value: 0,
          });
          let finalData = _.uniqBy(sortedData, function (e) {
            return e.value;
          });
          this.setState({
            externalCategoryData: finalData,
            categoryCount: Math.ceil(count),
          });
        }

        if (type === "ModalCategory") {
          let filterData = _.unionBy(this.state?.categoryData, newdata);
          const sortedData = filterData?.sort((a, b) => {
            return a?.label.localeCompare(b?.label);
          });
          let finalData = _.uniqBy(sortedData, function (e) {
            return e.value;
          });
          finalData = finalData?.filter((item) => item?.value !== 0);
          this.setState({
            categoryData: finalData,
            categoryCount: Math.ceil(count),
          });
        }
      } else {
      }
    } catch (error) {}
  }

  handleOnScrollBottomCategory = (e, type) => {
    let {
      categoryCount,
      CategoryPage,
      isCategorySearch,
      searchCategoryCount,
      searchCategoryPage,
    } = this.state;
    if (
      isCategorySearch !== "" &&
      searchCategoryCount !== Math.ceil(searchCategoryPage / 20 + 1)
    ) {
      this.handleCategoryInputChange(searchCategoryPage + 20, isCategorySearch);
      this.setState({
        searchCategoryPage: searchCategoryPage + 20,
      });
    } else {
      if (
        categoryCount !==
          (categoryCount == 1 ? 1 : Math.ceil(CategoryPage / 20)) &&
        isCategorySearch == ""
      ) {
        this.fetchAllCategory(CategoryPage + 20, type);
        this.setState({
          CategoryPage: CategoryPage + 20,
        });
      }
    }
  };
  handleCategoryInputChange = _.debounce((page, value) => {
    if (value === "") return;
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/category?limit=20&offset=${page}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/category?limit=20&offset=${page}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/category?limit=20&offset=${page}&search=${value}`
      : `rls/category?limit=20&offset=${page}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchCategory, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Categories",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchCategory: finalData,
          searchCategoryCount: Math.ceil(count),
          isCategorySearch: value,
        });
      }
    });
  }, 300);
  fetchModalSelectedCategory = (CategoryId, CategoryName) => {
    let seletedCategory = [
      {
        label: CategoryName,
        value: CategoryId,
      },
    ];

    this.setState({
      categoryData: CategoryId ? seletedCategory : this.state.categoryData,
    });
  };

  handleSelectCategoryChange = async (e) => {
    this.setState({
      selectCategory: e,
      isLoading: true,
    });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/tournament/category/${e}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/tournament/category/${e}?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/tournament/category/${e}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/tournament/category/${e}`
        : this.props.match.path?.includes("afl")
        ? `afl/tournament/category/${e}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/tournament/category/${e}`
        : this.props.match.path?.includes("golf")
        ? `golf/tournament/category/${e}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/tournament/category/${e}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/tournament/category/${e}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/tournament/category/${e}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/tournament/category/${e}`
        : this.props.match.path?.includes("mma")
        ? `mma/tournament/category/${e}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/tournament/category/${e}`
        : `rls/tournament/category/${e}?SportId=14`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          SelectedTournamentList: data?.result,
          isLoading: false,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };
  handalValidate = () => {
    let { TournamentValues, tournamentType, tournamentFormat } = this.state;
    let propsMatch = this.props.match.path;

    let flag = true;
    if (
      TournamentValues?.TournamentName?.trim() === "" ||
      !TournamentValues?.TournamentName
    ) {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    if (TournamentValues?.gender === "") {
      flag = false;
      this.setState({
        errorGender: "This field is mandatory",
      });
    } else {
      this.setState({
        errorGender: "",
      });
    }

    if (propsMatch?.includes("cricket")) {
      if (tournamentType === null) {
        flag = false;
        this.setState({
          errorTournamentType: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournamentType: "",
        });
      }
      if (tournamentFormat === null) {
        flag = false;
        this.setState({
          errorTournamentFormat: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournamentFormat: "",
        });
      }
    }
    // if (this.props.match.path?.includes("cricket")) {
    //   if (TournamentValues?.CricketCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("basketball")) {
    //   if (TournamentValues?.NBACategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("afl")) {
    //   if (TournamentValues?.AFLCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("australianrules")) {
    //   if (TournamentValues?.ARCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("golf")) {
    //   if (TournamentValues?.GolfCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("tennis")) {
    //   if (TournamentValues?.TennisCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("baseball")) {
    //   if (TournamentValues?.BaseballCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("icehockey")) {
    //   if (TournamentValues?.IceHockeyCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("boxing")) {
    //   if (TournamentValues?.BoxingCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("mma")) {
    //   if (TournamentValues?.MMACategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else if (this.props.match.path?.includes("soccer")) {
    //   if (TournamentValues?.SoccerCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // } else {
    //   if (TournamentValues?.RLCategoryId === "") {
    //     flag = false;
    //     this.setState({
    //       errorCategory: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorCategory: "",
    //     });
    //   }
    // }

    return flag;
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handleSave = async () => {
    const { flag, selectCategory, tournamentType, tournamentFormat } =
      this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      try {
        let payload = {
          name: this.state?.TournamentValues?.TournamentName,
          ...(this.props.match.path?.includes("cricket") ||
          this.props.match.path?.includes("soccer")
            ? {
                Scd: this.state?.TournamentValues?.rapidTournamentId
                  ? this.state?.TournamentValues?.rapidTournamentId
                  : null,
              }
            : {
                rapidTournamentId: this.state?.TournamentValues
                  ?.rapidTournamentId
                  ? this.state?.TournamentValues?.rapidTournamentId
                  : null,
              }),
          sportKey: this.state?.TournamentValues?.SportKey,
          gender: this.state?.TournamentValues?.gender,
          SportId: this.props.match.path?.includes("cricket")
            ? 4
            : this.props.match.path?.includes("rugbyleague")
            ? 12
            : this.props.match.path?.includes("rugbyunion")
            ? 13
            : this.props.match.path?.includes("basketball")
            ? 10
            : this.props.match.path?.includes("afl")
            ? 15
            : this.props.match.path?.includes("australianrules")
            ? 9
            : this.props.match.path?.includes("golf")
            ? 16
            : this.props.match.path?.includes("tennis")
            ? 7
            : this.props.match.path?.includes("baseball")
            ? 11
            : this.props.match.path?.includes("icehockey")
            ? 17
            : this.props.match.path?.includes("boxing")
            ? 6
            : this.props.match.path?.includes("mma")
            ? 5
            : this.props.match.path?.includes("soccer")
            ? 8
            : 14,
        };
        if (this.props.match.path?.includes("cricket")) {
          payload = {
            ...payload,
            CricketCategoryId: this.state?.TournamentValues?.CricketCategoryId
              ? this.state?.TournamentValues?.CricketCategoryId
              : null,
            uniqueTournamentId:
              this.state.uniqueTournament === 0
                ? null
                : this.state.uniqueTournament,
            tournamentStartTime: this.state?.tournamentStartDate
              ? moment(this.state?.tournamentStartDate)
                  .tz(timezone)
                  .format("YYYY-MM-DD")
              : null,
            tournamentEndTime: this.state?.tournamentEndDate
              ? moment(this.state?.tournamentEndDate)
                  .tz(timezone)
                  .format("YYYY-MM-DD")
              : null,
            type: tournamentType ? tournamentType : null,
            options: tournamentFormat ? tournamentFormat : null,
          };
        } else if (this.props.match.path?.includes("basketball")) {
          payload = {
            ...payload,
            NBACategoryId: this.state?.TournamentValues?.NBACategoryId
              ? this.state?.TournamentValues?.NBACategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("afl")) {
          payload = {
            ...payload,
            AFLCategoryId: this.state?.TournamentValues?.AFLCategoryId
              ? this.state?.TournamentValues?.AFLCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("australianrules")) {
          payload = {
            ...payload,
            ARCategoryId: this.state?.TournamentValues?.ARCategoryId
              ? this.state?.TournamentValues?.ARCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("golf")) {
          payload = {
            ...payload,
            GolfCategoryId: this.state?.TournamentValues?.GolfCategoryId
              ? this.state?.TournamentValues?.GolfCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("tennis")) {
          payload = {
            ...payload,
            TennisCategoryId: this.state?.TournamentValues?.TennisCategoryId
              ? this.state?.TournamentValues?.TennisCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("baseball")) {
          payload = {
            ...payload,
            BaseballCategoryId: this.state?.TournamentValues?.BaseballCategoryId
              ? this.state?.TournamentValues?.BaseballCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("icehockey")) {
          payload = {
            ...payload,
            IceHockeyCategoryId: this.state?.TournamentValues
              ?.IceHockeyCategoryId
              ? this.state?.TournamentValues?.IceHockeyCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("boxing")) {
          payload = {
            ...payload,
            BoxingCategoryId: this.state?.TournamentValues?.BoxingCategoryId
              ? this.state?.TournamentValues?.BoxingCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("mma")) {
          payload = {
            ...payload,
            MMACategoryId: this.state?.TournamentValues?.MMACategoryId
              ? this.state?.TournamentValues?.MMACategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("soccer")) {
          payload = {
            ...payload,
            SoccerCategoryId: this.state?.TournamentValues?.SoccerCategoryId
              ? this.state?.TournamentValues?.SoccerCategoryId
              : null,
          };
        } else {
          payload = {
            ...payload,
            RLCategoryId: this.state?.TournamentValues?.RLCategoryId
              ? this.state?.TournamentValues?.RLCategoryId
              : null,
          };
        }

        if (flag?.length > 0) {
          let fileData = await this.setMedia(flag[0]);
          if (fileData) {
            payload = {
              ...payload,
              flag: fileData?.image?.filePath,
            };
            this.setState({
              uploadLogo: fileData?.image?.filePath,
            });
          }
        }
        let passApi = this.props.match.path?.includes("cricket")
          ? "crickets"
          : this.props.match.path?.includes("basketball")
          ? "nba"
          : this.props.match.path?.includes("afl")
          ? "afl"
          : this.props.match.path?.includes("australianrules")
          ? "ar"
          : this.props.match.path?.includes("golf")
          ? "golf"
          : this.props.match.path?.includes("tennis")
          ? "tennis"
          : this.props.match.path?.includes("baseball")
          ? "baseball"
          : this.props.match.path?.includes("icehockey")
          ? "icehockey"
          : this.props.match.path?.includes("boxing")
          ? "boxing"
          : this.props.match.path?.includes("mma")
          ? "mma"
          : this.props.match.path?.includes("soccer")
          ? "soccer"
          : "rls";
        const { status, data } = await axiosInstance.post(
          `${passApi}/tournament`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          if (selectCategory) {
            this.handleSelectCategoryChange(selectCategory);
          } else {
            this.fetchAllTournament(this.state.offset, this.state?.search);
          }
          this.setActionMessage(
            true,
            "Success",
            "Tournament Created Successfully"
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (error) {
        this.setActionMessage(true, "Error", "An error occurred");
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };
  handleUpdate = async () => {
    const { flag, selectCategory, tournamentType, tournamentFormat } =
      this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      try {
        let payload = {
          name: this.state?.TournamentValues?.TournamentName,
          rapidTournamentId: this.state?.TournamentValues?.rapidTournamentId
            ? this.state?.TournamentValues?.rapidTournamentId
            : null,
          sportKey: this.state?.TournamentValues?.SportKey,
          gender: this.state?.TournamentValues?.gender,
        };

        if (flag?.length > 0) {
          let fileData = await this.setMedia(flag[0]);
          if (fileData) {
            payload = {
              ...payload,
              flag: fileData?.image?.filePath,
            };
            this.setState({
              uploadLogo: fileData?.image?.filePath,
            });
          }
        } else {
          payload = {
            ...payload,
            flag: this.state.uploadLogo,
          };
        }
        if (this.props.match.path?.includes("cricket")) {
          payload = {
            ...payload,
            CricketCategoryId: this.state?.TournamentValues?.CricketCategoryId
              ? this.state?.TournamentValues?.CricketCategoryId
              : null,
            uniqueTournamentId:
              this.state.uniqueTournament === 0
                ? null
                : this.state.uniqueTournament,
            tournamentStartTime: this.state?.tournamentStartDate
              ? moment(this.state?.tournamentStartDate)
                  .tz(timezone)
                  .format("YYYY-MM-DD")
              : null,
            tournamentEndTime: this.state?.tournamentEndDate
              ? moment(this.state?.tournamentEndDate)
                  .tz(timezone)
                  .format("YYYY-MM-DD")
              : null,
            type: tournamentType ? tournamentType : null,
            options: tournamentFormat ? tournamentFormat : null,
          };
        } else if (this.props.match.path?.includes("basketball")) {
          payload = {
            ...payload,
            NBACategoryId: this.state?.TournamentValues?.NBACategoryId
              ? this.state?.TournamentValues?.NBACategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("afl")) {
          payload = {
            ...payload,
            AFLCategoryId: this.state?.TournamentValues?.AFLCategoryId
              ? this.state?.TournamentValues?.AFLCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("australianrules")) {
          payload = {
            ...payload,
            ARCategoryId: this.state?.TournamentValues?.ARCategoryId
              ? this.state?.TournamentValues?.ARCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("golf")) {
          payload = {
            ...payload,
            GolfCategoryId: this.state?.TournamentValues?.GolfCategoryId
              ? this.state?.TournamentValues?.GolfCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("tennis")) {
          payload = {
            ...payload,
            TennisCategoryId: this.state?.TournamentValues?.TennisCategoryId
              ? this.state?.TournamentValues?.TennisCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("baseball")) {
          payload = {
            ...payload,
            BaseballCategoryId: this.state?.TournamentValues?.BaseballCategoryId
              ? this.state?.TournamentValues?.BaseballCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("icehockey")) {
          payload = {
            ...payload,
            IceHockeyCategoryId: this.state?.TournamentValues
              ?.IceHockeyCategoryId
              ? this.state?.TournamentValues?.IceHockeyCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("boxing")) {
          payload = {
            ...payload,
            BoxingCategoryId: this.state?.TournamentValues?.BoxingCategoryId
              ? this.state?.TournamentValues?.BoxingCategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("mma")) {
          payload = {
            ...payload,
            MMACategoryId: this.state?.TournamentValues?.MMACategoryId
              ? this.state?.TournamentValues?.MMACategoryId
              : null,
          };
        } else if (this.props.match.path?.includes("soccer")) {
          payload = {
            ...payload,
            SoccerCategoryId: this.state?.TournamentValues?.SoccerCategoryId
              ? this.state?.TournamentValues?.SoccerCategoryId
              : null,
          };
        } else {
          payload = {
            ...payload,
            RLCategoryId: this.state?.TournamentValues?.RLCategoryId
              ? this.state?.TournamentValues?.RLCategoryId
              : null,
            SportId: this.props.match.path?.includes("rugbyleague")
              ? 12
              : this.props.match.path?.includes("rugbyunion")
              ? 13
              : 14,
          };
        }

        let passApi = this.props.match.path?.includes("cricket")
          ? "crickets"
          : this.props.match.path?.includes("basketball")
          ? "nba"
          : this.props.match.path?.includes("afl")
          ? "afl"
          : this.props.match.path?.includes("australianrules")
          ? "ar"
          : this.props.match.path?.includes("golf")
          ? "golf"
          : this.props.match.path?.includes("tennis")
          ? "tennis"
          : this.props.match.path?.includes("baseball")
          ? "baseball"
          : this.props.match.path?.includes("icehockey")
          ? "icehockey"
          : this.props.match.path?.includes("boxing")
          ? "boxing"
          : this.props.match.path?.includes("mma")
          ? "mma"
          : this.props.match.path?.includes("soccer")
          ? "soccer"
          : "rls";
        const { status, data } = await axiosInstance.put(
          `${passApi}/tournament/${this.state.TournamentValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          if (selectCategory) {
            this.handleSelectCategoryChange(selectCategory);
          } else {
            this.fetchAllTournament(this.state.offset, this.state?.search);
          }
          this.setActionMessage(
            true,
            "Success",
            "Tournament Updated Successfully"
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", "An error occurred");
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
      errorCategory: "",
      errorGender: "",
      flag: [],
      uploadLogo: "",
      uniqueTournament: 0,
      tournamentStartDate: null,
      tournamentEndDate: null,
      tournamentType: null,
      tournamentFormat: null,
      errorTournamentType: "",
      errorTournamentFormat: "",
    });
  };

  inputModal = (item, type) => () => {
    const { TournamentValues } = this.state;
    this.fetchAllCategory(0, "ModalCategory");
    this.setState({ isInputModalOpen: true });
    this.fetchuniqueTournament(0, 4);
    if (type === "edit") {
      if (this.props.match.path?.includes("cricket")) {
        this.fetchModalSelectedCategory(
          item?.CricketCategoryId,
          item?.CricketCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.Scd,
            CricketCategoryId: item?.CricketCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          uniqueTournament:
            item?.uniqueTournamentId == null ? 0 : item?.uniqueTournamentId,
          tournamentStartDate: item?.tournamentStartTime,
          tournamentEndDate: item?.tournamentEndTime,
          tournamentType: item?.type ? item?.type : null,
          tournamentFormat: item?.options ? item?.options : null,
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("basketball")) {
        this.fetchModalSelectedCategory(
          item?.NBACategoryId,
          item?.NBACategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            NBACategoryId: item?.NBACategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("afl")) {
        this.fetchModalSelectedCategory(
          item?.AFLCategoryId,
          item?.AFLCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            AFLCategoryId: item?.AFLCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("australianrules")) {
        this.fetchModalSelectedCategory(
          item?.ARCategoryId,
          item?.ARCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            ARCategoryId: item?.ARCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("golf")) {
        this.fetchModalSelectedCategory(
          item?.GolfCategoryId,
          item?.GolfCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            GolfCategoryId: item?.GolfCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          uploadLogo: item?.flag,
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("tennis")) {
        this.fetchModalSelectedCategory(
          item?.TennisCategoryId,
          item?.TennisCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            TennisCategoryId: item?.TennisCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("baseball")) {
        this.fetchModalSelectedCategory(
          item?.BaseballCategoryId,
          item?.BaseballCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            BaseballCategoryId: item?.BaseballCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("icehockey")) {
        this.fetchModalSelectedCategory(
          item?.IceHockeyCategoryId,
          item?.IceHockeyCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            IceHockeyCategoryId: item?.IceHockeyCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("boxing")) {
        this.fetchModalSelectedCategory(
          item?.BoxingCategoryId,
          item?.BoxingCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            BoxingCategoryId: item?.BoxingCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("mma")) {
        this.fetchModalSelectedCategory(
          item?.MMACategoryId,
          item?.MMACategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            MMACategoryId: item?.MMACategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("soccer")) {
        this.fetchModalSelectedCategory(
          item?.SoccerCategoryId,
          item?.SoccerCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.Scd,
            SoccerCategoryId: item?.SoccerCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      } else {
        this.fetchModalSelectedCategory(
          item?.RLCategoryId,
          item?.RLCategory?.name
        );
        this.setState({
          TournamentValues: {
            ...TournamentValues,
            TournamentName: item?.name,
            rapidTournamentId: item?.rapidTournamentId,
            RLCategoryId: item?.RLCategoryId,
            id: item?.id,
            SportKey: item?.sportKey,
            gender: item?.gender,
          },
          isEditMode: true,
        });
      }
    } else {
      this.setState({
        TournamentValues: {
          TournamentName: "",
          rapidTournamentId: "",
          RLCategoryId: "",
          CricketCategoryId: "",
          NBACategoryId: "",
          AFLCategoryId: "",
          ARCategoryId: "",
          GolfCategoryId: "",
          TennisCategoryId: "",
          BaseballCategoryId: "",
          IceHockeyCategoryId: "",
          BoxingCategoryId: "",
          MMACategoryId: "",
          SoccerCategoryId: "",
          id: "",
          SportKey: "",
          gender: "",
        },
        uniqueTournament: 0,
        tournamentStartDate: null,
        tournamentEndDate: null,
        tournamentType: null,
        tournamentFormat: null,
        uploadLogo: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    let { selectCategory } = this.state;
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/tournament/${this.state.itemToDelete}?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/tournament/${this.state.itemToDelete}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("afl")
        ? `afl/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("golf")
        ? `golf/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("mma")
        ? `mma/tournament/${this.state.itemToDelete}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/tournament/${this.state.itemToDelete}`
        : `rls/tournament/${this.state.itemToDelete}?SportId=14`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          if (selectCategory) {
            this.handleSelectCategoryChange(selectCategory);
          } else {
            this.fetchAllTournament(this.state.offset, this.state?.search);
          }
        });
        this.setActionMessage(
          true,
          "Success",
          "Tournament Deleted Successfully"
        );
      } else {
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ isLoading: false });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
      this.setState({ isLoading: false });
    }
  };
  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  inputVariationModal = (item) => {
    this.setState({
      isVariationModalOpen: true,
      TournamentValues: {
        TournamentName: item?.name,
        id: item?.id,
      },
    });
  };
  toggleVariationModal = () => {
    this.setState({
      isVariationModalOpen: false,
      TournamentValues: {
        TournamentName: "",
        id: "",
      },
    });
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  fetchuniqueTournament = async (Page, sportId) => {
    const SportId = this.props.match.path?.includes("cricket")
      ? 4
      : this.props.match.path?.includes("rugbyleague")
      ? 12
      : this.props.match.path?.includes("rugbyunion")
      ? 13
      : this.props.match.path?.includes("basketball")
      ? 10
      : this.props.match.path?.includes("afl")
      ? 15
      : this.props.match.path?.includes("australianrules")
      ? 9
      : this.props.match.path?.includes("golf")
      ? 16
      : this.props.match.path?.includes("tennis")
      ? 7
      : this.props.match.path?.includes("baseball")
      ? 11
      : this.props.match.path?.includes("icehockey")
      ? 17
      : this.props.match.path?.includes("boxing")
      ? 6
      : this.props.match.path?.includes("mma")
      ? 5
      : this.props.match.path?.includes("soccer")
      ? 8
      : 14;
    this.setState({ isuniqueTournamentLoading: true });
    const passApi = `allsport/unique-tournament?SportId=${SportId}&limit=20&offset=${Page}`;
    try {
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let players = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(this.state?.uniqueTournamentData, newdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        var finalSortedData = sortedData?.unshift({ label: "None", value: 0 });

        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          uniqueTournamentData: finalData,
          uniqueTournamentCount: Math.ceil(count),
          isuniqueTournamentLoading: false,
        });
      } else {
        this.setState({ isuniqueTournamentLoading: false });
      }
    } catch (err) {
      this.setState({ isuniqueTournamentLoading: false });
    }
  };

  handleOnScrollBottomuniqueTournament = (e, type) => {
    let {
      uniqueTournamentCount,
      uniqueTournamentPage,
      isuniqueTournamentSearch,
      searchuniqueTournamentCount,
      searchuniqueTournamentPage,
      childSport,
    } = this.state;
    if (
      isuniqueTournamentSearch !== "" &&
      searchuniqueTournamentCount !==
        Math.ceil(searchuniqueTournamentPage / 20 + 1)
    ) {
      this.handleuniqueTournamentInputChange(
        searchuniqueTournamentPage + 20,
        isuniqueTournamentSearch
      );
      this.setState({
        searchuniqueTournamentPage: searchuniqueTournamentPage + 20,
      });
    } else {
      if (
        uniqueTournamentCount !== Math.ceil(uniqueTournamentPage / 20) &&
        isuniqueTournamentSearch == ""
      ) {
        this.fetchuniqueTournament(uniqueTournamentPage + 20, childSport);
        this.setState({
          uniqueTournamentPage: uniqueTournamentPage + 20,
        });
      }
    }
  };
  handleuniqueTournamentInputChange = (uniqueTournamentPage, value) => {
    const SportId = this.props.match.path?.includes("cricket")
      ? 4
      : this.props.match.path?.includes("rugbyleague")
      ? 12
      : this.props.match.path?.includes("rugbyunion")
      ? 13
      : this.props.match.path?.includes("basketball")
      ? 10
      : this.props.match.path?.includes("afl")
      ? 15
      : this.props.match.path?.includes("australianrules")
      ? 9
      : this.props.match.path?.includes("golf")
      ? 16
      : this.props.match.path?.includes("tennis")
      ? 7
      : this.props.match.path?.includes("baseball")
      ? 11
      : this.props.match.path?.includes("icehockey")
      ? 17
      : this.props.match.path?.includes("boxing")
      ? 6
      : this.props.match.path?.includes("mma")
      ? 5
      : this.props.match.path?.includes("soccer")
      ? 8
      : 14;
    const passApi = `allsport/unique-tournament?SportId=${SportId}&limit=20&offset=${uniqueTournamentPage}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(
          this.state?.searchuniqueTournamentData,
          newdata
        );
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchuniqueTournamentData: finalData,
          searchuniqueTournamentCount: Math.ceil(count),
          isuniqueTournamentSearch: value,
        });
      }
    });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllTournament(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  handleChangeFeatureBookMaker = async (isFeatured, id) => {
    const SportId = this.props.match.path?.includes("cricket")
      ? 4
      : this.props.match.path?.includes("rugbyleague")
      ? 12
      : this.props.match.path?.includes("rugbyunion")
      ? 13
      : this.props.match.path?.includes("basketball")
      ? 10
      : this.props.match.path?.includes("afl")
      ? 15
      : this.props.match.path?.includes("australianrules")
      ? 9
      : this.props.match.path?.includes("golf")
      ? 16
      : this.props.match.path?.includes("tennis")
      ? 7
      : this.props.match.path?.includes("baseball")
      ? 11
      : this.props.match.path?.includes("icehockey")
      ? 17
      : this.props.match.path?.includes("boxing")
      ? 6
      : this.props.match.path?.includes("mma")
      ? 5
      : this.props.match.path?.includes("soccer")
      ? 8
      : 14;
    try {
      const { status, data } = await axiosInstance.put(
        `/allsport/featuredOrder?SportId=${SportId}`,
        {
          isFeatured: isFeatured,
          tournamentId: id,
        }
      );
      if (status === 200) {
        this.setActionMessage(true, "Success", data?.result?.message);
      } else {
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err.response.data.message);
    }
  };

  handleCheckBoxChange = (e) => {
    const { value, checked } = e.target;
    const { checkBoxValues } = this.state;
    if (checked) {
      if (checkBoxValues?.length < 6) {
        let checkboxdata = [...checkBoxValues, Number(value)];
        this.setState({
          checkBoxValues: checkboxdata,
        });
      }
      this.handleChangeFeatureBookMaker(true, Number(value));
    } else {
      let checkboxdata = checkBoxValues?.filter(
        (element) => element !== Number(value)
      );
      this.setState({
        checkBoxValues: checkboxdata,
      });
      this.handleChangeFeatureBookMaker(false, Number(value));
    }
  };

  getPageAndSportId() {
    const path = this.props.match.path;

    if (path.includes("cricket")) {
      return { page: "cricket", sportId: 4 };
    }

    if (path.includes("australianrules")) {
      return { page: "australianrules", sportId: 9 };
    }

    if (path.includes("rugbyleague")) {
      return { page: "rugbyleague", sportId: 12 };
    }

    if (path.includes("soccer")) {
      return { page: "soccer", sportId: 8 };
    }

    // Default fallback
    return { page: "rugbyleague", sportId: 12 };
  }

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      TournamentValues,
      CategoryPage,
      categoryData,
      externalCategoryData,
      selectCategory,
      TournamentList,
      TournamentCount,
      SelectedTournamentList,
      errorRequire,
      errorCategory,
      searchCategory,
      isCategorySearch,
      search,
      isVariationModalOpen,
      errorGender,
      flag,
      uploadLogo,
      isuniqueTournamentSearch,
      searchuniqueTournamentData,
      uniqueTournament,
      uniqueTournamentData,
      isuniqueTournamentLoading,
      tournamentStartDate,
      tournamentEndDate,
      checkBoxValues,
      tournamentType,
      tournamentFormat,
      errorTournamentType,
      errorTournamentFormat,
    } = this.state;
    const pageNumbers = [];
    if (TournamentCount > 0) {
      for (let i = 1; i <= Math.ceil(TournamentCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    let FinalTournamentList = selectCategory
      ? SelectedTournamentList
      : TournamentList;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Tournaments</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Tournaments
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "20%", marginTop: "0px" }}
              >
                <option value="">Select Category</option>
                {CategoryData?.length > 0 &&
                  CategoryData?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.categoryName}
                    </option>
                  ))}
              </SelectBox> */}
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Category"
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomCategory(e, "ExternalCategory")
                  }
                  onInputChange={(e) => this.handleCategoryInputChange(0, e)}
                  value={
                    isCategorySearch
                      ? searchCategory?.find((item) => {
                          return item?.value == selectCategory;
                        })
                      : externalCategoryData?.find((item) => {
                          return item?.value == selectCategory;
                        })
                  }
                  onChange={(e) => this.handleSelectCategoryChange(e?.value)}
                  options={
                    isCategorySearch ? searchCategory : externalCategoryData
                  }
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllTournament(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TournamentList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TournamentList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable"
                    aria-label="simple table"
                    style={{ minWidth: "max-content" }}
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Feature</TableCell>
                        <TableCell style={{ width: "150px" }}>
                          {this.props.match.path?.includes("cricket") ||
                          this.props.match.path?.includes("soccer")
                            ? "Tournament Key"
                            : "Rapid Tournament Id"}
                        </TableCell>
                        {this.props.match.path?.includes("golf") ? (
                          <TableCell>Logo</TableCell>
                        ) : (
                          <></>
                        )}
                        <TableCell
                          // style={{ width: "15%" }}
                          style={{ width: "150px" }}
                        >
                          Tournament Name
                        </TableCell>
                        {(this.props.match.path?.includes("cricket") ||
                          this.props.match.path?.includes("australianrules") ||
                          this.props.match.path?.includes("rugbyleague") ||
                          this.props.match.path?.includes("soccer")) && (
                          <TableCell></TableCell>
                        )}
                        <TableCell>variation</TableCell>

                        {this.props.match.path?.includes("cricket") && (
                          <>
                            <TableCell>Type</TableCell>
                            <TableCell>Format</TableCell>
                          </>
                        )}
                        <TableCell>Gender</TableCell>
                        <TableCell>Category</TableCell>
                        <TableCell>Sport Key</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {FinalTournamentList?.length > 0 ? (
                        FinalTournamentList?.map((item) => {
                          return (
                            <TableRow
                              className="table-rows listTable-Row"
                              key={item?.id}
                            >
                              <TableCell> {item?.id} </TableCell>
                              <TableCell>
                                <Checkbox
                                  disableRipple
                                  disableFocusRipple
                                  disableTouchRipple
                                  className="filter-racing"
                                  icon={<Unchecked />}
                                  checkedIcon={<Checked />}
                                  name="filter"
                                  value={item?.id}
                                  onChange={(event) => {
                                    this.handleCheckBoxChange(event, item?.id);
                                  }}
                                  checked={checkBoxValues?.includes(item?.id)}
                                />
                              </TableCell>
                              <TableCell>
                                {" "}
                                {this.props.match.path?.includes("cricket") ||
                                this.props.match.path?.includes("soccer")
                                  ? item?.Scd
                                  : item?.rapidTournamentId}
                              </TableCell>
                              {this.props.match.path?.includes("golf") ? (
                                <TableCell className="upload-img">
                                  {item?.flag?.includes("uploads") ? (
                                    <img
                                      src={config.mediaUrl + item?.flag}
                                      alt="teamlogo"
                                    />
                                  ) : item?.flag ? (
                                    <img src={item?.flag} alt="teamlogo" />
                                  ) : (
                                    ""
                                  )}
                                </TableCell>
                              ) : (
                                <></>
                              )}
                              <TableCell>{item?.name}</TableCell>
                              {(this.props.match.path?.includes("cricket") ||
                                this.props.match.path?.includes(
                                  "australianrules"
                                ) ||
                                this.props.match.path?.includes(
                                  "rugbyleague"
                                ) ||
                                this.props.match.path?.includes("soccer")) && (
                                <TableCell>
                                  <Button
                                    variant="contained"
                                    style={{
                                      backgroundColor: "#4455C7",
                                      color: "#fff",
                                      borderRadius: "8px",
                                      textTransform: "capitalize",
                                      marginLeft: "15px",
                                    }}
                                    onClick={() => {
                                      const { page, sportId } =
                                        this.getPageAndSportId();
                                      this.props.navigate(
                                        `/${page}/seasons/${sportId}/${item?.id}`
                                      );
                                    }}
                                  >
                                    View Seasons
                                  </Button>
                                </TableCell>
                              )}

                              <TableCell>
                                <Button
                                  variant="contained"
                                  style={{
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    borderRadius: "8px",
                                    textTransform: "capitalize",
                                    // padding: "13px 24px 12px",
                                    marginLeft: "15px",
                                  }}
                                  onClick={() => {
                                    this.inputVariationModal(item);
                                  }}
                                >
                                  Add/Edit variation
                                </Button>
                              </TableCell>
                              {this.props.match.path?.includes("cricket") && (
                                <>
                                  <TableCell
                                    style={{ textTransform: "capitalize" }}
                                  >
                                    {item?.type}
                                  </TableCell>
                                  <TableCell
                                    style={{ textTransform: "capitalize" }}
                                  >
                                    {item?.options}
                                  </TableCell>
                                </>
                              )}
                              <TableCell>{item?.gender}</TableCell>
                              {this.props.match.path?.includes("cricket") ? (
                                <TableCell>
                                  {item?.CricketCategory?.name}
                                </TableCell>
                              ) : this.props.match.path?.includes(
                                  "basketball"
                                ) ? (
                                <TableCell>{item?.NBACategory?.name}</TableCell>
                              ) : this.props.match.path?.includes("afl") ? (
                                <TableCell>{item?.AFLCategory?.name}</TableCell>
                              ) : this.props.match.path?.includes(
                                  "australianrules"
                                ) ? (
                                <TableCell>{item?.ARCategory?.name}</TableCell>
                              ) : this.props.match.path?.includes("golf") ? (
                                <TableCell>
                                  {item?.GolfCategory?.name}
                                </TableCell>
                              ) : this.props.match.path?.includes("tennis") ? (
                                <TableCell>
                                  {item?.TennisCategory?.name}
                                </TableCell>
                              ) : this.props.match.path?.includes(
                                  "baseball"
                                ) ? (
                                <TableCell>
                                  {item?.BaseballCategory?.name}
                                </TableCell>
                              ) : this.props.match.path?.includes(
                                  "icehockey"
                                ) ? (
                                <TableCell>
                                  {item?.IceHockeyCategory?.name}
                                </TableCell>
                              ) : this.props.match.path?.includes("boxing") ? (
                                <TableCell>
                                  {item?.BoxingCategory?.name}
                                </TableCell>
                              ) : this.props.match.path?.includes("mma") ? (
                                <TableCell>{item?.MMACategory?.name}</TableCell>
                              ) : this.props.match.path?.includes("soccer") ? (
                                <TableCell>
                                  {item?.SoccerCategory?.name}
                                </TableCell>
                              ) : (
                                <TableCell>{item?.RLCategory?.name}</TableCell>
                              )}
                              <TableCell>{item?.sportKey}</TableCell>
                              <TableCell>
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  style={{ cursor: "pointer" }}
                                  className="table-btn edit-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  style={{ cursor: "pointer" }}
                                  className="table-btn delete-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      {!selectCategory ? (
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}

                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  TournamentCount / rowPerPage > 1
                                    ? false
                                    : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />

                              {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        <></>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Tournament" : "Edit Tournament"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Tournament Name</label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Tournament Name"
                          value={TournamentValues?.TournamentName}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                TournamentName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorRequire ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRequire}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {this.props.match.path?.includes("cricket") ||
                          this.props.match.path?.includes("soccer")
                            ? "Tournament Key"
                            : "rapid Tournament Id"}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Tournament Id"
                          value={TournamentValues?.rapidTournamentId}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                rapidTournamentId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Category </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Category"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomCategory(
                              e,
                              "ModalCategory"
                            )
                          }
                          onInputChange={(e) =>
                            this.handleCategoryInputChange(0, e)
                          }
                          value={
                            isCategorySearch
                              ? searchCategory?.find((item) => {
                                  return this.props.match.path?.includes(
                                    "cricket"
                                  )
                                    ? item?.value ==
                                        TournamentValues?.CricketCategoryId
                                    : this.props.match.path?.includes(
                                        "basketball"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.NBACategoryId
                                    : this.props.match.path?.includes("afl")
                                    ? item?.value ==
                                      TournamentValues?.AFLCategoryId
                                    : this.props.match.path?.includes(
                                        "australianrules"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.ARCategoryId
                                    : this.props.match.path?.includes("golf")
                                    ? item?.value ==
                                      TournamentValues?.GolfCategoryId
                                    : this.props.match.path?.includes("tennis")
                                    ? item?.value ==
                                      TournamentValues?.TennisCategoryId
                                    : this.props.match.path?.includes(
                                        "baseball"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.BaseballCategoryId
                                    : this.props.match.path?.includes(
                                        "icehockey"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.IceHockeyCategoryId
                                    : this.props.match.path?.includes("boxing")
                                    ? item?.value ==
                                      TournamentValues?.BoxingCategoryId
                                    : this.props.match.path?.includes("mma")
                                    ? item?.value ==
                                      TournamentValues?.MMACategoryId
                                    : this.props.match.path?.includes("soccer")
                                    ? item?.value ==
                                      TournamentValues?.SoccerCategoryId
                                    : item?.value ==
                                      TournamentValues?.RLCategoryId;
                                })
                              : categoryData?.find((item) => {
                                  return this.props.match.path?.includes(
                                    "cricket"
                                  )
                                    ? item?.value ==
                                        TournamentValues?.CricketCategoryId
                                    : this.props.match.path?.includes(
                                        "basketball"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.NBACategoryId
                                    : this.props.match.path?.includes("afl")
                                    ? item?.value ==
                                      TournamentValues?.AFLCategoryId
                                    : this.props.match.path?.includes(
                                        "australianrules"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.ARCategoryId
                                    : this.props.match.path?.includes("golf")
                                    ? item?.value ==
                                      TournamentValues?.GolfCategoryId
                                    : this.props.match.path?.includes("tennis")
                                    ? item?.value ==
                                      TournamentValues?.TennisCategoryId
                                    : this.props.match.path?.includes(
                                        "baseball"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.BaseballCategoryId
                                    : this.props.match.path?.includes(
                                        "icehockey"
                                      )
                                    ? item?.value ==
                                      TournamentValues?.IceHockeyCategoryId
                                    : this.props.match.path?.includes("boxing")
                                    ? item?.value ==
                                      TournamentValues?.BoxingCategoryId
                                    : this.props.match.path?.includes("mma")
                                    ? item?.value ==
                                      TournamentValues?.MMACategoryId
                                    : this.props.match.path?.includes("soccer")
                                    ? item?.value ==
                                      TournamentValues?.SoccerCategoryId
                                    : item?.value ==
                                      TournamentValues?.RLCategoryId;
                                })
                          }
                          onChange={(e) =>
                            this.props.match.path?.includes("cricket")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    CricketCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("basketball")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    NBACategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("afl")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    AFLCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes(
                                  "australianrules"
                                )
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    ARCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("golf")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    GolfCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("tennis")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    TennisCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("baseball")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    BaseballCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("icehockey")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    IceHockeyCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("boxing")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    BoxingCategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("mma")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    MMACategoryId: e.value,
                                  },
                                })
                              : this.props.match.path?.includes("soccer")
                              ? this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    SoccerCategoryId: e.value,
                                  },
                                })
                              : this.setState({
                                  TournamentValues: {
                                    ...TournamentValues,
                                    RLCategoryId: e.value,
                                  },
                                })
                          }
                          options={
                            isCategorySearch ? searchCategory : categoryData
                          }
                        />
                        {errorCategory ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorCategory}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {this.props.match.path?.includes("cricket") && (
                        <Grid
                          item
                          xs={6}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">
                            {" "}
                            Unique Tournament
                          </label>
                          <Select
                            className="React cricket-select event-Tournament-select"
                            classNamePrefix="select"
                            placeholder="Unique Tournament"
                            menuPosition="fixed"
                            onMenuScrollToBottom={(e) =>
                              this.handleOnScrollBottomuniqueTournament(e)
                            }
                            onInputChange={(e) => {
                              this.handleuniqueTournamentInputChange(0, e);
                            }}
                            isLoading={isuniqueTournamentLoading}
                            value={
                              uniqueTournament != null &&
                              (isuniqueTournamentSearch
                                ? searchuniqueTournamentData?.find((item) => {
                                    return item?.value == uniqueTournament;
                                  })
                                : uniqueTournamentData?.find((item) => {
                                    return item?.value == uniqueTournament;
                                  }))
                            }
                            onChange={(e) =>
                              this.setState({
                                uniqueTournament: e.value,
                              })
                            }
                            options={
                              isuniqueTournamentSearch
                                ? searchuniqueTournamentData
                                : uniqueTournamentData
                            }
                          />
                        </Grid>
                      )}
                      <Grid
                        item
                        xs={this.props.match.path?.includes("cricket") ? 6 : 12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Sport Key</label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Sport Key"
                          value={TournamentValues?.SportKey}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                SportKey: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={12} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Gender </label>
                          <RadioGroup
                            aria-label="gender"
                            name="gender"
                            className="gender"
                            value={TournamentValues?.gender}
                            onChange={(e) =>
                              this.setState({
                                TournamentValues: {
                                  ...TournamentValues,
                                  gender: e?.target?.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="F"
                              control={
                                <Radio
                                  color="primary"
                                  checked={TournamentValues?.gender === "F"}
                                />
                              }
                              label="Female"
                            />
                            <FormControlLabel
                              value="M"
                              control={
                                <Radio
                                  color="primary"
                                  checked={TournamentValues?.gender === "M"}
                                />
                              }
                              label="Male"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorGender ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorGender}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {this.props.match.path?.includes("cricket") && (
                        <>
                          <Grid
                            item
                            xs={6}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              marginBottom: "8px",
                            }}
                          >
                            <label className="modal-label">Start Date</label>
                            <LocalizationProvider dateAdapter={AdapterDateFns}>
                              <DesktopDatePicker
                                autoOk
                                // disableToolbar
                                variant="inline"
                                format="yyyy/MM/dd"
                                placeholder="Start Date"
                                margin="normal"
                                id="date-picker-inline"
                                inputVariant="outlined"
                                value={
                                  tournamentStartDate
                                    ? typeof tournamentStartDate === "string"
                                      ? parseISO(
                                          moment(tournamentStartDate)
                                            ?.tz(timezone)
                                            ?.format("YYYY-MM-DD")
                                        )
                                      : tournamentStartDate
                                    : null
                                }
                                onChange={(e) =>
                                  this.setState({
                                    tournamentStartDate: e,
                                  })
                                }
                                KeyboardButtonProps={{
                                  "aria-label": "change date",
                                }}
                                className="date-picker-sponsored date-picker-fixture-modal"
                              />
                            </LocalizationProvider>
                          </Grid>
                          <Grid
                            item
                            xs={6}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              marginBottom: "8px",
                            }}
                          >
                            <label className="modal-label">End Date</label>
                            <LocalizationProvider dateAdapter={AdapterDateFns}>
                              <DesktopDatePicker
                                autoOk
                                // disableToolbar
                                variant="inline"
                                format="yyyy/MM/dd"
                                placeholder="End Date"
                                margin="normal"
                                id="date-picker-inline"
                                inputVariant="outlined"
                                value={
                                  tournamentEndDate
                                    ? typeof tournamentEndDate === "string"
                                      ? parseISO(
                                          moment(tournamentEndDate)
                                            ?.tz(timezone)
                                            ?.format("YYYY-MM-DD")
                                        )
                                      : tournamentEndDate
                                    : null
                                }
                                minDate={
                                  tournamentStartDate
                                    ? typeof tournamentStartDate === "string"
                                      ? parseISO(
                                          moment(tournamentStartDate)
                                            ?.tz(timezone)
                                            ?.format("YYYY-MM-DD")
                                        )
                                      : tournamentStartDate
                                    : null
                                }
                                onChange={(e) =>
                                  this.setState({
                                    tournamentEndDate: e,
                                  })
                                }
                                KeyboardButtonProps={{
                                  "aria-label": "change date",
                                }}
                                className="date-picker-sponsored date-picker-fixture-modal"
                              />
                            </LocalizationProvider>
                          </Grid>
                          <Grid
                            item
                            xs={6}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              marginBottom: "8px",
                            }}
                          >
                            <label className="modal-label">Type</label>
                            <Select
                              className="React teamsport-select event-Tournament-select"
                              classNamePrefix="select"
                              menuPosition="fixed"
                              placeholder="Type"
                              value={TournamentTypeOption?.find((op) => {
                                return op?.value === tournamentType;
                              })}
                              onChange={(e) => {
                                this.setState({
                                  tournamentType: e?.value,
                                  errorTournamentType: e?.value ? "" : e?.value,
                                });
                              }}
                              options={TournamentTypeOption}
                            />
                            {errorTournamentType ? (
                              <p
                                className="errorText"
                                style={{ margin: "5px 0 0 0" }}
                              >
                                {errorTournamentType}
                              </p>
                            ) : (
                              ""
                            )}
                          </Grid>
                          <Grid
                            item
                            xs={6}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              marginBottom: "8px",
                            }}
                          >
                            <label className="modal-label">Format Type</label>
                            <Select
                              className="React teamsport-select event-Tournament-select"
                              classNamePrefix="select"
                              menuPosition="fixed"
                              placeholder="Format Type"
                              value={TournamentFormatOptaion?.find((op) => {
                                return op?.value === tournamentFormat;
                              })}
                              onChange={(e) => {
                                this.setState({
                                  tournamentFormat: e?.value,
                                  errorTournamentFormat: e?.value
                                    ? ""
                                    : e?.value,
                                });
                              }}
                              options={TournamentFormatOptaion}
                            />
                            {errorTournamentFormat ? (
                              <p
                                className="errorText"
                                style={{ margin: "5px 0 0 0" }}
                              >
                                {errorTournamentFormat}
                              </p>
                            ) : (
                              ""
                            )}
                          </Grid>
                        </>
                      )}
                    </Grid>

                    {this.props.match.path?.includes("golf") ? (
                      <div className="blog-file-upload">
                        <h6>Logo</h6>
                        <FileUploader
                          onDrop={(flag) => this.handleFileUpload("flag", flag)}
                        />
                        <div className="logocontainer">
                          {flag?.length > 0
                            ? flag?.map((file, index) => (
                                <img
                                  className="auto-width"
                                  key={index}
                                  src={file.preview}
                                  alt="teamlogo"
                                />
                              ))
                            : uploadLogo &&
                              uploadLogo !== "" && (
                                <img
                                  className="auto-width"
                                  src={config.mediaUrl + uploadLogo}
                                  alt="teamlogo"
                                />
                              )}
                        </div>
                      </div>
                    ) : (
                      <></>
                    )}

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isVariationModalOpen}
              onClose={this.toggleVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Variation Details
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleVariationModal}
                />
                <CreateTournamentVariation
                  inputModal={this.toggleVariationModal}
                  TournamentValues={TournamentValues}
                  pathName={this.props?.match?.path}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Tournament;
