import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  TableSortLabel,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import { config } from "../../helpers/config";
import FileUploader from "../../library/common/components/FileUploader";
import DeleteIcon from "@mui/icons-material/Delete";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import he from "he";

import "../expertTips/expertTips.scss";
import "./featuredCalender.scss";
import moment from "moment-timezone";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import { IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import { identifiers } from "../../library/common/constants";
import _, { includes } from "lodash";
import { fetchFromStorage } from "../../library/utilities";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
const statusOption = [
  {
    label: "draft",
    value: "draft",
  },
  {
    label: "published",
    value: "published",
  },
];
class FeaturedCalender extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      expertTipsValues: {
        // startDate: moment(Date()).format("YYYY-MM-DD"),
        modalTrackId: null,
        raceName: "",
        type: "",
        prizeMoney: "",
        distance: "",
        raceCourseUrl: "",
      },
      extraInfoValues: {
        adShortCode: "",
        raceDetail: "",
        preview: "",
        trackCondition: "",
        seletedId: "",
      },
      modalSelectedRace: [],
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      ExpertTipsList: [],
      ExpertTipsCount: 0,
      serachValue: "",
      SelectedSport: null,
      AllSportsOption: [],
      SelectedUsers: null,
      AllUserOption: [],
      sortDate: null,
      startDateOpen: false,
      selectedModalSport: null,
      modalSportsOption: [],
      selectedSportObj: null,
      isTrackLoading: false,
      isTrackRaceLoading: false,
      isTrackAllRaceLoading: false,
      istypeRaceRunnerLoading: false,
      isEachWayRaceRunnerLoading: false,
      isLayRaceRunnerLoading: false,
      TrackData: [],
      TrackCount: 0,
      TrackPage: 0,
      searchTrack: [],
      searchTrackCount: 0,
      searchTrackPage: 0,
      isTrackSearch: "",
      TrackAllRaceData: [],
      TrackRaceData: [],
      BetRunnerData: [],
      EachWayRunnerData: [],
      FilteredEachWayRunnerData: [],
      LayRunnerData: [],
      selectedValues: {},
      SelectedId: "",
      // errorStartDate: "",
      errorModalSport: "",
      errorTrack: "",
      errorraceName: "",
      errortype: "",
      errorBetprice: "",
      errorBetRace: "",
      errorBetRaceRunner: "",
      errorprizeMoney: "",
      errorEachWayPrice: "",
      errorEachWayRace: "",
      errorEachWayRaceRunner: "",
      errordistance: "",
      errorLayPrice: "",
      errorLayRace: "",
      errorLayRaceRunner: "",
      errorRace: "",
      errorWpCreds: "",
      errorStateCode: "",
      errorAdShortCode: "",
      errorRaceDetail: "",
      errorPreview: "",
      errorTrackCondition: "",
      errorBannerImage: "",
      raceDetailContent: "",
      previewContent: "",
      tipsModalDetailsOpen: false,
      tipsModalDetails: {},
      tipsModalDetailsIsLoading: false,
      sortType: "id",
      sortId: false,
      sortEventName: true,
      sortEventDate: true,
      sortSportType: true,
      sortUser: true,
      defaultImage: [],
      defaultUploadImage: "",
      wpCategoryLoading: false,
      wpCategoryData: [],
      errorModalwpCategory: "",
      selectedStatus: "draft",
      isAddExtraInfoModalOpen: false,
      errorRaceCourseUrl: "",
    };
  }

  // handleChange = (raceId, selectedOptions) => {
  //   this.setState((prevState) => ({
  //     selectedValues: {
  //       ...prevState?.selectedValues,
  //       [raceId]: selectedOptions?.map((option) => option?.value),
  //     },
  //   }));
  // };

  handleChange = (raceId, selectedOptions) => {
    this.setState((prevState) => {
      const updatedSelectedValues = { ...prevState?.selectedValues };
      if (selectedOptions && selectedOptions?.length > 0) {
        updatedSelectedValues[raceId] = selectedOptions?.map(
          (option) => option?.value
        );
      } else {
        delete updatedSelectedValues[raceId];
      }
      return {
        selectedValues: updatedSelectedValues,
      };
    });
  };

  componentDidMount() {
    this.fetchAllSports();
    // this.fetchAllUser();
    this.fetchAllEvent(0, null, null, "", this.state?.sortType, false, null);
    this.handleWpError();
  }

  componentDidUpdate(prevProps, prevState) {
    let {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      selectedValues,
      BetRunnerData,
      expertTipsValues,
      EachWayRunnerData,
    } = this.state;
    if (prevState.offset !== offset) {
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        sortType,
        sortData,
        SelectedUsers
      );
    }
    if (prevState.selectedValues !== selectedValues) {
      if (BetRunnerData?.length > 0 && expertTipsValues?.modalTrackBetRace) {
        this.setState({
          expertTipsValues: {
            ...expertTipsValues,
            modalBetRunnerId:
              selectedValues[expertTipsValues?.modalTrackBetRace]?.[0],
          },
        });
      }
      if (
        EachWayRunnerData?.length > 0 &&
        expertTipsValues?.modalTrackEachWayRace
      ) {
        const filterData = EachWayRunnerData?.filter((runner) =>
          selectedValues[expertTipsValues?.modalTrackEachWayRace]?.includes(
            runner?.value
          )
        );
        this.setState({
          FilteredEachWayRunnerData: filterData,
        });
      }
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEvent(0, null, null, "", "id", false, null);
      this.fetchAllSports();
      this.setState({
        offset: 0,
        currentPage: 1,
      });
    }
  }
  handleWpError = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/expertTips/errorHandled`
      );
      if (status === 200) {
        this.setState({
          errorWpCreds: "",
        });
      } else {
      }
    } catch (err) {
      this.setState({
        errorWpCreds: err?.response?.data?.message,
      });
      this.setActionMessage(
        true,
        "Error",
        err?.response?.data?.message,
        "wordpresserror"
      );
    }
  };
  handleStateCodeError = async (trackId) => {
    try {
      const { status, data } = await axiosInstance.get(
        `/expertTips/errorHandled?trackId=${trackId}`
      );
      if (status === 200) {
        this.setState({
          errorStateCode: "",
        });
      } else {
      }
    } catch (err) {
      this.setState({
        errorStateCode: err?.response?.data?.message,
      });
    }
  };
  fetchAllEvent = async (page, date, sportId, search, type, order, userId) => {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      // const passApi = `/racingFeatured/featuredCalender?limit=${rowPerPage}&offset=${page}&timezone=${timezone}&date=${
      //   date ? date : ""
      // }&SportId=${sportId ? sportId : ""}&search=${search}&orderBy=${
      //   type ? type : ""
      // }&sort=${order ? "ASC" : "DESC"}&UserId=${
      //   userId && userId != null ? userId : ""
      // }`;
      const passApi = `/racingFeatured/featuredCalender?limit=${rowPerPage}&offset=${page}&SportId=${
        sportId ? sportId : ""
      }&search=${search}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          ExpertTipsList: data?.result,
          isLoading: false,
          ExpertTipsCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchSingleEvent = async (id) => {
    this.setState({ tipsModalDetailsIsLoading: true });
    try {
      const passApi = `/racingFeatured/featuredCalender/${id}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          tipsModalDetailsIsLoading: false,
        });
        let newdata = [];
        let TrackAllRaceObject = (data?.result?.races || []).reduce(
          (acc, item) => {
            const key = item?.RaceId;
            const value = item?.runners?.map((option) => option?.id) || [];
            if (key !== undefined) {
              acc[key] = value;
            }
            return acc;
          },
          {}
        );
        this.setState({
          selectedValues: TrackAllRaceObject,
          tipsModalDetails: data?.result,
        });
      } else {
        this.setState({
          isLoading: false,
          tipsModalDetailsIsLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
        tipsModalDetailsIsLoading: false,
      });
    }
  };

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.sports + `?sportTypeId=1`
    );
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Races",
        value: 0,
      };
      const idOrder = { 1: 0, 2: 2, 3: 1 };
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        modalSportsOption: newdata?.sort((a, b) => {
          return (idOrder[a?.value] || 0) - (idOrder[b?.value] || 0);
        }),
        // isLoading: false,
      });
    }
  }

  async fetchAllUser() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(`/expertTips/users`);
    if (status === 200) {
      let newdata = [];
      let sportData = data.users?.map((item) => {
        newdata.push({
          label: item?.firstName + " " + item?.lastName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All User",
        value: 0,
      };
      let alldata = [alldatas, ...newdata];
      this.setState({
        AllUserOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),

        // isLoading: false,
      });
    }
  }

  handlesportchange = (e) => {
    const {
      offset,
      sortDate,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      SelectedSport: e?.value,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      0,
      sortDate,
      e?.value,
      serachValue,
      sortType,
      sortData,
      SelectedUsers
    );
  };

  handleUserchange = (e) => {
    const {
      offset,
      sortDate,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedSport,
    } = this.state;
    this.setState({
      SelectedUsers: e?.value,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      0,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortData,
      e?.value
    );
  };

  handalValidate = () => {
    let {
      expertTipsValues,
      selectedModalSport,
      selectedValues,
      TrackAllRaceData,
      errorStateCode,
    } = this.state;
    let flag = true;
    // if (
    //   expertTipsValues?.startDate === "" ||
    //   expertTipsValues?.startDate === null
    // ) {
    //   flag = false;
    //   this.setState({
    //     errorStartDate: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorStartDate: "",
    //   });
    // }
    if (selectedModalSport === null) {
      flag = false;
      this.setState({
        errorModalSport: "This field is mandatory",
      });
    } else {
      this.setState({
        errorModalSport: "",
      });
    }
    if (Boolean(errorStateCode)) {
      flag = false;
    }
    // if (expertTipsValues?.wpCategoryId?.length === 0) {
    //   flag = false;
    //   this.setState({
    //     errorModalwpCategory: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorModalwpCategory: "",
    //   });
    // }
    if (expertTipsValues?.modalTrackId === null) {
      flag = false;
      this.setState({
        errorTrack: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTrack: "",
      });
    }
    if (expertTipsValues?.raceName?.trim() === "") {
      flag = false;
      this.setState({
        errorraceName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorraceName: "",
      });
    }
    if (expertTipsValues?.type?.trim() === "") {
      flag = false;
      this.setState({
        errortype: "This field is mandatory",
      });
    } else {
      this.setState({
        errortype: "",
      });
    }

    if (expertTipsValues?.prizeMoney === "") {
      flag = false;
      this.setState({
        errorprizeMoney: "This field is mandatory",
      });
    } else {
      this.setState({
        errorprizeMoney: "",
      });
    }

    if (expertTipsValues?.distance === "") {
      flag = false;
      this.setState({
        errordistance: "This field is mandatory",
      });
    } else {
      this.setState({
        errordistance: "",
      });
    }
    if (expertTipsValues?.raceCourseUrl === "") {
      flag = false;
      this.setState({
        errorRaceCourseUrl: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRaceCourseUrl: "",
      });
    }
    // if (this.state?.isEditMode) {
    if (Boolean(this.state.defaultUploadImage)) {
      this.setState({
        errorBannerImage: "",
      });
    } else if (this.state?.defaultImage?.length > 0) {
      this.setState({
        errorBannerImage: "",
      });
    } else {
      flag = false;
      this.setState({
        errorBannerImage: "This field is mandatory",
      });
    }
    // } else {
    //   if (this.state?.defaultImage?.length == 0) {
    //     flag = false;
    //     this.setState({
    //       errorBannerImage: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorBannerImage: "",
    //     });
    //   }
    // }
    return flag;
  };

  handleExtraInfoValidate = () => {
    let { extraInfoValues, modalSelectedRace } = this.state;
    let flag = true;

    if (extraInfoValues?.adShortCode?.trim() === "") {
      flag = false;
      this.setState({
        errorAdShortCode: "This field is mandatory",
      });
    } else {
      this.setState({
        errorAdShortCode: "",
      });
    }

    // if (extraInfoValues?.trackCondition?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorTrackCondition: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorTrackCondition: "",
    //   });
    // }
    if (extraInfoValues?.raceDetail?.trim() === "") {
      flag = false;
      this.setState({
        errorRaceDetail: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRaceDetail: "",
      });
    }
    if (extraInfoValues?.preview?.trim() === "") {
      flag = false;
      this.setState({
        errorPreview: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPreview: "",
      });
    }
    // if (modalSelectedRace?.length == 0 || !modalSelectedRace) {
    //   flag = false;
    //   this.setState({
    //     errorRace: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorRace: "",
    //   });
    // }
    return flag;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      selectedModalSport: null,
      TrackData: [],
      TrackCount: 0,
      TrackPage: 0,
      searchTrack: [],
      searchTrackCount: 0,
      searchTrackPage: 0,
      isTrackSearch: "",
      TrackRaceData: [],
      BetRunnerData: [],
      TrackAllRaceData: [],
      selectedValues: {},
      expertTipsValues: {
        // startDate: moment(Date()).format("YYYY-MM-DD"),
        modalTrackId: null,
        raceName: "",
        type: "",
        prizeMoney: "",
        distance: "",
        raceCourseUrl: "",
      },
      // errorStartDate: "",
      errorModalSport: "",
      errorTrack: "",
      errorraceName: "",
      errortype: "",
      errorStateCode: "",
      errorprizeMoney: "",
      errordistance: "",
      defaultImage: [],
      defaultUploadImage: "",
      errorRaceCourseUrl: "",
    });
  };

  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    // this.wpCategory(item);
    if (type === "edit") {
      // this.fetchSingleEvent(item?.id, item?.wayRaceId);
      this.fetchAllTrack(0, item?.Sport?.id, item);
      // this.eventRaceList(item?.Event?.id);
      // this.eventAllRaceList(item?.Event?.id);
      // this.typeRaceRunner(item?.betRaceId, "first");

      this.setState({
        SelectedId: item?.id,
        isEditMode: true,
        selectedModalSport: item?.Sport?.id,
        expertTipsValues: {
          // startDate: moment(item?.date).tz(timezone).format("YYYY-MM-DD"),
          modalTrackId: item?.TrackId,
          raceName: item?.raceName,
          type: item?.type,
          prizeMoney: item?.priceMoney,
          distance: item?.distance,
          raceCourseUrl: item?.raceCourseUrl,
        },
        defaultUploadImage: item?.featuredImage,
      });
    } else {
      this.fetchAllTrack(0, 1);
      this.setState({
        isEditMode: false,
        selectedModalSport: 1,
        errorStateCode: "",
        expertTipsValues: {
          // startDate: moment(Date()).format("YYYY-MM-DD"),
          modalTrackId: null,
          raceName: "",
          type: "",
          prizeMoney: "",
          distance: "",
          raceCourseUrl: "",
        },
      });
    }
  };

  tipsDetailsModalOpen = (item) => {
    this.setState({ tipsModalDetailsOpen: true });
    this.fetchSingleEvent(item?.id);
  };

  toggleTipsDetailsModalOpen = () => {
    this.setState({ tipsModalDetailsOpen: false, tipsModalDetails: {} });
  };

  setActionMessage = (display = false, type = "", message = "", isWperror) => {
    const clearMessageBox = () =>
      this.setState({
        messageBox: { display: false, type: "", message: "" },
      });
    this.setState(
      {
        messageBox: { display, type, message },
      },
      isWperror
        ? () => {}
        : () => {
            setTimeout(clearMessageBox, 3000);
          }
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    try {
      const passApi = `/racingFeatured/featuredCalender/${this.state.itemToDelete}`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
        });
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, ExpertTipsList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  clearStartDate = () => {
    const {
      offset,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      sortDate: null,
      startDateOpen: false,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      offset,
      null,
      SelectedSport,
      serachValue,
      sortType,
      sortData,
      SelectedUsers
    );
  };

  handleSortStartDate = (date) => {
    const {
      offset,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAllEvent(
        0,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        SelectedSport,
        serachValue,
        sortType,
        sortData,
        SelectedUsers
      );
    }
  };

  handleModalsportchange = (e) => {
    const { expertTipsValues, errorModalSport } = this.state;
    this.setState({
      selectedModalSport: e.value,
      errorModalSport: e.value ? "" : errorModalSport,
      TrackData: [],
      TrackData: [],
      TrackCount: 0,
      TrackPage: 0,
      searchTrack: [],
      searchTrackCount: 0,
      searchTrackPage: 0,
      isTrackSearch: "",
      TrackRaceData: [],
      BetRunnerData: [],
      EachWayRunnerData: [],
      FilteredEachWayRunnerData: [],
      LayRunnerData: [],
      TrackAllRaceData: [],
      selectedValues: {},
      errorStateCode: "",
      expertTipsValues: {
        ...this.state.expertTipsValues,
        modalTrackId: null,
        modalTrackBetRace: null,
        modalBetRunnerId: null,
        modalTrackEachWayRace: null,
        modalTrackWayRunnerId: null,
        modalTrackLayRace: null,
        modalTrackLayRunnerId: null,
      },
    });
    // this.setState({
    //   selectedModalSportObj: e.value,
    // });
    this.fetchAllTrack(0, e.value);
  };

  // async fetchAllTrack(date, sportId) {
  //   this.setState({ isTrackLoading: true });
  //   const passApi = `expertTips/getDropDown?sportId=${sportId}&date=${date}&timezone=Asia/Calcutta`;
  //   const { status, data } = await axiosInstance.get(passApi);
  //   if (status === 200) {
  //     this.setState({ isTrackLoading: false });
  //     let newdata = [];
  //     let Track = data?.result?.map((item) => {
  //       newdata.push({
  //         label: item?.eventName,
  //         value: item?.id,
  //         trackId: item?.trackId,
  //       });
  //     });
  //     let filterData = _.unionBy(this.state?.TrackData, newdata);
  //     const sortedData = filterData?.sort((a, b) => {
  //       return a?.label.localeCompare(b?.label);
  //     });
  //     let finalData = _.uniqBy(sortedData, function (e) {
  //       return e.value;
  //     });
  //     this.setState({
  //       TrackData: finalData,
  //     });
  //   } else {
  //     this.setState({ isTrackLoading: false });
  //   }
  // }
  async fetchAllTrack(TrackPage, sportId, selectedItem) {
    this.setState({ isTrackLoading: true });
    const passApi = `track?limit=20&offset=${TrackPage}&matchString=&sportId=${sportId}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackLoading: false });
      let count = data?.result?.count / 20;
      let newdata = [];
      let Track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
          trackId: item?.id,
        });
      });
      if (selectedItem) {
        newdata.push({
          label: selectedItem?.Track?.name,
          value: selectedItem?.TrackId,
          trackId: selectedItem?.TrackId,
        });
      }
      let filterData = _.unionBy(this.state?.TrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      finalData = finalData?.filter((item) => item?.value !== 0);
      this.setState({
        TrackData: finalData,
        TrackCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomTrack = (e, type) => {
    let {
      TrackCount,
      TrackPage,
      isTrackSearch,
      searchTrackCount,
      searchTrackPage,
    } = this.state;
    if (
      isTrackSearch !== "" &&
      searchTrackCount !== Math.ceil(searchTrackPage / 20 + 1)
    ) {
      this.handleTrackInputChange(
        searchTrackPage + 20,
        isTrackSearch,
        this.state.selectedModalSport
      );
      this.setState({
        searchTrackPage: searchTrackPage + 20,
      });
    } else {
      if (
        TrackCount !== (TrackCount == 1 ? 1 : Math.ceil(TrackPage / 20)) &&
        isTrackSearch == ""
      ) {
        this.fetchAllTrack(TrackPage + 20, this.state.selectedModalSport);
        this.setState({
          TrackPage: TrackPage + 20,
        });
      }
    }
  };
  handleTrackInputChange = (TrackPage, value, sportId) => {
    const passApi = `track?limit=20&offset=${TrackPage}&matchString=${value}&sportId=${sportId}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label:
              item?.name +
              (item?.Country?.countryCode
                ? " (" + item?.Country?.countryCode + ")"
                : ""),
            //   +
            //   " " +
            //   "(" +
            //   (item?.sportId === 1
            //     ? "Horse"
            //     : item?.sportId === 3
            //     ? "Greyhound"
            //     : item?.sportId === 2
            //     ? "Harness"
            //     : "") +
            //   ")",
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTrack: finalData,
          searchTrackCount: Math.ceil(count),
          isTrackSearch: value,
          searchTrackPage: TrackPage,
        });
      }
    });
  };

  async eventRaceList(eventId, info, id) {
    this.setState({ isTrackRaceLoading: true });
    const passApi = `expertTips/getDropDown?eventId=${eventId}&timezone=Asia/Calcutta`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackRaceLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label: "R" + item?.raceNumber + " " + item?.raceName,
          value: item?.id,
          trackCondition: item?.trackCondition,
        });
      });
      const selectedRaces = info
        ? newdata?.filter((race) => info?.raceIds?.includes(race?.value))
        : [];
      if (info) {
        this.setState({
          isLoading: false,
          extraInfoValues: {
            adShortCode: info?.raceInformation,
            raceDetail: info?.raceHistory,
            preview: info?.usefulFacts,
            trackCondition: info?.condition,
            seletedId: id,
          },
          modalSelectedRace: selectedRaces,
          TrackRaceData: newdata,
          defaultUploadImage: info?.featuredImage,
        });
      } else {
        this.setState({
          TrackRaceData: newdata,
          extraInfoValues: {
            ...this.state.extraInfoValues,
            seletedId: id,
          },
        });
      }
    } else {
      this.setState({ isTrackRaceLoading: false });
    }
  }

  async eventAllRaceList(eventId) {
    this.setState({ isTrackAllRaceLoading: true });
    const passApi = `expertTips/getDropDown?eventId=${eventId}&type=race&timezone=Asia/Calcutta`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackAllRaceLoading: false });

      let newdata = [];
      const sortingRace = data?.result?.sort(
        (a, b) => a.raceNumber - b.raceNumber
      );
      let TrackAllRace = data?.result?.map((item) => {
        newdata.push({
          raceId: item?.id,
          raceNumber: "Race" + item?.raceNumber,
          runner: item?.runner?.map((obj) => {
            return {
              label:
                obj?.runnerNumber +
                "." +
                " " +
                obj?.animal?.name +
                " " +
                "(" +
                obj?.barrierNumber +
                ")",
              value: obj?.id,
            };
          }),
        });
      });
      this.setState({
        TrackAllRaceData: newdata,
      });
    } else {
      this.setState({ isTrackAllRaceLoading: false });
    }
  }

  async typeRaceRunner(RaceId, Isfirst) {
    const { selectedValues, expertTipsValues } = this.state;
    this.setState({ istypeRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=Asia/Calcutta`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ istypeRaceRunnerLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      if (Isfirst == "first") {
        this.setState({
          BetRunnerData: newdata,
        });
      } else {
        this.setState({
          BetRunnerData: newdata,
          expertTipsValues: {
            ...expertTipsValues,
            modalBetRunnerId: selectedValues[RaceId]?.[0],
            modalTrackBetRace: RaceId,
          },
        });
      }
    } else {
      this.setState({ istypeRaceRunnerLoading: false });
    }
  }

  async eachWayRaceRunner(RaceId, selectedRaceValue) {
    const { selectedValues } = this.state;
    this.setState({ isEachWayRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=Asia/Calcutta`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isEachWayRaceRunnerLoading: false });

      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      let filterData;
      if (selectedRaceValue) {
        filterData = newdata?.filter((runner) =>
          selectedRaceValue[RaceId]?.includes(runner?.value)
        );
      } else {
        filterData = newdata?.filter((runner) =>
          selectedValues[RaceId]?.includes(runner?.value)
        );
      }
      this.setState({
        EachWayRunnerData: newdata,
        FilteredEachWayRunnerData: filterData,
      });
    } else {
      this.setState({ isEachWayRaceRunnerLoading: false });
    }
  }

  async layRaceRunner(RaceId) {
    this.setState({ isLayRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=Asia/Calcutta`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isLayRaceRunnerLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      this.setState({
        LayRunnerData: newdata,
      });
    } else {
      this.setState({ isLayRaceRunnerLoading: false });
    }
  }

  async wpCategory() {
    this.setState({ wpCategoryLoading: true });
    const passApi = `/expertTips/getWpCategories`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ wpCategoryLoading: false });
      let newdata = [];
      let categoryId = data?.result?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      this.setState({
        wpCategoryData: newdata,
      });
    } else {
      this.setState({ wpCategoryLoading: false });
    }
  }

  handleSave = async () => {
    const {
      expertTipsValues,
      selectedModalSport,
      TrackAllRaceData,
      selectedValues,
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      defaultImage,
    } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });

      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      let payload = {
        TrackId: expertTipsValues?.modalTrackId,
        // date: moment(expertTipsValues?.startDate).format("YYYY-MM-DD"),
        SportId: selectedModalSport,
        raceName: expertTipsValues?.raceName,
        type: expertTipsValues?.type,
        distance: expertTipsValues?.distance,
        raceCourseUrl: expertTipsValues?.raceCourseUrl,
        priceMoney: expertTipsValues?.prizeMoney,
      };
      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            featuredImage: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      }
      try {
        const { status, data } = await axiosInstance.post(
          `/racingFeatured/featuredCalender`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData
            // SelectedUsers
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          defaultImage: [],
          defaultUploadImage: "",
        });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const {
      expertTipsValues,
      selectedModalSport,
      TrackAllRaceData,
      selectedValues,
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      defaultImage,
      selectedStatus,
    } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      let payload = {
        TrackId: expertTipsValues?.modalTrackId,
        // date: moment(expertTipsValues?.startDate).format("YYYY-MM-DD"),
        SportId: selectedModalSport,
        raceName: expertTipsValues?.raceName,
        type: expertTipsValues?.type,
        distance: expertTipsValues?.distance,
        raceCourseUrl: expertTipsValues?.raceCourseUrl,
        priceMoney: expertTipsValues?.prizeMoney,
      };
      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            featuredImage: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          featuredImage:
            this.state.defaultUploadImage &&
            this.state.defaultUploadImage !== ""
              ? this.state.defaultUploadImage
              : null,
        };
      }
      try {
        const { status, data } = await axiosInstance.put(
          `/racingFeatured/featuredCalender/${SelectedId}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
        });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleExtraInfoSave = async () => {
    const { isAddExtraInfoModalOpen, extraInfoValues, modalSelectedRace } =
      this.state;
    let selectedRace = [];
    const raceSelected = modalSelectedRace?.map((race) =>
      selectedRace?.push(race?.value)
    );
    if (this.handleExtraInfoValidate()) {
      let payload = {
        raceInformation: extraInfoValues?.adShortCode,
        // condition: extraInfoValues?.trackCondition,
        condition: null,
        raceHistory: extraInfoValues?.raceDetail,
        usefulFacts: extraInfoValues?.preview,
        // race: selectedRace,
      };

      try {
        const { status, data } = await axiosInstance.put(
          `/racingFeatured/featuredCalender/raceDetails/${extraInfoValues?.seletedId}`,
          payload
        );
        if (status === 200) {
          this.toggleAddExtaraInfoModal();
          this.setState({
            isLoading: false,
          });
          // this.fetchAllEvent(
          //   offset,
          //   sortDate,
          //   SelectedSport,
          //   serachValue,
          //   sortType,
          //   sortData
          //   // SelectedUsers
          // );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.toggleAddExtaraInfoModal();
          this.setState({
            isLoading: false,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.toggleAddExtaraInfoModal();
        this.setState({
          isLoading: false,
          isAddExtraInfoModalOpen: false,
        });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };
  handleClearClick = () => {
    this.setState({ serachValue: "" });
  };

  sortLabelHandler = (type) => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortId,
        SelectedUsers
      );
      this.setState({
        sortId: !sortId,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "eventName") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortEventName,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: !sortEventName,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "eventDate") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortEventDate,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: !sortEventDate,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "SportId") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortSportType,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: !sortSportType,
        sortUser: true,
        currentPage: 1,
      });
    } else {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortUser,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: !sortUser,
        currentPage: 1,
      });
    }
  };

  handleKeyDown = (event) => {
    var { sortDate, SelectedSport, serachValue, sortType, SelectedUsers } =
      this.state;
    if (event.key === "Enter") {
      this.fetchAllEvent(
        0,
        sortDate,
        SelectedSport,
        serachValue,
        sortType,
        false,
        SelectedUsers
      );
      this.setState({ currentPage: 1 });
    }
  };

  addInfoModal = (item) => {
    this.setState({ isAddExtraInfoModalOpen: true });

    this.fetchIndividualExtraInfo(item?.id, item);
  };
  toggleAddExtaraInfoModal = () => {
    this.setState({
      isAddExtraInfoModalOpen: !this.state.isAddExtraInfoModalOpen,
      extraInfoValues: {
        adShortCode: "",
        raceDetail: "",
        preview: "",
        trackCondition: "",
        seletedId: "",
      },
      modalSelectedRace: [],
      raceDetailContent: "",
      previewContent: "",
    });
  };

  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files, errorBannerImage: "" });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };
  handleRaceDetailChange = (content) => {
    const { extraInfoValues, errorRaceDetail } = this.state;
    this.setState({
      extraInfoValues: {
        ...extraInfoValues,
        raceDetail: content,
      },
      errorRaceDetail: content?.trim() == "" ? errorRaceDetail : "",
    });
  };
  handlePreviewChange = (content) => {
    const { extraInfoValues, errorPreview } = this.state;
    this.setState({
      extraInfoValues: {
        ...extraInfoValues,
        preview: content,
      },
      errorPreview: content?.trim() == "" ? errorPreview : "",
    });
  };
  fetchIndividualExtraInfo = async (id, item) => {
    this.setState({ isLoading: true });
    const { extraInfoValues } = this.state;
    try {
      const passApi = `/racingFeatured/featuredCalender/raceDetails/${id}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          isLoading: false,
        });
        const info = data?.result;
        this.eventRaceList(item?.EventId, info, id);

        const htmlRaceDetailString = info?.raceHistory
          ? he.decode(String(info?.raceHistory))
          : "";
        if (typeof htmlRaceDetailString === "string") {
          this.setState({
            raceDetailContent: htmlRaceDetailString,
          });
        }
        const htmlPreviewString = info?.usefulFacts
          ? he.decode(String(info?.usefulFacts))
          : "";
        if (typeof htmlPreviewString === "string") {
          this.setState({
            previewContent: htmlPreviewString,
          });
        }
      } else {
        this.setState({
          isLoading: false,
          extraInfoValues: {
            ...extraInfoValues,
            seletedId: id,
          },
        });
      }
    } catch {
      this.eventRaceList(item?.EventId, null, id);
      this.setState({
        isLoading: false,
        extraInfoValues: {
          ...extraInfoValues,
          seletedId: id,
        },
      });
    }
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      ExpertTipsList,
      ExpertTipsCount,
      serachValue,
      AllSportsOption,
      SelectedSport,
      sortDate,
      startDateOpen,
      expertTipsValues,
      modalSportsOption,
      selectedModalSport,
      isTrackLoading,
      searchTrack,
      isTrackSearch,
      isTrackRaceLoading,
      istypeRaceRunnerLoading,
      isEachWayRaceRunnerLoading,
      isLayRaceRunnerLoading,
      TrackData,
      TrackRaceData,
      BetRunnerData,
      EachWayRunnerData,
      FilteredEachWayRunnerData,
      LayRunnerData,
      TrackAllRaceData,
      selectedValues,
      // errorStartDate,
      errorModalSport,
      errorTrack,
      errorraceName,
      errortype,
      errorBetprice,
      errorBetRace,
      errorBetRaceRunner,
      errorprizeMoney,
      errorEachWayPrice,
      errorEachWayRace,
      errorEachWayRaceRunner,
      errordistance,
      errorRaceCourseUrl,
      errorLayPrice,
      errorLayRace,
      errorLayRaceRunner,
      errorBannerImage,
      errorRace,
      tipsModalDetailsOpen,
      tipsModalDetails,
      tipsModalDetailsIsLoading,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      AllUserOption,
      defaultImage,
      defaultUploadImage,
      wpCategoryLoading,
      wpCategoryData,
      errorModalwpCategory,
      errorStateCode,
      selectedStatus,
      isAddExtraInfoModalOpen,
      errorAdShortCode,
      errorRaceDetail,
      errorPreview,
      errorTrackCondition,
      raceDetailContent,
      previewContent,
      extraInfoValues,
      modalSelectedRace,
    } = this.state;
    const pageNumbers = [];
    if (ExpertTipsCount > 0) {
      for (let i = 1; i <= Math.ceil(ExpertTipsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    const user = fetchFromStorage(identifiers.user);
    return (
      <>
        {!isAddExtraInfoModalOpen ? (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              {/* <Paper className="pageWrapper"> */}
              {messageBox?.display && (
                <ActionMessage
                  message={messageBox?.message}
                  type={messageBox?.type}
                  styleClass={messageBox?.styleClass}
                />
              )}
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>

                  <Typography className="active_p">
                    {" "}
                    Featured Calender{" "}
                  </Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={3}>
                  <Typography variant="h1" align="left">
                    Featured Calender
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={9}
                  className="admin-filter-wrap admin-fixture-wrap"
                >
                  <Select
                    className="React teamsport-select external-select"
                    classNamePrefix="select"
                    placeholder="Select Race"
                    value={AllSportsOption?.find((item) => {
                      return item?.value == SelectedSport;
                    })}
                    //   isLoading={isLoading}
                    onChange={(e) => this.handlesportchange(e)}
                    options={AllSportsOption}
                  />
                  <TextField
                    placeholder="Search "
                    size="small"
                    variant="outlined"
                    className="event-search"
                    onKeyDown={(e) => this.handleKeyDown(e)}
                    value={serachValue}
                    onChange={(e) => {
                      this.setState({ serachValue: e.target.value });
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <img src={SearchIcons} alt="icon" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          {serachValue && (
                            <IconButton
                              onClick={() => this.handleClearClick()}
                              edge="end"
                              style={{ minWidth: "unset" }}
                              size="large"
                            >
                              <CancelIcon />
                            </IconButton>
                          )}
                        </InputAdornment>
                      ),
                    }}
                    style={{
                      background: "#ffffff",
                    }}
                  />
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455c7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                    }}
                    onClick={() => {
                      this.fetchAllEvent(
                        0,
                        sortDate,
                        SelectedSport,
                        serachValue,
                        sortType,
                        false,
                        SelectedUsers
                      );
                      this.setState({ currentPage: 1 });
                    }}
                  >
                    Search
                  </Button>

                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    className={
                      Boolean(this.state.errorWpCreds) ? "disabled-btn" : ""
                    }
                    onClick={this.inputModal(null, "create")}
                    disabled={Boolean(this.state.errorWpCreds)}
                  >
                    Add New
                  </Button>
                </Grid>
              </Grid>

              {isLoading && <Loader />}
              {!isLoading && ExpertTipsList?.length === 0 && (
                <p>No Data Available</p>
              )}

              {!isLoading && ExpertTipsList?.length > 0 && (
                <TableContainer component={Paper}>
                  <Table
                    className="listTable market-error-table"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer" }}
                        >
                          DID
                          {/* <TableSortLabel
                        active={true}
                        direction={
                          sortType === "id"
                            ? sortId
                              ? "asc"
                              : "desc"
                            : "desc"
                        }
                      /> */}
                        </TableCell>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("eventName")}
                          style={{ cursor: "pointer" }}
                        >
                          Race Course
                          {/* <TableSortLabel
                        active={true}
                        direction={
                          sortType === "eventName"
                            ? sortEventName
                              ? "asc"
                              : "desc"
                            : "asc"
                        }
                      /> */}
                        </TableCell>
                        {/* <TableCell
                          style={{ cursor: "pointer" }}
                        >
                          Event Date
                        
                        </TableCell> */}

                        <TableCell
                          // onClick={() => this.sortLabelHandler("SportId")}
                          style={{ cursor: "pointer" }}
                        >
                          Sport Type
                          {/* <TableSortLabel
                        active={true}
                        direction={
                          sortType === "SportId"
                            ? sortSportType
                              ? "asc"
                              : "desc"
                            : "asc"
                        }
                      /> */}
                        </TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Prize</TableCell>
                        <TableCell>Race Name</TableCell>
                        <TableCell>Distance</TableCell>
                        <TableCell style={{ width: "15%" }}>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {ExpertTipsList?.map((item) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={item?.id}
                          >
                            <TableCell>{item?.id} </TableCell>
                            <TableCell>{item?.raceCourse}</TableCell>
                            {/* <TableCell>
                              {item?.date
                                ? moment(item?.date).format("DD/MM/YYYY")
                                : ""}
                            </TableCell> */}
                            {/* <TableCell style={{ textTransform: "capitalize" }}>
                            {item?.status}
                          </TableCell> */}
                            <TableCell>{item?.Sport?.sportName}</TableCell>
                            <TableCell>{item?.type}</TableCell>
                            <TableCell>
                              {item?.priceMoney ? `$${item?.priceMoney}` : ""}
                            </TableCell>
                            <TableCell>{item?.raceName}</TableCell>
                            <TableCell>{item?.distance}</TableCell>
                            {/* <TableCell>
                            <Button
                              className="table-btn"
                              variant="contained"
                              style={{
                                fontSize: "9px",
                                backgroundColor: "#4455C7",
                                color: "#fff",
                                fontWeight: "400",
                                textTransform: "none",
                                width: "max-content",
                              }}
                              onClick={() => {
                                this.tipsDetailsModalOpen(item);
                              }}
                            >
                              Tips Details
                            </Button>
                          </TableCell> */}
                            <TableCell>
                              <Box
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  columnGap: "3px",
                                }}
                              >
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  style={{ cursor: "pointer", minWidth: "0px" }}
                                  className={`table-btn edit-btn ${
                                    // moment(item?.Event?.eventDate).isBefore(
                                    //   moment(),
                                    //   "day"
                                    // ) ||
                                    Boolean(this.state.errorWpCreds)
                                      ? "disabled-btn"
                                      : ""
                                  }`}
                                  disabled={
                                    // moment(item?.Event?.eventDate).isBefore(
                                    //   moment(),
                                    //   "day"
                                    // ) ||
                                    Boolean(this.state.errorWpCreds)
                                  }
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  style={{ cursor: "pointer", minWidth: "0px" }}
                                  className={
                                    Boolean(this.state.errorWpCreds)
                                      ? "disabled-btn table-btn delete-btn"
                                      : "table-btn delete-btn"
                                  }
                                  disabled={Boolean(this.state.errorWpCreds)}
                                >
                                  Delete
                                </Button>
                                <Button
                                  className="table-btn"
                                  variant="contained"
                                  style={{
                                    fontSize: "14px",
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    fontWeight: "400",
                                    textTransform: "none",
                                    width: "max-content",
                                    maxHeight: "40px",
                                    borderRadius: "8px",
                                  }}
                                  onClick={() => this.addInfoModal(item)}
                                >
                                  Add Extra Info
                                </Button>
                              </Box>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                ExpertTipsCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              {/* </Paper> */}

              <ShowModal
                isModalOpen={isModalOpen}
                onClose={this.toggleModal}
                Content="Are you sure you want to delete?"
                onOkayLabel="Yes"
                onOkay={this.deleteItem}
                onCancel={this.toggleModal}
              />
            </Grid>
          </Grid>
        ) : (
          <>
            <Grid container className="page-content adminLogin">
              <Grid item xs={12} className="pageWrapper">
                <Box className="bredcrumn-wrap">
                  <Breadcrumbs
                    separator="/"
                    aria-label="breadcrumb"
                    className="breadcrumb"
                  >
                    <Link underline="hover" color="inherit" to="/dashboard">
                      Home
                    </Link>
                    <Link
                      underline="hover"
                      color="inherit"
                      to="/featured-calender"
                    >
                      Featured Calender
                    </Link>
                    <Typography className="active_p">Add Extra Info</Typography>
                  </Breadcrumbs>
                </Box>
                <Grid container direction="row" alignItems="center">
                  <Grid item xs={4}>
                    <Typography variant="h1" align="left">
                      Add Extra Info
                    </Typography>
                  </Grid>
                  <Grid
                    item
                    xs={8}
                    style={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "6px 10px",
                        // marginTop: "5px",
                      }}
                      onClick={this.toggleAddExtaraInfoModal}
                    >
                      Back
                    </Button>
                  </Grid>
                </Grid>
                {isLoading ? (
                  <Loader />
                ) : (
                  <Box className="expert-tips-modal">
                    <Grid container>
                      <Box className="race-runner-wrap mb-8">
                        <Grid
                          item
                          xs={12}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text mb-8"
                        >
                          <label className="modal-label">
                            Race Comment
                            <span className="color-red">*</span>
                          </label>
                          <TextField
                            className="teamsport-textfield"
                            variant="outlined"
                            type="textarea"
                            color="primary"
                            size="small"
                            multiline
                            maxRows={3}
                            rows={3}
                            placeholder="Race Comment"
                            value={extraInfoValues?.adShortCode}
                            onChange={(e) =>
                              this.setState({
                                extraInfoValues: {
                                  ...extraInfoValues,
                                  adShortCode: e?.target?.value,
                                },
                                errorAdShortCode: e?.target?.value
                                  ? ""
                                  : errorAdShortCode,
                              })
                            }
                          />
                          {errorAdShortCode ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorAdShortCode}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                      </Box>
                      {/* <Box className="race-runner-wrap mb-8">
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">
                          Past winner Race
                          <span className="color-red">*</span>
                        </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Select Race for Past winners"
                          isMulti
                          // isDisabled={!expertTipsValues?.modalTrackId}
                          isLoading={isTrackRaceLoading}
                          value={modalSelectedRace}
                          options={TrackRaceData}
                          onChange={(e) => {
                            this.setState({
                              modalSelectedRace: e,
                              errorRace: e?.value ? "" : errorRace,
                              extraInfoValues: {
                                ...extraInfoValues,
                                trackCondition: e?.[0]?.trackCondition,
                              },
                            });
                          }}
                          menuPortalTarget={document.body}
                          styles={{
                            menuPortal: (base) => ({
                              ...base,
                              zIndex: 9999,
                            }),
                          }}
                        />
                        {errorRace ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorRace}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text mb-8"
                      >
                        <label className="modal-label">
                          {" "}
                          Conditions <span className="color-red">*</span>{" "}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          color="primary"
                          size="small"
                          placeholder="Condition"
                          value={extraInfoValues?.trackCondition}
                          onChange={(e) =>
                            this.setState({
                              extraInfoValues: {
                                ...extraInfoValues,
                                trackCondition: e?.target?.value,
                              },
                            })
                          }
                        />
                        {errorTrackCondition ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTrackCondition}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Box> */}
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          textAlign: "left",
                        }}
                        className="teamsport-text mb-8"
                      >
                        <label className="modal-label">
                          {" "}
                          Race History <span className="color-red">*</span>
                        </label>

                        <div className="featured-race-editor">
                          <SunEditor
                            onChange={this.handleRaceDetailChange}
                            setContents={raceDetailContent}
                            setOptions={{
                              buttonList: [
                                ["undo", "redo"],
                                ["font", "fontSize", "formatBlock"],
                                [
                                  "bold",
                                  "underline",
                                  "italic",
                                  "strike",
                                  "subscript",
                                  "superscript",
                                ],
                                ["removeFormat"],
                                ["outdent", "indent"],
                                ["align", "horizontalRule", "list", "table"],
                                ["link"],
                                ["fullScreen", "showBlocks", "codeView"],
                              ],
                            }}
                          />
                        </div>
                        {errorRaceDetail ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRaceDetail}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Box className="race-runner-wrap mb-8">
                        <Grid
                          item
                          xs={12}
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            textAlign: "left",
                          }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">
                            {" "}
                            Useful Facts
                            <span className="color-red"> *</span>{" "}
                          </label>

                          <div className="featured-race-editor">
                            <SunEditor
                              onChange={this.handlePreviewChange}
                              setContents={previewContent}
                              setOptions={{
                                buttonList: [
                                  ["undo", "redo"],
                                  ["font", "fontSize", "formatBlock"],
                                  [
                                    "bold",
                                    "underline",
                                    "italic",
                                    "strike",
                                    "subscript",
                                    "superscript",
                                  ],
                                  ["removeFormat"],
                                  ["outdent", "indent"],
                                  ["align", "horizontalRule", "list", "table"],
                                  ["link"],
                                  ["fullScreen", "showBlocks", "codeView"],
                                ],
                              }}
                            />
                          </div>
                          {errorPreview ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorPreview}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                      </Box>
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          <ButtonComponent
                            className="mt-3 admin-btn-green"
                            onClick={this.handleExtraInfoSave}
                            color="primary"
                            value={!isLoading ? "Save" : "Loading..."}
                            disabled={isLoading}
                          />

                          <ButtonComponent
                            onClick={this.toggleAddExtaraInfoModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </Grid>
            </Grid>
          </>
        )}
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={this.toggleInputModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {!isEditMode
                ? "Add Race To Featured Calender"
                : "Update Race To Featured Calender"}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleInputModal}
            />
            <Grid container className="page-content adminLogin">
              <Grid item xs={12} className="pageWrapper">
                <Box className="expert-tips-modal featured-calender-modal">
                  <Grid container>
                    <Box className="race-runner-wrap mb-8">
                      {/* <Grid
                        item
                        xs={6}
                        className="date-time-picker-wrap"
                        style={{ marginBottom: "15px" }}
                      >
                        <label className="modal-label">
                          {" "}
                          Select Date <span className="color-red">*</span>
                        </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            variant="inline"
                            inputVariant="outlined"
                            ampm={false}
                            value={
                              expertTipsValues?.startDate
                                ? typeof expertTipsValues?.startDate ===
                                  "string"
                                  ? parseISO(
                                      moment(expertTipsValues?.startDate)
                                        .tz(timezone)
                                        .format("YYYY-MM-DD")
                                    )
                                  : expertTipsValues?.startDate
                                : null
                            }
                            onChange={(e) => {
                              // this.fetchAllTrack(
                              //   moment(e).tz(timezone).format("YYYY-MM-DD"),
                              //   1
                              // );
                              this.fetchAllTrack(0, 1);
                              this.setState({
                                expertTipsValues: {
                                  ...expertTipsValues,
                                  startDate: moment(e)
                                    .tz(timezone)
                                    .format("YYYY-MM-DD"),
                                  modalTrackId: null,
                                },
                                errorStateCode: "",
                                selectedModalSport: 1,
                                TrackData: [],
                                TrackRaceData: [],
                                BetRunnerData: [],

                                TrackAllRaceData: [],
                                selectedValues: {},
                              });
                            }}
                            autoOk={true}
                            format="yyyy/MM/dd"
                            className="date-time-picker"
                            disablePast={!isEditMode}
                          />
                        </LocalizationProvider>
                        {errorStartDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorStartDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid> */}
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">
                          {" "}
                          Race Type <span className="color-red">*</span>
                        </label>
                        <Select
                          className="React teamsport-select external-select"
                          classNamePrefix="select"
                          placeholder="Select Race"
                          menuPosition="fixed"
                          value={
                            selectedModalSport &&
                            modalSportsOption?.find((item) => {
                              return item?.value == selectedModalSport;
                            })
                          }
                          //   isLoading={isLoading}
                          onChange={(e) => this.handleModalsportchange(e)}
                          options={modalSportsOption}
                        />
                        {errorModalSport ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorModalSport}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">
                          {" "}
                          Track <span className="color-red">*</span>
                        </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Track"
                          isDisabled={!selectedModalSport}
                          isLoading={isTrackLoading}
                          value={
                            expertTipsValues?.modalTrackId &&
                            (isTrackSearch
                              ? searchTrack?.find((item) => {
                                  return (
                                    item?.value ==
                                    expertTipsValues?.modalTrackId
                                  );
                                })
                              : TrackData?.find((item) => {
                                  return (
                                    item?.value ==
                                    expertTipsValues?.modalTrackId
                                  );
                                }))
                          }
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomTrack(e)
                          }
                          onInputChange={(e) =>
                            this.handleTrackInputChange(
                              0,
                              e,
                              this.state.selectedModalSport
                            )
                          }
                          // value={
                          //   eventValues?.modalTrackId
                          //     ? isTrackSearch
                          //       ? searchTrack?.find((item) => {
                          //           return (
                          //             item?.value == eventValues?.modalTrackId
                          //           );
                          //         })
                          //       : TrackData?.find((item) => {
                          //           return (
                          //             item?.value == eventValues?.modalTrackId
                          //           );
                          //         })
                          //     : null
                          // }
                          options={isTrackSearch ? searchTrack : TrackData}
                          // options={TrackData}
                          onChange={(e) => {
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                modalTrackId: e?.value,
                                modalTrackBetRace: null,
                                modalTrackEachWayRace: null,
                                modalTrackLayRace: null,
                                modalBetRunnerId: null,
                                modalTrackWayRunnerId: null,
                                modalTrackLayRunnerId: null,
                              },
                              TrackRaceData: [],
                              BetRunnerData: [],
                              EachWayRunnerData: [],
                              FilteredEachWayRunnerData: [],
                              LayRunnerData: [],
                              TrackAllRaceData: [],
                              selectedValues: {},
                              errorTrack: e?.value ? "" : errorTrack,
                            });
                            this.eventRaceList(e?.value, null);
                            this.eventAllRaceList(e?.value);
                            // this.handleStateCodeError(e?.value);
                          }}
                        />
                        {errorTrack ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorTrack}
                          </p>
                        ) : (
                          ""
                        )}
                        {errorStateCode ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorStateCode}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Box>
                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text mb-8"
                      >
                        <label className="modal-label">
                          {" "}
                          Race Name <span className="color-red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="text"
                          //   multiline
                          //   maxRows={2}
                          color="primary"
                          size="small"
                          placeholder="Race Name"
                          value={expertTipsValues?.raceName}
                          onChange={(e) =>
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                raceName: e?.target?.value,
                              },
                              errorraceName: e?.target?.value
                                ? ""
                                : errorraceName,
                            })
                          }
                        />
                        {errorraceName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorraceName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Type
                          <span className="color-red">*</span>{" "}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="text"
                          //   multiline
                          //   maxRows={2}
                          color="primary"
                          size="small"
                          placeholder="Type"
                          value={expertTipsValues?.type}
                          onChange={(e) =>
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                type: e?.target?.value,
                              },
                              errortype: e?.target?.value ? "" : errortype,
                            })
                          }
                          //   disabled={
                          //     TrackAllRaceData?.length !==
                          //       Object?.keys(selectedValues).length ||
                          //     !Boolean(expertTipsValues?.modalTrackId)
                          //   }
                        />
                        {errortype ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errortype}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Box>
                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Prize money <span className="color-red">*</span>
                          {/* <span className="color-red">*</span> */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="number"
                          //   multiline
                          //   maxRows={2}
                          color="primary"
                          size="small"
                          placeholder="Prize money"
                          value={expertTipsValues?.prizeMoney}
                          //   disabled={
                          //     TrackAllRaceData?.length !==
                          //       Object?.keys(selectedValues).length ||
                          //     !Boolean(expertTipsValues?.modalTrackId)
                          //   }
                          onChange={(e) => {
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                prizeMoney: e?.target?.value,
                              },

                              errorprizeMoney: e?.target?.value
                                ? ""
                                : errorprizeMoney,
                            });
                            if (!e?.target?.value) {
                              this.setState({
                                expertTipsValues: {
                                  ...expertTipsValues,
                                  modalTrackEachWayRace: null,
                                  modalTrackWayRunnerId: null,
                                  prizeMoney: "",
                                },
                              });
                            }
                          }}
                        />
                        {errorprizeMoney ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorprizeMoney}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Distance <span className="color-red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Distance"
                          value={expertTipsValues?.distance}
                          onChange={(e) => {
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                distance: e?.target?.value,
                              },
                              errordistance: e?.target?.value
                                ? ""
                                : errordistance,
                            });
                            if (!e?.target?.value) {
                              this.setState({
                                expertTipsValues: {
                                  ...expertTipsValues,
                                  distance: "",
                                },
                              });
                            }
                          }}
                        />
                        {errordistance ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errordistance}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Box>

                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Race Course URL <span className="color-red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Race Course URL"
                          value={expertTipsValues?.raceCourseUrl}
                          onChange={(e) => {
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                raceCourseUrl: e?.target?.value,
                              },
                              errorRaceCourseUrl: e?.target?.value
                                ? ""
                                : errorRaceCourseUrl,
                            });
                            if (!e?.target?.value) {
                              this.setState({
                                expertTipsValues: {
                                  ...expertTipsValues,
                                  raceCourseUrl: "",
                                },
                              });
                            }
                          }}
                        />
                        {errorRaceCourseUrl ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRaceCourseUrl}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Box>
                    <div
                      className="blog-file-upload"
                      style={{ width: "100%", marginTop: "0px" }}
                    >
                      <label className="modal-label"> Banner Image </label>
                      <FileUploader
                        onDrop={(image) =>
                          this.handleFileUpload("defaultImage", image)
                        }
                        style={{ marginTop: "5px" }}
                      />
                      <Box
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <div className="logocontainer">
                          {defaultImage?.length > 0
                            ? defaultImage?.map((file, index) => (
                                <img
                                  className="auto-width"
                                  key={index}
                                  src={file.preview}
                                  alt="player"
                                />
                              ))
                            : defaultUploadImage &&
                              defaultUploadImage !== "" && (
                                <img
                                  className="auto-width"
                                  src={
                                    defaultUploadImage?.includes("uploads")
                                      ? config.mediaUrl + defaultUploadImage
                                      : defaultUploadImage
                                  }
                                  alt="player"
                                />
                              )}
                        </div>
                        {(defaultImage?.length > 0 ||
                          (defaultUploadImage &&
                            defaultUploadImage !== "")) && (
                          <Box className="delete-icon-wrap">
                            <DeleteIcon
                              className="delete-icon"
                              onClick={() => this.handleFeatureLogoRemove()}
                              style={{ cursor: "pointer" }}
                            />
                          </Box>
                        )}
                      </Box>
                      {errorBannerImage ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorBannerImage}
                        </p>
                      ) : (
                        ""
                      )}
                    </div>
                  </Grid>

                  <Grid container>
                    <Grid item xs={3}>
                      <div style={{ marginTop: "20px", display: "flex" }}>
                        {!isEditMode ? (
                          <ButtonComponent
                            className="mt-3 admin-btn-green"
                            onClick={this.handleSave}
                            color="primary"
                            value={!isLoading ? "Save" : "Loading..."}
                            disabled={isLoading}
                          />
                        ) : (
                          <ButtonComponent
                            className="mt-3 admin-btn-green"
                            onClick={this.handleUpdate}
                            color="secondary"
                            value={!isLoading ? "Update" : "Loading..."}
                            disabled={isLoading}
                          />
                        )}

                        <ButtonComponent
                          onClick={this.toggleInputModal}
                          className="mr-lr-30 back-btn-modal"
                          value="Back"
                        />
                      </div>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
            </Grid>
          </div>
        </Modal>
        <Modal
          className="modal modal-input tips-modal-details"
          open={tipsModalDetailsOpen}
          onClose={this.toggleTipsDetailsModalOpen}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">
              {tipsModalDetails?.tips?.Event?.eventName} Tips Details
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleTipsDetailsModalOpen}
            />
            {tipsModalDetailsIsLoading ? (
              <Box className="modal-loader">
                <Loader />
              </Box>
            ) : (
              <Box className="tips-details">
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Key Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.keyComment}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Bet Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.betComment
                      ? tipsModalDetails?.tips?.betComment
                      : "-"}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.betPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">Bet Race :</Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.betRace
                      ? "R" +
                        tipsModalDetails?.tips?.betRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.betRace?.raceName
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.betParticipant
                      ? tipsModalDetails?.tips?.betParticipant?.runnerNumber +
                        "." +
                        tipsModalDetails?.tips?.betParticipant?.animal?.name +
                        " (" +
                        tipsModalDetails?.tips?.betParticipant?.barrierNumber +
                        ")"
                      : "-"}
                    {/* {tipsModalDetails?.tips?.betParticipant?.runnerNumber}
                  {"."} {tipsModalDetails?.tips?.betParticipant?.animal?.name}{" "}
                  ({tipsModalDetails?.tips?.betParticipant?.barrierNumber}) */}
                  </Typography>
                </Box>

                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Each Way Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.wayComment
                      ? tipsModalDetails?.tips?.wayComment
                      : "-"}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Each Way Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.wayPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Each Way Race :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.wayRace
                      ? "R" +
                        tipsModalDetails?.tips?.wayRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.wayRace?.raceName
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Each Way Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {/* {tipsModalDetails?.tips?.wayParticipant?.runnerNumber}
                  {"."} {tipsModalDetails?.tips?.wayParticipant?.animal?.name}{" "}
                  ({tipsModalDetails?.tips?.wayParticipant?.barrierNumber}) */}

                    {tipsModalDetails?.tips?.wayParticipant
                      ? tipsModalDetails?.tips?.wayParticipant?.runnerNumber +
                        "." +
                        tipsModalDetails?.tips?.wayParticipant?.animal?.name +
                        " (" +
                        tipsModalDetails?.tips?.wayParticipant?.barrierNumber +
                        ")"
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Lay Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.layComment
                      ? tipsModalDetails?.tips?.layComment
                      : "-"}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Lay Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.layPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Lay Race :
                  </Typography>
                  <Typography className="details-para">
                    {/* {"R" + tipsModalDetails?.tips?.layRace?.raceNumber}{" "}
                  {tipsModalDetails?.tips?.layRace?.raceName} */}
                    {tipsModalDetails?.tips?.layRace
                      ? "R" +
                        tipsModalDetails?.tips?.layRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.layRace?.raceName
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Lay Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {/* {tipsModalDetails?.tips?.layParticipant?.runnerNumber}
                  {"."} {tipsModalDetails?.tips?.layParticipant?.animal?.name}{" "}
                  ({tipsModalDetails?.tips?.layParticipant?.barrierNumber}) */}
                    {tipsModalDetails?.tips?.layParticipant
                      ? tipsModalDetails?.tips?.layParticipant?.runnerNumber +
                        "." +
                        tipsModalDetails?.tips?.layParticipant?.animal?.name +
                        " (" +
                        tipsModalDetails?.tips?.layParticipant?.barrierNumber +
                        ")"
                      : "-"}
                  </Typography>
                </Box>

                {tipsModalDetails?.races?.map((item, index) => {
                  return (
                    <>
                      <Box
                        className="d-flex align-item-baseline col-35 mb-18 details"
                        key={index}
                      >
                        <Typography className="detsils-header">
                          {"Race " + item?.raceNumber}:
                        </Typography>
                        <Box className="w-60">
                          {item?.runners?.map((obj) => (
                            <Typography className="details-para">
                              {obj?.runnerNumber}
                              {"."} {obj?.animal?.name} ({obj?.barrierNumber})
                            </Typography>
                          ))}
                        </Box>
                      </Box>
                    </>
                  );
                })}
              </Box>
            )}
            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    onClick={this.toggleTipsDetailsModalOpen}
                    // className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
      </>
    );
  }
}
export default FeaturedCalender;
