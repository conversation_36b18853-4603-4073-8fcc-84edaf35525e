import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import moment from "moment-timezone";

import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import Horse from "../../images/dark_horse.svg";
import Greys from "../../images/dar-greyhound.svg";
import Harnes from "../../images/dark_harness.svg";
import { ReactComponent as LeftArrow } from "../../images/left-arrow.svg";
import { ReactComponent as RightArrow } from "../../images/right-arrow.svg";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import "../TeamSport/teamsport.scss";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
class FeatureRace extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      eventValues: {
        eventName: "",
        awayTeamId: "",
        homeTeamId: "",
        startTime: "",
        endTime: "",
        rapidEventId: "",
        id: "",
        modalTrackId: null,
      },
      EventList: [],
      EventCount: 0,
      TrackData: [],
      TrackCount: 0,
      TrackPage: 0,
      errorSport: "",
      errorTrack: "",
      stepperCount: 1,
      externalTeamData: [],
      externalTeamCount: 0,
      selectTeam: "",
      SelectedExternalTeamList: [],
      ExternalTeamPage: 0,
      externalTrackData: [],
      externalTrackCount: 0,
      selectTrack: "",
      SelectedExternalTrackList: [],
      ExternalTrackPage: 0,
      filterDate: null,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      searchTeam: [],
      searchTeamCount: 0,
      searchTeamPage: 0,
      isTeamSearch: "",
      isExternalTrackSearch: "",
      searchExternalTrackCount: 0,
      searchExternalTrackPage: 0,
      searchExternalTrack: [],
      searchTrack: [],
      searchTrackCount: 0,
      searchTrackPage: 0,
      isTrackSearch: "",
      AwayTeamData: [],
      AwayTeamCount: 0,
      AwayTeamPage: 0,
      searchAwayTeam: [],
      searchAwayTeamCount: 0,
      searchAwayTeamPage: 0,
      isAwayTeamSearch: "",
      HomeTeamData: [],
      HomeTeamCount: 0,
      HomeTeamPage: 0,
      searchHomeTeam: [],
      searchHomeTeamCount: 0,
      searchHomeTeamPage: 0,
      isHomeTeamSearch: "",
      isTrackLoading: false,
      isAwayTeamLoading: false,
      isHomeTeamLoading: false,
      search: "",
      isIdentifierModal: false,
      isIdentifierEventId: "",
      isIdentifierEventData: "",
      isVariationModalOpen: false,
      SelectedSport: null,
      selectedModalSport: null,
      AllSportsOption: [],
      modalSportsOption: [],
      selectedSportObj: null,
      selectedModalSportObj: "",
      draggedItem: null,
      isSortChange: false,
    };
  }
  componentDidMount() {
    this.fetchAllEvent(this.state.SelectedSport, this.state?.selectTrack);
    // this.fetchAllTeam(this.state.ExternalTeamPage);
    // this.fetchAllExternalTrack(this.state.ExternalTrackPage);
    this.fetchAllSports();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllEvent(this.state.SelectedSport, this.state?.selectTrack);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEvent(this.state.SelectedSport, this.state?.selectTrack);
      // this.fetchAllTeam(0);
      // this.fetchAllExternalTrack(0);
      this.setState({
        offset: 0,
        currentPage: 1,
        ExternalTeamPage: 0,
        externalTeamData: [],
        ExternalTeamPage: 0,
        selectTeam: "",
        SelectedExternalTeamList: [],
        ExternalTrackPage: 0,
        externalTrackData: [],
        selectTrack: "",
        SelectedExternalTrackList: [],
        searchExternalTrack: [],
        filterDate: null,
        searchTeam: [],
        searchTeamPage: 0,
        searchTrack: [],
        searchTrackPage: 0,
        TrackData: [],
        AwayTeamData: [],
        HomeTeamData: [],
        search: "",
      });
    }
  }

  fetchAllEvent = async (sportId, TrackId) => {
    let { rowPerPage, offset, filterDate, timezone } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = `/racingFeatured/racingFeaturedAdmin?sportId=${
        sportId ? sportId : ""
      }&trackId=${TrackId ? TrackId : ""}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          EventList: data?.result,
          isLoading: false,
          EventCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  handalValidate = () => {
    let { selectedModalSport, eventValues } = this.state;
    let flag = true;
    if (!selectedModalSport) {
      flag = false;
      this.setState({
        errorSport: "This field is mandatory",
      });
    } else {
      this.setState({
        errorSport: "",
      });
    }
    if (!eventValues?.modalTrackId) {
      flag = false;
      this.setState({
        errorTrack: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTrack: "",
      });
    }
    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      const { eventValues, selectedModalSport } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        sportId: selectedModalSport,
        trackId: eventValues?.modalTrackId,
      };
      try {
        const { status } = await axiosInstance.post(
          `racingFeatured/racingfeatured`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEvent(this.state.SelectedSport, this.state?.selectTrack);
          this.setActionMessage(
            true,
            "Success",
            `Feature Race Created Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
        }
      } catch {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
        });
      }
    }
  };

  async fetchAllTrack(TrackPage, sportId) {
    const passApi = `track?limit=20&offset=${TrackPage}&matchString=&sportId=${sportId}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let Track = data?.result?.rows?.map((item) => {
        newdata.push({
          label:
            item?.name +
            (item?.Country?.countryCode
              ? " (" + item?.Country?.countryCode + ")"
              : ""),
          // +
          // " " +
          // "(" +
          // (item?.sportId === 1
          //   ? "Horse"
          //   : item?.sportId === 3
          //   ? "Greyhound"
          //   : item?.sportId === 2
          //   ? "Harness"
          //   : "") +
          // ")",
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.TrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      finalData = finalData?.filter((item) => item?.value !== 0);
      this.setState({
        TrackData: finalData,
        TrackCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomTrack = (e, type) => {
    let {
      TrackCount,
      TrackPage,
      isTrackSearch,
      searchTrackCount,
      searchTrackPage,
    } = this.state;
    if (
      isTrackSearch !== "" &&
      searchTrackCount !== Math.ceil(searchTrackPage / 20 + 1)
    ) {
      this.handleTrackInputChange(
        searchTrackPage + 20,
        isTrackSearch,
        this.state.selectedModalSportObj
      );
      this.setState({
        searchTrackPage: searchTrackPage + 20,
      });
    } else {
      if (
        TrackCount !== (TrackCount == 1 ? 1 : Math.ceil(TrackPage / 20)) &&
        isTrackSearch == ""
      ) {
        this.fetchAllTrack(TrackPage + 20, this.state.selectedModalSportObj);
        this.setState({
          TrackPage: TrackPage + 20,
        });
      }
    }
  };
  handleTrackInputChange = (TrackPage, value, sportId) => {
    const passApi = `track?limit=20&offset=${TrackPage}&matchString=${value}&sportId=${sportId}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label:
              item?.name +
              (item?.Country?.countryCode
                ? " (" + item?.Country?.countryCode + ")"
                : ""),
            //   +
            //   " " +
            //   "(" +
            //   (item?.sportId === 1
            //     ? "Horse"
            //     : item?.sportId === 3
            //     ? "Greyhound"
            //     : item?.sportId === 2
            //     ? "Harness"
            //     : "") +
            //   ")",
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTrack: finalData,
          searchTrackCount: Math.ceil(count),
          isTrackSearch: value,
          searchTrackPage: TrackPage,
        });
      }
    });
  };

  //   fetchAllTeam = async (Track) => {
  //     const passApi = `sportFeatured/getTeam?sportId=${this.state.SelectedSport}&TrackId=${Track}`;
  //     const { status, data } = await axiosInstance.get(passApi);
  //     if (status === 200) {
  //       let newdata = [];
  //       let categories = data?.result?.map((item) => {
  //         newdata.push({
  //           label: item?.name,
  //           value: item?.id,
  //         });
  //       });
  //       let filterdata = newdata?.filter((item) => item?.value !== 0);
  //       let mergeData = _.unionBy(this.state?.externalTeamData, filterdata);
  //       const sortedData = mergeData?.sort((a, b) => {
  //         return a?.label.localeCompare(b?.label);
  //       });
  //       let alldatas = sortedData?.unshift({
  //         label: "All Teams",
  //         value: 0,
  //       });
  //       let finalData = _.uniqBy(sortedData, function (e) {
  //         return e.value;
  //       });

  //       this.setState({
  //         externalTeamData: finalData,
  //       });
  //     }
  //   };
  //   handleOnScrollBottomExternalTeam = (e, type) => {
  //     let {
  //       externalTeamCount,
  //       ExternalTeamPage,
  //       isTeamSearch,
  //       searchTeamCount,
  //       searchTeamPage,
  //     } = this.state;
  //     if (
  //       isTeamSearch !== "" &&
  //       searchTeamCount !== Math.ceil(searchTeamPage / 20 + 1)
  //     ) {
  //       this.handleTeamInputChange(searchTeamPage + 20, isTeamSearch);
  //       this.setState({
  //         searchTeamPage: searchTeamPage + 20,
  //       });
  //     } else {
  //       if (
  //         externalTeamCount !== Math.ceil(ExternalTeamPage / 20) &&
  //         isTeamSearch == ""
  //       ) {
  //         this.fetchAllTeam(this.state.selectTrack);
  //         this.setState({
  //           ExternalTeamPage: ExternalTeamPage + 20,
  //         });
  //       }
  //     }
  //   };
  //   handleTeamInputChange = (ExternalTeamPage, value) => {
  //     const passApi = this.props.match.path?.includes("cricket")
  //       ? `crickets/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("rugbyleague")
  //       ? `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=12`
  //       : this.props.match.path?.includes("rugbyunion")
  //       ? `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=13`
  //       : this.props.match.path?.includes("basketball")
  //       ? `nba/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("afl")
  //       ? `afl/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("australianrules")
  //       ? `ar/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("golf")
  //       ? `golf/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("tennis")
  //       ? `tennis/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("baseball")
  //       ? `baseball/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("icehockey")
  //       ? `icehockey/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("boxing")
  //       ? `boxing/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("mma")
  //       ? `mma/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : this.props.match.path?.includes("soccer")
  //       ? `soccer/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
  //       : `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=14`;
  //     axiosInstance.get(passApi).then((data) => {
  //       if (data?.status === 200) {
  //         let count = data?.data?.result?.count / 20;
  //         let newdata = [];
  //         let categories = data?.data?.result?.rows?.map((item) => {
  //           newdata.push({
  //             label: item?.name,
  //             value: item?.id,
  //           });
  //         });
  //         let filterdata = newdata?.filter((item) => item?.value !== 0);
  //         let mergeData = _.unionBy(this.state?.searchTeam, filterdata);
  //         const sortedData = mergeData?.sort((a, b) => {
  //           return a?.label.localeCompare(b?.label);
  //         });
  //         let alldatas = sortedData?.unshift({
  //           label: "All Teams",
  //           value: 0,
  //         });
  //         let finalData = _.uniqBy(sortedData, function (e) {
  //           return e.value;
  //         });
  //         this.setState({
  //           searchTeam: finalData,
  //           searchTeamCount: Math.ceil(count),
  //           isTeamSearch: value,
  //         });
  //       }
  //     });
  //   };
  //   handleExternalTeamChange = (e) => {
  //     this.setState({
  //       selectTeam: e.value,
  //       currentPage: 1,
  //     });
  //     this.fetchAllEvent(
  //       this.state.SelectedSport,
  //       this.state?.selectTrack,
  //       e?.value
  //     );
  //   };
  fetchAllExternalTrack = async (TrackPage, sportId) => {
    const passApi = `track?limit=20&offset=${TrackPage}&matchString=&sportId=${sportId}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label:
            item?.name +
            (item?.Country?.countryCode
              ? " (" + item?.Country?.countryCode + ")"
              : ""),
          // +
          // " " +
          // "(" +
          // (item?.sportId === 1
          //   ? "Horse"
          //   : item?.sportId === 3
          //   ? "Greyhound"
          //   : item?.sportId === 2
          //   ? "Harness"
          //   : "") +
          // ")",

          value: item?.id,
        });
      });
      let filterdata = newdata?.filter((item) => item?.value !== 0);
      let mergeData = _.unionBy(this.state?.externalTrackData, filterdata);
      const sortedData = mergeData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let alldatas = mergeData?.unshift({
        label: "All Tracks",
        value: 0,
      });
      let finalData = _.uniqBy(mergeData, function (e) {
        return e.value;
      });

      this.setState({
        externalTrackData: finalData,
        externalTrackCount: Math.ceil(count),
        ExternalTrackPage: TrackPage,
      });
    }
  };
  handleOnScrollBottomExternalTrack = (e, type) => {
    let {
      externalTrackCount,
      ExternalTrackPage,
      isExternalTrackSearch,
      searchExternalTrackCount,
      searchExternalTrackPage,
    } = this.state;
    if (
      isExternalTrackSearch !== "" &&
      searchExternalTrackCount !== Math.ceil(searchExternalTrackPage / 20 + 1)
    ) {
      this.handleExternalTrackInputChange(
        searchExternalTrackPage + 20,
        isExternalTrackSearch,
        this.state.selectedSportObj
      );
      this.setState({
        searchExternalTrackPage: searchExternalTrackPage + 20,
      });
    } else {
      if (
        externalTrackCount !== Math.ceil(ExternalTrackPage / 20) &&
        isExternalTrackSearch == ""
      ) {
        this.fetchAllExternalTrack(
          ExternalTrackPage + 20,
          this.state.selectedSportObj
        );
        this.setState({
          ExternalTrackPage: ExternalTrackPage + 20,
        });
      }
    }
  };
  handleExternalTrackInputChange = (ExternalTrackPage, value, sportId) => {
    const passApi = `track?limit=20&offset=${ExternalTrackPage}&matchString=${value}&sportId=${sportId}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label:
              item?.name +
              (item?.Country?.countryCode
                ? " (" + item?.Country?.countryCode + ")"
                : ""),
            //   +
            //   " " +
            //   "(" +
            //   (item?.sportId === 1
            //     ? "Horse"
            //     : item?.sportId === 3
            //     ? "Greyhound"
            //     : item?.sportId === 2
            //     ? "Harness"
            //     : "") +
            //   ")",
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchExternalTrack, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = mergeData?.unshift({
          label: "All Tracks",
          value: 0,
        });
        let finalData = _.uniqBy(mergeData, function (e) {
          return e.value;
        });
        this.setState({
          searchExternalTrack: finalData,
          searchExternalTrackCount: Math.ceil(count),
          isExternalTrackSearch: value,
          searchExternalTrackPage: ExternalTrackPage,
        });
      }
    });
  };
  handleExternalTrackChange = (e) => {
    this.setState({
      selectTrack: e.value,
      currentPage: 1,
      externalTeamData: [],
      selectTeam: null,
    });
    this.fetchAllEvent(this.state.SelectedSport, e?.value);
    // this.fetchAllTeam(e.value);
  };
  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.sports + `?sportTypeId=1`
    );
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Races",
        value: 0,
      };

      let alldata = [alldatas, ...newdata];

      // let pushData = newdata.push(alldatas);
      // let sortData = newdata?.sort((a, b) => {
      //   return a.value > b.value ? 1 : -1;
      // });
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        modalSportsOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        // isLoading: false,
      });
    }
  }
  handlesportchange = (e) => {
    this.setState({
      SelectedSport: e.value,
      externalTrackData: [],
      externalTrackCount: 0,
      ExternalTrackPage: 0,
      isExternalTrackSearch: "",
      searchExternalTrackCount: 0,
      searchExternalTrackPage: 0,
      searchExternalTrack: [],
      selectTrack: null,
      externalTeamData: [],
      selectTeam: null,
    });

    this.setState({
      selectedSportObj: e?.value,
    });
    this.fetchAllEvent(e?.value, this.state.selectTrack);
    this.fetchAllExternalTrack(0, e?.value);
  };
  handleModalsportchange = (e) => {
    this.setState({
      selectedModalSport: e.value,
      TrackData: [],
      TrackCount: 0,
      TrackPage: 0,
      searchTrack: [],
      searchTrackCount: 0,
      searchTrackPage: 0,
      isTrackSearch: "",
      HomeTeamData: [],
      eventValues: {
        ...this.state.eventValues,
        modalTrackId: null,
        homeTeamId: null,
      },
    });
    this.setState({
      selectedModalSportObj: e.value,
    });
    this.fetchAllTrack(0, e.value);
  };

  //   async fetchAllHomeTeam(Track) {
  //     const passApi = `sportFeatured/getTeam?sportId=${this.state.selectedModalSport}&TrackId=${Track}`;
  //     const { status, data } = await axiosInstance.get(passApi);
  //     if (status === 200) {
  //       let newdata = [];
  //       let Track = data?.result?.map((item) => {
  //         newdata.push({
  //           label: item?.name,
  //           value: item?.id,
  //         });
  //       });
  //       let filterData = _.unionBy(this.state?.HomeTeamData, newdata);
  //       const sortedData = filterData?.sort((a, b) => {
  //         return a?.label.localeCompare(b?.label);
  //       });
  //       let finalData = _.uniqBy(sortedData, function (e) {
  //         return e.value;
  //       });
  //       finalData = finalData?.filter((item) => item?.value !== 0);
  //       this.setState({
  //         HomeTeamData: finalData,
  //       });
  //     }
  //   }

  //   handleOnScrollBottomHomeTeam = (e, type) => {
  //     let {
  //       HomeTeamCount,
  //       HomeTeamPage,
  //       isHomeTeamSearch,
  //       searchHomeTeamCount,
  //       searchHomeTeamPage,
  //       eventValues,
  //     } = this.state;
  //     if (
  //       isHomeTeamSearch !== "" &&
  //       searchHomeTeamCount !== Math.ceil(searchHomeTeamPage / 20 + 1)
  //     ) {
  //       this.handleHomeTeamInputChange(searchHomeTeamPage + 20, isHomeTeamSearch);
  //       this.setState({
  //         searchHomeTeamPage: searchHomeTeamPage + 20,
  //       });
  //     } else {
  //       if (
  //         HomeTeamCount !==
  //           (HomeTeamCount == 1 ? 1 : Math.ceil(HomeTeamPage / 20)) &&
  //         isHomeTeamSearch == ""
  //       ) {
  //         this.fetchAllHomeTeam(eventValues?.modalTrackId);
  //         this.setState({
  //           HomeTeamPage: HomeTeamPage + 20,
  //         });
  //       }
  //     }
  //   };
  //   handleHomeTeamInputChange = (Page, value) => {
  //     const passApi = this.props.match.path?.includes("cricket")
  //       ? `crickets/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("rugbyleague")
  //       ? `rls/team?limit=20&offset=${Page}&search=${value}&SportId=12`
  //       : this.props.match.path?.includes("rugbyunion")
  //       ? `rls/team?limit=20&offset=${Page}&search=${value}&SportId=13`
  //       : this.props.match.path?.includes("basketball")
  //       ? `nba/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("afl")
  //       ? `afl/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("australianrules")
  //       ? `ar/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("golf")
  //       ? `golf/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("tennis")
  //       ? `tennis/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("baseball")
  //       ? `baseball/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("icehockey")
  //       ? `icehockey/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("boxing")
  //       ? `boxing/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("mma")
  //       ? `mma/team?limit=20&offset=${Page}&search=${value}`
  //       : this.props.match.path?.includes("soccer")
  //       ? `soccer/team?limit=20&offset=${Page}&search=${value}`
  //       : `rls/team?limit=20&offset=${Page}&search=${value}&SportId=14`;
  //     axiosInstance.get(passApi).then((data) => {
  //       if (data?.status === 200) {
  //         let count = data?.data?.result?.count / 20;
  //         let newdata = [];
  //         let categories = data?.data?.result?.rows?.map((item) => {
  //           newdata.push({
  //             label: item?.name,
  //             value: item?.id,
  //           });
  //         });
  //         let filterData = _.unionBy(this.state?.searchHomeTeam, newdata);
  //         const sortedData = filterData?.sort((a, b) => {
  //           return a?.label.localeCompare(b?.label);
  //         });
  //         let finalData = _.uniqBy(sortedData, function (e) {
  //           return e.value;
  //         });
  //         this.setState({
  //           searchHomeTeam: finalData,
  //           searchHomeTeamCount: Math.ceil(count),
  //           isHomeTeamSearch: value,
  //         });
  //       }
  //     });
  //   };
  //   fetchSelectedHomeTeam = async (id, HomeTeamId) => {
  //     this.setState({
  //       isHomeTeamLoading: true,
  //     });
  //     let passApi = this.props.match.path?.includes("cricket")
  //       ? "crickets"
  //       : this.props.match.path?.includes("basketball")
  //       ? "nba"
  //       : this.props.match.path?.includes("afl")
  //       ? "afl"
  //       : this.props.match.path?.includes("australianrules")
  //       ? "ar"
  //       : this.props.match.path?.includes("golf")
  //       ? "golf"
  //       : this.props.match.path?.includes("tennis")
  //       ? "tennis"
  //       : this.props.match.path?.includes("baseball")
  //       ? "baseball"
  //       : this.props.match.path?.includes("icehockey")
  //       ? "icehockey"
  //       : this.props.match.path?.includes("boxing")
  //       ? "boxing"
  //       : this.props.match.path?.includes("mma")
  //       ? "mma"
  //       : this.props.match.path?.includes("soccer")
  //       ? "soccer"
  //       : "rls";
  //     try {
  //       const { status, data } = await axiosInstance.get(
  //         `${passApi}/team/${HomeTeamId}`
  //       );
  //       if (status === 200) {
  //         let seletedHomeTeam = [
  //           {
  //             label: data?.result?.name,
  //             value: HomeTeamId,
  //           },
  //         ];
  //         this.setState({
  //           isHomeTeamLoading: false,
  //           HomeTeamData: HomeTeamId
  //             ? _.uniqBy(
  //                 [...seletedHomeTeam, ...this.state.HomeTeamData],
  //                 function (e) {
  //                   return e.value;
  //                 }
  //               )
  //             : this.state.HomeTeamData,
  //         });
  //       } else {
  //         this.setState({
  //           isHomeTeamLoading: false,
  //         });
  //       }
  //     } catch (err) {
  //       this.setState({
  //         isHomeTeamLoading: false,
  //       });
  //     }
  //   };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorSport: "",
      errorTrack: "",
      TrackPage: 0,
      HomeTeamData: [],
    });
  };
  inputModal = (item, type) => () => {
    const { eventValues } = this.state;
    // this.fetchAllTrack(0);
    // this.fetchAllAwayTeam(0);
    // this.fetchAllHomeTeam(0);
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
    } else {
      this.setState({
        eventValues: {
          homeTeamId: "",
          id: "",
          modalTrackId: null,
        },
        isEditMode: false,
        selectedModalSport: null,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    try {
      const passApi = `racingFeatured/racingfeatured/${this.state.itemToDelete}`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        const { SelectedSport, selectTrack, selectTeam } = this.state;
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvent(SelectedSport, selectTrack);
        });
        this.setActionMessage(
          true,
          "Success",
          " Feature Race Deleted Successfully!"
        );
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  // handleDragStart = (item) => {
  //   this.setState({ draggedItem: item });
  // };

  // // Function to handle the drop and reorder the items
  // handleDrop = (targetIndex) => {
  //   const { EventList, draggedItem } = this.state;

  //   if (draggedItem) {
  //     const updatedItems = [...EventList];
  //     const draggedIndex = EventList.indexOf(draggedItem);

  //     // Remove the dragged item from the original position
  //     updatedItems.splice(draggedIndex, 1);

  //     // Insert the dragged item at the new position
  //     updatedItems.splice(targetIndex, 0, draggedItem);

  //     this.setState({
  //       EventList: updatedItems,
  //       draggedItem: null,
  //       isSortChange: true,
  //     });
  //   }
  // };

  handleDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    const updatedList = Array.from(this.state?.EventList);
    const [draggedItem] = updatedList.splice(result.source.index, 1);
    updatedList.splice(result.destination.index, 0, draggedItem);
    this.setState({ EventList: updatedList, isSortChange: true });
  };

  handleOrderChange = async () => {
    const { EventList, SelectedSport, selectTeam, selectTrack } = this.state;

    let newdata = [];
    let categories = EventList?.map((item) => {
      newdata.push(item?.id);
    });
    try {
      const { status, data } = await axiosInstance.put(
        `racingFeatured/updateSortOrder`,
        newdata
      );
      if (status === 200) {
        this.setState({ isSortChange: false });
        this.fetchAllEvent(SelectedSport, selectTrack);
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isSortChange: false });
      }
    } catch (err) {
      this.setState({ isSortChange: false });
    }
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      eventValues,
      TrackData,
      TrackCount,
      TrackPage,
      EventList,
      EventCount,
      errorSport,
      errorTrack,
      stepperCount,
      externalTeamData,
      externalTeamCount,
      selectTeam,
      SelectedExternalTeamList,
      ExternalTeamPage,
      isExternalTrackSearch,
      searchExternalTrack,
      externalTrackData,
      selectTrack,
      filterDate,
      searchTeam,
      isTeamSearch,
      searchTrack,
      isTrackSearch,
      AwayTeamData,
      AwayTeamCount,
      AwayTeamPage,
      searchAwayTeam,
      searchAwayTeamCount,
      searchAwayTeamPage,
      isAwayTeamSearch,
      HomeTeamData,
      HomeTeamCount,
      HomeTeamPage,
      searchHomeTeam,
      searchHomeTeamCount,
      searchHomeTeamPage,
      isHomeTeamSearch,
      isTrackLoading,
      isAwayTeamLoading,
      isHomeTeamLoading,
      selectTrack,
      search,
      isIdentifierModal,
      isIdentifierEventId,
      isIdentifierEventData,
      isVariationModalOpen,
      AllSportsOption,
      modalSportsOption,
      SelectedSport,
      selectedModalSport,
    } = this.state;
    const pageNumbers = [];
    if (EventCount > 0) {
      for (let i = 1; i <= Math.ceil(EventCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p"> Featured Race </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Featured Race
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Race"
                  value={AllSportsOption?.find((item) => {
                    return item?.value == SelectedSport;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handlesportchange(e)}
                  options={AllSportsOption}
                />
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="All Tracks"
                  isDisabled={!SelectedSport}
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomExternalTrack(e)
                  }
                  onInputChange={(e) =>
                    this.handleExternalTrackInputChange(
                      0,
                      e,
                      this.state.selectedSportObj
                    )
                  }
                  value={
                    selectTrack
                      ? isExternalTrackSearch
                        ? searchExternalTrack?.find((item) => {
                            return item?.value == selectTrack;
                          })
                        : externalTrackData?.find((item) => {
                            return item?.value == selectTrack;
                          })
                      : null
                  }
                  onChange={(e) => this.handleExternalTrackChange(e)}
                  menuPosition="absolute"
                  options={
                    isExternalTrackSearch
                      ? searchExternalTrack
                      : externalTrackData
                  }
                />
                {/* <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="All Teams"
                  // onMenuScrollToBottom={(e) =>
                  //   this.handleOnScrollBottomExternalTeam(e)
                  // }
                  // onInputChange={(e) => this.handleTeamInputChange(0, e)}
                  isDisabled={!selectTrack}
                  value={
                    selectTeam
                      ? isTeamSearch
                        ? searchTeam?.find((item) => {
                            return item?.value == selectTeam;
                          })
                        : externalTeamData?.find((item) => {
                            return item?.value == selectTeam;
                          })
                      : null
                  }
                  onChange={(e) => this.handleExternalTeamChange(e)}
                  menuPosition="absolute"
                  options={isTeamSearch ? searchTeam : externalTeamData}
                /> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap"
                style={{ marginBottom: "10px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: this.state.isSortChange
                      ? "#4455c7"
                      : "rgba(0, 0, 0, 0.12)",
                    color: this.state.isSortChange
                      ? "#fff"
                      : "rgba(0, 0, 0, 0.26)",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  disabled={!this.state.isSortChange}
                  onClick={() => {
                    this.handleOrderChange();
                  }}
                >
                  Save Order
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && !EventList && <p>No Data Available</p>}

            {!isLoading && EventList?.length > 0 && (
              <>
                <DragDropContext onDragEnd={this.handleDragEnd}>
                  <TableContainer component={Paper}>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow>
                          <TableCell>DID</TableCell>
                          {/* <TableCell>rapidEventId</TableCell> */}
                          <TableCell> Sport </TableCell>

                          <TableCell> Track </TableCell>
                          <TableCell> Sort Order </TableCell>
                          <TableCell style={{ width: "10%" }}>
                            Created At
                          </TableCell>

                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <Droppable droppableId="your-droppable-id">
                        {(provided, snapshot) => (
                          <TableBody
                            className="table_body"
                            ref={provided.innerRef}
                          >
                            <TableRow className="table_row">
                              <TableCell
                                colSpan={100}
                                className="table-seprator"
                              ></TableCell>
                            </TableRow>
                            {EventList?.map((item, index) => {
                              return (
                                <Draggable
                                  key={item?.id}
                                  draggableId={`draggable-${item?.id}`}
                                  index={index}
                                >
                                  {(provided, snapshot) => (
                                    <TableRow
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className="table-rows listTable-Row"
                                      style={{
                                        cursor: "all-scroll",
                                        ...provided.draggableProps.style,
                                      }}
                                      key={item?.id}
                                    >
                                      <TableCell> {item?.id} </TableCell>
                                      {/* <TableCell>{item?.rapidEventId}</TableCell> */}
                                      <TableCell>
                                        {item?.Sport?.sportName}
                                      </TableCell>

                                      <TableCell>{item?.Track?.name}</TableCell>

                                      <TableCell>{item?.sortOrder}</TableCell>

                                      <TableCell
                                        style={{
                                          textAlign: !item?.createdAt
                                            ? "center"
                                            : "",
                                        }}
                                      >
                                        {item?.createdAt
                                          ? moment(item?.createdAt).format(
                                              "DD/MM/YYYY hh:mm:ss a"
                                            )
                                          : "-"}
                                      </TableCell>

                                      <TableCell
                                        style={{
                                          display: "flex",
                                          borderBottom: "none",
                                        }}
                                      >
                                        <Button
                                          onClick={this.setItemToDelete(
                                            item?.id
                                          )}
                                          className="table-btn delete-btn"
                                        >
                                          Delete
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  )}
                                </Draggable>
                              );
                            })}
                            {/* <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination"> */}
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            {/* <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                EventCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            /> */}
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                            {/* </div>
                        </TableCell>
                      </TableRow> */}
                          </TableBody>
                        )}
                      </Droppable>
                    </Table>
                  </TableContainer>
                </DragDropContext>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />
            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Featured Race"
                    : "Edit Featured Race"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        className="teamsport-text"
                        style={{ marginBottom: "15px" }}
                      >
                        <label className="modal-label"> Race </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          placeholder="Select Race"
                          value={modalSportsOption?.find((item) => {
                            return item?.value == selectedModalSport;
                          })}
                          //   isLoading={isLoading}
                          onChange={(e) => this.handleModalsportchange(e)}
                          options={modalSportsOption}
                        />
                        {errorSport ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorSport}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid item xs={12} style={{ marginBottom: "15px" }}>
                        <label className="modal-label"> Track </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Track"
                          isDisabled={!selectedModalSport}
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomTrack(e)
                          }
                          onInputChange={(e) =>
                            this.handleTrackInputChange(
                              0,
                              e,
                              this.state.selectedModalSportObj
                            )
                          }
                          isLoading={isTrackLoading}
                          value={
                            eventValues?.modalTrackId
                              ? isTrackSearch
                                ? searchTrack?.find((item) => {
                                    return (
                                      item?.value == eventValues?.modalTrackId
                                    );
                                  })
                                : TrackData?.find((item) => {
                                    return (
                                      item?.value == eventValues?.modalTrackId
                                    );
                                  })
                              : null
                          }
                          options={isTrackSearch ? searchTrack : TrackData}
                          onChange={(e) => {
                            this.setState({
                              eventValues: {
                                ...eventValues,
                                modalTrackId: e?.value,
                                // homeTeamId: null,
                              },
                              //   HomeTeamData: [],
                            });
                            // this.fetchAllHomeTeam(e?.value);
                          }}
                        />
                        {errorTrack ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorTrack}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* 
                      <Grid
                        item
                        xs={12}
                        className="teamsport-text"
                        style={{ marginBottom: "15px" }}
                      >
                        <label className="modal-label"> Team </label>
                        <Select
                          className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Team"
                          isDisabled={!eventValues?.modalTrackId}
                          // onMenuScrollToBottom={(e) =>
                          //   this.handleOnScrollBottomHomeTeam(e)
                          // }
                          // onInputChange={(e) =>
                          //   this.handleHomeTeamInputChange(0, e)
                          // }
                          isLoading={isHomeTeamLoading}
                          value={
                            eventValues?.homeTeamId
                              ? isHomeTeamSearch
                                ? searchHomeTeam?.find((item) => {
                                    return (
                                      item?.value == eventValues?.homeTeamId
                                    );
                                  })
                                : HomeTeamData?.find((item) => {
                                    return (
                                      item?.value == eventValues?.homeTeamId
                                    );
                                  })
                              : null
                          }
                          options={
                            isHomeTeamSearch ? searchHomeTeam : HomeTeamData
                          }
                          onChange={(e) =>
                            this.setState({
                              eventValues: {
                                ...eventValues,
                                homeTeamId: e?.value,
                              },
                            })
                          }
                        />
                      </Grid> */}
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default FeatureRace;
