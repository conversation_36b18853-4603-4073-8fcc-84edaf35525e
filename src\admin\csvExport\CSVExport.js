import { Button } from "@mui/material";
import React from "react";

const CSVExport = ({ data, filename }) => {
  const downloadCSV = () => {
    const csvData = convertToCSV(data);
    const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename || "data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const convertToCSV = (data) => {
    const header = Object.keys(data[0]).join(",");
    const rows = data.map((row) => Object.values(row).join(","));
    return `${header}\n${rows.join("\n")}`;
  };

  return (
    <Button
      variant="contained"
      style={{
        backgroundColor: "#4455C7",
        color: "#fff",
        borderRadius: "8px",
        textTransform: "capitalize",
        padding: "6px 10px",
      }}
      onClick={downloadCSV}
    >
      Export CSV
    </Button>
  );
};

export default CSVExport;
