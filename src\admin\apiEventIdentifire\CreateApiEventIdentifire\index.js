import React, { createRef } from "react";
import { Grid } from "@mui/material";
import {
  apiEventIdentifireFormModel,
  eventIdentifireForMeetings,
} from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
import { setValidation } from "../../../helpers/common";

let apiEventIdentifireFormModelArray = [];

class CreateApiEventIdentifire extends React.Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      values: {
        eventId: "",
        apiEventId: "",
        providerId: "",
      },
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
    };
  }

  componentDidMount() {
    apiEventIdentifireFormModelArray = this.props.isMeetings
      ? eventIdentifireForMeetings
      : apiEventIdentifireFormModel;
    if (this.props.isEditMode) {
      this.fetchCurrentEventIdentifire(this.props.id);
    }

    const { allEvents, allProvider, isOtherSport, otherSportName } = this.props;
    this.setState((prevState) => {
      return {
        values: {
          ...prevState.values,
          eventId: allEvents?.length > 0 ? allEvents[0].id : "",
          providerId: allProvider?.length > 0 ? allProvider[0].id : "",
        },
      };
    });
    apiEventIdentifireFormModelArray = apiEventIdentifireFormModelArray?.map(
      (fieldItem) => {
        if (fieldItem?.field === "eventId") {
          return {
            ...fieldItem,
            type: "dropdown",
            options: [
              ...allEvents?.map((tablecol, i) => {
                return {
                  id: i,
                  value: tablecol?.id,
                  label: tablecol?.eventName,
                };
              }),
            ],
          };
        } else if (fieldItem?.field === "apiEventId") {
          return {
            ...fieldItem,
            type: "text",
            label: isOtherSport
              ? otherSportName === "Match"
                ? "Match FeedId"
                : otherSportName === "Match"
                ? "Match Feed Id"
                : "Meeting Id"
              : "Fight FeedId",
          };
        } else if (fieldItem?.field === "providerId") {
          return {
            ...fieldItem,
            type: "dropdown",
            options: [
              ...allProvider?.map((tablecol, i) => {
                return {
                  id: i,
                  value: tablecol?.id,
                  label: tablecol?.providerName,
                };
              }),
            ],
          };
        }
        return fieldItem;
      }
    );
  }
  componentWillUnmount() {
    apiEventIdentifireFormModelArray = apiEventIdentifireFormModelArray.map(
      (fieldItem) => {
        return { ...fieldItem, errorMessage: "" };
      }
    );
  }

  fetchCurrentEventIdentifire = async (id) => {
    const { status, data } = await axiosInstance.get(
      URLS.apiEventIdentifire + `/${id}`
    );
    if (status === 200) {
      this.setState({
        values: data.result,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } });
  };

  validate = () => {
    let { apiEventId, providerId } = this.state.values;
    let flag = true;

    if (apiEventId === "" || providerId === "") {
      flag = false;
      // this.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      // this.setActionMessage(false);
    }

    return flag;
  };

  handleSave = async () => {
    const { isEditMode, isMeetings, eventIdToSend, isMeetingsIndentier } =
      this.props;
    this.setState({ isLoading: true });
    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode
        ? `${URLS.apiEventIdentifire}/${this.props.id}`
        : URLS.apiEventIdentifire;

      const values = removeErrorFieldsFromValues(form.formData);
      apiEventIdentifireFormModelArray = apiEventIdentifireFormModelArray?.map(
        (fieldItem) => {
          return setValidation(fieldItem, values);
        }
      );
      if (isMeetings && !isEditMode) {
        values["eventId"] = eventIdToSend;
      }
      if (this.validate()) {
        const { status } = await axiosInstance[method](url, values);
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          if (isMeetingsIndentier) {
            this.props.fetchAllEventIdentifire(eventIdToSend);
          } else {
            this.props.fetchAllEventIdentifire();
          }
          this.setActionMessage(
            true,
            "Success",
            `Api Event Identifier ${
              isEditMode ? "Edited" : "Created"
            } Successfully`
          );
        }
      }
    } catch (err) {
      this.setState({ isLoading: false });
      this.setActionMessage(
        true,
        "Error",
        `An error occurred while ${
          isEditMode ? "editing" : "creating"
        } Api Event Identifire`
      );
    }
  };

  handleChange = (field, value) => {
    let values = { ...this.state.values, [field]: value };
    this.setState({ values: values });
    apiEventIdentifireFormModelArray = apiEventIdentifireFormModelArray?.map(
      (fieldItem) => {
        if (field === fieldItem?.field) {
          return setValidation(fieldItem, values);
        } else {
          return fieldItem;
        }
      }
    );
    this.setActionMessage(false);
  };

  render() {
    var { values, messageBox, isLoading } = this.state;
    var { isEditMode } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            {/* <Paper className="pageWrapper api-provider"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}

            <Form
              values={values}
              model={apiEventIdentifireFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            />

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Back"
                  />
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateApiEventIdentifire;
