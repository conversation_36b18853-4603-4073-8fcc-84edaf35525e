import React from "react";
import {
  Box,
  Breadcrumbs,
  Button,
  Grid,
  TextField,
  Typography,
  IconButton,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import { Link } from "react-router-dom";
import Select, { components } from "react-select";
import "./configurationsEvent.scss";
import ButtonComponent from "../../../../library/common/components/Button";
import fantasyAxiosInstance from "../../../../helpers/Axios/fantasyAxios";
import { ActionMessage } from "../../../../library/common/components";

const statusOption = [
  {
    label: "Free",
    value: "free",
  },
  {
    label: "Paid",
    value: "paid",
  },
];

class ConfigurationsEvent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      eventsType: "paid",
      entryCoins: "",
      minUserEntry: "",
      competitionName: "",
      errorEntryCoins: "",
      // prizePoolFields: [
      //   {
      //     winPlace: 1,
      //     placeType: "FIRST",
      //     coins: "",
      //     description: "",
      //     errorCoins: "",
      //     errorDescription: "",
      //   },
      // ],
      // drawPoolFields: [
      //   // {
      //   //   drawNumber: 1,
      //   //   drawType: "",
      //   //   coins: "",
      //   //   description: "",
      //   //   errorDrawType: " ",
      //   //   errorCoins: "",
      //   //   errorDescription: "",
      //   // },
      // ],
      apiFetched: false, // New flag to track if data is fetched
    };
  }
  componentDidMount() {
    // Simulate fetching data from an API on component mount
    if (this.props?.modalType === "edit") {
      this.fetchEventConfigurations();
    }
  }

  // Simulate API call to fetch data

  async fetchEventConfigurations() {
    let props = this.props;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await fantasyAxiosInstance.get(
        `/events/event-configure/${props?.eventConfigurationId}?eventId=${props?.eventConfigurationList?.id}&sportId=${props?.eventConfigurationList.SportId}`
      );
      if (status === 200) {
        const response = data?.result;
        this.setState({
          isLoading: false,
          eventsType: response ? response?.eventType : this.state.eventsType,
          // entryCoins: response ? response?.entryCoin : this.state.entryCoins,
          competitionName: response
            ? response?.competitionName
            : this.state.competitionName,
          minUserEntry: response
            ? response?.minUserEntry
            : this.state.minUserEntry,
          // prizePoolFields: response
          //   ? response?.prizePool
          //   : this.state.prizePoolFields,
          // drawPoolFields: response
          //   ? response?.drawPool
          //   : this.state.drawPoolFields,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        {/* <SelectIndicator /> */}
      </components.DropdownIndicator>
    );
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  getOrdinalName = (index) => {
    const ordinals = ["FIRST", "SECOND", "THIRD", "FOURTH", "FIFTH"];
    return ordinals[index] || `${index + 1}TH`;
  };

  // Handle input change dynamically
  handleInputChange = (index, field, value) => {
    const prizePoolFields = [...this.state.prizePoolFields];
    prizePoolFields[index][field] = value;
    prizePoolFields[index][
      `error${field.charAt(0).toUpperCase() + field.slice(1)}`
    ] = value ? "" : `This field is required`;
    this.setState({ prizePoolFields });
  };

  handleDrawInputChange = (index, field, value) => {
    const drawPoolFields = [...this.state?.drawPoolFields];
    drawPoolFields[index][field] = value;
    drawPoolFields[index][
      `error${field.charAt(0).toUpperCase() + field.slice(1)}`
    ] = value ? "" : `This field is required`;
    this.setState({ drawPoolFields });
  };

  // Add a new field dynamically
  handleAddField = () => {
    this.setState((prevState) => {
      const newFields = [
        ...prevState?.prizePoolFields,
        {
          winPlace: prevState?.prizePoolFields?.length + 1,
          placeType: this.getOrdinalName(prevState.prizePoolFields?.length),
          coins: "",
          description: "",
          errorCoins: "",
          errorDescription: "",
        },
      ];
      return { prizePoolFields: newFields };
    });
  };

  handleDrawPoolAddField = () => {
    this.setState((prevState) => {
      const newFields = [
        ...prevState?.drawPoolFields,
        {
          drawNumber: prevState?.drawPoolFields?.length + 1,
          drawType: "",
          coins: "",
          description: "",
          errorDrawType: " ",
          errorCoins: "",
          errorDescription: "",
        },
      ];
      return { drawPoolFields: newFields };
    });
  };

  // Remove a specific field
  handleRemoveField = (index) => {
    this.setState((prevState) => {
      const prizePoolFields = [...prevState.prizePoolFields];
      prizePoolFields.splice(index, 1);

      // Recalculate winPlace and placeType for remaining prizePoolFields
      const updatedFields = prizePoolFields.map((field, idx) => ({
        ...field,
        winPlace: idx + 1,
        placeType: this.getOrdinalName(idx),
      }));

      return { prizePoolFields: updatedFields };
    });
  };

  // Remove a specific field for draw pool
  handleDrawPoolRemoveField = (index) => {
    this.setState((prevState) => {
      const drawPoolFields = [...prevState.drawPoolFields];
      drawPoolFields.splice(index, 1);

      // Recalculate winPlace and placeType for remaining drawPoolFields
      const updatedFields = drawPoolFields.map((field, idx) => ({
        ...field,
        drawNumber: idx + 1,
      }));

      return { drawPoolFields: updatedFields };
    });
  };

  // Validate all prizePoolFields
  handleValidate = () => {
    // const { eventsType, entryCoins, prizePoolFields, drawPoolFields } =
    //   this.state;
    let flag = true;

    // if (eventsType !== "free") {
    //   if (entryCoins === "") {
    //     flag = false;
    //     this.setState({
    //       errorEntryCoins: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorEntryCoins: "",
    //     });
    //   }
    // }
    // const updatedFields = prizePoolFields.map((field) => {
    //   const newField = { ...field };
    //   if (
    //     newField.coins === "" ||
    //     isNaN(newField.coins) ||
    //     Number(newField.coins) <= 0
    //   ) {
    //     newField.errorCoins = "Coins must be a positive number";
    //     flag = false;
    //   } else {
    //     newField.errorCoins = "";
    //   }

    //   if (newField.description.trim() === "") {
    //     newField.errorDescription = "Description is required";
    //     flag = false;
    //   } else {
    //     newField.errorDescription = "";
    //   }

    //   return newField;
    // });

    // const updatedDrawFields = drawPoolFields.map((field) => {
    //   const newField = { ...field };

    //   if (newField?.drawType === "") {
    //     newField.errorDrawType = "Draw Type is required";
    //     flag = false;
    //   } else {
    //     newField.errorDescription = "";
    //   }
    //   if (
    //     newField.coins === "" ||
    //     isNaN(newField.coins) ||
    //     Number(newField.coins) <= 0
    //   ) {
    //     newField.errorCoins = "Coins must be a positive number";
    //     flag = false;
    //   } else {
    //     newField.errorCoins = "";
    //   }

    //   if (newField?.description.trim() === "") {
    //     newField.errorDescription = "Description is required";
    //     flag = false;
    //   } else {
    //     newField.errorDescription = "";
    //   }

    //   return newField;
    // });

    // this.setState({
    //   prizePoolFields: updatedFields,
    //   // drawPoolFields: updatedDrawFields,
    // });
    return flag;
  };

  // Submit handler
  handleSubmit = async () => {
    let {
      eventsType,
      // entryCoins,
      minUserEntry,
      // prizePoolFields,
      // drawPoolFields,
      competitionName,
    } = this.state;
    let props = this.props;

    if (this.handleValidate()) {
      this.setState({ isLoading: true });
      // const validData = prizePoolFields?.map(
      //   ({ winPlace, placeType, coins, description }) => ({
      //     winPlace,
      //     placeType,
      //     coins: Number(coins),
      //     description,
      //   })
      // );
      // const validDrawData = drawPoolFields?.map(
      //   ({ drawNumber, drawType, coins, description }) => ({
      //     drawNumber,
      //     drawType,
      //     coins: Number(coins),
      //     description,
      //   })
      // );


      let seasonId;

      switch (props?.eventConfigurationList?.SportId) {
        case 4:
          seasonId = props?.eventConfigurationList?.CricketSeasonId;
          break;
        case 9:
          seasonId = props?.eventConfigurationList?.ARSeasonId;
          break;
        case 12:
          seasonId = props?.eventConfigurationList?.RLSeasonId;
          break;
        case 8:
          seasonId = props?.eventConfigurationList?.SoccerSeasonId;
          break;
        default:
          seasonId = props?.eventConfigurationList?.RLSeasonId;
          break;
      }
      

      console.log(seasonId, "seasonId")

      let payload = {
        eventName: props?.eventConfigurationList?.eventName,
        sportId: props?.eventConfigurationList?.SportId,
        eventId: props?.eventConfigurationList?.id,
        round: props?.eventConfigurationList?.round,
        startTime: props?.eventConfigurationList?.startTime,
        status: props?.eventConfigurationList?.status,
        eventType: eventsType,
        tournamentId:
          props?.eventConfigurationList?.SportId === 4
            ? props?.eventConfigurationList?.CricketTournamentId
            : props?.eventConfigurationList?.SportId === 9
            ? props?.eventConfigurationList?.ARTournamentId
            : props?.eventConfigurationList?.SportId === 8
            ? props?.eventConfigurationList?.SoccerTournamentId
            : props?.eventConfigurationList?.RLTournamentId,
        // prizePool: validData,
        // entryCoin: entryCoins,
        // drawPool: validDrawData,
        minUserEntry: minUserEntry,
        competitionName: competitionName,
        seasonId: seasonId,
      };

      let editPayload = {
        ...payload,
        competitionId: this.props?.eventConfigurationId,
      };

      try {
        const { status, data } = await fantasyAxiosInstance.post(
          `/events/event-configure `,
          this.props?.modalType === "edit" ? editPayload : payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
          });
          // this.fetchEventConfigurations();
          this.props?.toggleConfigurationModal();
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
          defaultImage: [],
          defaultUploadImage: "",
        });
      }
    }
  };

  render() {
    var {
      messageBox,
      eventsType,
      entryCoins,
      minUserEntry,
      errorEntryCoins,
      // prizePoolFields,
      // drawPoolFields,
      competitionName,
      // apiFetched,
    } = this.state;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Event</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  {this.props?.eventConfigurationList
                    ? this.props.eventConfigurationList?.eventName
                    : ""}
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{ display: "flex", justifyContent: "flex-end" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  onClick={this.props?.toggleConfigurationModal}
                >
                  Back
                </Button>
              </Grid>
            </Grid>
            <Box className="configuration-event-screen">
              <Grid container className="first-row-container">
                <Grid
                  item
                  xs={4}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    marginBottom: "8px",
                    alignItems: "flex-start",
                  }}
                >
                  <label className="modal-label">Competition Name</label>
                  <TextField
                    className="teamsport-textfield rec FAQ-textfield"
                    variant="outlined"
                    type="text"
                    color="primary"
                    size="small"
                    placeholder="Competition Name"
                    value={competitionName}
                    onChange={(e) =>
                      this.setState({
                        competitionName: e?.target?.value,
                      })
                    }
                  />
                </Grid>
                <Grid
                  item
                  xs={4}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    marginBottom: "8px",
                    alignItems: "flex-start",
                  }}
                >
                  <label className="modal-label">Event Type</label>
                  <Select
                    className="React teamsport-select event-Tournament-select event-type-select"
                    classNamePrefix="select"
                    menuPosition="fixed"
                    placeholder="Status"
                    value={
                      eventsType &&
                      statusOption?.find((item) => {
                        return item?.value === eventsType;
                      })
                    }
                    options={statusOption}
                    onChange={(e) => {
                      this.setState({
                        eventsType: e?.value,
                        entryCoins: e?.value === "free" ? "" : entryCoins,
                        minUserEntry: e?.value === "free" ? "" : minUserEntry,
                        // prizePoolFields:
                        //   e?.value === "free" ? [] : prizePoolFields,
                        // drawPoolFields:
                        //   e?.value === "free" ? [] : drawPoolFields,
                        errorEntryCoins:
                          e?.value === "free" ? "" : errorEntryCoins,
                      });
                    }}
                  />
                </Grid>
                {/* <Grid
                  item
                  xs={3}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    marginBottom: "8px",
                    alignItems: "flex-start",
                  }}
                >
                  <label className="modal-label">Entry Coins</label>
                  <TextField
                    className="teamsport-textfield rec FAQ-textfield"
                    variant="outlined"
                    type="number"
                    color="primary"
                    size="small"
                    placeholder="Entry Coins"
                    value={entryCoins}
                    disabled={eventsType === "free"}
                    onChange={(e) =>
                      this.setState({
                        entryCoins: e?.target?.value,

                        errorEntryCoins: e?.target?.value
                          ? ""
                          : errorEntryCoins,
                      })
                    }
                  />
                  {errorEntryCoins ? (
                    <p
                      className="errorText"
                      style={{ margin: "0px 0px 0px 0px" }}
                    >
                      {errorEntryCoins}
                    </p>
                  ) : (
                    ""
                  )}
                </Grid> */}
                <Grid
                  item
                  xs={4}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    marginBottom: "8px",
                    alignItems: "flex-start",
                  }}
                >
                  <label className="modal-label">Min User Entry</label>
                  <TextField
                    className="teamsport-textfield rec FAQ-textfield"
                    variant="outlined"
                    type="number"
                    color="primary"
                    size="small"
                    placeholder="Min User Entry"
                    value={minUserEntry}
                    disabled={eventsType === "free"}
                    onChange={(e) =>
                      this.setState({
                        minUserEntry: e?.target?.value,
                      })
                    }
                  />
                </Grid>
              </Grid>
              {/* {eventsType === "paid" ? (
                <> */}
              {/* <Box>
                <Box className="prizePool-header-container">
                  <Typography>Prize Pool</Typography>
                </Box>
                {prizePoolFields?.map((field, index) => (
                  <Box key={index} className="prizePool-row-container">
                    <Grid item xs={6}>
                      <Box className="prizePool-col">
                        <label className="modal-label">
                          Coins {field?.placeType}
                        </label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Coins"
                          name={`coins-${index}`}
                          value={field.coins}
                          onChange={(e) =>
                            this.handleInputChange(
                              index,
                              "coins",
                              e.target.value
                            )
                          }
                        />
                        {field.errorCoins && (
                          <p
                            className="errorText"
                            style={{ margin: 0, color: "red" }}
                          >
                            {field.errorCoins}
                          </p>
                        )}
                      </Box>
                    </Grid>

                    <Grid item xs={6}>
                      <Box className="prizePool-col">
                        <label className="modal-label">
                          Description {field?.placeType}
                        </label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Description"
                          multiline
                          rows={3}
                          name={`description-${index}`}
                          type="text"
                          value={field.description}
                          onChange={(e) =>
                            this.handleInputChange(
                              index,
                              "description",
                              e.target.value
                            )
                          }
                        />
                        {field?.errorDescription && (
                          <p
                            className="errorText"
                            style={{ margin: 0, color: "red" }}
                          >
                            {field?.errorDescription}
                          </p>
                        )}
                      </Box>
                    </Grid>

                    <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
                      {prizePoolFields?.length > 1 && (
                        <IconButton
                          color="secondary"
                          onClick={() => this.handleRemoveField(index)}
                          aria-label="remove field"
                          className="remove-icon"
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </Box>
                  </Box>
                ))}
                <Button
                  startIcon={<AddIcon />}
                  variant="contained"
                  onClick={this.handleAddField}
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                >
                  Add Prize Pool Field
                </Button>
              </Box> */}

              {/* <Box style={{ marginTop: "18px" }}>
                <Box className="prizePool-header-container">
                  <Typography>Draw Pool</Typography>
                </Box>
                {drawPoolFields?.map((field, index) => (
                  <>
                    <Box className="draw-pool-container">
                      <Box style={{ width: "100%" }}>
                        <Box key={index} className="prizePool-row-container">
                          <Grid item xs={6}>
                            <Box className="prizePool-col">
                              <label className="modal-label">
                                Draw Type {field?.drawNumber}
                              </label>
                              <TextField
                                className="teamsport-textfield rec FAQ-textfield"
                                variant="outlined"
                                type="text"
                                color="primary"
                                size="small"
                                placeholder="Draw Type"
                                name={`Draw Type-${index}`}
                                value={field?.drawType}
                                onChange={(e) =>
                                  this.handleDrawInputChange(
                                    index,
                                    "drawType",
                                    e.target.value
                                  )
                                }
                              />
                              {/* {field.errorDrawType && (
                                <p
                                  className="errorText"
                                  style={{ margin: 0, color: "red" }}
                                >
                                  {field.errorDrawType}
                                </p>
                              )} /}
                            </Box>
                          </Grid>

                          <Grid item xs={6}>
                            <Box className="prizePool-col">
                              <label className="modal-label">
                                Coins {field?.drawNumber}
                              </label>
                              <TextField
                                className="teamsport-textfield rec FAQ-textfield"
                                variant="outlined"
                                type="number"
                                color="primary"
                                size="small"
                                placeholder="Coins"
                                name={`coins-${index}`}
                                value={field.coins}
                                onChange={(e) =>
                                  this.handleDrawInputChange(
                                    index,
                                    "coins",
                                    e.target.value
                                  )
                                }
                              />
                              {/* {field.errorCoins && (
                                <p
                                  className="errorText"
                                  style={{ margin: 0, color: "red" }}
                                >
                                  {field.errorCoins}
                                </p>
                              )} /}
                            </Box>
                          </Grid>
                        </Box>
                        <Box className="prizePool-row-container">
                          <Grid item xs={12}>
                            <Box className="prizePool-col">
                              <label className="modal-label">
                                Description {field?.drawNumber}
                              </label>
                              <TextField
                                className="teamsport-textfield rec FAQ-textfield"
                                variant="outlined"
                                color="primary"
                                size="small"
                                placeholder="Description"
                                multiline
                                rows={3}
                                name={`description-${index}`}
                                type="text"
                                value={field.description}
                                onChange={(e) =>
                                  this.handleDrawInputChange(
                                    index,
                                    "description",
                                    e.target.value
                                  )
                                }
                              />
                              {/* {field?.errorDescription && (
                                <p
                                  className="errorText"
                                  style={{ margin: 0, color: "red" }}
                                >
                                  {field?.errorDescription}
                                </p>
                              )} /}
                            </Box>
                          </Grid>
                        </Box>
                      </Box>
                      <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
                        {drawPoolFields?.length > 1 && (
                          <IconButton
                            color="secondary"
                            onClick={() =>
                              this.handleDrawPoolRemoveField(index)
                            }
                            aria-label="remove field"
                            className="remove-icon"
                          >
                            <DeleteIcon />
                          </IconButton>
                        )}
                      </Box>
                    </Box>
                  </>
                ))}
                <Button
                  startIcon={<AddIcon />}
                  variant="contained"
                  onClick={this.handleDrawPoolAddField}
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                >
                  Add Draw Pool Field
                </Button>
              </Box> */}
              {/* </>
              ) : (
                ""
              )} */}

              <ButtonComponent
                style={{ marginTop: "15px" }}
                className="mt-3 admin-btn-green"
                onClick={() => this.handleSubmit()}
                color="primary"
                value={"Save"}
                // disabled={isLoading}
              />
            </Box>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default ConfigurationsEvent;
