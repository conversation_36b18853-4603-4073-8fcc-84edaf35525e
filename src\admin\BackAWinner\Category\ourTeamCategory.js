import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Typography,
  Breadcrumbs,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import RateReviewIcon from "@mui/icons-material/RateReview";
// import ButtonComponent from "../../library/common/components/Button";
import { Editor } from "react-draft-wysiwyg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import { Loader, ActionMessage } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import { config } from "../../../helpers/config";
import Pagination from "@mui/material/Pagination";
import SearchIcons from "../../../images/searchIcon.svg";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../../helpers/common";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../../library/common/components/Button";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import FileUploader from "../../../library/common/components/FileUploader";

class OurTeamCategory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      ourTeamCategory: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      ourteamCategoryValues: {
        categoryName: "",
        id: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      ourTeamCategoryCount: 0,
      bookKeeperId: "",
      errorName: "",
      search: "",
      draggedItem: null,
      isSortChange: false,
    };
  }

  componentDidMount() {
    // this.fetchAllOurTeamCategory(0, "");
    this.fetchAllOurTeamCategory("");
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      // this.fetchAllOurTeamCategory(this.state.offset, this.state?.search);
      this.fetchAllOurTeamCategory(this.state?.search);
    }
  }

  async fetchAllOurTeamCategory(searchvalue) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        // `ourteam/all/category?limit=${rowPerPage}&offset=${page}&name=${searchvalue}`
        `ourteam/all/category?name=${searchvalue}`
      );
      if (status === 200) {
        this.setState({
          ourTeamCategory: data?.result,
          isLoading: false,
          ourTeamCategoryCount: data?.count,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { ourteamCategoryValues } = this.state;
    let flag = true;
    if (ourteamCategoryValues?.categoryName?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        name: this.state?.ourteamCategoryValues?.categoryName.trim(),
      };
      let passApi = "/ourteam/category";
      try {
        const { status, data } = await axiosInstance.post(
          `${passApi}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          // this.fetchAllOurTeamCategory(this.state.offset, this.state?.search);
          this.fetchAllOurTeamCategory(this.state?.search);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
        });
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        name: this.state?.ourteamCategoryValues?.categoryName.trim(),
      };
      let passApi = "/ourteam/category/";
      try {
        const { status, data } = await axiosInstance.put(
          `${passApi}/${this.state.ourteamCategoryValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          // this.fetchAllOurTeamCategory(this.state.offset, this.state?.search);
          this.fetchAllOurTeamCategory(this.state?.search);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        ourteamCategoryValues: {
          categoryName: item?.name,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        ourteamCategoryValues: {
          categoryName: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllOurTeamCategory();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `/ourteam/category/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          // this.fetchAllOurTeamCategory(this.state.offset, this.state?.search);
          this.fetchAllOurTeamCategory(this.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          "Our Team Category Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handeModleLoading = (status) => {
    this.setState({
      isLoading: status,
    });
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllOurTeamCategory(search);
      this.setState({ currentPage: 1 });
    }
  };

  handleDragEnd = (result) => {
    if (!result?.destination) {
      return;
    }
    const updatedList = Array.from(this.state?.ourTeamCategory);
    const [draggedItem] = updatedList?.splice(result?.source?.index, 1);
    updatedList.splice(result?.destination?.index, 0, draggedItem);
    this.setState({ ourTeamCategory: updatedList, isSortChange: true });
  };

  handleOrderChange = async () => {
    const { offset, search, ourTeamCategory, sortLabelid } = this.state;
    let sortData = sortLabelid;

    let newdata = [];
    let categories = ourTeamCategory?.map((item) => {
      newdata.push(item?.id);
    });
    try {
      const { status, data } = await axiosInstance.put(
        `/ourteam/category/order/order`,
        newdata
      );
      if (status === 200) {
        this.setState({ isSortChange: false });
        this.fetchAllOurTeamCategory("");
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isSortChange: false });
      }
    } catch (err) {
      this.setState({ isSortChange: false });
    }
  };

  render() {
    var {
      ourTeamCategory,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      ourteamCategoryValues,
      errorName,
      ourTeamCategoryCount,
      search,
    } = this.state;

    const pageNumbers = [];
    if (ourTeamCategoryCount > 0) {
      for (let i = 1; i <= Math.ceil(ourTeamCategoryCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper > */}
            {messageBox?.display && (
              <ActionMessage
                message={messageBox?.message}
                type={messageBox?.type}
                styleClass={messageBox?.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Our Team Category</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Our Team Category
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    // this.fetchAllOurTeamCategory(0, search);
                    this.fetchAllOurTeamCategory(search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Button
                variant="contained"
                style={{
                  backgroundColor: this.state.isSortChange
                    ? "#4455c7"
                    : "rgba(0, 0, 0, 0.12)",
                  color: this.state.isSortChange
                    ? "#fff"
                    : "rgba(0, 0, 0, 0.26)",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                }}
                disabled={!this.state.isSortChange}
                onClick={() => {
                  this.handleOrderChange();
                }}
              >
                Save Order
              </Button>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && ourTeamCategory?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && ourTeamCategory?.length > 0 && (
              <DragDropContext onDragEnd={this.handleDragEnd}>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <Droppable droppableId="your-droppable-id">
                      {(provided, snapshot) => (
                        <TableBody
                          className="table_body"
                          ref={provided.innerRef}
                        >
                          <TableRow className="table_row">
                            <TableCell
                              colSpan={100}
                              className="table-seprator"
                            ></TableCell>
                          </TableRow>
                          {ourTeamCategory?.map((bookkeeper, i) => {
                            return (
                              <Draggable
                                key={bookkeeper?.id}
                                draggableId={`draggable-${bookkeeper?.id}`}
                                index={i}
                              >
                                {(provided, snapshot) => (
                                  <TableRow
                                    className="table-rows listTable-Row"
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    style={{
                                      cursor: "all-scroll",
                                      ...provided.draggableProps.style,
                                    }}
                                    key={bookkeeper?.id}
                                  >
                                    <TableCell>{bookkeeper?.id}</TableCell>
                                    <TableCell>
                                      <Box>{bookkeeper?.name}</Box>
                                    </TableCell>

                                    <TableCell>
                                      <Box style={{ display: "flex" }}>
                                        <Button
                                          onClick={this.inputModal(
                                            bookkeeper,
                                            "edit"
                                          )}
                                          className="table-btn edit-btn"
                                        >
                                          Edit
                                        </Button>
                                        <Button
                                          onClick={() =>
                                            this.setItemToDelete(bookkeeper.id)
                                          }
                                          className="table-btn delete-btn"
                                        >
                                          Delete
                                        </Button>
                                      </Box>
                                    </TableCell>
                                  </TableRow>
                                )}
                              </Draggable>
                            );
                          })}
                          {/* <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              ourTeamCategoryCount / rowPerPage > 1
                                ? false
                                : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow> */}
                        </TableBody>
                      )}
                    </Droppable>
                  </Table>
                </TableContainer>
              </DragDropContext>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Category" : "Edit Category"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleInputModal()}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">Our Team Category</label>
                      <TextField
                        className="teamsport-textfield eventname"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Our Team Category"
                        value={ourteamCategoryValues?.categoryName}
                        onChange={(e) =>
                          this.setState({
                            ourteamCategoryValues: {
                              ...ourteamCategoryValues,
                              categoryName: e.target.value,
                            },
                          })
                        }
                      />
                      {errorName ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorName}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default OurTeamCategory;
