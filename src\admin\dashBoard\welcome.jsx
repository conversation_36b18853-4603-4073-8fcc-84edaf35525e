import React, { Component } from "react";
import Grid from "@mui/material/Grid";

class Welcome extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      pagesList: [],
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
    };
  }

  render() {
    return (
      <Grid container className="page-content adminLogin">
        <Grid item xs={12} className="pageWrapper">
          <h1
            style={{
              height: "85vh",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: "#ffffff",
            }}
          >
            welcome to Smartb Admin panel
          </h1>
        </Grid>
      </Grid>
    );
  }
}

export default Welcome;
