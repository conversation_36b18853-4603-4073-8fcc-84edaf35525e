import React from "react";
import ReactDOM from "react-dom/client"; // Updated import
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import "./index.scss";
import App from "./components/index";
import * as serviceWorker from "./serviceWorker";
import * as redux from "./store/createStore";

window.store = redux.store;

const rootElement = document.getElementById("root");

// Using createRoot for React 18
const root = ReactDOM.createRoot(rootElement);

// Render the app
root.render(
  <Provider store={redux.store}>
    <PersistGate loading={null} persistor={redux.persistor}>
      <App />
    </PersistGate>
  </Provider>
);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
