import React, { Component } from "react";
import { <PERSON>rid, <PERSON>po<PERSON>, <PERSON>, <PERSON>readcrum<PERSON>, Button } from "@mui/material";
import { Link } from "react-router-dom";
import axiosInstance from "../../helpers/Axios";
import moment from "moment";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import ActionMessage from "../../library/common/components/ActionMessage";

class PastFixtureImport extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: null,
      endDate: null,
      isLoading: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      errorStartDate: "",
      errorEndDate: "",
    };
  }
  handleStartDate = (date) => {
    this.setState({ startDate: date });
  };
  handleEndDate = (date) => {
    this.setState({ endDate: date });
  };
  handleValidate = () => {
    let { startDate, endDate } = this.state;
    let flag = true;
    if (!startDate) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    if (!endDate) {
      flag = false;
      this.setState({
        errorEndDate: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorEndDate: "",
      });
    }
    return flag;
  };

  handlePastFixture = async () => {
    if (this.handleValidate()) {
      this.setState({ isLoading: true });
      const { startDate, endDate } = this.state;
      let payload = {
        startDate: moment(startDate).format("YYYY-MM-DD"),
        endDate: moment(endDate).format("YYYY-MM-DD"),
      };
      const { status, data } = await axiosInstance.post(
        "sync/past/fixture",
        payload
      );
      if (status === 200) {
        this.setState({
          isLoading: false,
          startDate: null,
          endDate: null,
        });
        this.setActionMessage(
          true,
          "Success",
          data?.sync
            ? "Past Fixture data import Process started, you can check progress on Error Logs"
            : "Right now Past Fixture process already in progress of last selected date so please try again once last process it completed"
        );
      }
    }
  };
  // setActionMessage = (display = false, type = "", message = "") => {
  //   let setActionMessage = {
  //     display: display,
  //     type: type,
  //     message: message,
  //   };
  //   this.setState({ messageBox: setActionMessage });
  // };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  render() {
    const { startDate, endDate, messageBox, errorStartDate, errorEndDate } =
      this.state;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Racing
                </Link>
                <Typography className="active_p">
                  Past Fixture import
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  Past Fixture import
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
                className="admin-fixture-wrap"
              >
                <Grid item xs={3}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <Grid container>
                      <DesktopDatePicker
                        autoOk
                        // disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        placeholder="Start Date"
                        margin="normal"
                        id="date-picker-inline"
                        inputVariant="outlined"
                        value={
                          startDate
                            ? typeof startDate === "string"
                              ? parseISO(startDate)
                              : startDate
                            : null
                        }
                        onChange={(e) => this.handleStartDate(e)}
                        KeyboardButtonProps={{
                          "aria-label": "change date",
                        }}
                        slots={{
                          openPickerIcon: TodayIcon,
                        }}
                        slotProps={{
                          field: {
                            placeholder: "Start Date",
                          },
                        }}
                        className="date-picker-fixture"
                      />
                    </Grid>
                  </LocalizationProvider>
                  {errorStartDate ? (
                    <p className="errorText" style={{ margin: "0px 0 0 0" }}>
                      {errorStartDate}
                    </p>
                  ) : (
                    ""
                  )}
                </Grid>
                <Grid item xs={3}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <Grid container>
                      <DesktopDatePicker
                        autoOk
                        // disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        placeholder="End Date"
                        margin="normal"
                        id="date-picker-inline"
                        inputVariant="outlined"
                        value={
                          endDate
                            ? typeof endDate === "string"
                              ? parseISO(endDate)
                              : endDate
                            : null
                        }
                        minDate={
                          startDate
                            ? typeof startDate === "string"
                              ? parseISO(startDate)
                              : startDate
                            : null
                        }
                        onChange={(e) => this.handleEndDate(e)}
                        KeyboardButtonProps={{
                          "aria-label": "change date",
                        }}
                        slots={{
                          openPickerIcon: TodayIcon,
                        }}
                        slotProps={{
                          field: {
                            placeholder: "End Date",
                          },
                        }}
                        className="date-picker-fixture"
                      />
                    </Grid>
                  </LocalizationProvider>
                  {errorEndDate ? (
                    <p className="errorText" style={{ margin: "0px 0 0 0" }}>
                      {errorEndDate}
                    </p>
                  ) : (
                    ""
                  )}
                </Grid>
                <div>
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "10px 15px",
                      marginTop: "5px",
                    }}
                    onClick={() => this.handlePastFixture()}
                  >
                    import fixture
                  </Button>
                </div>
              </Grid>
            </Grid>
            {/* <Grid container direction="row" alignItems="center">
              <Grid item xs={12} style={{ textAlign: "end" }}>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "10px 15px",
                    marginTop: "5px",
                  }}
                  // onClick={() => this.handlePastFixture()}
                >
                  Clear
                </Button>
              </Grid>
            </Grid> */}
          </Grid>
        </Grid>
      </>
    );
  }
}

export default PastFixtureImport;
