.teamsport-text {
  .teamsport-textfield {
    width: 95%;
    margin-bottom: 15px;
    margin-top: 3px;

    input {
      border-radius: 8px;
    }

    .MuiOutlinedInput-root {
      border-radius: 8px;
      background-color: #ffffff;
      .MuiOutlinedInput-inputMarginDense {
        padding: 10.5px;
        background-color: #ffffff;
      }
      .MuiOutlinedInput-inputMultiline {
        padding: 10.5px;
      }
    }
    .Mui-disabled {
      opacity: 0.4;
    }
  }

  .eventname {
    width: 97%;
  }
}

.teamsport-select {
  width: 22%;

  .select__control {
    height: 100%;
    margin-top: 3px;
    border-radius: 8px;
  }

  .select__menu {
    text-align: left;
  }

  .select__value-container {
    text-transform: capitalize;
    text-align: left;
  }

  .select__menu-list {
    text-transform: capitalize;
  }
}

.national-select {
  .teamsport-select {
    width: 95%;
  }
}

.radio-wrap {
  .gender {
    flex-direction: row;

    .MuiButtonBase-root {
      min-width: auto;
    }
  }
}

.teamsport-multiple-select {
  width: 97%;
}

.teamsport-dob-wrap {
  .dob-picker {
    margin: 0px;
  }
}

.date-time-picker-wrap {
  .date-time-picker {
    width: 95%;

    input {
      background: white;
      padding: 10.5px 14px;
      border-radius: 8px;
    }

    fieldset {
      border-radius: 8px;
    }
  }

  .MuiInputBase-input::placeholder {
    color: rgba(0, 0, 0, 0.87);
    opacity: 0.87;
  }
}

.filter-date-picker {
  width: 25%;

  .MuiOutlinedInput-adornedEnd {
    padding-right: 0px;
  }

  .MuiInputAdornment-positionEnd {
    button {
      min-width: auto;
    }
  }
}

.eventname {
  width: 97%;
}

.event-Tournament-select {
  width: 95%;
  min-height: 40px;
}

.external-select {
  .select__control {
    margin-top: 0px;
    .select__value-container {
      text-align: left;
    }
  }
}

.txt-field-class {
  background: #ffffff;
  width: 282px;
}

.future-fixture-wrap {
  .external-select {
    margin-right: 10px;

    .select__control {
      min-height: 40px;
    }
  }
  .position-select {
    width: 27%;
  }
}

.MuiTableCell-root {
  border-bottom: none;
}

.MuiTableRow-root {
  border-bottom: 1px solid rgba(224, 224, 224, 1);
}

.stepper-input-wrap {
  .stepper-input {
    display: flex;
    border: 1px solid #c9c9c9;
    border-radius: 8px;
    max-height: 36px;
    width: 95%;
    margin-top: 6px;

    .stepper-input__button {
      padding: 10px 14px;
      min-width: 35px;
      cursor: pointer;
      border-radius: 0px;

      border-left: 1px solid #c9c9c9;
      border-collapse: collapse;

      &:first-child {
        border-left: 0px;
        border-right: 1px solid #c9c9c9;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
      }
    }

    .stepper-input__content {
      // font-family: $regulerFont;
      font-size: 16px;
      line-height: 20px;
      font-weight: 600;
      padding: 8px 26px;
      background-color: #ffffff;
      color: #000000;
      width: 100%;
      text-align: center;
    }
  }
}

.upload-img {
  img {
    max-width: 35px;
    max-height: 35px;
  }
}

.search-field {
  .MuiOutlinedInput-root {
    border-radius: 8px;
    background-color: #ffffff;
    min-height: 40px;
  }
}

.error-select {
  width: 30%;
}

.event-search {
  background-color: #ffffff;
  width: 30%;

  .MuiInputBase-root {
    border-radius: 8px;
  }
}

.danger {
  background-color: red;
  color: #ffffff;
  padding: 0px 5px;
  border-radius: 6px;
  cursor: pointer;
}

.mb15 {
  margin-bottom: 15px;
}

.fixture-import-wrap {
  .external-select .select__control {
    min-height: 40px !important;
  }
}
