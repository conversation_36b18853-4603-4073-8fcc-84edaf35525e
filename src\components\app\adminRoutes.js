import Dashboard from "../../admin/dashBoard/Dashboard";
import Sports from "../../admin/sports";
import SportType from "../../admin/sportType";
import ApiProvider from "../../admin/APIprovider";
import ApiProviderUpdate from "../../admin/APIprovider/APIproviderUpdate";
import ApiProviderApisFeildMapping from "../../admin/APIprovider/apiProviderApisFeildMapping";

import Users from "../../admin/Users";
import Players from "../../admin/players";
import Country from "../../admin/country";
import States from "../../admin/states";
import Bookkeeper from "../../admin/bookkeeper";
import BookkeeperReview from "../../admin/bookkeeper/reviewBookkeeper/review.js";

import RacingErrorLogs from "../../admin/sportsDetailsTeam/RacingErrorLogs";
import ErrorDetailsPage from "../../admin/sportsDetailsTeam/RacingErrorLogs/errorDetailsPage";

import Events from "../../admin/events";
import ApiEventIdentifire from "../../admin/apiEventIdentifire";

import RaceTable from "../../admin/raceTable";
import Cities from "../../admin/cities";
import ApiKeyIdentifire from "../../admin/apiKeyIdentifire";
import Track from "../../admin/track";
import ApiRaceIdentifire from "../../admin/apiRaceIdentifire";
import Animal from "../../admin/animal";
import SportsDetails from "../../admin/sportsDetails";
import RunnersDetails from "../../admin/sportsDetails/runnersDetails";

import AnimalDetails from "../../admin/sportsDetails/animals/index";
import JockeyDetails from "../../admin/sportsDetails/jockeys/index";
// import TrainersDetails from '../../admin/sportsDetails/trainers/index'
import NoData from "../../admin/noData";
import TracksDetails from "../../admin/sportsDetails/tracksDetails";
import RacingMeetings from "../../admin/sportsDetails/meetings";
import OddsView from "../../admin/sportsDetails/odds";
import OddseventView from "../../admin/sportsDetails/odds/oddsEvent";
import NotFound from "../404/404";

import Welcome from "../../admin/dashBoard/welcome";
import PastFixtureImport from "../../admin/pastFixtureImport/pastFixture";
import Cricketcategory from "../../admin/Cricket/category/Category";
import Crickettournaments from "../../admin/Cricket/Tournament/Tournament";
import CricketTeam from "../../admin/Cricket/Team/Team";
import CricketPlayer from "../../admin/Cricket/Player/Player";
import CricketEvent from "../../admin/Cricket/Event/event";
import CricketMarket from "../../admin/Cricket/Market/Market";
import TeamSportsCategory from "../../admin/TeamSport/Category/Category";
import TeamSportsTournaments from "../../admin/TeamSport/Tournament/Tournament";
import TeamSportsTeam from "../../admin/TeamSport/Team/Team";
import TeamSportsPlayer from "../../admin/TeamSport/Player/Player";
import TeamSportsEvent from "../../admin/TeamSport/Event/Event";
import TeamSportsMarket from "../../admin/TeamSport/Market/Market";
import MarketError from "../../admin/TeamSport/MarketError/MarketError.jsx";
import EventError from "../../admin/TeamSport/EventError/EventError.jsx";
import UniqueTournament from "../../admin/TeamSport/uniqueTournament/UniqueTournament.js";
import TeamSportDashboard from "../../admin/TeamSport/Dashboard/Dashboard.jsx";
import TeamSportAutomation from "../../admin/TeamSport/Automation/Automation.jsx";
import TeamSportLabel from "../../admin/TeamSport/Label/Label.js";
import TeamSportFutureImport from "../../admin/TeamSport/FutureFixtureImport/FutureFixtureImport";
import Automation from "../../admin/Automation/Automation.jsx";
import AdvertisementSection from "../../admin/AdvertisementSection/AdvertisementSection.jsx";
import AdvertisingScreen from "../../admin/AdvertisingScreen/AdvertisingScreen.jsx";
import SponsoredOdds from "../../admin/sponsoredOdds";
import TeamSportMatchup from "../../admin/TeamSport/MatchUp/MatchUp";
import NewsImport from "../../admin/News/NewsImport";
import NewsCategory from "../../admin/News/NewsCategory";
import NewsTag from "../../admin/News/NewsTag";
import NewsArticle from "../../admin/News/NewsArticle";
import MobileAdvertisementSection from "../../admin/MobileAdvertisementSection/MobileAdvertisementSection";
import BookkeeperCounter from "../../admin/BookkeeperClickCounter/BookkeeperCounter";
import FeatureSports from "../../admin/featureSports/FetureSports";
import FeatureRace from "../../admin/featureRace/FeatureRace";
import UserDashboard from "../../admin/UserDashboard";
import ClientInfo from "../../admin/clientInfo";
import MediaGallery from "../../admin/mediaGallery";
import AdCampaign from "../../admin/adCampaign";
import CampaignInfo from "../../admin/campaignInfo";
import SportPlayerMerge from "../../admin/TeamSport/SportPlayerMerge/sportPlayerMerge.jsx";
import SponsorsLogo from "../../admin/sponsorsLogo";
import ExpertTips from "../../admin/expertTips/ExpertTips.js";
import BAWFeaturedRace from "../../admin/expertTips/featuredRace/featuredRace.js";
import WeeklyNewsLetter from "../../admin/expertTips/weeklyNewsletter/WeeklyNewsLatter.js";
import RecommendedWebsite from "../../admin/RecommendedWebsite/RecommendedWebsite.js";
import OurTeams from "../../admin/BackAWinner/Teams/OurTeams.js";
import OurTeamCategory from "../../admin/BackAWinner/Category/ourTeamCategory.js";
import TipsOfTheDay from "../../admin/expertTips/tipsOfTheDay/TipOfTheDay.js";
import Seasons from "../../admin/TeamSport/Seasons/index.js";
import SportExpertTips from "../../admin/sportExpertTips/index.js";
import UsersReferral from "../../admin/UsersReferral/index.js";
import OurTeamPositions from "../../admin/BackAWinner/Positions/index.js";
import FeaturedCalender from "../../admin/featuredCalender/featuredCalender.js";
import ScraperFixtureSync from "../../admin/scraperFixtureSync/ScraperFixtureSync.js";
import SubscriberDashboard from "../../admin/subscriberDashboard/index.js";
import CompTipsPage from "../../admin/tippingComp/tippingComp.jsx";
import SubscriptionActive from "../../admin/subscriptionActive/index.js";
import TippingFAQs from "../../admin/tippingFAQs/index.js";
import TippingPrize from "../../admin/tippingPrize/index.js";
import SubCoupon from "../../admin/subscriptionCoupon/index.js";
import PremiumTippingComps from "../../admin/tippingPremiumPrize/index.js";
import Testimonial from "../../admin/testimonial/testimonial.js";
import SocContactUs from "../../admin/SOCContactUs/SOCContactUs.js";
import SubscriptionDashboard from "../../admin/subscriptionDashboard/index.js";
import FantasyCoins from "../../admin/fantasyCoins/index.js";
import FantasyWithdraw from "../../admin/fantasyWithdraw/index.js";
import FantasyKYC from "../../admin/fantasyKYC/index.js";
import TeamSportRules from "../../admin/TeamSport/Rules/index.js";
import FantasyUserCoins from "../../admin/fantasyUserCoins/index.js";
import TippingList from "../../admin/tippingList/index.js";
import TippingTipsterList from "../../admin/tippingList/tippingListId/index.js";
import SOCSmartplayDashboard from "../../admin/socSmartplayDashboard/index.js";
import SOCBroadcast from "../../admin/socBroadcast/index.js";
import Squad from "../../admin/TeamSport/Squad/index.js";
import SyncSeasons from "../../admin/TeamSport/syncSeasons/SyncSeasons.js";
import LineupEvent from "../../admin/TeamSport/Event/lineupEvent/index.js";
import SOCSmartplayDashboardNew from "../../admin/socSmartplayDashboardNew/index.js";

const adminRoutesArray = [
  { path: "/dashboard", component: Welcome },
  { path: "/sports", component: Sports },
  { path: "/sporttype", component: SportType },
  {
    path: "/racing/:name/:id/:eventid/:eventname",
    component: SportsDetails,
  },
  {
    path: "/racing/dashboard",
    component: Dashboard,
  },
  {
    path: "/racing/odds",
    component: OddsView,
  },
  {
    path: "/racing/odds/:eventId",
    component: OddsView,
  },
  {
    path: "/racing/odds/:eventId/:raceId",
    component: OddsView,
  },
  { path: "/racing/tracks", component: TracksDetails },
  { path: "/racing/errorLogs", component: RacingErrorLogs },
  { path: "/racing/pastFixture", component: PastFixtureImport },
  // { path: "/racing/pastFixture", component: PastFixtureImport },
  { path: "/racing/errorLogs/errorDetails/:id", component: ErrorDetailsPage },
  {
    path: "/racingdetail/:name/:id/animal/:animalname/:typeid",
    component: AnimalDetails,
  },
  {
    path: "/racingdetail/:name/:id/:player/:typeid",
    component: JockeyDetails,
  },
  // { path: '/racing/:name/:id/trainers', component: TrainersDetails },
  { path: "/racing/meetings/:name/:id", component: RacingMeetings },
  { path: "/racing/:name", component: RacingMeetings },
  { path: "/noData/:pagename", component: NoData },
  {
    path: "/:sportname/:sportid/:eventname/:eventid/runners/:id",
    component: RunnersDetails,
  },
  { path: "/apiprovider", component: ApiProvider },
  {
    path: "/apiprovider/apifieldmapping/:providerid/:id",
    component: ApiProviderApisFeildMapping,
  },
  {
    path: "/apiprovider/apiproviderupdate/:id",
    component: ApiProviderUpdate,
  },
  { path: "/apikeyidentifire", component: ApiKeyIdentifire },
  { path: "/users", component: Users },
  { path: "/users-referral", component: UsersReferral },
  { path: "/players", component: Players },
  { path: "/countries", component: Country },
  { path: "/countries/states/:id/:countryname", component: States },
  {
    path: "/countries/cities/:countryid/:id/:countryname/:stateName",
    component: Cities,
  },
  { path: "/animal", component: Animal },
  { path: "/bookkeeper", component: Bookkeeper },
  { path: "/bookkeeper-review", component: BookkeeperReview },
  { path: "/events", component: Events },
  { path: "/apieventidentifire", component: ApiEventIdentifire },
  { path: "/racetable", component: RaceTable },
  { path: "/apiraceidentifire", component: ApiRaceIdentifire },
  { path: "/track", component: Track },
  { path: "/cricket/category", component: TeamSportsCategory },
  { path: "/cricket/tournaments", component: TeamSportsTournaments },
  { path: "/cricket/team", component: TeamSportsTeam },
  { path: "/cricket/player", component: TeamSportsPlayer },
  { path: "/cricket/event", component: TeamSportsEvent },
  { path: "/cricket/event/:id", component: LineupEvent },
  { path: "/cricket/market", component: TeamSportsMarket },
  { path: "/cricket/marketerror", component: MarketError },
  { path: "/cricket/eventerror", component: EventError },
  { path: "/cricket/futurefixtureimport", component: TeamSportFutureImport },
  { path: "/cricket/dashboard", component: TeamSportDashboard },
  { path: "/cricket/automation", component: TeamSportAutomation },
  { path: "/cricket/Label", component: TeamSportLabel },
  { path: "/cricket/seasons/:sportId/:tournamentId", component: Seasons },
  { path: "/cricket/unique-tournament", component: UniqueTournament },
  { path: "/cricket/rules", component: TeamSportRules },
  { path: "/cricket/squad", component: Squad },
  { path: "/rugbyleague/category", component: TeamSportsCategory },
  { path: "/rugbyleague/tournaments", component: TeamSportsTournaments },
  { path: "/rugbyleague/team", component: TeamSportsTeam },
  { path: "/rugbyleague/player", component: TeamSportsPlayer },
  { path: "/rugbyleague/event", component: TeamSportsEvent },
  { path: "/rugbyleague/event/:id", component: LineupEvent },
  { path: "/rugbyleague/market", component: TeamSportsMarket },
  { path: "/rugbyleague/marketerror", component: MarketError },
  { path: "/rugbyleague/eventerror", component: EventError },
  { path: "/rugbyleague/dashboard", component: TeamSportDashboard },
  {
    path: "/rugbyleague/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/rugbyleague/automation", component: TeamSportAutomation },
  { path: "/rugbyleague/Label", component: TeamSportLabel },
  { path: "/rugbyleague/seasons/:sportId/:tournamentId", component: Seasons },
  { path: "/rugbyleague/rules", component: TeamSportRules },
  { path: "/rugbyleague/squad", component: Squad },
  { path: "/rugbyunion/category", component: TeamSportsCategory },
  { path: "/rugbyunion/tournaments", component: TeamSportsTournaments },
  { path: "/rugbyunion/team", component: TeamSportsTeam },
  { path: "/rugbyunion/player", component: TeamSportsPlayer },
  { path: "/rugbyunion/event", component: TeamSportsEvent },
  { path: "/rugbyunion/market", component: TeamSportsMarket },
  { path: "/rugbyunion/marketerror", component: MarketError },
  { path: "/rugbyunion/eventerror", component: EventError },
  { path: "/rugbyunion/dashboard", component: TeamSportDashboard },
  { path: "/rugbyunion/futurefixtureimport", component: TeamSportFutureImport },
  { path: "/rugbyunion/automation", component: TeamSportAutomation },
  { path: "/rugbyunion/Label", component: TeamSportLabel },
  { path: "/basketball/category", component: TeamSportsCategory },
  { path: "/basketball/tournaments", component: TeamSportsTournaments },
  { path: "/basketball/team", component: TeamSportsTeam },
  { path: "/basketball/player", component: TeamSportsPlayer },
  { path: "/basketball/event", component: TeamSportsEvent },
  { path: "/basketball/market", component: TeamSportsMarket },
  { path: "/basketball/marketerror", component: MarketError },
  { path: "/basketball/eventerror", component: EventError },
  { path: "/basketball/dashboard", component: TeamSportDashboard },
  { path: "/basketball/futurefixtureimport", component: TeamSportFutureImport },
  { path: "/basketball/matchup", component: TeamSportMatchup },
  { path: "/basketball/automation", component: TeamSportAutomation },
  { path: "/basketball/Label", component: TeamSportLabel },
  { path: "/afl/category", component: TeamSportsCategory },
  { path: "/afl/tournaments", component: TeamSportsTournaments },
  { path: "/afl/team", component: TeamSportsTeam },
  { path: "/afl/player", component: TeamSportsPlayer },
  { path: "/afl/event", component: TeamSportsEvent },
  { path: "/afl/market", component: TeamSportsMarket },
  { path: "/afl/marketerror", component: MarketError },
  { path: "/afl/eventerror", component: EventError },
  { path: "/afl/dashboard", component: TeamSportDashboard },
  { path: "/afl/futurefixtureimport", component: TeamSportFutureImport },
  { path: "/afl/matchup", component: TeamSportMatchup },
  { path: "/afl/automation", component: TeamSportAutomation },
  { path: "/afl/Label", component: TeamSportLabel },
  { path: "/australianrules/category", component: TeamSportsCategory },
  { path: "/australianrules/tournaments", component: TeamSportsTournaments },
  { path: "/australianrules/team", component: TeamSportsTeam },
  { path: "/australianrules/player", component: TeamSportsPlayer },
  { path: "/australianrules/event", component: TeamSportsEvent },
  { path: "/australianrules/event/:id", component: LineupEvent },
  { path: "/australianrules/market", component: TeamSportsMarket },
  { path: "/australianrules/marketerror", component: MarketError },
  { path: "/australianrules/eventerror", component: EventError },
  { path: "/australianrules/dashboard", component: TeamSportDashboard },
  {
    path: "/australianrules/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/australianrules/automation", component: TeamSportAutomation },
  { path: "/australianrules/Label", component: TeamSportLabel },
  {
    path: "/australianrules/seasons/:sportId/:tournamentId",
    component: Seasons,
  },
  { path: "/australianrules/rules", component: TeamSportRules },
  { path: "/australianrules/squad", component: Squad },
  { path: "/golf/category", component: TeamSportsCategory },
  { path: "/golf/tournaments", component: TeamSportsTournaments },
  { path: "/golf/team", component: TeamSportsTeam },
  { path: "/golf/player", component: TeamSportsPlayer },
  { path: "/golf/event", component: TeamSportsEvent },
  { path: "/golf/market", component: TeamSportsMarket },
  { path: "/golf/marketerror", component: MarketError },
  { path: "/golf/eventerror", component: EventError },
  { path: "/golf/dashboard", component: TeamSportDashboard },
  {
    path: "/golf/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/golf/automation", component: TeamSportAutomation },
  { path: "/golf/Label", component: TeamSportLabel },
  { path: "/tennis/category", component: TeamSportsCategory },
  { path: "/tennis/tournaments", component: TeamSportsTournaments },
  { path: "/tennis/team", component: TeamSportsTeam },
  { path: "/tennis/player", component: TeamSportsPlayer },
  { path: "/tennis/event", component: TeamSportsEvent },
  { path: "/tennis/market", component: TeamSportsMarket },
  { path: "/tennis/marketerror", component: MarketError },
  { path: "/tennis/eventerror", component: EventError },
  { path: "/tennis/dashboard", component: TeamSportDashboard },
  {
    path: "/tennis/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/tennis/automation", component: TeamSportAutomation },
  { path: "/tennis/Label", component: TeamSportLabel },
  { path: "/baseball/category", component: TeamSportsCategory },
  { path: "/baseball/tournaments", component: TeamSportsTournaments },
  { path: "/baseball/team", component: TeamSportsTeam },
  { path: "/baseball/player", component: TeamSportsPlayer },
  { path: "/baseball/event", component: TeamSportsEvent },
  { path: "/baseball/market", component: TeamSportsMarket },
  { path: "/baseball/marketerror", component: MarketError },
  { path: "/baseball/eventerror", component: EventError },
  { path: "/baseball/dashboard", component: TeamSportDashboard },
  {
    path: "/baseball/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/baseball/matchup", component: TeamSportMatchup },
  { path: "/baseball/automation", component: TeamSportAutomation },
  { path: "/baseball/Label", component: TeamSportLabel },
  { path: "/icehockey/category", component: TeamSportsCategory },
  { path: "/icehockey/tournaments", component: TeamSportsTournaments },
  { path: "/icehockey/team", component: TeamSportsTeam },
  { path: "/icehockey/player", component: TeamSportsPlayer },
  { path: "/icehockey/event", component: TeamSportsEvent },
  { path: "/icehockey/market", component: TeamSportsMarket },
  { path: "/icehockey/marketerror", component: MarketError },
  { path: "/icehockey/eventerror", component: EventError },
  { path: "/icehockey/dashboard", component: TeamSportDashboard },
  {
    path: "/icehockey/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/icehockey/matchup", component: TeamSportMatchup },
  { path: "/icehockey/automation", component: TeamSportAutomation },
  { path: "/icehockey/Label", component: TeamSportLabel },
  { path: "/boxing/category", component: TeamSportsCategory },
  { path: "/boxing/tournaments", component: TeamSportsTournaments },
  { path: "/boxing/team", component: TeamSportsTeam },
  { path: "/boxing/player", component: TeamSportsPlayer },
  { path: "/boxing/event", component: TeamSportsEvent },
  { path: "/boxing/market", component: TeamSportsMarket },
  { path: "/boxing/marketerror", component: MarketError },
  { path: "/boxing/eventerror", component: EventError },
  { path: "/boxing/dashboard", component: TeamSportDashboard },
  {
    path: "/boxing/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/boxing/automation", component: TeamSportAutomation },
  { path: "/boxing/Label", component: TeamSportLabel },
  { path: "/mma/category", component: TeamSportsCategory },
  { path: "/mma/tournaments", component: TeamSportsTournaments },
  { path: "/mma/team", component: TeamSportsTeam },
  { path: "/mma/player", component: TeamSportsPlayer },
  { path: "/mma/event", component: TeamSportsEvent },
  { path: "/mma/market", component: TeamSportsMarket },
  { path: "/mma/marketerror", component: MarketError },
  { path: "/mma/eventerror", component: EventError },
  { path: "/mma/dashboard", component: TeamSportDashboard },
  {
    path: "/mma/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/mma/automation", component: TeamSportAutomation },
  { path: "/mma/Label", component: TeamSportLabel },
  { path: "/soccer/category", component: TeamSportsCategory },
  { path: "/soccer/tournaments", component: TeamSportsTournaments },
  { path: "/soccer/team", component: TeamSportsTeam },
  { path: "/soccer/player", component: TeamSportsPlayer },
  { path: "/soccer/event", component: TeamSportsEvent },
  { path: "/soccer/event/:id", component: LineupEvent },
  { path: "/soccer/market", component: TeamSportsMarket },
  { path: "/soccer/marketerror", component: MarketError },
  { path: "/soccer/eventerror", component: EventError },
  { path: "/soccer/dashboard", component: TeamSportDashboard },
  {
    path: "/soccer/futurefixtureimport",
    component: TeamSportFutureImport,
  },
  { path: "/soccer/automation", component: TeamSportAutomation },
  { path: "/soccer/Label", component: TeamSportLabel },
  { path: "/soccer/seasons/:sportId/:tournamentId", component: Seasons },
  { path: "/soccer/rules", component: TeamSportRules },
  { path: "/soccer/squad", component: Squad },
  { path: "/soccer/sync-seasons", component: SyncSeasons },

  { path: "/automation", component: Automation },
  { path: "/sponsoredodds", component: SponsoredOdds },
  { path: "/advertisementmanagement", component: AdvertisementSection },
  { path: "/advertisingscreen", component: AdvertisingScreen },
  {
    path: "/mobile-advertisementmanagement",
    component: MobileAdvertisementSection,
  },
  { path: "/soc-broadcast", component: SOCBroadcast },
  { path: "/newsimport", component: NewsImport },
  { path: "/news/category", component: NewsCategory },
  { path: "/news/tag", component: NewsTag },
  { path: "/news/article", component: NewsArticle },
  { path: "/bookkeepercounter", component: BookkeeperCounter },
  { path: "/featuresports", component: FeatureSports },
  { path: "/featurerace", component: FeatureRace },
  { path: "/user-dashboard", component: UserDashboard },
  { path: "/subscriber-dashboard", component: SubscriberDashboard },
  { path: "/soc-smartPlay-dashboard", component: SOCSmartplayDashboard },
  { path: "/soc-smartPlay-dashboard-new", component: SOCSmartplayDashboardNew },
  { path: "/clients", component: ClientInfo },
  { path: "/mediagallery/:id", component: MediaGallery },
  { path: "/adcampaign", component: AdCampaign },
  { path: "/campaigninfo/:id", component: CampaignInfo },
  { path: "/sportplayermerge", component: SportPlayerMerge },
  { path: "/sponsors-logo", component: SponsorsLogo },
  { path: "/expert-tips", component: ExpertTips },
  { path: "/tips-of-the-day", component: TipsOfTheDay },
  { path: "/sport-expert-tips", component: SportExpertTips },
  { path: "/recommended-website", component: RecommendedWebsite },
  { path: "/backawinner/ourteam", component: OurTeams },
  { path: "/backawinner/ourteam-Category", component: OurTeamCategory },
  { path: "/backawinner/ourteam-Positions", component: OurTeamPositions },
  { path: "/backawinner/featured-race", component: BAWFeaturedRace },
  { path: "/backawinner/weekly-newsletter", component: WeeklyNewsLetter },
  { path: "/featured-calender", component: FeaturedCalender },
  { path: "/scraper-fixture-config", component: ScraperFixtureSync },
  { path: "/tippingcomp-tips", component: CompTipsPage },
  { path: "/subscription-active", component: SubscriptionActive },
  { path: "/subscription-dashboard", component: SubscriptionDashboard },
  { path: "/tipping-FAQs", component: TippingFAQs },
  { path: "/tipping-prize", component: TippingPrize },
  { path: "/tipping-list", component: TippingList },
  {
    path: "/tipping-list/:sportID/:id/:compName",
    component: TippingTipsterList,
  },
  { path: "/premium-tipping-comps", component: PremiumTippingComps },
  { path: "/subscriptionCoupon", component: SubCoupon },
  { path: "/testimonial", component: Testimonial },
  { path: "/contact-us", component: SocContactUs },
  { path: "/fantasy-coins", component: FantasyCoins },
  { path: "/fantasy-withdraw", component: FantasyWithdraw },
  { path: "/fantasy-kyc", component: FantasyKYC },
  { path: "/fantasy-user-coins", component: FantasyUserCoins },
  { path: "*", component: NotFound },
];

export const adminRoutes = adminRoutesArray?.map((item) => {
  return { ...item, exact: true, adminroute: true };
});
