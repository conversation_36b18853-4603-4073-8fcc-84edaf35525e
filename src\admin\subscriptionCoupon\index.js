import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import DateFnsUtils from "@date-io/date-fns";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select from "react-select";
import moment from "moment-timezone";
import "./subscriptionCoupon.scss";
import _ from "lodash";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const disCountOption = [
  {
    label: "Yes",
    value: true,
  },
  {
    label: "No",
    value: false,
  },
];

const statusOption = [
  {
    label: "active",
    value: "active",
  },
  {
    label: "deleted",
    value: "deleted",
  },
];

const categoryOption = [
  {
    label: "Fantasy",
    value: "fantasy",
  },
  {
    label: "SOC",
    value: "soc",
  },
];

class SubCoupon extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      couponValues: {
        code: "",
        name: "",
        description: "",
        uses: "",
        totalUses: "",
        isPercentage: false,
        discountAmount: "",
        startAt: moment().tz(timezone).format("YYYY-MM-DD"),
        expireAt: null,
        minPurchased: "",
        maxDiscount: "",
        status: "active",
        numberOfMonth: "",
        extraFreeDays: "",
        category: "soc",
      },
      TippingFAQList: [],
      TippingFAQCount: 0,
      errorCode: "",
      errorName: "",
      errorDiscountAmount: "",
      errorExtraDays: "",
      errorStartAt: "",
      errorMinPurchased: "",
      errorMaxDiscount: "",
      errorNumberOfMonth: "",
      isSearch: "",
      selectedTippingFAQID: "",
    };
  }

  componentDidMount() {
    this.fetchTippingFAQs(0, "");
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset, isSearch } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchTippingFAQs(offset, isSearch);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchTippingFAQs(offset, "");
      this.setState({
        offset: 0,
        currentPage: 1,
        isSearch: "",
      });
    }
  }

  async fetchTippingFAQs(page, search) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/subscription/couponcodes?limit=${rowPerPage}&offset=${page}&search=${search}`
      );
      if (status === 200) {
        this.setState({
          TippingFAQList: data?.data,
          isLoading: false,
          TippingFAQCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { couponValues } = this.state;
    let flag = true;
    if (couponValues?.code?.trim() === "") {
      flag = false;
      this.setState({
        errorCode: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCode: "",
      });
    }

    if (couponValues?.name?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }

    if (!couponValues?.discountAmount && !couponValues?.extraFreeDays) {
      flag = false;
      this.setState({
        errorDiscountAmount: "This field is mandatory",
      });
    } else if (couponValues?.isPercentage === true) {
      if (
        couponValues?.discountAmount &&
        Number(couponValues?.discountAmount) > 100
      ) {
        flag = false;
        this.setState({
          errorDiscountAmount:
            "Please enter discount amount less than or equal to 100",
        });
      } else {
        this.setState({
          errorDiscountAmount: "",
        });
      }
    } else {
      this.setState({
        errorDiscountAmount: "",
      });
    }
    // if (couponValues?.minPurchased?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorMinPurchased: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorMinPurchased: "",
    //   });
    // }
    // if (couponValues?.maxDiscount?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorMaxDiscount: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorMaxDiscount: "",
    //   });
    // }

    if (couponValues?.startAt === "" || couponValues?.startAt === null) {
      flag = false;
      this.setState({
        errorStartAt: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartAt: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      const { couponValues, offset, isSearch } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        code: couponValues?.code?.trim(),
        name: couponValues?.name?.trim(),
        description: couponValues?.description?.trim(),
        uses: couponValues?.uses ? couponValues?.uses : 1,
        totalUses: couponValues?.totalUses,
        discountAmount: couponValues?.discountAmount,
        months: couponValues?.numberOfMonth
          ? couponValues?.numberOfMonth
          : null,
        extraDays: couponValues?.extraFreeDays
          ? couponValues?.extraFreeDays
          : null,
        isPercentage: couponValues?.isPercentage,
        startAt: couponValues?.startAt
          ? moment(couponValues?.startAt).tz(timezone).format("YYYY-MM-DD")
          : null,
        expireAt: couponValues?.expireAt
          ? moment(couponValues?.expireAt).tz(timezone).format("YYYY-MM-DD")
          : null,
        minPurchased: couponValues?.minPurchased,
        maxDiscount: couponValues?.maxDiscount,
        status: couponValues?.status,
        category: couponValues?.category,
      };

      try {
        const { status, data } = await axiosInstance.post(
          `/subscription/couponcodes`,
          payload
        );
        if (status === 200 || status === 201) {
          this.setState({ isLoading: false });
          this.toggleInputModal();
          this.fetchTippingFAQs(offset, isSearch);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false });
          this.toggleInputModal();
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false });
        this.toggleInputModal();
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const { couponValues, selectedTippingFAQID, offset, isSearch } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        code: couponValues?.code?.trim(),
        name: couponValues?.name?.trim(),
        description: couponValues?.description?.trim(),
        uses: couponValues?.uses ? couponValues?.uses : 1,
        totalUses: couponValues?.totalUses,
        discountAmount: couponValues?.discountAmount,
        months: couponValues?.numberOfMonth
          ? couponValues?.numberOfMonth
          : null,
        extraDays: couponValues?.extraFreeDays
          ? couponValues?.extraFreeDays
          : null,
        isPercentage: couponValues?.isPercentage,
        startAt: couponValues?.startAt
          ? moment(couponValues?.startAt).tz(timezone).format("YYYY-MM-DD")
          : null,
        expireAt: couponValues?.expireAt
          ? moment(couponValues?.expireAt).tz(timezone).format("YYYY-MM-DD")
          : null,
        minPurchased: couponValues?.minPurchased,
        maxDiscount: couponValues?.maxDiscount,
        status: couponValues?.status,
        category: couponValues?.category,
      };

      try {
        const { status, data } = await axiosInstance.put(
          `/subscription/couponcodes/${selectedTippingFAQID}`,
          payload
        );
        if (status === 200 || status === 201) {
          this.setState({ isLoading: false });
          this.toggleInputModal();
          this.fetchTippingFAQs(offset, isSearch);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false });
          this.toggleInputModal();
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false });
        this.toggleInputModal();
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorCode: "",
      errorName: "",
      errorDiscountAmount: "",
      errorExtraDays: "",
      errorStartAt: "",
      errorMinPurchased: "",
      errorMaxDiscount: "",
      errorNumberOfMonth: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        couponValues: {
          name: item?.name,
          code: item?.code,
          description: item?.description,
          uses: item?.uses,
          totalUses: item?.totalUses,
          isPercentage: item?.isPercentage,
          discountAmount: item?.discountAmount,
          numberOfMonth: item?.months,
          extraFreeDays: item?.extraDays,
          startAt: item?.startAt,
          expireAt: item?.expireAt,
          minPurchased: item?.minPurchased,
          maxDiscount: item?.maxDiscount,
          status: item?.status,
          category: item?.category,
        },
        isEditMode: true,
        selectedTippingFAQID: item?.id,
      });
    } else {
      this.setState({
        couponValues: {
          code: "",
          name: "",
          description: "",
          uses: "",
          totalUses: "",
          isPercentage: false,
          discountAmount: "",
          numberOfMonth: "",
          extraFreeDays: "",
          startAt: moment().tz(timezone).format("YYYY-MM-DD"),
          expireAt: null,
          minPurchased: "",
          maxDiscount: "",
          status: "active",
          category: "soc",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { itemToDelete, offset, isSearch } = this.state;

    try {
      const { status, data } = await axiosInstance.delete(
        `/subscription/couponcodes/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchTippingFAQs(offset, isSearch);
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, TippingFAQList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handleClearClick = () => {
    this.setState({ isSearch: "" });
    this.fetchTippingFAQs(this.state?.offset, "");
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      couponValues,
      TippingFAQList,
      TippingFAQCount,
      errorCode,
      errorName,
      errorDiscountAmount,
      errorExtraDays,
      errorStartAt,
      errorMinPurchased,
      errorMaxDiscount,
      errorNumberOfMonth,
      isSearch,
    } = this.state;
    const pageNumbers = [];

    if (TippingFAQCount > 0) {
      for (let i = 1; i <= Math.ceil(TippingFAQCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  Subscription Coupon
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Typography variant="h1" align="left">
                  Subscription Coupon
                </Typography>
              </Grid>

              <Grid
                item
                xs={7}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => this.fetchTippingFAQs(offset, isSearch)}
                >
                  Search
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TippingFAQList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TippingFAQList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table
                  className="listTable"
                  aria-label="simple table"
                  style={{ minWidth: "max-content" }}
                >
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell style={{ cursor: "pointer" }}>ID</TableCell>
                      <TableCell>Code</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell style={{ width: "400px" }}>
                        Description
                      </TableCell>
                      <TableCell>Uses</TableCell>
                      <TableCell>Total Uses</TableCell>
                      <TableCell>isPercentage</TableCell>
                      <TableCell>Discount Amount</TableCell>
                      <TableCell>Extra Months</TableCell>
                      <TableCell>Extra Free Days</TableCell>
                      <TableCell>Start Date</TableCell>
                      <TableCell>Expiry Date</TableCell>
                      <TableCell>Min. Purchased</TableCell>
                      <TableCell>Max. Discount</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {TippingFAQList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id ?? ""} </TableCell>
                          <TableCell>{item?.code ?? ""}</TableCell>
                          <TableCell>{item?.name ?? ""}</TableCell>
                          <TableCell>{item?.description ?? ""}</TableCell>
                          <TableCell>{item?.uses ?? ""}</TableCell>
                          <TableCell>{item?.totalUses ?? ""}</TableCell>
                          <TableCell>
                            {item?.isPercentage && item?.isPercentage === true
                              ? "Yes"
                              : "No"}
                          </TableCell>
                          <TableCell>{item?.discountAmount ?? ""}</TableCell>
                          <TableCell>{item?.months ?? ""}</TableCell>
                          <TableCell>{item?.extraDays ?? ""}</TableCell>
                          <TableCell>
                            {item?.startAt
                              ? moment(item?.startAt)
                                  .tz(timezone)
                                  .format("DD-MM-YYYY")
                              : ""}
                          </TableCell>
                          <TableCell>
                            {item?.expireAt
                              ? moment(item?.expireAt)
                                  .tz(timezone)
                                  .format("DD-MM-YYYY")
                              : ""}
                          </TableCell>
                          <TableCell>{item?.minPurchased ?? ""}</TableCell>
                          <TableCell>{item?.maxDiscount ?? ""}</TableCell>
                          <TableCell>{item?.status}</TableCell>
                          <TableCell>{item?.category}</TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              TippingFAQCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input coupon-modal"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Coupon" : "Edit Coupon"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Coupon Code</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Coupon Code"
                          value={couponValues?.code}
                          onKeyDown={(e) => {
                            if (e.key === " ") {
                              e.preventDefault();
                            }
                          }}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                code: e?.target?.value,
                              },
                              errorCode: e?.target?.value ? "" : errorCode,
                            })
                          }
                        />
                        {errorCode ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorCode}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Coupon Name</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Coupon Name"
                          value={couponValues?.name}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                name: e?.target?.value,
                              },
                              errorName: e?.target?.value ? "" : errorName,
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">
                          Coupon Description
                        </label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield full-width"
                          variant="outlined"
                          type="textarea"
                          multiline
                          maxRows={3}
                          color="primary"
                          size="small"
                          placeholder="Coupon Description"
                          value={couponValues?.description}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                description: e?.target?.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Coupon Uses</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Coupon Uses"
                          value={couponValues?.uses}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                uses: e?.target?.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Total Uses</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Total Uses"
                          value={couponValues?.totalUses}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                totalUses: e?.target?.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Status</label>
                        <Select
                          className="React cricket-select sponsored-select-modal coupon-select-modal"
                          classNamePrefix="select"
                          placeholder="Status"
                          menuPosition="fixed"
                          value={statusOption?.find((item) => {
                            return item?.value == couponValues?.status;
                          })}
                          // isLoading={isLoading}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                status: e?.value,
                              },
                            })
                          }
                          options={statusOption}
                        />
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">
                          Discount in Percentage ?
                        </label>
                        <Select
                          className="React cricket-select sponsored-select-modal coupon-select-modal"
                          classNamePrefix="select"
                          placeholder="Discount Percentage"
                          menuPosition="fixed"
                          value={disCountOption?.find((item) => {
                            return item?.value == couponValues?.isPercentage;
                          })}
                          // isLoading={isLoading}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                isPercentage: e?.value,
                              },
                            })
                          }
                          options={disCountOption}
                        />
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Discount Amount</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Discount Amount"
                          value={couponValues?.discountAmount || ""}
                          onChange={(e) => {
                            const value = Number(e.target.value);

                            if (value >= 1 || e.target.value === "") {
                              this.setState({
                                couponValues: {
                                  ...couponValues,
                                  discountAmount: e.target.value,
                                },
                                errorDiscountAmount: "",
                              });
                            } else {
                              this.setState({
                                errorDiscountAmount:
                                  "Discount amount must be at least 1",
                              });
                            }
                          }}
                          inputProps={{ min: 1 }}
                        />
                        {errorDiscountAmount && (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0", color: "red" }}
                          >
                            {errorDiscountAmount}
                          </p>
                        )}
                      </Grid>

                      {/* <Grid
                      item
                      xs={6}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        marginBottom: "8px",
                      }}
                    >
                      <label className="modal-label">Extra Free Days</label>
                      <TextField
                        className="teamsport-textfield rec coupon-textfield"
                        variant="outlined"
                        type="number"
                        color="primary"
                        size="small"
                        placeholder="Extra Free Days"
                        value={couponValues?.extraFreeDays}
                        onChange={(e) =>
                          this.setState({
                            couponValues: {
                              ...couponValues,
                              extraFreeDays: e?.target?.value,
                            },
                          })
                        }
                      />
                    </Grid> */}
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Extra Free Days</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Extra Free Days"
                          value={couponValues?.extraFreeDays || ""}
                          onChange={(e) => {
                            const value = Number(e.target.value);

                            if (value >= 1 || e.target.value === "") {
                              this.setState({
                                couponValues: {
                                  ...couponValues,
                                  extraFreeDays: e.target.value,
                                },
                                errorExtraDays: "",
                              });
                            } else {
                              this.setState({
                                errorExtraDays: "Extra Days must be at least 1",
                              });
                            }
                          }}
                          inputProps={{ min: 1 }}
                        />
                        {errorExtraDays && (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0", color: "red" }}
                          >
                            {errorExtraDays}
                          </p>
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Start Date</label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            disablePast={!isEditMode ? true : false}
                            variant="inline"
                            format="yyyy/MM/dd"
                            placeholder="Start Date"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              couponValues?.startAt
                                ? typeof couponValues?.startAt === "string"
                                  ? parseISO(
                                      moment(couponValues?.startAt)
                                        .tz(timezone)
                                        .format("YYYY-MM-DD")
                                    )
                                  : couponValues?.startAt
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                couponValues: {
                                  ...couponValues,
                                  startAt: e,
                                  expireAt: null,
                                },
                                errorStartAt: e ? "" : errorStartAt,
                              })
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal coupon-date-picker "
                          />
                        </LocalizationProvider>
                        {errorStartAt ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorStartAt}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Expiry Date</label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            // disableToolbar
                            variant="inline"
                            format="yyyy/MM/dd"
                            placeholder="End Date"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              couponValues?.expireAt
                                ? typeof couponValues?.expireAt === "string"
                                  ? parseISO(
                                      moment(couponValues?.expireAt)
                                        .tz(timezone)
                                        .format("YYYY-MM-DD")
                                    )
                                  : couponValues?.expireAt
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                couponValues: {
                                  ...couponValues,
                                  expireAt: e,
                                },
                              })
                            }
                            minDate={
                              couponValues?.startAt
                                ? typeof couponValues?.startAt === "string"
                                  ? parseISO(
                                      moment(couponValues?.startAt)
                                        .tz(timezone)
                                        .format("YYYY-MM-DD")
                                    )
                                  : couponValues?.startAt
                                : null
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal coupon-date-picker"
                          />
                        </LocalizationProvider>
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Minimum Purchase</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Minimum Purchase"
                          value={couponValues?.minPurchased}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value === "" || Number(value) >= 0) {
                              this.setState({
                                couponValues: {
                                  ...couponValues,
                                  minPurchased: value,
                                },
                                errorMinPurchased: "",
                              });
                            } else {
                              this.setState({
                                errorMinPurchased:
                                  "Minimum purchase cannot be a negative value.",
                              });
                            }
                          }}
                          inputProps={{ min: 0 }}
                        />
                        {errorMinPurchased ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorMinPurchased}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Maximum Discount</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Maximum Discount"
                          value={couponValues?.maxDiscount}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value === "" || Number(value) >= 0) {
                              this.setState({
                                couponValues: {
                                  ...couponValues,
                                  maxDiscount: value,
                                },
                                errorMaxDiscount: "",
                              });
                            } else {
                              this.setState({
                                errorMaxDiscount:
                                  "Maximum purchase cannot be a negative value.",
                              });
                            }
                          }}
                          inputProps={{ min: 0 }}
                        />
                        {errorMaxDiscount ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorMaxDiscount}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Number of Months</label>
                        <TextField
                          className="teamsport-textfield rec coupon-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Number of Months"
                          value={couponValues?.numberOfMonth}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value === "" || Number(value) >= 0) {
                              this.setState({
                                couponValues: {
                                  ...couponValues,
                                  numberOfMonth: value,
                                },
                                errorNumberOfMonth: "",
                              });
                            } else {
                              this.setState({
                                errorNumberOfMonth:
                                  "Number Of Month cannot be a negative value.",
                              });
                            }
                          }}
                          inputProps={{ min: 0 }}
                        />
                        {errorNumberOfMonth ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorNumberOfMonth}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Category</label>
                        <Select
                          className="React cricket-select sponsored-select-modal coupon-select-modal"
                          classNamePrefix="select"
                          placeholder="Status"
                          menuPosition="fixed"
                          value={categoryOption?.find((item) => {
                            return item?.value === couponValues?.category;
                          })}
                          // isLoading={isLoading}
                          onChange={(e) =>
                            this.setState({
                              couponValues: {
                                ...couponValues,
                                category: e?.value,
                              },
                            })
                          }
                          options={categoryOption}
                        />
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default SubCoupon;
