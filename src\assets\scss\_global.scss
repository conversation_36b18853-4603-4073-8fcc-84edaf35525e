@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap");

@font-face {
  font-family: "VeneerClean-Soft";
  src: url("../fonts/VeneerClean-Soft.eot");
  /* IE9*/
  src: url("../fonts/VeneerClean-Soft.eot?#iefix") format("embedded-opentype"),
    /* IE6-IE8 */
    url("../fonts/VeneerClean-Soft.woff2") format("woff2"),
    /* chrome、firefox */
    url("../fonts/VeneerClean-Soft.woff") format("woff"),
    /* chrome、firefox */
    url("../fonts/VeneerClean-Soft.ttf") format("truetype"),
    /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
    url("../fonts/VeneerClean-Soft.svg#VeneerClean-Soft") format("svg");
  /* iOS 4.1- */
}

@font-face {
  font-family: "Apotek-Comp-Regular";
  src: url("../fonts/Apotek_Comp_Regular.otf") format("truetype");
  font-display: swap;
}

body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;

  span,
  h4,
  h5,
  h6,
  a,
  p,
  li {
    font-family: $regulerFont !important;
  }

  h1,
  h2,
  h3 {
    font-family: $primaryFont;
  }

  a {
    text-decoration: none;
  }

  .getty-img-container {
    iframe {
      position: absolute !important;
    }
  }

  iframe {
    position: inherit !important;
    display: none;
  }

  .MuiCheckbox-colorSecondary {
    background: transparent !important;
  }

  /* ===== Scrollbar CSS ===== */
  /* Firefox */
  // * {
  //   scrollbar-width: 4px;
  //   scrollbar-color: #4455c7 #e7e9ec;
  // }

  /* Chrome, Edge, and Safari */
  ::-webkit-scrollbar {
    border-radius: 10px;
    height: 4px;
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: #e7e9ec;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #4455c7;
    border-radius: 10px;
    border: 0px solid #ffffff;
  }
}

.admin-filter-wrap {
  display: flex;
  column-gap: 15px;
  justify-content: right;
}

img {
  max-width: 100%;
}

.MuiContainer-maxWidthLg {
  max-width: 1404px !important;
}

.menu_bar {
  padding: 10px;
  background: $success;
}

body a {
  text-decoration: none;
}

body .color-orange {
  color: #ff6b00;
}

.bg-green {
  background: #ffffff !important;
}

.bg-light-green {
  background: #6abf4b !important;
}

.MuiButton-contained {
  box-shadow: none !important;
}

.mr-lr-30.back-btn-modal {
  background-color: #e0e0e0;
}

.btn.btn-success {
  background: $success;
  border-color: $success;
}

.btn.btn-warning {
  background: $warning;
}

.btn.btn-purplue {
  background: $purplue;
}

.btn.btn-default {
  background: #656565;
}

.btn.bg-transparent {
  background: transparent;
}

.bg-alert {
  background: #ff6b00;
}

.text-upper {
  text-transform: uppercase;
}

.MuiPaper-root.color-white,
.MuiButton-contained.color-white,
.color-white {
  color: #fff;
}

.color-danger,
.color-error {
  color: #c10016;
}

.error.color-danger {
  color: #b33a3a;
}

.color-green {
  color: #00833e;
}

.color-light-green {
  color: #6abf4b;
}

.color-yellow {
  color: #ffa400;
}

.color-blue {
  color: #17276b;
}

.color-fill-green,
.color-fill-green svg,
.color-fill-green svg * {
  color: #00833e;
  fill: #00833e;
}

.color-fill-light-green,
.color-fill-light-green svg,
.color-fill-light-green svg * {
  color: #6abf4b;
  fill: #6abf4b;
}

.color-fill-orange,
.color-fill-orange svg,
.color-fill-orange svg * {
  color: #ff6b00;
  fill: #ff6b00;
}

.color-fill-yellow,
.color-fill-yellow svg,
.color-fill-yellow svg * {
  color: #ffa400;
  fill: #ffa400;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.modal-head {
  font-size: 22.4px;
  font-family: "Inter" !important;
  font-weight: 600;
}

.text-right {
  text-align: right !important;

  @media (max-width: 1279px) {
    &.md-text-left {
      text-align: left;
    }
  }
}

.mt-30 {
  margin-top: 30px;
}

.mt-22 {
  margin-top: 22px !important;
}

.mb-30 {
  margin-bottom: 30px;
}

.mr-30 {
  margin-right: 30px;
}

.ml-10 {
  margin-left: 10px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.modal-padding {
  padding: 20px 20px;
}

.flex {
  display: flex;
  align-items: center;
}

.font-size125 {
  font-size: 14px !important;
  line-height: 16px !important;
}

.text-upper-user {
  text-transform: uppercase;
  font-weight: 600 !important;
  font-size: 16px !important;
  line-height: 18px !important;
}

.rm-padding-i {
  padding: 0 !important;
}

ul.horizontal-list {
  display: flex;
  flex-direction: row;
  flex-flow: wrap;
  padding: 0;

  li {
    width: auto;
    margin-right: 10px;
  }

  &.with-rightspace li {
    margin-right: 25px;
  }
}

.modal.modal-input>.paper,
.paper.modal-show-scroll {
  overflow-y: auto;
  max-height: 100%;
  border-radius: 8px;
}

.news-modal {
  max-width: 1080px;
  margin: 0px auto;
  position: fixed;
  z-index: 1300;
  inset: 0px;
  top: 5% !important;
  bottom: 5% !important;
}
.pre-whitespace {
  white-space: pre;
}

.color-picker-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 18px;

  .color-picker-box {
    width: 50%;

    h6 {
      margin: 0px 0px 12px;
    }

    .chrome-picker {
      width: 100% !important;
    }
  }
}

.sync-fixture-textarea {
  .MuiInputBase-multiline {
    padding: 10.5px !important;
  }
}