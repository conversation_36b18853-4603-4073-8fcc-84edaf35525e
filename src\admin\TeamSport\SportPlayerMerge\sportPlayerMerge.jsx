import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../teamsport.scss";

const nationalData = [
  {
    label: "Yes",
    value: true,
  },
  {
    label: "No",
    value: false,
  },
];
class SportPlayerMerge extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,

      LabelList: [],
      LabelCount: 0,
      errorParentSport: "",
      errorChildSport: "",
      errorParentPlayer: "",
      errorChildPlayer: "",
      flag: [],
      uploadLogo: "",
      createError: "",
      isVariationModalOpen: false,
      search: "",
      displayErrorName: "",
      sportsOption: [],
      parentSport: null,
      childSport: null,
      parentPlayerCount: 0,
      parentPlayerPage: 0,
      isParentPlayerSearch: "",
      searchParentPlayerCount: 0,
      searchParentPlayerPage: 0,
      searchParentPlayerData: [],
      parentPlayer: null,
      parentPlayerData: [],
      childPlayerCount: 0,
      childPlayerPage: 0,
      isChildPlayerSearch: "",
      searchChildPlayerCount: 0,
      searchChildPlayerPage: 0,
      searchChildPlayerData: [],
      childPlayer: null,
      childPlayerData: [],
      isParentPlayerLoading: false,
      isChildPlayerLoading: false,
      mergeId: null,
    };
  }

  componentDidMount() {
    this.fetchAllList(0, "");
    // this.fetchAllCountry(0);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllList(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllList(0, "");
      this.setState({
        offset: 0,
        selectCategory: "",
        currentPage: 1,
        search: "",
      });
    }
  }

  fetchAllList = async (page, searchvalue) => {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = `player/sport/relation?limit=20&offset=${page}&search=${searchvalue}`;

      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          LabelList: data?.result,
          isLoading: false,
          LabelCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };
  fetchAllSports = async () => {
    const { status, data } = await axiosInstance.get(
      URLS.sports + `?sportTypeId=2`
    );
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });

      // let pushData = newdata.push(alldatas);
      // let sortData = newdata?.sort((a, b) => {
      //   return a.value > b.value ? 1 : -1;
      // });
      this.setState({
        sportsOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
      });
    }
  };
  fetchParentPlayer = async (Page, sportId) => {
    this.setState({ isParentPlayerLoading: true });
    const passApi = `allsport/player?SportId=${sportId}&limit=20&offset=${Page}`;
    try {
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let players = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(this.state?.parentPlayerData, newdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });

        this.setState({
          parentPlayerData: finalData,
          parentPlayerCount: Math.ceil(count),
          isParentPlayerLoading: false,
        });
      } else {
        this.setState({ isParentPlayerLoading: false });
      }
    } catch (err) {
      this.setState({ isParentPlayerLoading: false });
    }
  };
  fetchSelectedParentSport = (item, SportId) => {
    let seletedParentSport = [
      {
        label: item?.sportName,
        value: SportId,
      },
    ];
    this.setState({
      parentPlayerData: SportId
        ? _.uniqBy(
            [...seletedParentSport, ...this.state.sportsOption],
            function (e) {
              return e.value;
            }
          )
        : this.state.sportsOption,
      parentSport: SportId,
    });
  };
  fetchSelectedParentPlayer = (item, playerId) => {
    let seletedParentPlayer = [
      {
        label: item?.playerName,
        value: playerId,
      },
    ];
    this.setState({
      parentPlayerData: playerId
        ? _.uniqBy(
            [...seletedParentPlayer, ...this.state.parentPlayerData],
            function (e) {
              return e.value;
            }
          )
        : this.state.parentPlayerData,
      parentPlayer: playerId,
    });
  };
  fetchSelectedChildSport = (item, SportId) => {
    let seletedChildSport = [
      {
        label: item?.toSportName,
        value: SportId,
      },
    ];
    this.setState({
      childPlayerData: SportId
        ? _.uniqBy(
            [...seletedChildSport, ...this.state.sportsOption],
            function (e) {
              return e.value;
            }
          )
        : this.state.sportsOption,
      childSport: SportId,
    });
  };
  fetchSelectedChildPlayer = (item, playerId) => {
    let seletedChildPlayer = [
      {
        label: item?.toplayerName,
        value: playerId,
      },
    ];
    this.setState({
      childPlayerData: playerId
        ? _.uniqBy(
            [...seletedChildPlayer, ...this.state.childPlayerData],
            function (e) {
              return e.value;
            }
          )
        : this.state.childPlayerData,
      childPlayer: playerId,
    });
  };

  handleOnScrollBottomParentPlayer = (e, type) => {
    let {
      parentPlayerCount,
      parentPlayerPage,
      isParentPlayerSearch,
      searchParentPlayerCount,
      searchParentPlayerPage,
      parentSport,
    } = this.state;
    if (
      isParentPlayerSearch !== "" &&
      searchParentPlayerCount !== Math.ceil(searchParentPlayerPage / 20 + 1)
    ) {
      this.handleParentPlayerInputChange(
        searchParentPlayerPage + 20,
        isParentPlayerSearch
      );
      this.setState({
        searchParentPlayerPage: searchParentPlayerPage + 20,
      });
    } else {
      if (
        parentPlayerCount !== Math.ceil(parentPlayerPage / 20) &&
        isParentPlayerSearch == ""
      ) {
        this.fetchParentPlayer(parentPlayerPage + 20, parentSport);
        this.setState({
          parentPlayerPage: parentPlayerPage + 20,
        });
      }
    }
  };
  handleParentPlayerInputChange = (parentPlayerPage, value) => {
    const passApi = `allsport/player?SportId=${this.state.parentSport}&limit=20&offset=${parentPlayerPage}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(this.state?.searchParentPlayerData, newdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });

        this.setState({
          searchParentPlayerData: finalData,
          searchParentPlayerCount: Math.ceil(count),
          isParentPlayerSearch: value,
        });
      }
    });
  };

  fetchChildPlayer = async (Page, sportId) => {
    this.setState({ isChildPlayerLoading: true });
    const passApi = `allsport/player?SportId=${sportId}&limit=20&offset=${Page}`;
    try {
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let players = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(this.state?.childPlayerData, newdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          childPlayerData: finalData,
          childPlayerCount: Math.ceil(count),
          isChildPlayerLoading: false,
        });
      } else {
        this.setState({ isChildPlayerLoading: false });
      }
    } catch (err) {
      this.setState({ isChildPlayerLoading: false });
    }
  };

  handleOnScrollBottomChildPlayer = (e, type) => {
    let {
      childPlayerCount,
      childPlayerPage,
      isChildPlayerSearch,
      searchChildPlayerCount,
      searchChildPlayerPage,
      childSport,
    } = this.state;
    if (
      isChildPlayerSearch !== "" &&
      searchChildPlayerCount !== Math.ceil(searchChildPlayerPage / 20 + 1)
    ) {
      this.handleChildPlayerInputChange(
        searchChildPlayerPage + 20,
        isChildPlayerSearch
      );
      this.setState({
        searchChildPlayerPage: searchChildPlayerPage + 20,
      });
    } else {
      if (
        childPlayerCount !== Math.ceil(childPlayerPage / 20) &&
        isChildPlayerSearch == ""
      ) {
        this.fetchChildPlayer(childPlayerPage + 20, childSport);
        this.setState({
          childPlayerPage: childPlayerPage + 20,
        });
      }
    }
  };
  handleChildPlayerInputChange = (childPlayerPage, value) => {
    const passApi = `allsport/player?SportId=${this.state.childSport}&limit=20&offset=${childPlayerPage}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let mergeData = _.unionBy(this.state?.searchChildPlayerData, newdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchChildPlayerData: finalData,
          searchChildPlayerCount: Math.ceil(count),
          isChildPlayerSearch: value,
        });
      }
    });
  };

  handalValidate = () => {
    let { parentSport, parentPlayer, childSport, childPlayer } = this.state;

    let flag = true;
    if (!parentSport) {
      flag = false;
      this.setState({
        errorParentSport: "This field is mandatory",
      });
    } else {
      this.setState({
        errorParentSport: "",
      });
    }
    if (!parentPlayer) {
      flag = false;
      this.setState({
        errorParentPlayer: "This field is mandatory",
      });
    } else {
      this.setState({
        errorParentPlayer: "",
      });
    }
    if (!childSport) {
      flag = false;
      this.setState({
        errorChildSport: "This field is mandatory",
      });
    } else {
      this.setState({
        errorChildSport: "",
      });
    }
    if (!childPlayer) {
      flag = false;
      this.setState({
        errorChildPlayer: "This field is mandatory",
      });
    } else {
      this.setState({
        errorChildPlayer: "",
      });
    }

    return flag;
  };
  handleSave = async () => {
    const { parentSport, parentPlayer, childSport, childPlayer } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });

      let payload = {
        SportId: parentSport,
        PlayerId: parentPlayer,
        toSportId: childSport,
        toPlayerId: childPlayer,
      };
      let passApi = `player/sport/relation`;
      try {
        const { status, data } = await axiosInstance.post(passApi, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.fetchAllList(this.state.offset, this.state?.search);
          this.setActionMessage(
            true,
            "Success",
            data?.status ? "Sport Player Relation Successfully..." : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };
  handleUpdate = async () => {
    const { parentSport, parentPlayer, childSport, childPlayer, mergeId } =
      this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });

      let payload = {
        SportId: parentSport,
        PlayerId: parentPlayer,
        toSportId: childSport,
        toPlayerId: childPlayer,
      };

      let passApi = `player/sport/relation/${mergeId}`;
      try {
        const { status, data } = await axiosInstance.put(passApi, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.fetchAllList(this.state.offset, this.state?.search);
          this.setActionMessage(
            true,
            "Success",
            data?.status ? "Sport Player Relation Updated Successfully..." : ""
          );
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      parentSport: null,
      childSport: null,
      parentPlayer: null,
      childPlayer: null,
      errorParentSport: "",
      errorChildSport: "",
      errorParentPlayer: "",
      errorChildPlayer: "",
      createError: "",
      parentPlayerPage: 0,
      childPlayerPage: 0,
      parentPlayerData: [],
      searchParentPlayerData: [],
      childPlayerData: [],
      searchChildPlayerData: [],
      mergeId: null,
    });
  };

  inputModal = (item, type) => () => {
    this.fetchAllSports();
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      // this.fetchSelectedCountry(item?.CountryId, item?.Country?.country);
      this.fetchParentPlayer(0, item?.SportId);
      this.fetchChildPlayer(0, item?.toSportId);
      this.fetchSelectedParentSport(item, item?.SportId);
      this.fetchSelectedParentPlayer(item, item?.PlayerId);
      this.fetchSelectedChildSport(item, item?.toSportId);
      this.fetchSelectedChildPlayer(item, item?.toPlayerId);
      this.setState({
        isEditMode: true,
        mergeId: item?.id,
      });
    } else {
      this.setState({
        parentSport: null,
        childSport: null,
        parentPlayer: null,
        childPlayer: null,
        isEditMode: false,
        parentPlayerData: [],
        searchParentPlayerData: [],
        childPlayerData: [],
        searchChildPlayerData: [],
        mergeId: null,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const passApi = `player/sport/relation/${this.state.itemToDelete}`;
      const { data, status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllList(this.state.offset, this.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          data?.status ? "Sport Player Relation Delete Successfully..." : ""
        );
      } else {
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  // handleFileUpload = (name, files) => {
  //   this.setState({ [name]: files });
  // };
  // setMedia = async (files) => {
  //   const formData = new FormData();
  //   formData.append("image", files ? files : null);
  //   if (files !== undefined) {
  //     const { status, data } = await axiosInstance.post(URLS.media, formData, {
  //       header: { "Content-Type": "multipart/form-data" },
  //     });
  //     if (status === 200) {
  //       return data;
  //     } else {
  //       return false;
  //     }
  //   }
  //   return false;
  // };

  inputVariationModal = (item) => {
    this.setState({
      isVariationModalOpen: true,
      labelValues: {
        labelName: item?.name,
        id: item?.id,
      },
    });
  };
  toggleVariationModal = () => {
    this.setState({
      isVariationModalOpen: false,
      labelValues: {
        labelName: "",
        id: "",
      },
    });
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllList(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      labelValues,
      countryAll,
      countryCount,
      pageCountry,
      searchCountry,
      searchCountryCount,
      SearchCountrypage,
      isCountrySearch,
      LabelList,
      LabelCount,
      errorParentSport,
      errorChildSport,
      errorParentPlayer,
      errorChildPlayer,
      flag,
      uploadLogo,
      createError,
      search,
      displayErrorName,
      parentSport,
      childSport,
      sportsOption,

      isParentPlayerSearch,
      searchParentPlayerData,
      parentPlayer,
      parentPlayerData,
      isChildPlayerSearch,
      searchChildPlayerData,
      childPlayer,
      childPlayerData,
      isParentPlayerLoading,
      isChildPlayerLoading,
    } = this.state;

    const pageNumbers = [];

    if (LabelCount > 0) {
      for (let i = 1; i <= Math.ceil(LabelCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Sport Player Merge</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Sport Player Merge
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "20%", marginTop: "0px" }}
              >
                <option value="">Select Category</option>
                {CategoryData?.length > 0 &&
                  CategoryData?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.categoryName}
                    </option>
                  ))}
              </SelectBox> */}
                {/* <Select
                className="React teamsport-select"
                classNamePrefix="select"
                // onMenuScrollToBottom={(e) =>
                //   this.handleOnScrollBottomDistance(e)
                // }
                // onInputChange={(e) => this.handleDistanceInputChange(1, e)}
                // value={
                //   isDistanceSearch
                //     ? searchDistance?.find((item) => {
                //         return item?.value == distance;
                //       })
                //     : distanceAll?.find((item) => {
                //         return item?.value == distance;
                //       })
                // }
                // onChange={(e) =>
                //   this.setState({
                //     distance: e.value,
                //   })
                // }
                options={countryData}
              /> */}
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllList(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && LabelList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && LabelList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        {/* <TableCell>rapidTeamId</TableCell> */}
                        {/* <TableCell>Logo</TableCell> */}
                        <TableCell style={{ width: "25%" }}>
                          Player Name
                        </TableCell>
                        <TableCell>Parent Team</TableCell>
                        <TableCell>Child Team</TableCell>
                        {/* <TableCell>National</TableCell> */}
                        {/* <TableCell>Gender</TableCell> */}
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {LabelList?.map((item) => {
                        return (
                          <TableRow className="table-rows listTable-Row">
                            <TableCell> {item?.id} </TableCell>
                            {/* <TableCell>{item?.rapidTeamId}</TableCell> */}
                            {/* <TableCell className="upload-img">
                            {item?.flag?.includes("uploads") ? (
                              <img
                                src={config.mediaUrl + item?.flag}
                                alt="teamlogo"
                              />
                            ) : item?.flag ? (
                              <img src={item?.flag} alt="teamlogo" />
                            ) : (
                              ""
                            )}
                          </TableCell> */}
                            <TableCell>{item?.playerName}</TableCell>
                            <TableCell>{item?.sportName}</TableCell>
                            <TableCell>{item?.toSportName}</TableCell>
                            {/* <TableCell>
                            {item?.national === false ? "No" : "Yes"}
                          </TableCell> */}
                            {/* <TableCell>{item?.gender}</TableCell> */}
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn edit-btn"
                                style={{ cursor: "pointer" }}
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                className="table-btn delete-btn"
                                style={{ cursor: "pointer" }}
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                LabelCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Sport Player Relation "
                    : "Edit Sport Player Relation"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        marginBottom: "8px",
                      }}
                    >
                      <label className="modal-label">Parent Sport</label>
                      <Select
                        className="React cricket-select sponsored-select-modal"
                        classNamePrefix="select"
                        placeholder="Parent Sport"
                        menuPosition="fixed"
                        value={sportsOption?.find((item) => {
                          return item?.value == parentSport;
                        })}
                        isLoading={isLoading}
                        onChange={(e) => {
                          this.setState({
                            parentSport: e.value,
                            parentPlayer: null,
                            parentPlayerData: [],
                            searchParentPlayerData: [],
                            childSport: null,
                            childPlayer: null,
                            childPlayerData: [],
                            searchChildPlayerData: [],
                            parentPlayerPage: 0,
                            childPlayerPage: 0,
                          });
                          this.fetchParentPlayer(0, e.value);
                        }}
                        options={sportsOption}
                        // isOptionDisabled={() =>
                        //   sponsoredValues?.selectedprovider?.length >= 2
                        // }
                      />
                      {errorParentSport ? (
                        <p className="errorText" style={{ margin: "0 0 0 0" }}>
                          {errorParentSport}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        marginBottom: "8px",
                      }}
                    >
                      <label className="modal-label">Parent Player</label>
                      <Select
                        className="React cricket-select sponsored-select-modal"
                        classNamePrefix="select"
                        placeholder="Select Team"
                        onMenuScrollToBottom={(e) =>
                          this.handleOnScrollBottomParentPlayer(e)
                        }
                        isLoading={isParentPlayerLoading}
                        onInputChange={(e) =>
                          this.handleParentPlayerInputChange(0, e)
                        }
                        value={
                          parentPlayer &&
                          (isParentPlayerSearch
                            ? searchParentPlayerData?.find((item) => {
                                return item?.value == parentPlayer;
                              })
                            : parentPlayerData?.find((item) => {
                                return item?.value == parentPlayer;
                              }))
                        }
                        onChange={(e) =>
                          this.setState({ parentPlayer: e.value })
                        }
                        options={
                          isParentPlayerSearch
                            ? searchParentPlayerData
                            : parentPlayerData
                        }
                      />
                      {errorParentPlayer ? (
                        <p className="errorText" style={{ margin: "0 0 0 0" }}>
                          {errorParentPlayer}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        marginBottom: "8px",
                      }}
                    >
                      <label className="modal-label">Child Sport</label>
                      <Select
                        className="React cricket-select sponsored-select-modal"
                        classNamePrefix="select"
                        placeholder="Child Sport"
                        menuPosition="fixed"
                        value={
                          childSport &&
                          sportsOption?.find((item) => {
                            return item?.value == childSport;
                          })
                        }
                        isLoading={isLoading}
                        onChange={(e) => {
                          this.setState({
                            childSport: e.value,
                            childPlayer: null,
                            childPlayerData: [],
                            searchChildPlayerData: [],
                            childPlayerPage: 0,
                          });
                          this.fetchChildPlayer(0, e.value);
                        }}
                        options={sportsOption}
                        // isOptionDisabled={() =>
                        //   sponsoredValues?.selectedprovider?.length >= 2
                        // }
                      />
                      {errorChildSport ? (
                        <p className="errorText" style={{ margin: "0 0 0 0" }}>
                          {errorChildSport}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        marginBottom: "8px",
                      }}
                    >
                      <label className="modal-label">Child Player</label>
                      <Select
                        className="React cricket-select sponsored-select-modal"
                        classNamePrefix="select"
                        placeholder="Select Team"
                        onMenuScrollToBottom={(e) =>
                          this.handleOnScrollBottomChildPlayer(e)
                        }
                        onInputChange={(e) =>
                          this.handleChildPlayerInputChange(0, e)
                        }
                        isLoading={isChildPlayerLoading}
                        value={
                          childPlayer &&
                          (isChildPlayerSearch
                            ? searchChildPlayerData?.find((item) => {
                                return item?.value == childPlayer;
                              })
                            : childPlayerData?.find((item) => {
                                return item?.value == childPlayer;
                              }))
                        }
                        onChange={(e) =>
                          this.setState({ childPlayer: e.value })
                        }
                        options={
                          isChildPlayerSearch
                            ? searchChildPlayerData
                            : childPlayerData
                        }
                      />
                      {errorChildPlayer ? (
                        <p className="errorText" style={{ margin: "0 0 0 0" }}>
                          {errorChildPlayer}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Merge" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn"
                            value="Back"
                          />
                        </div>
                        {createError ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0", width: "300px" }}
                          >
                            {createError}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default SportPlayerMerge;
