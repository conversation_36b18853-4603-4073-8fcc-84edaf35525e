import { Validators } from "../../../library/utilities/Validators";

export const bookkeeperFormModel = [
  {
    label: "Name",
    value: "",
    type: "text",
    placeholder: "Name",
    field: "name",
    validators: [
      { check: Validators.required, message: "This field is mandatory" },
    ],
    required: true,
    className: "12",
  },
  // {
  //   label: "Variation",
  //   value: "",
  //   type: "variations",
  //   placeholder: "Variation",
  //   field: "variation",
  //   required: false,
  //   className: "12",
  // },
  {
    label: "Status",
    value: "",
    type: "dropdown",
    placeholder: "Status",
    field: "status",
    validators: [
      { check: Validators.required, message: "This field is mandatory" },
    ],
    required: true,
    className: "12",
    options: [
      { value: "", label: "No Status Selected" },
      { id: 1, value: "active", label: "active" },
      { id: 2, value: "inactive", label: "inactive" },
      { id: 3, value: "deleted", label: "deleted" },
    ],
  },
  {
    label: "Link",
    value: "",
    type: "text",
    placeholder: "Name",
    field: "affiliate_link",
    validators: [
      { check: Validators.required, message: "This field is mandatory" },
    ],
    required: true,
    className: "12",
  },
  {
    label: "Email",
    value: "",
    type: "text",
    placeholder: "Email",
    field: "email",
    validators: [
      { check: Validators.required, message: "This field is mandatory" },
    ],
    required: false,
    className: "12",
  },
  {
    label: "Short Description",
    value: "",
    type: "textarea",
    placeholder: "",
    field: "sort_descriptions",
    validators: [],
    required: false,
    className: "12",
    extraProps: {
      rows: 4,
    },
  },
  {
    label: "Featured content",
    value: "",
    type: "textarea",
    placeholder: "",
    field: "featured_content",
    validators: [],
    required: false,
    className: "12",
    extraProps: {
      rows: 4,
    },
  },
  {
    label: "Is OurPartner",
    value: "",
    type: "dropdown",
    placeholder: "Is OurPartner",
    field: "isPartner",
    validators: [],
    required: false,
    className: "12",
    options: [
      { id: 1, value: false, label: "No" },
      { id: 2, value: true, label: "Yes" },
    ],
  },
];
