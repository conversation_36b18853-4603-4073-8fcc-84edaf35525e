<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="62.906" height="38.616" viewBox="0 0 62.906 38.616">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_1577" data-name="Path 1577" d="M326.06,482.758a11.345,11.345,0,0,0-1.75-2.281c-.6-.476-.794-.864-1.078-.889s-.552.139-.733-.078-.268-.579-.578-.6a.527.527,0,0,0-.465.129.5.5,0,0,1-.578-.078c-.258-.181-1.1-.424-1.336-.579s-.113.269-.026.346-.119.026-.542-.078-.743-.078-.371.284.568.734.836.864.062.269-.387.476-3.9,2.591-5.252,3.377a37.852,37.852,0,0,0-3.729,2.429c-.747.6-7.559.689-8.712.493s-6.681-.76-8.066-.981-3.48-.823-5.454-.074-4.709,2.177-5.1,4.811-.429,4.207,1.845,5.739,5.728,2.986,5.767,4.874a23.661,23.661,0,0,1-.2,3.381c-.039.667.039,1.528.942,1.8a32.078,32.078,0,0,1,5.215,2.279c.784.593,3.138,2.239,3.3,2.516a2.178,2.178,0,0,1,.118.984c0,.237.631.588.982.746s.859.43,1.135.549.395-.47.355-1.1a2.514,2.514,0,0,0-.863-2.047,10.573,10.573,0,0,1-1.3-1.335,2.671,2.671,0,0,0-1.214-.9,27.2,27.2,0,0,1-4.238-2.2,2.424,2.424,0,0,1-1.1-2.279c0-1.181.193-3.579.271-4.128a6.546,6.546,0,0,0,.237-1.453c-.039-.509.232-.316.587.039a17.885,17.885,0,0,0,3.493,2,39.233,39.233,0,0,0,3.883,1.023c1.061.2,1.88.317,2.117.4s.2.474.237.746.75.554,1.061.747a27.12,27.12,0,0,0,2.94,2.59,14.215,14.215,0,0,1,2.314,1.611c.276.237.118.628.118.747s-.395.4-1.14.593a12.277,12.277,0,0,1-2.551.391,3.428,3.428,0,0,1-1.84-.351c-.395-.2-.039-.549-.2-.786a1.736,1.736,0,0,0-1.569-.786,2,2,0,0,0-1.293.43c-.2.079-.281.311.271.707s1.066.984,1.377,1.063a2.835,2.835,0,0,1,1.253.667c.39.391.9,1.1,1.608.984s2.59-.826,3.217-1.023a6.294,6.294,0,0,1,1.332-.316,1,1,0,0,0,1.14-.47c.429-.667.548-.791.587-1.1s.311-.9-.276-1.335a14.106,14.106,0,0,1-1.727-2.6c-.311-.43-1.174-1.963-1.174-1.963a9.6,9.6,0,0,0,1.293-.549c.592-.317,1.372-1.142,1.687-1.182a5.255,5.255,0,0,0,1.411-.628,4.041,4.041,0,0,0,1.337-1.888,13.012,13.012,0,0,0,.824-3.377c.079-.984.153-1.651.153-1.651a2.459,2.459,0,0,1,.433-.932,20.133,20.133,0,0,0,1.75-2.694,3.783,3.783,0,0,1,1.182-1.6,2.206,2.206,0,0,1,1.363-.372c.465.052.852.346,1.275.424s2.028,1.1,2.622,1.252,1.2.372,1.3.631.088.605.371.605,1.094.129,1.146-.052.165-.3.371-.217.836-.181.888-.466-.1-.657.155-.838.465-.672.207-.941-.526-.491-.733-.734-1.791-2.42-2.137-2.86A5.527,5.527,0,0,1,326.06,482.758Z" transform="translate(-282.537 -478.344)" fill="#6abf4b" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_1576" data-name="Path 1576" d="M326.06,482.758a11.345,11.345,0,0,0-1.75-2.281c-.6-.476-.794-.864-1.078-.889s-.552.139-.733-.078-.268-.579-.578-.6a.527.527,0,0,0-.465.129.5.5,0,0,1-.578-.078c-.258-.181-1.1-.424-1.336-.579s-.113.269-.026.346-.119.026-.542-.078-.743-.078-.371.284.568.734.836.864.062.269-.387.476-3.9,2.591-5.252,3.377a37.852,37.852,0,0,0-3.729,2.429c-.747.6-7.559.689-8.712.493s-6.681-.76-8.066-.981-3.48-.823-5.454-.074-4.709,2.177-5.1,4.811-.429,4.207,1.845,5.739,5.728,2.986,5.767,4.874a23.661,23.661,0,0,1-.2,3.381c-.039.667.039,1.528.942,1.8a32.078,32.078,0,0,1,5.215,2.279c.784.593,3.138,2.239,3.3,2.516a2.178,2.178,0,0,1,.118.984c0,.237.631.588.982.746s.859.43,1.135.549.395-.47.355-1.1a2.514,2.514,0,0,0-.863-2.047,10.573,10.573,0,0,1-1.3-1.335,2.671,2.671,0,0,0-1.214-.9,27.2,27.2,0,0,1-4.238-2.2,2.424,2.424,0,0,1-1.1-2.279c0-1.181.193-3.579.271-4.128a6.546,6.546,0,0,0,.237-1.453c-.039-.509.232-.316.587.039a17.885,17.885,0,0,0,3.493,2,39.233,39.233,0,0,0,3.883,1.023c1.061.2,1.88.317,2.117.4s.2.474.237.746.75.554,1.061.747a27.12,27.12,0,0,0,2.94,2.59,14.215,14.215,0,0,1,2.314,1.611c.276.237.118.628.118.747s-.395.4-1.14.593a12.277,12.277,0,0,1-2.551.391,3.428,3.428,0,0,1-1.84-.351c-.395-.2-.039-.549-.2-.786a1.736,1.736,0,0,0-1.569-.786,2,2,0,0,0-1.293.43c-.2.079-.281.311.271.707s1.066.984,1.377,1.063a2.835,2.835,0,0,1,1.253.667c.39.391.9,1.1,1.608.984s2.59-.826,3.217-1.023a6.294,6.294,0,0,1,1.332-.316,1,1,0,0,0,1.14-.47c.429-.667.548-.791.587-1.1s.311-.9-.276-1.335a14.106,14.106,0,0,1-1.727-2.6c-.311-.43-1.174-1.963-1.174-1.963a9.6,9.6,0,0,0,1.293-.549c.592-.317,1.372-1.142,1.687-1.182a5.255,5.255,0,0,0,1.411-.628,4.041,4.041,0,0,0,1.337-1.888,13.012,13.012,0,0,0,.824-3.377c.079-.984.153-1.651.153-1.651a2.459,2.459,0,0,1,.433-.932,20.133,20.133,0,0,0,1.75-2.694,3.783,3.783,0,0,1,1.182-1.6,2.206,2.206,0,0,1,1.363-.372c.465.052.852.346,1.275.424s2.028,1.1,2.622,1.252,1.2.372,1.3.631.088.605.371.605,1.094.129,1.146-.052.165-.3.371-.217.836-.181.888-.466-.1-.657.155-.838.465-.672.207-.941-.526-.491-.733-.734-1.791-2.42-2.137-2.86A5.527,5.527,0,0,1,326.06,482.758Z" transform="translate(-282.537 -478.344)" fill="#6abf4b"/>
    </clipPath>
  </defs>
  <g id="Group_6992" data-name="Group 6992" transform="translate(1332.343 -1776.481)">
    <g id="Group_6616" data-name="Group 6616" transform="translate(-1292.743 1797.957)">
      <path id="Path_1510" data-name="Path 1510" d="M496.338,642.146c-.147.276-.181.313-.4.422a.778.778,0,0,1-.314.055c-.128-.018.126-.073.257-.2S496.485,641.871,496.338,642.146Z" transform="translate(-495.586 -642.055)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6617" data-name="Group 6617" transform="translate(-1293.584 1798.723)">
      <path id="Path_1511" data-name="Path 1511" d="M489.08,648.973c-.037.074-.31.423-.477.459a1.355,1.355,0,0,1-.46.094c-.257,0,.126-.039.293-.149s-.491.348-.293.037a2.484,2.484,0,0,1,.405-.441C488.64,648.881,489.117,648.9,489.08,648.973Z" transform="translate(-488.057 -648.91)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6618" data-name="Group 6618" transform="translate(-1294.259 1798.789)">
      <path id="Path_1512" data-name="Path 1512" d="M482.887,649.554a1.265,1.265,0,0,1-.385.386.84.84,0,0,1-.46.055c-.092,0,.071-.037.312-.184S482.978,649.388,482.887,649.554Z" transform="translate(-482.015 -649.503)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6619" data-name="Group 6619" transform="translate(-1297.597 1799.404)">
      <path id="Path_1513" data-name="Path 1513" d="M454.167,655.01a4.4,4.4,0,0,1-1.1.257,2.879,2.879,0,0,1-.735-.092c-.183-.055-.35-.055.147-.073s1.011-.073,1.324-.092A1.467,1.467,0,0,1,454.167,655.01Z" transform="translate(-452.146 -655.002)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6620" data-name="Group 6620" transform="translate(-1298.525 1798.655)">
      <path id="Path_1514" data-name="Path 1514" d="M444.622,648.471c-.46-.018-.515-.184-.644-.165s-.3-.037.184.183a1.277,1.277,0,0,0,.864.184C445.229,648.636,445.083,648.489,444.622,648.471Z" transform="translate(-443.842 -648.304)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6621" data-name="Group 6621" transform="translate(-1298.525 1797.476)">
      <path id="Path_1515" data-name="Path 1515" d="M443.892,637.829a2.515,2.515,0,0,0,.79.5c.22.073.071-.02-.314-.277S443.746,637.664,443.892,637.829Z" transform="translate(-443.849 -637.755)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6622" data-name="Group 6622" transform="translate(-1297.357 1795.809)">
      <path id="Path_1516" data-name="Path 1516" d="M454.439,622.92c.238.223.422.314.458.443s-.108.055-.367-.165S454.2,622.7,454.439,622.92Z" transform="translate(-454.294 -622.838)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6623" data-name="Group 6623" transform="translate(-1298.718 1795.439)">
      <path id="Path_1517" data-name="Path 1517" d="M442.242,619.627c.277.312.481.351.738.663s-.367-.053-.57-.276S441.965,619.315,442.242,619.627Z" transform="translate(-442.117 -619.524)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6624" data-name="Group 6624" transform="translate(-1296.231 1795.692)">
      <path id="Path_1518" data-name="Path 1518" d="M464.6,621.845a4.077,4.077,0,0,1,.607.461c.147.147.094.129-.293-.018s-.479-.3-.534-.406S464.471,621.772,464.6,621.845Z" transform="translate(-464.367 -621.794)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6625" data-name="Group 6625" transform="translate(-1295.954 1796.283)">
      <path id="Path_1519" data-name="Path 1519" d="M466.9,627.092c.167.092.4,0,.589.183s.293.372-.074.241S466.73,627,466.9,627.092Z" transform="translate(-466.852 -627.078)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6626" data-name="Group 6626" transform="translate(-1296.46 1796.368)">
      <path id="Path_1520" data-name="Path 1520" d="M462.562,627.969a2.793,2.793,0,0,0,.644.535c.385.22-.057.092-.314-.037s-.311-.3-.477-.461S462.305,627.746,462.562,627.969Z" transform="translate(-462.321 -627.838)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6627" data-name="Group 6627" transform="translate(-1297.536 1794.939)">
      <path id="Path_1521" data-name="Path 1521" d="M453.076,615.208c.513.333.369.537-.055.223S452.563,614.876,453.076,615.208Z" transform="translate(-452.698 -615.048)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6628" data-name="Group 6628" transform="translate(-1296.763 1795.544)">
      <path id="Path_1522" data-name="Path 1522" d="M459.883,620.6c.275.314.128.222.293.388s.2.3-.147.073-.5-.608-.369-.5S459.608,620.281,459.883,620.6Z" transform="translate(-459.615 -620.47)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6634" data-name="Group 6634" transform="translate(-1302.354 1789.934)">
      <path id="Path_1532" data-name="Path 1532" d="M424.458,570.267A5.081,5.081,0,0,0,422.2,571.7a12.679,12.679,0,0,1-2.581,2.212,11.471,11.471,0,0,1-2.521,1.43,4.026,4.026,0,0,1-1.335-.2,2.036,2.036,0,0,1-.832-.766c-.06-.155-.022.2.16.389a2.119,2.119,0,0,0,.924.562c.32.06.582.156.723.156s-.382.174-.582.174a3.886,3.886,0,0,1-1.225-.718c-.22-.213-.18-.058.02.135a2.683,2.683,0,0,0,.442.35,3.265,3.265,0,0,1-.844-.389,5.876,5.876,0,0,0-1.264-.505,11.612,11.612,0,0,0-1.365-.194c-.242,0-1.2.039-1.385.078s.179.1.481.058a6.213,6.213,0,0,1,1.065-.02c.421.02.624.291.462.213s-1.043-.2-.662-.058.5.195.2.272a2.261,2.261,0,0,0-.662.291.983.983,0,0,1-.7.077c-.262-.039-.021.118.26.2a2.279,2.279,0,0,1,.663.31,7.34,7.34,0,0,0,1.847.621c.4.019.381.116.783.116s.342.134-.2.155a5,5,0,0,1-1.547-.136,5.749,5.749,0,0,0-1-.291c-.24-.039-.281-.1.422.213s-.2.058-.5-.039a3.289,3.289,0,0,0-.9-.155c-.242,0,.179.058.461.116s1.306.544,1.647.66a3,3,0,0,0,.663.116c.3.058-.8,0-1.1-.1s-1.163-.5-1.485-.563a4.828,4.828,0,0,0-.542-.058c-.14-.02.2.116.482.155a5.4,5.4,0,0,1,1.123.447,2.808,2.808,0,0,0,.824.291,3.625,3.625,0,0,1-.924-.058c-.3-.078-.884-.31-1.205-.426s-.642-.058-.2.058a15.914,15.914,0,0,0,1.807.563c.321,0,.841.039.421.194a3.326,3.326,0,0,1-1.425-.058,4.583,4.583,0,0,1-.683-.408c-.22-.116-.242-.076.08.137a3.485,3.485,0,0,0,1.447.5s.442-.04.12.058a6.857,6.857,0,0,0-1.567.66,5.775,5.775,0,0,0-.863.777c-.12.194.122,0,.382-.234a5.79,5.79,0,0,1,.983-.7c.242-.116.7-.252.9-.329s.661-.135-.062.194a8.608,8.608,0,0,0-1.706,1.009c.361-.155,1-.563,1.265-.641a4.875,4.875,0,0,1,.983-.329c.262,0,.561.213.8.213a9.767,9.767,0,0,0,1.927.039,5.441,5.441,0,0,0,1.727-.757c.281-.194.381-.059.1.175a4.273,4.273,0,0,1-1,.621,2.22,2.22,0,0,1-1,.137c-.442-.019-1.2-.136-.723.039a2.418,2.418,0,0,0,.883.174c.12,0-.342.194-.622.252a20.323,20.323,0,0,0-2.048.466,2.309,2.309,0,0,0-.662.331c-.281.194-.061.214.12.078a1.76,1.76,0,0,1,.724-.331c.341-.077,1.567-.252,2.108-.29a8.907,8.907,0,0,0,1.886-.544,4.1,4.1,0,0,0,1.185-.583c.22-.213.341-.232.22-.019s-.459.37-.621.505.341,0,.481-.155.442-.407.583-.6.8-.719.623-.331a4.469,4.469,0,0,1-1.165,1.126,4.34,4.34,0,0,1-1.2.525,3.268,3.268,0,0,1-.744.1c-.3.019.26.1.522.039a5.109,5.109,0,0,0,1.285-.408c.422-.232.743-.446.743-.446a3.932,3.932,0,0,1-.583.542,2.87,2.87,0,0,1-.823.389c-.4.1-.062.155.24.039a2.184,2.184,0,0,0,.884-.427,6.521,6.521,0,0,0,.743-.679c.12-.194.382-.756.623-1.086a5.066,5.066,0,0,1,.8-1.01,1.583,1.583,0,0,1,.562-.271c.1,0,.041.408-.18.718a8.893,8.893,0,0,1-.944,1.262,3.8,3.8,0,0,1-.682.446c-.241.137-.5.311-.3.272a1.861,1.861,0,0,0,.682-.214c.2-.135.079.1-.142.292s-.581.484-.3.368.381-.037.743-.446.784-1.144,1.065-1.436.622-.836.8-.971.924-.173,1.165-.31a6.4,6.4,0,0,0,.923-.718c.1-.118-.2-2.038-.02-2.7S424.458,570.267,424.458,570.267Z" transform="translate(-409.58 -570.267)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6635" data-name="Group 6635" transform="translate(-1299.249 1794.592)">
      <path id="Path_1533" data-name="Path 1533" d="M438.949,612.627a3,3,0,0,0-1.043-.583c-.462-.136-.784-.156-.3.116a4.075,4.075,0,0,0,.943.466A1.209,1.209,0,0,0,438.949,612.627Z" transform="translate(-437.365 -611.947)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6636" data-name="Group 6636" transform="translate(-1300.628 1795.039)">
      <path id="Path_1534" data-name="Path 1534" d="M428.516,616.724c.261.019-.943-.195-1.285-.311a9.9,9.9,0,0,0-1.1-.387,5.657,5.657,0,0,0-.863-.078c-.321,0-.342.078.14.136a3.973,3.973,0,0,1,1.1.252,5.844,5.844,0,0,0,1.063.408C427.893,616.783,428.255,616.705,428.516,616.724Z" transform="translate(-425.031 -615.948)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6637" data-name="Group 6637" transform="translate(-1299.288 1796.41)">
      <path id="Path_1535" data-name="Path 1535" d="M439.3,629.04c-.14-.039-.964-.447-1.245-.582a4.218,4.218,0,0,0-.983-.233c-.222-.039.242.1.6.233s.763.408.963.486a1.558,1.558,0,0,0,.663.213,3.163,3.163,0,0,0,1.085-.213c.22-.116-.16-.039-.522.039S439.444,629.079,439.3,629.04Z" transform="translate(-437.018 -628.219)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6638" data-name="Group 6638" transform="translate(-1292.582 1795.075)">
      <path id="Path_1536" data-name="Path 1536" d="M498.324,616.446a5.243,5.243,0,0,0-.863,1.028c-.22.35-.3.5-.4.66s0,.04.381-.407a12.131,12.131,0,0,1,.923-1.068C498.666,616.387,498.724,616.058,498.324,616.446Z" transform="translate(-497.023 -616.268)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6641" data-name="Group 6641" transform="translate(-1298.212 1785.093)">
      <path id="Path_1542" data-name="Path 1542" d="M449.279,526.952c-.136.034-2.467,1.023-2.467,1.023l-.17.375s1.4-.494,1.7-.63l1.454-.645Z" transform="translate(-446.642 -526.952)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6642" data-name="Group 6642" transform="translate(-1299.548 1785.695)">
      <path id="Path_1543" data-name="Path 1543" d="M439.949,532.349a9.912,9.912,0,0,1-1.5.8,7.973,7.973,0,0,1-1.838.341c-.425.034-.731.034-.731.034l-.153.324s.714-.1.953-.1a11.6,11.6,0,0,0,1.7-.324,6.219,6.219,0,0,1,.647-.2s-.392,1.654-.783,2.745a5.357,5.357,0,0,1-1.718,2.472,11.106,11.106,0,0,1-1.31.563,1.421,1.421,0,0,1-.493-.034l-.034.341a1.261,1.261,0,0,0,.527,0,12.044,12.044,0,0,0,2.229-1.074,16.086,16.086,0,0,0,1.14-2.3c.136-.375.7-2.676.833-2.847a6.984,6.984,0,0,1,1.069-.672C440.661,532.307,439.949,532.349,439.949,532.349Z" transform="translate(-434.692 -532.34)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6643" data-name="Group 6643" transform="translate(-1316.552 1779.661)">
      <g id="Group_6593" data-name="Group 6593" transform="translate(0 0)">
        <g id="Group_6592" data-name="Group 6592" clip-path="url(#clip-path)">
          <g id="Group_6591" data-name="Group 6591">
            <g id="Group_6590" data-name="Group 6590">
              <g id="Group_6589" data-name="Group 6589" clip-path="url(#clip-path-2)">
                <g id="Group_6588" data-name="Group 6588" transform="translate(-26.601 -10.044)">
                  <path id="Path_1544" data-name="Path 1544" d="M329.651,478.344H282.536v34.871h47.115V478.344Z" transform="translate(-255.935 -468.3)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_65" data-name="Ellipse 65" cx="16.987" cy="22.042" rx="16.987" ry="22.042" transform="matrix(0.351, -0.936, 0.936, 0.351, 0, 31.811)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_66" data-name="Ellipse 66" cx="16.806" cy="21.808" rx="16.806" ry="21.808" transform="translate(0.282 31.725) rotate(-69.445)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_67" data-name="Ellipse 67" cx="16.625" cy="21.573" rx="16.625" ry="21.573" transform="translate(0.565 31.637) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_68" data-name="Ellipse 68" cx="16.445" cy="21.339" rx="16.445" ry="21.339" transform="translate(0.851 31.552) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_69" data-name="Ellipse 69" cx="16.264" cy="21.105" rx="16.264" ry="21.105" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.133, 31.466)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_70" data-name="Ellipse 70" cx="16.083" cy="20.87" rx="16.083" ry="20.87" transform="translate(1.415 31.377) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_71" data-name="Ellipse 71" cx="15.902" cy="20.635" rx="15.902" ry="20.635" transform="matrix(0.351, -0.936, 0.936, 0.351, 1.698, 31.292)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_72" data-name="Ellipse 72" cx="15.722" cy="20.401" rx="15.722" ry="20.401" transform="translate(1.98 31.206) rotate(-69.445)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_73" data-name="Ellipse 73" cx="15.541" cy="20.167" rx="15.541" ry="20.167" transform="translate(2.266 31.118) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_74" data-name="Ellipse 74" cx="15.36" cy="19.932" rx="15.36" ry="19.932" transform="translate(2.548 31.032) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_75" data-name="Ellipse 75" cx="15.18" cy="19.698" rx="15.18" ry="19.698" transform="translate(2.831 30.947) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_76" data-name="Ellipse 76" cx="14.999" cy="19.463" rx="14.999" ry="19.463" transform="translate(3.113 30.858) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_77" data-name="Ellipse 77" cx="14.818" cy="19.228" rx="14.818" ry="19.228" transform="translate(3.395 30.772) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_78" data-name="Ellipse 78" cx="14.637" cy="18.994" rx="14.637" ry="18.994" transform="translate(3.681 30.687) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_79" data-name="Ellipse 79" cx="14.457" cy="18.76" rx="14.457" ry="18.76" transform="translate(3.964 30.595) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_80" data-name="Ellipse 80" cx="14.276" cy="18.525" rx="14.276" ry="18.525" transform="matrix(0.351, -0.936, 0.936, 0.351, 4.246, 30.509)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_81" data-name="Ellipse 81" cx="14.095" cy="18.29" rx="14.095" ry="18.29" transform="translate(4.528 30.421) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_82" data-name="Ellipse 82" cx="13.915" cy="18.056" rx="13.915" ry="18.056" transform="translate(4.811 30.335) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_83" data-name="Ellipse 83" cx="13.734" cy="17.822" rx="13.734" ry="17.822" transform="matrix(0.351, -0.936, 0.936, 0.351, 5.097, 30.25)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_84" data-name="Ellipse 84" cx="13.553" cy="17.587" rx="13.553" ry="17.587" transform="translate(5.379 30.161) rotate(-69.448)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_85" data-name="Ellipse 85" cx="13.373" cy="17.353" rx="13.373" ry="17.353" transform="translate(5.661 30.076) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_86" data-name="Ellipse 86" cx="13.192" cy="17.118" rx="13.192" ry="17.118" transform="translate(5.943 29.99) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_87" data-name="Ellipse 87" cx="13.011" cy="16.883" rx="13.011" ry="16.883" transform="translate(6.226 29.901) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_88" data-name="Ellipse 88" cx="12.83" cy="16.649" rx="12.83" ry="16.649" transform="translate(6.512 29.816) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_89" data-name="Ellipse 89" cx="12.65" cy="16.415" rx="12.65" ry="16.415" transform="matrix(0.351, -0.936, 0.936, 0.351, 6.794, 29.731)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_90" data-name="Ellipse 90" cx="12.469" cy="16.18" rx="12.469" ry="16.18" transform="matrix(0.351, -0.936, 0.936, 0.351, 7.077, 29.642)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_91" data-name="Ellipse 91" cx="12.288" cy="15.946" rx="12.288" ry="15.946" transform="translate(7.359 29.556) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_92" data-name="Ellipse 92" cx="12.108" cy="15.711" rx="12.108" ry="15.711" transform="translate(7.641 29.467) rotate(-69.444)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_93" data-name="Ellipse 93" cx="11.927" cy="15.477" rx="11.927" ry="15.477" transform="translate(7.927 29.379) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_94" data-name="Ellipse 94" cx="11.746" cy="15.242" rx="11.746" ry="15.242" transform="translate(8.209 29.293) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_95" data-name="Ellipse 95" cx="11.565" cy="15.008" rx="11.565" ry="15.008" transform="translate(8.492 29.205) rotate(-69.448)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_96" data-name="Ellipse 96" cx="11.385" cy="14.773" rx="11.385" ry="14.773" transform="translate(8.774 29.119) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_97" data-name="Ellipse 97" cx="11.204" cy="14.539" rx="11.204" ry="14.539" transform="translate(9.056 29.034) rotate(-69.445)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_98" data-name="Ellipse 98" cx="11.023" cy="14.304" rx="11.023" ry="14.304" transform="translate(9.343 28.945) rotate(-69.449)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_99" data-name="Ellipse 99" cx="10.843" cy="14.07" rx="10.843" ry="14.07" transform="matrix(0.351, -0.936, 0.936, 0.351, 9.625, 28.86)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="10.662" cy="13.835" rx="10.662" ry="13.835" transform="translate(9.907 28.774) rotate(-69.446)" fill="#6abf4b"/>
                  <path id="Path_1545" data-name="Path 1545" d="M163.947,511.439c0-5.939,5.935-9.919,13.256-8.891s13.256,6.678,13.255,12.617-5.935,9.92-13.256,8.891S163.947,517.378,163.947,511.439Z" transform="translate(-150.599 -489.656)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_101" data-name="Ellipse 101" cx="10.3" cy="13.366" rx="10.3" ry="13.366" transform="translate(10.472 28.6) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_102" data-name="Ellipse 102" cx="10.12" cy="13.132" rx="10.12" ry="13.132" transform="translate(10.758 28.515) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_103" data-name="Ellipse 103" cx="9.939" cy="12.897" rx="9.939" ry="12.897" transform="translate(11.04 28.426) rotate(-69.449)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_104" data-name="Ellipse 104" cx="9.758" cy="12.663" rx="9.758" ry="12.663" transform="translate(11.323 28.341) rotate(-69.448)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_105" data-name="Ellipse 105" cx="9.578" cy="12.428" rx="9.578" ry="12.428" transform="translate(11.605 28.248) rotate(-69.448)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_106" data-name="Ellipse 106" cx="9.397" cy="12.194" rx="9.397" ry="12.194" transform="translate(11.887 28.163) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_107" data-name="Ellipse 107" cx="9.216" cy="11.959" rx="9.216" ry="11.959" transform="translate(12.173 28.078) rotate(-69.447)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_108" data-name="Ellipse 108" cx="9.036" cy="11.725" rx="9.036" ry="11.725" transform="translate(12.456 27.989) rotate(-69.449)" fill="#6abf4b"/>
                  <path id="Path_1546" data-name="Path 1546" d="M182.362,525.188c0-5.018,5.014-8.38,11.2-7.511s11.2,5.642,11.2,10.659-5.014,8.38-11.2,7.511S182.362,530.206,182.362,525.188Z" transform="translate(-166.956 -503.116)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_109" data-name="Ellipse 109" cx="8.674" cy="11.256" rx="8.674" ry="11.256" transform="translate(13.02 27.818) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_110" data-name="Ellipse 110" cx="8.493" cy="11.021" rx="8.493" ry="11.021" transform="matrix(0.351, -0.936, 0.936, 0.351, 13.303, 27.729)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_111" data-name="Ellipse 111" cx="8.313" cy="10.787" rx="8.313" ry="10.787" transform="translate(13.585 27.643) rotate(-69.446)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_112" data-name="Ellipse 112" cx="8.132" cy="10.552" rx="8.132" ry="10.552" transform="translate(13.871 27.558) rotate(-69.447)" fill="#6abf4b"/>
                  <path id="Path_1547" data-name="Path 1547" d="M192.59,532.833c0-4.506,4.5-7.525,10.057-6.745s10.056,5.066,10.056,9.571-4.5,7.525-10.057,6.745S192.59,537.338,192.59,532.833Z" transform="translate(-176.041 -510.6)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_113" data-name="Ellipse 113" cx="7.77" cy="10.083" rx="7.77" ry="10.083" transform="translate(14.435 27.384) rotate(-69.448)" fill="#6abf4b"/>
                  <path id="Path_1548" data-name="Path 1548" d="M196.668,535.918c0-4.3,4.3-7.183,9.6-6.438s9.6,4.836,9.6,9.136-4.3,7.183-9.6,6.438S196.668,540.219,196.668,535.918Z" transform="translate(-179.663 -513.618)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_114" data-name="Ellipse 114" cx="7.409" cy="9.614" rx="7.409" ry="9.614" transform="translate(15 27.21) rotate(-69.448)" fill="#6abf4b"/>
                  <path id="Path_1549" data-name="Path 1549" d="M200.778,538.937c0-4.1,4.093-6.841,9.142-6.131s9.142,4.606,9.142,8.7-4.093,6.841-9.143,6.131S200.778,543.033,200.778,538.937Z" transform="translate(-183.314 -516.576)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_115" data-name="Ellipse 115" cx="7.048" cy="9.145" rx="7.048" ry="9.145" transform="translate(15.569 27.032) rotate(-69.449)" fill="#6abf4b"/>
                  <path id="Path_1550" data-name="Path 1550" d="M204.856,541.988c0-3.891,3.889-6.5,8.685-5.825s8.685,4.375,8.685,8.266-3.889,6.5-8.685,5.825S204.856,545.879,204.856,541.988Z" transform="translate(-186.936 -519.564)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_116" data-name="Ellipse 116" cx="6.686" cy="8.676" rx="6.686" ry="8.676" transform="translate(16.133 26.861) rotate(-69.445)" fill="#6abf4b"/>
                  <path id="Path_1551" data-name="Path 1551" d="M208.934,545.039c0-3.687,3.684-6.157,8.228-5.518s8.228,4.145,8.227,7.831-3.684,6.157-8.228,5.518S208.934,548.726,208.934,545.039Z" transform="translate(-190.558 -522.551)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_117" data-name="Ellipse 117" cx="6.325" cy="8.208" rx="6.325" ry="8.208" transform="translate(16.701 26.687) rotate(-69.449)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_118" data-name="Ellipse 118" cx="6.144" cy="7.973" rx="6.144" ry="7.973" transform="translate(16.984 26.602) rotate(-69.447)" fill="#6abf4b"/>
                  <path id="Path_1552" data-name="Path 1552" d="M215.084,549.633c0-3.38,3.377-5.644,7.542-5.058s7.542,3.8,7.542,7.178-3.377,5.644-7.543,5.058S215.084,553.012,215.084,549.633Z" transform="translate(-196.021 -527.047)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_119" data-name="Ellipse 119" cx="5.783" cy="7.504" rx="5.783" ry="7.504" transform="translate(17.548 26.428) rotate(-69.448)" fill="#6abf4b"/>
                  <path id="Path_1553" data-name="Path 1553" d="M219.162,552.718c0-3.174,3.172-5.3,7.085-4.751s7.085,3.569,7.085,6.744-3.172,5.3-7.085,4.751S219.162,555.892,219.162,552.718Z" transform="translate(-199.643 -530.065)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_120" data-name="Ellipse 120" cx="5.421" cy="7.035" rx="5.421" ry="7.035" transform="translate(18.117 26.254) rotate(-69.452)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_121" data-name="Ellipse 121" cx="5.24" cy="6.8" rx="5.24" ry="6.8" transform="matrix(0.351, -0.936, 0.936, 0.351, 18.399, 26.168)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_122" data-name="Ellipse 122" cx="5.06" cy="6.566" rx="5.06" ry="6.566" transform="translate(18.681 26.082) rotate(-69.447)" fill="#6abf4b"/>
                  <path id="Path_1554" data-name="Path 1554" d="M227.35,558.82c0-2.765,2.763-4.618,6.171-4.139s6.171,3.109,6.171,5.873-2.763,4.618-6.171,4.138S227.35,561.584,227.35,558.82Z" transform="translate(-206.916 -536.04)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_123" data-name="Ellipse 123" cx="4.698" cy="6.097" rx="4.698" ry="6.097" transform="translate(19.246 25.905) rotate(-69.445)" fill="#6abf4b"/>
                  <path id="Path_1555" data-name="Path 1555" d="M231.46,561.839c0-2.56,2.559-4.276,5.714-3.832s5.714,2.878,5.714,5.438-2.558,4.276-5.714,3.832S231.46,564.4,231.46,561.839Z" transform="translate(-210.567 -538.999)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_124" data-name="Ellipse 124" cx="4.337" cy="5.628" rx="4.337" ry="5.628" transform="translate(19.814 25.731) rotate(-69.45)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_125" data-name="Ellipse 125" cx="4.156" cy="5.394" rx="4.156" ry="5.394" transform="matrix(0.351, -0.936, 0.936, 0.351, 20.097, 25.645)" fill="#6abf4b"/>
                  <path id="Path_1556" data-name="Path 1556" d="M237.578,566.433c0-2.253,2.251-3.763,5.028-3.372s5.028,2.533,5.028,4.785-2.251,3.763-5.029,3.372S237.578,568.685,237.578,566.433Z" transform="translate(-216.001 -543.495)" fill="#6abf4b"/>
                  <path id="Path_1557" data-name="Path 1557" d="M239.617,567.975c0-2.15,2.149-3.591,4.8-3.219s4.8,2.418,4.8,4.568-2.149,3.591-4.8,3.219S239.617,570.126,239.617,567.975Z" transform="translate(-217.812 -545.004)" fill="#6abf4b"/>
                  <path id="Path_1558" data-name="Path 1558" d="M241.688,569.518c0-2.048,2.047-3.42,4.572-3.065s4.571,2.3,4.571,4.351-2.047,3.42-4.572,3.065S241.688,571.566,241.688,569.518Z" transform="translate(-219.652 -546.513)" fill="#6abf4b"/>
                  <path id="Path_1559" data-name="Path 1559" d="M243.727,571.026c0-1.946,1.944-3.25,4.343-2.912s4.343,2.187,4.342,4.133-1.944,3.249-4.343,2.912S243.727,572.972,243.727,571.026Z" transform="translate(-221.463 -547.991)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_126" data-name="Ellipse 126" cx="3.253" cy="4.221" rx="3.253" ry="4.221" transform="translate(21.512 25.212) rotate(-69.451)" fill="#6abf4b"/>
                  <path id="Path_1560" data-name="Path 1560" d="M247.805,574.112c0-1.741,1.74-2.907,3.886-2.605s3.885,1.957,3.885,3.7-1.74,2.907-3.886,2.605S247.8,575.853,247.805,574.112Z" transform="translate(-225.085 -551.009)" fill="#6abf4b"/>
                  <ellipse id="Ellipse_127" data-name="Ellipse 127" cx="2.891" cy="3.752" rx="2.891" ry="3.752" transform="translate(22.077 25.037) rotate(-69.453)" fill="#6abf4b"/>
                  <path id="Path_1561" data-name="Path 1561" d="M251.915,577.162c0-1.536,1.535-2.565,3.429-2.3s3.428,1.727,3.428,3.263-1.535,2.565-3.429,2.3S251.915,578.7,251.915,577.162Z" transform="translate(-228.736 -553.996)" fill="#6abf4b"/>
                  <path id="Path_1562" data-name="Path 1562" d="M253.954,578.705c0-1.433,1.433-2.394,3.2-2.146s3.2,1.612,3.2,3.046-1.433,2.394-3.2,2.146S253.954,580.139,253.954,578.705Z" transform="translate(-230.547 -555.505)" fill="#6abf4b"/>
                  <path id="Path_1563" data-name="Path 1563" d="M255.993,580.181c0-1.331,1.331-2.223,2.972-1.993a3.256,3.256,0,0,1,2.971,2.828c0,1.331-1.33,2.223-2.972,1.993A3.256,3.256,0,0,1,255.993,580.181Z" transform="translate(-232.358 -556.954)" fill="#6abf4b"/>
                  <path id="Path_1564" data-name="Path 1564" d="M258.033,581.724c0-1.229,1.228-2.052,2.743-1.839a3.005,3.005,0,0,1,2.742,2.61c0,1.229-1.228,2.052-2.743,1.839A3.005,3.005,0,0,1,258.033,581.724Z" transform="translate(-234.17 -558.464)" fill="#6abf4b"/>
                  <path id="Path_1565" data-name="Path 1565" d="M260.072,583.232c0-1.126,1.126-1.881,2.514-1.686a2.754,2.754,0,0,1,2.514,2.393c0,1.126-1.126,1.881-2.514,1.686A2.754,2.754,0,0,1,260.072,583.232Z" transform="translate(-235.981 -559.941)" fill="#6abf4b"/>
                  <path id="Path_1566" data-name="Path 1566" d="M262.143,584.775c0-1.024,1.024-1.71,2.286-1.532a2.5,2.5,0,0,1,2.286,2.175c0,1.024-1.024,1.71-2.286,1.532A2.5,2.5,0,0,1,262.143,584.775Z" transform="translate(-237.821 -561.451)" fill="#6abf4b"/>
                  <path id="Path_1567" data-name="Path 1567" d="M264.182,586.318c0-.922.921-1.539,2.057-1.379A2.254,2.254,0,0,1,268.3,586.9c0,.922-.921,1.539-2.057,1.379A2.254,2.254,0,0,1,264.182,586.318Z" transform="translate(-239.632 -562.96)" fill="#6abf4b"/>
                  <path id="Path_1568" data-name="Path 1568" d="M266.221,587.826c0-.819.819-1.368,1.829-1.226a2,2,0,0,1,1.828,1.74c0,.819-.819,1.368-1.829,1.226A2,2,0,0,1,266.221,587.826Z" transform="translate(-241.443 -564.438)" fill="#6abf4b"/>
                  <path id="Path_1569" data-name="Path 1569" d="M268.26,589.369c0-.717.716-1.2,1.6-1.073a1.753,1.753,0,0,1,1.6,1.523c0,.717-.717,1.2-1.6,1.073A1.753,1.753,0,0,1,268.26,589.369Z" transform="translate(-243.254 -565.947)" fill="#6abf4b"/>
                  <path id="Path_1570" data-name="Path 1570" d="M270.3,590.911c0-.614.614-1.026,1.372-.919a1.5,1.5,0,0,1,1.371,1.306c0,.614-.614,1.026-1.372.919A1.5,1.5,0,0,1,270.3,590.911Z" transform="translate(-245.065 -567.456)" fill="#6abf4b"/>
                  <path id="Path_1571" data-name="Path 1571" d="M272.37,592.42c0-.512.512-.855,1.143-.766a1.252,1.252,0,0,1,1.143,1.088c0,.512-.512.855-1.143.766A1.252,1.252,0,0,1,272.37,592.42Z" transform="translate(-246.905 -568.935)" fill="#6abf4b"/>
                  <path id="Path_1572" data-name="Path 1572" d="M274.41,593.962c0-.41.409-.684.915-.613a1,1,0,0,1,.914.87c0,.409-.41.684-.915.613A1,1,0,0,1,274.41,593.962Z" transform="translate(-248.717 -570.443)" fill="#6abf4b"/>
                  <path id="Path_1573" data-name="Path 1573" d="M276.449,595.505c0-.307.307-.513.686-.459a.751.751,0,0,1,.686.653c0,.307-.307.513-.686.459A.751.751,0,0,1,276.449,595.505Z" transform="translate(-250.528 -571.953)" fill="#6abf4b"/>
                  <path id="Path_1574" data-name="Path 1574" d="M278.488,597.013c0-.2.2-.342.457-.307a.5.5,0,0,1,.457.435c0,.2-.205.342-.457.306A.5.5,0,0,1,278.488,597.013Z" transform="translate(-252.339 -573.43)" fill="#6abf4b"/>
                  <path id="Path_1575" data-name="Path 1575" d="M280.527,598.556c0-.1.1-.171.229-.153a.25.25,0,0,1,.228.218c0,.1-.1.171-.229.153A.25.25,0,0,1,280.527,598.556Z" transform="translate(-254.15 -574.94)" fill="#6abf4b"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_6644" data-name="Group 6644" transform="translate(-1289.681 1800.149)">
      <path id="Path_1578" data-name="Path 1578" d="M528.086,661.672a7.12,7.12,0,0,0,2.228,1.321c1.277.535,2.1.962,2.437,1.079a1.508,1.508,0,0,1,.951.9,4.206,4.206,0,0,1,.493,1.906,30.432,30.432,0,0,0-.326,3.8,4.879,4.879,0,0,1-.25,1.948,14.383,14.383,0,0,0-.784,2.233c0,.292-.075.418-.534,1.037s-.5.987-1.035.284a2.609,2.609,0,0,1-.576-2.023c.117-.543.568-.494.9-.619a.864.864,0,0,0,.417-1.12,2.319,2.319,0,0,1,.125-1.363c.083-.335.284-3.027.284-3.687a1.748,1.748,0,0,0-.367-1.279,2.644,2.644,0,0,0-1.486-.619c-.417,0-1.861-.125-2.52-.125s-2.069-.251-2.645-.251a4.384,4.384,0,0,1-1.936-.535c-.785-.376-.534-.2.167-.577S528.086,661.672,528.086,661.672Z" transform="translate(-522.978 -661.672)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6645" data-name="Group 6645" transform="translate(-1288.067 1781.163)">
      <path id="Path_1579" data-name="Path 1579" d="M545.387,492.244a1.184,1.184,0,0,0-.686-.365c-.34-.1-.933-.148-.809,0s.118.39-.272.39-.779-.049-.779.124a.763.763,0,0,1-.543.662c-.365.074-1.13.47-.711.495s-.291.267-.661.267-.623.017-.939.017-1.924-.286-1.628-.138.152.319-.12.245-.828-.1-.488.05.306.393.059.492-.535-.124-.294,0,.836.5.49.57-.736.213-.366.362.488.514.612.687.228.371.543.223,4-1.555,4.459-1.8a23.1,23.1,0,0,0,1.984-2.109C545.357,492.368,545.578,492.461,545.387,492.244Z" transform="translate(-537.426 -491.785)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6646" data-name="Group 6646" transform="translate(-1300.824 1786.784)">
      <path id="Path_1580" data-name="Path 1580" d="M424.7,542.19a10.784,10.784,0,0,0-1.155-.093c-.229-.062-.366.08-.2.278a2.71,2.71,0,0,0,1.075.755c.432.124,2.675.508,2.922.508s1.97,0,2.847-.062.63-.941.63-.941S425.194,542.314,424.7,542.19Z" transform="translate(-423.273 -542.083)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6647" data-name="Group 6647" transform="translate(-1300.566 1781.025)">
      <path id="Path_1581" data-name="Path 1581" d="M428.011,490.582c.154.093.3.749.713,1.12s.921.662.952.879.26.2.692.353,3.217.94,3.817,1.188,1.736.68,1.891,1.064a1.858,1.858,0,0,1-.353,1.771c-.494.464-.908.358-1.093.556a3.788,3.788,0,0,1-.908.477l-1.3-.633s-.425-.348-1.993-.317a5.539,5.539,0,0,1-3.429-.963c-.679-.426-1.476-.985-1.415-1.987a3.024,3.024,0,0,1,.79-2.135c.538-.587.668-.73.946-1.04S427.857,490.489,428.011,490.582Z" transform="translate(-425.583 -490.551)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6648" data-name="Group 6648" transform="translate(-1298.868 1779.183)">
      <path id="Path_1582" data-name="Path 1582" d="M451.394,474.194a3.573,3.573,0,0,1,.834,2.037c.1,1.157,1.409.85.971.944s-1.8.12-1.731.288a2.972,2.972,0,0,1,.049,1.2c-.074.44-.365.544.074.91s.439.588.661.489a13.491,13.491,0,0,0,1.495-1.077c.39-.322.617-.613.957-.836s.291-.242.562-.025a2.371,2.371,0,0,1,.488.737c.123.192.277.168-.167.539s-.735.866-1.081,1.207a19.194,19.194,0,0,1-1.717,1.665c-.315.173-.853.717-1.2.594a3.964,3.964,0,0,1-1.155-1.157c-.148-.247-.488-.954-.562-1.127a6.66,6.66,0,0,1-.444-.811c-.093-.3-.241-.322-.537-.272a2.61,2.61,0,0,1-.908.074c-.39-.049-4.642-.722-4.988-.87a1.876,1.876,0,0,1-.745-.767,10.089,10.089,0,0,0-1.387-1.251c-.272-.2.127-.573.646-.944a13.765,13.765,0,0,1,3.681-1.424,20.873,20.873,0,0,1,3.187-.247c.587.025,1.89.025,2.3.074S451.3,474.12,451.394,474.194Z" transform="translate(-440.778 -474.066)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6649" data-name="Group 6649" transform="translate(-1299.489 1787.08)">
      <path id="Path_1583" data-name="Path 1583" d="M441.77,544.733s-.828.3-1.507.587-1.754,1.064-2.6,1.572A3.641,3.641,0,0,0,436.416,548c-.167.216-.432.446-.753.786a1.889,1.889,0,0,0-.445,1c0,.124.2.172.476.309s.723.477.846.384.234.031.525.154.921.755,1.2.786,1.26.062,1.476.062.167-.384-.185-.508a1.313,1.313,0,0,1-.859-.711,2.146,2.146,0,0,1-.247-1.139c.062-.278.432-.446.785-.662s1.675-.786,2.138-1.033a12.05,12.05,0,0,1,1.322-.57c.247-.093-.044-.649-.383-1.281A4.659,4.659,0,0,0,441.77,544.733Z" transform="translate(-435.217 -544.733)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6650" data-name="Group 6650" transform="translate(-1332.343 1789.542)">
      <path id="Path_1584" data-name="Path 1584" d="M158.34,566.762a5.816,5.816,0,0,0-2.594,1.71,14.732,14.732,0,0,1-2.966,2.634,13.079,13.079,0,0,1-2.9,1.7,4.478,4.478,0,0,1-1.535-.234,2.374,2.374,0,0,1-.956-.913c-.069-.184-.025.232.184.463a2.422,2.422,0,0,0,1.062.669c.368.071.669.186.831.186s-.439.208-.669.208a4.428,4.428,0,0,1-1.408-.855c-.253-.254-.207-.069.023.162a3.1,3.1,0,0,0,.508.417,3.683,3.683,0,0,1-.97-.463,6.619,6.619,0,0,0-1.452-.6,12.905,12.905,0,0,0-1.569-.231c-.278,0-1.383.046-1.592.092s.206.115.553.069a6.908,6.908,0,0,1,1.224-.023c.483.023.717.346.531.254s-1.2-.232-.761-.069.577.232.23.325a2.565,2.565,0,0,0-.761.346,1.092,1.092,0,0,1-.807.092c-.3-.046-.025.14.3.232a2.581,2.581,0,0,1,.762.369,8.239,8.239,0,0,0,2.123.74c.461.023.438.138.9.138s.393.16-.23.185a5.558,5.558,0,0,1-1.778-.161,6.416,6.416,0,0,0-1.153-.346c-.276-.046-.322-.117.485.254s-.23.069-.577-.046a3.652,3.652,0,0,0-1.038-.184c-.278,0,.206.069.529.138s1.5.648,1.893.786a3.342,3.342,0,0,0,.761.138c.345.069-.924,0-1.27-.115s-1.337-.6-1.707-.671a5.379,5.379,0,0,0-.623-.069c-.161-.023.232.138.554.184a6.071,6.071,0,0,1,1.291.532,3.15,3.15,0,0,0,.947.346,4,4,0,0,1-1.062-.069c-.345-.092-1.016-.369-1.385-.508s-.738-.069-.23.069a17.819,17.819,0,0,0,2.077.671c.368,0,.967.046.483.231a3.691,3.691,0,0,1-1.638-.069,5.225,5.225,0,0,1-.785-.486c-.253-.138-.278-.091.092.163a3.916,3.916,0,0,0,1.663.6s.508-.047.138.069a7.734,7.734,0,0,0-1.8.786,6.722,6.722,0,0,0-.992.926c-.138.231.14,0,.439-.278a6.629,6.629,0,0,1,1.13-.832c.278-.138.809-.3,1.039-.392s.76-.162-.071.231a9.8,9.8,0,0,0-1.96,1.2c.414-.185,1.154-.671,1.454-.763a5.454,5.454,0,0,1,1.13-.392c.3,0,.645.254.923.254a10.813,10.813,0,0,0,2.215.046,6.154,6.154,0,0,0,1.985-.9c.322-.231.438-.071.115.208a4.887,4.887,0,0,1-1.155.74,2.466,2.466,0,0,1-1.153.163c-.508-.023-1.385-.161-.831.046a2.691,2.691,0,0,0,1.015.208c.138,0-.393.231-.715.3a22.714,22.714,0,0,0-2.353.555,2.611,2.611,0,0,0-.761.394c-.322.231-.071.255.138.092a1.991,1.991,0,0,1,.832-.394c.391-.092,1.8-.3,2.422-.346a9.972,9.972,0,0,0,2.168-.648,4.66,4.66,0,0,0,1.362-.694c.253-.254.391-.277.253-.023s-.528.44-.714.6.391,0,.553-.185.508-.485.669-.717.923-.857.715-.394a5.217,5.217,0,0,1-1.339,1.341,4.894,4.894,0,0,1-1.383.625,3.622,3.622,0,0,1-.855.115c-.345.023.3.116.6.047a5.735,5.735,0,0,0,1.477-.486c.485-.277.854-.531.854-.531a4.579,4.579,0,0,1-.669.646,3.254,3.254,0,0,1-.946.463c-.461.115-.071.185.276.046a2.476,2.476,0,0,0,1.016-.509,7.6,7.6,0,0,0,.854-.809c.138-.231.439-.9.715-1.294a6,6,0,0,1,.923-1.2,1.787,1.787,0,0,1,.646-.323c.115,0,.048.486-.207.855A10.545,10.545,0,0,1,153,576.1a4.349,4.349,0,0,1-.785.531c-.276.163-.576.371-.345.324a2.08,2.08,0,0,0,.784-.255c.23-.161.09.117-.163.348s-.668.577-.345.438.438-.044.854-.531.9-1.363,1.224-1.71.715-1,.923-1.157,1.062-.206,1.339-.369a7.4,7.4,0,0,0,1.061-.855c.115-.14-.23-2.427-.023-3.213S158.34,566.762,158.34,566.762Z" transform="translate(-141.241 -566.762)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6651" data-name="Group 6651" transform="translate(-1321.112 1795.665)">
      <path id="Path_1588" data-name="Path 1588" d="M243.233,621.759a6.206,6.206,0,0,0-.992,1.224c-.253.417-.344.6-.461.786s0,.048.438-.484a14.362,14.362,0,0,1,1.061-1.272C243.626,621.689,243.694,621.3,243.233,621.759Z" transform="translate(-241.738 -621.548)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6652" data-name="Group 6652" transform="translate(-1318.828 1796.122)">
      <path id="Path_1589" data-name="Path 1589" d="M263.394,625.786c-.439.3-1.488.644-1.154.555a2.426,2.426,0,0,0,1.016-.323C263.833,625.693,263.833,625.484,263.394,625.786Z" transform="translate(-262.175 -625.64)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6653" data-name="Group 6653" transform="translate(-1306.091 1797.746)">
      <path id="Path_1590" data-name="Path 1590" d="M378.98,642.064c0,.849.279,4.4.279,5.439a6.429,6.429,0,0,0,1.353,3.4c.532.469,5.075,4.031,5.707,4.31a4.9,4.9,0,0,1,2.29,1.509c.7.939.405.786-.658.786a8.282,8.282,0,0,1-1.843-.049c-.163-.1-.38-.369-.3-.584a.424.424,0,0,0-.122-.474,5.833,5.833,0,0,0-.5-.365c-.095-.054-8.467-6.733-8.876-7.018s.042-4.841.083-6.163.084-2.266.084-2.517.033-.259.492.2A23.177,23.177,0,0,0,378.98,642.064Z" transform="translate(-376.141 -640.165)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6654" data-name="Group 6654" transform="translate(-1285.09 1787.829)">
      <path id="Path_1591" data-name="Path 1591" d="M577.075,553.613s-4.235-.65-4.957-.773-4.865-.831-5.587-.933-1.212-.246-1.79-.348-.66-.123-.66-.123l-.02.287s.2.076.823.266,2.143.374,2.681.5,2.476.477,3.259.6,3.335.415,3.688.518,1.612.307,1.816.328.725.05.725.05Z" transform="translate(-564.061 -551.435)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6655" data-name="Group 6655" transform="translate(-1288.392 1779.274)">
      <path id="Path_1592" data-name="Path 1592" d="M538.651,475.055a.815.815,0,0,1-.174.6c-.12.077-.119.126,0,.4s.163.4.205.518-.011.236-.2.206-.305-.021-.31.146.007.246-.086.263-.141.066-.08.142-.039.114-.09.2,0,.266,0,.329a.248.248,0,0,1-.275.2c-.17-.021-.589-.083-.722-.085a8.492,8.492,0,0,0-.86-.015c-.122.028-.591-.626-.891-.912a3.507,3.507,0,0,1-.53-1.128c-.316-.658.081-.424.373-.613a1.163,1.163,0,0,1,1-.232c.434.1,1.112-.182,1.523-.185A6.041,6.041,0,0,1,538.651,475.055Z" transform="translate(-534.519 -474.885)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6656" data-name="Group 6656" transform="translate(-1289.121 1776.481)">
      <path id="Path_1593" data-name="Path 1593" d="M532.588,450.817a2.82,2.82,0,0,0-1.949-.924,2.733,2.733,0,0,0-1.373.387,2.188,2.188,0,0,0-.892,1.164c-.187.531-.054.662-.239,1.061s-.237.348.109.57.461.411.677.233a1.672,1.672,0,0,1,.949-.379,1.53,1.53,0,0,0,.854.052,5.665,5.665,0,0,1,1.551-.04c.261.037.775.049.849.072a4.415,4.415,0,0,0-.082-1.223A2.017,2.017,0,0,0,532.588,450.817Z" transform="translate(-527.988 -449.893)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
    <g id="Group_6657" data-name="Group 6657" transform="translate(-1285.354 1777.83)">
      <path id="Path_1594" data-name="Path 1594" d="M563.108,462.046a1.494,1.494,0,0,1,.172.879c-.067.292-.14.276-.323.25a4.764,4.764,0,0,1-.993-.189c-.2-.1-.4-.39-.157-.612a1.538,1.538,0,0,1,.823-.394C562.886,461.954,563.024,461.928,563.108,462.046Z" transform="translate(-561.695 -461.958)" fill="#6abf4b" fill-rule="evenodd"/>
    </g>
  </g>
</svg>
