import React, { Component, createRef } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
  Typography,
} from "@mui/material";
import Select from "react-select";
import axiosInstance from "../../../helpers/Axios";
import { ActionMessage, Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import Pagination from '@mui/material/Pagination';
// import { ActionMessage } from "../../../library/common/components";\

export default class ReferralDetail extends Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      referralData: [],
      reviewsCount: 0,
      isLoading: false,
      selectedReviewStatus: null,
      selectedModalNewsStatus: null,
      currentPage: 1,
      rowPerPage: 10,
      totalCount: 0,
      // offset: 0,
      itemToDelete: null,
      isModalOpen: false,
      totalPage: 0,
    };
  }

  componentDidMount() {
    this.fetchAllReferral();
  }

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  handleReviewStatusChange = (e) => {
    this.setState({
      selectedReviewStatus: e.label,
      currentPage: 1,
    });
    // this.fetchAllReviews(1, e.label);
  };

  async fetchAllReferral() {
    this.setState({ isLoading: true });
    const { userIdReferral } = this.props;
    try {
      let passApi = `/referralCode/detail/${userIdReferral}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          referralData: data?.result,
          isLoading: false,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
    // this.fetchAllReviews(page, this.state.selectedReviewStatus);
  };

  render() {
    var {
      messageBox,
      isLoading,
      referralData,
      selectedReviewStatus,
      selectedModalNewsStatus,
      isModalOpen,
      currentPage,
      rowPerPage,
      totalPage,
      totalCount,
    } = this.state;

    var { userDetailReferral } = this.props;

    const pageNumbers = [];
    let currentPageRow = userDetailReferral?.creditInfo;

    if (userDetailReferral?.length > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = reviewsData?.slice(indexOfFirstTodo, indexOfLastTodo);

      for (
        let i = 1;
        i <= Math.ceil(userDetailReferral?.length / rowPerPage);
        i++
      ) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} style={{ marginTop: "10px" }}>
            <Paper className="pageWrapper api-provider table-height modalTable">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              {isLoading ? (
                <Box className="message tablePadding">
                  <Loader />{" "}
                </Box>
              ) : referralData?.length > 0 ? (
                <>
                  {" "}
                  <TableContainer>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow className="table-rows listTable-Row">
                          <TableCell>Id</TableCell>
                          <TableCell>User</TableCell>
                          <TableCell>Referral Type</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {referralData?.map((data, i) => (
                          <TableRow
                            key={i}
                            className="table-rows listTable-Row"
                          >
                            <TableCell>{data?.id}</TableCell>
                            <TableCell>
                              {data?.firstName + " " + data?.lastName}
                            </TableCell>
                            <TableCell>{data?.referType}</TableCell>
                          </TableRow>
                        ))}
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination reviewPagination">
                              {/* <button
                              className={
                                bookkeeper.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                bookkeeper.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                              {/* <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  totalCount / rowPerPage > 1 ? false : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={totalPage}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              /> */}
                              {/* <button
                              className={
                                bookkeeper.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                bookkeeper.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <ShowModal
                    isModalOpen={isModalOpen}
                    onClose={this.toggleModal}
                    Content="Are you sure you want to delete?"
                    onOkayLabel="Yes"
                    onOkay={this.deleteItem}
                    onCancel={this.toggleModal}
                  />
                </>
              ) : (
                <Box className="message tablePadding">No Data Available</Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </>
    );
  }
}
