import React, { useState } from 'react';
import * as $ from 'jquery';
import { Button, Loader } from 'library/common/components';
import ReactPlayer from 'react-player';

const ModalVideo = ({ closeModalFunction, playVideo, videoLink }) => {
  const [loading, setLoading] = useState(false);
  const closeModal = () => {
    setLoading(false);
    $('#videoModal').modal('hide');
    closeModalFunction();
  };

  return (
    <div
      className="modal fade"
      data-backdrop="static"
      id="videoModal"
      tabIndex="-1"
      role="dialog"
      aria-labelledby="exampleModalCenterTitle"
      aria-hidden="true">
      <div className="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div className="modal-content rounded-0">
          <div className="modal-header">
            <h5 className="modal-title" id="exampleModalCenterTitle">
              Help
            </h5>
            {loading && <Loader />}
            <button type="button" className="close" data-dismiss="modal" aria-label="Close" onClick={closeModal}>
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div className="modal-body">
            <div className=" video-wrapper">
              <ReactPlayer
                url={videoLink}
                className="react-player"
                controls={true}
                width="100%"
                height="100%"
                playing={playVideo}
                fileConfig={{ forceAudio: true }}
              />
            </div>
            <Button value="Close" icon="fa-times-circle" styleClass="btn-danger mr-3" onClick={closeModal} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModalVideo;
