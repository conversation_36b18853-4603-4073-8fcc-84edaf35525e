import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import CreateAnimal from "./CreateAnimal";
// import { showVariations } from "../../helpers/common";
import Pagination from '@mui/material/Pagination';
import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
import { ReactSVG } from "react-svg";

class Animal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      animals: [],
      allGender: [],
      allColour: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      searchInput: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
    };
  }

  componentDidMount() {
    this.fetchAllAniaml();
    this.fetchAllGender();
    this.fetchAllColour();
  }

  fetchAllAniaml = async () => {
    const { status, data } = await axiosInstance.get(URLS.animal);
    if (status === 200) {
      this.setState({ animals: data.result });
    }
  };

  async fetchAllGender() {
    const { status, data } = await axiosInstance.get(URLS.animalGender);
    if (status === 200) {
      this.setState({ allGender: data.result });
    }
  }

  getGender = (id) => {
    let { allGender } = this.state;
    let name = allGender
      .filter((obj) => obj.id === id)
      .map((object) => object.name);
    return name;
  };

  async fetchAllColour() {
    const { status, data } = await axiosInstance.get(URLS.animalColour);
    if (status === 200) {
      this.setState({ allColour: data.result });
    }
  }

  getColour = (id) => {
    let { allColour } = this.state;
    let name = allColour
      .filter((obj) => obj.id === id)
      .map((object) => object.name);
    return name;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllAniaml();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.animal}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllAniaml();
        });
        this.setActionMessage(true, "Success", "Animal Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, animals } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < animals.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  render() {
    var {
      animals,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      searchInput,
    } = this.state;
    const pageNumbers = [];

    searchInput !== "" &&
      (animals = animals?.filter(
        (obj) =>
          obj?.name
            ?.toString()
            .toLowerCase()
            .includes(searchInput.toString().toLowerCase()) ||
          obj?.variation
            ?.toString()
            .toLowerCase()
            .includes(searchInput.toString().toLowerCase())
      ));

    let currentPageRow = animals;

    if (animals?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = animals.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(animals.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12}>
            <Paper className="pageWrapper">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}

              <Grid container direction="row" alignItems="space-around">
                <Grid item xs={8}>
                  <h3 className="text-left">Animal</h3>
                </Grid>
                <Grid item xs={2}>
                  <input
                    type="text"
                    className=""
                    placeholder="search"
                    value={searchInput}
                    onChange={(e) =>
                      this.setState({
                        ...this.state.searchInput,
                        searchInput: e.target.value,
                      })
                    }
                    style={{
                      fontSize: "16px",
                      borderRadius: "3px",
                      minHeight: "40px",
                      border: "1px solid #ddd",
                      paddingLeft: "10px",
                    }}
                  />
                </Grid>
                <Grid item xs={2}>
                  <ButtonComponent
                    className="addButton admin-btn-green"
                    onClick={this.inputModal(null, "create")}
                    color="primary"
                    value="Add New"
                  />
                </Grid>
              </Grid>
              {isLoading && <Loader />}
              {!isLoading && animals.length === 0 && <p>No Data Available</p>}
              {!isLoading && animals.length > 0 && (
                <>
                  <TableContainer component={Paper}>
                    <Table
                      className="listTable api-provider-listTable"
                      aria-label="simple table"
                    >
                      <TableHead>
                        <TableRow>
                          <TableCell>DID</TableCell>
                          <TableCell>Name</TableCell>
                          <TableCell>Variation</TableCell>
                          <TableCell>Runner Number</TableCell>
                          <TableCell>Age</TableCell>
                          <TableCell>Gender</TableCell>
                          <TableCell>Colour</TableCell>
                          <TableCell>Silk Colour</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {currentPageRow?.map((animal, i) => (
                          <TableRow key={i}>
                            <TableCell>{animal.id}</TableCell>
                            <TableCell>{animal.name}</TableCell>
                            <TableCell>
                              {/* {showVariations(animal.variation)} */}
                              {animal.variation}
                            </TableCell>
                            <TableCell>{animal.runnerNumber}</TableCell>
                            <TableCell>{animal.age}</TableCell>
                            <TableCell>{animal.genderId}</TableCell>
                            <TableCell>{animal.colourId}</TableCell>
                            <TableCell>{animal.silkColour}</TableCell>
                            <TableCell>
                              <EditIcon
                                onClick={this.inputModal(animal.id, "edit")}
                                color="primary"
                                className="mr10 cursor iconBtn admin-btn-green"
                              />
                              <DeleteOutlineIcon
                                onClick={this.setItemToDelete(animal.id)}
                                color="secondary"
                                className="mr10 cursor iconBtn admin-btn-orange"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <div className="tablePagination">
                    <button
                      className={
                        animals.length / rowPerPage > 1
                          ? "btn-navigation"
                          : "btn-navigation-disabled"
                      }
                      disabled={animals.length / rowPerPage > 1 ? false : true}
                      onClick={() => this.handlePaginationButtonClick("prev")}
                    >
                      <ReactSVG src={arrowLeft} />
                    </button>
                    <Pagination
                      hideNextButton
                      hidePrevButton
                      disabled={animals.length / rowPerPage > 1 ? false : true}
                      page={currentPage}
                      onChange={this.handlePaginationClick}
                      count={pageNumbers[pageNumbers?.length - 1]}
                      siblingCount={2}
                      boundaryCount={1}
                      size="small"
                    />
                    <button
                      className={
                        animals.length / rowPerPage > 1
                          ? "btn-navigation"
                          : "btn-navigation-disabled"
                      }
                      disabled={animals.length / rowPerPage > 1 ? false : true}
                      onClick={() => this.handlePaginationButtonClick("next")}
                    >
                      <ReactSVG src={arrowRight} />
                    </button>
                  </div>
                </>
              )}
            </Paper>

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Animal" : "Edit Animal"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateAnimal
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  allGender={this.state.allGender}
                  allColour={this.state.allColour}
                  fetchAllAniaml={this.afterChangeRefresh}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Animal;
