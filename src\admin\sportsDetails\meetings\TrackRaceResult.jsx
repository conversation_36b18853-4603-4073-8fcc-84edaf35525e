import React, { useState, useEffect } from "react";
import { TableCell, Chip } from "@mui/material";
import withStyles from '@mui/styles/withStyles';
import axiosInstance from "../../../helpers/Axios";
// import { EventEmitter } from "../../../../services/event";
import moment from "moment";
import {
  Link,
  // useLocation,
  // useParams,
  // useSearchParams,
} from "react-router-dom";

const StyledTableCell = withStyles((theme) => ({
  head: {
    backgroundColor: "transparent",
    fontWeight: 400,
  },
  body: {
    borderBottom: "none",
  },
}))(TableCell);

const TrackRaceResult = ({
  race,
  race_obj,
  isMobile,
  intl,
  meetingsDetails,
}) => {
  const [winValue, setwinValue] = useState("");
  const [isLoading, setisLoading] = useState(false);

  let raceResultSummaryData = race_obj?.RaceResultSummary?.summary
    ? JSON.parse(race_obj?.RaceResultSummary?.summary)
    : [];
  let resultData = raceResultSummaryData?.filter(
    (obj) => obj?.Position == 1 || obj?.Position == 2 || obj?.Position == 3
  );
  useEffect(() => {
    let current_time_string = moment().format("YYYY/MM/DD HH:mm:ss");
    let end_time_string = moment(race_obj?.startTimeDate).format(
      "YYYY/MM/DD HH:mm:ss"
    );
    let diff_sec = moment(end_time_string).diff(current_time_string, "second");

    if (diff_sec < -840) {
      // fetchFinishedRaceWin(race_obj?.id);
    } else {
      let winRes = setInterval(() => {
        let current_time_string = moment().format("YYYY/MM/DD HH:mm:ss");
        let diff_sec = moment(end_time_string).diff(
          current_time_string,
          "second"
        );
        if (diff_sec < -840) {
          // fetchFinishedRaceWin(race_obj?.id);
          clearInterval(winRes);
        }
      }, 1000);
    }
  }, []);

  // GET RACE RESULT
  const fetchFinishedRaceWin = async (id) => {
    setisLoading(true);
    const { status, data } = await axiosInstance.get(
      `events/getRaceWinner/${id}`
    );
    if (status === 200) {
      let filtered_result = data?.result.filter((obj) => obj !== null);

      let result_value = "";
      let dumy = filtered_result?.map((obj) => {
        result_value = result_value === "" ? obj : result_value + "," + obj;
      });

      setwinValue(result_value);
      setisLoading(false);
    }
  };

  var sportsName =
    race_obj?.sportId === 1
      ? "Horse Racing"
      : race_obj?.sportId === 2
        ? "Harness Racing"
        : race_obj?.sportId === 3
          ? "Greyhound Racing"
          : "";
  var navigatePath =
    race_obj &&
    meetingsDetails &&
    `/${sportsName}/${race_obj?.sportId}/${meetingsDetails?.eventName}/${race_obj?.eventId}/runners/${race_obj.id}`;

  return isMobile === false ? (
    <>
      <Link to={navigatePath}>
        <StyledTableCell
          align="center"
          className={
            resultData?.length > 0
              ? "paying"
              : "upcoming_race_cell_close interim"
          }
        // style={{ backgroundColor: "#bfccd8" }}
        // onClick={
        //   race_obj?.startTimeDate === null
        //     ? () => {}
        //     : () =>
        //         navigate(
        //           Routes.RunnerDetails(
        //             race?.sportId === 1
        //               ? "horse"
        //               : race?.sportId === 2
        //               ? "harness"
        //               : "greyhounds",
        //             race?.sportId,
        //             race_obj?.id,
        //             race?.trackId,
        //             race?.id,
        //             race_obj?.startTimeDate,
        //             intl
        //           )
        //         )
        // }
        >
          {resultData?.length > 0 ? (
            <Chip
              className={"singlerace-count-chip"}
              style={{
                backgroundColor: "transparent",
                cursor: "pointer",
              }}
              size="small"
              label={resultData.map((obj, index) => (
                <span>
                  {obj?.RunnerNumber}
                  {index != 2 ? "," : ""}
                </span>
              ))}
            />
          ) : (
            <Chip
              className={"singlerace-count-chip"}
              style={{
                backgroundColor: "transparent",
                cursor: "pointer",
              }}
              size="small"
              label={<span style={{ color: "red" }}>{"Closed"}</span>}
            />
          )}
        </StyledTableCell>
      </Link>
    </>
  ) : (
    <td className={winValue === "" ? "upcoming_race_cell_close" : ""}>
      {winValue !== "" ? (
        <Chip
          className={"singlerace-count-chip"}
          style={{
            backgroundColor: "transparent",
          }}
          size="small"
          label={<span>{winValue}</span>}
        />
      ) : (
        <Chip
          className={"singlerace-count-chip"}
          style={{
            backgroundColor: "transparent",
          }}
          size="small"
          label={<span>{"mmm"}</span>}
        />
      )}
    </td>
  );
};

export default TrackRaceResult;
