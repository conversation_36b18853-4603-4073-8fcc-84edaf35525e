import React, { Component } from "react";
import {
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Box,
} from "@mui/material";
import moment from "moment";
import "./errorLogs.scss";
import Pagination from "@mui/material/Pagination";

export class Inprogresstable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
    };
  }
  handlePaginationClick = (event, page) => {
    let {
      // rowPerPage,
      // offset
    } = this.state;
    this.props.currentPagechange(
      Number(page),
      (Number(page) - 1) * this.props.rowPerPage
    );
    // this.setState({
    //   currentPage: Number(page),
    //   offset: (Number(page) - 1) * rowPerPage,
    // });
  };
  render() {
    let { isLoading } = this.state;
    let { sportCount, currentPage, rowPerPage } = this.props;
    const pageNumbers = [];
    if (sportCount > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = tracksDetails.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    let isinProgresss = this.props?.inProgerssNewData?.filter((item) => {
      return item?.ReportMeetings?.errors?.length > 0;
    });

    return (
      <>
        <Box>
          {!isLoading && isinProgresss?.length === 0 && (
            <p>No Data Available</p>
          )}
          {!isLoading && isinProgresss?.length > 0 && (
            <TableContainer component={Paper}>
              <Table
                className="listTable api-provider-listTable"
                aria-label="simple table"
              >
                <TableHead>
                  <TableRow className="tableHead-row">
                    {/* <TableCell>SportId</TableCell> */}
                    <TableCell>ProviderId</TableCell>
                    {/* <TableCell>Status</TableCell>
                    <TableCell>Value To Insert</TableCell> */}
                    <TableCell>Description</TableCell>
                    {/* <TableCell>Import Type</TableCell> */}
                    <TableCell>Error date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow className="table_row">
                    <TableCell
                      colSpan={100}
                      className="table-seprator"
                    ></TableCell>
                  </TableRow>
                  {this.props?.inProgerssNewData?.map((item) => {
                    return (
                      <>
                        {item?.ReportMeetings?.map((obj) => {
                          return (
                            <>
                              {obj?.errors?.map((data) => {
                                return (
                                  <>
                                    <TableRow className="listTable-Row">
                                      {/* <TableCell>{data?.sportId}</TableCell> */}
                                      <TableCell>{item?.providerId}</TableCell>
                                      {/* <TableCell>{data?.status}</TableCell> */}
                                      {/* <TableCell>
                                        {data?.valueToInsert}
                                      </TableCell> */}
                                      <TableCell className="pre-whitespace">
                                        {data?.message}
                                      </TableCell>
                                      {/* <TableCell>{data?.importType}</TableCell> */}
                                      <TableCell>
                                        {" "}
                                        {moment(data.createdAt).format(
                                          "DD/MM/YYYY "
                                        )}
                                      </TableCell>
                                    </TableRow>{" "}
                                  </>
                                );
                              })}{" "}
                            </>
                          );
                        })}{" "}
                      </>
                    );
                  })}
                  <TableRow>
                    <TableCell colSpan={100} className="pagination">
                      <div className="tablePagination">
                        {/* <button
                              className={
                                offset >= 20
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={offset >= 20 ? false : true}
                              // disabled={
                              //   meetingsDetails.length / rowPerPage > 1 ? false : true
                              // }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                        <Pagination
                          hideNextButton
                          hidePrevButton
                          disabled={sportCount > 0 ? false : true}
                          page={currentPage}
                          onChange={this.handlePaginationClick}
                          count={pageNumbers[pageNumbers?.length - 1]}
                          siblingCount={2}
                          boundaryCount={1}
                          size="small"
                        />
                        {/* <button
                              className={
                                rowPerPage + offset < sportCount
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                rowPerPage + offset < sportCount ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Box>
      </>
    );
  }
}

export default Inprogresstable;
