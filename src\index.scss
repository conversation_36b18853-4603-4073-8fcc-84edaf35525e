@import './assets/scss/_variables.scss';
@import './assets/scss/_global.scss';
@import './assets/scss/_styles.scss';
@import './assets/scss/_responsive.scss';

* {
  outline: none;
}

a {
  color: inherit;
}

.media-files {
  width: 150px;
  height: 150px;
  object-fit: cover;
  object-position: center;
  margin: 0 20px 20px 0;
  cursor: pointer;
}

.paper {
  width: 400;
  background-color: #F5F5F5 !important;
  padding: 20px;
}

.modal {
  display: 'flex';
  padding: 10px;
  align-items: 'center';
  justify-content: 'center';
  position: 'absolute';
  width: 400px;
  height: auto;
  top: 20% !important;
  // left: 45% !important;
  margin-left: 0px auto !important;
}

.copied-url {
  padding: 0 20px;
  font-style: italic;
}

.header-user {
  display: flex;
  align-items: center;
}

.notification-icon {
  margin-right: 12px;
  cursor: pointer;

  svg {
    path {
      fill: #4455c7;
    }
  }
}

.logout-icon {
  background: none;
  border: none;
  color: #003764;
  cursor: pointer;
}

.margin-left-10 {
  margin-left: 10px !important;
}

.list-image {
  width: auto !important;
  height: 60px;
  object-fit: cover;
  object-position: center;
}

.submit-button {
  background-color: #6abf4b !important;
  border: none;
  padding: 10px 30px;
  margin-top: 15px;
  color: #fff;
  font-size: 19px;
  font-weight: bold;
  border-radius: 6px;
}

.cursor {
  cursor: pointer;
}

.register-button {
  .btn {
    padding: 5px 25px !important;
  }
}