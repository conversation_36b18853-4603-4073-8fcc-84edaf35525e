import React from "react";
import {
    Grid,
    Paper,
    TableContainer,
    Table,
    TableHead,
    TableRow,
    TableCell,
    TableBody,
    Modal,
    Box,
    Breadcrumbs,
    Typography,
    Button,
    TextField,
    InputAdornment,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../helpers/Axios";
import moment from "moment-timezone";
// import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from '@mui/material/Pagination';
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

// import "../cricket.scss";
const SelectedEventTypeOption = [
    {
        label: "active",
        value: 0
    },
    {
        label: "inactive",
        value: 1
    }
]
let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
class AdvertisingScreen extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            isLoading: false,
            EventData: [],
            SelectedEvent: "",
            SelectedEventType: "",
            errorEvent: "",
            errorEventType: "",
            messageBox: {
                display: false,
                type: "",
                message: "",
            },
        };
    }

    componentDidMount() {
        this.fetchAllEvents();
    }
    componentDidUpdate(prevProps, prevState) {
        // if (prevState.SelectedEvent !== this.state.SelectedEvent) {
        //     this.fetchAllCronJobs();
        // }
    }

    fetchAllEvents = async () => {
        this.setState({
            isLoading: true,
        });
        const passApi = `v2/events/trackList/?todate=${moment().format("YYYY-MM-DD")}&sportId=1,2,3&MeetingState=Aus/NZ,Intl&countryId=&stateId=&timezone=${timezone}`
        const { status, data } = await axiosInstance.get(passApi);
        if (status === 200) {
            let newdata = [];
            let categories = data?.events?.map((item) => {
                newdata.push({
                    label: item?.eventName,
                    value: item?.trackId,
                });
            });
            const sortedData = newdata?.sort((a, b) => {
                return a?.label.localeCompare(b?.label);
            });
            let finalData = _.uniqBy(sortedData, function (e) {
                return e.value;
            });

            this.setState({
                EventData: finalData,
            });
            const selectedEvent = await axiosInstance.get('ads/ad/admin/setting');
            if (selectedEvent?.status === 200) {
                let id = selectedEvent?.data?.result?.trackId;
                let status = selectedEvent?.data?.result?.status;
                let selectedData = finalData?.filter((obj) => obj?.value === id)
                this.setState({
                    isLoading: false,
                });
                if (selectedData?.length > 0) {
                    this.setState({
                        SelectedEvent: selectedData?.[0]?.value,
                        SelectedEventType: status
                    });
                }
            }
        }
    };
    handalValidate = () => {

        const { SelectedEvent, SelectedEventType } = this.state
        let flag = true;
        if (SelectedEvent === "") {
            flag = false;
            this.setState({
                errorEvent: "This field is mandatory",
            });
        } else {
            this.setState({
                errorEvent: "",
            });
        }
        if (SelectedEventType === "") {
            flag = false;
            this.setState({
                errorEventType: "This field is mandatory",
            });
        } else {
            this.setState({
                errorEventType: "",
            });
        }
        return flag;
    };
    handleSave = async () => {
        const { SelectedEvent, SelectedEventType } = this.state

        if (this.handalValidate()) {
            let payload = {
                trackId: SelectedEvent,
                status: SelectedEventType
            };
            const { status } = await axiosInstance.post(`ads/ad/admin/setting`, payload);
            if (status === 200) {
                this.setActionMessage(
                    true,
                    "Success",
                    `Advertising Event update Successfully`
                );
            }
        }
    };
    setActionMessage = (display = false, type = "", message = "") => {
        this.setState({ messageBox: { display, type, message } }, () =>
            setTimeout(
                () =>
                    this.setState({
                        messageBox: { display: false, type: "", message: "" },
                    }),
                3000
            )
        );
    };
    render() {
        var {
            EventData,
            SelectedEvent,
            SelectedEventType,
            isLoading,
            errorEventType,
            errorEvent,
            messageBox
        } = this.state;
        return (
            <>
                <Grid container className="page-content adminLogin">
                    <Grid item xs={12} className="pageWrapper">
                        {/* <Paper className="pageWrapper"> */}
                        {messageBox.display && (
                            <ActionMessage
                                message={messageBox.message}
                                type={messageBox.type}
                                styleClass={messageBox.styleClass}
                            />
                        )}
                        <Box className="bredcrumn-wrap">
                            <Breadcrumbs
                                separator="/"
                                aria-label="breadcrumb"
                                className="breadcrumb"
                            >
                                <Link underline="hover" color="inherit" to="/dashboard">
                                    Home
                                </Link>
                                <Typography className="active_p">Advertising Screen</Typography>
                            </Breadcrumbs>
                        </Box>
                        <Grid container direction="row" alignItems="center">
                            <Grid item xs={3}>
                                <Typography variant="h1" align="left">
                                    Advertising Screen
                                </Typography>
                            </Grid>
                            <Grid item xs={9} className="admin-filter-wrap">
                                <Select
                                    className="React cricket-select external-select"
                                    classNamePrefix="select"
                                    placeholder="Select Event"
                                    value={
                                        EventData?.find((item) => {
                                            return item?.value == SelectedEvent;
                                        })
                                    }
                                    isLoading={isLoading}
                                    onChange={
                                        (e) => this.setState({
                                            SelectedEvent: e.value
                                        })
                                    }
                                    options={EventData}
                                />
                                {errorEvent ? (
                                    <p
                                        className="errorText"
                                        style={{ margin: "0px 0 0 0" }}
                                    >
                                        {errorEvent}
                                    </p>
                                ) : (
                                    ""
                                )}
                                <Select
                                    className="React cricket-select external-select"
                                    classNamePrefix="select"
                                    placeholder="Select Status"
                                    value={
                                        SelectedEventTypeOption?.find((item) => {
                                            return item?.label == SelectedEventType;
                                        })
                                    }
                                    isLoading={isLoading}
                                    onChange={
                                        (e) => this.setState({
                                            SelectedEventType: e.label
                                        })
                                    }
                                    options={SelectedEventTypeOption}
                                />
                                {errorEventType ? (
                                    <p
                                        className="errorText"
                                        style={{ margin: "0px 0 0 0" }}
                                    >
                                        {errorEventType}
                                    </p>
                                ) : (
                                    ""
                                )}
                                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  // value={search}
                  onChange={(e) => {
                    // setSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />*/}
                                <Button
                                    variant="contained"
                                    style={{
                                        backgroundColor: "#4455c7",
                                        color: "#fff",
                                        borderRadius: "8px",
                                        textTransform: "capitalize",
                                    }}
                                    onClick={() => this.handleSave()}
                                >
                                    Update
                                </Button>
                            </Grid>
                        </Grid>

                        {/* </Paper> */}
                    </Grid>
                </Grid>
            </>
        );
    }
}
export default AdvertisingScreen;

