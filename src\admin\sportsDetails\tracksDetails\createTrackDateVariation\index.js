import React, { Component } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
} from "@mui/material";
import axiosInstance from "../../../../helpers/Axios";
import { Loader } from "../../../../library/common/components";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../../../library/common/components/Button";
import ShowModal from "../../../../components/common/ShowModal/ShowModal";
import DatePicker from "react-datepicker";
import Select from "react-select";
import { URLS } from "../../../../library/common/constants";
import _ from "lodash";
import moment from "moment";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import DateFnsUtils from "@date-io/date-fns";

export class CreateTrackDateVariation extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      isEditMode: false,
      isInputModalOpen: false,
      isModalOpen: false,
      itemToDelete: "",
      seletedValue: "",
      allProvider: [],
      actualTimeDate: null,
      variationTimeDate: null,
      errorActualTimeDate: "",
      errorVariationTimeDate: "",
      errorSeletedValue: "",
    };
  }

  componentDidMount() {
    // this.fetchAllProvider();
    this.fetchAllBookkepperProvider();
  }

  fetchAllProvider = async () => {
    const { status, data } = await axiosInstance.get(URLS.apiProvider);
    if (status === 200) {
      let data_obj = _.orderBy(data?.result, ["id"], ["asc"]);
      let newdata = [];
      let provider = data_obj?.map((item) => {
        newdata.push({
          label: item?.providerName,
          value: item?.id,
        });
      });
      this.setState({ allProvider: newdata });
    }
  };
  fetchAllBookkepperProvider = async () => {
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeepers/`
      );
      if (status === 200) {
        let activeProvide = data?.result?.filter((item) => {
          return item?.status === "active";
        });
        let newdata = [];
        let provider = activeProvide?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.apiProviderId,
          });
        });
        this.setState({ allProvider: newdata, isLoading: false });
      }
    } catch (err) {
      this.setState({ isLoading: false });
    }
  };

  handleActualTimeDate = (date) => {
    this.setState({ actualTimeDate: date });
  };
  handleVariationTimeDate = (date) => {
    this.setState({ variationTimeDate: date });
  };
  inputModal = (id, type, data) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        idToSend: id,
        isEditMode: true,
        seletedValue: data?.providerId,
        actualTimeDate: new Date(data?.actualDate),
        variationTimeDate: new Date(data?.variationDate),
      });
    } else {
      this.setState({
        isEditMode: false,
        seletedValue: "",
        actualTimeDate: null,
        variationTimeDate: null,
      });
    }
  };
  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorActualTimeDate: "",
      errorVariationTimeDate: "",
      errorSeletedValue: "",
    });
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };
  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  handleValidate = () => {
    let { actualTimeDate, variationTimeDate, seletedValue } = this.state;
    let flag = true;

    if (!actualTimeDate) {
      flag = false;
      this.setState({
        errorActualTimeDate: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorActualTimeDate: "",
      });
    }
    if (!variationTimeDate) {
      flag = false;

      this.setState({
        errorVariationTimeDate: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorVariationTimeDate: "",
      });
    }
    if (seletedValue === "") {
      flag = false;

      this.setState({
        errorSeletedValue: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorSeletedValue: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    if (this.handleValidate()) {
      let payload = {
        actualDate: moment.utc(this.state?.actualTimeDate),
        providerId: this.state?.seletedValue,
        variationDate: moment.utc(this.state?.variationTimeDate),
      };

      this.setState({ isLoading: true, isEditMode: false });
      const { status } = await axiosInstance.post(
        `track/track/date/variation/${this.props?.id}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.props.fetchAllTracks();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      }
    }
  };

  handleUpdate = async () => {
    if (this.handleValidate()) {
      let payload = {
        actualDate: moment.utc(this.state?.actualTimeDate),
        providerId: this.state?.seletedValue,
        variationDate: moment.utc(this.state?.variationTimeDate),
      };
      this.setState({ isLoading: true, isEditMode: true });
      const { status } = await axiosInstance.put(
        `track/track/date/variation/${this.state?.idToSend}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.props.fetchAllTracks();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Edited Successfully`
        //   );
      }
    }
  };
  deleteItem = async () => {
    let payload = {
      id: this.state?.itemToDelete,
    };
    try {
      const { status } = await axiosInstance.delete(
        `track/track/date/variation/${this.state?.itemToDelete}`,
        payload
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.props.fetchAllTracks();
        });
        // this.setActionMessage(
        //   true,
        //   "Success",
        //   "Country variation Deleted Successfully!"
        // );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  render() {
    const {
      isLoading,
      isInputModalOpen,
      isEditMode,
      isModalOpen,
      seletedValue,
      allProvider,
      variationTimeDate,
      actualTimeDate,
      errorActualTimeDate,
      errorVariationTimeDate,
      errorSeletedValue,
    } = this.state;

    const { trackDateVariationsData } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Button
            variant="contained"
            style={{
              backgroundColor: "#4455C7",
              color: "#fff",
              borderRadius: "8px",
              textTransform: "capitalize",
              padding: "13px 24px 12px",
              marginLeft: "auto",
              marginBottom: "10px",
            }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button>
          <Grid item xs={12}>
            <Paper className="pageWrapper api-provider">
              {/* {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )} */}
              {isLoading ? (
                <Box className="message">
                  <Loader />
                </Box>
              ) : trackDateVariationsData[0]?.TrackDateVariations?.length >
                0 ? (
                <>
                  {" "}
                  <TableContainer>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow className="table-rows listTable-Row">
                          <TableCell>Id</TableCell>
                          <TableCell style={{ minWidth: "70px" }}>
                            Provider Id
                          </TableCell>
                          <TableCell>Actual Date</TableCell>
                          <TableCell style={{ minWidth: "85px" }}>
                            Variation Date
                          </TableCell>
                          <TableCell style={{ minWidth: "130px" }}>
                            Action
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {trackDateVariationsData[0]?.TrackDateVariations.map(
                          (data) => (
                            <TableRow>
                              <TableCell>{data?.id}</TableCell>
                              <TableCell>{data?.providerId}</TableCell>
                              <TableCell>
                                {moment(data?.actualDate).format("DD/MM/YYYY")}
                              </TableCell>
                              <TableCell>
                                {moment(data?.variationDate).format(
                                  "DD/MM/YYYY"
                                )}
                              </TableCell>
                              <TableCell>
                                <Button
                                  style={{ minWidth: "0px" }}
                                  onClick={this.inputModal(
                                    data?.id,
                                    "edit",
                                    data
                                  )}
                                  className="table-btn edit-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  style={{ minWidth: "0px" }}
                                  onClick={this.setItemToDelete(data?.id)}
                                  className="table-btn delete-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          )
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              ) : (
                <Box className="message">No Variation Data Avilable</Box>
              )}
            </Paper>
          </Grid>
        </Grid>
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={this.toggleInputModal}
        >
          {/* {isLoading && <Loader />} */}
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {!isEditMode
                ? "Create Track Date variation"
                : "Edit Track Date variation"}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleInputModal}
            />

            <Grid
              item
              xs={12}
              className="runnerInfo"
              style={{ display: "flex" }}
            >
              <Grid item xs={6}>
                <div className="track-date-picker">
                  <label>Actual Date</label>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <Grid container>
                      <DesktopDatePicker
                        autoOk
                        disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        placeholder="DD/MM//YYYY"
                        margin="normal"
                        id="date-picker-inline"
                        inputVariant="outlined"
                        value={actualTimeDate ? parseISO(actualTimeDate) : null}
                        onChange={(e) => this.handleActualTimeDate(e)}
                        KeyboardButtonProps={{
                          "aria-label": "change date",
                        }}
                        className="track-picker"
                      />
                    </Grid>
                  </LocalizationProvider>
                  {errorActualTimeDate ? (
                    <p className="errorText" style={{ margin: "0px 0 0 0" }}>
                      {errorActualTimeDate}
                    </p>
                  ) : (
                    ""
                  )}
                </div>
              </Grid>
              <Grid item xs={6}>
                <div className="track-date-picker">
                  <label>Variation Date</label>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <Grid container>
                      <DesktopDatePicker
                        autoOk
                        disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        placeholder="DD/MM//YYYY"
                        margin="normal"
                        id="date-picker-inline"
                        inputVariant="outlined"
                        value={
                          variationTimeDate ? parseISO(variationTimeDate) : null
                        }
                        onChange={(e) => this.handleVariationTimeDate(e)}
                        KeyboardButtonProps={{
                          "aria-label": "change date",
                        }}
                        className="track-picker"
                      />
                    </Grid>
                  </LocalizationProvider>
                  {errorVariationTimeDate ? (
                    <p className="errorText" style={{ margin: "0px 0 0 0" }}>
                      {errorVariationTimeDate}
                    </p>
                  ) : (
                    ""
                  )}
                </div>
              </Grid>
            </Grid>
            <Grid item xs={6}>
              <Box className="select-box">
                <label className="modal-label">Provider Id</label>
                <Select
                  className="React "
                  classNamePrefix="select"
                  menuPosition="fixed"
                  value={allProvider?.find((op) => {
                    return op?.value === seletedValue;
                  })}
                  onChange={(e) =>
                    this.setState({
                      seletedValue: e?.value,
                    })
                  }
                  options={allProvider && allProvider}
                />
                {errorSeletedValue ? (
                  <p className="errorText" style={{ margin: "-14px 0 0 0" }}>
                    {errorSeletedValue}
                  </p>
                ) : (
                  ""
                )}
              </Box>
            </Grid>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={() => this.handleSave()}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      //   disabled={!addInput}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={() => this.handleUpdate()}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      //   disabled={!variationToSend}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={() => this.toggleInputModal()}
                    className="mr-lr-30"
                    value="Back"
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
        <ShowModal
          isModalOpen={isModalOpen}
          onClose={this.toggleModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={this.deleteItem}
          onCancel={this.toggleModal}
        />
      </>
    );
  }
}

export default CreateTrackDateVariation;
