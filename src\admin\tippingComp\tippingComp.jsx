import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Box,
  Typography,
  Breadcrumbs,
  Button,
  FormControl,
  Checkbox,
  TextField,
  Grid,
} from "@mui/material";

import { Link, useLocation, useNavigate } from "react-router-dom";
import _ from "lodash";

import { useParams } from "react-router-dom";
import Select, { components } from "react-select";
import { ReactComponent as LeftArrow } from "../../images/tips/left-arrow.svg";
import { ReactComponent as RightArrow } from "../../images/tips/right-arrow.svg";
import moment from "moment-timezone";
import { ReactComponent as Uncheck } from "../../images/tips/Checkbox.svg";
import { ReactComponent as Check } from "../../images/tips/check.svg";
import { ReactComponent as Minus } from "../../images/tips/counter-minus.svg";
import { ReactComponent as Plus } from "../../images/tips/counter-plus.svg";
import { ReactComponent as Notcheck } from "../../images/tips/notcheck.svg";
import { ReactComponent as BlueCheck } from "../../images/tips/blue-checkbox.svg";
import DefaultImg from "../../images/tips/smartb_default.png";
import { ActionMessage, Loader } from "../../library/common/components";

import { useDispatch, useSelector } from "react-redux";
import "./compTips.scss";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import { config } from "../../helpers/config";
import TippingSportsOdds from "./TippingSportsOdds";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const DropdownIndicator = (props) => {
  return (
    <components.DropdownIndicator {...props}>
      {/* <SelectIndicator /> */}
    </components.DropdownIndicator>
  );
};

function CompTipsPage() {
  const [stepperCount, setStepperCount] = useState(null);
  const [rounds, setRounds] = useState([]);
  const limit = 20;
  const [sports, setSports] = useState([]);
  const [selectedSports, setSelectedSports] = useState(null);
  const [OrgAll, setOrgAll] = useState([]);
  const [selectedOrg, setSelectedOrg] = useState(null);
  const [OrgApiCount, setOrgApiCount] = useState(0);
  const [isOrgSearch, setIsOrgSearch] = useState("");
  const [countOrg, setcountOrg] = useState(0);
  const [searchOrg, setsearchOrg] = useState([]);
  const [searchOrgCount, setsearchOrgCount] = useState(0);
  const [SearchOrgpage, setSearchOrgpage] = useState(0);
  const [pageOrg, setpageOrg] = useState(0);
  const [eventLength, setEventLength] = useState(0);
  const [eventByIdData, setEventByIdData] = useState({});
  const [allEventData, setAllEventData] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [eventsData, setEventsData] = useState(allEventData);
  const [groupedEventData, setGroupedData] = useState({});
  const [counts, setCounts] = useState(0);
  const [isJoker, setIsJoker] = useState(false);
  const [jokerRound, setJockerRound] = useState(null);
  const [completedRound, setCompletedRound] = useState([]);
  const [cutoffDate, setcutoffDate] = useState(null);
  const [lastDate, setLastDate] = useState(null);
  const [atLeastOneError, setAtLeastOneError] = useState("");
  const [isPublicCompLoading, setIsPublicCompLoading] = useState(false);
  const [compPage, setCompPage] = useState(1);
  const [isPublicCompsSearch, setIsPublicCompsSearch] = useState("");
  const [searchPublicComps, setsearchPublicComps] = useState([]);
  const [searchPublicCompsCount, setsearchPublicCompsCount] = useState(0);
  const [SearchPublicCompspage, setSearchPublicCompspage] = useState(1);
  const [allComps, setAllComps] = useState([]);
  const [compCount, setCompCount] = useState(0);
  const [selectedComp, setSelectedComp] = useState(null);
  const [isUserLoading, setIsUserCompLoading] = useState(false);
  const [userData, setUserData] = useState([]);
  const [userCount, setUserCount] = useState(0);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isUserSearch, setIsUserSearch] = useState("");
  const [searchUser, setSearchUser] = useState([]);
  const [searchUserCount, setSearchUserCount] = useState(0);
  const [searchUserpage, setSearchUserpage] = useState(0);
  const [pageUser, setPageUser] = useState(0);
  const [apiTimeout, setApiTimeout] = useState(null);
  const [BookkeeperData, setBookKeeperData] = useState([]);
  const [newRoundDataOption, setNewRoundDataOption] = useState([]);
  const [messageBox, setMessageBox] = useState({
    display: false,
    type: "",
    message: "",
  });
  const rowperPage = 20;

  const convetToGroupData = (eventdata) => {
    const groupedData = _.groupBy(eventdata, (event) =>
      moment(event?.startTime).format("YYYY-MM-DD")
    );
    setGroupedData(groupedData);
  };

  useEffect(() => {
    fetchSportData();
    fetchBookKeeper();
    // fetchAllUsers(0, []);
  }, []);

  useEffect(() => {
    if (allEventData?.length > 0) {
      const modifiedEventData = allEventData?.map((item, index) =>
        index === 0 ? { ...item, margin: true } : item
      );
      convetToGroupData(modifiedEventData);
      setEventsData(modifiedEventData);
      const margin = allEventData?.[0]?.roundData?.margin;
    }
    setAtLeastOneError("");
  }, [allEventData]);

  useEffect(() => {
    if (stepperCount !== null && selectedUser !== null) {
      const getData = setTimeout(() => {
        getEventByID(stepperCount, selectedUser, selectedComp);
      }, 2000);
      setApiTimeout(getData);
      return () => clearTimeout(getData);
    }
  }, [stepperCount]);

  const fetchBookKeeper = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeepers`
      );
      if (status === 200) {
        setBookKeeperData(data?.result);
      } else {
      }
    } catch (err) {}
  };

  const fetchRoundData = async (tID, sID, cID) => {
    const date = moment(Date()).format("YYYY-MM-DD");
    try {
      const { status, data } = await axiosInstance.get(
        `/tipping/rounds?timezone=${timezone}&tournamentId=${tID}&SportId=${sID}&competitionId=${cID}`
      );
      if (status === 200) {
        let newdata = [];
        let track = data?.result?.map((item) => {
          newdata.push({
            label: `${sID == 4 ? `Day` : `Round`} ${Number(item)}`,
            value: item,
          });
        });
        setCompletedRound(data?.completedRound);
        setNewRoundDataOption(
          data?.roundList?.map((round) => ({
            value: round?.round,
            displayName: round?.displayName,
            label: `${
              round?.displayName
                ? round?.displayName
                : (sID === 4 ? "Day" : "Round") + " " + round?.round
            }`,
          }))
        );

        setRounds(data?.result);
        const passRound = data?.current ? data?.current : data?.result?.[0];
        setStepperCount(passRound !== undefined ? passRound : 0);
        selectedUser &&
          passRound !== undefined &&
          getEventByID(stepperCount, selectedUser, cID);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const handleIncrement = () => {
    setCounts(Number(counts) + 1);
  };

  const handleDecrement = (tipId, index) => {
    if (counts > 0) {
      setCounts(Number(counts) - 1);
    }
  };

  const fetchDayName = (date) => {
    var days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    var d = new Date(date);
    var dayName = days[d.getDay()];
    return dayName;
  };

  const getEventByID = async (round, userId, compId, signal) => {
    setLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        `tipping/get/${
          selectedComp || compId
        }?timezone=${timezone}&round=${round}&UserId=${userId}`,
        { signal }
      );
      if (status === 200) {
        setEventByIdData(data?.result);
        const tipSubmitted = data?.events?.some(
          (ele) => ele?.roundPointId !== null
        );
        setEventLength(tipSubmitted ? 1 : 0);
        setIsJoker(data?.result?.joker);
        setJockerRound(data?.result?.joker);
        if (tipSubmitted && data?.result?.tippingType !== "winning") {
          const newArr = data?.events?.map((ele) => {
            const allTeamOddsKey =
              ele?.SportId === 4
                ? "CricketBetOffers"
                : ele?.SportId === 10
                ? "NBABetOffers"
                : ele?.SportId === 15
                ? "AFLBetOffers"
                : ele?.SportId === 9
                ? "ARBetOffers"
                : ele?.SportId === 7
                ? "TennisBetOffers"
                : ele?.SportId === 11
                ? "BaseballBetOffers"
                : ele?.SportId === 17
                ? "IceHockeyBetOffers"
                : ele?.SportId === 6
                ? "BoxingBetOffers"
                : ele?.SportId === 5
                ? "MMABetOffers"
                : ele?.SportId === 8
                ? "SoccerBetOffers"
                : ele?.SportId === 16
                ? "GolfBetOffers"
                : "RLBetOffers";
            const allTeamOddsVal =
              ele?.SportId === 4
                ? ele?.CricketBetOffers
                : ele?.SportId === 10
                ? ele?.NBABetOffers
                : ele?.SportId === 15
                ? ele?.AFLBetOffers
                : ele?.SportId === 9
                ? ele?.ARBetOffers
                : ele?.SportId === 7
                ? ele?.TennisBetOffers
                : ele?.SportId === 11
                ? ele?.BaseballBetOffers
                : ele?.SportId === 17
                ? ele?.IceHockeyBetOffers
                : ele?.SportId === 6
                ? ele?.BoxingBetOffers
                : ele?.SportId === 5
                ? ele?.MMABetOffers
                : ele?.SportId === 8
                ? ele?.SoccerBetOffers
                : ele?.SportId === 16
                ? ele?.GolfBetOffers
                : ele?.RLBetOffers;
            return {
              ...ele,
              [allTeamOddsKey]: {
                awayOdds: {
                  ...allTeamOddsVal?.awayOdds,
                  odd:
                    ele?.awayTeam?.isTip === 1
                      ? ele?.roundData?.selectedOdd
                      : ele?.roundData?.oppositeOdd,
                  point:
                    ele?.awayTeam?.isTip === 1
                      ? ele?.roundData?.selectedPoint
                      : ele?.roundData?.oppositePoint,
                  BookKeeperId:
                    ele?.awayTeam?.isTip === 1
                      ? ele?.roundData?.selectedBookKeeperId
                      : ele?.roundData?.oppositeBookKeeperId,
                },
                homeOdds: {
                  ...allTeamOddsVal?.homeOdds,
                  odd:
                    ele?.homeTeam?.isTip === 1
                      ? ele?.roundData?.selectedOdd
                      : ele?.roundData?.oppositeOdd,
                  point:
                    ele?.homeTeam?.isTip === 1
                      ? ele?.roundData?.selectedPoint
                      : ele?.roundData?.oppositePoint,
                  BookKeeperId:
                    ele?.homeTeam?.isTip === 1
                      ? ele?.roundData?.selectedBookKeeperId
                      : ele?.roundData?.oppositeBookKeeperId,
                },
              },
            };
          });
          setAllEventData(newArr);
        } else {
          setAllEventData(data?.events);
        }
        setLoading(false);

        setCounts(
          data?.events?.[0]?.roundData?.margin
            ? data?.events?.[0]?.roundData?.margin
            : counts
        );
        setcutoffDate(data?.result?.date);
        convetToGroupData(data?.events);
      } else {
        setLoading(false);
      }
    } catch (err) {}
  };

  const getAllEvents = async (round, userId) => {
    setLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        `tipping/events?timezone=${timezone}&tournamentId=${selectedOrg}&SportId=${selectedSports}&startDate=${moment()
          .tz(timezone)
          .format(
            "YYYY-MM-DD"
          )}&round=${round}&competitionId=${selectedComp}&UserId=${userId}`
      );
      if (status === 200) {
        setLoading(false);
        setAllEventData(data?.result);
        setCounts(0);
        convetToGroupData(data?.result);
        setcutoffDate(data?.competition?.date);
      } else {
        setLoading(false);
      }
    } catch (err) {
      setLoading(false);
    }
  };

  const MyMinusNutton = () => {
    return (
      <Button>
        <Minus />
      </Button>
    );
  };
  const MyPLusNutton = () => {
    return (
      <Button>
        <Plus />
      </Button>
    );
  };

  const getFields = (list, field) => {
    return list.reduce(function (carry, item) {
      if (typeof item === "object" && field in item) {
        carry.push(item[field]);
      }
      return carry;
    }, []);
  };

  const fetchSportData = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/sports/sport/?sportTypeId=${2}`
      );
      if (status === 200) {
        var sportsdata = data?.result.map((s) => {
          return { ...s, label: s?.sportName, value: s?.id };
        });
        var filterSports = getFields(sportsdata, "id");

        var sdata = _.orderBy(sportsdata, ["label"], ["asc"])?.filter((item) =>
          [9, 4, 12]?.includes(item?.id)
        );

        setSports(sdata);
        setSelectedSports(sdata?.[0]?.id);
        fetchOrgData(0, sdata?.[0]?.id, []);
      }
    } catch (err) {}
  };

  const fetchOrgData = async (page, sID, OrgAll) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/tournament?SportId=${sID}&offset=${page}&limit=${limit}`
      );
      if (status === 200) {
        setOrgApiCount(OrgApiCount + 1);
        setcountOrg(Math.ceil(data?.result?.count / 20));
        let newdata = [];
        let track = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(OrgAll, newdata)?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        const unique = _.uniqBy(filterData, function (e) {
          return e.value;
        });
        setOrgAll(unique);
        setSelectedOrg(unique?.[0]?.value);

        fetchPublicComp(sID, unique?.[0]?.value, 1, []);
      }
    } catch (err) {}
  };

  const handleOrgInputChange = (page, value, sid) => {
    axiosInstance
      .get(
        `public/tournament?SportId=${sid}&limit=${limit}&offset=${page}&search=${value}`
      )
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          setsearchOrgCount(Math.ceil(res?.data?.result?.count / 20));
          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.name,
              value: item?.id,
            });
          });
          const mergeData = _.unionBy(searchOrg, newdata);
          const filterData = _.uniqBy(mergeData, function (e) {
            return e.value;
          });
          setsearchOrg(filterData);
          setIsOrgSearch(value);
        }
      });
  };

  const handleInputChangeOrg = (newValue, actionMeta) => {
    if (actionMeta.action === "input-change") {
      handleOrgInputChange(0, newValue, selectedSports);
    }
  };

  const handleOnScrollBottomOrg = () => {
    if (
      isOrgSearch !== "" &&
      searchOrgCount !== Math.ceil(SearchOrgpage / 20)
    ) {
      handleOrgInputChange(SearchOrgpage + 20, isOrgSearch, selectedSports);

      setSearchOrgpage(SearchOrgpage + 20);
    } else {
      if (countOrg !== 0 && countOrg !== Math.ceil(pageOrg / 20 + 1)) {
        fetchOrgData(pageOrg + 20, selectedSports, OrgAll);
        setpageOrg(pageOrg + 20);
      }
    }
  };

  const fetchPublicComp = async (sports, tournament, page, allComps) => {
    setCompPage(page);
    setIsPublicCompLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        // `tipping/getAll?SportId=${sports ? sports : ""}&tournamentId=${tournament ? tournament : ""
        // }&privacy=public&limit=${rowperPage}&page=${page}`
        `tipping/competition/list?SportId=${
          sports ? sports : ""
        }&tournamentId=${
          tournament ? tournament : ""
        }&limit=${rowperPage}&page=${page}&privacy=&isAdmin=true`
      );
      if (status === 200) {
        const count = Math.ceil(data?.count / rowperPage);
        setCompCount(count);
        let newdata = [];
        let track = data?.result?.map((item) => {
          newdata.push({
            label: item?.competitionName,
            value: item?.id,
          });
        });

        let filterData = [...allComps, ...newdata];
        const uniqueData = _.uniqBy(filterData, function (e) {
          return e.value;
        });
        setAllComps(uniqueData);
        setIsPublicCompLoading(false);
      } else {
        setIsPublicCompLoading(false);
      }
    } catch (err) {
      console.log(err);
      setIsPublicCompLoading(false);
    }
  };
  const handlePublicCompsInputChange = (page, value) => {
    axiosInstance
      .get(
        `tipping/getAll?SportId=${
          selectedSports ? selectedSports : ""
        }&tournamentId=${
          selectedOrg ? selectedOrg : ""
        }&privacy=public&limit=${rowperPage}&page=${page}&search=${value}`
      )
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result;
          setsearchPublicCompsCount(Math.ceil(res?.data?.count / rowperPage));
          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.competitionName,
              value: item?.id,
            });
          });
          const mergeData = _.unionBy(allComps, newdata);
          const filterData = _.uniqBy(mergeData, function (e) {
            return e.value;
          });
          setsearchPublicComps(filterData);
          setIsPublicCompsSearch(value);
          setSearchPublicCompspage(page);
        }
      });
  };

  const handleInputChangePublicComp = (newValue, actionMeta) => {
    if (actionMeta.action === "input-change") {
      handlePublicCompsInputChange(1, newValue);
    }
  };

  const handleOnScrollBottomPublicComps = () => {
    if (
      isPublicCompsSearch !== "" &&
      searchPublicCompsCount !== Math.ceil(SearchPublicCompspage)
    ) {
      handlePublicCompsInputChange(
        SearchPublicCompspage + 1,
        isPublicCompsSearch
      );

      setSearchPublicCompspage(SearchPublicCompspage + 1);
    } else {
      if (compCount !== 0 && compCount !== Math.ceil(compPage)) {
        fetchPublicComp(selectedSports, selectedOrg, compPage + 1, allComps);
        setCompPage(compPage + 1);
      }
    }
  };

  const fetchAllUsers = async (offset, userData, compId) => {
    setIsUserCompLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        `tipping/userList/${compId}`
      );
      if (status === 200) {
        setUserData(data?.result);

        setUserCount(Math.ceil(data?.result?.count / 20));
        let newdata = [];
        let user = data?.result?.map((item) => {
          newdata.push({
            label: item?.User?.firstName + " " + item?.User?.lastName,
            value: item?.UserId,
          });
        });
        let filterData = [...userData, ...newdata];
        const unique = _.uniqBy(filterData, function (e) {
          return e.value;
        });
        const sortUserData = unique?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        setUserData(sortUserData);
        setIsUserCompLoading(false);
        // setSelectedUser(unique?.[0]?.value)

        // fetchPublicComp(sID, unique?.[0]?.value, 1)
      } else {
        setIsUserCompLoading(false);
      }
    } catch (err) {
      setIsUserCompLoading(false);
    }
  };

  const handleUserInputChange = (search, offset) => {
    axiosInstance
      .get(`${URLS.users}?search=${search}&limit=20&offset=${offset}&allUser=`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.data;
          setSearchUserCount(Math.ceil(res?.data?.result?.count / 20));
          let newdata = [];
          const resuser = response?.forEach((item) => {
            newdata.push({
              label: item?.firstName + " " + item?.lastName,
              value: item?.id,
            });
          });
          const mergeData = _.unionBy([...searchUser, ...newdata], "value");
          setSearchUser(mergeData);
          setIsUserSearch(search);
        }
      });
  };

  const handleInputChangeUser = (newValue, actionMeta) => {
    if (actionMeta.action === "input-change") {
      handleUserInputChange(newValue, 0);
    }
  };

  const handleOnScrollBottomUser = () => {
    if (
      isUserSearch !== "" &&
      searchUserCount !== Math.ceil(searchUserpage / 20)
    ) {
      handleUserInputChange(isUserSearch, searchUserpage + 20);

      setSearchUserpage(searchUserpage + 20);
    } else {
      if (userCount !== 0 && userCount !== Math.ceil(pageUser / 20 + 1)) {
        fetchAllUsers(pageUser + 20, userData, selectedComp);
        setPageUser(pageUser + 20);
      }
    }
  };

  const handleCheckboxClick = (event, item, teamType) => {
    const eventList = allEventData?.map((obj) => {
      if (obj?.id == item?.id) {
        if (teamType === "hometeam") {
          return {
            ...obj,
            awayTeam: {
              ...obj?.awayTeam,
              isTip: event.target.checked ? 0 : obj?.awayTeam?.isTip,
            },
            homeTeam: {
              ...obj?.homeTeam,
              isTip: event.target.checked ? 1 : 0,
            },
          };
        } else {
          return {
            ...obj,
            awayTeam: {
              ...obj?.awayTeam,
              isTip: event.target.checked ? 1 : 0,
            },
            homeTeam: {
              ...obj?.homeTeam,
              isTip: event.target.checked ? 0 : obj?.homeTeam?.isTip,
            },
          };
        }
      } else {
        return obj;
      }
    });
    if (handleValidate(eventList)) {
      setAtLeastOneError("");
    }
    convetToGroupData(eventList);
    setAllEventData(eventList);
  };
  const handleValidate = (tipdata) => {
    const isTipSelected = tipdata?.some(
      (item) => item?.awayTeam?.isTip || item?.homeTeam?.isTip
    );
    return isTipSelected;
  };
  const handleSubmit = async () => {
    if (handleValidate(eventsData)) {
      setAtLeastOneError("");
      let payload = {};

      const finalPayload = allEventData?.map((item, index) => {
        const allTeamOdds =
          item?.SportId === 4
            ? item?.CricketBetOffers
            : item?.SportId === 10
            ? item?.NBABetOffers
            : item?.SportId === 15
            ? item?.AFLBetOffers
            : item?.SportId === 9
            ? item?.ARBetOffers
            : item?.SportId === 7
            ? item?.TennisBetOffers
            : item?.SportId === 11
            ? item?.BaseballBetOffers
            : item?.SportId === 17
            ? item?.IceHockeyBetOffers
            : item?.SportId === 6
            ? item?.BoxingBetOffers
            : item?.SportId === 5
            ? item?.MMABetOffers
            : item?.SportId === 8
            ? item?.SoccerBetOffers
            : item?.SportId === 16
            ? item?.GolfBetOffers
            : item?.RLBetOffers;
        const selectedOdds = item?.homeTeam?.isTip
          ? allTeamOdds?.homeOdds
          : item?.awayTeam?.isTip
          ? allTeamOdds?.awayOdds
          : null;
        const selectedOppositeOdds = item?.homeTeam?.isTip
          ? allTeamOdds?.awayOdds
          : item?.awayTeam?.isTip
          ? allTeamOdds?.homeOdds
          : null;
        payload = {
          round: stepperCount,
          teamId:
            item?.awayTeam?.isTip == 1
              ? item?.awayTeamId
              : item?.homeTeam?.isTip == 1
              ? item?.homeTeamId
              : null,
          eventId: item?.id,
          SportId: item?.SportId,
          ...(eventByIdData?.tippingType !== "winning" && {
            selectedOdd: selectedOdds?.odd,
          }),
          ...(eventByIdData?.tippingType === "spread" && {
            selectedPoint: selectedOdds?.point,
          }),
          ...(eventByIdData?.tippingType !== "winning" && {
            selectedBookKeeperId: selectedOdds?.BookKeeperId,
          }),
          ...(eventByIdData?.tippingType !== "winning" && {
            oppositeOdd: selectedOppositeOdds?.odd,
          }),
          ...(eventByIdData?.tippingType === "spread" && {
            oppositePoint: selectedOppositeOdds?.point,
          }),
          ...(eventByIdData?.tippingType !== "winning" && {
            oppositeBookKeeperId: selectedOppositeOdds?.BookKeeperId,
          }),
        };
        payload = index === 0 ? { ...payload, margin: counts } : payload;
        return payload;
      });
      const passPayload = {
        jocker: jokerRound ? jokerRound : isJoker ? stepperCount : null,
        submitToAll: eventByIdData?.sameTipToAll,
        events: finalPayload,
        UserId: selectedUser,
      };
      try {
        const { status, data } = await axiosInstance.put(
          `tipping/addEvent/${selectedComp}`,
          passPayload
        );
        if (status === 200) {
          // toast.success(data?.message, {
          //     position: "bottom-center",
          //     autoClose: 3000,
          //     hideProgressBar: true,
          //     closeOnClick: true,
          //     pauseOnHover: true,
          //     draggable: true,
          //     theme: "colored"
          // });
          setActionMessage(true, "Success", data?.message);
          getEventByID(stepperCount, selectedUser, selectedComp);
        } else {
        }
      } catch (err) {
        setActionMessage(true, "Error", err?.response?.data?.message);
        console.log("error", err?.response?.data?.message);
      }
    } else {
      setAtLeastOneError("Please select atleast one tip");
    }
  };

  const handleReset = () => {
    const eventList = allEventData?.map((obj) => {
      return {
        ...obj,
        awayTeam: {
          ...obj?.awayTeam,
          isTip: 0,
        },
        homeTeam: {
          ...obj?.homeTeam,
          isTip: 0,
        },
      };
    });
    convetToGroupData(eventList);
    setAllEventData(eventList);
    setCounts(0);
  };
  const fetchDayandTime = () => {
    if (eventByIdData?.cutOffTime) {
      const dayWithTime = moment
        .utc(
          `${eventByIdData?.cutOffWeek} ${eventByIdData?.cutOffTime}`,
          "dddd HH:mm:ss"
        )
        .tz(timezone)
        .format("h:mm A");
      return (
        <span>
          {" "}
          {eventByIdData?.cutOffWeek +
            " " +
            moment(cutoffDate).format("DD/MM/YYYY") +
            " | " +
            dayWithTime}
        </span>
      );
    }
  };

  const fetchjokerBox = () => {
    // if ((jokerRound !== 0 && !jokerRound) || jokerRound == stepperCount) {
    return (
      <Box className="joker-box">
        <Box className="top-sec">
          <Typography className="joker-txt">Joker Round</Typography>
        </Box>
        <Box className="bottom-sec">
          <Box>
            <FormControl>
              <label>
                <Checkbox
                  icon={<Uncheck />}
                  checkedIcon={<BlueCheck />}
                  checked={isJoker}
                  onChange={(e) => setIsJoker(e?.target?.checked)}
                  disabled={
                    eventByIdData?.joker
                      ? true
                      : eventByIdData?.remainingJoker == 0
                      ? true
                      : !eventByIdData?.joker &&
                        eventByIdData?.remainingJoker > 0
                      ? false
                      : false
                  }
                />
                {/* {filter?.icon} */}
              </label>
            </FormControl>
          </Box>
          <Box>
            <Typography className="select-txt">Select Joker Round</Typography>
            <Typography className="detail-txt">
              You are allowed to select Joker Round once per season. Selecting a
              Joker will double your round points.
            </Typography>
          </Box>
        </Box>
      </Box>
    );
    // } else {
    //   return null;
    // }
  };

  const setActionMessage = useCallback(
    (display = false, type = "", message = "") => {
      setMessageBox({ display, type, message });
      setTimeout(() => {
        setMessageBox({ display: false, type: "", message: "" });
      }, 3000);
    },
    []
  );

  const handleSelectChange = (selectedOption) => {
    setStepperCount(selectedOption.value);
  };

  return (
    <>
      <Grid container className="page-content adminLogin">
        <Grid item xs={12} className="pageWrapper">
          <Box className="content-wrap">
            <Box className="comp-tips-wrap">
              {/* <Paper className="pageWrapper"> */}
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>

                  <Typography className="active_p">
                    Tipping Comp Tips
                  </Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={3}>
                  <Typography variant="h1" align="left">
                    Tipping Comp Tips
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={9}
                  className="admin-filter-wrap admin-fixture-wrap"
                >
                  <Select
                    className="React teamsport-select external-select sort-select"
                    value={
                      selectedSports &&
                      sports?.find((item) => {
                        return item?.value === selectedSports;
                      })
                    }
                    onChange={(e) => {
                      setSelectedSports(e?.value == 0 ? "" : e?.value);
                      setSelectedComp(null);
                      setSelectedUser(null);
                      if (e?.value) {
                        fetchOrgData(0, e?.value, []);
                      }
                    }}
                    options={sports}
                    classNamePrefix="select"
                    placeholder="Select sport"
                    components={{ DropdownIndicator }}
                  />
                  <Select
                    className={`React sort-select teamsport-select external-select ${
                      selectedSports ? "" : "disable-state"
                    }`}
                    onMenuScrollToBottom={(e) => handleOnScrollBottomOrg(e)}
                    onInputChange={handleInputChangeOrg}
                    onChange={(e) => {
                      setSelectedOrg(e?.value);

                      setOrgApiCount(0);
                      setpageOrg(0);
                      // setisOrgSelectOpen(false);
                      setSelectedComp(null);
                      setSelectedUser(null);
                      fetchPublicComp(selectedSports, e?.value, 1, []);
                    }}
                    // onFocus={() => setisOrgSelectOpen(true)}
                    // onBlur={() => setisOrgSelectOpen(false)}
                    value={
                      selectedOrg &&
                      (isOrgSearch
                        ? searchOrg?.find((item) => {
                            return item?.value == selectedOrg;
                          })
                        : OrgAll?.find((item) => {
                            return item?.value == selectedOrg;
                          }))
                    }
                    options={isOrgSearch ? searchOrg : OrgAll}
                    classNamePrefix="select"
                    placeholder="Select league"
                    isDisabled={selectedSports ? false : true}
                    components={{ DropdownIndicator }}
                  />
                  <Select
                    className={`React teamsport-select external-select sort-select ${
                      selectedOrg ? "" : "disable-state"
                    }`}
                    onMenuScrollToBottom={(e) =>
                      handleOnScrollBottomPublicComps(e)
                    }
                    onInputChange={handleInputChangePublicComp}
                    value={
                      selectedComp &&
                      (isPublicCompsSearch
                        ? searchPublicComps?.find((item) => {
                            return item?.value === selectedComp;
                          })
                        : allComps?.find((item) => {
                            return item?.value === selectedComp;
                          }))
                    }
                    onChange={(e) => {
                      setSelectedComp(e?.value);
                      fetchRoundData(
                        selectedOrg,
                        selectedSports,
                        e?.value ? e?.value : ""
                      );
                      fetchAllUsers(0, [], e?.value);
                      setSelectedUser(null);
                    }}
                    options={isPublicCompsSearch ? searchPublicComps : allComps}
                    classNamePrefix="select"
                    placeholder="Competition Name"
                    isDisabled={selectedOrg ? false : true}
                    components={{ DropdownIndicator }}
                    isLoading={isPublicCompLoading}
                  />
                  <Select
                    className="React teamsport-select external-select"
                    classNamePrefix="select"
                    placeholder="Select User"
                    // onMenuScrollToBottom={(e) => handleOnScrollBottomUser(e)}
                    // onInputChange={handleInputChangeUser}
                    onChange={(e) => {
                      setSelectedUser(e?.value);
                      setPageUser(0);
                      getEventByID(stepperCount, e?.value, selectedComp);
                    }}
                    isDisabled={selectedComp ? false : true}
                    value={
                      selectedUser &&
                      // isUserSearch
                      //   ? searchUser?.find((item) => {
                      //     return item?.value == selectedUser;
                      //   })
                      //   :
                      userData?.find((item) => {
                        return item?.value == selectedUser;
                      })
                    }
                    isLoading={isUserLoading}
                    options={isUserSearch ? searchUser : userData}
                  />
                </Grid>
              </Grid>

              <Grid container direction="row" alignItems="center"></Grid>

              {selectedComp && selectedUser && (
                <Box className="tab-search-section">
                  <Box className="stepper-score-sec">
                    <Box className="stepper-input">
                      <Button
                        className="stepper-input__button"
                        onClick={(e) => {
                          e.preventDefault();
                          const index = rounds?.indexOf(stepperCount);
                          setStepperCount(rounds[index - 1]);
                          // getEventByID(rounds[index - 1]);
                          setCounts(0);
                        }}
                        disabled={!stepperCount || stepperCount == rounds[0]}
                        startIcon={<LeftArrow />}
                      >
                        Previous
                      </Button>

                      {/* <div className="stepper-input__content">
                      {eventByIdData?.SportId == 4
                        ? stepperCount
                          ? `Day ${Number(stepperCount)}`
                          : `Day 0`
                        : stepperCount
                          ? `Round ${Number(stepperCount)}`
                          : `Round 0`}
                    </div> */}
                      <Select
                        value={newRoundDataOption?.find(
                          (item) => item?.value === stepperCount
                        )}
                        onChange={handleSelectChange}
                        options={newRoundDataOption}
                        className="React tipping-select"
                        classNamePrefix="select"
                        isSearchable={false}
                      />

                      <Button
                        className="stepper-input__button Next-btn"
                        onClick={(e) => {
                          e.preventDefault();
                          const index = rounds?.indexOf(stepperCount);
                          e.preventDefault();
                          setStepperCount(rounds[index + 1]);
                          // getEventByID(rounds[index + 1]);
                          setCounts(0);
                        }}
                        disabled={
                          stepperCount == null ||
                          stepperCount == rounds[rounds?.length - 1]
                        }
                        endIcon={<RightArrow />}
                      >
                        Next
                      </Button>
                    </Box>

                    <Box className="score-share-sec">
                      <Box className="score-sec">
                        {!Boolean(isLoading) && (
                          <>
                            <Box style={{ textAlign: "left" }}>
                              {" "}
                              {eventByIdData?.tippingType == "winning"
                                ? "Winner Tipping"
                                : eventByIdData?.tippingType == "odds"
                                ? "Odds Tipping"
                                : eventByIdData?.tippingType == "spread"
                                ? "SpreadLine Tipping"
                                : ""}
                            </Box>
                            <Box className="bottom-line-sec">
                              <Typography className="final-txt">
                                {eventByIdData?.SportId == 4 ? "Day" : "Round"}{" "}
                                {Number(stepperCount)}
                                {eventByIdData &&
                                eventByIdData?.tipDeadline == "game"
                                  ? " Game Based"
                                  : ` tipping closes at:`}
                                {eventByIdData &&
                                  eventByIdData?.tipDeadline == "round" && (
                                    <span>{fetchDayandTime()}</span>
                                  )}
                              </Typography>
                            </Box>
                          </>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              )}
              {selectedComp && selectedUser && (
                <Box className="page-deatils-wrap padding-container">
                  {isLoading ? (
                    <div className="allsport-loader-center app-loader">
                      <Loader />
                    </div>
                  ) : (
                    <>
                      {allEventData && allEventData?.length > 0 ? (
                        <>
                          {Object.entries(groupedEventData)?.map(
                            ([date, tips], parentIndex) => {
                              return (
                                <Box className="tips-sec" key={date}>
                                  <Box className="title-date-sec">
                                    <Typography className="title-date">
                                      {date ? fetchDayName(date) : ""}{" "}
                                      {date
                                        ? moment
                                            .utc(date)
                                            .local()
                                            .format("DD/MM/YYYY")
                                        : ""}
                                    </Typography>
                                  </Box>
                                  {tips?.map((tip, index) => {
                                    const dateTocheck =
                                      eventByIdData?.tipDeadline == "game"
                                        ? moment(tip?.startTime)
                                        : lastDate;
                                    const IsScore =
                                      eventLength === 0
                                        ? tip?.awayScore && tip?.homeScore
                                          ? true
                                          : false
                                        : Number(selectedSports) != 4
                                        ? Boolean(tip?.homeScore ?? false)
                                        : tip?.ScoreBoard
                                        ? Object.keys(tip?.ScoreBoard)?.length >
                                            0 &&
                                          Boolean(tip?.ScoreBoard?.Epr == 2)
                                        : false;
                                    return (
                                      <>
                                        <Box className="comp-tip-data-sec">
                                          <Typography className="time-venue-txt">
                                            {tip?.startTime
                                              ? moment
                                                  .utc(tip?.startTime)
                                                  .local()
                                                  .format("hh:mm A")
                                              : ""}{" "}
                                            <span>
                                              {tip?.venue
                                                ? "|" + tip?.venue
                                                : ""}
                                            </span>
                                          </Typography>
                                          <Box
                                            className={
                                              eventLength === 0
                                                ? `comp-odds-tips-sec`
                                                : tip?.homeScore ||
                                                  (tip?.ScoreBoard &&
                                                    Object.keys(tip?.ScoreBoard)
                                                      ?.length > 0 &&
                                                    tip?.ScoreBoard?.Epr == 2)
                                                ? `comp-odds-tips-sec `
                                                : tip?.winnerCode == 3 ||
                                                  (tip?.winnerCode === null &&
                                                    tip?.status === null &&
                                                    moment
                                                      .utc(tip?.startTime)
                                                      .tz(timezone) <
                                                      moment().tz(timezone))
                                                ? `comp-odds-tips-sec indefinite-result no-result`
                                                : `comp-odds-tips-sec no-result`
                                            }
                                          >
                                            <Box className="left-sec">
                                              <Box className="team-wrap">
                                                <Box className="team-img-wrap">
                                                  <img
                                                    className="team-img"
                                                    src={
                                                      tip?.homeTeam?.flag?.includes(
                                                        "uploads"
                                                      )
                                                        ? config.mediaUrl +
                                                          tip?.homeTeam?.flag
                                                        : DefaultImg
                                                    }
                                                    alt="team"
                                                  />
                                                </Box>
                                                <Box className="comp-name-tip-wrap">
                                                  <Typography className="team-name">
                                                    {tip?.homeTeam?.name}
                                                  </Typography>
                                                </Box>
                                              </Box>

                                              <TippingSportsOdds
                                                data={tip}
                                                type={"odds"}
                                                team={"hometeam"}
                                                teamId={tip?.homeTeamId}
                                                isScore={IsScore}
                                                eventLength={eventLength}
                                                eventByIdData={eventByIdData}
                                                BookkeeperData={BookkeeperData}
                                              />

                                              {eventLength !== 0 && (
                                                <>
                                                  {Number(selectedSports) !=
                                                  4 ? (
                                                    tip?.homeScore ? (
                                                      <Box className="blue-score">
                                                        <Typography className="score">
                                                          {tip?.homeScore
                                                            ?.current ?? 0}
                                                        </Typography>
                                                      </Box>
                                                    ) : (
                                                      <></>
                                                    )
                                                  ) : tip?.ScoreBoard &&
                                                    Object.keys(tip?.ScoreBoard)
                                                      ?.length > 0 &&
                                                    tip?.ScoreBoard?.Epr ==
                                                      2 ? (
                                                    <Box className="blue-score">
                                                      {tip?.ScoreBoard?.Exd >
                                                      "1" ? (
                                                        <Typography className="score">
                                                          {tip?.ScoreBoard
                                                            ?.Tr1C1 ?? "-"}
                                                          {tip?.ScoreBoard
                                                            ?.Tr1CW1 &&
                                                          tip?.ScoreBoard
                                                            ?.Tr1CW1 === 10
                                                            ? ""
                                                            : (tip?.ScoreBoard
                                                                ?.Tr1CW1 ||
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CW1 ===
                                                                  0) &&
                                                              `/${
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CW1
                                                              }${
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CD1 &&
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CD1 === 1
                                                                  ? "d"
                                                                  : ""
                                                              }`}{" "}
                                                          {(tip?.ScoreBoard
                                                            ?.Tr1C2 ||
                                                            tip?.ScoreBoard
                                                              ?.Tr1C2 === 0) &&
                                                            `& ${tip?.ScoreBoard?.Tr1C2}`}
                                                          {tip?.ScoreBoard
                                                            ?.Tr1CW2 &&
                                                          tip?.ScoreBoard
                                                            ?.Tr1CW2 === 10
                                                            ? ""
                                                            : (tip?.ScoreBoard
                                                                ?.Tr1CW2 ||
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CW2 ===
                                                                  0) &&
                                                              `/${
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CW2
                                                              }${
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CD2 &&
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CD2 === 1
                                                                  ? "d"
                                                                  : ""
                                                              }`}{" "}
                                                        </Typography>
                                                      ) : (
                                                        <Typography className="score">
                                                          {tip?.ScoreBoard
                                                            ?.Tr1C1 ?? "-"}
                                                          {tip?.ScoreBoard
                                                            ?.Tr1CW1 &&
                                                          tip?.ScoreBoard
                                                            ?.Tr1CW1 === 10
                                                            ? ""
                                                            : (tip?.ScoreBoard
                                                                ?.Tr1CW1 ||
                                                                tip?.ScoreBoard
                                                                  ?.Tr1CW1 ===
                                                                  0) &&
                                                              `/${tip?.ScoreBoard?.Tr1CW1}`}{" "}
                                                        </Typography>
                                                      )}
                                                    </Box>
                                                  ) : (
                                                    <></>
                                                  )}
                                                </>
                                              )}
                                              <Box className="check-box-sec">
                                                <FormControl>
                                                  <label>
                                                    <Checkbox
                                                      icon={
                                                        // tip?.winnerCode == 3 ? (
                                                        //     <Notcheck />
                                                        // ) : dateTocheck < moment() &&
                                                        //     tip?.homeTeam?.isTip !==
                                                        //     1 &&
                                                        //     tip?.awayTeam?.isTip !==
                                                        //     1 ? (
                                                        //     <Notcheck />
                                                        // ) :
                                                        <Uncheck />
                                                      }
                                                      checkedIcon={
                                                        // tip?.winnerCode ? (
                                                        //     tip?.winnerCode == 1 ? (
                                                        //         <Check />
                                                        //     ) : (
                                                        //         <Notcheck />
                                                        //     )
                                                        // ) :
                                                        <Check />
                                                      }
                                                      disabled={
                                                        (eventByIdData?.tippingType ===
                                                          "odds" &&
                                                          eventLength !== 0) ||
                                                        (eventByIdData?.tippingType ===
                                                          "spread" &&
                                                          eventLength !== 0)
                                                      }
                                                      value="homeTeamId"
                                                      onChange={(event) => {
                                                        handleCheckboxClick(
                                                          event,
                                                          tip,
                                                          "hometeam"
                                                        );
                                                      }}
                                                      checked={
                                                        tip?.homeTeam?.isTip ==
                                                        1
                                                      }
                                                    />
                                                  </label>
                                                </FormControl>
                                              </Box>
                                            </Box>
                                            <Box className="vs">VS</Box>
                                            <Box className="left-sec flex-rr">
                                              <Box className="team-wrap">
                                                <Box className="team-img-wrap">
                                                  <img
                                                    className="team-img"
                                                    src={
                                                      tip?.awayTeam?.flag?.includes(
                                                        "uploads"
                                                      )
                                                        ? config?.mediaUrl +
                                                          tip?.awayTeam?.flag
                                                        : DefaultImg
                                                    }
                                                    alt="team"
                                                  />
                                                </Box>
                                                <Box className="comp-name-tip-wrap">
                                                  <Typography className="team-name">
                                                    {tip?.awayTeam?.name}
                                                  </Typography>
                                                </Box>
                                              </Box>

                                              <TippingSportsOdds
                                                data={tip}
                                                type={"odds"}
                                                team={"awayteam"}
                                                teamId={tip?.awayTeamId}
                                                isScore={IsScore}
                                                eventLength={eventLength}
                                                eventByIdData={eventByIdData}
                                                BookkeeperData={BookkeeperData}
                                              />

                                              {eventLength !== 0 && (
                                                <>
                                                  {Number(selectedSports) !=
                                                  4 ? (
                                                    tip?.awayScore ? (
                                                      <Box className="blue-score">
                                                        <Typography className="score">
                                                          {tip?.awayScore
                                                            ?.current ?? 0}
                                                        </Typography>
                                                      </Box>
                                                    ) : (
                                                      <></>
                                                    )
                                                  ) : tip?.ScoreBoard &&
                                                    Object.keys(tip?.ScoreBoard)
                                                      ?.length > 0 &&
                                                    tip?.ScoreBoard?.Epr ==
                                                      2 ? (
                                                    <Box className="blue-score">
                                                      {tip?.ScoreBoard?.Exd >
                                                      "1" ? (
                                                        <Typography className="score">
                                                          {tip?.ScoreBoard
                                                            ?.Tr2C1 ?? "-"}
                                                          {tip?.ScoreBoard
                                                            ?.Tr2CW1 &&
                                                          tip?.ScoreBoard
                                                            ?.Tr2CW1 === 10
                                                            ? ""
                                                            : (tip?.ScoreBoard
                                                                ?.Tr2CW1 ||
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CW1 ===
                                                                  0) &&
                                                              `/${
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CW1
                                                              }${
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CD1 &&
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CD1 === 1
                                                                  ? "d"
                                                                  : ""
                                                              }`}{" "}
                                                          {(tip?.ScoreBoard
                                                            ?.Tr2C2 ||
                                                            tip?.ScoreBoard
                                                              ?.Tr2C2 === 0) &&
                                                            `& ${tip?.ScoreBoard?.Tr2C2}`}
                                                          {tip?.ScoreBoard
                                                            ?.Tr2CW2 &&
                                                          tip?.ScoreBoard
                                                            ?.Tr2CW2 === 10
                                                            ? ""
                                                            : (tip?.ScoreBoard
                                                                ?.Tr2CW2 ||
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CW2 ===
                                                                  0) &&
                                                              `/${
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CW2
                                                              }${
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CD2 &&
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CD2 === 1
                                                                  ? "d"
                                                                  : ""
                                                              }`}{" "}
                                                        </Typography>
                                                      ) : (
                                                        <Typography className="score">
                                                          {tip?.ScoreBoard
                                                            ?.Tr2C1 ?? "-"}
                                                          {tip?.ScoreBoard
                                                            ?.Tr2CW1 &&
                                                          tip?.ScoreBoard
                                                            ?.Tr2CW1 === 10
                                                            ? ""
                                                            : (tip?.ScoreBoard
                                                                ?.Tr2CW1 ||
                                                                tip?.ScoreBoard
                                                                  ?.Tr2CW1 ===
                                                                  0) &&
                                                              `/ ${tip?.ScoreBoard?.Tr2CW1}`}{" "}
                                                        </Typography>
                                                      )}
                                                    </Box>
                                                  ) : (
                                                    <></>
                                                  )}
                                                </>
                                              )}
                                              <Box className="check-box-sec">
                                                <FormControl>
                                                  <label>
                                                    <Checkbox
                                                      icon={<Uncheck />}
                                                      checkedIcon={<Check />}
                                                      value="awayTeamId"
                                                      onChange={(event) => {
                                                        handleCheckboxClick(
                                                          event,
                                                          tip,
                                                          "awayteam"
                                                        );
                                                      }}
                                                      checked={
                                                        tip?.awayTeam?.isTip ==
                                                        1
                                                      }
                                                      disabled={
                                                        (eventByIdData?.tippingType ===
                                                          "odds" &&
                                                          eventLength !== 0) ||
                                                        (eventByIdData?.tippingType ===
                                                          "spread" &&
                                                          eventLength !== 0)
                                                      }
                                                    />
                                                  </label>
                                                </FormControl>
                                              </Box>
                                            </Box>
                                          </Box>
                                          {parentIndex == 0 &&
                                            index == 0 &&
                                            eventByIdData?.marginRound == 1 && (
                                              <>
                                                <Box className="margin-sec">
                                                  <Typography className="margin-txt">
                                                    {selectedSports == 4
                                                      ? "Number of 6s"
                                                      : "Margin"}
                                                  </Typography>
                                                </Box>
                                                <Box className="counter-score-sec">
                                                  <Box className="counter-input">
                                                    <Button
                                                      disableFocusRipple
                                                      disableRipple
                                                      className="counter-input__button"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        handleDecrement(
                                                          tip?.id,
                                                          index
                                                        );
                                                      }}
                                                      disabled={counts == 0}
                                                    >
                                                      <MyMinusNutton />
                                                    </Button>
                                                    <TextField
                                                      id="outlined-basic"
                                                      variant="outlined"
                                                      className="margin-textfield"
                                                      name="compName"
                                                      onChange={(e) => {
                                                        const input =
                                                          e?.target?.value;
                                                        const numericInput =
                                                          input?.replace(
                                                            /[^0-9]/g,
                                                            ""
                                                          );
                                                        const removeLeadingZeros =
                                                          (numericInput) => {
                                                            if (
                                                              numericInput.length >=
                                                              2
                                                            ) {
                                                              const result =
                                                                parseInt(
                                                                  numericInput,
                                                                  10
                                                                ).toString();
                                                              return result;
                                                            }
                                                            return numericInput;
                                                          };
                                                        const finalMargin =
                                                          removeLeadingZeros(
                                                            numericInput
                                                          );

                                                        setCounts(
                                                          finalMargin
                                                            ? Number(
                                                                finalMargin
                                                              )
                                                            : 0
                                                        );
                                                      }}
                                                      onFocus={(e) =>
                                                        e.target.select()
                                                      }
                                                      value={counts}
                                                    />

                                                    <Button
                                                      disableFocusRipple
                                                      disableRipple
                                                      className="counter-input__button plus-btn"
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        handleIncrement(
                                                          tip?.id,
                                                          index
                                                        );
                                                      }}
                                                    >
                                                      <MyPLusNutton />
                                                    </Button>
                                                  </Box>
                                                </Box>
                                              </>
                                            )}
                                        </Box>
                                      </>
                                    );
                                  })}
                                </Box>
                              );
                            }
                          )}
                          {eventByIdData?.jokerRound === 1 ? (
                            fetchjokerBox()
                          ) : (
                            <></>
                          )}
                          {(eventByIdData?.tippingType === "odds" &&
                            eventLength !== 0) ||
                          (eventByIdData?.tippingType === "spread" &&
                            eventLength !== 0) ? (
                            <></>
                          ) : (
                            <>
                              <Box className="btn-flex">
                                <>
                                  <Box className="submit-tips-btn-sec">
                                    <Button
                                      disableElevation
                                      disableFocusRipple
                                      disableRipple
                                      fullWidth
                                      className="submit-tips-btn"
                                      onClick={() => {
                                        handleSubmit();
                                      }}
                                      disabled={
                                        eventByIdData?.marginRound == 1 &&
                                        Boolean(
                                          eventsData?.[0]?.awayTeam?.isTip ||
                                            eventsData?.[0]?.homeTeam?.isTip
                                        ) &&
                                        counts === 0
                                      }
                                    >
                                      {eventLength == 0 ? "Submit" : "Update"}{" "}
                                      tips
                                    </Button>
                                  </Box>
                                </>
                              </Box>
                              {eventByIdData?.marginRound == 1 &&
                                Boolean(
                                  eventsData?.[0]?.awayTeam?.isTip ||
                                    eventsData?.[0]?.homeTeam?.isTip
                                ) &&
                                counts === 0 && (
                                  <Box className="margin-error">
                                    <Typography className="error-msg-margin">
                                      Please select{" "}
                                      {selectedSports == 4
                                        ? "Number of 6s"
                                        : "Margin"}{" "}
                                      at least 1
                                    </Typography>
                                  </Box>
                                )}

                              {atLeastOneError ? (
                                <Box className="margin-error">
                                  <Typography className="error-msg-margin">
                                    {atLeastOneError}
                                  </Typography>
                                </Box>
                              ) : (
                                <></>
                              )}
                              <Box className="clear-all-box">
                                <Typography
                                  className="clear-all"
                                  onClick={() => {
                                    handleReset();
                                  }}
                                >
                                  Clear all
                                </Typography>
                              </Box>
                            </>
                          )}
                        </>
                      ) : (
                        <Box
                          style={{
                            textAlign: "center",
                          }}
                        >
                          No Data Available
                        </Box>
                      )}
                    </>
                  )}
                </Box>
              )}
            </Box>
          </Box>
        </Grid>
      </Grid>
    </>
  );
}

export default CompTipsPage;
