body {
  margin: 0;
  font-family: "Open Sans", "Segoe UI", "Roboto", "Oxygen", "Ubuntu",
    "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  overflow: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

h6,
body .MuiTypography-subtitle2 {
  font-family: "Open Sans";
  font-weight: 500;
  font-size: 15px;
}

body .MuiButton-root {
  font-family: "Inter";
}

.MuiAppBar-root.footer-top-bar {
  background-color: #e3e3e3;
  padding-top: 32px;
  padding-bottom: 31px;
  margin-bottom: 15px;

  @media (max-width: 599px) {
    padding: 10px 0px;
    margin-top: 25px;
  }
}

a.underline {
  text-decoration: underline;
}

.rm-pad {
  padding: 0 !important;
}

.pad-lr-15 {
  @media (min-width: 768px) {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.pad-lr-30 {
  @media (min-width: 768px) {
    padding-left: 30px;
    padding-right: 30px;
  }
}

body .mr-lr-30,
.mr-lr-30 {
  background-color: #e0e0e0;
  @media (min-width: 768px) {
    margin-left: 30px;
    margin-right: 30px;
  }
}

body .title-with-border {
  position: relative;
  font-size: 20px;

  &:after {
    display: block;
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    border-bottom: 1px solid #464545;
  }

  @media (max-width: 767px) {
    font-size: 16px;
  }
}

.App {
  &.hide_sidebar {
    .page-contents {
      max-width: 100% !important;
      flex-basis: 100% !important;
    }

    .page-sidebar {
      display: none !important;
    }
  }
}

.error {
  font-size: 11px;
  color: #ff6b00;
  position: absolute;
  left: 0;
  bottom: -17px;
}

.res_message {
  font-size: 12px;
}

.content-bottom-banneradd {
  margin-bottom: 20px;
}

.form {
  .MuiFormControl-root {
    width: 95%;
  }

  .rounded-input input,
  .rounded-input textarea {
    border-radius: 4px;
    border: 1px solid #81d8df;
    padding: 5px 10px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);

    &.MuiInput-underline:after,
    &.MuiInput-underline:before {
      display: none !important;
    }
  }

  .MuiInputBase-multiline {
    padding: 10.5px;
  }

  .MuiFormControlLabel-label,
  label {
    font-size: 16px;
    transform: scale(1);
    color: #656565;
  }

  label + .MuiInput-formControl {
    margin-top: 25px;
  }

  .submit-btn {
    background: #256fd5;
    color: #fff;
    font-size: 13px;
    padding: 5px 25px;

    &.bg-dark-orange {
      background: #ff6b00;
    }

    &.bg-orange {
      background: #ffa400;
    }
  }
}

.appbarpad {
  padding: 20px 0;

  &.main-menu-wrap {
    padding: 25px 0;

    @media (max-width: 599px) {
      padding: 10px 0px;
    }
  }
}

body .title {
  font-size: 25px;
  line-height: 41px;
  font-weight: 500;
}

body .big-title {
  color: #ff6b00;
  font-size: 30px;
  line-height: 41px;
  font-weight: 500;
  margin-top: 20px;
  margin-bottom: 40px;
}

body .cap-title {
  color: #00833e;
  text-transform: uppercase;
  font-size: 30px;
  line-height: 41px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 25px;
  text-align: center;
}

.section {
  padding-top: 50px;
  padding-bottom: 50px;
}

.regular-contents {
  padding-bottom: 25px;

  li {
    margin-bottom: 5px;
  }

  .MuiTypography-subtitle2,
  p,
  li,
  strong {
    font-family: "Open Sans";
    font-weight: normal;
    font-size: 20px;
    line-height: 28px;
  }

  strong {
    font-weight: 500;
  }

  p {
    margin-bottom: 10px;

    p {
      margin-bottom: 0;
      margin-top: 0;
    }
  }

  // p.MuiTypography-body2{
  //     font-size: 18px;
  //     line-height: 24px;
  // }
}

.user-view {
  padding-left: 10px;
  padding-right: 10px;

  .MuiTypography-subtitle1 {
    font-size: 16px;
    line-height: 20px;
  }
}

.page-banner {
  position: relative;
  padding-bottom: 50px;

  .banner-left-image,
  .banner-right-image {
    position: absolute;
    top: 0;
    bottom: 0;
    height: 100%;
  }

  .banner-left-image {
    left: 0;
  }

  .banner-right-image {
    right: 0;
  }

  @media (max-width: 959px) {
    .banner-left-image,
    .banner-right-image {
      position: relative;
      top: auto;
      left: auto;
      right: auto;
      bottom: auto;
      display: block;
      object-fit: contain;
      height: auto;
    }
  }

  @media (max-width: 1024px) {
    .banner-left-image,
    .banner-right-image {
      position: static;
    }
  }
}

.blank-header-bar {
  display: none !important;
  padding: 0;

  @media (max-width: 959px) {
    position: relative;
    min-height: 32px;

    img {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }

  img {
    // height: 100%;
    display: block;

    @media (max-width: 599px) {
    }
  }
}

.header-bar,
.login-bar {
  display: block !important;
}

.blank_header_top_bar {
  .blank-header-bar {
    display: block !important;
  }

  .header-bar,
  .login-bar {
    display: none !important;
  }
}

.header-bar {
  position: relative;

  @media screen and (max-width: 767px) {
    align-items: flex-end;
  }
}

.checkboxIcon {
  width: 18px;
  height: 18px;
  border: 1px solid #81d8df;
  border-radius: 50%;
  background: transparent;

  &.checkboxIcon-checked {
    background: #81d8df;
  }
}

.checkboxIcon-sqare {
  width: 18px;
  height: 18px;
  border: 1px solid #707070;
  border-radius: 0;
  background: transparent;

  &.checkboxIcon-checked {
    background: #707070;
  }
}

.sportlist-wrap .MuiTab-wrapper {
  .fillblack svg {
    &,
    path {
      fill: #000;
    }
  }
}

body .MuiListItem-button:hover {
  background-color: transparent;
}

.activeSportTitle {
  display: flex;
}

// .backarrow {}

.MuiButton-label {
  .spinner {
    line-height: 16px;
    margin-left: 0;
    margin-top: 3px;
  }
}

@media (max-width: 599px) {
  .aboutus-members .section {
    padding-top: 0;
    padding-bottom: 25px;
  }

  .aboutus-members .user-view {
    margin-bottom: 25px;
  }

  .contact-title-wrap .mobile-heading {
    font-size: 30px;
    font-weight: 600;
  }
}

.admin-btn-green:not(:disabled) {
  background-color: #4455c7 !important;
  color: #fff !important;
  min-width: auto;
}

.admin-btn-orange:not(:disabled) {
  background-color: #ff6b00 !important;
  color: #fff !important;
}

.admin-btn-purple {
  background-color: #4455c7 !important;
  color: #fff !important;
  padding: 10px 15px !important;
  font-size: 16px !important;
  min-width: 104px !important;
  border-radius: 8px !important;
  line-height: 25px !important;
  height: 44px !important;
}

.purple {
  background-color: #4455c7 !important;
}

.outlined {
  border: 1px solid #4455c7 !important;
  background-color: #ffffff !important;
}

.admin-btn-outlined {
  border: 1px solid #4455c7 !important;
  background-color: #ffffff !important;
  padding: 13px 24px 12px !important;
  min-width: 84px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  height: 44px;
}

.admin-btn-back {
  font-size: 24px !important;
  min-width: 24px !important;
  border-radius: 50% !important;
  box-shadow: 1px 1px 1px 1px #e6e6e6;
  float: left;
  margin: 10px 5px 0px 0px !important;
  background-color: #ffffff !important;
  padding: 6px !important;
}

.admin-btn-margin {
  margin: 0px 0px 0px 0px !important;
}

.admin-page-heading {
  display: inline-flex;
  float: left;
  margin: 5px 15px 20px;
}

.tablePagination {
  display: inline-flex;
  margin-top: 27px;
  margin-bottom: 38px;

  .btn-navigation {
    min-width: auto;
    background: none;
    border: none;
    cursor: pointer;
    margin: 0 10px;

    svg {
      height: 15px;
      width: 16px;
    }
  }

  .MuiButtonBase-root {
    min-width: 25px;
    height: 25px;
  }

  .btn-navigation-disabled {
    min-width: auto;
    background: none;
    border: none;
    margin: 0 10px;
    cursor: auto;

    svg * {
      fill: #707070;
    }

    svg {
      height: 15px;
      width: 16px;
    }
  }
}

.reviewPagination {
  .MuiButtonBase-root {
    min-width: 25px !important;
    height: 25px;
  }
}

.admin-delete-loader {
  position: fixed;
  top: 50%;
  left: 50%;
}

.admin-delete-modal-loader {
  position: absolute;
  left: 50%;
  top: 50%;
}

.MuiGrid-grid-xs-10 {
  max-width: 85.7% !important;
  flex-basis: 85.7% !important;

  @media ((max-width: 1460px)) {
    max-width: 82.7% !important;
  }
}

.MuiGrid-grid-xs-2 {
  max-width: 14.3% !important;

  @media ((max-width: 1460px)) {
    max-width: 17.3% !important;
  }
}

.modal-label {
  font-size: 16px;
  font-family: "Inter" !important;
  font-weight: 600;
}

.details-search-picker {
  margin-top: 9px !important;

  .MuiOutlinedInput-adornedEnd {
    background-color: #ffffff;
  }

  .MuiOutlinedInput-input {
    padding: 13.5px 14px !important;
  }

  .MuiIconButton-root {
    min-width: 0px !important;
    padding: 0px 5px !important;
  }
}

.table-rows {
  td {
    padding: 6px 15px !important;
  }
}

.text-wrap {
  white-space: pre-wrap;
  overflow-wrap: break-word;
  max-width: 390px;
}

.table-btn {
  font-size: 16px;
  border-radius: 8px !important;
  span {
    font-size: 16px;
  }
}

.edit-btn {
  background-color: #ff7900 !important;
  color: white !important;
  border-radius: 8px !important;
  margin: 2px !important;
  min-width: 70px !important;
}
.disabled-btn {
  opacity: 0.5 !important;
}
.delete-btn {
  background-color: #cc0000 !important;
  color: white !important;
  border-radius: 8px !important;
  margin: 2px !important;
  min-width: 70px !important;
}

.info-btn {
  background-color: #4455c7 !important;
  color: white !important;
  border-radius: 8px !important;
  margin: 2px !important;
  min-width: 70px !important;
}

.modal-country-name {
  font-family: $primaryFont;
  margin-left: 15px;
  font-size: 18.72px;
}

.message {
  text-align: center;
  font-family: "Inter" !important;
}

.tablePadding {
  padding: 70px 0px;
}

.EditraceWrap {
  width: 100%;

  .text-label {
    font-size: 16px;
    font-family: "Inter" !important;
    font-weight: 600;
  }

  .textfield-tracks {
    width: 95%;

    .MuiOutlinedInput-root {
      font-size: 16px;
      border-radius: 8px;
      min-height: 45px;
      border: 1px solid #ddd;
      padding-left: 10px;
      width: 99%;
      font-family: "Inter" !important;
      font-weight: 500;
      margin-bottom: 15px;
      background-color: #ffffff;
    }
  }

  .select-box {
    .select-box-manual {
      font-size: 16px;
      border-radius: 8px;
      min-height: 45px;
      border: 1px solid #d4d6d8;
      padding-left: 10px;
      width: 95%;
      margin-bottom: 15px;
      margin-top: 4px;
    }

    .select-box-arrow {
      appearance: none;
      -moz-appearance: none;
      -webkit-appearance: none;
      background-image: url(../../images/dronArrow.svg);
      background-repeat: no-repeat;
      background-position: calc(100% - 13.8px);
      background-size: 10px;
    }

    .React {
      width: 95%;
      margin-top: 5px;
      font-size: 16px;
      border: 1px solid #d4d6d8;
      margin-bottom: 15px;
      border-radius: 8px;

      .select__control {
        min-height: 45px;
        border-radius: 8px;
      }

      // .select__indicators {
      //   appearance: none;
      //   -moz-appearance: none;
      //   -webkit-appearance: none;
      //   background-image: url(../../images/dronArrow.svg);
      //   background-repeat: no-repeat;
      //   background-position: calc(100% - 13.8px);
      //   background-size: 10px;
      // }
    }
  }

  .input-field {
    .react-datepicker-wrapper {
      width: 95%;
    }
  }
}

.search-track {
  @media (max-width: 1460px) {
    width: 350px !important;
  }
}

.select-box-track {
  .React {
    margin-bottom: 20px;

    .css-yk16xz-control {
      border-radius: 8px;
      padding: 3px;
    }
  }
}

.error-search {
  @media (max-width: 1470px) {
    width: 48% !important;
  }
}

.listTable {
  .table-country-flag {
    img {
      max-width: 40px;
      float: left;
      padding-right: 10px;
    }
  }
}
.market-error-table .MuiTableCell-head {
  padding: 10px;
}
.market-error-table {
  .table-rows {
    td {
      padding: 6px 10px !important;
    }
  }
}
.admin-fixture-wrap {
  .date-picker-fixture {
    margin-right: 10px;
    .MuiOutlinedInput-input {
      padding: 8px 14px;
      background-color: white;
    }
    .MuiOutlinedInput-adornedEnd {
      padding-right: 0px;
      border-radius: 8px;
      background-color: #ffffff;

      .MuiOutlinedInput-input {
        padding: 14.5px 0px 14.5px 12px;
      }
    }

    .MuiInputAdornment-root {
      .MuiButtonBase-root {
        min-width: 0px;
      }
    }
  }
  .advt-datepicker {
    .MuiFormHelperText-root.Mui-error {
      color: #f44336;
      position: absolute;
      bottom: -22px;
      z-index: 11;
    }
  }
}
