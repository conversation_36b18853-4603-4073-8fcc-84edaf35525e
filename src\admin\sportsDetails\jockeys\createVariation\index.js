import React, { Component } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
} from "@mui/material";
import axiosInstance from "../../../../helpers/Axios";
import { Loader } from "../../../../library/common/components";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../../../library/common/components/Button";
import ShowModal from "../../../../components/common/ShowModal/ShowModal";
import { URLS } from "../../../../library/common/constants";

class CreateVariation extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      isEditMode: false,
      isInputModalOpen: false,
      variationToSend: "",
      addInput: "",
      isModalOpen: false,
      itemToDelete: "",
    };
  }
  inputModal = (id, type, variation) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        idToSend: id,
        isEditMode: true,
        variationToSend: variation,
      });
    } else {
      this.setState({ isEditMode: false, addInput: "" });
    }
  };
  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };
  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };
  handleSave = async () => {
    const { playertypeName } = this.props;
    let payload = {
      variation: this.state?.addInput,
      //   TrackId: this.props?.id,
    };

    this.setState({ isLoading: true, isEditMode: false });
    let url =
      playertypeName === "Jockeys" || playertypeName === "Driver"
        ? URLS.getJockeys + `jockeys/jockey/variation/${this.props?.id}`
        : URLS.getJockeys + `trainers/trainer/variation/${this.props?.id}`;

    const { status } = await axiosInstance.post(url, payload);
    if (status === 200) {
      this.setState({ isLoading: false, isInputModalOpen: false });
      this.props.fetchAllJockey();
      //   this.setActionMessage(
      //     true,
      //     "Success",
      //     `Country variation Created Successfully`
      //   );
    }
  };
  handleUpdate = async () => {
    const { playertypeName } = this.props;
    let payload = {
      variation: this.state?.variationToSend,
      //   TrackId: this.props?.id,
    };
    this.setState({ isLoading: true, isEditMode: true });

    let url =
      playertypeName === "Jockeys" || playertypeName === "Driver"
        ? URLS.getJockeys + `jockeys/jockey/variation/${this.state?.idToSend}`
        : URLS.getJockeys +
          `trainers/trainer/variation/${this.state?.idToSend}`;
    const { status } = await axiosInstance.put(url, payload);
    if (status === 200) {
      this.setState({ isLoading: false, isInputModalOpen: false });
      this.props.fetchAllJockey();
      //   this.setActionMessage(
      //     true,
      //     "Success",
      //     `Country variation Edited Successfully`
      //   );
    }
  };
  deleteItem = async () => {
    const { playertypeName } = this.props;
    let url =
      playertypeName === "Jockeys" || playertypeName === "Driver"
        ? URLS.getJockeys +
          `jockeys/jockey/variation/${this.state?.itemToDelete}`
        : URLS.getJockeys +
          `trainers/trainer/variation/${this.state?.itemToDelete}`;

    try {
      const { status } = await axiosInstance.delete(url);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.props.fetchAllJockey();
        });
        // this.setActionMessage(
        //   true,
        //   "Success",
        //   "Country variation Deleted Successfully!"
        // );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };
  render() {
    const {
      isLoading,
      isEditMode,
      isInputModalOpen,
      variationToSend,
      addInput,
      isModalOpen,
    } = this.state;
    const { variationData, playertypeName } = this.props;

    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          {/* <Button
            className="modal-btn admin-btn-green"
            style={{ marginLeft: "auto", marginBottom: "10px" }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button> */}
          <Button
            variant="contained"
            style={{
              backgroundColor: "#4455C7",
              color: "#fff",
              borderRadius: "8px",
              textTransform: "capitalize",
              padding: "13px 24px 12px",
              marginLeft: "auto",
              marginBottom: "10px",
            }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button>
          <Grid item xs={12}>
            <Paper className="pageWrapper api-provider">
              {/* {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )} */}
              {isLoading ? (
                <Box className="message">
                  <Loader />
                </Box>
              ) : variationData?.[0]?.length > 0 ? (
                <>
                  {" "}
                  <TableContainer>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow className="table-rows listTable-Row">
                          <TableCell>Id</TableCell>
                          <TableCell>Variation</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {variationData?.[0]?.map((data) => (
                          <TableRow>
                            <TableCell>{data?.id}</TableCell>
                            <TableCell>{data?.variation}</TableCell>
                            <TableCell>
                              <Button
                                style={{ minWidth: "0px" }}
                                onClick={this.inputModal(
                                  data?.id,
                                  "edit",
                                  data?.variation
                                )}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                style={{ minWidth: "0px" }}
                                onClick={this.setItemToDelete(data?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              ) : (
                <Box className="message">No Variation Data Avilable</Box>
              )}
            </Paper>
          </Grid>
        </Grid>
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={this.toggleInputModal}
        >
          {/* {isLoading && <Loader />} */}
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {!isEditMode
                ? `Create ${playertypeName} variation`
                : `Edit ${playertypeName}  variation`}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleInputModal}
            />

            <Grid item xs={12} className="runnerInfo">
              <Grid
                item
                xs={12}
                className="runnerInfo-text"
                style={{ display: "flex", flexDirection: "column" }}
              >
                <label className="modal-label">
                  {playertypeName} Variation
                </label>
                <TextField
                  className="textfield-tracks"
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder={`${playertypeName}` + "variation"}
                  value={isEditMode ? variationToSend : addInput}
                  onChange={(e) => {
                    isEditMode
                      ? this.setState({ variationToSend: e.target.value })
                      : this.setState({ addInput: e.target.value });
                  }}
                />
              </Grid>
            </Grid>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={!addInput}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleUpdate}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={!variationToSend}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.toggleInputModal}
                    className="mr-lr-30"
                    value="Back"
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
        <ShowModal
          isModalOpen={isModalOpen}
          onClose={this.toggleModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={this.deleteItem}
          onCancel={this.toggleModal}
        />
      </>
    );
  }
}

export default CreateVariation;
