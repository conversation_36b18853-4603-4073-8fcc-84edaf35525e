import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
import { Link } from "react-router-dom";
import moment from "moment-timezone";
import SearchIcons from "../../images/searchIcon.svg";
import Select from "react-select";
import ButtonComponent from "../../library/common/components/Button";
import DateFnsUtils from "@date-io/date-fns";
import TodayIcon from "@mui/icons-material/Today";
import TableSortLabel from "@mui/material/TableSortLabel";
import { MdKeyboardBackspace } from "react-icons/md";
import { config } from "../../helpers/config";

let campaignDataArray = [
  {
    id: 1,
    title: "ABC",
    startDate: "12-09-2022",
    endDate: "12-10-2022",
    pageName: "XYZ",
    type: "Web",
  },
  {
    id: 2,
    title: "ABC",
    startDate: "12-09-2022",
    endDate: "12-10-2022",
    pageName: "XYZ",
    type: "App",
  },
  {
    id: 3,
    title: "ABC",
    startDate: "12-09-2022",
    endDate: "12-10-2022",
    pageName: "XYZ",
    type: "Both",
  },
];

const typeOptions = [
  {
    label: "web",
    value: 1,
  },

  {
    label: "app",
    value: 2,
  },
  {
    label: "both",
    value: 3,
  },
];

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

export default class CampaignInfo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      campaignInfoData: [],
      campaignInfoCount: 0,
      isInputModalOpen: false,
      isActivityModalOpen: false,
      isDetailsModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      userId: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      campaingValues: {
        id: "",
        type: "",
        page_id: null,
        mediaDetailId: null,
      },
      errorPage: "",
      errorMedia: "",
      errorType: "",
      search: "",
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      paginationPage: [],
      csvListData: [],
      clientDetail: [],
      sortStartDate: true,
      sortEndDate: true,
      sortDate: null,
      filterEndDate: null,
      startDateOpen: false,
      endDateOpen: false,
      sortType: "id",
      sortLabelid: false,
      sortName: true,
      sportsOption: [],
      pageData: [],
      pageOptions: [],
      mediaOptions: [],
      mediaOptionsData: [],
      mediaLoader: false,
      mediaOptionsFlag: false,
      editId: null,
    };
  }

  componentDidMount() {
    this.fetchCampaignInfolist(0, "id", false);
    // this.fetchAllSports();
    // this.fetchAllMedia();
  }

  fetchMediaOptions = async (id, type) => {
    const { campaignInfoData, campaingValues } = this.state;
    const { ClientId } = this.props.location.state;

    this.setState({ mediaLoader: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/campaign/mediadetails/findAll?clientId=${ClientId}`
      );
      if (status === 200) {
        let newdata = [];
        let optionTypeData = data?.result?.filter((item) => {
          return item?.type === type;
        });
        let optionData = optionTypeData?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        const sortedData = newdata?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        this.setState({
          mediaLoader: false,
          mediaOptionsFlag: true,
          mediaOptionsData: sortedData,
        });
      } else {
        this.setState({ mediaLoader: false });
      }
    } catch {
      this.setState({ mediaLoader: false });
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage, selectedPage, type, order } = this.state;

    const offsetNew = (Number(page) - 1) * rowPerPage;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
    this.fetchCampaignInfolist((Number(page) - 1) * rowPerPage, type, order);
  };

  inputModal = (item, type) => () => {
    let { offset, order, campaingValues, campaignInfoData, pageOptions } =
      this.state;

    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchPageData(item);
      this.fetchCurrentCampaignInfo(item?.id);
      this.fetchMediaOptions(
        campaignInfoData && campaignInfoData[0]?.Campaign?.ClientId,
        item?.MediaDetail?.type
      );
      this.setState({
        editId: item?.id,
      });
    } else {
      this.fetchPageData();
      this.setState({
        campaingValues: {
          id: "",
          type: "",
          page_id: null,
          mediaDetailId: null,
        },
        isEditMode: false,
        editId: null,
      });
    }
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      campaingValues: {
        id: "",
        type: "",
        page_id: null,
        mediaDetailId: null,
      },
      errorMedia: "",
      errorPage: "",
      mediaOptionsData: [],
      mediaOptionsFlag: false,
    });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  backToNavigatePage = () => {
    this.props.navigate(`/adcampaign`);
  };

  // async fetchAllSports() {
  //   this.setState({ isLoading: true });
  //   const { status, data } = await axiosInstance.get(URLS.sports);
  //   if (status === 200) {
  //     let newdata = [];
  //     let sportData = data.result?.map((item) => {
  //       newdata.push({
  //         label: item?.sportName,
  //         value: item?.id,
  //       });
  //     });
  //     let alldatas = {
  //       label: "All Sport",
  //       value: 0,
  //     };

  //     let alldata = [alldatas, ...newdata];

  //     // let pushData = newdata.push(alldatas);
  //     // let sortData = newdata?.sort((a, b) => {
  //     //   return a.value > b.value ? 1 : -1;
  //     // });
  //     this.setState({
  //       AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
  //         return a.label > b.label ? 1 : -1;
  //       }),
  //       isLoading: false,
  //       pageOptions: newdata?.sort((a, b) => {
  //         return a.label > b.label ? 1 : -1;
  //       }),
  //     });
  //   }
  // }

  fetchPageData = async (editPageData) => {
    const advData = [];
    let campaignId = this.props.match.params.id;
    try {
      const { status, data } = await axiosInstance.get(
        `/campaign/pagelist/${campaignId}`
      );
      if (status === 200) {
        let newdata = [];
        let listdata = data?.result?.map((item) => {
          newdata.push({
            label: item?.pageName,
            value: item?.id,
          });
        });
        editPageData &&
          newdata.push({
            label: editPageData?.page_name,
            value: editPageData?.page_id,
          });
        const sortedData = newdata?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        this.setState({
          pageOptions: sortedData,
        });
      }
    } catch {}
  };

  fetchPageList = () => {
    const { pageData } = this.state;
    let newdata = [];
    let listdata = pageData?.map((item) => {
      newdata.push({
        label: item?.pageName,
        value: item?.id,
      });
    });
    const sortedData = newdata?.sort((a, b) => {
      return a?.label.localeCompare(b?.label);
    });
    this.setState({
      pageOptions: sortedData,
      // ExternalAdvPageListOptions: [
      //   {
      //     label: "All Pages",
      //     value: 0,
      //   },
      //   ...sortedData,
      // ],
    });
  };

  fetchCampaignInfolist = async (offsetsize, type, order) => {
    let id = this.props.match.params.id;
    const { rowPerPage, search } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/campaign/campaginAds?campaignId=${id}&limit=${rowPerPage}&offset=${offsetsize}&key=${
          type ? type : ""
        }&order=${order ? "ASC" : "DESC"}&search=${search}&timeZone=${timezone}`
      );
      if (status === 200) {
        const pageNumbers = [];
        if (data?.count > 0) {
          for (let i = 1; i <= Math.ceil(data?.count / rowPerPage); i++) {
            pageNumbers.push(i);
          }
        }
        this.setState({
          isLoading: false,
          campaignInfoData: data?.result,
          campaignInfoCount: data?.count,
          paginationPage: pageNumbers,
          offset: offsetsize,
          mediaOptionsData: [],
        });
      }
    } catch {
      this.setState({ isLoading: false });
    }
  };

  handleValidate = () => {
    let { campaingValues, mediaOptionsData } = this.state;
    let flag = true;
    // if (mediaOptionsData?.length > 0) {
    if (
      campaingValues?.mediaDetailId === "" ||
      campaingValues?.mediaDetailId === null
    ) {
      flag = false;
      this.setState({
        errorMedia: "This field is mandatory, Select a Type first",
      });
    } else {
      this.setState({
        errorMedia: "",
      });
    }
    // }

    if (campaingValues?.page_id === "" || campaingValues?.page_id === null) {
      flag = false;
      this.setState({
        errorPage: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPage: "",
      });
    }

    return flag;
  };

  deleteItem = async () => {
    this.setState({ isModalOpen: false });
    const { offset, type, order, sortDate, filterEndDate, itemToDelete } =
      this.state;
    try {
      const { status } = await axiosInstance.delete(
        `/campaign/campaginAds/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchCampaignInfolist(offset, type, order);
        });
        this.setActionMessage(
          true,
          "Success",
          "Campaign Info Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  fetchCurrentCampaignInfo = async (id) => {
    const { status, data } = await axiosInstance.get(
      `/campaign/campaginAds/${id} `
    );
    if (status === 200) {
      // const pageId = 2;
      // const pageName = "Tennis";
      // const convertedObject =
      //   {
      //     label: data?.result?.page_name,
      //     value: data?.result?.page_id,
      //   }
      // let finalObjct = JSON.stringify(convertedObject);
      let pageIdObt = {
        value: data?.result?.page_id,
        label: data?.result?.page_name,
      };
      this.setState({
        campaingValues: {
          id: data?.result?.CampaignId,
          type: data?.result?.MediaDetail?.type,
          // type: data?.result?.type,
          page_id: pageIdObt,
          // page_id: data?.result?.page_id,
          mediaDetailId: data?.result?.MediaDetailId,
        },
        isEditMode: true,
      });
    }
  };

  handleSave = async () => {
    const { HomeArticleData, sortDate, filterEndDate } = this.state;
    let id = this.props.match.params.id;
    if (this.handleValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const {
        campaignInfoData,
        campaingValues,
        offset,
        type,
        order,
        sortName,
        sortType,
        sortLabelid,
        selectedPage,
      } = this.state;
      let sortData = sortType === "id" ? sortLabelid : sortName;

      // let payload = {
      //   campaignId: parseInt(id, 10),
      //   mediaDetailId: campaingValues?.mediaDetailId,
      //   page_id: campaingValues?.page_id?.map((item) => item?.value),
      //   page_name: campaingValues?.page_id?.map((item) => item?.label),
      // };
      let payload = {
        campaignId: parseInt(id, 10),
        mediaDetailId: campaingValues?.mediaDetailId,
        page_id: [campaingValues?.page_id?.value],
        page_name: [campaingValues?.page_id?.label],
      };
      try {
        const { status, data } = await axiosInstance.post(
          `/campaign/campaginAds?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
            campaingValues: {
              ...campaingValues,
              page_id: null,
            },
          });
          this.fetchCampaignInfolist(offset, sortType, sortData);

          this.setActionMessage(
            true,
            "Success",
            `Campaign Info Created Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          data?.message &&
            this.setActionMessage(
              true,
              "Error",
              `The Campaign for ${data?.message}`
            );

          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(
          true,
          "Error",
          `The Campaign for ${err?.response?.data?.message}`
        );

        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  handleUpdate = async () => {
    const { HomeArticleData, sortDate, filterEndDate } = this.state;
    let id = this.props.match.params.id;
    if (this.handleValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const {
        campaignInfoData,
        campaingValues,
        type,
        order,
        editId,
        image,
        script,
        uploadImage,
        offset,
        sortEndDate,
        sortStartDate,
        sortName,
        sortType,
        sortLabelid,
      } = this.state;
      let sortData = sortType === "id" ? sortLabelid : sortName;
      let payload = {
        campaignId: parseInt(id, 10),
        mediaDetailId: campaingValues?.mediaDetailId,
        page_id: campaingValues?.page_id?.value,
        page_name: campaingValues?.page_id?.label,
      };
      try {
        const { status, data } = await axiosInstance.put(
          `/campaign/campaginAds/${editId}?timeZone=${timezone}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
          });
          this.fetchCampaignInfolist(offset, sortType, sortData);
          this.setActionMessage(
            true,
            "Success",
            `Campaign Info Edited Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  handleSortStartDate = (date) => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      filterEndDate,
    } = this.state;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        sortType,
        sortData,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        filterEndDate
      );
    }
  };
  handleFilterEndDate = (date) => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      sortDate,
    } = this.state;
    this.setState({
      filterEndDate: date
        ? moment(date).tz(timezone).format("YYYY-MM-DD")
        : null,
    });
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchTableAdlist(
        selectedPage,
        selectedBanner,
        0,
        sortType,
        sortData,
        sortDate,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null
      );
    }
  };

  clearStartDate = () => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      filterEndDate,
    } = this.state;
    this.setState({
      sortDate: null,
      startDateOpen: false,
    });
    this.fetchTableAdlist(
      selectedPage,
      selectedBanner,
      0,
      sortType,
      sortData,
      null,
      filterEndDate
    );
  };
  clearEndDate = () => {
    const {
      selectedPage,
      selectedBanner,
      offset,
      sortType,
      sortData,
      sortDate,
    } = this.state;
    this.setState({
      filterEndDate: null,
      endDateOpen: false,
    });
    this.fetchTableAdlist(
      selectedPage,
      selectedBanner,
      0,
      sortType,
      sortData,
      sortDate,
      null
    );
  };

  sortLabelHandler = (type) => {
    const { sortLabelid, sortName } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchCampaignInfolist(0, type, !sortLabelid);
      this.setState({
        sortLabelid: !sortLabelid,
        sortName: true,
        currentPage: 1,
      });
    } else if (type === "page_name") {
      this.fetchCampaignInfolist(0, type, !sortName);
      this.setState({
        sortLabelid: false,
        sortName: !sortName,
        currentPage: 1,
      });
    }
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { type, order } = this.state;
    if (event.key === "Enter") {
      this.fetchCampaignInfolist(0, type, order);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      campaignInfoData,
      isModalOpen,
      rowPerPage,
      currentPage,
      messageBox,
      isLoading,
      paginationPage,
      isInputModalOpen,
      isEditMode,
      campaignInfoCount,
      campaingValues,
      mediaLoader,
      mediaOptionsFlag,
      offset,
      userId,
      search,
      errorTitle,
      errorPage,
      errorMedia,
      errorType,
      errorstartDate,
      errorendDate,
      sortType,
      sortLabelid,
      pageOptions,
      mediaOptions,
      mediaOptionsData,
      campaignCount,
      errorClient,
      type,
      order,
      sortName,
      sortDate,
      filterEndDate,
      startDateOpen,
      endDateOpen,
    } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Campaign
                </Link>
                <Link underline="hover" color="inherit" to="/adcampaign">
                  Ad Campaign
                </Link>
                <Typography className="active_p">Campaign Info</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    columnGap: "20px",
                  }}
                >
                  <Button
                    className="admin-btn-margin admin-btn-back"
                    onClick={this.backToNavigatePage}
                  >
                    <MdKeyboardBackspace />
                  </Button>
                  <Typography variant="h1" align="left">
                    Campaign Info
                  </Typography>
                </Box>
              </Grid>
              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap future-fixture-wrap"
              >
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    // this.fetchAllTeam(0);
                    this.fetchCampaignInfolist(0, type, order);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && campaignInfoData?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && campaignInfoData?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ width: "10%" }}
                        >
                          DID{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortLabelid
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("page_name")}
                          style={{ width: "15%" }}
                        >
                          Page Name{" "}
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "page_name"
                                ? sortName
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell>Clicks</TableCell>
                        <TableCell>Impressions</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {campaignInfoData?.length > 0 ? (
                        campaignInfoData?.map((campaign, i) => (
                          <TableRow
                            key={i}
                            className="table-rows listTable-Row"
                          >
                            <TableCell>{campaign?.id}</TableCell>
                            <TableCell>{campaign?.page_name}</TableCell>
                            <TableCell>{campaign?.totalClick}</TableCell>
                            <TableCell>{campaign?.totalImpression}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(campaign, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(campaign?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                campaignInfoCount / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={paginationPage[paginationPage?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create Campaign Info" : "Edit Campaign Info"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        className="radio-wrap ad-radio"
                        style={{ marginTop: "15px" }}
                      >
                        <FormControl component="fieldset">
                          <label className="modal-label"> Ad Type </label>
                          <RadioGroup
                            aria-label="Ad Type"
                            name="Ad Type"
                            className="gender"
                            value={campaingValues?.type}
                            onChange={(e) => {
                              this.setState({
                                campaingValues: {
                                  ...campaingValues,
                                  type: e.target.value,
                                },
                                createError: "",
                              });
                              this.fetchMediaOptions(
                                campaignInfoData &&
                                  campaignInfoData[0]?.Campaign?.ClientId,
                                e.target.value
                              );
                            }}
                          >
                            <FormControlLabel
                              value="image"
                              control={
                                <Radio
                                  color="primary"
                                  checked={campaingValues?.type === "image"}
                                />
                              }
                              label="Image"
                            />
                            <FormControlLabel
                              value="script"
                              control={
                                <Radio
                                  color="primary"
                                  checked={campaingValues?.type === "script"}
                                />
                              }
                              label="Script"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorType && campaingValues?.type === "" ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorType}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        className="national-select teamsport-text"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label"> Media </label>
                        <Select
                          className="React teamsport-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Select Media"
                          value={mediaOptionsData?.find((item) => {
                            return (
                              item?.value == campaingValues?.mediaDetailId &&
                              campaingValues?.mediaDetailId
                            );
                          })}
                          onChange={(option) => {
                            this.setState({
                              campaingValues: {
                                ...campaingValues,
                                mediaDetailId: option?.value,
                              },
                            });
                          }}
                          options={mediaOptionsData}
                          isDisabled={
                            mediaOptionsData?.length > 0 ? false : true
                          }
                          isLoading={mediaLoader}
                        />
                        {errorMedia && !mediaOptionsFlag ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorMedia}
                          </p>
                        ) : mediaOptionsFlag === true &&
                          mediaOptionsData?.length === 0 ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            Media for this client is not available
                          </p>
                        ) : (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorMedia}
                          </p>
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        className="national-select teamsport-text"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label"> Page </label>
                        <Select
                          className="React cricket-select sponsored-select-modal"
                          classNamePrefix="select"
                          placeholder="Select Pages"
                          menuPosition="fixed"
                          // isMulti={isEditMode ? false : true}
                          value={pageOptions?.find((item) => {
                            return (
                              item?.value === campaingValues?.page_id?.value
                            );
                          })}
                          isLoading={isLoading}
                          onChange={(e) => {
                            this.setState({
                              campaingValues: {
                                ...campaingValues,
                                page_id: e,
                              },
                            });
                          }}
                          options={pageOptions}
                          // isOptionDisabled={() =>
                          //   campaingValues?.page_id?.length >= 2
                          // }
                        />
                        {errorPage ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorPage}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
