@import "../../assets/scss/variables.scss";
.pageWrapper {
  .breadcrumb {
    color: #191919;
    font-size: 11.42px;
    line-height: 14px;
    text-transform: uppercase;
  }

  .active_p {
    font-size: 11.42px;
    line-height: 14px;
    color: #4455c7;
    text-transform: uppercase;
  }

  h1 {
    font-size: 43.9px;
    font-weight: normal;
    line-height: 65px;
    letter-spacing: 0;
    font-family: VeneerClean-Soft;
    margin: 0px;
  }
}

.addButton.btn {
  border-radius: 8px;
  padding: 13px 24px 12px;
  width: 106px;
}

.sport-tab {
  padding: 19px 18px 9px 18px;
  box-shadow: 0px 3px 9px 0px #0000000d;
  background: #ffffff;

  .racing-tab-detail {
    border-bottom: 2px solid #4455c7;
  }

  .MuiTab-root {
    min-width: 120px;
    opacity: 1;
  }

  .MuiButtonBase-root.active {
    color: #003764;
  }

  .MuiButtonBase-root {
    font-size: 16px;
    font-family: VeneerClean-Soft !important;
    color: #191919;
    line-height: 23px;
  }
  .MuiTabs-indicator {
    height: 3px;
    background-color: #003764;
  }

  .Filteritemlist-wrap {
    display: flex;
    align-items: center;
    margin-top: 9px;
    img {
      max-width: none;
    }
    .Filteritemlist-datepicker {
      display: contents;
      .MuiGrid-container {
        background-color: #ffffff;
      }
      .MuiFormControl-marginNormal {
        margin: 0px;
      }

      .MuiOutlinedInput-input {
        padding: 10.5px 14px;
      }
    }
  }

  .Filteritemlist-racing {
    display: flex;
    list-style-type: none;
    padding: 0px;
    margin: 0px;

    li {
      margin-right: 14px;

      label {
        display: flex;
        grid-column-gap: 5.2px;
        column-gap: 5.2px;
        font-size: 12px;
        line-height: 15px;
        align-items: center;

        .PrivateSwitchBase-root-3 {
          padding: 0px;
        }

        .PrivateSwitchBase-root-4 {
          padding: 0px;
        }
      }
    }
  }
}

.fixture {
  background: #78c2a7;
}

.notfixture {
  background: #d84727;
  cursor: pointer;
}

.ignore {
  background: #d4d6d8;
  cursor: pointer;
}
body .fetch-btn {
  background-color: #4455c7;
  color: #fff;
  border-radius: 8px;
  text-transform: capitalize;
  padding: 13px 20px 12px;
  margin-bottom: 15px;
}
body .fetch-btn:hover {
  background-color: #4455c7;
}
