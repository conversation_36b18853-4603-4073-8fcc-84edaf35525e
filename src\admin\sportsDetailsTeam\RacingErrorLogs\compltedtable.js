import React, { Component } from "react";
import {
  Button,
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Box,
  Modal,
  TextareaAutosize,
} from "@mui/material";
import moment from "moment";
import { Loader } from "../../../library/common/components";
import "./errorLogs.scss";
import Pagination from "@mui/material/Pagination";
// import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
// import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
// import { th } from "date-fns/locale";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import axiosInstance from "../../../helpers/Axios";
import Select from "react-select";
import ButtonComponent from "../../../library/common/components/Button";
import CancelIcon from "@mui/icons-material/Cancel";

const StatusOptions = [
  { label: "open", value: "open" },
  { label: "completed", value: "completed" },
  { label: "inprogress", value: "inprogress" },
];

const searchParams = new URLSearchParams(window.location.search);
const errorTabData = searchParams.get("errorTab");
export class Compltedtable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      open: "",
      isErrorDeleteModal: false,
      errorid: "",
      deleteType: "",
      errorStatusModal: false,
      seletedValue: "",
      isLoading: false,
      reportId: "",
      providersDetails: [],
    };
  }
  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      // offset
    } = this.state;

    this.props.currentPagechange(
      Number(page),
      (Number(page) - 1) * this.props.rowPerPage
    );
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  bookmakerName = (bookmakerId) => {
    let { providersDetails } = this.state;

    let provider = providersDetails?.filter((item) => {
      return item?.apiProviderId === bookmakerId;
    });
    return provider?.[0]?.name;
  };
  fetchProviders = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeepers/`
      );
      if (status === 200) {
        let activeProvider = data?.result?.filter((item) => {
          return item?.status === "active";
        });
        this.setState({
          providersDetails: activeProvider,
        });
      }
    } catch (err) {}
  };
  navigateToErrorDetails = (data) => {
    this.props.navigate(`/racing/errorLogs/errorDetails/${data?.id}`, {
      state: {
        detail: data,
        offset: this.state?.offset,
        rowPerPage: this.props?.rowPerPage,
        errorType: 0,
        selectType:
          this.props?.selectedType !== null ? this.props?.selectedType : "",
      },
    });
  };
  handleErrorDelateModal = (id, type) => {
    this.setState({ isErrorDeleteModal: true, errorid: id, deleteType: type });
  };
  togglehandleErrorDelateModal = () => {
    this.setState({ isErrorDeleteModal: false });
  };
  handleErrorDelateReport = async () => {
    this.setState({ isLoading: true });

    try {
      let { status, data } = await axiosInstance.delete(
        `adminNotification/reports/${this.state?.errorid}?type=${this.state.deleteType}`
      );
      if (status === 200) {
        this.setState({ isLoading: false, errorid: "", deleteType: "" });
        this.props.afterRefresh();
        this.togglehandleErrorDelateModal();
      }
    } catch (err) {
      this.setState({ isLoading: false });
    }
  };
  hadleStausUpdate = (data) => {
    this.setState({
      errorStatusModal: true,
      seletedValue: data?.status,
      reportId: data?.id,
    });
  };
  togglehadleStausUpdate = () => {
    this.setState({ errorStatusModal: false });
  };
  handleSave = async () => {
    let payload = {
      reportStatus: this.state?.seletedValue,
    };
    this.setState({ isLoading: true });
    const { status } = await axiosInstance.put(
      `adminNotification/reports/${this.state?.reportId}`,
      payload
    );
    if (status === 200) {
      this.setState({ isLoading: false, errorStatusModal: false });
      this.props.afterRefresh();
      // this.props.fetchAllTracks();
    }
  };
  reportMeetingBlank = () => {
    let reportmeeting = this.props.newdata?.filter(
      (item) => item?.ReportMeetings?.length > 0
    );
    return reportmeeting?.length > 0 ? true : false;
  };
  componentDidMount = () => {
    this.reportMeetingBlank();
    this.fetchProviders();
  };
  render() {
    let {
      open,
      isErrorDeleteModal,
      errorid,
      errorStatusModal,
      seletedValue,
      reportId,
    } = this.state;
    let { sportCount, currentPage, rowPerPage, isLoading } = this.props;
    const pageNumbers = [];

    if (sportCount > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = tracksDetails.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        {isLoading ? (
          <Box style={{ paddingTop: "20px" }}>
            <Loader />
          </Box>
        ) : (
          <Box>
            {!isLoading && this.props?.newdata?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && this.props?.newdata?.length > 0 && (
              <TableContainer component={Paper}>
                <Table
                  className="listTable api-provider-listTable"
                  aria-label="simple table"
                >
                  <TableHead>
                    <TableRow className="tableHead-row">
                      {/* <TableCell style={{ width: "5px" }}></TableCell> */}
                      <TableCell>Id</TableCell>
                      {/* <TableCell>Status</TableCell>
                  <TableCell>Value To Insert</TableCell> */}
                      <TableCell>Bookmaker Name</TableCell>
                      <TableCell>Name</TableCell>
                      {/* <TableCell>Import Type</TableCell> */}

                      {/* <TableCell>Working Status</TableCell> */}
                      <TableCell style={{ minWidth: "185px" }}>
                        CreatedAt
                      </TableCell>
                      <TableCell style={{ minWidth: "185px" }}>
                        UpdatedAt
                      </TableCell>

                      <TableCell>Report Status</TableCell>
                      <TableCell>Update Status</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>UserName</TableCell>
                      <TableCell>URL</TableCell>
                      <TableCell>Data</TableCell>
                      <TableCell>Server</TableCell>
                      <TableCell>Proxy</TableCell>
                      <TableCell>Error Details</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {this.props?.newdata?.length > 0 ? (
                      this.reportMeetingBlank() ? (
                        this.props?.newdata?.map((item, index) => {
                          let isColleps = item?.ReportMeetings?.filter(
                            (subItem) => {
                              return subItem?.errors?.length != 0;
                            }
                          );

                          return (
                            <>
                              {(
                                item?.type === "scrap"
                                  ? true
                                  : item?.ReportMeetings?.length > 0
                              ) ? (
                                <TableRow className="listTable-Row">
                                  {/* <TableCell style={{ width: "5px" }}>
                          {/* <IconButton
                            aria-label="expand row"
                            size="small"
                            className="colleps-arrow"
                            onClick={() =>
                              this.setState({
                                open: open == item?.id ? "" : item?.id,
                              })
                            }
                          >
                            {open === item?.id ? (
                              <KeyboardArrowUpIcon />
                            ) : (
                              <KeyboardArrowDownIcon />
                            )}
                          </IconButton> 
                        </TableCell> */}
                                  <TableCell>{item?.id}</TableCell>
                                  <TableCell>
                                    {this.bookmakerName(item?.providerId)}
                                  </TableCell>

                                  <TableCell>{item?.name}</TableCell>
                                  <TableCell>
                                    {" "}
                                    {moment(item?.createdAt).format(
                                      "DD/MM/YYYY h:mm:ss a"
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    {" "}
                                    {moment(item?.updatedAt).format(
                                      "DD/MM/YYYY h:mm:ss a"
                                    )}
                                  </TableCell>

                                  <TableCell
                                    style={{ textTransform: "capitalize" }}
                                  >
                                    {item?.status}
                                  </TableCell>
                                  <TableCell>{item?.reportStatus}</TableCell>
                                  <TableCell>{item?.type}</TableCell>
                                  <TableCell>{item?.User?.username}</TableCell>
                                  <TableCell>
                                    <a href={item?.url} target="_blank">
                                      URL
                                    </a>
                                  </TableCell>
                                  <TableCell>
                                    <TextareaAutosize
                                      maxRows={4}
                                      minRows={3}
                                      aria-label="maximum height"
                                      placeholder="data"
                                      defaultValue={item?.data}
                                    />
                                  </TableCell>
                                  <TableCell>{item?.server}</TableCell>
                                  <TableCell>{item?.proxy}</TableCell>
                                  <TableCell
                                    style={{ cursor: "pointer" }}
                                    onClick={() => {
                                      return item?.ReportMeetings?.length > 0
                                        ? this.navigateToErrorDetails(item)
                                        : "";
                                    }}
                                  >
                                    {item?.ReportMeetings?.length > 0
                                      ? "Error Details"
                                      : ""}
                                  </TableCell>
                                  <TableCell>
                                    <Button
                                      className="table-btn edit-btn"
                                      onClick={() => {
                                        this.hadleStausUpdate(item);
                                      }}
                                      style={{ cursor: "pointer" }}
                                    >
                                      Edit
                                    </Button>
                                    <Button
                                      style={{ minWidth: "0px" }}
                                      onClick={() =>
                                        this.handleErrorDelateModal(
                                          item?.id,
                                          item?.type
                                        )
                                      }
                                      className="table-btn delete-btn"
                                    >
                                      Delete
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ) : (
                                ""
                              )}
                              {/* <TableRow className="colleps-row">
                        <TableCell
                          colSpan={100}
                          style={{
                            padding: "0px",
                            borderBottom: "none",
                          }}
                        >
                          {/* <Collapse
                            in={open === item?.id}
                            timeout="auto"
                            unmountOnExit
                          >
                            {isColleps?.length > 0 ? (
                              <Box>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow className="tableHead-row">
                                      <TableCell></TableCell>
                                      <TableCell>Meeting Name</TableCell>
                                      <TableCell>Description</TableCell>
                                      {/* <TableCell>Error Date</TableCell> 
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {item?.ReportMeetings &&
                                      item?.ReportMeetings?.map((obj) => {
                                        return (
                                          <>
                                            {obj?.errors?.length > 0 &&
                                              obj?.errors?.map((data) => {
                                                return (
                                                  <>
                                                    <TableRow className="listTable-Row">
                                                      <TableCell></TableCell>
                                                      <TableCell>
                                                        {obj?.name}
                                                      </TableCell>

                                                      <TableCell>
                                                        {data?.message}
                                                      </TableCell>
                                                      {/* <TableCell>
                                                  {" "}
                                                  {moment(
                                                    data.createdAt
                                                  ).format("DD/MM/YYYY ")}
                                                </TableCell> 
                                                </TableRow>
                                              </>
                                            );
                                          })}
                                        </>
                                      );
                                    })}
                                  </TableBody>
                                </Table>
                              </Box>
                            ) : (
                              <Box
                                className="no-data"
                                style={{
                                  textAlign: "center",
                                  fontSize: "16px",
                                }}
                              >
                                <p>No Errors Available</p>
                              </Box>
                            )}
                          </Collapse> 
                        </TableCell>
                      </TableRow> */}
                            </>
                          );
                        })
                      ) : (
                        <TableRow className="listTable-Row">
                          <TableCell
                            className="no-data"
                            style={{
                              textAlign: "center",
                              fontSize: "16px",
                            }}
                            colSpan={100}
                          >
                            <p>No Errors Available</p>
                          </TableCell>
                        </TableRow>
                      )
                    ) : (
                      <TableRow className="listTable-Row">
                        <TableCell
                          className="no-data"
                          style={{
                            textAlign: "center",
                            fontSize: "16px",
                          }}
                          colSpan={100}
                        >
                          <p>No Errors Available</p>
                        </TableCell>
                      </TableRow>
                    )}
                    {this.reportMeetingBlank() && (
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                            className={
                              offset >= 20
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={offset >= 20 ? false : true}
                            // disabled={
                            //   meetingsDetails.length / rowPerPage > 1 ? false : true
                            // }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}

                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                this.props?.sportCount > 0 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />

                            {/* <button
                            className={
                              rowPerPage + offset < sportCount
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              rowPerPage + offset < sportCount ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        )}
        <Modal
          className="modal modal-input"
          open={errorStatusModal}
          onClose={this.togglehadleStausUpdate}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">Error Update Status</h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.togglehadleStausUpdate}
            />
            <Box>
              <Grid item xs={12}>
                <Box className="select-box">
                  <label className="modal-label">Status</label>
                  <Select
                    className="React "
                    classNamePrefix="select"
                    menuPosition="fixed"
                    // menuShouldBlockScroll
                    // value={seletedValue}
                    value={
                      // StatusOptions &&
                      StatusOptions?.find((op) => {
                        return op?.value === seletedValue;
                      })
                    }
                    onChange={(e) =>
                      this.setState({
                        seletedValue: e?.value,
                      })
                    }
                    options={StatusOptions && StatusOptions}
                    // options={StatusOptions}
                  />
                </Box>
              </Grid>
              <Grid container>
                <Grid item xs={3}>
                  <div style={{ marginTop: "20px", display: "flex" }}>
                    <ButtonComponent
                      className="mt-3 purple"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Updating..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />

                    <Button
                      className="mr-lr-30 outlined"
                      variant="outlined"
                      // color="primary"
                      onClick={this.togglehadleStausUpdate}
                      style={{ minWidth: "auto" }}
                    >
                      Back
                    </Button>
                  </div>
                </Grid>
              </Grid>
            </Box>
          </div>
        </Modal>
        <ShowModal
          isModalOpen={isErrorDeleteModal}
          onClose={this.togglehandleErrorDelateModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={() => {
            this.handleErrorDelateReport();
          }}
          onCancel={this.togglehandleErrorDelateModal}
        />
      </>
    );
  }
}

export default Compltedtable;
