import React, { Component, createRef } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Button,
  Modal,
  TextField,
  Box,
} from "@mui/material";
// import { variationFormModel } from "./form-constant";
import Form from "../../../../library/common/components/Form";
import ButtonComponent from "../../../../library/common/components/Button";
import ActionMessage from "../../../../library/common/components/ActionMessage";
import { URLS } from "../../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../../library/utilities";
import axiosInstance from "../../../../helpers/Axios";
import { setValidation } from "../../../../helpers/common";
import CancelIcon from "@mui/icons-material/Cancel";
import ShowModal from "../../../../components/common/ShowModal/ShowModal";
import { Loader } from "../../../../library/common/components";

class CreateLabelVariation extends Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
      isEditMode: false,
      isInputModalOpen: false,
      isModalOpen: false,
      itemToDelete: null,
      variationToSend: "",
      addInput: "",
      idToSend: "",
      LabelVariationData: [],
    };
  }
  componentDidMount() {
    this.fetchAllLabelVariation();
  }

  fetchAllLabelVariation = async () => {
    this.setState({ isLoading: true });
    const passApi = this.props?.pathName?.includes("cricket")
      ? `crickets/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("rugbyleague")
      ? `rls/label/variation/${this.props?.labelValues?.id}?SportId=12`
      : this.props?.pathName?.includes("rugbyunion")
      ? `rls/label/variation/${this.props?.labelValues?.id}?SportId=13`
      : this.props?.pathName?.includes("basketball")
      ? `nba/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("afl")
      ? `afl/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("australianrules")
      ? `ar/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("golf")
      ? `golf/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("tennis")
      ? `tennis/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("baseball")
      ? `baseball/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("icehockey")
      ? `icehockey/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("boxing")
      ? `boxing/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("mma")
      ? `mma/label/variation/${this.props?.labelValues?.id}`
      : this.props?.pathName?.includes("soccer")
      ? `soccer/label/variation/${this.props?.labelValues?.id}`
      : `rls/label/variation/${this.props?.labelValues?.id}?SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          LabelVariationData: data?.result,
        });
        // this.setActionMessage(
        //   true,
        //   "Success",
        //   `Label variation Created Successfully`
        // );
      } else {
        // this.setActionMessage(true, "Error", data?.message);
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
        });
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({
        isLoading: false,
        isInputModalOpen: false,
      });
    }
  };

  inputModal = (id, type, variation) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        idToSend: id,
        isEditMode: true,
        variationToSend: variation,
      });
    } else {
      this.setState({ isEditMode: false, addInput: "" });
    }
  };
  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };
  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  handleSave = async () => {
    let payload = {
      variation: this.state?.addInput,
      //   marketId: this.props?.labelValues?.id,
    };
    if (
      this.props?.pathName?.includes("cricket") ||
      this.props?.pathName?.includes("basketball") ||
      this.props?.pathName?.includes("afl") ||
      this.props?.pathName?.includes("australianrules") ||
      this.props?.pathName?.includes("golf") ||
      this.props?.pathName?.includes("tennis") ||
      this.props?.pathName?.includes("baseball") ||
      this.props?.pathName?.includes("icehockey") ||
      this.props?.pathName?.includes("boxing") ||
      this.props?.pathName?.includes("mma") ||
      this.props?.pathName?.includes("soccer")
    ) {
      payload = {
        ...payload,
      };
    } else {
      payload = {
        ...payload,
        SportId: this.props?.pathName?.includes("rugbyleague")
          ? 12
          : this.props?.pathName?.includes("rugbyunion")
          ? 13
          : 14,
      };
    }
    this.setState({ isLoading: true, isEditMode: false });
    let passApi = this.props?.pathName?.includes("cricket")
      ? "crickets"
      : this.props?.pathName?.includes("basketball")
      ? "nba"
      : this.props?.pathName?.includes("afl")
      ? "afl"
      : this.props?.pathName?.includes("australianrules")
      ? "ar"
      : this.props?.pathName?.includes("golf")
      ? "golf"
      : this.props?.pathName?.includes("tennis")
      ? "tennis"
      : this.props?.pathName?.includes("baseball")
      ? "baseball"
      : this.props?.pathName?.includes("icehockey")
      ? "icehockey"
      : this.props?.pathName?.includes("boxing")
      ? "boxing"
      : this.props?.pathName?.includes("mma")
      ? "mma"
      : this.props?.pathName?.includes("soccer")
      ? "soccer"
      : "rls";
    try {
      const { data, status } = await axiosInstance.post(
        `${passApi}/label/variation/${this.props?.labelValues?.id}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllLabelVariation();
        this.setActionMessage(
          true,
          "Success",
          `Label variation Created Successfully`
        );
      } else {
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({ isLoading: false, isInputModalOpen: false });
    }
  };
  handleUpdate = async () => {
    let payload = {
      variation: this.state?.variationToSend,
      //   marketId: this.props?.labelValues?.id,
    };
    if (
      this.props?.pathName?.includes("cricket") ||
      this.props?.pathName?.includes("basketball") ||
      this.props?.pathName?.includes("afl") ||
      this.props?.pathName?.includes("australianrules") ||
      this.props?.pathName?.includes("golf") ||
      this.props?.pathName?.includes("tennis") ||
      this.props?.pathName?.includes("baseball") ||
      this.props?.pathName?.includes("icehockey") ||
      this.props?.pathName?.includes("boxing") ||
      this.props?.pathName?.includes("mma") ||
      this.props?.pathName?.includes("soccer")
    ) {
      payload = {
        ...payload,
      };
    } else {
      payload = {
        ...payload,
        SportId: this.props?.pathName?.includes("rugbyleague")
          ? 12
          : this.props?.pathName?.includes("rugbyunion")
          ? 13
          : 14,
      };
    }
    this.setState({ isLoading: true, isEditMode: true });
    let passApi = this.props?.pathName?.includes("cricket")
      ? "crickets"
      : this.props?.pathName?.includes("basketball")
      ? "nba"
      : this.props?.pathName?.includes("afl")
      ? "afl"
      : this.props?.pathName?.includes("australianrules")
      ? "ar"
      : this.props?.pathName?.includes("golf")
      ? "golf"
      : this.props?.pathName?.includes("tennis")
      ? "tennis"
      : this.props?.pathName?.includes("baseball")
      ? "baseball"
      : this.props?.pathName?.includes("icehockey")
      ? "icehockey"
      : this.props?.pathName?.includes("boxing")
      ? "boxing"
      : this.props?.pathName?.includes("mma")
      ? "mma"
      : this.props?.pathName?.includes("soccer")
      ? "soccer"
      : "rls";
    try {
      const { status, data } = await axiosInstance.put(
        `${passApi}/label/variation/${this.state?.idToSend}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllLabelVariation();
        this.setActionMessage(
          true,
          "Success",
          `Label variation Edited Successfully`
        );
      } else {
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({ isLoading: false, isInputModalOpen: false });
    }
  };
  deleteItem = async () => {
    try {
      const passApi = this.props?.pathName?.includes("cricket")
        ? `crickets/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("rugbyleague")
        ? `rls/label/variation/${this.state.itemToDelete}?SportId=12`
        : this.props?.pathName?.includes("rugbyunion")
        ? `rls/label/variation/${this.state.itemToDelete}?SportId=13`
        : this.props?.pathName?.includes("basketball")
        ? `nba/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("afl")
        ? `afl/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("australianrules")
        ? `ar/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("golf")
        ? `golf/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("tennis")
        ? `tennis/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("baseball")
        ? `baseball/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("icehockey")
        ? `icehockey/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("boxing")
        ? `boxing/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("mma")
        ? `mma/label/variation/${this.state.itemToDelete}`
        : this.props?.pathName?.includes("soccer")
        ? `soccer/label/variation/${this.state.itemToDelete}`
        : `rls/label/variation/${this.state.itemToDelete}?SportId=14`;

      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllLabelVariation();
        });
        this.setActionMessage(
          true,
          "Success",
          "label variation Deleted Successfully!"
        );
      } else {
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({ itemToDelete: null, isModalOpen: false });
    }
  };

  render() {
    var {
      values,
      messageBox,
      isLoading,
      isEditMode,
      isInputModalOpen,
      isModalOpen,
      variationToSend,
      addInput,
      idToSend,
      LabelVariationData,
    } = this.state;

    let LabelVariationDatas = this.props?.pathName?.includes("cricket")
      ? LabelVariationData?.CricketOddLabelVariations
      : this.props?.pathName?.includes("rugbyleague")
      ? LabelVariationData?.RLOddLabelVariations
      : this.props?.pathName?.includes("rugbyunion")
      ? LabelVariationData?.RLOddLabelVariations
      : this.props?.pathName?.includes("basketball")
      ? LabelVariationData?.NBAOddLabelVariations
      : this.props?.pathName?.includes("afl")
      ? LabelVariationData?.AFLOddLabelVariations
      : this.props?.pathName?.includes("australianrules")
      ? LabelVariationData?.AROddLabelVariations
      : this.props?.pathName?.includes("golf")
      ? LabelVariationData?.GolfOddLabelVariations
      : this.props?.pathName?.includes("tennis")
      ? LabelVariationData?.TennisOddLabelVariations
      : this.props?.pathName?.includes("baseball")
      ? LabelVariationData?.BaseballOddLabelVariations
      : this.props?.pathName?.includes("icehockey")
      ? LabelVariationData?.IceHockeyOddLabelVariations
      : this.props?.pathName?.includes("boxing")
      ? LabelVariationData?.BoxingOddLabelVariations
      : this.props?.pathName?.includes("mma")
      ? LabelVariationData?.MMAOddLabelVariations
      : this.props?.pathName?.includes("soccer")
      ? LabelVariationData?.SoccerOddLabelVariations
      : LabelVariationData?.RLOddLabelVariations;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          {/* <Button
            className="modal-btn admin-btn-green"
            style={{ marginLeft: "auto", marginBottom: "10px" }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button> */}
          <Button
            variant="contained"
            style={{
              backgroundColor: "#4455C7",
              color: "#fff",
              borderRadius: "8px",
              textTransform: "capitalize",
              padding: "13px 24px 12px",
              marginLeft: "auto",
              marginBottom: "10px",
            }}
            onClick={this.inputModal(null, "create", null)}
          >
            Add
          </Button>
          <Grid item xs={12}>
            <Paper className="pageWrapper api-provider">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              {isLoading ? (
                <Box className="message">
                  <Loader />
                </Box>
              ) : LabelVariationDatas?.length > 0 ? (
                <>
                  {" "}
                  <TableContainer>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow className="table-rows listTable-Row">
                          <TableCell>Id</TableCell>
                          <TableCell>Variation</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {LabelVariationDatas?.map((data) => (
                          <TableRow>
                            <TableCell>{data?.id}</TableCell>
                            <TableCell>{data?.variation}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(
                                  data?.id,
                                  "edit",
                                  data?.variation
                                )}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(data?.id)}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              ) : (
                <Box className="message">No Variation Data Available</Box>
              )}
            </Paper>
          </Grid>
        </Grid>
        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={this.toggleInputModal}
        >
          {/* {isLoading && <Loader />} */}
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {!isEditMode ? "Create Label variation" : "Edit Label variation"}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleInputModal}
            />

            <Grid item xs={12} className="runnerInfo">
              <Grid
                item
                xs={12}
                className="runnerInfo-text"
                style={{ display: "flex", flexDirection: "column" }}
              >
                <label className="modal-label">Label Variation</label>
                <TextField
                  className="textfield-tracks"
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder="Label variation"
                  value={isEditMode ? variationToSend : addInput}
                  onChange={(e) => {
                    isEditMode
                      ? this.setState({ variationToSend: e.target.value })
                      : this.setState({ addInput: e.target.value });
                  }}
                />
              </Grid>
            </Grid>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={!addInput}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleUpdate}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={!variationToSend}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
        <ShowModal
          isModalOpen={isModalOpen}
          onClose={this.toggleModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={this.deleteItem}
          onCancel={this.toggleModal}
        />
      </>
    );
  }
}

export default CreateLabelVariation;
