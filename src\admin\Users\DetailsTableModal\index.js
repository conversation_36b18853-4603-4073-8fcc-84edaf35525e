import React, { Component } from "react";
import {
  Grid,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Box,
  Typography,
} from "@mui/material";
import moment from "moment-timezone";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
export default class DetailsTableModal extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    var { userDetail } = this.props;

    const bookMakerValues = userDetail?.usermeta
      ?.filter((item) => item?.key === "bookMaker")
      ?.map((item) => item?.value);
    const sportOrEventValues = userDetail?.usermeta
      ?.filter((item) => item?.key === "sportOrEvent")
      ?.map((item) => item?.value);
    const offeringsValues = userDetail?.usermeta
      ?.filter((item) => item?.key === "offerings")
      ?.map((item) => item?.value);

    const filteredBookMakerValues = bookMakerValues?.filter(
      (value) => value !== "Other - Please specify" || value !== "Other"
    );
    const resultBookMaker = filteredBookMakerValues?.join(", ");
    const filteredSportOrEventValues = sportOrEventValues?.filter(
      (value) => value !== "Other - Please specify" || value !== "Other"
    );
    const resultSportOrEvent = filteredSportOrEventValues?.join(", ");

    const filteredOfferingsValues = offeringsValues?.filter(
      (value) => value !== "Other - Please specify" || value !== "Other"
    );
    const resultOfferings = filteredOfferingsValues?.join(", ");

    //   const updatedHearAbout = userDetail?.hearedAbout?.map(item => {
    //     if (item?.includes("other")) {
    //       return item?.split(":")[1];
    //     }
    //     return item;
    //   });
    //   return updatedHearAbout?.toString():
    // }
    const fetchNotificationData = () => {
      const SelectedData =
        userDetail?.NotificationPreference?.sportsValue?.filter((ele) => {
          if (ele?.SportId === 1) {
            return (
              ele?.tips ||
              ele?.news ||
              ele?.fixtures ||
              ele?.dailyBestBet ||
              ele?.smartBNewsLetter ||
              ele?.weeklyNewsLetter
            );
          } else {
            return ele?.tips || ele?.news || ele?.fixtures;
          }
        });

      const finalData = SelectedData?.filter(
        (e) => e?.SportId !== 2 && e?.SportId !== 3
      )?.sort((a, b) => a?.Sport?.sportName.localeCompare(b?.Sport?.sportName));
      return (
        <>
          <Box className="flex mt-10" style={{ alignItems: "baseline" }}>
            <Typography className="text-upper-user" variant="h6">
              Notification For:
            </Typography>

            <Box>
              {finalData?.map((item, index) => {
                let newData = [];
                const offeringTips =
                  item?.tips &&
                  newData?.push(
                    item?.SportId === 1 ? "Tips of the Day" : "Tips"
                  );
                const offeringNews = item?.news && newData?.push("News");
                const offeringFixture =
                  item?.fixtures && newData?.push("Fixtures & Results");
                const offeringBestBet =
                  item?.dailyBestBet && newData.push("Daily Best Bet");
                const weeklyNewsLetter =
                  item?.weeklyNewsLetter && newData.push("Weekly Newsletter");
                const smartbNewsLetter =
                  item?.smartBNewsLetter && newData.push("SmartB Newsletter");
                return (
                  <Typography className="ml-10 font-size125">
                    <span style={{ fontWeight: "600" }}>
                      {item?.SportId === 1 ? "Racing" : item?.Sport?.sportName}:{" "}
                    </span>
                    {/* {item?.tips && "Tips"}
                    {item?.news && "News"}
                    {item?.fixtures && "Fixtures & Results"} */}
                    {newData?.toString()}
                  </Typography>
                );
              })}
            </Box>
          </Box>
        </>
      );
    };

    const fetchHearAboutData = () => {
      const updatedHearAbout = userDetail?.hearedAbout?.map((item) => {
        if (item?.includes("other")) {
          if (item?.split(":")[1]) {
            return item?.split(":")[1];
          } else {
            return "Other";
          }
        }
        return item;
      });
      return updatedHearAbout?.toString();
    };
    const fetchGender = (gender) => {
      if (gender == "m") {
        return "Male";
      } else if (gender == "f") {
        return "Female";
      } else if (gender == "nb") {
        return "Non-binary";
      } else if (gender == "pnts") {
        return "Prefer not to say";
      } else {
        return null;
      }
    };
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} style={{ marginTop: "10px" }}>
            <Paper className="pageWrapper api-provider table-height modalTable">
              {userDetail?.dob ||
              userDetail?.phone ||
              userDetail?.residentalAddress ||
              userDetail?.usermeta?.length > 0 ? (
                <Box className="modal-padding">
                  {" "}
                  <Box className="flex">
                    <Typography className="text-upper-user" variant="h6">
                      DOB:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {userDetail?.dob && userDetail?.dob
                        ? moment(userDetail?.dob).format("DD-MM-YYYY")
                        : ""}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      Phone No:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {userDetail?.Country?.phoneCode
                        ? `+${userDetail?.Country?.phoneCode} `
                        : ""}{" "}
                      {userDetail?.phone && userDetail?.phone}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      Sign Up Date:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {moment(userDetail?.createdAt)
                        .tz(timezone)
                        .format("DD-MM-YYYY")}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      Username:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {userDetail?.nickName}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      Gender:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {fetchGender(userDetail?.gender)}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      BookMaker Account:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {userDetail?.bookMakerAccount === true ? "True" : "False"}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      State:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {userDetail?.address?.State?.state &&
                        userDetail?.address?.State?.state}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      Country:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {userDetail?.address?.Country?.country &&
                        userDetail?.address?.Country?.country}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      BookMaker:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {resultBookMaker}
                    </Typography>
                  </Box>
                  <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      Sport or Event:
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {resultSportOrEvent}
                    </Typography>
                  </Box>
                  {fetchNotificationData()}
                  {/* <Box className="flex mt-10">
                    <Typography className="text-upper-user" variant="h6">
                      How did you hear about us :
                    </Typography>
                    <Typography className="ml-10 font-size125">
                      {fetchHearAboutData()}
                    </Typography>
                  </Box> */}
                  {/* <Box>
                    <Typography>Subscription Details</Typography>
                  </Box> */}
                  {userDetail?.SubscriptionPurchased !== null ? (
                    <>
                      <Box className="flex mt-10">
                        <Typography className="text-upper-user" variant="h6">
                          Subscription Plan:
                        </Typography>
                        <Typography className="ml-10 font-size125">
                          {userDetail?.SubscriptionPurchased?.SubscriptionPlan
                            ?.name &&
                            userDetail?.SubscriptionPurchased?.SubscriptionPlan
                              ?.name}
                        </Typography>
                      </Box>
                      <Box className="flex mt-10">
                        <Typography className="text-upper-user" variant="h6">
                          Duration:
                        </Typography>
                        <Typography className="ml-10 font-size125">
                          {userDetail?.SubscriptionPurchased?.SubscriptionPlan
                            ?.duration &&
                            userDetail?.SubscriptionPurchased?.SubscriptionPlan
                              ?.duration}
                        </Typography>
                      </Box>
                      <Box className="flex mt-10">
                        <Typography className="text-upper-user" variant="h6">
                          Amount:
                        </Typography>
                        <Typography className="ml-10 font-size125">
                          {userDetail?.SubscriptionPurchased &&
                            userDetail?.SubscriptionPurchased?.SubscriptionPlan
                              ?.currency}{" "}
                          $
                          {
                            userDetail?.SubscriptionPurchased?.SubscriptionPlan
                              ?.amount
                          }
                        </Typography>
                      </Box>
                      <Box className="flex mt-10">
                        <Typography className="text-upper-user" variant="h6">
                          Expires On:
                        </Typography>
                        <Typography className="ml-10 font-size125">
                          {userDetail?.SubscriptionPurchased?.expireAt &&
                            moment(
                              userDetail?.SubscriptionPurchased?.expireAt
                            ).format(`DD-MM-YYYY`)}
                        </Typography>
                      </Box>
                      <Box className="flex mt-10">
                        <Typography className="text-upper-user" variant="h6">
                          Purchase Platform:
                        </Typography>
                        <Typography className="ml-10 font-size125">
                          {userDetail?.SubscriptionPurchased?.plateform &&
                            userDetail?.SubscriptionPurchased?.plateform}
                        </Typography>
                      </Box>
                    </>
                  ) : (
                    <></>
                  )}
                </Box>
              ) : (
                <Box className="message tablePadding">No Details Available</Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </>
    );
  }
}
