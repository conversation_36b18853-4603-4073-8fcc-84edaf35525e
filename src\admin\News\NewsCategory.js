import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../src/images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import { config } from "../../helpers/config";
import FileUploader from "../../library/common/components/FileUploader";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { ActionMessage, Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select from "react-select";
import _ from "lodash";
import DeleteIcon from "@mui/icons-material/Delete";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../TeamSport/teamsport.scss";

class NewsCategory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      categoryValues: {
        categoryTitle: "",
        categoryInitialTitle: "",
        newsHomeRelation: "No",
        id: "",
      },
      categorylist: [],
      categoryCount: 0,
      errorTitle: "",
      errorInitialTitle: "",
      errorHomeRelation: "",
      errorParentSport: "",
      search: "",
      parentSport: null,
      sportsOption: [],
      isSportLoading: false,
      defaultImage: [],
      defaultUploadImage: "",
    };
  }

  componentDidMount() {
    this.fetchAllCategory(0, "");
    this.fetchAllSports();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllCategory(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllCategory(0, "");
      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
      });
    }
  }

  async fetchAllCategory(page, searchvalue) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      let passApi = `v2/news/admin/category?isAll=1&limit=${rowPerPage}&offset=${page}&search=${searchvalue}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          categorylist: data?.result?.result,
          isLoading: false,
          categoryCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }
  handalValidate = () => {
    let { categoryValues } = this.state;

    let flag = true;
    if (categoryValues?.categoryTitle?.trim() === "") {
      flag = false;
      this.setState({
        errorTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTitle: "",
      });
    }
    if (categoryValues?.categoryInitialTitle?.trim() === "") {
      flag = false;
      this.setState({
        errorInitialTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorInitialTitle: "",
      });
    }
    if (categoryValues?.newsHomeRelation === "") {
      flag = false;
      this.setState({
        errorHomeRelation: "This field is mandatory",
      });
    } else {
      this.setState({
        errorHomeRelation: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    const { defaultImage } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        title: this.state?.categoryValues?.categoryTitle,
        initialTitle: this.state?.categoryValues?.categoryInitialTitle,
        newshomerelation:
          this.state?.categoryValues?.newsHomeRelation === "Yes" ? true : false,
        SportId: this.state?.parentSport === 0 ? null : this.state?.parentSport,
      };
      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            image: config.mediaUrl + fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: config.mediaUrl + fileData?.image?.filePath,
          });
        }
      }
      try {
        const { status } = await axiosInstance.post(
          `v2/news/admin/category`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.fetchAllCategory(this.state.offset, this?.state?.search);
          this.setActionMessage(
            true,
            "Success",
            `News Category Created Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
          });
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          defaultImage: [],
          defaultUploadImage: "",
        });
      }
    }
  };

  handleUpdate = async () => {
    const { defaultImage } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        title: this.state?.categoryValues?.categoryTitle,
        initialTitle: this.state?.categoryValues?.categoryInitialTitle,
        newshomerelation:
          this.state?.categoryValues?.newsHomeRelation === "Yes" ? true : false,
        SportId: this.state?.parentSport === 0 ? null : this.state?.parentSport,
      };
      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            image: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: config.mediaUrl + fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          image:
            this.state.defaultUploadImage &&
            this.state.defaultUploadImage !== ""
              ? this.state.defaultUploadImage
              : null,
        };
      }
      try {
        const { status } = await axiosInstance.put(
          `v2/news/admin/category/${this.state.categoryValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.fetchAllCategory(this.state.offset, this?.state?.search);
          this.setActionMessage(
            true,
            "Success",
            `News Category Updated Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
          });
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          defaultImage: [],
          defaultUploadImage: "",
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTitle: "",
      errorInitialTitle: "",
      errorHomeRelation: "",
      parentSport: null,
      errorParentSport: "",
      defaultImage: [],
      defaultUploadImage: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchSelectedParentSport(item, item?.SportId);
      this.setState({
        categoryValues: {
          categoryTitle: item?.title,
          categoryInitialTitle: item?.initialTitle,
          id: item?.id,
          newsHomeRelation: item?.NewsHomeArticles?.length > 0 ? "Yes" : "No",
        },
        parentSport: item?.SportId,
        isEditMode: true,
        defaultUploadImage: item?.image,
      });
    } else {
      this.setState({
        categoryValues: {
          categoryTitle: "",
          categoryInitialTitle: "",
          id: "",
          newsHomeRelation: "",
        },
        parentSport: null,
        isEditMode: false,
        defaultUploadImage: "",
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const passApi = `v2/news/admin/category/${this.state.itemToDelete}`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllCategory(this.state.offset, this?.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          "News Category Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, categorylist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  fetchSelectedParentSport = (item, SportId) => {
    let seletedParentSport = [
      {
        label: item?.sportName,
        value: SportId,
      },
    ];
    this.setState({
      parentPlayerData: SportId
        ? _.uniqBy(
            [...seletedParentSport, ...this.state.sportsOption],
            function (e) {
              return e.value;
            }
          )
        : this.state.sportsOption,
      parentSport: SportId,
    });
  };
  fetchAllSports = async () => {
    this.setState({ isSportLoading: true });
    try {
      const { status, data } = await axiosInstance.get(URLS.sports);
      if (status === 200) {
        let newdata = [];
        let sportData = data.result?.map((item) => {
          newdata.push({
            label: item?.sportName,
            value: item?.id,
          });
        });

        // let pushData = newdata.push(alldatas);
        let sortData = newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        });
        let alldatas = sortData?.unshift({
          label: "None",
          value: 0,
        });
        let finalData = _.uniqBy(sortData, function (e) {
          return e.value;
        });
        this.setState({
          sportsOption: finalData,
          isSportLoading: false,
        });
      } else {
        this.setState({ isSportLoading: false });
      }
    } catch (err) {
      this.setState({ isSportLoading: false });
    }
  };
  fetchSportName = (SportId) => {
    const { sportsOption } = this.state;
    const filteredData = sportsOption?.filter(
      (item) => item?.value === SportId
    );
    return filteredData?.[0]?.label;
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };
  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllCategory(0, search);
      this.setState({ currentPage: 1 });
    }
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      categoryValues,
      categorylist,
      categoryCount,
      errorTitle,
      errorInitialTitle,
      errorHomeRelation,
      search,
      errorParentSport,
      parentSport,
      sportsOption,
      isSportLoading,
      defaultImage,
      defaultUploadImage,
    } = this.state;
    const pageNumbers = [];
    if (categoryCount > 0) {
      for (let i = 1; i <= Math.ceil(categoryCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  News
                </Link>
                <Typography className="active_p">News Category</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  News Category
                </Typography>
              </Grid>

              <Grid item xs={8} className="admin-filter-wrap">
                {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "60%" }}
              >
                <option value="">Select Sport Type</option>
                {allSportsType?.length > 0 &&
                  allSportsType?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.sportType}
                    </option>
                  ))}
              </SelectBox> */}
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllCategory(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && categorylist?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && categorylist?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell>DID</TableCell>
                      <TableCell style={{ width: "25%" }}> Title </TableCell>
                      <TableCell style={{ width: "25%" }}>
                        Initial Title
                      </TableCell>
                      <TableCell>Sports</TableCell>
                      <TableCell>Default Image</TableCell>
                      <TableCell>Home Relation</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {categorylist?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.title}</TableCell>
                          <TableCell>{item?.initialTitle}</TableCell>
                          <TableCell>
                            {this.fetchSportName(item?.SportId)}
                          </TableCell>
                          <TableCell
                            style={{ width: "90px", textAlign: "center" }}
                          >
                            {item?.image && item?.image !== "" ? (
                              <img
                                className="auto-width"
                                src={config.mediaUrl + item?.image}
                                alt="featureImage"
                              />
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            {item?.NewsHomeArticles?.length > 0 ? "Yes" : "No"}
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                            className={
                              categorylist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              categorylist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              categoryCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                            className={
                              categorylist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              categorylist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New News Category"
                    : "Edit News Category"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Title </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Category Title"
                          value={categoryValues?.categoryTitle}
                          onChange={(e) =>
                            this.setState({
                              categoryValues: {
                                ...categoryValues,
                                categoryTitle: e.target.value,
                              },
                            })
                          }
                        />
                        {errorTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Initial Title</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Category Initial Title"
                          value={categoryValues?.categoryInitialTitle}
                          onChange={(e) =>
                            this.setState({
                              categoryValues: {
                                ...categoryValues,
                                categoryInitialTitle: e.target.value,
                              },
                            })
                          }
                        />
                        {errorInitialTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorInitialTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Home Relation </label>
                          <RadioGroup
                            aria-label="homeRealation"
                            name="homeRealation"
                            className="gender"
                            value={categoryValues?.newsHomeRelation}
                            onChange={(e) =>
                              this.setState({
                                categoryValues: {
                                  ...categoryValues,
                                  newsHomeRelation: e.target.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="Yes"
                              control={
                                <Radio
                                  color="primary"
                                  checked={
                                    categoryValues?.newsHomeRelation === "Yes"
                                  }
                                />
                              }
                              label="Yes"
                            />
                            <FormControlLabel
                              value="No"
                              control={
                                <Radio
                                  color="primary"
                                  checked={
                                    categoryValues?.newsHomeRelation === "No"
                                  }
                                />
                              }
                              label="No"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorHomeRelation ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorHomeRelation}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Sport</label>
                        <Select
                          className="React cricket-select sponsored-select-modal"
                          classNamePrefix="select"
                          placeholder="Sport"
                          menuPosition="fixed"
                          value={sportsOption?.find((item) => {
                            return item?.value == parentSport;
                          })}
                          isLoading={isSportLoading}
                          onChange={(e) => {
                            this.setState({
                              parentSport: e.value,
                            });
                          }}
                          options={sportsOption}
                          // isOptionDisabled={() =>
                          //   sponsoredValues?.selectedprovider?.length >= 2
                          // }
                        />
                        {errorParentSport ? (
                          <p
                            className="errorText"
                            style={{ margin: "0 0 0 0" }}
                          >
                            {errorParentSport}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <div
                        className="blog-file-upload"
                        style={{ width: "97%", marginTop: "10px" }}
                      >
                        <label className="modal-label"> Default Image </label>
                        <FileUploader
                          onDrop={(image) =>
                            this.handleFileUpload("defaultImage", image)
                          }
                          style={{ marginTop: "5px" }}
                        />
                        <Box
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <div className="logocontainer">
                            {defaultImage?.length > 0
                              ? defaultImage?.map((file, index) => (
                                  <img
                                    className="auto-width"
                                    key={index}
                                    src={file.preview}
                                    alt="player"
                                  />
                                ))
                              : defaultUploadImage &&
                                defaultUploadImage !== "" && (
                                  <img
                                    className="auto-width"
                                    src={
                                      defaultUploadImage?.includes("uploads")
                                        ? config.mediaUrl + defaultUploadImage
                                        : defaultUploadImage
                                    }
                                    alt="player"
                                  />
                                )}
                          </div>
                          {(defaultImage?.length > 0 ||
                            (defaultUploadImage &&
                              defaultUploadImage !== "")) && (
                            <Box className="delete-icon-wrap">
                              <DeleteIcon
                                className="delete-icon"
                                onClick={() => this.handleFeatureLogoRemove()}
                                style={{ cursor: "pointer" }}
                              />
                            </Box>
                          )}
                        </Box>
                      </div>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default NewsCategory;
