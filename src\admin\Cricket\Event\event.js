import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
// import {
//   MuiPickersUtilsProvider,
//   KeyboardDatePicker,
//   KeyboardDateTimePicker,
// } from "@material-ui/pickers";
import DateFnsUtils from "@date-io/date-fns";
import moment from "moment-timezone";

import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../cricket.scss";

class Event extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      eventValues: {
        eventName: "",
        awayTeamId: "",
        homeTeamId: "",
        startTime: "",
        endTime: "",
        rapidEventId: "",
        id: "",
        CricketTournamentId: "",
      },
      EventList: [],
      EventCount: 0,
      TournamentData: [],
      TournamentCount: 0,
      TournamentPage: 0,
      errorName: "",
      errorAwayTeamId: "",
      errorHomeTeamId: "",
      errorStartTime: "",
      errorEndTime: "",
      errorTournament: "",
      externalTeamData: [],
      externalTeamCount: 0,
      selectTeam: "",
      SelectedExternalTeamList: [],
      ExternalTeamPage: 0,
      filterDate: null,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      searchTeam: [],
      searchTeamCount: 0,
      searchTeamPage: 0,
      isTeamSearch: "",
      searchTournament: [],
      searchTournamentCount: 0,
      searchTournamentPage: 0,
      isTournamentSearch: "",
    };
  }

  componentDidMount() {
    this.fetchAllEvent(0, this.state?.selectTeam, this.state?.filterDate);
    this.fetchAllTeam(this.state.ExternalTeamPage);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllEvent(
        this.state.offset,
        this.state?.selectTeam,
        this.state?.filterDate
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEvent(0, this.state?.selectTeam, this.state?.filterDate);
      this.fetchAllTeam(0);
      this.setState({
        offset: 0,
        currentPage: 1,
        ExternalTeamPage: 0,
        externalTeamData: [],
        selectTeam: "",
      });
    }
  }

  fetchAllEvent = async (page, teamId, date) => {
    let { rowPerPage, offset, timezone } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `crickets/event?limit=${rowPerPage}&offset=${page}&teamId=${
          teamId === 0 ? "" : teamId
        }&startDate=${
          date === null ? moment()?.format("YYYY-MM-DD") : date
        }&endDate=${date === null ? "" : date}&timezone=${timezone}`
      );
      if (status === 200) {
        this.setState({
          EventList: data?.result?.rows,
          isLoading: false,
          EventCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  handalValidate = () => {
    let { eventValues } = this.state;
    let flag = true;
    if (eventValues?.eventName === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (eventValues?.awayTeamId === "") {
      flag = false;
      this.setState({
        errorAwayTeamId: "This field is mandatory",
      });
    } else {
      this.setState({
        errorAwayTeamId: "",
      });
    }
    if (eventValues?.homeTeamId === "") {
      flag = false;
      this.setState({
        errorHomeTeamId: "This field is mandatory",
      });
    } else {
      this.setState({
        errorHomeTeamId: "",
      });
    }
    if (eventValues?.startTime === "") {
      flag = false;
      this.setState({
        errorStartTime: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartTime: "",
      });
    }
    if (eventValues?.endTime === "") {
      flag = false;
      this.setState({
        errorEndTime: "This field is mandatory",
      });
    } else {
      this.setState({
        errorEndTime: "",
      });
    }
    if (
      eventValues?.CricketTournamentId === "" ||
      eventValues?.CricketTournamentId === null
    ) {
      flag = false;
      this.setState({
        errorTournament: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTournament: "",
      });
    }

    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      const { eventValues } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        eventName: eventValues?.eventName,
        awayTeamId: eventValues?.awayTeamId,
        homeTeamId: eventValues?.homeTeamId,
        startTime: eventValues?.startTime,
        endTime: eventValues?.endTime,
        rapidEventId: eventValues?.rapidEventId,
        SportId: 4,
        CricketTournamentId: eventValues?.CricketTournamentId,
      };
      try {
        const { status } = await axiosInstance.post(`crickets/event`, payload);
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEvent(
            this.state.offset,
            this.state?.selectTeam,
            this.state?.filterDate
          );
          //   this.setActionMessage(
          //     true,
          //     "Success",
          //     `Country variation Created Successfully`
          //   );
        } else {
          this.setState({
            isLoading: false,
          });
        }
      } catch {
        this.setState({
          isLoading: false,
        });
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      const { eventValues } = this.state;
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        eventName: eventValues?.eventName,
        awayTeamId: eventValues?.awayTeamId,
        homeTeamId: eventValues?.homeTeamId,
        startTime: eventValues?.startTime,
        endTime: eventValues?.endTime,
        rapidEventId: eventValues?.rapidEventId,
        SportId: 4,
        CricketTournamentId: eventValues?.CricketTournamentId,
      };
      try {
        const { status } = await axiosInstance.put(
          `crickets/event/${this.state.eventValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllEvent(
            this.state.offset,
            this.state?.selectTeam,
            this.state?.filterDate
          );
          //   this.setActionMessage(
          //     true,
          //     "Success",
          //     `Country variation Edited Successfully`
          //   );
        } else {
          this.setState({
            isLoading: false,
          });
        }
      } catch {
        this.setState({
          isLoading: false,
        });
      }
    }
  };
  async fetchAllTournament(TournamentPage) {
    const { status, data } = await axiosInstance.get(
      `crickets/tournament?limit=20&offset=${TournamentPage}`
    );
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let tournament = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.TournamentData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      finalData = finalData?.filter((item) => item?.value !== 0);
      this.setState({
        TournamentData: finalData,
        TournamentCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomTournament = (e, type) => {
    let {
      TournamentCount,
      TournamentPage,
      isTournamentSearch,
      searchTournamentCount,
      searchTournamentPage,
    } = this.state;
    if (
      isTournamentSearch !== "" &&
      searchTournamentCount !== Math.ceil(searchTournamentPage / 20 + 1)
    ) {
      this.handleTournamentInputChange(
        searchTournamentPage + 20,
        isTournamentSearch
      );
      this.setState({
        searchTournamentPage: searchTournamentPage + 20,
      });
    } else {
      if (
        TournamentCount !==
          (TournamentCount == 1 ? 1 : Math.ceil(TournamentPage / 20)) &&
        isTournamentSearch == ""
      ) {
        this.fetchAllTournament(TournamentPage + 20);
        this.setState({
          TournamentPage: TournamentPage + 20,
        });
      }
    }
  };
  handleTournamentInputChange = (TournamentPage, value) => {
    const passApi = `crickets/tournament?limit=20&offset=${TournamentPage}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchTournament, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTournament: finalData,
          searchTournamentCount: Math.ceil(count),
          isTournamentSearch: value,
        });
      }
    });
  };
  fetchSelectedTournament = async (id, TournamentId) => {
    const { status, data } = await axiosInstance.get(`crickets/event/${id}`);
    if (status === 200) {
      let seletedTournament = [
        {
          label: data?.result?.CricketTournament?.name,
          value: TournamentId,
        },
      ];

      this.setState({
        TournamentData: TournamentId
          ? _.uniqBy(
              [...seletedTournament, ...this.state.TournamentData],
              function (e) {
                return e.value;
              }
            )
          : this.state.TournamentData,
      });
    }
  };

  fetchAllTeam = async (ExternalTeamPage) => {
    const passApi = `crickets/team?limit=20&offset=${ExternalTeamPage}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterdata = newdata?.filter((item) => item?.value !== 0);
      let mergeData = _.unionBy(this.state?.externalTeamData, filterdata);
      const sortedData = mergeData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let alldatas = sortedData?.unshift({
        label: "All Teams",
        value: 0,
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        externalTeamData: finalData,
        externalTeamCount: Math.ceil(count),
      });
    }
  };
  handleOnScrollBottomExternalTeam = (e, type) => {
    let {
      externalTeamCount,
      ExternalTeamPage,
      isTeamSearch,
      searchTeamCount,
      searchTeamPage,
    } = this.state;
    if (
      isTeamSearch !== "" &&
      searchTeamCount !== Math.ceil(searchTeamPage / 20 + 1)
    ) {
      this.handleTeamInputChange(searchTeamPage + 20, isTeamSearch);
      this.setState({
        searchTeamPage: searchTeamPage + 20,
      });
    } else {
      if (
        externalTeamCount !== Math.ceil(ExternalTeamPage / 20) &&
        isTeamSearch == ""
      ) {
        this.fetchAllTeam(ExternalTeamPage + 20);
        this.setState({
          ExternalTeamPage: ExternalTeamPage + 20,
        });
      }
    }
  };
  handleTeamInputChange = (ExternalTeamPage, value) => {
    const passApi = `crickets/team?limit=20&offset=${ExternalTeamPage}&search=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchTeam, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Teams",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTeam: finalData,
          searchTeamCount: Math.ceil(count),
          isTeamSearch: value,
        });
      }
    });
  };
  handleExternalTeamChange = (e) => {
    this.setState({
      selectTeam: e.value,
    });
    this.fetchAllEvent(this.state.offset, e?.value, this.state?.filterDate);
  };
  handleFilterDateChange = (e) => {
    this.setState({
      filterDate: moment(e)?.format("YYYY-MM-DD"),
    });
    this.fetchAllEvent(
      this.state.offset,
      this.state.selectTeam,
      moment(e)?.format("YYYY-MM-DD")
    );
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorAwayTeamId: "",
      errorHomeTeamId: "",
      errorStartTime: "",
      errorEndTime: "",
      errorTournament: "",
      TournamentPage: 0,
    });
  };

  inputModal = (item, type) => () => {
    this.fetchAllTournament(0);

    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchSelectedTournament(item?.id, item?.CricketTournamentId);
      this.setState({
        eventValues: {
          eventName: item?.eventName,
          awayTeamId: item?.awayTeamId,
          homeTeamId: item?.homeTeamId,
          startTime: item?.startTime,
          endTime: item?.endTime,
          rapidEventId: item?.rapidEventId,
          id: item?.id,
          CricketTournamentId: item?.CricketTournamentId,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        eventValues: {
          eventName: "",
          awayTeamId: "",
          homeTeamId: "",
          startTime: new Date(),
          endTime: new Date(),
          rapidEventId: "",
          id: "",
          CricketTournamentId: "",
        },
        isEditMode: false,
      });
    }
  };

  // setActionMessage = (display = false, type = "", message = "") => {
  //   this.setState({ messageBox: { display, type, message } }, () =>
  //     setTimeout(
  //       () =>
  //         this.setState({
  //           messageBox: { display: false, type: "", message: "" },
  //         }),
  //       3000
  //     )
  //   );
  // };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `crickets/event/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvent(
            this.state.offset,
            this.state?.selectTeam,
            this.state?.filterDate
          );
        });
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      SportVariationModal,
      eventValues,
      TournamentData,
      TournamentCount,
      TournamentPage,
      EventList,
      EventCount,
      errorName,
      errorAwayTeamId,
      errorHomeTeamId,
      errorStartTime,
      errorEndTime,
      errorTournament,
      externalTeamData,
      externalTeamCount,
      selectTeam,
      SelectedExternalTeamList,
      ExternalTeamPage,
      filterDate,
      searchTeam,
      isTeamSearch,
      searchTournament,
      isTournamentSearch,
    } = this.state;
    const pageNumbers = [];

    // sportType !== "" &&
    //   (EventList = EventList.filter((obj) => obj.sportTypeId == sportType));

    // let currentPageRow = EventList;

    // if (EventList?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = EventList.slice(indexOfFirstTodo, indexOfLastTodo);

    if (EventCount > 0) {
      for (let i = 1; i <= Math.ceil(EventCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {/* {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )} */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Cricket
                </Link>
                <Typography className="active_p">Event</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Event
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                  onChange={(e) => this.setState({ sportType: e.target.value })}
                  style={{ width: "20%", marginTop: "0px" }}
                >
                  <option value="">Select Category</option>
                  {CategoryData?.length > 0 &&
                    CategoryData?.map((obj, i) => (
                      <option key={i} value={obj?.id}>
                        {obj?.categoryName}
                      </option>
                    ))}
                </SelectBox> */}
                <Box className="date-time-picker-wrap filter-date-picker">
                  {/* <MuiPickersUtilsProvider utils={DateFnsUtils}>
                    <KeyboardDatePicker
                      variant="inline"
                      inputVariant="outlined"
                      ampm={false}
                      value={filterDate}
                      onChange={(e) => this.handleFilterDateChange(e)}
                      autoOk={true}
                      disableToolbar
                      format="yyyy/MM/dd"
                      placeholder="All"
                      className="date-time-picker"
                    />
                  </MuiPickersUtilsProvider> */}
                </Box>
                <Select
                  className="React teamsport-select external-select"
                  classNamePrefix="select"
                  placeholder="Select Team"
                  onMenuScrollToBottom={(e) =>
                    this.handleOnScrollBottomExternalTeam(e)
                  }
                  onInputChange={(e) => this.handleTeamInputChange(0, e)}
                  value={
                    isTeamSearch
                      ? searchTeam?.find((item) => {
                          return item?.value == selectTeam;
                        })
                      : externalTeamData?.find((item) => {
                          return item?.value == selectTeam;
                        })
                  }
                  onChange={(e) => this.handleExternalTeamChange(e)}
                  menuPosition="absolute"
                  options={isTeamSearch ? searchTeam : externalTeamData}
                />
                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  // value={search}
                  onChange={(e) => {
                    // setSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  // onClick={() => fetchAnimal()}
                >
                  Search
                </Button> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && EventList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && EventList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>rapidEventId</TableCell>
                        <TableCell>Event Name</TableCell>
                        {/* <TableCell style={{ width: "25%" }}>
                          variation
                        </TableCell> */}
                        {/* <TableCell style={{ width: "25%" }}>variation</TableCell> */}
                        <TableCell>AwayTeamId</TableCell>
                        <TableCell>HomeTeamId</TableCell>
                        <TableCell>Start Time</TableCell>
                        <TableCell>End Time</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {EventList?.map((item) => {
                        return (
                          <TableRow className="table-rows listTable-Row">
                            <TableCell> {item?.id} </TableCell>
                            <TableCell>{item?.rapidEventId}</TableCell>
                            <TableCell>{item?.eventName}</TableCell>
                            <TableCell>{item?.awayTeamId}</TableCell>
                            <TableCell>{item?.homeTeamId}</TableCell>
                            <TableCell
                              style={{
                                textAlign: !item?.startTime ? "center" : "",
                              }}
                            >
                              {item?.startTime
                                ? moment(item?.startTime).format(
                                    "DD/MM/YYYY hh:mm:ss a"
                                  )
                                : "-"}
                            </TableCell>
                            <TableCell
                              style={{
                                textAlign: !item?.endTime ? "center" : "",
                              }}
                            >
                              {item?.endTime
                                ? moment(item?.endTime).format(
                                    "DD/MM/YYYY hh:mm:ss a"
                                  )
                                : "-"}
                            </TableCell>
                            <TableCell
                              style={{ display: "flex", borderBottom: "none" }}
                            >
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                className="table-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                EventCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Event" : "Edit Event"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="cricket-text"
                      >
                        <label className="modal-label"> Event Name</label>
                        <TextField
                          className="cricket-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Event Name"
                          value={eventValues?.eventName}
                          onChange={(e) =>
                            this.setState({
                              eventValues: {
                                ...eventValues,
                                eventName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="cricket-text"
                      >
                        <label className="modal-label">rapid Event Id</label>
                        <TextField
                          className="cricket-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Event Id"
                          value={eventValues?.rapidEventId}
                          onChange={(e) =>
                            this.setState({
                              eventValues: {
                                ...eventValues,
                                rapidEventId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <label className="modal-label"> Tournament </label>
                        <Select
                          className="React cricket-select cricket-multiple-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Tournament"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomTournament(e)
                          }
                          onInputChange={(e) =>
                            this.handleTournamentInputChange(0, e)
                          }
                          value={
                            isTournamentSearch
                              ? searchTournament?.find((item) => {
                                  return (
                                    item?.value ==
                                    eventValues?.CricketTournamentId
                                  );
                                })
                              : TournamentData?.find((item) => {
                                  return (
                                    item?.value ==
                                    eventValues?.CricketTournamentId
                                  );
                                })
                          }
                          options={
                            isTournamentSearch
                              ? searchTournament
                              : TournamentData
                          }
                          onChange={(e) =>
                            this.setState({
                              eventValues: {
                                ...eventValues,
                                CricketTournamentId: e?.value,
                              },
                            })
                          }
                        />
                        {errorTournament ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorTournament}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="cricket-text">
                        <label className="modal-label"> AwayTeam Id </label>
                        <TextField
                          className="cricket-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="AwayTeam Id"
                          value={eventValues?.awayTeamId}
                          onChange={(e) =>
                            this.setState({
                              eventValues: {
                                ...eventValues,
                                awayTeamId: e.target.value,
                              },
                            })
                          }
                        />
                        {errorAwayTeamId ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorAwayTeamId}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="cricket-text">
                        <label className="modal-label"> HomeTeam ID</label>
                        <TextField
                          className="cricket-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="HomeTeam Id"
                          value={eventValues?.homeTeamId}
                          onChange={(e) =>
                            this.setState({
                              eventValues: {
                                ...eventValues,
                                homeTeamId: e.target.value,
                              },
                            })
                          }
                        />
                        {errorHomeTeamId ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorHomeTeamId}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="date-time-picker-wrap">
                        <label className="modal-label"> Start Time </label>
                        {/* <MuiPickersUtilsProvider utils={DateFnsUtils}>
                          <KeyboardDateTimePicker
                            variant="inline"
                            inputVariant="outlined"
                            ampm={false}
                            value={eventValues?.startTime}
                            onChange={(e) =>
                              this.setState({
                                eventValues: {
                                  ...eventValues,
                                  startTime: e,
                                },
                              })
                            }
                            autoOk={true}
                            format="yyyy/MM/dd HH:mm"
                            className="date-time-picker"
                          />
                        </MuiPickersUtilsProvider> */}
                        {errorStartTime ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorStartTime}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="date-time-picker-wrap">
                        <label className="modal-label"> End Time </label>
                        {/* <MuiPickersUtilsProvider utils={DateFnsUtils}>
                          <MuiPickersUtilsProvider utils={DateFnsUtils}>
                            <KeyboardDateTimePicker
                              variant="inline"
                              inputVariant="outlined"
                              ampm={false}
                              value={eventValues?.endTime}
                              onChange={(e) =>
                                this.setState({
                                  eventValues: {
                                    ...eventValues,
                                    endTime: e,
                                  },
                                })
                              }
                              autoOk={true}
                              format="yyyy/MM/dd HH:mm"
                              className="date-time-picker"
                            />
                          </MuiPickersUtilsProvider>
                        </MuiPickersUtilsProvider> */}
                        {errorEndTime ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorEndTime}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Event;
