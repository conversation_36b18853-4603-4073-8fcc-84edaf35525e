import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
} from "@mui/material";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select, { components } from "react-select";
import "./rules.scss";
import axiosInstance from "../../../helpers/Axios";

const CricketPositionOption = [
  {
    label: "All Rounder",
    value: "allRounder",
  },
  {
    label: "Batsman",
    value: "batsman",
  },
  {
    label: "Bowler",
    value: "bowler",
  },
  {
    label: "WicketKeeper",
    value: "wicketKeeper",
  },
  {
    label:"Reserve",
    value:"reserve"
  }
];

const RLPositionOption = [
  {
    label: "Backs (BAC)",
    value: "BAC",
  },
  {
    label: "Halves (HAL)",
    value: "HAL",
  },
  {
    label: "Back Rowers (BR)",
    value: "BR",
  },
  {
    label: "Front Row Fowards (FRF)",
    value: "FRF",
  },
  {
    label: "Interchange (IC)",
    value: "IC",
  },
  {
    label:"Reserve",
    value:"reserve"
  }
];

const ARPositionOption = [
  {
    label: "Back Line (BL)",
    value: "BL",
  },
  {
    label: "Half Back Line (HBL)",
    value: "HBL",
  },
  {
    label: "Midfield (MID)",
    value: "MID",
  },
  {
    label: "Half Forward Line (HFL)",
    value: "HFL",
  },
  {
    label: "Forward Line (FL)",
    value: "FL",
  },
  {
    label: "Followers (FOL)",
    value: "FOL",
  },
  {
    label: "Interchange (IC)",
    value: "IC",
  },
  {
    label:"Reserve",
    value:"reserve"
  }
];

const SoccerPositionOption = [
  {
    label: "Goal Keeper",
    value: "GKP",
  },
  {
    label: "Defender",
    value: "DEF",
  },
  {
    label: "Midfielder",
    value: "MID",
  },
  {
    label: "Forward",
    value: "FWD",
  },
  {
    label:"Reserve",
    value:"reserve"
  }
];

class TeamSportRules extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      rulesValue: {
        minPlayer: "",
        maxPlayer: "",
        positionType: null,
      },
      RulesList: [],
      RulesCount: 0,
      errorMinPlayer: "",
      errorMaxPlayer: "",
      errorPositionType: "",
      selectedRulesID: "",
      selectedExternalStatus: null,
      errorCreate: "",
    };
  }

  componentDidMount() {
    this.fetchRulesList();
  }

  componentDidUpdate(prevProps, prevState) {
    // const { offset } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchRulesList();
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchRulesList();
      // this.setState({
      //   offset: 0,
      //   currentPage: 1,
      // });
    }
  }

  async fetchRulesList() {
    this.setState({ isLoading: true });
    const SportId = this.props.match.path?.includes("cricket")
      ? 4
      : this.props.match.path?.includes("rugbyleague")
      ? 12
      : this.props.match.path?.includes("rugbyunion")
      ? 13
      : this.props.match.path?.includes("basketball")
      ? 10
      : this.props.match.path?.includes("afl")
      ? 15
      : this.props.match.path?.includes("australianrules")
      ? 9
      : this.props.match.path?.includes("golf")
      ? 16
      : this.props.match.path?.includes("tennis")
      ? 7
      : this.props.match.path?.includes("baseball")
      ? 11
      : this.props.match.path?.includes("icehockey")
      ? 17
      : this.props.match.path?.includes("boxing")
      ? 6
      : this.props.match.path?.includes("mma")
      ? 5
      : this.props.match.path?.includes("soccer")
      ? 8
      : 14;
    try {
      const { status, data } = await axiosInstance.get(
        `/allsport/rule?sportId=${SportId}`
      );
      if (status === 200) {
        this.setState({
          RulesList: data?.result,
          isLoading: false,
          RulesCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { rulesValue } = this.state;
    let flag = true;
    if (rulesValue?.minPlayer === null) {
      flag = false;
      this.setState({
        errorMinPlayer: "This field is mandatory",
      });
    } else {
      this.setState({
        errorMinPlayer: "",
      });
    }
    if (this.props.match.path?.includes("soccer")) {
      if (rulesValue?.maxPlayer === null) {
        flag = false;
        this.setState({
          errorMaxPlayer: "This field is mandatory",
        });
      } else {
        this.setState({
          errorMaxPlayer: "",
        });
      }
    }

    if (rulesValue?.positionType === null) {
      flag = false;
      this.setState({
        errorPositionType: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPositionType: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    const { rulesValue } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props.match.path?.includes("boxing")
          ? 6
          : this.props.match.path?.includes("mma")
          ? 5
          : this.props.match.path?.includes("soccer")
          ? 8
          : 14,
        minPlayer: +rulesValue?.minPlayer,
        positionType: rulesValue?.positionType,
      };

      if (this.props.match.path?.includes("soccer")) {
        payload.maxPlayer = +rulesValue?.maxPlayer;
      }

      try {
        const { status, data } = await axiosInstance.post(
          `/allsport/rule `,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
            rulesValue: {
              minPlayer: "",
              maxPlayer: "",
              positionType: null,
            },
          });
          this.fetchRulesList();
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
        });

        // this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const { rulesValue, selectedRulesID } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props.match.path?.includes("boxing")
          ? 6
          : this.props.match.path?.includes("mma")
          ? 5
          : this.props.match.path?.includes("soccer")
          ? 8
          : 14,
        minPlayer: +rulesValue?.minPlayer,
        positionType: rulesValue?.positionType,
      };

      if (this.props.match.path?.includes("soccer")) {
        payload.maxPlayer = +rulesValue?.maxPlayer;
      }
      try {
        const { status, data } = await axiosInstance.put(
          `/allsport/rule/${selectedRulesID}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
            rulesValue: {
              minPlayer: "",
              maxPlayer: "",
              positionType: null,
            },
          });
          this.fetchRulesList();
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorMinPlayer: "",
      errorMaxPlayer: "",
      errorPositionType: "",
      errorCreate: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        rulesValue: {
          minPlayer: item?.minPlayer,
          maxPlayer: item?.maxPlayer,
          positionType: item?.positionType,
        },

        isEditMode: true,
        selectedRulesID: item?.id,
      });
    } else {
      this.setState({
        rulesValue: {
          minPlayer: "",
          maxPlayer: "",
          positionType: null,
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { itemToDelete } = this.state;

    try {
      const { status, data } = await axiosInstance.delete(
        `/allsport/rule/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchRulesList();
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        {/* <SelectIndicator /> */}
      </components.DropdownIndicator>
    );
  };

  //   handlePaginationClick = (event, page) => {
  //     let { rowPerPage } = this.state;
  //     this.setState({
  //       currentPage: Number(page),
  //       offset: (Number(page) - 1) * rowPerPage,
  //     });
  //   };

  //   handlePaginationButtonClick = (navDirection) => {
  //     let { currentPage, rowPerPage, offset } = this.state;
  //     if (navDirection === "prev") {
  //       if (offset >= rowPerPage) {
  //         this.setState({
  //           offset: offset - rowPerPage,
  //           currentPage: currentPage - 1,
  //         });
  //       }
  //     } else {
  //       this.setState({
  //         offset: offset + rowPerPage,
  //         currentPage: currentPage + 1,
  //       });
  //     }
  //   };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      //   rowPerPage,
      //   currentPage,
      rulesValue,
      RulesList,
      //   RulesCount,
      errorMinPlayer,
      errorMaxPlayer,
      errorPositionType,
      //   selectedExternalStatus,
      errorCreate,
    } = this.state;
    // const pageNumbers = [];

    // if (RulesCount > 0) {
    //   for (let i = 1; i <= Math.ceil(RulesCount / rowPerPage); i++) {
    //     pageNumbers.push(i);
    //   }
    // }
    const propsType = this.props.match.path;

    const positionOption = propsType?.includes("cricket")
      ? CricketPositionOption
      : propsType?.includes("rugbyleague")
      ? RLPositionOption
      : propsType?.includes("australianrules")
      ? ARPositionOption
      : propsType?.includes("soccer")
      ? SoccerPositionOption
      : [];

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Rules</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Rules
                </Typography>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && RulesList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && RulesList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>ID</TableCell>
                        <TableCell>Min Player</TableCell>
                        {this.props.match.path?.includes("soccer") && (
                          <TableCell>Max Player</TableCell>
                        )}
                        <TableCell>Position Type</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>

                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {RulesList?.map((item, index) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={index}
                          >
                            <TableCell> {item?.id} </TableCell>
                            <TableCell>{item?.minPlayer}</TableCell>
                            {this.props.match.path?.includes("soccer") && (
                              <TableCell>{item?.maxPlayer}</TableCell>
                            )}
                            <TableCell style={{ textTransform: "capitalize" }}>
                              {item?.positionType}
                            </TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      {/* <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                RulesCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow> */}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Rules" : "Edit Rules"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Min Player</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Min Player"
                          value={rulesValue?.minPlayer}
                          onChange={(e) =>
                            this.setState({
                              rulesValue: {
                                ...rulesValue,
                                minPlayer: e?.target?.value,
                              },
                              errorMinPlayer: e?.target?.value
                                ? ""
                                : errorMinPlayer,
                            })
                          }
                        />
                        {errorMinPlayer ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorMinPlayer}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {this.props.match.path?.includes("soccer") && (
                        <Grid
                          item
                          xs={12}
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            marginBottom: "8px",
                          }}
                        >
                          <label className="modal-label">Max Player</label>
                          <TextField
                            className="teamsport-textfield rec FAQ-textfield"
                            variant="outlined"
                            type="number"
                            color="primary"
                            size="small"
                            placeholder="Min Player"
                            value={rulesValue?.maxPlayer}
                            onChange={(e) =>
                              this.setState({
                                rulesValue: {
                                  ...rulesValue,
                                  maxPlayer: e?.target?.value,
                                },
                                errorMaxPlayer: e?.target?.value
                                  ? ""
                                  : errorMaxPlayer,
                              })
                            }
                          />
                          {errorMaxPlayer ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0px 0px 0px" }}
                            >
                              {errorMaxPlayer}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                      )}
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Position</label>
                        <Select
                          className="React teamsport-select event-Tournament-select premium-key-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Position"
                          value={
                            rulesValue?.positionType &&
                            positionOption?.find((item) => {
                              return item?.value === rulesValue?.positionType;
                            })
                          }
                          options={positionOption}
                          onChange={(e) => {
                            this.setState({
                              rulesValue: {
                                ...rulesValue,
                                positionType: e?.value,
                              },
                              errorPositionType: e?.value
                                ? ""
                                : errorPositionType,
                            });
                          }}
                        />
                        {errorPositionType ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorPositionType}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <span style={{ fontSize: "12px", color: "red" }}>
                        {errorCreate ? errorCreate : ""}
                      </span>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default TeamSportRules;
