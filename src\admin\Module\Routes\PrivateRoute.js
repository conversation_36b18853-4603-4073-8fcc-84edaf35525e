import React from "react";
import { Navigate, Route, useLocation } from "react-router-dom";
import DefaultLayout from "../DefaultLayout";

const PrivateRoute = ({ isLoggedIn, component: Component, ...rest }) => {
  const location = useLocation();

  if (isLoggedIn) {
    return (
      <Route
        {...rest}
        element={
          <DefaultLayout>
            <Component />
          </DefaultLayout>
        }
      />
    );
  } else {
    return (
      <Navigate
        to={{
          pathname: "/",
          state: { from: location },
        }}
      />
    );
  }
};

export default PrivateRoute;
