import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  Checkbox,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import CreateSports from "./createSports";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";
import { ReactComponent as Unchecked } from "../../images/uncheck-star.svg";
import { ReactComponent as Checked } from "../../images/check-star.svg";
import CreateSportsVariation from "./createSportVariation";

class Sports extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      sports: [],
      allSportsType: [],
      sportType: "",
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      SportVariationModal: false,
      sportId: "",
      sportName: "",
      sportVariationData: [],
      checkBoxValues: [],
      draggedItem: null,
      isSortChange: false,
    };
  }

  componentDidMount() {
    this.fetchAllSports();
  }

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      const sortedData = data?.result?.sort(
        (a, b) => a.sortOrder - b.sortOrder
      );
      let newdata = [];
      const filteredData = data?.result?.filter(
        (item) => item?.isFeatured === true
      );
      let categories = filteredData?.map((item) => {
        newdata.push(item?.id);
      });
      this.setState({
        sports: sortedData,
        isLoading: false,
        checkBoxValues: newdata,
      });
      this.fetchAllSportType();
    }
  }

  async fetchAllSportType() {
    const { status, data } = await axiosInstance.get(URLS.sportType);
    if (status === 200) {
      this.setState({ allSportsType: data.result });
    }
  }

  getSportType = (id) => {
    let { allSportsType } = this.state;
    let sportType = allSportsType
      .filter((obj) => obj.id === id)
      .map((object) => object.sportType);
    return sportType;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.sports}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllSports();
        });
        this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, sports } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < sports.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };
  inputSportVariationModal = (id, name, data) => {
    this.setState({
      SportVariationModal: true,
      sportId: id,
      sportName: name,
      sportVariationData: data,
    });
  };
  toggleinputSportVariationModal = () => {
    this.setState({ SportVariationModal: false });
  };

  handleDragStart = (item) => {
    this.setState({ draggedItem: item });
  };

  // Function to handle the drop and reorder the items
  handleDrop = (targetIndex) => {
    const { sports, draggedItem } = this.state;
    if (draggedItem) {
      const updatedItems = sports ? [...sports] : [];
      const draggedIndex = sports.indexOf(draggedItem);

      // Remove the dragged item from the original position
      updatedItems.splice(draggedIndex, 1);

      // Insert the dragged item at the new position
      updatedItems.splice(targetIndex, 0, draggedItem);
      this.setState({
        sports: updatedItems,
        draggedItem: null,
        isSortChange: true,
      });
    }
  };
  handleOrderChange = async () => {
    const { sports } = this.state;

    let newdata = [];
    let categories = sports?.map((item) => {
      newdata.push(item?.id);
    });
    try {
      const { status, data } = await axiosInstance.put(
        `/sports/sport/featuredOrder/featuredOrder`,
        {
          featuredOrder: newdata,
        }
      );
      if (status === 200) {
        this.setState({ isSortChange: false });
        this.fetchAllSports();
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isSortChange: false });
      }
    } catch (err) {
      this.setState({ isSortChange: false });
    }
  };

  handleChangeFeatureSport = async (isFeatured, id) => {
    try {
      const { status, data } = await axiosInstance.put(
        `/sports/sport/featuredOrder/featuredOrder`,
        {
          isFeatured: isFeatured,
          SportId: id,
        }
      );
      if (status === 200) {
        this.setActionMessage(true, "Success", data?.message);
      } else {
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err.response.data.message);
    }
  };

  handleCheckBoxChange = (e) => {
    const { value, checked } = e.target;
    const { checkBoxValues } = this.state;
    if (checked) {
      let checkboxdata = [...checkBoxValues, Number(value)];
      this.setState({
        checkBoxValues: checkboxdata,
      });

      this.handleChangeFeatureSport(true, Number(value));
    } else {
      let checkboxdata = checkBoxValues?.filter(
        (element) => element !== Number(value)
      );
      this.setState({
        checkBoxValues: checkboxdata,
      });
      this.handleChangeFeatureSport(false, Number(value));
    }
  };

  render() {
    var {
      sports,
      sportType,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      allSportsType,
      SportVariationModal,
      sportId,
      // sportVariationData,
      sportName,
      checkBoxValues,
    } = this.state;
    const pageNumbers = [];

    sportType !== "" &&
      (sports = sports.filter((obj) => obj.sportTypeId == sportType));

    let currentPageRow = sports;

    // if (sports?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = sports.slice(indexOfFirstTodo, indexOfLastTodo);

    //   for (let i = 1; i <= Math.ceil(sports.length / rowPerPage); i++) {
    //     pageNumbers.push(i);
    //   }
    // }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Master Data
                </Link>
                <Typography className="active_p">Sports</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={8}>
                <Typography variant="h1" align="left">
                  Sports
                </Typography>
              </Grid>
              <Grid item xs={4} className="admin-filter-wrap">
                <SelectBox
                  onChange={(e) => this.setState({ sportType: e.target.value })}
                  style={{ width: "60%" }}
                >
                  <option value="">Select Sport Type</option>
                  {allSportsType?.length > 0 &&
                    allSportsType?.map((obj, i) => (
                      <option key={i} value={obj?.id}>
                        {obj?.sportType}
                      </option>
                    ))}
                </SelectBox>

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap"
                style={{ marginBottom: "10px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: this.state.isSortChange
                      ? "#4455c7"
                      : "rgba(0, 0, 0, 0.12)",
                    color: this.state.isSortChange
                      ? "#fff"
                      : "rgba(0, 0, 0, 0.26)",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  disabled={!this.state.isSortChange}
                  onClick={() => {
                    this.handleOrderChange();
                  }}
                >
                  Save Order
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && sports.length === 0 && <p>No Data Available</p>}
            {!isLoading && sports.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Feature</TableCell>
                        <TableCell>Sport Type</TableCell>
                        <TableCell>Sport Name</TableCell>
                        <TableCell align="center">Sport Final Round</TableCell>
                        {/* <TableCell >
                          variation
                        </TableCell> */}
                        <TableCell>variation</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {sports?.map((sport, i) => (
                        <TableRow
                          key={i}
                          className="table-rows listTable-Row"
                          draggable
                          onDragStart={() => this.handleDragStart(sport)}
                          onDragOver={(e) => e.preventDefault()}
                          onDrop={() => this.handleDrop(i)}
                        >
                          <TableCell>{sport.id}</TableCell>
                          <TableCell>
                            <Checkbox
                              disableRipple
                              disableFocusRipple
                              disableTouchRipple
                              className="filter-racing"
                              icon={<Unchecked />}
                              checkedIcon={<Checked />}
                              name="filter"
                              value={sport?.id}
                              onChange={(event) => {
                                this.handleCheckBoxChange(event, sport?.id);
                              }}
                              checked={checkBoxValues?.includes(sport?.id)}
                              disabled={
                                sport?.id == 1 ||
                                sport?.id == 2 ||
                                sport?.id == 3
                              }
                            />
                          </TableCell>
                          <TableCell>
                            {this.getSportType(sport.sportTypeId)}
                          </TableCell>
                          <TableCell>{sport.sportName}</TableCell>
                          <TableCell align="center">
                            {sport?.sportFinalCount}
                          </TableCell>
                          {/* <TableCell>
                            {showVariations(sport.variation)}
                          </TableCell> */}
                          <TableCell>
                            <Button
                              variant="contained"
                              style={{
                                backgroundColor: "#4455C7",
                                color: "#fff",
                                borderRadius: "8px",
                                textTransform: "capitalize",
                                padding: "8px 10px ",
                                marginLeft: "15px",
                              }}
                              onClick={() =>
                                this.inputSportVariationModal(
                                  sport.id,
                                  sport.sportName,
                                  sport?.SportVariations
                                )
                              }
                            >
                              Add/Edit variation
                            </Button>
                          </TableCell>
                          <TableCell>
                            {/* <EditIcon
                              onClick={this.inputModal(sport.id, "edit")}
                              color="primary"
                              className="mr10 cursor iconBtn admin-btn-green"
                            /> */}
                            <Button
                              onClick={this.inputModal(sport.id, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            {/* <DeleteOutlineIcon
                              onClick={this.setItemToDelete(sport.id)}
                              color="secondary"
                              className="cursor iconBtn admin-btn-orange"
                            /> */}
                            <Button
                              onClick={this.setItemToDelete(sport.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {/* <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> /}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> /}
                          </div>
                        </TableCell>
                      </TableRow> */}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Sports" : "Edit Sports"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateSports
                  type={this.state.type}
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllSports={this.afterChangeRefresh}
                  allSportsType={allSportsType}
                />
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={SportVariationModal}
              onClose={this.toggleinputSportVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">{sportName} Variation Details</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleinputSportVariationModal}
                />
                <CreateSportsVariation
                  inputModal={this.toggleinputSportVariationModal}
                  fetchAllSports={this.afterChangeRefresh}
                  id={sportId}
                  sportVariationsData={sports?.filter(
                    (item) => sportId == item.id
                  )}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Sports;

const SelectBox = styled.select`
  width: 100%;
  margin-top: 5px;
  font-size: 16px;
  border-radius: 3px;
  min-height: 38px;
  border: 1px solid #ddd;
  padding-left: 10px;
`;
