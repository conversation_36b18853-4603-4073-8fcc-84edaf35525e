import Axios from "axios/index";

import { errorHand<PERSON>, fetchFromStorage } from "../../library/utilities";
import { identifiers } from "../../library/common/constants";
import { config } from "../config";


const customAxios = Axios.create({
  baseURL: config.individualApiUrl,
  headers: { "Content-Type": "application/json" },
});

customAxios.interceptors.request.use((config) => {
  const token = fetchFromStorage(identifiers.token);
  const clonedConfig = config;

  if (token) {
    clonedConfig.headers.common = {
      Authorization: `Bearer ${token.access_token}`,
      "Content-Type": "application/json",
    };
  }

  return clonedConfig;
});

customAxios.interceptors.response.use(
  (config) => {
    return config;
  },
  (error) => {
    errorHandler(error);
    return Promise.reject(error);
  }
);
export default  customAxios;
