.rugby-text {
  .rugby-textfield {
    width: 95%;
    margin-bottom: 15px;
    margin-top: 3px;
    input {
      border-radius: 8px;
    }

    .MuiOutlinedInput-root {
      border-radius: 8px;

      .MuiOutlinedInput-inputMarginDense {
        padding: 10.5px;
        background-color: #ffffff;
      }
      .MuiOutlinedInput-inputMultiline {
        padding: 10.5px;
      }
    }
  }
  .eventname {
    width: 97%;
  }
}
.rugby-select {
  width: 20%;
  .select__control {
    height: 100%;
    margin-top: 3px;
    border-radius: 8px;
  }
  .select__menu {
    text-align: left;
  }
}
.national-select {
  .rugby-select {
    width: 95%;
  }
}
.radio-wrap {
  .gender {
    flex-direction: row;
    .MuiButtonBase-root {
      min-width: auto;
    }
  }
}
.rugby-multiple-select {
  width: 97%;
}
.rugby-dob-wrap {
  .dob-picker {
    margin: 0px;
  }
}

.date-time-picker-wrap {
  .date-time-picker {
    width: 95%;
    input {
      background: white;
      padding: 10.5px 14px;
      border-radius: 8px;
    }
    fieldset {
      border-radius: 8px;
    }
  }
}
.eventname {
  width: 97%;
}
.event-Tournament-select {
  width: 95%;
}
.external-select {
  .select__control {
    margin-top: 0px;
  }
}
.MuiTableCell-root {
  border-bottom: none;
}
.MuiTableRow-root {
  border-bottom: 1px solid rgba(224, 224, 224, 1);
}
.stepper-input-wrap {
  margin-top: 15px !important;
  .stepper-input {
    display: flex;
    border: 1px solid #c9c9c9;
    border-radius: 8px;
    max-height: 36px;
    width: 95%;
    .stepper-input__button {
      padding: 10px 14px;
      min-width: 35px;
      cursor: pointer;
      border-radius: 0px;

      border-left: 1px solid #c9c9c9;
      border-collapse: collapse;
      &:first-child {
        border-left: 0px;
        border-right: 1px solid #c9c9c9;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
      }
    }
    .stepper-input__content {
      // font-family: $regulerFont;
      font-size: 16px;
      line-height: 20px;
      font-weight: 600;
      padding: 8px 26px;
      background-color: #ffffff;
      color: #000000;
      width: 100%;
      text-align: center;
    }
  }
}
