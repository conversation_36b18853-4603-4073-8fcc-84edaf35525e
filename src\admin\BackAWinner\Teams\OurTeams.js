import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../../../src/images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../../library/common/constants";
// import ActionMessage from "../../../library/common/components/ActionMessage";
import { ActionMessage, Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../../helpers/common";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import "../../TeamSport/teamsport.scss";
import "../backAWinner.scss";
import _ from "lodash";

const PositionOption = [
  { label: "Author", value: "Author" },
  { label: "Editor", value: "Editor" },
];
// const CategoryOption = [
//   { label: "Back A Winner Team Members", value: "Back A Winner Team Members" },
//   { label: "Other Team Members", value: "Other Team Members" },
// ];
// const ExternalCategoryOption = [
//   { label: "All Team Members", value: null },
//   { label: "Back A Winner Team Members", value: "Back A Winner Team Members" },
//   { label: "Other Team Members", value: "Other Team Members" },
// ];
class OurTeams extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      teamValues: {
        name: "",
        position: null,
        sport: "",
        about: "",
        linkedin: "",
        twitter: "",
        instagram: "",
        category: null,
        id: "",
      },
      tagList: [],
      tagCount: 0,
      errorTitle: "",
      errorCategory: "",
      errorAbout: "",
      errorContent: "",
      errorTwitter: "",
      errorLinkedin: "",
      errorInstagram: "",
      errorPosition: "",
      search: "",
      selectedCategory: null,
      ExternalCategoryOption: [],
      CategoryOption: [],
      draggedItem: null,
      isSortChange: false,
      positionOptions: [],
    };
  }

  componentDidMount() {
    this.fetchAllTeamDetails("", null);
    this.fetchAllOurTeamCategory();
    this.getPositionsForTeam();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllTeamDetails(this.state?.search, this.state.selectedCategory);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllTeamDetails("", null);
      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
      });
    }
  }

  async fetchAllTeamDetails(searchvalue, selectedCategory) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      let passApi = `ourteam?search=${searchvalue}&categoryId=${
        selectedCategory ? selectedCategory : ""
      }`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          tagList: data?.result,
          isLoading: false,
          tagCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  async fetchAllOurTeamCategory() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(`ourteam/all/category`);
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Category",
        value: 0,
      };
      let alldata = [alldatas, ...newdata];
      this.setState({
        ExternalCategoryOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        CategoryOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
      });
    }
  }
  handalValidate = () => {
    let { teamValues } = this.state;
    // const socialMediaRegex = {
    //   twitter: /^(https?:\/\/)?(www\.)?twitter\.com\/([a-zA-Z0-9_]+)/,
    //   linkedin:
    //     /^(https?:\/\/)?(www\.)?linkedin\.com\/(in|company)\/([a-zA-Z0-9\-_]+)/,
    //   instagram: /^(https?:\/\/)?(www\.)?instagram\.com\/([a-zA-Z0-9_]+)/,
    // };
    const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
    let flag = true;
    if (teamValues?.name?.trim() === "") {
      flag = false;
      this.setState({
        errorTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTitle: "",
      });
    }
    if (teamValues?.category === null) {
      flag = false;
      this.setState({
        errorCategory: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCategory: "",
      });
    }
    if (teamValues?.position === null) {
      flag = false;
      this.setState({
        errorPosition: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPosition: "",
      });
    }
    if (teamValues?.sport?.trim() === "") {
      flag = false;
      this.setState({
        errorContent: "This field is mandatory",
      });
    } else {
      this.setState({
        errorContent: "",
      });
    }
    if (teamValues?.about?.trim() === "") {
      flag = false;
      this.setState({
        errorAbout: "This field is mandatory",
      });
    } else {
      this.setState({
        errorAbout: "",
      });
    }
    if (
      teamValues?.twitter &&
      teamValues?.twitter?.trim() !== "" &&
      !urlRegex.test(teamValues?.twitter)
    ) {
      flag = false;
      this.setState({
        errorTwitter: "Please enter a valid URL",
      });
    } else {
      this.setState({
        errorTwitter: "",
      });
    }
    if (
      teamValues?.linkedin &&
      teamValues?.linkedin?.trim() !== "" &&
      !urlRegex.test(teamValues?.linkedin)
    ) {
      flag = false;
      this.setState({
        errorLinkedin: "Please enter a valid URL",
      });
    } else {
      this.setState({
        errorLinkedin: "",
      });
    }
    if (
      teamValues?.instagram &&
      teamValues?.instagram?.trim() !== "" &&
      !urlRegex.test(teamValues?.instagram)
    ) {
      flag = false;
      this.setState({
        errorInstagram: "Please enter a valid URL",
      });
    } else {
      this.setState({
        errorInstagram: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    const { teamValues } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        name: teamValues?.name,
        position: teamValues?.position,
        sports: teamValues?.sport,
        content: teamValues?.about,
        socialProfile: {
          twitter: teamValues?.twitter ? teamValues?.twitter : null,
          instagram: teamValues?.instagram ? teamValues?.instagram : null,
          linkedin: teamValues?.linkedin ? teamValues?.linkedin : null,
        },
        categoryId: teamValues?.category,
        positionId: teamValues?.position,
      };
      try {
        const { status } = await axiosInstance.post(`ourteam`, payload);
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllTeamDetails(
            this?.state?.search,
            this.state.selectedCategory
          );
          this.setActionMessage(
            true,
            "Success",
            `New Team Member Created Successfully`
          );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  handleUpdate = async () => {
    const { teamValues } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        name: teamValues?.name,
        position: teamValues?.position,
        sports: teamValues?.sport,
        content: teamValues?.about,
        socialProfile: {
          twitter: teamValues?.twitter ? teamValues?.twitter : null,
          instagram: teamValues?.instagram ? teamValues?.instagram : null,
          linkedin: teamValues?.linkedin ? teamValues?.linkedin : null,
        },
        categoryId: teamValues?.category,
        positionId: teamValues?.position,
      };

      try {
        const { status } = await axiosInstance.put(
          `ourteam/${this.state.teamValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllTeamDetails(
            this?.state?.search,
            this.state.selectedCategory
          );
          this.setActionMessage(
            true,
            "Success",
            `Team Member Updated Successfully`
          );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTitle: "",
      errorCategory: "",
      errorAbout: "",
      errorContent: "",
      errorPosition: "",
      errorTwitter: "",
      errorLinkedin: "",
      errorInstagram: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });

    if (type === "edit") {
      this.setState({
        teamValues: {
          name: item?.name,
          position: item?.OurTeamPosition?.id,
          sport: item?.sports,
          about: item?.content,
          linkedin: item?.socialProfile?.linkedin
            ? item?.socialProfile?.linkedin
            : null,
          twitter: item?.socialProfile?.twitter
            ? item?.socialProfile?.twitter
            : null,
          instagram: item?.socialProfile?.instagram
            ? item?.socialProfile?.instagram
            : null,
          category: item?.teamCategory?.id,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        teamValues: {
          name: "",
          position: null,
          sport: "",
          about: "",
          linkedin: "",
          twitter: "",
          instagram: "",
          category: null,
          id: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const passApi = `ourteam/${this.state.itemToDelete}`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllTeamDetails(
            this?.state?.search,
            this.state.selectedCategory
          );
        });
        this.setActionMessage(
          true,
          "Success",
          "Team Member Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, tagList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };
  handleExternalCategoryChange = (e) => {
    this.setState({
      selectedCategory: e.value,
      currentPage: 1,
    });
    this.fetchAllTeamDetails(this.state.search, e.value);
  };

  // handleDragStart = (item) => {
  //   this.setState({ draggedItem: item });
  // };

  // Function to handle the drop and reorder the items

  // handleDrop = (targetIndex) => {
  //   const { tagList, draggedItem } = this.state;

  //   if (draggedItem) {
  //     const updatedItems = [...tagList];
  //     const draggedIndex = tagList.indexOf(draggedItem);

  //     // Remove the dragged item from the original position
  //     updatedItems.splice(draggedIndex, 1);

  //     // Insert the dragged item at the new position
  //     updatedItems.splice(targetIndex, 0, draggedItem);

  //     this.setState({
  //       tagList: updatedItems,
  //       draggedItem: null,
  //       isSortChange: true,
  //     });
  //   }
  // };

  handleDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    const updatedList = Array.from(this.state?.tagList);
    const [draggedItem] = updatedList.splice(result.source.index, 1);
    updatedList.splice(result.destination.index, 0, draggedItem);
    this.setState({ tagList: updatedList, isSortChange: true });
  };

  handleOrderChange = async () => {
    const { offset, search, tagList, sortLabelid } = this.state;
    let sortData = sortLabelid;

    let newdata = [];
    let categories = tagList?.map((item) => {
      newdata.push(item?.id);
    });
    try {
      const { status, data } = await axiosInstance.put(
        `ourteam/update/order`,
        newdata
      );
      if (status === 200) {
        this.setState({ isSortChange: false });
        this.fetchAllTeamDetails(
          this.state?.search,
          this.state.selectedCategory
        );
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isSortChange: false });
      }
    } catch (err) {
      this.setState({ isSortChange: false });
    }
  };

  handleKeyDown = (event) => {
    var { search, selectedCategory } = this.state;
    if (event.key === "Enter") {
      this.fetchAllTeamDetails(search, selectedCategory);
      this.setState({ currentPage: 1 });
    }
  };

  async getPositionsForTeam() {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      let passApi = `/ourteam/all/position?limit=20&offset=${offset}`;
      // let passApi = `ourteam?search=${searchvalue}&categoryId=${
      //   selectedCategory ? selectedCategory : ""
      // }`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = Math.ceil(data?.result?.count / 20);
        let newdata = [];
        let category = data?.result?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });

        let filterData = _.unionBy(this.state?.positionOptions, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });

        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });

        // finalData = finalData?.filter((item) => item?.value !== 0);
        this.setState({
          positionOptions: finalData,
          isCategoryLoading: false,
          categoryCount: count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handleOnScrollBottomCategory = (e, type) => {
    let {
      categoryCount,
      categoryOffset,
      isCategorySearch,
      searchCatogoryCount,
      searchCategoryPage,
    } = this.state;
    if (
      isCategorySearch !== "" &&
      searchCatogoryCount !== Math.ceil(searchCategoryPage / 20 + 1)
    ) {
      this.handleCategoryInputChange(searchCategoryPage + 20, isCategorySearch);
      this.setState({
        searchCategoryPage: searchCategoryPage + 20,
      });
    } else {
      if (
        categoryCount !==
          (categoryCount == 1 ? 1 : Math.ceil(categoryOffset / 20)) &&
        isCategorySearch == ""
      ) {
        this.fetchAllCategory(categoryOffset + 20);
        this.setState({
          categoryOffset: categoryOffset + 20,
        });
      }
    }
  };
  handleCategoryInputChange = (categoryOffset, value) => {
    // const passApi = `v2/news/admin/category?isAll=1&limit=20&offset=${categoryOffset}&search=${value}`;
    const passApi = `/ourteam/all/position?limit=20&offset=${categoryOffset}&name=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let category = data?.result?.result?.map((item) => {
          newdata.push({
            label: item?.initialTitle,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchCategory, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchCategory: finalData,
          searchCatogoryCount: Math.ceil(count),
          isCategorySearch: value,
        });
      }
    });
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      teamValues,
      tagList,
      tagCount,
      errorTitle,
      errorCategory,
      errorContent,
      errorPosition,
      errorTwitter,
      errorLinkedin,
      errorInstagram,
      errorAbout,
      search,
      selectedCategory,
      ExternalCategoryOption,
      CategoryOption,
      positionOptions,
    } = this.state;
    const pageNumbers = [];
    if (tagCount > 0) {
      for (let i = 1; i <= Math.ceil(tagCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                {/* <Link underline="hover" color="inherit">
                News
              </Link> */}
                <Typography className="active_p">Our Team</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  Our Team
                </Typography>
              </Grid>

              <Grid
                item
                xs={8}
                className="admin-filter-wrap admin-fixture-wrap"
              >
                <Select
                  className="React teamsport-select external-select text-capitalize ourteam-select"
                  classNamePrefix="select"
                  placeholder="Select Category"
                  value={ExternalCategoryOption?.find((item) => {
                    return item?.value == selectedCategory;
                  })}
                  onChange={(e) => this.handleExternalCategoryChange(e)}
                  options={ExternalCategoryOption}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllTeamDetails(search, selectedCategory);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Button
                variant="contained"
                style={{
                  backgroundColor: this.state.isSortChange
                    ? "#4455c7"
                    : "rgba(0, 0, 0, 0.12)",
                  color: this.state.isSortChange
                    ? "#fff"
                    : "rgba(0, 0, 0, 0.26)",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                }}
                disabled={!this.state.isSortChange}
                onClick={() => {
                  this.handleOrderChange();
                }}
              >
                Save Order
              </Button>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && tagList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && tagList?.length > 0 && (
              <DragDropContext onDragEnd={this.handleDragEnd}>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell> Name </TableCell>
                        <TableCell> Content </TableCell>
                        <TableCell> Position </TableCell>
                        <TableCell> Category </TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <Droppable droppableId="your-droppable-id">
                      {(provided, snapshot) => (
                        <TableBody
                          className="table_body"
                          ref={provided.innerRef}
                        >
                          <TableRow className="table_row">
                            <TableCell
                              colSpan={100}
                              className="table-seprator"
                            ></TableCell>
                          </TableRow>
                          {tagList?.map((item, index) => {
                            return (
                              <Draggable
                                key={item?.id}
                                draggableId={`draggable-${item?.id}`}
                                index={index}
                              >
                                {(provided, snapshot) => (
                                  <TableRow
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    className="table-rows listTable-Row"
                                    style={{
                                      cursor: "all-scroll",
                                      ...provided.draggableProps.style,
                                    }}
                                    key={item?.id}
                                  >
                                    <TableCell> {item?.id} </TableCell>
                                    <TableCell>{item?.name}</TableCell>
                                    <TableCell>{item?.sports}</TableCell>
                                    <TableCell>
                                      {item?.OurTeamPosition?.name
                                        ? item?.OurTeamPosition?.name
                                        : "-"}
                                    </TableCell>
                                    <TableCell>
                                      {item?.teamCategory?.name}
                                    </TableCell>
                                    <TableCell>
                                      <Button
                                        onClick={this.inputModal(item, "edit")}
                                        className="table-btn edit-btn"
                                      >
                                        Edit
                                      </Button>
                                      <Button
                                        onClick={this.setItemToDelete(item?.id)}
                                        className="table-btn delete-btn"
                                      >
                                        Delete
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                )}
                              </Draggable>
                            );
                          })}
                          {/* <TableRow>
                    <TableCell colSpan={100} className="pagination">
                      <div className="tablePagination">
                        {/* <button
                            className={
                              tagList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              tagList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> /}
                        <Pagination
                          hideNextButton
                          hidePrevButton
                          disabled={tagCount / rowPerPage > 1 ? false : true}
                          page={currentPage}
                          onChange={this.handlePaginationClick}
                          count={pageNumbers[pageNumbers?.length - 1]}
                          siblingCount={2}
                          boundaryCount={1}
                          size="small"
                        />
                        {/* <button
                            className={
                              tagList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              tagList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> /}
                      </div>
                    </TableCell>
                  </TableRow> */}
                        </TableBody>
                      )}
                    </Droppable>
                  </Table>
                </TableContainer>
              </DragDropContext>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input recommended-modal our-team-modal"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Team Member" : "Edit Team Member"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Name </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Name"
                          style={{ marginTop: "0px" }}
                          value={teamValues?.name}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                name: e.target.value,
                              },
                              errorTitle: e?.target?.value ? "" : errorTitle,
                            })
                          }
                        />
                        {errorTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">Category</label>
                        <Select
                          className="React teamsport-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={CategoryOption?.find((op) => {
                            return op?.value === teamValues?.category;
                          })}
                          onChange={(e) => {
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                category: e?.value,
                              },
                              errorCategory: e?.value ? "" : e?.value,
                            });
                          }}
                          options={CategoryOption}
                        />
                        {errorCategory ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorCategory}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">Position</label>
                        <Select
                          className="React teamsport-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomCategory(e)
                          }
                          onInputChange={(e) =>
                            this.handleCategoryInputChange(0, e)
                          }
                          value={positionOptions?.find((op) => {
                            return op?.value === teamValues?.position;
                          })}
                          onChange={(e) => {
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                position: e?.value,
                                errorPosition: e?.target?.value
                                  ? ""
                                  : errorPosition,
                              },
                            });
                          }}
                          options={positionOptions}
                        />
                        {teamValues?.position === null && errorPosition ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorPosition}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Content </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Content"
                          style={{ marginTop: "0px" }}
                          value={teamValues?.sport}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                sport: e.target.value,
                              },
                              errorContent: e?.target?.value
                                ? ""
                                : errorContent,
                            })
                          }
                        />
                        {errorContent ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorContent}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text recommended-textarea"
                      >
                        <label className="modal-label">
                          {" "}
                          About Team Member
                        </label>

                        <TextField
                          className="teamsport-textfield rec recommended-textfield"
                          variant="outlined"
                          type="textarea"
                          multiline
                          maxRows={3}
                          color="primary"
                          size="small"
                          placeholder="Short Description"
                          value={teamValues?.about}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                about: e?.target?.value,
                              },
                              errorAbout: e?.target?.value ? "" : errorAbout,
                            })
                          }
                        />
                        {errorAbout ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorAbout}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Twitter </label>
                        <TextField
                          className="teamsport-textfield recommended-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Twitter Link"
                          style={{ marginTop: "0px" }}
                          value={teamValues?.twitter}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                twitter: e.target.value,
                              },
                              errorTwitter:
                                e.target.value?.trim() !== "" &&
                                !/^(ftp|http|https):\/\/[^ "]+$/.test(
                                  e.target.value
                                )
                                  ? errorTwitter
                                  : "",
                            })
                          }
                        />
                        {errorTwitter ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTwitter}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> LinkendIn </label>
                        <TextField
                          className="teamsport-textfield recommended-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="LinkendIn Link"
                          style={{ marginTop: "0px" }}
                          value={teamValues?.linkedin}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                linkedin: e.target.value,
                              },
                              errorLinkedin:
                                e.target.value?.trim() !== "" &&
                                !/^(ftp|http|https):\/\/[^ "]+$/.test(
                                  e.target.value
                                )
                                  ? errorLinkedin
                                  : "",
                            })
                          }
                        />
                        {errorLinkedin ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorLinkedin}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Instagram </label>
                        <TextField
                          className="teamsport-textfield recommended-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Instagram Link"
                          style={{ marginTop: "0px" }}
                          value={teamValues?.instagram}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                instagram: e.target.value,
                              },
                              errorInstagram:
                                e.target.value?.trim() !== "" &&
                                !/^(ftp|http|https):\/\/[^ "]+$/.test(
                                  e.target.value
                                )
                                  ? errorInstagram
                                  : "",
                            })
                          }
                        />
                        {errorInstagram ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorInstagram}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default OurTeams;
