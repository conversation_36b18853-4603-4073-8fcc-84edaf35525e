import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  TableSortLabel,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import { config } from "../../../helpers/config";
import FileUploader from "../../../library/common/components/FileUploader";
import DeleteIcon from "@mui/icons-material/Delete";
// import "../teamsport.scss";
import "../expertTips.scss";
import moment from "moment-timezone";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import { IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import { identifiers } from "../../../library/common/constants";
import _, { includes } from "lodash";
import { fetchFromStorage } from "../../../library/utilities";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import he from "he";
import slugify from "slugify";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 0,
});
class BAWFeaturedRace extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      expertTipsValues: {
        startDate: moment(Date()).format("YYYY-MM-DD"),
        modalTrackId: null,
        raceDetail: "",
        preview: "",
        trackCondition: "",
        weather: "",
        adShortCode: "",
        modalSelectedRace: null,
        title: "",
        raceDetailTitle: "",
        raceCourseUrl: "",
        previewTitle: "",
        runnerByRunnerTitle: "",
        tipsTitle: "",
        raceResultTitle: "",
        first: null,
        second: null,
        third: null,
        fourth: null,
        firstMargin: "",
        secondMargin: "",
        thirdMargin: "",
        fourthMargin: "",
        weekRacePreview: "",
      },
      defaultImage: [],
      defaultUploadImage: "",
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      ExpertTipsList: [],
      ExpertTipsCount: 0,
      providersDetails: [],
      selectedProvider: 0,
      serachValue: "",
      SelectedSport: null,
      AllSportsOption: [],
      SelectedUsers: null,
      AllUserOption: [],
      sortDate: null,
      startDateOpen: false,
      selectedModalSport: null,
      modalSportsOption: [],
      selectedSportObj: null,
      isTrackLoading: false,
      isTrackRaceLoading: false,
      isTrackAllRaceLoading: false,
      isBestBetRaceRunnerLoading: false,
      isEachWayRaceRunnerLoading: false,
      isLayRaceRunnerLoading: false,
      TrackData: [],
      calenderId: "",
      type: "",
      topSelection: [],
      TrackAllRaceData: [],
      TrackRaceData: [],
      errorStartDate: "",
      errorModalSport: "",
      errorTrack: "",
      errorAdShortCode: "",
      errorRaceDetail: "",
      errorPreview: "",
      errorTopSelection: "",
      errorTitle: "",
      errorRaceDetailTitle: "",
      errorPreviewTitle: "",
      errorRunnerByRunnerTitle: "",
      errorTipsTitle: "",
      errorRaceResultTitle: "",
      errorRaceCourseUrl: "",
      errorWeekRacePreview: "",
      errorBannerImage: "",
      errorWpCreds: "",
      errorStateCode: "",
      errorApi: "",
      tipsModalDetailsOpen: false,
      tipsModalDetails: {},
      tipsModalDetailsIsLoading: false,
      sortType: "id",
      sortId: false,
      sortEventName: true,
      sortEventDate: true,
      sortSportType: true,
      sortUser: true,
      wpCategoryLoading: false,
      wpCategoryData: [],
      errorModalwpCategory: "",
      runnerCommentValues: [],
      runnerCommentErrors: [],
      SelectedId: "",
      raceDetailContent: "",
      previewContent: "",
      weekRacePreviewContent: "",
      thisWeekRace: [],
    };
  }

  // handleChange = (raceId, selectedOptions) => {
  //   this.setState((prevState) => ({
  //     selectedValues: {
  //       ...prevState?.selectedValues,
  //       [raceId]: selectedOptions?.map((option) => option?.value),
  //     },
  //   }));
  // };

  //   handleRunnerInputChange = (index, value) => {
  //     const { runnerCommentValues, runnerCommenterrors } = this.state;
  //     const updatedValues = [...runnerCommentValues];
  //     updatedValues[index] = value;

  //     // Update state with new values
  //     this.setState({
  //       runnerCommentValues: updatedValues,
  //       // Clear validation error if value is provided
  //       runnerCommenterrors: value
  //         ? [
  //             ...runnerCommenterrors.slice(0, index),
  //             "",
  //             ...runnerCommenterrors.slice(index + 1),
  //           ]
  //         : runnerCommenterrors,
  //     });
  //   };
  handleRunnerInputChange = (index, value) => {
    const { runnerCommentValues, runnerCommentErrors } = this.state;
    const updatedValues = [...runnerCommentValues];
    updatedValues[index] = value;

    // Update state with new values
    this.setState({
      runnerCommentValues: updatedValues,
      runnerCommentErrors:
        value && value !== "<p><br></p>"
          ? [
              ...runnerCommentErrors.slice(0, index),
              "",
              ...runnerCommentErrors.slice(index + 1),
            ]
          : runnerCommentErrors,
    });

    // Validate all text fields
    // this.validateInputs(updatedValues);
  };

  // Validate all text fields
  // validateInputs = () => {
  //   const { runnerCommentValues } = this.state;
  //   const errors = runnerCommentValues?.map((value) =>
  //     value ? "" : "This field is required"
  //   );
  //   // Update state with validation errors
  //   this.setState({ runnerCommentErrors: errors });
  // };

  // Validate all text fields when any text field changes
  handleValidationOnChange = (index, value) => {
    // Update state with new value for the changed text field
    this.handleInputChange(index, value);
  };
  componentDidMount() {
    this.fetchAllSports();
    this.fetchAllUser();
    this.fetchAllEvent(0, null, null, "", this.state?.sortType, false, null);
    this.handleWpError();
  }

  componentDidUpdate(prevProps, prevState) {
    let {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    if (prevState.offset !== offset) {
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "raceName"
          ? sortEventName
          : sortType === "startDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        sortType,
        sortData,
        SelectedUsers
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEvent(0, null, null, "", "id", false, null);
      this.fetchAllSports();
      this.setState({
        offset: 0,
        currentPage: 1,
      });
    }
  }

  handleWpError = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/expertTips/errorHandled`
      );
      if (status === 200) {
        this.setState({
          errorWpCreds: "",
        });
      } else {
      }
    } catch (err) {
      this.setState({
        errorWpCreds: err?.response?.data?.message,
      });
      this.setActionMessage(
        true,
        "Error",
        err?.response?.data?.message,
        "wordpresserror"
      );
    }
  };
  handleStateCodeError = async (trackId) => {
    try {
      const { status, data } = await axiosInstance.get(
        `/expertTips/errorHandled?trackId=${trackId}`
      );
      if (status === 200) {
        this.setState({
          errorStateCode: "",
        });
      } else {
      }
    } catch (err) {
      this.setState({
        errorStateCode: err?.response?.data?.message,
      });
    }
  };

  fetchAllEvent = async (page, date, sportId, search, type, order, userId) => {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = `/racingFeatured/featuredRace?limit=${rowPerPage}&offset=${page}&timezone=${timezone}&date=${
        date ? date : ""
      }&SportId=${sportId ? sportId : ""}&search=${search}&orderBy=${
        type ? type : ""
      }&sort=${order ? "ASC" : "DESC"}&UserId=${
        userId && userId != null ? userId : ""
      }`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          // ExpertTipsList: data?.result?.rows,
          ExpertTipsList: data?.result,
          isLoading: false,
          ExpertTipsCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchSingleEvent = async (id) => {
    const { expertTipsValues } = this.state;
    this.setState({ tipsModalDetailsIsLoading: true });
    try {
      const passApi = `/racingFeatured/featuredRace/${id}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.bestBetRaceRunner(
          data?.result?.RaceId,
          data?.result?.FeaturedRaceRunners
        );
        this.setState({
          tipsModalDetailsIsLoading: false,
        });
        let newdata = [];

        const runnerComment = data?.result?.FeaturedRaceRunners?.map((runner) =>
          newdata?.push(runner?.comment ? runner?.comment : "")
        );
        const item = data?.result;
        const date = item?.FeaturedRaceCalender?.date;
        this.setState({
          type: item?.FeaturedRaceCalender?.type,
          expertTipsValues: {
            ...expertTipsValues,
            startDate: moment(item?.Race?.Event?.eventDate)
              .tz(timezone)
              .format("YYYY-MM-DD"),
            modalTrackId: item?.calenderId,
            raceDetail: item?.raceDetails,
            preview: item?.preview,
            previewTitle: item?.previewTitle,
            trackCondition: item?.trackCondition
              ? _.startCase(
                  _.replace(item?.trackCondition, /([a-zA-Z])(\d)/, "$1 $2")
                )
              : null,
            weather: this.capitalizeString(item?.weather),
            // adShortCode: item?.shortCode,
            modalSelectedRace: item?.RaceId,
            weekRacePreview: item?.weekRacePreview,
            // title: `${item?.FeaturedRaceCalender?.raceName} - ${moment(date)
            //   .tz(timezone)
            //   .format("YYYY")}`,
            title: item?.title,
            raceDetailTitle: item?.raceDetailTitle,
            runnerByRunnerTitle: item?.runnerByRunnerTitle,
            tipsTitle: item?.selectedTipTitle,
            raceResultTitle: item?.resultTitle,
          },
          tipsModalDetails: data?.result,
        });
      } else {
        this.setState({
          isLoading: false,
          tipsModalDetailsIsLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
        tipsModalDetailsIsLoading: false,
      });
    }
  };

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.sports + `?sportTypeId=1`
    );
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Races",
        value: 0,
      };
      let alldata = [alldatas, ...newdata];
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        modalSportsOption: newdata?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        // isLoading: false,
      });
    }
  }

  async fetchAllUser() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(`/expertTips/users`);
    if (status === 200) {
      let newdata = [];
      let sportData = data.users?.map((item) => {
        newdata.push({
          label: item?.firstName + " " + item?.lastName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All User",
        value: 0,
      };
      let alldata = [alldatas, ...newdata];
      this.setState({
        AllUserOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),

        // isLoading: false,
      });
    }
  }

  handlesportchange = (e) => {
    const {
      offset,
      sortDate,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      SelectedSport: e?.value,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "raceName"
        ? sortEventName
        : sortType === "startDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      0,
      sortDate,
      e?.value,
      serachValue,
      sortType,
      sortData,
      SelectedUsers
    );
  };

  handleUserchange = (e) => {
    const {
      offset,
      sortDate,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedSport,
    } = this.state;
    this.setState({
      SelectedUsers: e?.value,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "raceName"
        ? sortEventName
        : sortType === "startDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      0,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortData,
      e?.value
    );
  };

  handalValidate = () => {
    let {
      expertTipsValues,
      selectedModalSport,
      selectedValues,
      TrackAllRaceData,
      runnerCommentValues,
      errorStateCode,
      topSelection,
    } = this.state;
    let flag = true;
    if (
      expertTipsValues?.startDate === "" ||
      expertTipsValues?.startDate === null
    ) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    // if (selectedModalSport === null) {
    //   flag = false;
    //   this.setState({
    //     errorModalSport: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorModalSport: "",
    //   });
    // }

    if (expertTipsValues?.modalTrackId === null) {
      flag = false;
      this.setState({
        errorTrack: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTrack: "",
      });
    }
    if (Boolean(errorStateCode)) {
      flag = false;
    }

    if (!Boolean(expertTipsValues?.modalSelectedRace)) {
      flag = false;
      this.setState({
        errorRace: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRace: "",
      });
    }
    // if (expertTipsValues?.adShortCode?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorAdShortCode: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorAdShortCode: "",
    //   });
    // }
    if (expertTipsValues?.raceDetail?.trim() === "") {
      flag = false;
      this.setState({
        errorRaceDetail: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRaceDetail: "",
      });
    }
    if (expertTipsValues?.preview?.trim() === "") {
      flag = false;
      this.setState({
        errorPreview: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPreview: "",
      });
    }
    if (topSelection?.length == 0 || topSelection == null) {
      flag = false;
      this.setState({
        errorTopSelection: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTopSelection: "",
      });
    }

    const runnerCommentErrors = runnerCommentValues.map((value) =>
      value && value !== "<p><br></p>" ? "" : "This field is required"
    );

    this.setState({ runnerCommentErrors });

    // Check if any validation error exists for runner comments field
    if (runnerCommentErrors.some((error) => error !== "")) {
      flag = false;
    }
    // if (expertTipsValues?.title?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorTitle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorTitle: "",
    //   });
    // }
    // if (expertTipsValues?.raceDetailTitle?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorRaceDetailTitle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorRaceDetailTitle: "",
    //   });
    // }
    // if (expertTipsValues?.raceCourseUrl?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorRaceCourseUrl: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorRaceCourseUrl: "",
    //   });
    // }
    // if (this.state?.isEditMode) {
    //   if (Boolean(this.state.defaultUploadImage)) {
    //     this.setState({
    //       errorBannerImage: "",
    //     });
    //   } else if (this.state?.defaultImage?.length > 0) {
    //     this.setState({
    //       errorBannerImage: "",
    //     });
    //   } else {
    //     flag = false;
    //     this.setState({
    //       errorBannerImage: "This field is mandatory",
    //     });
    //   }
    // } else {
    //   if (this.state?.defaultImage?.length == 0) {
    //     flag = false;
    //     this.setState({
    //       errorBannerImage: "This field is mandatory",
    //     });
    //   } else {
    //     this.setState({
    //       errorBannerImage: "",
    //     });
    //   }
    // }
    // if (expertTipsValues?.previewTitle?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorPreviewTitle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorPreviewTitle: "",
    //   });
    // }
    // if (expertTipsValues?.runnerByRunnerTitle?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorRunnerByRunnerTitle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorRunnerByRunnerTitle: "",
    //   });
    // }
    // if (expertTipsValues?.tipsTitle?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorTipsTitle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorTipsTitle: "",
    //   });
    // }
    // if (expertTipsValues?.raceResultTitle?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorRaceResultTitle: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorRaceResultTitle: "",
    //   });
    // }
    // if (expertTipsValues?.weekRacePreview?.trim() === "") {
    //   flag = false;
    //   this.setState({
    //     errorWeekRacePreview: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorWeekRacePreview: "",
    //   });
    // }
    return flag;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      selectedModalSport: null,
      TrackData: [],
      calenderId: "",
      typr: "",
      topSelection: [],
      TrackRaceData: [],
      BetRunnerData: [],
      EachWayRunnerData: [],
      LayRunnerData: [],
      TrackAllRaceData: [],
      selectedValues: {},
      expertTipsValues: {
        startDate: moment(Date()).format("YYYY-MM-DD"),
        modalTrackId: null,
        raceDetail: "",
        preview: "",
        trackCondition: "",
        weather: "",
        adShortCode: "",
        modalSelectedRace: null,
      },
      errorStartDate: "",
      errorModalSport: "",
      errorTrack: "",
      errorAdShortCode: "",
      errorRaceDetail: "",
      errorPreview: "",
      errorTopSelection: "",
      errorTitle: "",
      errorRaceDetailTitle: "",
      errorPreviewTitle: "",
      errorRunnerByRunnerTitle: "",
      errorTipsTitle: "",
      errorRaceResultTitle: "",
      errorRaceCourseUrl: "",
      errorWeekRacePreview: "",
      errorBannerImage: "",
      errorStateCode: "",
      errorApi: "",
      defaultImage: [],
      defaultUploadImage: "",
      errorModalwpCategory: "",
      raceDetailContent: "",
      previewContent: "",
      weekRacePreviewContent: "",
      runnerCommentErrors: [],
    });
  };

  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files, errorBannerImage: "" });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    const { expertTipsValues } = this.state;
    if (type === "edit") {
      this.fetchSingleEvent(item?.id);
      this.fetchAllTrack(
        moment(item?.Race?.Event?.eventDate).tz(timezone).format("YYYY-MM-DD"),
        item?.calenderId,
        item
      );
      // this.fetchThisWeekRace(
      //   moment(item?.Race?.Event?.eventDate).tz(timezone).format("YYYY-MM-DD")
      // );
      const htmlRaceDetailString = he.decode(String(item?.raceDetails));
      if (typeof htmlRaceDetailString === "string") {
        this.setState({
          raceDetailContent: htmlRaceDetailString,
        });
      }
      const htmlPreviewString = he.decode(String(item?.preview));
      if (typeof htmlPreviewString === "string") {
        this.setState({
          previewContent: htmlPreviewString,
        });
      }
      const htmlOtherRaceString = he.decode(String(item?.weekRacePreview));
      if (typeof htmlOtherRaceString === "string") {
        this.setState({
          weekRacePreviewContent: htmlOtherRaceString,
        });
      }
      this.eventRaceList(item?.Race?.eventId, item?.calenderId, item?.RaceId);
      // this.bestBetRaceRunner(item?.RaceId, item?.FeaturedRaceRunners);
      const firstRunner = item?.FeaturedRaceRunners?.filter(
        (runner) => runner?.position == 1
      );
      const secondRunner = item?.FeaturedRaceRunners?.filter(
        (runner) => runner?.position == 2
      );
      const thirdRunner = item?.FeaturedRaceRunners?.filter(
        (runner) => runner?.position == 3
      );
      const fourthRunner = item?.FeaturedRaceRunners?.filter(
        (runner) => runner?.position == 4
      );

      this.setState({
        calenderId: item?.calenderId,
        SelectedId: item?.id,
        isEditMode: true,
        selectedModalSport: item?.Sport?.id,
        defaultUploadImage: item?.featuredImage,
        expertTipsValues: {
          ...expertTipsValues,
          startDate: moment(item?.Race?.Event?.eventDate)
            .tz(timezone)
            .format("YYYY-MM-DD"),
          modalTrackId: item?.calenderId,
          raceDetail: item?.raceDetails,
          preview: item?.preview,
          trackCondition: item?.trackCondition
            ? _.startCase(
                _.replace(item?.trackCondition, /([a-zA-Z])(\d)/, "$1 $2")
              )
            : null,
          weather: this.capitalizeString(item?.weather),
          // adShortCode: item?.shortCode,
          // modalSelectedRace: item?.RaceId,
          modalSelectedRace: item?.RaceId,
          title: item?.title,
          raceDetailTitle: item?.raceDetailTitle,
          previewTitle: item?.previewTitle,
          runnerByRunnerTitle: item?.runnerByRunnerTitle,
          tipsTitle: item?.selectedTipTitle,
          raceResultTitle: item?.resultTitle,
          // title: item?.title,
          // raceDetailTitle: item?.raceDetailTitle,
          // raceCourseUrl: item?.raceCourseUrl,
          // previewTitle: item?.previewTitle,
          // runnerByRunnerTitle: item?.runnerByRunnerTitle,
          // tipsTitle: item?.selectedTipTitle,
          // raceResultTitle: item?.resultTitle,
          weekRacePreview: item?.weekRacePreview,
          // first: firstRunner?.[0]?.RacingParticipantId,
          // second: secondRunner?.[0]?.RacingParticipantId,
          // third: thirdRunner?.[0]?.RacingParticipantId,
          // fourth: fourthRunner?.[0]?.RacingParticipantId,
          // firstMargin: firstRunner?.[0]?.margin,
          // secondMargin: secondRunner?.[0]?.margin,
          // thirdMargin: thirdRunner?.[0]?.margin,
          // fourthMargin: fourthRunner?.[0]?.margin,
        },
      });
    } else {
      this.fetchAllTrack(moment().tz(timezone).format("YYYY-MM-DD"));
      this.fetchThisWeekRace(moment().tz(timezone).format("YYYY-MM-DD"));
      this.setState({
        isEditMode: false,
        calenderId: "",
        type: "",
        topSelection: [],
        selectedModalSport: null,
        selectedValues: {},
        defaultUploadImage: "",
        raceDetailContent: "",
        previewContent: "",
        weekRacePreviewContent: "",
        errorStateCode: "",
        errorApi: "",
        expertTipsValues: {
          startDate: moment(Date()).format("YYYY-MM-DD"),
          modalTrackId: null,
          raceDetail: "",
          preview: "",
          trackCondition: "",
          weather: "",
          adShortCode: "",
          modalSelectedRace: null,
          title: "",
          raceDetailTitle: "",
          raceCourseUrl: "",
          previewTitle: "",
          runnerByRunnerTitle: "",
          tipsTitle: "",
          raceResultTitle: "",
          weekRacePreview: "",
          first: null,
          second: null,
          third: null,
          fourth: null,
          firstMargin: "",
          secondMargin: "",
          thirdMargin: "",
          fourthMargin: "",
        },
        runnerCommentErrors: [],
      });
    }
  };

  tipsDetailsModalOpen = (item) => {
    this.setState({ tipsModalDetailsOpen: true });
    this.fetchSingleEvent(item?.id);
    // this.bestBetRaceRunner(item?.RaceId, item?.FeaturedRaceRunners);
  };

  toggleTipsDetailsModalOpen = () => {
    this.setState({ tipsModalDetailsOpen: false, tipsModalDetails: {} });
  };

  setActionMessage = (display = false, type = "", message = "", isWperror) => {
    const clearMessageBox = () =>
      this.setState({
        messageBox: { display: false, type: "", message: "" },
      });
    this.setState(
      {
        messageBox: { display, type, message },
      },
      isWperror
        ? () => {}
        : () => {
            setTimeout(clearMessageBox, 3000);
          }
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "raceName"
        ? sortEventName
        : sortType === "startDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    try {
      const passApi = `/racingFeatured/featuredRace/${this.state.itemToDelete}`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
        });
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, ExpertTipsList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  clearStartDate = () => {
    const {
      offset,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      sortDate: null,
      startDateOpen: false,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "raceName"
        ? sortEventName
        : sortType === "startDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      offset,
      null,
      SelectedSport,
      serachValue,
      sortType,
      sortData,
      SelectedUsers
    );
  };
  capitalizeString = (str) => {
    // Check if the input is a string and not empty
    if (typeof str !== "string" || str.length === 0) {
      return "";
    }

    // Extract the first letter and convert to uppercase
    const firstLetter = str.charAt(0).toUpperCase();

    // Extract the rest of the string and convert to lowercase
    const restOfString = str.slice(1).toLowerCase();

    // Combine and return the capitalized string
    return firstLetter + restOfString;
  };
  handleSortStartDate = (date) => {
    const {
      offset,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "raceName"
        ? sortEventName
        : sortType === "startDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAllEvent(
        0,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        SelectedSport,
        serachValue,
        sortType,
        sortData,
        SelectedUsers
      );
    }
  };

  // handleModalsportchange = (e) => {
  //   const { expertTipsValues, errorModalSport } = this.state;
  //   this.setState({
  //     selectedModalSport: e.value,
  //     errorModalSport: e.value ? "" : errorModalSport,
  //     TrackData: [],
  //     TrackRaceData: [],
  //     BetRunnerData: [],
  //     errorStateCode: "",
  //     topSelection: [],
  //     expertTipsValues: {
  //       ...this.state.expertTipsValues,
  //       modalTrackId: null,
  //       modalSelectedRace: null,
  //       trackCondition: "",
  //       weather: "",
  //       adShortCode: "",
  //     },
  //   });
  //   // this.setState({
  //   //   selectedModalSportObj: e.value,
  //   // });
  //   this.fetchAllTrack(expertTipsValues?.startDate);
  // };

  async fetchAllTrack(date, selectedCalenderId, item) {
    const { expertTipsValues } = this.state;
    this.setState({ isTrackLoading: true });
    const passApi = `/racingFeatured/calendarDropDown?date=${date}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackLoading: false });
      let newdata = [];
      let Track = data?.result?.map((item) => {
        newdata.push({
          label: item?.trackName + " - " + item?.raceName,
          value: item?.calenderId,
          eventId: item?.EventId,
          trackId: item?.trackId,
          calenderId: item?.calenderId,
          raceName: item?.raceName,
          type: item?.type,
          trackName: item?.trackName,
          distance: item?.distance,
          priceMoney: item?.priceMoney,
          raceCourseUrl: item?.raceCourseUrl,
        });
      });

      let filterData = _.unionBy(this.state?.TrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        TrackData: finalData,
      });
    } else {
      this.setState({ isTrackLoading: false });
    }
  }
  fetchThisWeekRace = async (date) => {
    const passApi = `/racingFeatured/pastWeekFeaturedRace?date=${date}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({
        thisWeekRace: data?.result,
      });
    } else {
    }
  };
  async eventRaceList(eventId, calenderId, RaceId) {
    this.setState({
      isTrackRaceLoading: true,
      calenderId: calenderId,
    });
    const passApi = `/expertTips/getDropDown?eventId=${eventId}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackRaceLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label: "R" + item?.raceNumber + " " + item?.raceName,
          value: item?.id,
          trackCondition: item?.trackCondition
            ? _.startCase(
                _.replace(item?.trackCondition, /([a-zA-Z])(\d)/, "$1 $2")
              )
            : null,
          weather: item?.Weather?.weatherType,
        });
      });
      this.setState({
        TrackRaceData: newdata,
      });
      if (RaceId) {
        this.setState({
          expertTipsValues: {
            ...this.state.expertTipsValues,
            modalSelectedRace: RaceId,
          },
        });
      }
    } else {
      this.setState({ isTrackRaceLoading: false });
    }
  }

  async bestBetRaceRunner(RaceId, editRunnerData) {
    this.setState({ isBestBetRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isBestBetRaceRunnerLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        const isTipSelectedData = editRunnerData
          ? editRunnerData?.filter(
              (runner) => runner?.RacingParticipantId == item?.id
            )
          : [];

        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
          isTipSelected: isTipSelectedData?.[0]?.isTipSelected,
          tipOrder: isTipSelectedData?.[0]?.tipOrder
            ? isTipSelectedData?.[0]?.tipOrder
            : null,
        });
      });
      const topselectedTips = newdata
        ?.filter((runner) => runner?.isTipSelected == true)
        .sort((a, b) => a.tipOrder - b.tipOrder);
      //create mode
      const commentValue =
        newdata?.length > 0 ? Array(newdata?.length).fill("") : [];

      //edit mode
      let runnerCommentdata = [];
      const runnerComment = editRunnerData?.map((runner) =>
        runnerCommentdata?.push(runner?.comment ? runner?.comment : "")
      );
      const errorComments = editRunnerData?.map((val) =>
        val?.comment ? "" : "This field is required"
      );

      this.setState({
        BetRunnerData: newdata,
        runnerCommentValues: editRunnerData ? runnerCommentdata : commentValue,
        // runnerCommentErrors: editRunnerData ? errorComments : [],
        topSelection: topselectedTips,
      });
    } else {
      this.setState({ isBestBetRaceRunnerLoading: false });
    }
  }

  async eachWayRaceRunner(RaceId) {
    this.setState({ isEachWayRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=Asia/Calcutta`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isEachWayRaceRunnerLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      this.setState({
        EachWayRunnerData: newdata,
      });
    } else {
      this.setState({ isEachWayRaceRunnerLoading: false });
    }
  }

  async layRaceRunner(RaceId) {
    this.setState({ isLayRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=Asia/Calcutta`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isLayRaceRunnerLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      this.setState({
        LayRunnerData: newdata,
      });
    } else {
      this.setState({ isLayRaceRunnerLoading: false });
    }
  }

  setDefaultRaceDetails = (e) => {
    // const slugifyStr = slugify(e.raceName, {
    //   replacement: "-", // replace spaces with replacement character, defaults to `-`
    //   remove: /[*+~.()'"!:@]/g, // remove characters that match regex, defaults to `undefined`
    //   lower: true, // convert to lower case, defaults to `false`
    //   strict: false, // strip special characters except replacement, defaults to `false`
    //   locale: "en", // language code of the locale to use
    //   trim: true, // trim leading and trailing replacement chars, defaults to `true`
    // });

    // <p style="margin-bottom:20px">You can read more about the race history, lead-up races, and past winners on the  <a href="https://${
    //   config?.baseUrl.includes("staging") ||
    //   config?.baseUrl.includes("testing")
    //     ? "testing.backawinner.com.au"
    //     : "www.backawinner.com.au"
    // }/races/${slugifyStr}" target="._blank">${e.raceName}</a> page.</p>
    const detailContent = `
      <p style="margin-bottom:20px"><span>The ${e.raceName} is a ${
      e.type
    } race held at ${e.trackName} over ${
      e.distance
    } metres.</span> [Add in conditions] 
    The total prize money this year is ${formatter.format(e.priceMoney)}.</p>

      <p style="margin-bottom:20px">You can read more about the race history, lead-up races, and past winners on the  <a href=${
        e?.raceCourseUrl
      } target="._blank">${e.raceName}</a> page.</p>
    `;
    this.setState({
      raceDetailContent: detailContent,
    });
  };

  // async wpCategory() {
  //   this.setState({ wpCategoryLoading: true });
  //   const passApi = `/expertTips/getWpCategories`;
  //   const { status, data } = await axiosInstance.get(passApi);
  //   if (status === 200) {
  //     this.setState({ wpCategoryLoading: false });
  //     let newdata = [];
  //     let categoryId = data?.result?.map((item) => {
  //       newdata.push({
  //         label: item?.name,
  //         value: item?.id,
  //       });
  //     });
  //     this.setState({
  //       wpCategoryData: newdata,
  //     });
  //   } else {
  //     this.setState({ wpCategoryLoading: false });
  //   }
  // }

  handleSave = async (saveType, statusType) => {
    const {
      expertTipsValues,
      calenderId,
      selectedModalSport,
      TrackAllRaceData,
      selectedValues,
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      defaultImage,
      runnerCommentValues,
      BetRunnerData,
      topSelection,
      thisWeekRace,
    } = this.state;
    const isValidationChecked = saveType === 1 ? this.handalValidate() : true;
    if (isValidationChecked) {
      this.setState({ isLoading: true, isEditMode: false });
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "raceName"
          ? sortEventName
          : sortType === "startDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;

      const runnerdata = BetRunnerData?.map((runner, index) => {
        return {
          RacingParticipantId: runner?.value,
          comment: runnerCommentValues[index]
            ? runnerCommentValues[index]
            : null,
        };
      });
      let selectionData = [];
      const topTipsSelection = topSelection?.map((runner) =>
        selectionData?.push(runner?.value)
      );
      const raceResult = [
        {
          position: 1,
          RacingParticipantId: expertTipsValues?.first,
          margin: expertTipsValues?.firstMargin
            ? Number(expertTipsValues?.firstMargin)
            : null,
        },
        {
          position: 2,
          RacingParticipantId: expertTipsValues?.second,
          margin: expertTipsValues?.secondMargin
            ? Number(expertTipsValues?.secondMargin)
            : null,
        },
        {
          position: 3,
          RacingParticipantId: expertTipsValues?.third,
          margin: expertTipsValues?.thirdMargin
            ? Number(expertTipsValues?.thirdMargin)
            : null,
        },
        {
          position: 4,
          RacingParticipantId: expertTipsValues?.fourth,
          margin: expertTipsValues?.fourthMargin
            ? Number(expertTipsValues?.fourthMargin)
            : null,
        },
      ];

      const weekRace = thisWeekRace?.map((ele) => {
        return {
          featuredId: ele?.id,
          type: ele?.FeaturedRaceCalender?.type,
          raceName: ele?.FeaturedRaceCalender?.raceName,
          year: moment(expertTipsValues?.startDate).tz(timezone).format("YYYY"),
          raceCourse: ele?.FeaturedRaceCalender?.raceCourse,
          distance: ele?.FeaturedRaceCalender?.distance,
          bwPostUrl: ele?.bwPostUrl,
        };
      });
      let payload = {
        // SportId: selectedModalSport,
        RaceId: expertTipsValues?.modalSelectedRace,
        calenderId: calenderId,
        trackCondition: expertTipsValues?.trackCondition,
        weather: expertTipsValues?.weather,
        raceDetails: expertTipsValues?.raceDetail,
        preview: expertTipsValues?.preview,
        runners: runnerdata,
        tipSelected: selectionData,
        title: expertTipsValues?.title,
        // raceCourseUrl: expertTipsValues?.raceCourseUrl,
        raceDetailTitle: expertTipsValues?.raceDetailTitle,
        previewTitle: expertTipsValues?.previewTitle,
        runnerByRunnerTitle: expertTipsValues?.runnerByRunnerTitle,
        selectedTipTitle: expertTipsValues?.tipsTitle,
        resultTitle: expertTipsValues?.raceResultTitle,
        weekRacePreview: weekRace,
        status: statusType,
        // result: raceResult,
        // wpCategoryIds: expertTipsValues?.wpCategoryId?.join(","),
      };
      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            featuredImage: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      }
      try {
        const { status, data } = await axiosInstance.post(
          `racingFeatured/featuredRace?isPostTowp=${saveType}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
            raceDetailContent: "",
            previewContent: "",
            weekRacePreviewContent: "",
            errorApi: "",
          });
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
            raceDetailContent: "",
            previewContent: "",
            weekRacePreviewContent: "",
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          // isInputModalOpen: false,
          defaultImage: [],
          defaultUploadImage: "",
          errorApi: err?.response?.data?.message,
          // raceDetailContent: "",
          // previewContent: "",
          // weekRacePreviewContent: "",
        });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async (saveType, statusType) => {
    const {
      expertTipsValues,
      selectedModalSport,
      TrackAllRaceData,
      selectedValues,
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      defaultImage,
      runnerCommentValues,
      BetRunnerData,
      topSelection,
      calenderId,
      thisWeekRace,
    } = this.state;
    const isValidationChecked = saveType === 1 ? this.handalValidate() : true;
    if (isValidationChecked) {
      this.setState({ isLoading: true, isEditMode: true });

      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "raceName"
          ? sortEventName
          : sortType === "startDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;

      const runnerdata = BetRunnerData?.map((runner, index) => {
        return {
          RacingParticipantId: runner?.value,
          comment: runnerCommentValues[index]
            ? runnerCommentValues[index]
            : null,
        };
      });
      let selectionData = [];
      const topTipsSelection = topSelection?.map((runner) =>
        selectionData?.push(runner?.value)
      );
      const raceResult = [
        {
          position: 1,
          RacingParticipantId: expertTipsValues?.first,
          margin: expertTipsValues?.firstMargin
            ? Number(expertTipsValues?.firstMargin)
            : null,
        },
        {
          position: 2,
          RacingParticipantId: expertTipsValues?.second,
          margin: expertTipsValues?.secondMargin
            ? Number(expertTipsValues?.secondMargin)
            : null,
        },
        {
          position: 3,
          RacingParticipantId: expertTipsValues?.third,
          margin: expertTipsValues?.thirdMargin
            ? Number(expertTipsValues?.thirdMargin)
            : null,
        },
        {
          position: 4,
          RacingParticipantId: expertTipsValues?.fourth,
          margin: expertTipsValues?.fourthMargin
            ? Number(expertTipsValues?.fourthMargin)
            : null,
        },
      ];
      const weekRace = thisWeekRace?.map((ele) => {
        return {
          featuredId: ele?.id,
          type: ele?.FeaturedRaceCalender?.type,
          raceName: ele?.FeaturedRaceCalender?.raceName,
          year: moment(expertTipsValues?.startDate).tz(timezone).format("YYYY"),
          raceCourse: ele?.FeaturedRaceCalender?.raceCourse,
          distance: ele?.FeaturedRaceCalender?.distance,
          bwPostUrl: ele?.bwPostUrl,
        };
      });
      let payload = {
        // SportId: selectedModalSport,
        RaceId: expertTipsValues?.modalSelectedRace,
        calenderId: calenderId,
        trackCondition: expertTipsValues?.trackCondition,
        weather: expertTipsValues?.weather,
        // shortCode: expertTipsValues?.adShortCode,
        raceDetails: expertTipsValues?.raceDetail,
        preview: expertTipsValues?.preview,
        runners: runnerdata,
        tipSelected: selectionData,
        title: expertTipsValues?.title,
        // raceCourseUrl: expertTipsValues?.raceCourseUrl,
        raceDetailTitle: expertTipsValues?.raceDetailTitle,
        previewTitle: expertTipsValues?.previewTitle,
        runnerByRunnerTitle: expertTipsValues?.runnerByRunnerTitle,
        selectedTipTitle: expertTipsValues?.tipsTitle,
        resultTitle: expertTipsValues?.raceResultTitle,
        weekRacePreview: expertTipsValues?.weekRacePreview,
        status: statusType,
        // result: raceResult,
      };

      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            featuredImage: fileData?.image?.filePath,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          featuredImage:
            this.state.defaultUploadImage &&
            this.state.defaultUploadImage !== ""
              ? this.state.defaultUploadImage
              : null,
        };
      }
      try {
        const { status, data } = await axiosInstance.put(
          `/racingFeatured/featuredRace/${SelectedId}?isPostTowp=${saveType}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
            raceDetailContent: "",
            previewContent: "",
            weekRacePreviewContent: "",
            errorApi: "",
          });
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
            raceDetailContent: "",
            previewContent: "",
            weekRacePreviewContent: "",
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          // isInputModalOpen: false,
          defaultImage: [],
          defaultUploadImage: "",
          errorApi: err?.response?.data?.message,
          // raceDetailContent: "",
          // previewContent: "",
          // weekRacePreviewContent: "",
        });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleClearClick = () => {
    this.setState({ serachValue: "" });
  };

  sortLabelHandler = (type) => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortId,
        SelectedUsers
      );
      this.setState({
        sortId: !sortId,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "raceName") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortEventName,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: !sortEventName,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "startDate") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortEventDate,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: !sortEventDate,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "SportId") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortSportType,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: !sortSportType,
        sortUser: true,
        currentPage: 1,
      });
    } else {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortUser,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: !sortUser,
        currentPage: 1,
      });
    }
  };

  handleKeyDown = (event) => {
    var { sortDate, SelectedSport, serachValue, sortType, SelectedUsers } =
      this.state;
    if (event.key === "Enter") {
      this.fetchAllEvent(
        0,
        sortDate,
        SelectedSport,
        serachValue,
        sortType,
        false,
        SelectedUsers
      );
      this.setState({ currentPage: 1 });
    }
  };

  handleRaceDetailChange = (content) => {
    const { expertTipsValues, errorRaceDetail } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        raceDetail: content,
      },
      errorRaceDetail: content?.trim() == "" ? errorRaceDetail : "",
    });
  };
  handlePreviewChange = (content) => {
    const { expertTipsValues, errorPreview } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        preview: content,
      },
      errorPreview: content?.trim() == "" ? errorPreview : "",
    });
  };
  handleWeekRacePreviewChange = (content) => {
    const { expertTipsValues, errorWeekRacePreview } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        weekRacePreview: content,
      },
      errorWeekRacePreview: content?.trim() == "" ? errorWeekRacePreview : "",
    });
  };

  fetchRaceResult = (tipsModalDetails) => {
    const firstRunner = tipsModalDetails?.FeaturedRaceRunners?.filter(
      (runner) => runner?.position == 1
    )?.[0];
    const secondRunner = tipsModalDetails?.FeaturedRaceRunners?.filter(
      (runner) => runner?.position == 2
    )?.[0];
    const thirdRunner = tipsModalDetails?.FeaturedRaceRunners?.filter(
      (runner) => runner?.position == 3
    )?.[0];
    const fourthRunner = tipsModalDetails?.FeaturedRaceRunners?.filter(
      (runner) => runner?.position == 4
    )?.[0];
    return (
      <Table>
        <TableHead>
          <TableRow>
            <TableCell> Result</TableCell>
            <TableCell>Horse</TableCell>
            <TableCell>Margin</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell> 1st </TableCell>
            <TableCell>
              {firstRunner?.RacingParticipant?.runnerNumber +
                "." +
                " " +
                firstRunner?.RacingParticipant?.animal?.name +
                " " +
                "(" +
                firstRunner?.RacingParticipant?.barrierNumber +
                ")"}
            </TableCell>
            <TableCell></TableCell>
          </TableRow>
          <TableRow>
            <TableCell> 2nd </TableCell>
            <TableCell>
              {secondRunner?.RacingParticipant?.runnerNumber +
                "." +
                " " +
                secondRunner?.RacingParticipant?.animal?.name +
                " " +
                "(" +
                secondRunner?.RacingParticipant?.barrierNumber +
                ")"}
            </TableCell>
            <TableCell>{secondRunner?.margin + " " + "length"}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell> 3rd </TableCell>
            <TableCell>
              {thirdRunner?.RacingParticipant?.runnerNumber +
                "." +
                " " +
                thirdRunner?.RacingParticipant?.animal?.name +
                " " +
                "(" +
                thirdRunner?.RacingParticipant?.barrierNumber +
                ")"}
            </TableCell>
            <TableCell>{thirdRunner?.margin + " " + "length"}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell> $th </TableCell>
            <TableCell>
              {fourthRunner?.RacingParticipant?.runnerNumber +
                "." +
                " " +
                fourthRunner?.animal?.name +
                " " +
                "(" +
                fourthRunner?.barrierNumber +
                ")"}
            </TableCell>
            <TableCell>{fourthRunner?.margin + " " + "length"}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      ExpertTipsList,
      ExpertTipsCount,
      serachValue,
      AllSportsOption,
      SelectedSport,
      sortDate,
      startDateOpen,
      expertTipsValues,
      modalSportsOption,
      selectedModalSport,
      isTrackLoading,
      isTrackRaceLoading,
      isBestBetRaceRunnerLoading,
      isEachWayRaceRunnerLoading,
      isLayRaceRunnerLoading,
      TrackData,
      calenderId,
      type,
      topSelection,
      TrackRaceData,
      BetRunnerData,
      EachWayRunnerData,
      LayRunnerData,
      TrackAllRaceData,
      selectedValues,
      errorStartDate,
      errorModalSport,
      errorTrack,
      errorRaceDetail,
      errorPreview,
      errorRace,
      errorTopSelection,
      errorAdShortCode,
      errorTitle,
      errorRaceDetailTitle,
      errorPreviewTitle,
      errorRunnerByRunnerTitle,
      errorTipsTitle,
      errorRaceResultTitle,
      errorRaceCourseUrl,
      errorWeekRacePreview,
      errorBannerImage,
      tipsModalDetailsOpen,
      tipsModalDetails,
      tipsModalDetailsIsLoading,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      AllUserOption,
      defaultImage,
      defaultUploadImage,
      wpCategoryLoading,
      wpCategoryData,
      errorModalwpCategory,
      runnerCommentValues,
      runnerCommentErrors,
      raceDetailContent,
      previewContent,
      weekRacePreviewContent,
      errorStateCode,
      errorApi,
      thisWeekRace,
    } = this.state;
    const pageNumbers = [];
    if (ExpertTipsCount > 0) {
      for (let i = 1; i <= Math.ceil(ExpertTipsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    const user = fetchFromStorage(identifiers.user);
    return (
      <>
        {!isInputModalOpen ? (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              {/* <Paper className="pageWrapper"> */}
              {messageBox?.display && (
                <ActionMessage
                  message={messageBox?.message}
                  type={messageBox?.type}
                  styleClass={messageBox?.styleClass}
                />
              )}
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>
                  <Link underline="hover" color="inherit" to="#">
                    Back a winner
                  </Link>
                  <Typography className="active_p">
                    BAW Featured Race
                  </Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={3}>
                  <Typography variant="h1" align="left">
                    BAW Featured Race
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={9}
                  className="admin-filter-wrap admin-fixture-wrap"
                >
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      clearable
                      // onClear={() => this.clearStartDate()}
                      // open={startDateOpen}
                      // onOpen={() => this.setState({ startDateOpen: true })}
                      // onClose={() => this.setState({ startDateOpen: false })}
                      autoOk
                      // disableToolbar
                      // variant="inline"
                      format="dd/MM/yyyy"
                      placeholder="Start Date"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      value={
                        sortDate
                          ? typeof sortDate === "string"
                            ? parseISO(sortDate)
                            : sortDate
                          : null
                      }
                      slots={{
                        openPickerIcon: TodayIcon,
                      }}
                      slotProps={{
                        field: {
                          placeholder: "DD/MM/YYYY",
                          clearable: true,
                          // onClear: () => this.clearStartDate(),
                        },
                      }}
                      onChange={(e) => this.handleSortStartDate(e)}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      className="date-picker-fixture expert-tips-date-picker"
                      style={{ margin: "0px 10px 0px 0px", width: "24%" }}
                    />
                  </LocalizationProvider>
                  <Select
                    className="React teamsport-select external-select"
                    classNamePrefix="select"
                    placeholder="Select Race Type"
                    value={AllSportsOption?.find((item) => {
                      return item?.value == SelectedSport;
                    })}
                    //   isLoading={isLoading}
                    onChange={(e) => this.handlesportchange(e)}
                    options={AllSportsOption}
                  />
                  <TextField
                    placeholder="Search "
                    size="small"
                    variant="outlined"
                    className="event-search"
                    onKeyDown={(e) => this.handleKeyDown(e)}
                    value={serachValue}
                    onChange={(e) => {
                      this.setState({ serachValue: e.target.value });
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <img src={SearchIcons} alt="icon" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          {serachValue && (
                            <IconButton
                              onClick={() => this.handleClearClick()}
                              edge="end"
                              style={{ minWidth: "unset" }}
                              size="large"
                            >
                              <CancelIcon />
                            </IconButton>
                          )}
                        </InputAdornment>
                      ),
                    }}
                    style={{
                      background: "#ffffff",
                    }}
                  />
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455c7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                    }}
                    onClick={() => {
                      this.fetchAllEvent(
                        0,
                        sortDate,
                        SelectedSport,
                        serachValue,
                        sortType,
                        false,
                        SelectedUsers
                      );
                      this.setState({ currentPage: 1 });
                    }}
                  >
                    Search
                  </Button>

                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    className={
                      Boolean(this.state.errorWpCreds) ? "disabled-btn" : ""
                    }
                    onClick={this.inputModal(null, "create")}
                    disabled={Boolean(this.state.errorWpCreds)}
                  >
                    Add New
                  </Button>
                </Grid>
              </Grid>
              {user?.role !== "expertTip" ? (
                <Grid container direction="row" alignItems="center">
                  <Grid item xs={12} className="user-select-wrap cg-15">
                    {/* <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    onClick={() =>
                      this.props.navigate(`/tips-of-the-day`)
                    }
                  >
                    Select Tip Of The Day
                  </Button> */}
                    <Select
                      className="React teamsport-select external-select"
                      classNamePrefix="select"
                      placeholder="Select User"
                      value={AllUserOption?.find((item) => {
                        return item?.value == SelectedUsers;
                      })}
                      //   isLoading={isLoading}
                      onChange={(e) => this.handleUserchange(e)}
                      options={AllUserOption}
                    />
                  </Grid>
                </Grid>
              ) : (
                <></>
              )}

              {isLoading && <Loader />}
              {!isLoading && ExpertTipsList?.length === 0 && (
                <p>No Data Available</p>
              )}

              {!isLoading && ExpertTipsList?.length > 0 && (
                <TableContainer component={Paper}>
                  <Table
                    className="listTable market-error-table"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer" }}
                        >
                          DID
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortId
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("raceName")}
                          style={{ cursor: "pointer" }}
                        >
                          Race Name
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "raceName"
                                ? sortEventName
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("startDate")}
                          style={{ cursor: "pointer" }}
                        >
                          Race Date
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "startDate"
                                ? sortEventDate
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        {/* <TableCell
                        onClick={() => this.sortLabelHandler("SportId")}
                        style={{ cursor: "pointer" }}
                      >
                        Sport Type
                        <TableSortLabel
                          active={true}
                          direction={
                            sortType === "SportId"
                              ? sortSportType
                                ? "asc"
                                : "desc"
                              : "asc"
                          }
                        />
                      </TableCell> */}
                        <TableCell style={{ textTransform: "capitalize" }}>
                          BAW Status
                        </TableCell>
                        {user?.role !== "expertTip" ? (
                          <TableCell
                            onClick={() => this.sortLabelHandler("UserId")}
                            style={{ cursor: "pointer" }}
                          >
                            User
                            <TableSortLabel
                              active={true}
                              direction={
                                sortType === "UserId"
                                  ? sortUser
                                    ? "asc"
                                    : "desc"
                                  : "asc"
                              }
                            />
                          </TableCell>
                        ) : (
                          <></>
                        )}
                        <TableCell>Race Details</TableCell>
                        <TableCell style={{ width: "15%" }}>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {ExpertTipsList?.map((item) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={item?.id}
                          >
                            <TableCell>{item?.id} </TableCell>
                            <TableCell>{item?.Race?.raceName}</TableCell>
                            <TableCell>
                              {moment(item?.Race?.startDate).format(
                                "DD/MM/YYYY hh:mm:ss a"
                              )}
                            </TableCell>
                            {/* <TableCell>{item?.Sport?.sportName}</TableCell> */}
                            <TableCell>{item?.customStatus}</TableCell>
                            {user?.role !== "expertTip" ? (
                              <TableCell>
                                {item?.User?.firstName +
                                  " " +
                                  item?.User?.lastName}
                              </TableCell>
                            ) : (
                              <></>
                            )}
                            <TableCell>
                              <Button
                                className="table-btn"
                                variant="contained"
                                style={{
                                  fontSize: "14px",
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  fontWeight: "400",
                                  textTransform: "none",
                                  width: "max-content",
                                }}
                                onClick={() => {
                                  this.tipsDetailsModalOpen(item);
                                }}
                              >
                                Race Details
                              </Button>
                            </TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className={`table-btn edit-btn ${
                                  moment(item?.Race?.Event?.eventDate).isBefore(
                                    moment(),
                                    "day"
                                  ) || Boolean(this.state.errorWpCreds)
                                    ? "disabled-btn"
                                    : ""
                                }`}
                                disabled={
                                  moment(item?.Race?.Event?.eventDate).isBefore(
                                    moment(),
                                    "day"
                                  ) || Boolean(this.state.errorWpCreds)
                                }
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className={
                                  Boolean(this.state.errorWpCreds)
                                    ? "disabled-btn table-btn delete-btn"
                                    : "table-btn delete-btn"
                                }
                                disabled={Boolean(this.state.errorWpCreds)}
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                ExpertTipsCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              {/* </Paper> */}

              <ShowModal
                isModalOpen={isModalOpen}
                onClose={this.toggleModal}
                Content="Are you sure you want to delete?"
                onOkayLabel="Yes"
                onOkay={this.deleteItem}
                onCancel={this.toggleModal}
              />
            </Grid>
          </Grid>
        ) : (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>
                  <Link underline="hover" color="inherit" to="#">
                    Back a winner
                  </Link>
                  <Typography className="active_p">
                    BAW Featured Race
                  </Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={4}>
                  <Typography variant="h1" align="left">
                    {!isEditMode
                      ? "Create New Featured Race"
                      : "Edit Featured Race"}
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={8}
                  style={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    onClick={this.toggleInputModal}
                  >
                    Back
                  </Button>
                </Grid>
              </Grid>
              <Box className="expert-tips-modal">
                <Grid container>
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={4}
                      className="date-time-picker-wrap"
                      style={{ marginBottom: "15px" }}
                    >
                      <label className="modal-label">
                        {" "}
                        Select Date <span className="color-red">*</span>
                      </label>
                      <LocalizationProvider dateAdapter={AdapterDateFns}>
                        <DesktopDatePicker
                          variant="inline"
                          inputVariant="outlined"
                          ampm={false}
                          value={
                            expertTipsValues?.startDate
                              ? parseISO(
                                  moment(expertTipsValues?.startDate)
                                    .tz(timezone)
                                    .format("YYYY-MM-DD")
                                )
                              : null
                          }
                          onChange={(e) => {
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                startDate: moment(e)
                                  .tz(timezone)
                                  .format("YYYY-MM-DD"),
                                modalTrackId: null,
                                trackCondition: "",
                                weather: "",
                                adShortCode: "",
                                modalSelectedRace: null,
                                title: "",
                                raceDetailTitle: "",
                                previewTitle: "",
                                runnerByRunnerTitle: "",
                                tipsTitle: "",
                                raceResultTitle: "",
                              },
                              type: "",
                              topSelection: [],
                              errorStateCode: "",
                              selectedModalSport: null,
                              TrackData: [],
                              calenderId: "",
                              TrackRaceData: [],
                              BetRunnerData: [],
                            });
                            this.fetchAllTrack(
                              moment(e).tz(timezone).format("YYYY-MM-DD")
                            );
                            this.fetchThisWeekRace(
                              moment(e).tz(timezone).format("YYYY-MM-DD")
                            );
                          }}
                          autoOk={true}
                          format="yyyy/MM/dd"
                          className="date-time-picker"
                          disablePast={!isEditMode}
                        />
                      </LocalizationProvider>
                      {errorStartDate ? (
                        <p
                          className="errorText"
                          style={{ margin: "4px 0 0 0" }}
                        >
                          {errorStartDate}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid item xs={4} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        {" "}
                        Featured Race <span className="color-red">*</span>
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Featured Race"
                        // isDisabled={!selectedModalSport}
                        isLoading={isTrackLoading}
                        value={
                          expertTipsValues?.modalTrackId &&
                          TrackData?.find((item) => {
                            return (
                              item?.value == expertTipsValues?.modalTrackId
                            );
                          })
                        }
                        options={TrackData}
                        onChange={(e) => {
                          this.setState({
                            type: `${e?.type}`,
                            topSelection: [],
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalTrackId: e?.value,
                              modalSelectedRace: null,
                              trackCondition: "",
                              weather: "",
                              title: `${e?.raceName} - ${moment(
                                expertTipsValues?.startDate
                              )
                                .tz(timezone)
                                .format("YYYY")}`,
                              raceDetailTitle: `${moment(
                                expertTipsValues?.startDate
                              )
                                .tz(timezone)
                                .format("YYYY")} ${e?.raceName} Details`,
                              raceCourseUrl: "",
                              previewTitle: `${e?.raceName} ${moment(
                                expertTipsValues?.startDate
                              )
                                .tz(timezone)
                                .format("YYYY")} Preview`,
                              runnerByRunnerTitle: `${moment(
                                expertTipsValues?.startDate
                              )
                                .tz(timezone)
                                .format("YYYY")} ${
                                e?.raceName
                              } Runner by Runner`,
                              tipsTitle: `${e?.raceName} ${moment(
                                expertTipsValues?.startDate
                              )
                                .tz(timezone)
                                .format("YYYY")} Tips`,
                              raceResultTitle: `${moment(
                                expertTipsValues?.startDate
                              )
                                .tz(timezone)
                                .format("YYYY")} ${e?.raceName} Results`,
                            },
                            TrackRaceData: [],
                            BetRunnerData: [],
                            errorTrack: e?.value ? "" : errorTrack,
                          });
                          this.setDefaultRaceDetails(e);
                          this.eventRaceList(e?.eventId, e?.calenderId);
                          this.handleStateCodeError(e?.trackId);
                        }}
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorTrack ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorTrack}
                        </p>
                      ) : (
                        ""
                      )}
                      {errorStateCode ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorStateCode}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid item xs={4} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Select Race
                        <span className="color-red">*</span>
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Select Race"
                        isDisabled={!expertTipsValues?.modalTrackId}
                        isLoading={isTrackRaceLoading}
                        value={
                          expertTipsValues?.modalSelectedRace &&
                          TrackRaceData?.find((item) => {
                            return (
                              item?.value == expertTipsValues?.modalSelectedRace
                            );
                          })
                        }
                        options={TrackRaceData}
                        onChange={(e) => {
                          const selectedRace = TrackRaceData?.filter(
                            (race) => race?.value == e?.value
                          );
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalSelectedRace: e?.value,
                              trackCondition: selectedRace?.[0]?.trackCondition,
                              weather: this.capitalizeString(
                                selectedRace?.[0]?.weather
                              ),
                              first: null,
                              second: null,
                              third: null,
                              fourth: null,
                              firstMargin: "",
                              secondMargin: "",
                              thirdMargin: "",
                              fourthMargin: "",
                            },
                            topSelection: [],
                            errorRace: e?.value ? "" : errorRace,
                            BetRunnerData: [],
                          });
                          this.bestBetRaceRunner(e?.value);
                        }}
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorRace ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorRace}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  {!tipsModalDetailsIsLoading && (
                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={4}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text mb-8"
                      >
                        <label className="modal-label">
                          Title
                          {/* <span className="color-red">*</span> */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          color="primary"
                          size="small"
                          // multiline
                          // maxRows={3}
                          // rows={3}
                          placeholder="Title"
                          value={expertTipsValues?.title}
                          disabled
                          // onChange={(e) =>
                          //   this.setState({
                          //     expertTipsValues: {
                          //       ...expertTipsValues,
                          //       title: e?.target?.value,
                          //     },
                          //     errorTitle: e?.target?.value ? "" : errorTitle,
                          //   })
                          // }
                        />
                        {/* {errorTitle ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorTitle}
                      </p>
                    ) : (
                      ""
                    )} */}
                      </Grid>
                      <Grid
                        item
                        xs={4}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text mb-8"
                      >
                        <label className="modal-label">
                          {" "}
                          Race Type
                          {/* <span className="color-red">*</span> */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          color="primary"
                          size="small"
                          // multiline
                          // maxRows={3}
                          // rows={3}
                          placeholder="Title"
                          disabled
                          value={type}
                          // onChange={(e) =>
                          //   this.setState({
                          //     type: e?.target?.value,
                          //   })
                          // }
                        />
                      </Grid>
                      <Grid
                        item
                        xs={4}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text mb-8"
                      >
                        <label className="modal-label">
                          Race Detail Title
                          {/* <span className="color-red">*</span> */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          color="primary"
                          size="small"
                          // multiline
                          // maxRows={3}
                          // rows={3}
                          disabled
                          placeholder="Race Detail Title"
                          value={expertTipsValues?.raceDetailTitle}
                          // onChange={(e) =>
                          //   this.setState({
                          //     expertTipsValues: {
                          //       ...expertTipsValues,
                          //       raceDetailTitle: e?.target?.value,
                          //     },
                          //     errorRaceDetailTitle: e?.target?.value
                          //       ? ""
                          //       : errorRaceDetailTitle,
                          //   })
                          // }
                        />
                        {/* {errorRaceDetailTitle ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorRaceDetailTitle}
                      </p>
                    ) : (
                      ""
                    )} */}
                      </Grid>
                    </Box>
                  )}
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={4}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text mb-8"
                    >
                      <label className="modal-label"> Track condition</label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        type="textarea"
                        color="primary"
                        size="small"
                        placeholder="Track condition"
                        value={expertTipsValues?.trackCondition}
                        disabled={!Boolean(expertTipsValues?.modalSelectedRace)}
                        onChange={(e) =>
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              trackCondition: e?.target?.value,
                            },
                          })
                        }
                      />
                    </Grid>
                    <Grid
                      item
                      xs={4}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text mb-8"
                    >
                      <label className="modal-label"> Weather condition</label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        type="textarea"
                        color="primary"
                        size="small"
                        placeholder="Weather condition"
                        disabled={!Boolean(expertTipsValues?.modalSelectedRace)}
                        value={expertTipsValues?.weather}
                        onChange={(e) =>
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              weather: e?.target?.value,
                            },
                          })
                        }
                      />
                    </Grid>
                    {/* <Grid
                      item
                      xs={4}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text mb-8"
                    >
                      <label className="modal-label">
                        Race Course URL
                        <span className="color-red">*</span>
                      </label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        type="textarea"
                        color="primary"
                        size="small"
                        // multiline
                        // maxRows={3}
                        // rows={3}
                        placeholder="Race Course URL"
                        value={expertTipsValues?.raceCourseUrl}
                        onChange={(e) =>
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              raceCourseUrl: e?.target?.value,
                            },
                            errorRaceCourseUrl: e?.target?.value
                              ? ""
                              : errorRaceCourseUrl,
                          })
                        }
                      />
                      {errorRaceCourseUrl ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorRaceCourseUrl}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid> */}
                  </Box>
                  {/* <Box className="race-runner-wrap mb-8">
                  <Grid
                    item
                    xs={12}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text mb-8"
                  >
                    <label className="modal-label">
                      Ads Short Code
                      <span className="color-red">*</span>
                    </label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="textarea"
                      color="primary"
                      size="small"
                      multiline
                      maxRows={3}
                      rows={3}
                      placeholder="Ads Short Code"
                      value={expertTipsValues?.adShortCode}
                      onChange={(e) =>
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            adShortCode: e?.target?.value,
                          },
                          errorAdShortCode: e?.target?.value
                            ? ""
                            : errorAdShortCode,
                        })
                      }
                    />
                    {errorAdShortCode ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorAdShortCode}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                </Box> */}
                  {/* <div
                  className="blog-file-upload"
                  style={{ width: "100%", marginTop: "0px" }}
                >
                  <label className="modal-label"> Banner Image </label>
                  <FileUploader
                    onDrop={(image) =>
                      this.handleFileUpload("defaultImage", image)
                    }
                    style={{ marginTop: "5px" }}
                  />
                  <Box
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <div className="logocontainer">
                      {defaultImage?.length > 0
                        ? defaultImage?.map((file, index) => (
                            <img
                              className="auto-width"
                              key={index}
                              src={file.preview}
                              alt="player"
                            />
                          ))
                        : defaultUploadImage &&
                          defaultUploadImage !== "" && (
                            <img
                              className="auto-width"
                              src={
                                defaultUploadImage?.includes("uploads")
                                  ? config.baseUrl + defaultUploadImage
                                  : defaultUploadImage
                              }
                              alt="player"
                            />
                          )}
                    </div>
                    {(defaultImage?.length > 0 ||
                      (defaultUploadImage && defaultUploadImage !== "")) && (
                      <Box className="delete-icon-wrap">
                        <DeleteIcon
                          className="delete-icon"
                          onClick={() => this.handleFeatureLogoRemove()}
                          style={{ cursor: "pointer" }}
                        />
                      </Box>
                    )}
                  </Box>
                  {errorBannerImage ? (
                    <p
                      className="errorText"
                      style={{ margin: "-14px 0 0 0" }}
                    >
                      {errorBannerImage}
                    </p>
                  ) : (
                    ""
                  )}
                </div> */}
                  <Grid
                    item
                    xs={12}
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      textAlign: "left",
                    }}
                    className="teamsport-text mb-8"
                  >
                    <label className="modal-label">
                      {" "}
                      Race Details <span className="color-red">*</span>
                    </label>

                    <div className="featured-race-editor">
                      <SunEditor
                        onChange={this.handleRaceDetailChange}
                        setContents={raceDetailContent}
                        setOptions={{
                          buttonList: [
                            ["undo", "redo"],
                            ["font", "fontSize", "formatBlock"],
                            [
                              "bold",
                              "underline",
                              "italic",
                              "strike",
                              "subscript",
                              "superscript",
                            ],
                            ["removeFormat"],
                            ["outdent", "indent"],
                            ["align", "horizontalRule", "list", "table"],
                            ["link"],
                            ["fullScreen", "showBlocks", "codeView"],
                          ],
                          defaultStyle: "font-size: 16px;",
                        }}
                      />
                    </div>
                    {errorRaceDetail ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorRaceDetail}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                  {!tipsModalDetailsIsLoading && (
                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Preview Title
                          {/* <span className="color-red"> *</span>{" "} */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          // multiline
                          // maxRows={2}
                          // rows={3}
                          color="primary"
                          size="small"
                          placeholder="Preview Title"
                          disabled
                          value={expertTipsValues?.previewTitle}
                          // onChange={(e) =>
                          //   this.setState({
                          //     expertTipsValues: {
                          //       ...expertTipsValues,
                          //       previewTitle: e?.target?.value,
                          //     },
                          //     errorPreviewTitle: e?.target?.value
                          //       ? ""
                          //       : errorPreviewTitle,
                          //   })
                          // }
                        />
                        {/* {errorPreviewTitle ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorPreviewTitle}
                      </p>
                    ) : (
                      ""
                    )} */}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      ></Grid>
                    </Box>
                  )}
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        textAlign: "left",
                      }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">
                        {" "}
                        Preview
                        <span className="color-red"> *</span>{" "}
                      </label>
                      {/* <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="textarea"
                      multiline
                      maxRows={2}
                      rows={3}
                      color="primary"
                      size="small"
                      placeholder="Preview"
                      value={expertTipsValues?.preview}
                      onChange={(e) =>
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            preview: e?.target?.value,
                          },
                          errorPreview: e?.target?.value ? "" : errorPreview,
                        })
                      }
                    /> */}
                      <div className="featured-race-editor">
                        <SunEditor
                          onChange={this.handlePreviewChange}
                          setContents={previewContent}
                          setOptions={{
                            buttonList: [
                              ["undo", "redo"],
                              ["font", "fontSize", "formatBlock"],
                              [
                                "bold",
                                "underline",
                                "italic",
                                "strike",
                                "subscript",
                                "superscript",
                              ],
                              ["removeFormat"],
                              ["outdent", "indent"],
                              ["align", "horizontalRule", "list", "table"],
                              ["link"],
                              ["fullScreen", "showBlocks", "codeView"],
                            ],
                            defaultStyle: "font-size: 16px;",
                          }}
                        />
                      </div>
                      {errorPreview ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorPreview}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  {!tipsModalDetailsIsLoading && (
                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Runner by Runner Title
                          {/* <span className="color-red"> *</span>{" "} */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          // multiline
                          // maxRows={2}
                          // rows={3}
                          color="primary"
                          size="small"
                          placeholder="Runner by Runner Title"
                          disabled
                          value={expertTipsValues?.runnerByRunnerTitle}
                          // onChange={(e) =>
                          //   this.setState({
                          //     expertTipsValues: {
                          //       ...expertTipsValues,
                          //       runnerByRunnerTitle: e?.target?.value,
                          //     },
                          //     errorRunnerByRunnerTitle: e?.target?.value
                          //       ? ""
                          //       : errorRunnerByRunnerTitle,
                          //   })
                          // }
                        />
                        {/* {errorRunnerByRunnerTitle ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorRunnerByRunnerTitle}
                      </p>
                    ) : (
                      ""
                    )} */}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      ></Grid>
                    </Box>
                  )}
                  <Box className="race-all-runner ">
                    {BetRunnerData?.map((runner, index) => {
                      return (
                        <>
                          <Grid
                            item
                            xs={12}
                            style={{ marginBottom: "15px" }}
                            className="teamsport-text"
                          >
                            <>
                              <label className="modal-label">
                                {runner?.label}
                                <span className="color-red">*</span>
                              </label>
                              {/* <TextField
                                className="teamsport-textfield"
                                variant="outlined"
                                type="textarea"
                                multiline
                                maxRows={2}
                                color="primary"
                                size="small"
                                placeholder="Runner Comments"
                                value={runnerCommentValues?.[index]}
                                // Handle input change and update state
                                onChange={(e) =>
                                  this.handleRunnerInputChange(
                                    index,
                                    e?.target?.value
                                  )
                                }
                                // error={!!runnerCommentErrors[index]}
                                helperText={runnerCommentErrors[index]}
                              /> */}
                              <div className="featured-race-editor runner-comments-editor">
                                <SunEditor
                                  onChange={(e) =>
                                    this.handleRunnerInputChange(index, e)
                                  }
                                  setContents={runnerCommentValues?.[index]}
                                  setOptions={{
                                    buttonList: [
                                      ["undo", "redo"],

                                      ["bold", "underline", "italic", "strike"],
                                      ["removeFormat"],

                                      ["link"],
                                      ["fullScreen", "codeView"],
                                    ],
                                    defaultStyle: "font-size: 16px;",
                                  }}
                                />
                                {runnerCommentErrors[index] ? (
                                  <p
                                    className="errorText"
                                    style={{ margin: "0px 0 0 0" }}
                                  >
                                    {runnerCommentErrors[index]}
                                  </p>
                                ) : (
                                  ""
                                )}
                              </div>
                              {/* {renderError(race)} */}
                            </>
                          </Grid>
                        </>
                      );
                    })}
                  </Box>

                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        textAlign: "left",
                      }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">
                        {" "}
                        Previews for other races this week{" "}
                        {/* <span className="color-red">*</span> */}
                      </label>
                      <Box
                        className="preview-race-wrap"
                        style={{
                          display: "grid",
                          gridTemplateColumns: "auto auto",
                          margin: "10px 0px",
                        }}
                      >
                        {!isEditMode
                          ? thisWeekRace?.map((item) => {
                              return (
                                <>
                                  <span>
                                    {item?.FeaturedRaceCalender?.type +
                                      " " +
                                      item?.FeaturedRaceCalender?.raceName +
                                      " " +
                                      moment(expertTipsValues?.startDate)
                                        .tz(timezone)
                                        .format("YYYY") +
                                      " " +
                                      "(" +
                                      item?.FeaturedRaceCalender?.raceCourse +
                                      " " +
                                      item?.FeaturedRaceCalender?.distance +
                                      "m" +
                                      ")"}
                                  </span>
                                </>
                              );
                            })
                          : expertTipsValues?.weekRacePreview?.map((item) => {
                              return (
                                <>
                                  <span>
                                    {item?.type +
                                      " " +
                                      item?.raceName +
                                      " " +
                                      item?.year +
                                      " " +
                                      "(" +
                                      item?.raceCourse +
                                      " " +
                                      item?.distance +
                                      "m" +
                                      ")"}
                                  </span>
                                </>
                              );
                            })}
                      </Box>
                      {/* <div className="featured-race-editor">
                      <SunEditor
                        onChange={this.handleWeekRacePreviewChange}
                        setContents={weekRacePreviewContent}
                        setOptions={{
                          buttonList: [
                            ["undo", "redo"],
                            ["font", "fontSize", "formatBlock"],
                            [
                              "bold",
                              "underline",
                              "italic",
                              "strike",
                              "subscript",
                              "superscript",
                            ],
                            ["removeFormat"],
                            ["outdent", "indent"],
                            ["align", "horizontalRule", "list", "table"],
                            ["link"],
                            ["fullScreen", "showBlocks", "codeView"],
                          ],
                          defaultStyle: 'font-size: 16px;'
                        }}
                      />
                    </div>
                    {errorWeekRacePreview ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorWeekRacePreview}
                      </p>
                    ) : (
                      ""
                    )} */}
                    </Grid>
                  </Box>
                  {!tipsModalDetailsIsLoading && (
                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Selected Tips Title
                          {/* <span className="color-red"> *</span>{" "} */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          // multiline
                          // maxRows={2}
                          // rows={3}
                          color="primary"
                          size="small"
                          placeholder="Selected Tips Title"
                          value={expertTipsValues?.tipsTitle}
                          disabled
                          // onChange={(e) =>
                          //   this.setState({
                          //     expertTipsValues: {
                          //       ...expertTipsValues,
                          //       tipsTitle: e?.target?.value,
                          //     },
                          //     errorTipsTitle: e?.target?.value
                          //       ? ""
                          //       : errorTipsTitle,
                          //   })
                          // }
                        />
                        {/* {errorTipsTitle ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorTipsTitle}
                      </p>
                    ) : (
                      ""
                    )} */}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      ></Grid>
                    </Box>
                  )}
                  <Box className="race-runner-wrap mb-8">
                    <Grid item xs={12} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Tip Selection
                        <span className="color-red">*</span>
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select tipselection-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Tip Selection"
                        isMulti
                        value={topSelection}
                        options={BetRunnerData}
                        isDisabled={
                          !Boolean(expertTipsValues?.modalSelectedRace)
                        }
                        onChange={(e) => {
                          this.setState({
                            topSelection: e,

                            errorTopSelection:
                              e && e?.length > 0 ? "" : errorTopSelection,
                          });
                        }}
                        isOptionDisabled={(option) => {
                          // selectedValues[race?.raceId]?.includes(
                          //   option.value
                          // ) ||
                          return (topSelection?.length || 0) >= 4;
                        }}
                      />
                      {errorTopSelection ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorTopSelection}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  {!tipsModalDetailsIsLoading && (
                    <Box className="race-runner-wrap mb-8">
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Race Result Title
                          {/* <span className="color-red"> *</span>{" "} */}
                        </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          // multiline
                          // maxRows={2}
                          // rows={3}
                          color="primary"
                          size="small"
                          placeholder="Race Result Title"
                          disabled
                          value={expertTipsValues?.raceResultTitle}
                          // onChange={(e) =>
                          //   this.setState({
                          //     expertTipsValues: {
                          //       ...expertTipsValues,
                          //       raceResultTitle: e?.target?.value,
                          //     },
                          //     errorRaceResultTitle: e?.target?.value
                          //       ? ""
                          //       : errorRaceResultTitle,
                          //   })
                          // }
                        />
                        {/* {errorRaceResultTitle ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorRaceResultTitle}
                      </p>
                    ) : (
                      ""
                    )} */}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      ></Grid>
                    </Box>
                  )}
                  {/* <Box className="race-runner-wrap mb-8">
                  <Grid item xs={6} style={{ marginBottom: "15px" }}>
                    <label className="modal-label">1st Runner</label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      placeholder="Select 1st Runner"
                      isDisabled={
                        !Boolean(expertTipsValues?.modalSelectedRace)
                      }
                      value={
                        expertTipsValues?.first &&
                        BetRunnerData?.find((item) => {
                          return item?.value == expertTipsValues?.first;
                        })
                      }
                      options={BetRunnerData}
                      onChange={(e) => {
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            first: e?.value,
                            second: null,
                            third: null,
                            fourth: null,
                          },
                        });
                      }}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={6}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text"
                  >
                    <label className="modal-label"> 1st Runner Margin</label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="number"
                      // multiline
                      // maxRows={2}
                      // rows={3}
                      color="primary"
                      size="small"
                      placeholder="1st Runner Margin"
                      value={expertTipsValues?.firstMargin}
                      onChange={(e) =>
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            firstMargin: e?.target?.value,
                          },
                        })
                      }
                    />
                  </Grid>
                </Box>
                <Box className="race-runner-wrap mb-8">
                  <Grid item xs={6} style={{ marginBottom: "15px" }}>
                    <label className="modal-label">2nd Runner</label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      placeholder="Select 2nd Runner"
                      isDisabled={
                        !Boolean(expertTipsValues?.modalSelectedRace) ||
                        !Boolean(expertTipsValues?.first)
                      }
                      value={
                        expertTipsValues?.second &&
                        BetRunnerData?.find((item) => {
                          return item?.value == expertTipsValues?.second;
                        })
                      }
                      options={BetRunnerData?.filter(
                        (runner) => runner?.value !== expertTipsValues?.first
                      )}
                      onChange={(e) => {
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            second: e?.value,
                            third: null,
                            fourth: null,
                          },
                        });
                      }}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={6}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text"
                  >
                    <label className="modal-label"> 2nd Runner Margin</label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="number"
                      // multiline
                      // maxRows={2}
                      // rows={3}
                      color="primary"
                      size="small"
                      placeholder="2nd Runner Margin"
                      value={expertTipsValues?.secondMargin}
                      onChange={(e) =>
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            secondMargin: e?.target?.value,
                          },
                        })
                      }
                    />
                  </Grid>
                </Box>
                <Box className="race-runner-wrap mb-8">
                  <Grid item xs={6} style={{ marginBottom: "15px" }}>
                    <label className="modal-label">3rd Runner</label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      placeholder="Select 3rd Runner"
                      isDisabled={
                        !Boolean(expertTipsValues?.modalSelectedRace) ||
                        !Boolean(expertTipsValues?.second)
                      }
                      value={
                        expertTipsValues?.third &&
                        BetRunnerData?.find((item) => {
                          return item?.value == expertTipsValues?.third;
                        })
                      }
                      options={BetRunnerData?.filter(
                        (runner) =>
                          runner?.value !== expertTipsValues?.first &&
                          runner?.value !== expertTipsValues?.second
                      )}
                      onChange={(e) => {
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            third: e?.value,
                            fourth: null,
                          },
                        });
                      }}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={6}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text"
                  >
                    <label className="modal-label"> 3rd Runner Margin</label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="number"
                      // multiline
                      // maxRows={2}
                      // rows={3}
                      color="primary"
                      size="small"
                      placeholder="3rd Runner Margin"
                      value={expertTipsValues?.thirdMargin}
                      onChange={(e) =>
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            thirdMargin: e?.target?.value,
                          },
                        })
                      }
                    />
                  </Grid>
                </Box>
                <Box className="race-runner-wrap mb-8">
                  <Grid item xs={6} style={{ marginBottom: "15px" }}>
                    <label className="modal-label">4th Runner</label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      placeholder="Select 4th Runner"
                      isDisabled={
                        !Boolean(expertTipsValues?.modalSelectedRace) ||
                        !Boolean(expertTipsValues?.third)
                      }
                      value={
                        expertTipsValues?.fourth &&
                        BetRunnerData?.find((item) => {
                          return item?.value == expertTipsValues?.fourth;
                        })
                      }
                      options={BetRunnerData?.filter(
                        (runner) =>
                          runner?.value !== expertTipsValues?.first &&
                          runner?.value !== expertTipsValues?.second &&
                          runner?.value !== expertTipsValues?.third
                      )}
                      onChange={(e) => {
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            fourth: e?.value,
                          },
                        });
                      }}
                    />
                  </Grid>
                  <Grid
                    item
                    xs={6}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text"
                  >
                    <label className="modal-label"> 4th Runner Margin</label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="number"
                      // multiline
                      // maxRows={2}
                      // rows={3}
                      color="primary"
                      size="small"
                      placeholder="4th Runner Margin"
                      value={expertTipsValues?.fourthMargin}
                      onChange={(e) =>
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            fourthMargin: e?.target?.value,
                          },
                        })
                      }
                    />
                  </Grid>
                </Box> */}
                </Grid>

                <Grid container>
                  <Grid item xs={2}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      {!isEditMode ? (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleSave(0, "draft")}
                          color="primary"
                          value={
                            !isLoading ? "Save as SmartB draft" : "Loading..."
                          }
                          disabled={isLoading}
                        />
                      ) : (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleUpdate(0, "draft")}
                          color="secondary"
                          value={
                            !isLoading ? "Save as SmartB draft" : "Loading..."
                          }
                          disabled={isLoading}
                        />
                      )}
                      {!isEditMode ? (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleSave(1, "draft")}
                          color="primary"
                          value={
                            !isLoading ? "Save As BAW Draft" : "Loading..."
                          }
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      ) : (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleUpdate(1, "draft")}
                          color="secondary"
                          value={
                            !isLoading ? "Save As BAW Draft" : "Loading..."
                          }
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      )}

                      {!isEditMode ? (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleSave(1, "published")}
                          color="primary"
                          value={!isLoading ? "Publish on BAW" : "Loading..."}
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      ) : (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleUpdate(1, "published")}
                          color="secondary"
                          value={!isLoading ? "Publish on BAW" : "Loading..."}
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      )}

                      <ButtonComponent
                        onClick={this.toggleInputModal}
                        className="mr-lr-30 back-btn"
                        value="Back"
                      />
                    </div>
                    {errorApi ? (
                      <p
                        className="errorText"
                        style={{ margin: "4px 0px 4px" }}
                      >
                        {errorApi}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        )}
        <Modal
          className="modal modal-input tips-modal-details"
          open={tipsModalDetailsOpen}
          onClose={this.toggleTipsDetailsModalOpen}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">Race Details</h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleTipsDetailsModalOpen}
            />
            {tipsModalDetailsIsLoading ? (
              <Box className="modal-loader">
                <Loader />
              </Box>
            ) : (
              <Box className="tips-details">
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">Title :</Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.title}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Featured Race :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.Race?.track?.name}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">Race :</Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.Race?.raceName}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Track Condition :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.trackCondition
                      ? tipsModalDetails?.trackCondition
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">Weather :</Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.weather
                      ? tipsModalDetails?.weather
                      : "-"}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Race Course URL :
                  </Typography>
                  <Typography
                    className="details-para"
                    style={{
                      wordBreak: "break-word",
                      textDecoration: "underline",
                      color: "blue",
                    }}
                  >
                    {tipsModalDetails?.raceCourseUrl && (
                      <a href={tipsModalDetails?.raceCourseUrl} target="_blank">
                        {tipsModalDetails?.raceCourseUrl}
                      </a>
                    )}
                  </Typography>
                </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Race Detail :
                  </Typography>
                  <Typography
                    className="details-para"
                    dangerouslySetInnerHTML={{
                      __html: tipsModalDetails?.raceDetails,
                    }}
                  >
                    {/* {tipsModalDetails?.raceDetails
                    ? tipsModalDetails?.raceDetails
                    : "-"} */}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">Preview :</Typography>
                  <Typography
                    className="details-para"
                    dangerouslySetInnerHTML={{
                      __html: tipsModalDetails?.preview,
                    }}
                  >
                    {/* {tipsModalDetails?.preview
                    ? tipsModalDetails?.preview
                    : "-"} */}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Tip's Selection :
                  </Typography>
                  <Typography className="details-para">
                    {topSelection?.map((runner) => {
                      return <Box>{runner.label}</Box>;
                    })}
                  </Typography>
                </Box>
                <h3 className="text-center modal-head">Runner Details</h3>
                {BetRunnerData?.map((item, index) => {
                  return (
                    <>
                      <Box
                        className="d-flex align-item-baseline col-35 mb-18 details"
                        key={index}
                      >
                        <Typography className="detsils-header">
                          {item?.label}:
                        </Typography>
                        <Box className="w-60">
                          <Typography
                            className="details-para"
                            dangerouslySetInnerHTML={{
                              __html: runnerCommentValues?.[index]
                                ? runnerCommentValues?.[index]
                                : "",
                            }}
                          ></Typography>
                        </Box>
                      </Box>
                    </>
                  );
                })}
                {tipsModalDetails?.isResultDeclared == true && (
                  <>
                    <h3 className="text-center modal-head">Result Details</h3>
                    {this.fetchRaceResult(tipsModalDetails)}
                  </>
                )}
              </Box>
            )}
            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    onClick={this.toggleTipsDetailsModalOpen}
                    // className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
      </>
    );
  }
}
export default BAWFeaturedRace;
