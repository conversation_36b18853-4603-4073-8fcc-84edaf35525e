import React, { Component } from "react";
import { config } from "../../helpers/config";
import { URLS } from "../../library/common/constants";
import axiosInstance from "../../helpers/Axios";

class ImageUploader extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedImage: null,
    };
  }

  handleImageChange = (e) => {
    const file = e.target.files[0];
    this.setState({ selectedImage: file });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };
  handleImageUpload = async () => {
    try {
      this.props.handeModleLoading(true);
      let fileData = await this.setMedia(this.state.selectedImage);
      if (fileData?.success) {
        this.props.handeModleLoading(false);
        if (fileData) {
          const imageUrl = config.mediaUrl + fileData?.image?.filePath;
          if (this.props.onImageUpload(imageUrl)) {
            this.props.onImageUpload(imageUrl);
          }
        }
      } else {
        this.props.handeModleLoading(false);
      }
    } catch (err) {
      this.props.handeModleLoading(false);
    }
    // Perform any necessary image upload logic here, for example, using a file upload API.
    // After the upload is successful, get the image URL and call this.props.onImageUpload with it.
    // const imageUrl = ...; // URL of the uploaded image
    // this.props.onImageUpload(imageUrl);
  };

  render() {
    return (
      <div style={{ paddingTop: "10px" }}>
        <input type="file" accept="image/*" onChange={this.handleImageChange} />
        <button onClick={this.handleImageUpload}>Upload</button>
      </div>
    );
  }
}

export default ImageUploader;
