import React, { Component } from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  TextField,
  Box,
  Button,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import "./editRunnerExtraInfo.scss";
import ButtonComponent from "../../../library/common/components/Button";
import axiosInstance from "../../../helpers/Axios";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import DateFnsUtils from "@date-io/date-fns";
import moment from "moment";
import { Loader } from "../../../library/common/components";

class EditPreviousRunnerInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalPreviousModalOpen: false,
      runnerId: "",
      previousData: {},
      finish: "",
      numberOfRunners: "",
      track: "",
      distance: "",
      Jockey: "",
      weight: "",
      margin: "",
      startingPrice: "",
      condition: "",
      winner: "",
      second: "",
      third: "",
      classes: "",
      prizeMoney: "",
      time: "",
      isLoading: false,
      selectedDate: null,
    };
  }

  inputModal = (id, data) => {
    let newDate = data?.date;
    let date1 = newDate.split("/");
    let runnerDate = `${date1[2]}-${date1[1]}-${date1[0]}`;

    this.setState({
      isModalPreviousModalOpen: true,
      runnerId: id,
      previousData: data,
      finish: data?.finish,
      numberOfRunners: data?.number_of_runners,
      track: data?.track,
      distance: data?.distance,
      Jockey: data?.jockey,
      weight: data?.weight_carried,
      margin: data?.margin,
      startingPrice: data?.starting_price,
      condition: "Heavy",
      winner: data?.winner,
      second: data?.second,
      third: data?.third,
      classes: data?.classes,
      prizeMoney: data?.prize_money,
      selectedDate: runnerDate,
      time: data?.time,
    });
  };
  toggleRunnerPreviousInfoModal = () => {
    this.setState({ isModalPreviousModalOpen: false });
  };
  handleDateChange = (date) => {
    let selectedDates = moment(new Date(date)).format("YYYY-MM-DD");
    // let payloadDate = moment(selectedDates).format("DD-MM-yyyy");
    this.setState({ selectedDate: selectedDates });
  };
  handleSave = async () => {
    const { runnerExtraInfoData, runnerInfoData, newData } = this.props;
    const {
      previousData,
      classes,
      distance,
      winner,
      weight,
      track,
      startingPrice,
      second,
      prizeMoney,
      numberOfRunners,
      margin,
      finish,
      third,
      selectedDate,
      time,
    } = this.state;
    let data = newData?.filter((item) => {
      return this.state?.runnerId !== item?.id;
    });

    let data1 = [
      {
        barrier: previousData?.barrier,
        classes: classes,
        date: moment(selectedDate).format("DD/MM/YYYY"),
        distance: distance,
        finish: finish,
        id: previousData?.id,
        in_run: previousData?.in_run,
        margin: margin,
        number: previousData?.number,
        number_of_runners: numberOfRunners,
        prize_money: prizeMoney,
        second: second,
        starting_price: startingPrice,
        time: time,
        time_ran: previousData?.time_ran,
        track: track,
        track_condition: previousData?.track_condition,
        weight_carried: weight,
        winner: winner,
        third: third ? third : "",
      },
    ];
    const previousInfoData = [...data, ...data1];
    const previousRunsData = previousInfoData?.sort((a, b) => {
      return a?.id - b?.id;
    });
    const previousRun = previousRunsData?.map(function (obj) {
      delete obj?.id;
      return obj;
    });

    const info = {
      dam: runnerExtraInfoData?.dam,
      entrant_comment: runnerExtraInfoData?.entrant_comment,
      entrant_comment_alternative:
        runnerExtraInfoData?.entrant_comment_alternative,
      entrant_id: runnerExtraInfoData?.entrant_id,
      owners_info: runnerExtraInfoData?.owners_info,
      past_runner_performances: runnerExtraInfoData?.past_runner_performances,
      previous_runs: previousRun,
      runner_info: runnerExtraInfoData?.runner_info,
      sire: runnerExtraInfoData?.sire,
      trainer_info: runnerExtraInfoData?.trainer_info,
      speedmap: runnerExtraInfoData?.speedmap,
    };
    const newInfo = JSON.stringify(info);

    let payload = {
      RacingParticipantId: runnerInfoData?.RacingParticipantId,
      raceId: this.props?.runnerInfoData?.raceId,
      info: newInfo,
    };
    try {
      const method = "put";
      const url = `/events/runnerExtraInfo/${this.props?.id}`;
      const { status } = await axiosInstance[method](url, payload);
      if (status === 200) {
        this.setState({ isLoading: false });
        this.props.inputModal();
        this.props.fetchAllRunnersInfo();
      }
    } catch (err) { }
  };
  render() {
    const { newData } = this.props;
    const {
      isModalPreviousModalOpen,
      // runnerId,
      previousData,
      finish,
      numberOfRunners,
      track,
      distance,
      Jockey,
      weight,
      margin,
      startingPrice,
      condition,
      winner,
      second,
      third,
      classes,
      prizeMoney,
      isLoading,
      selectedDate,
      time,
    } = this.state;


    return (
      <>
        <Box>
          {this.props?.isLoading && (
            <Box style={{ textAlign: "center" }}>
              <Loader />
            </Box>
          )}
          {newData?.length > 0 ? (
            <TableContainer component={Paper}>
              <Table
                className="listTable api-provider-listTable"
                aria-label="simple table"
              >
                <TableHead className="tableHead-row">
                  <TableRow>
                    <TableCell>id</TableCell>
                    <TableCell>place</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell style={{ minWidth: "70px" }}>Track</TableCell>
                    <TableCell>Distance</TableCell>
                    <TableCell>Jockey</TableCell>
                    <TableCell>Weight</TableCell>
                    <TableCell>Margin</TableCell>
                    <TableCell>SP</TableCell>
                    <TableCell>Condition</TableCell>
                    <TableCell style={{ minWidth: "335px" }}>Result</TableCell>
                    <TableCell>CLass</TableCell>
                    <TableCell>Race Time</TableCell>
                    <TableCell>Prize Money</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody className="table_body">
                  <TableRow className="table_row">
                    <TableCell
                      colSpan={100}
                      className="table-seprator"
                    ></TableCell>
                  </TableRow>

                  {newData?.map((runner) => {
                    return (
                      <TableRow>
                        <TableCell>{runner?.id}</TableCell>
                        <TableCell>
                          {" "}
                          <span>
                            {runner?.finish && runner?.number_of_runners
                              ? runner?.finish + "/" + runner?.number_of_runners
                              : "-"}
                          </span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>{runner?.date ? runner?.date : "-"}</span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>{runner?.track ? runner?.track : "-"}</span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>
                            {runner?.distance ? runner?.distance + "m" : "-"}
                          </span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>{runner?.jockey ? runner?.jockey : "-"}</span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>
                            {runner?.weight_carried
                              ? runner?.weight_carried
                              : "-"}
                          </span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>
                            {runner?.margin ? runner?.margin + "L" : "-"}
                          </span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>
                            {runner?.margin
                              ? "$" + runner?.starting_price
                              : "-"}
                          </span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          <span>Heavy</span>
                        </TableCell>
                        <TableCell>
                          {runner?.winner ? (
                            <span>
                              {"1."} {runner?.winner}
                            </span>
                          ) : (
                            ""
                          )}
                          {runner?.second ? (
                            <span className="disable">
                              {"2."} {runner?.second}
                            </span>
                          ) : (
                            ""
                          )}
                          {runner?.third ? (
                            <span className="disable">
                              {"3."} {runner?.third}
                            </span>
                          ) : (
                            ""
                          )}
                        </TableCell>
                        <TableCell>
                          <span>{runner?.classes ? runner?.classes : "-"}</span>
                        </TableCell>
                        <TableCell>
                          {" "}
                          {runner?.time ? runner?.time : "-"}
                        </TableCell>
                        <TableCell>
                          {" "}
                          {runner?.prize_money
                            ? "$" + runner?.prize_money
                            : "-"}
                        </TableCell>
                        <TableCell>
                          <Button
                            style={{ cursor: "pointer" }}
                            onClick={() => this.inputModal(runner?.id, runner)}
                            className="edit-btn"
                          >
                            Edit
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <TableCell
              align="center"
              style={{
                textAlign: "center",
              }}
              colSpan={100}
            >
              No Data Available
            </TableCell>
          )}
        </Box>
        <Modal
          className="modal modal-input"
          open={isModalPreviousModalOpen}
          onClose={this.toggleRunnerPreviousInfoModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">Edit Runners Previous Info</h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleRunnerPreviousInfoModal}
            />
            <Box className="runnerInfo-contanier">
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Finish</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Finish"
                    value={finish}
                    onChange={(e) => this.setState({ finish: e.target.value })}
                  />
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Number Of Runners</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Number Of Runners"
                    value={numberOfRunners}
                    onChange={(e) =>
                      this.setState({ numberOfRunners: e.target.value })
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Track</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Track"
                    value={track}
                    onChange={(e) => this.setState({ track: e.target.value })}
                  />
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Distance</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Distance"
                    value={distance}
                    onChange={(e) =>
                      this.setState({ distance: e.target.value })
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Jockey</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Jockey"
                    value={Jockey}
                    onChange={(e) => this.setState({ Jockey: e.target.value })}
                  />
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Weight</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Weight"
                    value={weight}
                    onChange={(e) => this.setState({ weight: e.target.value })}
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Margin</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Margin"
                    value={margin}
                    onChange={(e) => this.setState({ margin: e.target.value })}
                  />
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Starting Price</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Starting Price"
                    value={startingPrice}
                    onChange={(e) =>
                      this.setState({ startingPrice: e.target.value })
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Condition</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Condition"
                    value={condition}
                    onChange={(e) =>
                      this.setState({ condition: e.target.value })
                    }
                  />
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Winner</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Winner"
                    value={winner}
                    onChange={(e) => this.setState({ winner: e.target.value })}
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Second</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Second"
                    value={second}
                    onChange={(e) => this.setState({ second: e.target.value })}
                  />
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Third</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Third"
                    value={third}
                    onChange={(e) => this.setState({ third: e.target.value })}
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Classes</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Classes"
                    value={classes}
                    onChange={(e) => this.setState({ classes: e.target.value })}
                  />
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Prize Money</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Prize Money"
                    value={prizeMoney}
                    onChange={(e) =>
                      this.setState({ prizeMoney: e.target.value })
                    }
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} className="runnerInfo">
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Date</label>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      disableToolbar
                      variant="inline"
                      format="dd/MM/yyyy"
                      placeholder="DD/MM/YYYY"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      // value={"2022-03-22"}
                      value={selectedDate ? parseISO(selectedDate) : null}
                      onChange={this.handleDateChange}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      autoOk
                      className="details-runner-picker"
                    />
                  </LocalizationProvider>
                </Grid>
                <Grid item xs={6} className="runnerInfo-text">
                  <label className="modal-label">Time</label>
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    placeholder="Time"
                    value={time}
                    onChange={(e) => this.setState({ time: e.target.value })}
                  />
                </Grid>
              </Grid>
              <Grid container>
                <Grid item xs={3}>
                  <div style={{ margin: "20px 0px", display: "flex" }}>
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />

                    <ButtonComponent
                      onClick={this.props?.inputModal}
                      className="mr-lr-30"
                      value="Back"
                      style={{ minWidth: "auto" }}
                    />
                  </div>
                </Grid>
              </Grid>
            </Box>
          </div>
        </Modal>
      </>
    );
  }
}

export default EditPreviousRunnerInfo;
