<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>Maintenance Page</title>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .center-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: left;
      }

      .player-img {
        margin-bottom: 20px;
      }

      h1 {
        color: #fff;
        font-size: 61.47px;
        font-family: "Veneer Clean Soft";
        line-height: 86.05px;
        font-weight: 400;
      }

      @media (max-width: 1820px) {
        h1 {
          font-size: 55px;
        }
      }
      @media (max-width: 1600px) {
        h1 {
          font-size: 45px;
        }
      }
      @media (max-width: 1360px) {
        h1 {
          font-size: 40px;
        }
      }
      @media (max-width: 420px) {
        h1 {
          font-size: 31.36px;
        }
      }

      .players-box {
        display: flex;
        justify-content: center;
        align-items: center;
        }

        @media (max-width: 1240px) {
          .players-box{
            display: block;
            text-align: center;
          }
        }

        .players-img{
          max-width: 70%;
          height: 100vh;
        }

        @media (max-width: 1240px) {
          .players-img{
              position: absolute;
              /* height: 700px; */
              width: 440px;
              max-width: unset;
              height:unset;
              bottom: 0;
              left: 30%;
          }
        }
        @media (max-width: 860px) {
          .players-img{
             /* position: absolute;
            width: 650px;
            max-width: unset;
          height:unset;
          bottom: 0; */
            left: 20%;
          }
        }
        @media (max-width: 690px) {
          .players-img{
            position: absolute;
            /* height: 650px; */
            width: 400px;
            /* object-fit: cover; */
            bottom: 0;
            left: 12%;
          }
        }
        @media (max-width: 620px) {
          .players-img{
            position: absolute;
            /* height: 600px; */
            width: 380px;
            bottom: 0;
            left: 10%;
          }
        }
        @media (max-width: 490px) {
          .players-img{
            position: absolute;
            /* height: 360px; */
            width: 360px;
            bottom: 0;
            left: 10%;
          }
        }
        @media (max-width: 440px) {
          .players-img{
            position: absolute;
            /* height: 500px; */
            width: 325px;
            bottom: 0;
            left: 2%;
          }
        }
        @media (max-width: 390px) {
          .players-img{
            position: absolute;
            /* height: 580px; */
            width: 320px;
            bottom: 0;
            left: 0 ;
          }
        }

      p {
        font-weight: 300;
        color: #fff;
        font-size: 22.4px;
        font-family: "inter";
        line-height: 31.36px;
      }

      @media (max-width: 420px) {
        p {
          font-size: 16px;
        }
      }

      @media (max-width: 490px) {
        .txt-box{
          margin-top: 100px;
        }
      }
      @media (max-width: 400px) {
        .txt-box{
          margin-top: 70px;
        }
      }
      @media (max-width: 1120px) {
        .txt-box{
           margin-top: 60px;
           /* margin-top: 140px; */
            text-align: center;
            width: 100%;
        }
      }

      .maintenance-banner-box {
        background: linear-gradient(
          5deg,
          rgba(4, 17, 29, 1) 0%,
          rgba(2, 29, 51, 1) 59%
        );
        background-repeat: repeat;
        background-position: top center;
        background-size: auto;
        overflow: hidden;
        height: 100vh;
      }

      .maintenance-banner-box .image-container {
        background-image: url("https://i.postimg.cc/yNHdSmnC/Group-29312.png");
         background-repeat: no-repeat;
          background-position: top center;
          background-size: cover;
          overflow: hidden;
        height: 100vh;
      }

      .body .maintenance-banner-box .image-container .content-container {
        /* max-width: 1454px;
        width: 100%;
        margin: 0 auto;
        display: flex;
        justify-content: end;
        align-items: center; */
      }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box {
        /* position: relative; */
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box {
        /* position: absolute; */
        left: -48%;
        top: 40%;
      }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .title {
        font-family: "Veneer Clean Soft" !important;
        line-height: 86.05px !important;
        font-size: 61.47px !important;
        color: #fff !important;
      }

      @media (max-width: 1360px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .title {
          font-size: 34px;
        }
      }

      @media (max-width: 1120px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .title {
          font-size: 31.36px;
        }
              
            }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .des {
        margin-top: 23px !important;
        font-family: "inter" !important;
        font-size: 22.4px !important;
        color: #fff !important;
      }
/* width: 415px;
    height: 350px; */
      @media (max-width: 1120px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .txt-box
        .des {
          font-size: 16px;
        }
              
            }

      .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {
        /* position: absolute; */
        height: 100vh;
          max-width: 70%;
        max-height: 100%;
        /* bottom: 0; */
      }

      @media (max-width: 1580px) {
        .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {margin-left: -10%}
            
          }
          @media (max-width: 1440px) {
            .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {margin-left: -10%}
          }
          @media (max-width: 1120px) {
             .body
        .maintenance-banner-box
        .image-container
        .content-container
        .players-box
        .players-img {
          object-fit: cover;
            width: 500px;
            height: 700px;
            margin-top: -12%;
        }
            
          }

      @media (max-width: 1120px) {
        .body
          .maintenance-banner-box
          .image-container
          .content-container
          .players-box
          .players-box {
          flex-direction: column;
        }
      }
      
        .body
        .maintenance-banner-box
        .image-container
        .responsive-content-container {
        display: none;
      }
      .go-to-home-btn {
    margin-top: 18px;
    border: 1px solid #fff;
    border-radius: 8px;
    color: #fff;
    font-size: 14px;
    line-height: 16px;
    font-family: "inter";
    font-weight: 500;
    padding: 10px 20px;
    background-color: transparent;
    text-transform: uppercase;
    cursor: pointer;
  }
      /* @media (max-width: 1320px) {
    .body .maintenance-banner-box .image-container .responsive-content-container{
max-width: 1454px;
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    }
    
  } */
      /* 
  @media (max-width: 1500px) {
    .body .maintenance-banner-box .image-container .responsive-content-container {
      padding-top: 140px;
    }
  }

  @media (max-width: 910px) {
    .body .maintenance-banner-box .image-container .responsive-content-container {
      padding-top: 50px;
    }
  }

  @media (max-width: 390px) {
    .body .maintenance-banner-box .image-container .responsive-content-container {
      padding-top: 100px;
    }
  } */

      /* .body .maintenance-banner-box .image-container .responsive-content-container .flex-box {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .txt-box {
    display: none;
  }

  @media (max-width: 1320px) {
    .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .txt-box {
      display: block;
      margin-top: 50px;
      text-align: center;
    }
  }

   .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .txt-box .title {
    font-family: Arial, sans-serif;
    font-size: 31.36px;
    color: #fff;
    line-height: 20px;
  }

  .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .txt-box .des {
    margin-top: 13px;
    font-family: Arial, sans-serif;
    font-size: 22.4px;
    color: #fff;
    line-height: 31.36px;
  }

  .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .players-img-box {
    display: none;
  }

  @media (max-width: 1320px) {
    .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .players-img-box {
      display: block;
    }
  }

   .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .players-img-box .players-img {
    height: 100vh;
  }

  @media (max-width: 1140px) {
    .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .players-img-box .players-img {
      height: 81vh;
    }
  }

  @media (max-width: 860px) {
    .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .players-img-box {
      display: block;
      position: absolute;
      bottom: 0px;
    }
    .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .players-img-box .players-img {
      width: 350px;
      height: 335px;
    }
  }

  @media (max-width: 590px) {
    .body .maintenance-banner-box .image-container .responsive-content-container .flex-box .players-img-box .players-img {
      width: 300px;
      height: 285px;
    }
  } */
    </style>
  </head>

  <body>
    <script>
      if (performance.navigation.type === 1) {
          const prevPage = localStorage.getItem("previous_page");
        if (prevPage) {
          setTimeout(() => {
            window.location.href = prevPage;
            localStorage.removeItem("previous_page");
          }, 1000);
        }else {
          window.location.href = "/"
        } 
      }
  </script>
    <script>
      function handleRefresh() {
        const prevPage = localStorage.getItem("previous_page");
        if (prevPage) {
          setTimeout(() => {
            window.location.href = prevPage;
            localStorage.removeItem("previous_page");
          }, 1000);
        } else {
          window.location.href = "/"
        }
      }
  </script>
    <div class="maintenance-banner-box">
      <div class="image-container">
        <div class="content-container">
          <div class="players-box">
            <div class="txt-box">
              <h1 > We'll be back soon</p>
              <p>We're busy updating the SmartB experience.
              <br/>Please check back soon for the exciting new updates.</p>
              <Button
              variant="outlined"
              class="go-to-home-btn"
              onclick="handleRefresh()"
            >
              Refresh
            </Button>
            </div>
            <img
              class="players-img"
              src="https://i.postimg.cc/nhvVZsxR/players.png"
            />
          </div>
        </div>
        <!-- <div class="responsive-content-container">
          <div class="flex-box">
            <div class="txt-box">
              <h1 > We'll be back soon</p>
              <p >We're busy updating the SmartB experience. Please check back soon for the exciting new updates.</p>
            </div>
            <div class="players-img-box">
              <img
                class="players-img"
                src="https://i.postimg.cc/LstVzVpt/players-small.png"
              />
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </body>
</html>
