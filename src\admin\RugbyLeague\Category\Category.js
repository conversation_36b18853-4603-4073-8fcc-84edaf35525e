import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../rugbyleague.scss";
class Category extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      categoryValues: {
        categoryName: "",
        rapidId: "",
        id: "",
      },
      categorylist: [],
      categoryCount: 0,
      errorRequire: "",
    };
  }

  componentDidMount() {
    this.fetchAllCategory();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllCategory();
    }
  }

  async fetchAllCategory() {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `rls/category?limit=${rowPerPage}&offset=${offset}`
      );
      if (status === 200) {
        this.setState({
          categorylist: data?.result?.rows,
          isLoading: false,
          categoryCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }
  handalValidate = () => {
    let { categoryValues } = this.state;

    let flag = true;
    if (categoryValues?.categoryName === "") {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        name: this.state?.categoryValues?.categoryName,
        rapidId: this.state?.categoryValues?.rapidId,
        SportId: 12,
      };
      const { status } = await axiosInstance.post(`rls/category`, payload);
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllCategory();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      }
    }
  };

  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        name: this.state?.categoryValues?.categoryName,
        rapidId: this.state?.categoryValues?.rapidId,
        SportId: 12,
      };
      const { status } = await axiosInstance.put(
        `rls/category/${this.state.categoryValues?.id}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllCategory();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Edited Successfully`
        //   );
      }
    }
  };

  //   async fetchAllSportType() {
  //     const { status, data } = await axiosInstance.get(URLS.sportType);
  //     if (status === 200) {
  //       this.setState({ allSportsType: data.result });
  //     }
  //   }

  //   getSportType = (id) => {
  //     let { allSportsType } = this.state;
  //     let sportType = allSportsType
  //       .filter((obj) => obj.id === id)
  //       .map((object) => object.sportType);
  //     return sportType;
  //   };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        categoryValues: {
          categoryName: item?.name,
          rapidId: item?.rapidId,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        categoryValues: {
          categoryName: "",
          rapidId: "",
          id: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `rls/category/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllCategory();
        });
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, categorylist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      categoryValues,
      categorylist,
      categoryCount,
      errorRequire,
    } = this.state;
    const pageNumbers = [];
    // sportType !== "" &&
    //   (categorylist = categorylist.filter((obj) => obj.sportTypeId == sportType));

    // let currentPageRow = categorylist;

    // if (categorylist?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = categorylist.slice(indexOfFirstTodo, indexOfLastTodo);
    if (categoryCount > 0) {
      for (let i = 1; i <= Math.ceil(categoryCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {/* {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )} */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Rugby League
                </Link>
                <Typography className="active_p">Category</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  Category
                </Typography>
              </Grid>

              <Grid item xs={8} className="admin-filter-wrap">
                {/* <SelectBox
                  onChange={(e) => this.setState({ sportType: e.target.value })}
                  style={{ width: "60%" }}
                >
                  <option value="">Select Sport Type</option>
                  {allSportsType?.length > 0 &&
                    allSportsType?.map((obj, i) => (
                      <option key={i} value={obj?.id}>
                        {obj?.sportType}
                      </option>
                    ))}
                </SelectBox> */}
                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  // value={search}
                  onChange={(e) => {
                    // setSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  // onClick={() => fetchAnimal()}
                >
                  Search
                </Button> */}

                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && categorylist?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && categorylist?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell>DID</TableCell>
                      <TableCell style={{ width: "25%" }}>rapid Id</TableCell>
                      <TableCell style={{ width: "25%" }}>
                        Category Name
                      </TableCell>
                      {/* <TableCell style={{ width: "25%" }}>
                          variation
                        </TableCell> */}
                      {/* <TableCell style={{ width: "25%" }}>variation</TableCell> */}
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {categorylist?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.rapidId}</TableCell>
                          <TableCell>{item?.name}</TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn"
                            >
                              Edit
                            </Button>
                            <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                              className={
                                categorylist.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                categorylist.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              categoryCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                              className={
                                categorylist.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                categorylist.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Category" : "Edit Category"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="rugby-text"
                      >
                        <label className="modal-label"> Category Name</label>
                        <TextField
                          className="rugby-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Category Name"
                          value={categoryValues?.categoryName}
                          onChange={(e) =>
                            this.setState({
                              categoryValues: {
                                ...categoryValues,
                                categoryName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorRequire ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRequire}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="rugby-text"
                      >
                        <label className="modal-label"> rapidId</label>
                        <TextField
                          className="rugby-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Id"
                          value={categoryValues?.rapidId}
                          onChange={(e) =>
                            this.setState({
                              categoryValues: {
                                ...categoryValues,
                                rapidId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Category;
