.cricket-text {
  .cricket-textfield {
    width: 95%;
    margin-bottom: 15px;
    margin-top: 3px;
    input {
      border-radius: 8px;
    }

    .MuiOutlinedInput-root {
      border-radius: 8px;

      .MuiOutlinedInput-inputMarginDense {
        padding: 10.5px;
        background-color: #ffffff;
      }
      .MuiOutlinedInput-inputMultiline {
        padding: 10.5px;
      }
    }
  }
  .eventname {
    width: 97%;
  }
}
.cricket-select {
  width: 20%;
  .select__control {
    height: 100%;
    margin-top: 3px;
    border-radius: 8px;
  }
  .select__menu {
    text-align: left;
  }
}
.national-select {
  .cricket-select {
    width: 95%;
  }
  .campaign-select {
    width: 98%;
  }
}
.radio-wrap {
  .gender {
    flex-direction: row;
    .MuiButtonBase-root {
      min-width: auto;
    }
  }
}
.cricket-multiple-select {
  width: 97%;
}
.cricket-dob-wrap {
  .dob-picker {
    margin: 0px;
  }
}

.date-time-picker-wrap {
  .date-time-picker {
    width: 95%;
    input {
      background: white;
      padding: 10.5px 14px;
      border-radius: 8px;
    }
    fieldset {
      border-radius: 8px;
    }
  }
}
.eventname {
  width: 97%;
}
.event-Tournament-select {
  width: 95%;
}
.external-select {
  .select__control {
    margin-top: 0px;
  }
}
.MuiTableCell-root {
  border-bottom: none;
}
.MuiTableRow-root {
  border-bottom: 1px solid rgba(224, 224, 224, 1);
}
