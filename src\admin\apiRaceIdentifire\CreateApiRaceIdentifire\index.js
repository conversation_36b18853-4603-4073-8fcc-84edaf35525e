import React, { createRef } from "react";
import { Grid } from "@mui/material";
import {
  apiRaceIdentifireFormModel,
  apiRaceIdentifireForFixtures,
} from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
import { setValidation } from "../../../helpers/common";

let apiRaceIdentifireFormModelArray = [];

class CreateApiRaceIdentifire extends React.Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      values: {
        raceId: "",
        apiRaceId: "",
        providerId: "",
      },
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
    };
  }

  componentDidMount() {
    apiRaceIdentifireFormModelArray = this.props.isFixture
      ? apiRaceIdentifireForFixtures
      : apiRaceIdentifireFormModel;
    if (this.props.isEditMode) {
      this.fetchCurrentRaceIdentifire(this.props.id);
    }

    const { allRaces, allApiProvider } = this.props;
    this.setState((prevState) => {
      return {
        values: {
          ...prevState.values,
          raceId: allRaces?.length > 0 ? allRaces[0].id : "",
          providerId: allApiProvider?.length > 0 ? allApiProvider[0].id : "",
        },
      };
    });
    apiRaceIdentifireFormModelArray = apiRaceIdentifireFormModelArray?.map(
      (fieldItem) => {
        if (fieldItem?.field === "raceId") {
          return {
            ...fieldItem,
            type: "dropdown",
            options: [
              ...allRaces?.map((tablecol, i) => {
                return {
                  id: i,
                  value: tablecol?.id,
                  label: tablecol?.raceName,
                };
              }),
            ],
          };
        } else if (fieldItem?.field === "providerId") {
          return {
            ...fieldItem,
            type: "dropdown",
            options: [
              ...allApiProvider?.map((tablecol, i) => {
                return {
                  id: i,
                  value: tablecol?.id,
                  label: tablecol?.providerName,
                };
              }),
            ],
          };
        }
        return fieldItem;
      }
    );
  }
  componentWillUnmount() {
    apiRaceIdentifireFormModelArray = apiRaceIdentifireFormModelArray.map(
      (fieldItem) => {
        return { ...fieldItem, errorMessage: "" };
      }
    );
  }

  fetchCurrentRaceIdentifire = async (id) => {
    const { status, data } = await axiosInstance.get(
      URLS.apiRaceIdentifire + `/${id}`
    );
    if (status === 200) {
      this.setState({
        values: data.result,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } });
  };

  validate = () => {
    let { apiRacetId, providerId } = this.state.values;
    let flag = true;

    if (apiRacetId === "" || providerId === "") {
      flag = false;
      // this.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      // this.setActionMessage(false);
    }

    return flag;
  };

  handleSave = async () => {
    const { isEditMode, isFixture, raceIdToSend } = this.props;
    this.setState({ isLoading: true });
    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode
        ? `${URLS.apiRaceIdentifire}/${this.props.id}`
        : URLS.apiRaceIdentifire;

      const values = removeErrorFieldsFromValues(form.formData);
      apiRaceIdentifireFormModelArray = apiRaceIdentifireFormModelArray?.map(
        (fieldItem) => {
          return setValidation(fieldItem, values);
        }
      );
      if (isFixture && !isEditMode) {
        values["raceId"] = raceIdToSend;
      }
      if (this.validate()) {
        const { status } = await axiosInstance[method](url, values);
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          this.props.fetchAllRaceIdentifire(this.props.raceIdToSend);
          this.setActionMessage(
            true,
            "Success",
            `Api Race Identifier ${
              isEditMode ? "Edited" : "Created"
            } Successfully`
          );
        }
      }
    } catch (err) {
      this.setState({ isLoading: false });
      this.setActionMessage(
        true,
        "Error",
        `An error occurred while ${
          isEditMode ? "editing" : "creating"
        } Api Race Identifier`
      );
    }
  };

  handleChange = (field, value) => {
    let values = { ...this.state.values, [field]: value };
    this.setState({ values: values });
    apiRaceIdentifireFormModelArray = apiRaceIdentifireFormModelArray?.map(
      (fieldItem) => {
        if (field === fieldItem?.field) {
          return setValidation(fieldItem, values);
        } else {
          return fieldItem;
        }
      }
    );
    this.setActionMessage(false);
  };

  render() {
    var { values, messageBox, isLoading } = this.state;
    var { isEditMode } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            {/* <Paper className="pageWrapper api-provider"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}

            <Form
              values={values}
              model={apiRaceIdentifireFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            />

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Back"
                  />
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateApiRaceIdentifire;
