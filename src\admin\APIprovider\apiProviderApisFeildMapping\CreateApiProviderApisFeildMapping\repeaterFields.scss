.w-50 {
  width: 50%;
  display: inline-block;
  vertical-align: top;
}
.repeater-wrap {
  display: block;
  clear: both;
  margin-bottom: 15px;
  .repeater-addButton {
    display: block;
    width: 180px;
    button {
      font-size: 14px;
      line-height: 20px;
      text-transform: normal;
    }
  }
  .repeater-item {
    position: relative;
    display: block;
    clear: both;
    padding: 35px 10px 10px;
    margin-bottom: 10px;
    border: 1px solid #333;
    label {
      display: block;
      // font-weight: 600;
    }
    .radio_input_wrap {
      label {
        display: inline-block;
      }
    }
  }
  .repeater-item-remove {
    position: absolute;
    right: 30px;
    top: 0;
    display: block;
    width: 22px;
    font-size: 16px;
  }
  .radio_input_wrap {
    display: inline-block;
    vertical-align: top;
  }
  .repeater-item-field {
    width: 50%;
    margin-bottom: 10px;
    display: inline-block;
    vertical-align: top;
    &.thirdwidth {
      width: 33%;
    }
    &.fullwidth {
      width: 100%;
    }
    .errorText {
      margin: -8px 0 0 0;
      font-size: 12px;
    }
  }
}
