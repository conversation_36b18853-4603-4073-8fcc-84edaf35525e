import React, { useEffect } from "react";
import PropTypes from "prop-types";
import { Link, useNavigate } from "react-router-dom";
import "./404.scss";

const NotFound = (props) => {
  // const history = useNavigate();
  // const handleNavigate = () => {
  //   if (props.loginuser) {
  //     history.push("/dashboard");
  //   } else {
  //     history.push("/login");
  //   }
  // };
  // useEffect(() => {
  //   handleNavigate();
  // }, []);
  return (
    <div className="error-page-content">
      <div>
        <>
          <p> 404 Error </p>
          <p> We couldn’t find what you are looking for ! </p>
        </>

        <Link
          className="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary"
          to={props.loginuser ? "/dashboard" : "/login"}
          style={{ textDecoration: "none" }}
        >
          {props.loginuser ? "Go To Dashboard" : "Go To Login"}
        </Link>
      </div>
    </div>
  );
};

NotFound.propTypes = {
  classes: PropTypes.object,
};

NotFound.defaultProps = {
  classes: {},
};

export default NotFound;
