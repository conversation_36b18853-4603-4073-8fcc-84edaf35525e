import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  Paper,
  Typography,
  TextField,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHead,
  Modal,
  Breadcrumbs,
  InputAdornment,
  IconButton,
} from "@mui/material";

import CancelIcon from "@mui/icons-material/Cancel";
import Pagination from "@mui/material/Pagination";
// import { ReactSVG } from "react-svg";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import "./jockeys.scss";
import CreatePlayers from "../../players/CreatePlayers";
import { Loader } from "../../../library/common/components";
// import { errorHandler } from "../../../library/utilities";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import { Link } from "react-router-dom";
import SearchIcons from "../../../images/searchIcon.svg";
import CreateVariation from "./createVariation";
import _ from "lodash";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import { faAddressBook } from "@fortawesome/free-solid-svg-icons";

const Index = (props) => {
  const [search, setSearch] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isInputModalOpen, setIsInputModalOpen] = useState(false);
  const [idToSend, setIdToSend] = useState(null);
  const [data, setData] = useState([]);
  // const [allPlayersType, setAllPlayersType] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [sportCount, setSportCount] = useState(null);
  const [rowPerPage] = useState(20); //setRowPerPage
  const [offset, setOffset] = useState(0);
  const [pageNumbers] = useState([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState("");
  const [isVariationModalOpen, setisVariationModalOpen] = useState(false);
  const [isId, setisId] = useState("");
  const [isName, setisName] = useState("");

  // Merge States
  const [isMergeModalOpen, setisMergeModalOpen] = useState(false);
  const [searchTrack, setsearchTrack] = useState([]);
  const [searchModalTrack, setsearchModalTrack] = useState([]);
  const [ModalTrackData, setModalTrackData] = useState([]);
  const [searchModalChildTrack, setsearchModalChildTrack] = useState([]);
  const [ModalChildTrackData, setModalChildTrackData] = useState([]);
  const [childTrack, setchildTrack] = useState([]);
  const [searchTrackCount, setsearchTrackCount] = useState(0);
  const [searchTrackPage, setsearchTrackPage] = useState(0);
  const [ModalTrackCount, setModalTrackCount] = useState(0);
  const [searchModalTrackCount, setsearchModalTrackCount] = useState(0);
  const [searchModalTrackPage, setsearchModalTrackPage] = useState(0);
  const [ModalTrackPage, setModalTrackPage] = useState(0);
  const [ModalChildTrackCount, setModalChildTrackCount] = useState(0);
  const [searchModalChildTrackCount, setsearchModalChildTrackCount] =
    useState(0);
  const [searchModalChildTrackPage, setsearchModalChildTrackPage] = useState(0);
  const [ModalChildTrackPage, setModalChildTrackPage] = useState(0);
  const [isTrackSearch, setisTrackSearch] = useState("");
  const [isModalTrackSearch, setisModalTrackSearch] = useState("");
  const [isModalChildTrackSearch, setisModalChildTrackSearch] = useState("");
  const [ModalChildTrackSearch, setModalChildTrackSearch] = useState("");
  const [ModalParentTrackSearch, setModalParentTrackSearch] = useState("");
  const [parentTrack, setparentTrack] = useState("");
  const [createError, setcreateError] = useState("");
  const [rowToPass, setrowToPass] = useState({});
  const [messageBox, setmessageBox] = useState({
    display: false,
    type: "",
    message: "",
  });
  // const [variationData, setvariationData] = useState([]);

  // const handlePaginationButtonClick = (navDirection) => {
  //   if (navDirection === "prev") {
  //     if (offset >= 20) {
  //       setOffset(offset - 20);
  //       setCurrentPage(currentPage - 1);
  //     }
  //   } else {
  //     setOffset(offset + 20);
  //     setCurrentPage(currentPage + 1);
  //   }
  // };

  const handlePaginationClick = (event, page) => {
    setCurrentPage(Number(page));
    setOffset((Number(page) - 1) * rowPerPage);
  };

  if (sportCount > 0) {
    // const indexOfLastTodo = currentPage * rowPerPage;
    // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    // currentPageRow = data.slice(indexOfFirstTodo, indexOfLastTodo);

    for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
      pageNumbers.push(i);
    }
  }
  useEffect(() => {
    setSearch("");
    // fetchAllPlayersType();
    if (offset === 0) {
      getJockey(true);
    } else {
      setOffset(0);
      setCurrentPage(1);
    }

    // eslint-disable-next-line
  }, [props.match]);

  useEffect(() => {
    getJockey();
    // eslint-disable-next-line
  }, [offset]);

  const getJockey = async (type, searchInput) => {
    let searchValue = type === true ? "" : search;

    setIsLoading(true);
    let playerType = props.match.params.player;
    let url =
      playerType === "Jockeys" || playerType === "Driver"
        ? URLS.getJockeys +
          `jockeys/jockey?limit=${rowPerPage}&offset=${
            searchInput ? 0 : offset
          }&search=${searchValue}`
        : URLS.getJockeys +
          `trainers/trainer?limit=${rowPerPage}&offset=${
            searchInput ? 0 : offset
          }&search=${searchValue}`;

    try {
      const { status, data } = await axiosInstance.get(url);
      if (status === 200) {
        setData(data?.result?.rows);
        setIsLoading(false);
        setSportCount(data?.result?.count);
        // setSearch("");
      }
    } catch (err) {
      // console.log(err);
      setIsLoading(false);
    }
  };

  const afterChangeRefresh = () => {
    getJockey();
  };

  const toggleInputModal = () => {
    setIsInputModalOpen(!isInputModalOpen);
  };

  const setActionMessage = (display = false, type = "", message = "") => {
    //  this.setState({ messageBox: { display, type, message } }, () =>
    //    setTimeout(
    //      () =>
    //        this.setState({
    //          messageBox: { display: false, type: "", message: "" },
    //        }),
    //      3000
    //    )
    //  );
    setmessageBox(
      {
        display,
        type,
        message,
      },
      () =>
        setTimeout(
          () => setmessageBox({ display: false, type: "", message: "" }),
          3000
        )
    );
  };

  // const fetchAllPlayersType = async () => {
  //   const { status, data } = await axiosInstance.get(URLS.playersType);
  //   if (status === 200) {
  //     setAllPlayersType(data.result);
  //   }
  // };

  const inputModal = (id, type) => {
    setIsInputModalOpen(true);
    setIdToSend(id);
    setIsEditMode(type);
  };

  const handlePlayersDelete = async () => {
    setIsDeleteLoading("jockeyDelete");
    setIsDeleteModalOpen(false);
    let playerType = props.match.params.player;
    let url =
      playerType === "Jockeys" || playerType === "Driver"
        ? URLS.getJockeys + `jockeys/jockey/${itemToDelete}`
        : URLS.getJockeys + `trainers/trainer/${itemToDelete}`;

    try {
      const { status } = await axiosInstance.delete(url);
      if (status === 200) {
        afterChangeRefresh();
        setItemToDelete(null);
        setIsDeleteLoading("");
      }
    } catch (err) {
      // console.log(err);
    }
  };

  const setItemDelete = (id) => {
    setItemToDelete(id);
    setIsDeleteModalOpen(true);
  };

  const toggleDeleteModal = () => {
    setItemToDelete(null);
    setIsDeleteModalOpen(!isDeleteModalOpen);
  };
  const inputVariationModal = (id, name, Variations) => {
    // let playerType = props.match.params.player;
    setisVariationModalOpen(true);
    setisId(id);
    setisName(name);
  };
  const toggleVariationModal = () => {
    setisVariationModalOpen(false);
  };
  const handleClearClick = () => {
    setSearch("");
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      getJockey(null, "searchInput");
      setOffset(0);
      setCurrentPage(1);
    }
  };

  const mergeModal = (item) => {
    setisMergeModalOpen(true);
    fetchModalParentJockeyTrainer(0, "");
    fetchModalChildJockeyTrainer(0, "");
    let Tracks = "";
    Tracks = {
      value: item?.id,
      label: item?.name,
    };
    setparentTrack(Tracks);
    setchildTrack([]);
    // this.setState({
    //   parentTrack: Tracks,
    //   childTrack: [],
    // });
  };

  const toggleMergeModal = () => {
    setisMergeModalOpen(false);
    setcreateError("");
    setparentTrack("");
    setchildTrack("");
  };

  const fetchModalParentJockeyTrainer = async (ModalTrackPage, searchvalue) => {
    let playerType = props.match.params.player;

    const passApi =
      playerType === "Jockeys" || playerType === "Driver"
        ? URLS.getJockeys +
          `jockeys/jockey?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${searchvalue}`
        : URLS.getJockeys +
          `trainers/trainer?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${searchvalue}`;

    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(ModalTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      setModalTrackCount(Math.ceil(count));
      setModalTrackData(finalData);
    }
  };

  const handleOnScrollBottomModalParentJockeyTrainer = (e, type) => {
    if (
      isModalTrackSearch !== "" &&
      searchModalTrackCount !== Math.ceil(searchModalTrackPage / 20 + 1)
    ) {
      handleModalParentJockeyTrainerInputChange(
        searchModalTrackPage + 20,
        isModalTrackSearch
      );
      //  this.setState({
      //    searchModalTrackPage: searchModalTrackPage + 20,
      //  });
      setsearchModalChildTrackPage(searchModalTrackPage + 20);
    } else {
      if (
        ModalTrackCount !==
          (ModalTrackCount == 1 ? 1 : Math.ceil(ModalTrackPage / 20)) &&
        isModalTrackSearch == ""
      ) {
        fetchModalParentJockeyTrainer(ModalTrackPage + 20, isModalTrackSearch);
        //  this.setState({
        //    ModalTrackPage:,
        //  });
        setModalTrackPage(ModalTrackPage + 20);
      }
    }
  };
  const handleModalParentJockeyTrainerInputChange = (ModalTrackPage, value) => {
    let playerType = props.match.params.player;

    const passApi =
      playerType === "Jockeys" || playerType === "Driver"
        ? URLS.getJockeys +
          `jockeys/jockey?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${value}`
        : URLS.getJockeys +
          `trainers/trainer?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${value}`;

    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        setsearchModalTrack(finalData);
        setsearchModalTrackCount(Math.ceil(count));
        setisModalTrackSearch(value);
      }
    });
  };

  const fetchModalChildJockeyTrainer = async (ModalTrackPage, searchvalue) => {
    let playerType = props.match.params.player;

    const passApi =
      playerType === "Jockeys" || playerType === "Driver"
        ? URLS.getJockeys +
          `jockeys/jockey?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${searchvalue}`
        : URLS.getJockeys +
          `trainers/trainer?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${searchvalue}`;

    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(ModalChildTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      }).filter((Track) => Track?.value !== parentTrack?.value);

      setModalChildTrackCount(Math.ceil(count));
      setModalChildTrackData(finalData);
    }
  };

  const handleOnScrollBottomModalChildJockeyTrainer = (e, type) => {
    if (
      isModalChildTrackSearch !== "" &&
      searchModalChildTrackCount !==
        Math.ceil(searchModalChildTrackPage / 20 + 1)
    ) {
      handleModalChildJockeyTrainerInputChange(
        searchModalChildTrackPage + 20,
        isModalChildTrackSearch
      );
      setsearchModalChildTrackPage(searchModalChildTrackPage + 20);
    } else {
      if (
        ModalChildTrackCount !==
          (ModalChildTrackCount == 1
            ? 1
            : Math.ceil(ModalChildTrackPage / 20)) &&
        isModalChildTrackSearch == ""
      ) {
        fetchModalChildJockeyTrainer(
          ModalChildTrackPage + 20,
          isModalChildTrackSearch
        );
        setModalChildTrackPage(ModalChildTrackPage + 20);
      }
    }
  };
  const handleModalChildJockeyTrainerInputChange = (ModalTrackPage, value) => {
    let playerType = props.match.params.player;

    const passApi =
      playerType === "Jockeys" || playerType === "Driver"
        ? URLS.getJockeys +
          `jockeys/jockey?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${value}`
        : URLS.getJockeys +
          `trainers/trainer?limit=${rowPerPage}&offset=${ModalTrackPage}&search=${value}`;

    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        })?.filter((Track) => Track?.value !== parentTrack?.value);

        setsearchModalChildTrack(finalData);
        setsearchModalChildTrackCount(Math.ceil(count));
        setisModalChildTrackSearch(value);
      }
    });
  };

  const handalMergeJockeyTrainer = async () => {
    setIsLoading(true);
    let payloadJockey = {
      parentJockeyId: parentTrack?.value,
      childJockeyId: childTrack?.map((item) => item?.value),
    };
    let payloadTrainer = {
      parentTrainerId: parentTrack?.value,
      childTrainerId: childTrack?.map((item) => item?.value),
    };
    try {
      let playerType = props.match.params.player;
      const passApi =
        playerType === "Jockeys" || playerType === "Driver"
          ? `race/participant/merge/jockeys`
          : `race/participant/merge/trainers`;
      const { status, data } = await axiosInstance.post(
        passApi,
        playerType === "Jockeys" || playerType === "Driver"
          ? payloadJockey
          : payloadTrainer
      );
      if (status === 200) {
        setActionMessage(true, "Success", data?.message);
        setIsLoading(false);
        setisMergeModalOpen(false);
        getJockey();
      } else {
        setActionMessage(true, "Error", data?.message);
        setIsLoading(false);
        setcreateError(data?.message);
      }
    } catch (err) {
      setActionMessage(true, "Error", err?.response?.data?.message);
      setIsLoading(false);
      setcreateError(err?.response?.data?.message);
    }
  };

  const handleTrackDelete = async () => {
    setIsDeleteLoading("trackDelete");
    setIsDeleteModalOpen(false);

    try {
      const { status } = await axiosInstance.delete(`track/${itemToDelete}`);
      if (status === 200) {
        afterChangeRefresh();
        setIsDeleteLoading("");
        setItemToDelete(null);
      }
    } catch (err) {
      // console.log(err);
    }
  };
  return (
    <Grid container>
      <Grid item xs={12} className="pageWrapper">
        {/* <Paper className="pageWrapper"> */}
        {isDeleteLoading === "jockeyDelete" && (
          <div class="admin-delete-loader">
            <Loader />
          </div>
        )}
        <Box className="bredcrumn-wrap">
          <Breadcrumbs
            separator="/"
            aria-label="breadcrumb"
            className="breadcrumb"
          >
            <Link underline="hover" color="inherit" to="/dashboard">
              Home
            </Link>
            <Link underline="hover" color="inherit">
              racing
            </Link>
            <Link underline="hover" color="inherit">
              {props.match.params.name}
            </Link>
            <Typography className="active_p">
              {props.match.params.player}
            </Typography>
          </Breadcrumbs>
        </Box>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          {/* <Typography variant="h5" style={{ margin: "0" }}>
            {props.match.params.player}
          </Typography> */}
          <Typography variant="h1" align="left">
            {props.match.params.player}
          </Typography>
          <div>
            <TextField
              className={`txt-field-class`}
              placeholder="Search "
              size="small"
              variant="outlined"
              onKeyDown={(e) => handleKeyDown(e)}
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
              sx={{ margin: "8px" }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <img src={SearchIcons} alt="src" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    {search && (
                      <IconButton
                        onClick={() => handleClearClick()}
                        edge="end"
                        style={{ minWidth: "unset" }}
                        size="large"
                      >
                        <CancelIcon />
                      </IconButton>
                    )}
                  </InputAdornment>
                ),
              }}
              style={{
                background: "#ffffff",
              }}
            />
            <Button
              variant="contained"
              style={{
                backgroundColor: "#4455c7",
                color: "#fff",
                borderRadius: "8px",
                textTransform: "capitalize",
                margin: "8px",
              }}
              onClick={() => {
                getJockey(null, "searchInput");
                setOffset(0);
                setCurrentPage(1);
              }}
            >
              Search
            </Button>
            <Button
              variant="contained"
              style={{
                backgroundColor: "#4455c7",
                color: "#fff",
                borderRadius: "8px",
                textTransform: "capitalize",
                margin: "8px",
              }}
              onClick={() => inputModal(null, false)}
            >
              Add {props.match.params.player}
            </Button>
          </div>
        </Box>
        {isLoading && <Loader />}
        {!isLoading && data.length === 0 && <p>No Data Available</p>}
        {!isLoading && data.length > 0 && (
          <TableContainer component={Paper}>
            <Table className="listTable" aria-label="simple table">
              <TableHead>
                <TableRow className="tableHead-row">
                  <TableCell>DID</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Weight</TableCell>
                  <TableCell>Variation</TableCell>
                  <TableCell>Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody className="table_body">
                <TableRow className="table_row">
                  <TableCell
                    colSpan={100}
                    className="table-seprator"
                  ></TableCell>
                </TableRow>
                {/*                     .filter((val) => {
                      if (search === '' || search === null) return val
                      else if (
                        val?.name
                          .toString()
                          .toLowerCase()
                          .includes(search.toString().toLowerCase())
                      ) {
                        return val;
                      }
                    })
                    /* .slice(
                      currentPage * rowPerPage - rowPerPage,
                      currentPage * rowPerPage,
                    ) */}
                {data.map((row, index) => (
                  <TableRow key={row?.id} className="table-rows listTable-Row">
                    <TableCell>{row?.id}</TableCell>
                    <TableCell>{row?.name}</TableCell>
                    <TableCell>{row?.weight}</TableCell>
                    <TableCell>
                      {" "}
                      <Button
                        variant="contained"
                        style={{
                          backgroundColor: "#4455C7",
                          color: "#fff",
                          borderRadius: "8px",
                          textTransform: "capitalize",
                          // padding: "13px 24px 12px",
                          marginLeft: "15px",
                        }}
                        onClick={() => {
                          inputVariationModal(
                            row?.id,
                            row?.name,
                            row?.JockeyVariations
                          );
                        }}
                      >
                        Add/Edit variation
                      </Button>
                    </TableCell>
                    <TableCell>
                      <Button
                        className="table-btn edit-btn"
                        onClick={() => {
                          inputModal(row?.id, true);
                        }}
                        style={{ cursor: "pointer" }}
                      >
                        Edit
                      </Button>
                      <Button
                        className="table-btn delete-btn"
                        style={{ cursor: "pointer" }}
                        onClick={() => {
                          setItemDelete(row?.id);
                        }}
                      >
                        Delete
                      </Button>
                      <Button
                        className="table-btn"
                        style={{
                          cursor: "pointer",
                          backgroundColor: "#4455C7",
                          color: "#fff",
                          borderRadius: "8px",
                          textTransform: "uppercase",
                        }}
                        onClick={() => mergeModal(row)}
                      >
                        Merge
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={100} className="pagination">
                    {data?.length > 0 && (
                      <div className="tablePagination jokeys-pagination">
                        {/* <button
                          className={
                            offset >= 20
                              ? "btn-navigation"
                              : "btn-navigation-disabled"
                          }
                          disabled={offset >= 20 ? false : true}
                          onClick={() => handlePaginationButtonClick("prev")}
                        >
                          <ReactSVG src={arrowLeft} />
                        </button> */}
                        <Pagination
                          hideNextButton
                          hidePrevButton
                          disabled={sportCount > 0 ? false : true}
                          page={currentPage}
                          onChange={handlePaginationClick}
                          count={pageNumbers[pageNumbers?.length - 1]}
                          siblingCount={2}
                          boundaryCount={1}
                          size="small"
                        />
                        {/* <button
                          className={
                            rowPerPage + offset < sportCount
                              ? "btn-navigation"
                              : "btn-navigation-disabled"
                          }
                          disabled={
                            rowPerPage + offset < sportCount ? false : true
                          }
                          onClick={() => handlePaginationButtonClick("next")}
                        >
                          <ReactSVG src={arrowRight} />
                        </button> */}
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        )}
        {/* </Paper> */}

        <Modal
          className="modal modal-input"
          open={isInputModalOpen}
          onClose={toggleInputModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center">
              {isEditMode
                ? `Edit ${props.match.params.player}`
                : `Create ${props.match.params.player}`}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={toggleInputModal}
            />
            <CreatePlayers
              inputModal={toggleInputModal}
              id={idToSend}
              isEditMode={isEditMode}
              fetchAllPlayers={afterChangeRefresh}
              editJockeys={true}
              playertypeId={props.match.params.typeid}
              playertypeName={props.match.params.player}
            />
          </div>
        </Modal>
        <Modal
          className="modal modal-input"
          open={isVariationModalOpen}
          onClose={toggleVariationModal}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">
              {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
              {props.match.params.player} Variation Details({isName})
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={toggleVariationModal}
            />
            <CreateVariation
              inputModal={toggleVariationModal}
              id={isId}
              // variationData={variationData}
              playertypeName={props.match.params.player}
              fetchAllJockey={afterChangeRefresh}
              variationData={data
                ?.filter((item) => isId == item.id)
                .map((item) =>
                  props.match.params.player === "Jockeys" ||
                  props.match.params.player === "Driver"
                    ? item?.JockeyVariations
                    : item?.TrainerVariations
                )}
            />
          </div>
        </Modal>

        <Modal
          className="modal modal-input"
          open={isMergeModalOpen}
          onClose={() => toggleMergeModal()}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">
              Merge{" "}
              {props.match.params.player === "Jockeys" ? "Jockey" : "Trainer"}
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={() => toggleMergeModal()}
            />
            <Grid container className="page-content adminLogin text-left">
              <Grid item xs={12}>
                <label className="modal-label">
                  {" "}
                  Parent{" "}
                  {props.match.params.player === "Jockeys"
                    ? "Jockey"
                    : "Trainer"}{" "}
                </label>
                <Select
                  className="React teamsport-select teamsport-multiple-select mb15  merge-select"
                  classNamePrefix="select"
                  menuPosition="fixed"
                  onMenuScrollToBottom={(e) =>
                    handleOnScrollBottomModalParentJockeyTrainer(e)
                  }
                  onInputChange={(e) =>
                    handleModalParentJockeyTrainerInputChange(0, e)
                  }
                  value={
                    isModalTrackSearch
                      ? searchModalTrack?.find((item) => {
                          return item?.value == parentTrack;
                        })
                      : parentTrack
                  }
                  options={
                    isModalTrackSearch ? searchModalTrack : ModalTrackData
                  }
                  onChange={(e) => setparentTrack(e)}
                />
              </Grid>
              <Grid item xs={12}>
                <label className="modal-label">
                  {" "}
                  Child{" "}
                  {props.match.params.player === "Jockeys"
                    ? "Jockeys"
                    : "Trainers"}{" "}
                </label>
                <Select
                  className="React teamsport-select teamsport-multiple-select merge-select"
                  classNamePrefix="select"
                  menuPosition="fixed"
                  isMulti
                  onMenuScrollToBottom={(e) =>
                    handleOnScrollBottomModalChildJockeyTrainer(e)
                  }
                  onInputChange={(e) =>
                    handleModalChildJockeyTrainerInputChange(0, e)
                  }
                  value={
                    isModalChildTrackSearch !== ""
                      ? searchModalChildTrack?.find((item) => {
                          return item?.value === childTrack;
                        })
                      : childTrack
                  }
                  options={
                    isModalChildTrackSearch !== ""
                      ? searchModalChildTrack
                      : ModalChildTrackData
                  }
                  onChange={(e) => setchildTrack(e)}
                />
              </Grid>
            </Grid>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    className="mt-3 admin-btn-green"
                    onClick={handalMergeJockeyTrainer}
                    color="primary"
                    value={!isLoading ? "Merge" : "Loading..."}
                    disabled={isLoading}
                  />
                </div>
                {createError ? (
                  <p
                    className="errorText"
                    style={{ margin: "0px 0 0 0", width: "300px" }}
                  >
                    {createError}
                  </p>
                ) : (
                  ""
                )}
              </Grid>
            </Grid>
          </div>
        </Modal>

        <ShowModal
          isModalOpen={isDeleteModalOpen}
          onClose={toggleDeleteModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          onOkay={handlePlayersDelete}
          onCancel={toggleDeleteModal}
        />
      </Grid>
    </Grid>
  );
};

export default Index;
