import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../rugbyleague.scss";

const nationalData = [
  {
    label: "Yes",
    value: true,
  },
  {
    label: "No",
    value: false,
  },
];
class Team extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      teamValues: {
        teamName: "",
        national: "",
        gender: "",
        rapidTeamId: "",
        CountryId: "",
        id: "",
        CountryName: "",
      },
      countryAll: [],
      countryCount: 0,
      pageCountry: 0,
      searchCountry: [],
      searchCountryCount: 0,
      SearchCountrypage: 0,
      isCountrySearch: "",
      TeamList: [],
      TeamCount: 0,
      errorName: "",
      errorNational: "",
      errorGender: "",
      errorCountry: "",
    };
  }

  componentDidMount() {
    this.fetchAllTeam();
    // this.fetchAllCountry(0);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllTeam();
    }
  }
  fetchAllCountry = async (page) => {
    const { status, data } = await axiosInstance.get(
      // `${URLS.distance}?size=20&page=${page}`
      `${URLS.country}?limit=20&offset=${page}`
    );

    if (status === 200) {
      let Parray = [...this.state.countryAll];

      let count = data?.result?.count / 20;
      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.country,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state.countryAll, newdata);

      this.setState({
        countryAll: _.uniqBy(filterData, function (e) {
          return e.value;
        }),
        countryCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomCountry = () => {
    let {
      pageCountry,
      isCountrySearch,
      searchCountryCount,
      SearchCountrypage,
      countryCount,
    } = this.state;

    if (
      isCountrySearch !== "" &&
      searchCountryCount !== Math.ceil(SearchCountrypage / 20 + 1)
    ) {
      this.handleCountryInputChange(SearchCountrypage + 20, isCountrySearch);
      this.setState({
        SearchCountrypage: SearchCountrypage + 20,
      });
    } else {
      if (countryCount !== Math.ceil(pageCountry / 20)) {
        this.fetchAllCountry(pageCountry + 20);
        this.setState({
          pageCountry: pageCountry + 20,
        });
      }
    }
  };

  handleCountryInputChange = (page, value) => {
    // if (value.length > 2) {
    axiosInstance
      .get(`${URLS.country}?limit=20&offset=${page}&search=${value}`)
      .then((res) => {
        if (res.status === 200) {
          let response = res?.data?.result?.rows;
          //     let Parray = [...this.state.countryAll];
          let count = res?.data?.result?.count / 20;

          let newdata = [];
          let FinalData = response?.map((item) => {
            newdata.push({
              label: item?.country,
              value: item?.id,
            });
          });
          let filterData = _.unionBy(this.state.searchCountry, newdata);

          this.setState({
            searchCountry: _.uniqBy(filterData, function (e) {
              return e.value;
            }),
            searchCountryCount: Math.ceil(count),
            isCountrySearch: value,
          });
        }
      });
  };
  fetchSelectedCountry = (CountryId, CountryName) => {
    let seletedCountry = [
      {
        label: CountryName,
        value: CountryId,
      },
    ];

    this.setState({
      countryAll: CountryId ? seletedCountry : this.state.countryAll,
    });
  };
  fetchAllTeam = async () => {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `rls/team?limit=${rowPerPage}&offset=${offset}`
      );
      if (status === 200) {
        this.setState({
          TeamList: data?.result?.rows,
          isLoading: false,
          TeamCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  handalValidate = () => {
    let { teamValues } = this.state;

    let flag = true;
    if (teamValues?.teamName === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (teamValues?.national === "") {
      flag = false;
      this.setState({
        errorNational: "This field is mandatory",
      });
    } else {
      this.setState({
        errorNational: "",
      });
    }
    if (teamValues?.gender === "") {
      flag = false;
      this.setState({
        errorGender: "This field is mandatory",
      });
    } else {
      this.setState({
        errorGender: "",
      });
    }
    if (teamValues?.CountryId === "" || teamValues?.CountryId === null) {
      flag = false;
      this.setState({
        errorCountry: "This field is mandatory",
      });
    } else {
      this.setState({
        errorCountry: "",
      });
    }

    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        name: this.state?.teamValues?.teamName,
        rapidTeamId: this.state?.teamValues?.rapidTeamId,
        national: this.state?.teamValues?.national,
        gender: this.state?.teamValues?.gender,
        SportId: 12,
        CountryId: this.state?.teamValues?.CountryId,
      };
      const { status } = await axiosInstance.post(`rls/team`, payload);
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllTeam();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        name: this.state?.teamValues?.teamName,
        rapidTeamId: this.state?.teamValues?.rapidTeamId,
        national: this.state?.teamValues?.national,
        gender: this.state?.teamValues?.gender,
        SportId: 12,
        CountryId: this.state?.teamValues?.CountryId,
      };
      const { status } = await axiosInstance.put(
        `rls/team/${this.state.teamValues?.id}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchAllTeam();
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Edited Successfully`
        //   );
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorGender: "",
      errorNational: "",
      errorCountry: "",
    });
  };

  inputModal = (item, type) => () => {
    this.fetchAllCountry(0);
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchSelectedCountry(item?.CountryId, item?.Country?.country);
      this.setState({
        teamValues: {
          teamName: item?.name,
          national: item?.national,
          gender: item?.gender,
          rapidTeamId: item?.rapidTeamId,
          CountryId: item?.CountryId,
          CountryName: item?.Country?.country,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        teamValues: {
          teamName: "",
          national: "",
          gender: "",
          rapidTeamId: "",
          CountryId: null,
          CountryName: "",
          id: item?.id,
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `rls/team/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllTeam();
        });
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      }
    } catch (err) {
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      SportVariationModal,
      teamValues,
      countryAll,
      countryCount,
      pageCountry,
      searchCountry,
      searchCountryCount,
      SearchCountrypage,
      isCountrySearch,
      TeamList,
      TeamCount,
      errorName,
      errorNational,
      errorGender,
      errorCountry,
    } = this.state;
    const pageNumbers = [];
    // sportType !== "" &&
    //   (TeamList = TeamList.filter((obj) => obj.sportTypeId == sportType));

    // let currentPageRow = TeamList;

    // if (TeamList?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = TeamList.slice(indexOfFirstTodo, indexOfLastTodo);

    if (TeamCount > 0) {
      for (let i = 1; i <= Math.ceil(TeamCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {/* {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )} */}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Rugby League
                </Link>
                <Typography className="active_p">Team</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Team
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <SelectBox
                  onChange={(e) => this.setState({ sportType: e.target.value })}
                  style={{ width: "20%", marginTop: "0px" }}
                >
                  <option value="">Select Category</option>
                  {CategoryData?.length > 0 &&
                    CategoryData?.map((obj, i) => (
                      <option key={i} value={obj?.id}>
                        {obj?.categoryName}
                      </option>
                    ))}
                </SelectBox> */}
                {/* <Select
                  className="React rugby-select"
                  classNamePrefix="select"
                  // onMenuScrollToBottom={(e) =>
                  //   this.handleOnScrollBottomDistance(e)
                  // }
                  // onInputChange={(e) => this.handleDistanceInputChange(1, e)}
                  // value={
                  //   isDistanceSearch
                  //     ? searchDistance?.find((item) => {
                  //         return item?.value == distance;
                  //       })
                  //     : distanceAll?.find((item) => {
                  //         return item?.value == distance;
                  //       })
                  // }
                  // onChange={(e) =>
                  //   this.setState({
                  //     distance: e.value,
                  //   })
                  // }
                  options={countryData}
                /> */}
                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  // value={search}
                  onChange={(e) => {
                    // setSearch(e.target.value);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  // onClick={() => fetchAnimal()}
                >
                  Search
                </Button> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TeamList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && TeamList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell style={{ width: "25%" }}>
                          rapidTeamId
                        </TableCell>
                        <TableCell style={{ width: "25%" }}>
                          Team Name
                        </TableCell>
                        {/* <TableCell style={{ width: "25%" }}>
                          variation
                        </TableCell> */}
                        {/* <TableCell style={{ width: "25%" }}>variation</TableCell> */}
                        <TableCell>National</TableCell>
                        <TableCell>Gender</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {TeamList?.map((item) => {
                        return (
                          <TableRow className="table-rows listTable-Row">
                            <TableCell> {item?.id} </TableCell>
                            <TableCell>{item?.rapidTeamId}</TableCell>
                            <TableCell>{item?.name}</TableCell>
                            <TableCell>
                              {item?.national === false ? "No" : "Yes"}
                            </TableCell>
                            <TableCell>{item?.gender}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                className="table-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                TeamCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                sports.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                sports.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Team" : "Edit Team"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )} */}
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="rugby-text"
                    >
                      <label className="modal-label"> Team Name</label>
                      <TextField
                        className="rugby-textfield eventname"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Team Name"
                        value={teamValues?.teamName}
                        onChange={(e) =>
                          this.setState({
                            teamValues: {
                              ...teamValues,
                              teamName: e.target.value,
                            },
                          })
                        }
                      />
                      {errorName ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorName}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="rugby-text"
                      >
                        <label className="modal-label">rapid Team Id</label>
                        <TextField
                          className="rugby-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="rapid Team Id"
                          value={teamValues?.rapidTeamId}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                rapidTeamId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> Country </label>
                        <Select
                          className="React rugby-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Country"
                          onMenuScrollToBottom={(e) =>
                            this.handleOnScrollBottomCountry(e)
                          }
                          // isSearchable={false}
                          onInputChange={(e) =>
                            this.handleCountryInputChange(0, e)
                          }
                          value={
                            isCountrySearch
                              ? searchCountry?.find((item) => {
                                  return item?.value == teamValues?.CountryId;
                                })
                              : countryAll?.find((item) => {
                                  return item?.value == teamValues?.CountryId;
                                })
                          }
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                CountryId: e.value,
                              },
                            })
                          }
                          options={isCountrySearch ? searchCountry : countryAll}
                        />
                        {errorCountry ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorCountry}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="national-select">
                        <label className="modal-label"> National </label>
                        <Select
                          className="React rugby-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={nationalData?.find((item) => {
                            return item?.value === teamValues?.national;
                          })}
                          onChange={(e) =>
                            this.setState({
                              teamValues: {
                                ...teamValues,
                                national: e.value,
                              },
                            })
                          }
                          options={nationalData}
                        />
                        {errorNational ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorNational}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Gender </label>
                          <RadioGroup
                            aria-label="gender"
                            name="gender"
                            className="gender"
                            value={teamValues?.gender}
                            onChange={(e) =>
                              this.setState({
                                teamValues: {
                                  ...teamValues,
                                  gender: e.target.value,
                                },
                              })
                            }
                          >
                            <FormControlLabel
                              value="F"
                              control={
                                <Radio
                                  color="primary"
                                  checked={teamValues?.gender === "F"}
                                />
                              }
                              label="Female"
                            />
                            <FormControlLabel
                              value="M"
                              control={
                                <Radio
                                  color="primary"
                                  checked={teamValues?.gender === "M"}
                                />
                              }
                              label="Male"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorGender ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorGender}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Team;
