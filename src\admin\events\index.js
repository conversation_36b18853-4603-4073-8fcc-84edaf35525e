import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import CreateEvents from "./CreateEvents";
import Pagination from '@mui/material/Pagination';
import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
import { ReactSVG } from "react-svg";
import { getFormetedDate } from "../../helpers/common";

class Events extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      events: [],
      sportAll: [],
      leagueAll: [],
      locationAll: [],
      weatherAll: [],
      trackAll: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      searchInput: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
    };
  }

  componentDidMount() {
    this.fetchAllEvents();
  }

  async fetchAllEvents() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(URLS.events);
    if (status === 200) {
      this.setState({ events: data.result, isLoading: false });
      this.fetchAllSport();
      this.fetchAllLeague();
      this.fetchAllLocation();
      this.fetchAllWeather();
      this.fetchAllTrack();
    }
  }
  //
  fetchAllSport = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      this.setState({ sportAll: data.result });
    }
  };

  getSport = (id) => {
    let { sportAll } = this.state;
    let sportName = sportAll
      .filter((obj) => obj.id === id)
      .map((object) => object.sportName);
    return sportName;
  };

  fetchAllLeague = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.league);
    if (status === 200) {
      this.setState({ leagueAll: data.result });
    }
  };

  getLeague = (id) => {
    let { leagueAll } = this.state;
    let leagueName = leagueAll
      .filter((obj) => obj.id === id)
      .map((object) => object.leagueName);
    return leagueName;
  };

  fetchAllLocation = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.location);
    if (status === 200) {
      this.setState({ locationAll: data.result });
    }
  };

  getLocation = (id) => {
    let { locationAll } = this.state;
    let name = locationAll
      .filter((obj) => obj.id === id)
      .map((object) => object.venueName);
    return name;
  };

  fetchAllWeather = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.weather);
    if (status === 200) {
      this.setState({ weatherAll: data.result });
    }
  };

  getWeather = (id) => {
    let { weatherAll } = this.state;
    let name = weatherAll
      .filter((obj) => obj.id === id)
      .map((object) => object.weatherType);
    return name;
  };

  fetchAllTrack = async () => {
    const { status, data } = await axiosInstance.get(URLS.track);
    if (status === 200) {
      this.setState({ trackAll: data.result });
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllEvents();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.events}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvents();
        });
        this.setActionMessage(true, "Success", "Events Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, events } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < events.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  render() {
    var {
      events,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      searchInput,
    } = this.state;
    const pageNumbers = [];

    searchInput !== "" &&
      (events = events?.filter((obj) =>
        obj?.eventName
          ?.toString()
          .toLowerCase()
          .includes(searchInput.toString().toLowerCase())
      ));

    let currentPageRow = events;

    if (events?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = events.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(events.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12}>
            <Paper className="pageWrapper">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}

              <Grid container direction="row" alignItems="space-around">
                <Grid item xs={8}>
                  <h3 className="text-left">Events</h3>
                </Grid>
                <Grid item xs={2}>
                  <input
                    type="text"
                    className=""
                    placeholder="search"
                    value={searchInput}
                    onChange={(e) =>
                      this.setState({
                        ...this.state.searchInput,
                        searchInput: e.target.value,
                      })
                    }
                    style={{
                      fontSize: "16px",
                      borderRadius: "3px",
                      minHeight: "40px",
                      border: "1px solid #ddd",
                      paddingLeft: "10px",
                    }}
                  />
                </Grid>
                <Grid item xs={2}>
                  <ButtonComponent
                    className="addButton admin-btn-green"
                    onClick={this.inputModal(null, "create")}
                    color="primary"
                    value="Add New"
                  />
                </Grid>
              </Grid>
              {isLoading && <Loader />}
              {!isLoading && events.length === 0 && <p>No Data Available</p>}
              {!isLoading && events.length > 0 && (
                <>
                  <TableContainer component={Paper}>
                    <Table
                      className="listTable api-provider-listTable"
                      aria-label="simple table"
                    >
                      <TableHead>
                        <TableRow>
                          <TableCell>DID</TableCell>
                          <TableCell>Sport</TableCell>
                          <TableCell>League</TableCell>
                          {/* <TableCell>Api Event Id</TableCell> */}
                          <TableCell>Event Name</TableCell>
                          <TableCell>Description</TableCell>
                          {/* <TableCell>Category</TableCell> */}
                          <TableCell>Location</TableCell>
                          <TableCell>Event date</TableCell>
                          <TableCell>Event to date</TableCell>
                          <TableCell>comment</TableCell>
                          <TableCell>Weather</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {currentPageRow?.map((event, i) => (
                          <TableRow key={i}>
                            <TableCell>{event.id}</TableCell>
                            <TableCell>
                              {this.getSport(event.sportId)}
                            </TableCell>
                            <TableCell>
                              {this.getLeague(event.leagueId)}
                            </TableCell>
                            {/* <TableCell>{1}</TableCell> */}
                            <TableCell>{event.eventName}</TableCell>
                            <TableCell>{event.description}</TableCell>
                            {/* <TableCell>{event.categoryId}</TableCell> */}
                            <TableCell>
                              {this.getLocation(event.locationId)}
                            </TableCell>
                            <TableCell>
                              {getFormetedDate(event.eventDate)}
                            </TableCell>
                            <TableCell>
                              {getFormetedDate(event.eventToDate)}
                            </TableCell>
                            <TableCell>{event.comment}</TableCell>
                            <TableCell>
                              {this.getWeather(event.weather)}
                            </TableCell>
                            <TableCell>
                              <EditIcon
                                onClick={this.inputModal(
                                  event.id,
                                  "View Runner"
                                )}
                                color="primary"
                                className="mr10 cursor iconBtn admin-btn-green"
                              />
                              <DeleteOutlineIcon
                                onClick={this.setItemToDelete(event.id)}
                                color="secondary"
                                className="mr10 cursor iconBtn admin-btn-orange"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <div className="tablePagination">
                    <button
                      className={
                        events.length / rowPerPage > 1
                          ? "btn-navigation"
                          : "btn-navigation-disabled"
                      }
                      onClick={() => this.handlePaginationButtonClick("prev")}
                      disabled={events.length / rowPerPage > 1 ? false : true}
                    >
                      <ReactSVG src={arrowLeft} />
                    </button>
                    <Pagination
                      hideNextButton
                      hidePrevButton
                      disabled={events.length / rowPerPage > 1 ? false : true}
                      page={currentPage}
                      onChange={this.handlePaginationClick}
                      count={pageNumbers[pageNumbers?.length - 1]}
                      siblingCount={2}
                      boundaryCount={1}
                      size="small"
                    />
                    <button
                      className={
                        events.length / rowPerPage > 1
                          ? "btn-navigation"
                          : "btn-navigation-disabled"
                      }
                      onClick={() => this.handlePaginationButtonClick("next")}
                      disabled={events.length / rowPerPage > 1 ? false : true}
                    >
                      <ReactSVG src={arrowRight} />
                    </button>
                  </div>
                </>
              )}
            </Paper>

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Events" : "Edit Events"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateEvents
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllEvents={this.afterChangeRefresh}
                  sportAll={this.state.sportAll}
                  leagueAll={this.state.leagueAll}
                  locationAll={this.state.locationAll}
                  weatherAll={this.state.weatherAll}
                  trackAll={this.state.trackAll}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Events;
