import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import {
  LocalizationProvider,
  DesktopDatePicker,
  MobileDateTimePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import moment from "moment-timezone";

import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { ActionMessage, Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import { ReactComponent as LeftArrow } from "../../../images/left-arrow.svg";
import { ReactComponent as RightArrow } from "../../../images/right-arrow.svg";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";

import "../teamsport.scss";
import CreateIdentifierEvent from "./createIdentifierEvent/CreateIdentifierEvent";
import CreateEventVariation from "./createEventVariation/CreateEventVariation";
import RoundModal from "./roundModal/RoundModal";
import ConfigurationsEvent from "./configurationsEvent";
import fantasyAxiosInstance from "../../../helpers/Axios/fantasyAxios";
let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const statusData = [
  {
    label: "Pending",
    value: "upcoming",
  },
  {
    label: "In-Progress",
    value: "inprogress",
  },
  {
    label: "Finished",
    value: "finished",
  },
];
const winnerData = [
  {
    label: "Home Team",
    value: 1,
  },
  {
    label: "Away Team",
    value: 2,
  },
  {
    label: "Draw",
    value: 3,
  },
];

const EventTypeData = [
  {
    label: "Playoff",
    value: "playoff",
  },
  {
    label: "Final",
    value: "finale",
  },
];

class Event extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isEventModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      eventValues: {
        eventName: "",
        awayTeamId: "",
        homeTeamId: "",
        startTime: "",
        endTime: "",
        rapidEventId: "",
        id: "",
        RLTournamentId: "",
        CricketTournamentId: "",
        NBATournamentId: "",
        AFLTournamentId: "",
        ARTournamentId: "",
        ARTournamentName: "",
        GolfTournamentId: "",
        TennisTournamentId: "",
        BaseballTournamentId: "",
        IceHockeyTournamentId: "",
        BoxingTournamentId: "",
        MMATournamentId: "",
        SoccerTournamentId: "",
        eventType: "TEAM",
        OutrightsTeam: [],
        status: null,
        isEventType: null,
        isWeekType: null,
        winnerCode: null,
        homeScore: "",
        awayScore: "",
        seasonId: null,
      },
      EventList: [],
      EventCount: 0,
      TournamentData: [],
      TournamentCount: 0,
      TournamentPage: 0,
      errorName: "",
      errorAwayTeamId: "",
      errorHomeTeamId: "",
      errorStartTime: "",
      errorEndTime: "",
      errorTournament: "",
      errorOutrightsTeam: "",
      errorScore: "",
      errorIsEventType: "",
      stepperCount: 0,
      externalTeamData: [],
      externalTeamCount: 0,
      selectTeam: "",
      SelectedExternalTeamList: [],
      ExternalTeamPage: 0,
      externalTournamentData: [],
      externalTournamentCount: 0,
      selectTournament: "",
      SelectedExternalTournamentList: [],
      ExternalTournamentPage: 0,
      filterDate: null,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      searchTeam: [],
      searchTeamCount: 0,
      searchTeamPage: 0,
      isTeamSearch: "",
      isExternalTournamentSearch: "",
      searchExternalTournamentCount: 0,
      searchExternalTournamentPage: 0,
      searchTournament: [],
      searchTournamentCount: 0,
      searchTournamentPage: 0,
      isTournamentSearch: "",
      AwayTeamData: [],
      AwayTeamCount: 0,
      AwayTeamPage: 0,
      searchAwayTeam: [],
      searchAwayTeamCount: 0,
      searchAwayTeamPage: 0,
      isAwayTeamSearch: "",
      HomeTeamData: [],
      HomeTeamCount: 0,
      HomeTeamPage: 0,
      searchHomeTeam: [],
      searchHomeTeamCount: 0,
      searchHomeTeamPage: 0,
      isHomeTeamSearch: "",
      isTournamentLoading: false,
      isAwayTeamLoading: false,
      isHomeTeamLoading: false,
      search: "",
      isIdentifierModal: false,
      isIdentifierEventId: "",
      isIdentifierEventData: "",
      isVariationModalOpen: false,
      searchModalOutrightsTeam: [],
      searchModalOutrightsTeamCount: 0,
      searchModalOutrightsTeamPage: 0,
      isModalOutrightsTeamSearch: "",
      ModalOutrightsTeamData: [],
      ModalOutrightsTeamPage: 0,
      ModalOutrightsTeamCount: 0,
      checkBoxValues: [],
      selectedCheckBoxEvent: [],
      isRoundModal: false,
      roundDisplayName: "",
      eventConfigurationList: {},
      modalType: "",
      eventConfigurationScreen: false,
      eventConfigurationModal: false,
      eventConfigurationDetailsList: [],
      configurationLoading: false,
      eventConfigurationId: "",
      itemToDeleteEvent: null,
      WeekTypeData: [],
      selectCategory: "",
      CategoryPage: 0,
      externalCategoryData: [],
      categoryCount: 0,
      searchCategory: [],
      searchCategoryCount: 0,
      searchCategoryPage: 0,
      isCategorySearch: "",
    };
  }
  componentDidMount() {
    this.fetchAllEvent(
      0,
      this.state?.selectTeam,
      this.state?.filterDate,
      this.state?.selectTournament,
      ""
    );
    this.props.match.path?.includes("soccer") &&
      this.fetchAllCategory(0, "ExternalCategory");
    this.fetchAllTeam(this.state.ExternalTeamPage);
    this.fetchAllExternalTournament(this.state.ExternalTournamentPage);
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllEvent(
        this.state.offset,
        this.state?.selectTeam,
        this.state?.filterDate,
        this.state?.selectTournament,
        this.state?.search
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEvent(
        0,
        this.state?.selectTeam,
        this.state?.filterDate,
        this.state?.selectTournament,
        ""
      );
      this.props.match.path?.includes("soccer") &&
        this.fetchAllCategory(0, "ExternalCategory");

      this.fetchAllTeam(0);
      this.fetchAllExternalTournament(0);
      this.setState({
        offset: 0,
        currentPage: 1,
        ExternalTeamPage: 0,
        externalTeamData: [],
        ExternalTeamPage: 0,
        selectTeam: "",
        SelectedExternalTeamList: [],
        ExternalTournamentPage: 0,
        externalTournamentData: [],
        selectTournament: "",
        SelectedExternalTournamentList: [],
        filterDate: null,
        searchTeam: [],
        searchTeamPage: 0,
        searchTournament: [],
        searchTournamentPage: 0,
        TournamentData: [],
        AwayTeamData: [],
        HomeTeamData: [],
        search: "",
        checkBoxValues: [],
        selectedCheckBoxEvent: [],
      });
    }
  }

  fetchAllEvent = async (page, teamId, date, tournamentId, searchvalue) => {
    let { rowPerPage, offset, filterDate, timezone } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/event?limit=${rowPerPage}&offset=${page}&CricketTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/event?limit=${rowPerPage}&offset=${page}&RLTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&SportId=12&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/event?limit=${rowPerPage}&offset=${page}&RLTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&SportId=13&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("basketball")
        ? `nba/event?limit=${rowPerPage}&offset=${page}&NBATournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("afl")
        ? `afl/event?limit=${rowPerPage}&offset=${page}&AFLTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("australianrules")
        ? `ar/event?limit=${rowPerPage}&offset=${page}&ARTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("golf")
        ? `golf/event?limit=${rowPerPage}&offset=${page}&GolfTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true&isOuright=true`
        : this.props.match.path?.includes("tennis")
        ? `tennis/event?limit=${rowPerPage}&offset=${page}&TennisTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("baseball")
        ? `baseball/event?limit=${rowPerPage}&offset=${page}&BaseballTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/event?limit=${rowPerPage}&offset=${page}&IceHockeyTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("boxing")
        ? `boxing/event?limit=${rowPerPage}&offset=${page}&BoxingTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("mma")
        ? `mma/event?limit=${rowPerPage}&offset=${page}&MMATournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : this.props.match.path?.includes("soccer")
        ? `soccer/event?limit=${rowPerPage}&offset=${page}&SoccerTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&search=${searchvalue}&isAdmin=true`
        : `rls/event?limit=${rowPerPage}&offset=${page}&RLTournamentId=${
            tournamentId === 0 ? "" : tournamentId
          }&teamId=${teamId === 0 ? "" : teamId}&startDate=${
            tournamentId && date === null
              ? ""
              : date === null
              ? moment()?.format("YYYY-MM-DD")
              : date
          }&endDate=${
            date === null ? "" : date
          }&timezone=${timezone}&SportId=14&search=${searchvalue}&isAdmin=true`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        const finalRoundCount = data?.result?.sportFinalCount
          ? data?.result?.sportFinalCount
          : 4;

        const generatedWeekData = Array.from(
          { length: finalRoundCount },
          (_, index) => ({
            label: `Week ${index + 1}`,
            value: index + 1,
          })
        );
        this.setState({
          EventList: data?.result?.rows,
          isLoading: false,
          EventCount: data?.result?.count,
          WeekTypeData: generatedWeekData,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  handalValidate = () => {
    let { eventValues } = this.state;
    let flag = true;
    if (eventValues?.eventName?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (!this.props.match.path?.includes("golf")) {
      if (eventValues?.awayTeamId === "") {
        flag = false;
        this.setState({
          errorAwayTeamId: "This field is mandatory",
        });
      } else {
        this.setState({
          errorAwayTeamId: "",
        });
      }
      if (eventValues?.homeTeamId === "") {
        flag = false;
        this.setState({
          errorHomeTeamId: "This field is mandatory",
        });
      } else {
        this.setState({
          errorHomeTeamId: "",
        });
      }
    } else {
      if (this.state.eventValues.eventType === "TEAM") {
        if (eventValues?.awayTeamId === "") {
          flag = false;
          this.setState({
            errorAwayTeamId: "This field is mandatory",
          });
        } else {
          this.setState({
            errorAwayTeamId: "",
          });
        }
        if (eventValues?.homeTeamId === "") {
          flag = false;
          this.setState({
            errorHomeTeamId: "This field is mandatory",
          });
        } else {
          this.setState({
            errorHomeTeamId: "",
          });
        }
      } else {
        if (
          !eventValues?.OutrightsTeam ||
          eventValues?.OutrightsTeam?.length === 0
        ) {
          flag = false;
          this.setState({
            errorOutrightsTeam: "This field is mandatory",
          });
        } else {
          this.setState({
            errorOutrightsTeam: "",
          });
        }
      }
    }
    if (eventValues?.startTime === "") {
      flag = false;
      this.setState({
        errorStartTime: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartTime: "",
      });
    }
    // if (eventValues?.endTime === "") {
    //   flag = false;
    //   this.setState({
    //     errorEndTime: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorEndTime: "",
    //   });
    // }
    if (this.props.match.path?.includes("cricket")) {
      if (
        eventValues?.CricketTournamentId === "" ||
        eventValues?.CricketTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
      if (eventValues?.isEventType === null) {
        flag = false;
        this.setState({
          errorIsEventType: "This field is mandatory",
        });
      } else {
        this.setState({
          errorIsEventType: "",
        });
      }
    } else if (this.props.match.path?.includes("basketball")) {
      if (
        eventValues?.NBATournamentId === "" ||
        eventValues?.NBATournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("afl")) {
      if (
        eventValues?.AFLTournamentId === "" ||
        eventValues?.AFLTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("australianrules")) {
      if (
        eventValues?.ARTournamentId === "" ||
        eventValues?.ARTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
      if (eventValues?.status == "finished") {
        if (
          eventValues?.winnerCode &&
          eventValues?.homeScore &&
          eventValues?.awayScore
        ) {
          if (
            eventValues?.winnerCode == 1 &&
            Number(eventValues?.homeScore) <= Number(eventValues?.awayScore)
          ) {
            flag = false;
            this.setState({
              errorScore: "Home team score should be more than Away team score",
            });
          } else if (
            eventValues?.winnerCode == 2 &&
            Number(eventValues?.homeScore) >= Number(eventValues?.awayScore)
          ) {
            flag = false;
            this.setState({
              errorScore: "Away team score should be more than Home team score",
            });
          } else if (
            eventValues?.winnerCode == 3 &&
            Number(eventValues?.homeScore) != Number(eventValues?.awayScore)
          ) {
            flag = false;
            this.setState({
              errorScore: "Home team score and Away team score should be same",
            });
          } else {
            this.setState({
              errorScore: "",
            });
          }
        } else if (!Boolean(eventValues?.winnerCode)) {
          flag = false;
          this.setState({
            errorScore: "Winner Team required",
          });
        } else if (!Boolean(eventValues?.homeScore)) {
          flag = false;
          this.setState({
            errorScore: "Home Team Score required",
          });
        } else if (!Boolean(eventValues?.awayScore)) {
          flag = false;
          this.setState({
            errorScore: "Away Team Score required",
          });
        } else {
          this.setState({
            errorScore: "",
          });
        }
      }
    } else if (this.props.match.path?.includes("golf")) {
      if (
        eventValues?.GolfTournamentId === "" ||
        eventValues?.GolfTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("tennis")) {
      if (
        eventValues?.TennisTournamentId === "" ||
        eventValues?.TennisTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("baseball")) {
      if (
        eventValues?.BaseballTournamentId === "" ||
        eventValues?.BaseballTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("icehockey")) {
      if (
        eventValues?.IceHockeyTournamentId === "" ||
        eventValues?.IceHockeyTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("boxing")) {
      if (
        eventValues?.BoxingTournamentId === "" ||
        eventValues?.BoxingTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("mma")) {
      if (
        eventValues?.MMATournamentId === "" ||
        eventValues?.MMATournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("soccer")) {
      if (
        eventValues?.SoccerTournamentId === "" ||
        eventValues?.SoccerTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    } else if (this.props.match.path?.includes("rugbyleague")) {
      if (
        eventValues?.RLTournamentId === "" ||
        eventValues?.RLTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
      if (eventValues?.status == "finished") {
        if (
          eventValues?.winnerCode &&
          eventValues?.homeScore &&
          eventValues?.awayScore
        ) {
          if (
            eventValues?.winnerCode == 1 &&
            Number(eventValues?.homeScore) <= Number(eventValues?.awayScore)
          ) {
            flag = false;
            this.setState({
              errorScore: "Home team score should be more than Away team score",
            });
          } else if (
            eventValues?.winnerCode == 2 &&
            Number(eventValues?.homeScore) >= Number(eventValues?.awayScore)
          ) {
            flag = false;
            this.setState({
              errorScore: "Away team score should be more than Home team score",
            });
          } else if (
            eventValues?.winnerCode == 3 &&
            Number(eventValues?.homeScore) != Number(eventValues?.awayScore)
          ) {
            flag = false;
            this.setState({
              errorScore: "Home team score and Away team score should be same",
            });
          } else {
            this.setState({
              errorScore: "",
            });
          }
        } else if (!Boolean(eventValues?.winnerCode)) {
          flag = false;
          this.setState({
            errorScore: "Winner team required",
          });
        } else if (!Boolean(eventValues?.homeScore)) {
          flag = false;
          this.setState({
            errorScore: "Home team score required",
          });
        } else if (!Boolean(eventValues?.awayScore)) {
          flag = false;
          this.setState({
            errorScore: "Away team score required",
          });
        } else {
          this.setState({
            errorScore: "",
          });
        }
      }
    } else {
      if (
        eventValues?.RLTournamentId === "" ||
        eventValues?.RLTournamentId === null
      ) {
        flag = false;
        this.setState({
          errorTournament: "This field is mandatory",
        });
      } else {
        this.setState({
          errorTournament: "",
        });
      }
    }

    return flag;
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      const { eventValues, stepperCount, roundDisplayName } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      let teamsId = eventValues?.OutrightsTeam?.map((item) => {
        return item?.value;
      });

      console.log(eventValues, "eventValues");

      let payload = {
        eventName: eventValues?.eventName,
        awayTeamId: eventValues?.awayTeamId ? eventValues?.awayTeamId : null,
        homeTeamId: eventValues?.homeTeamId ? eventValues?.homeTeamId : null,
        startTime: eventValues?.startTime,
        // endTime: eventValues?.endTime,
        rapidEventId: eventValues?.rapidEventId,
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props?.match?.path?.includes("boxing")
          ? 6
          : this.props?.match?.path?.includes("mma")
          ? 5
          : this.props?.match?.path?.includes("soccer")
          ? 8
          : 14,
        // round: stepperCount,
        status: eventValues?.status,
        winnerCode: eventValues?.winnerCode,
      };
      if (this.props.match.path?.includes("cricket")) {
        payload = {
          ...payload,
          CricketTournamentId: eventValues?.CricketTournamentId,
          round: stepperCount || stepperCount == 0 ? stepperCount : null,
          displayName: roundDisplayName,
          eventType: eventValues?.isEventType,
          numberOfWeeks: eventValues?.isWeekType,
        };
      } else if (this.props.match.path?.includes("basketball")) {
        payload = {
          ...payload,
          NBATournamentId: eventValues?.NBATournamentId,
        };
      } else if (this.props.match.path?.includes("afl")) {
        payload = {
          ...payload,
          AFLTournamentId: eventValues?.AFLTournamentId,
        };
      } else if (this.props.match.path?.includes("australianrules")) {
        payload = {
          ...payload,
          ARTournamentId: eventValues?.ARTournamentId,
          round: stepperCount || stepperCount == 0 ? stepperCount : null,
          displayName: roundDisplayName,
          homeScore: eventValues?.homeScore
            ? {
                current: Number(eventValues?.homeScore),
                display: Number(eventValues?.homeScore),
              }
            : {},
          awayScore: eventValues?.awayScore
            ? {
                current: Number(eventValues?.awayScore),
                display: Number(eventValues?.awayScore),
              }
            : {},
          eventType: eventValues?.isEventType,
          numberOfWeeks: eventValues?.isWeekType,
        };
      } else if (this.props.match.path?.includes("golf")) {
        payload = {
          ...payload,
          GolfTournamentId: eventValues?.GolfTournamentId,
          teamIds: teamsId,
          outrights: eventValues?.eventType === "OUTRIGHTS" ? true : false,
        };
      } else if (this.props.match.path?.includes("tennis")) {
        payload = {
          ...payload,
          TennisTournamentId: eventValues?.TennisTournamentId,
        };
      } else if (this.props.match.path?.includes("baseball")) {
        payload = {
          ...payload,
          BaseballTournamentId: eventValues?.BaseballTournamentId,
        };
      } else if (this.props.match.path?.includes("icehockey")) {
        payload = {
          ...payload,
          IceHockeyTournamentId: eventValues?.IceHockeyTournamentId,
        };
      } else if (this.props.match.path?.includes("boxing")) {
        payload = {
          ...payload,
          BoxingTournamentId: eventValues?.BoxingTournamentId,
        };
      } else if (this.props.match.path?.includes("mma")) {
        payload = {
          ...payload,
          MMATournamentId: eventValues?.MMATournamentId,
        };
      } else if (this.props.match.path?.includes("soccer")) {
        payload = {
          ...payload,
          SoccerTournamentId: eventValues?.SoccerTournamentId,
        };
      } else {
        payload = {
          ...payload,
          RLTournamentId: eventValues?.RLTournamentId,
          round: stepperCount || stepperCount == 0 ? stepperCount : null,
          displayName: roundDisplayName,
          homeScore: eventValues?.homeScore
            ? {
                current: Number(eventValues?.homeScore),
                display: Number(eventValues?.homeScore),
              }
            : {},
          awayScore: eventValues?.awayScore
            ? {
                current: Number(eventValues?.awayScore),
                display: Number(eventValues?.awayScore),
              }
            : {},
          eventType: eventValues?.isEventType,
          numberOfWeeks: eventValues?.isWeekType,
        };
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";
      try {
        const { status } = await axiosInstance.post(
          `${passApi}/event`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            eventValues: {
              ...eventValues,
              ARTournamentName: "",
            },
          });
          this.fetchAllEvent(
            this.state.offset,
            this.state?.selectTeam,
            this.state?.filterDate,
            this.state?.selectTournament,
            this.state?.search
          );
          //   this.setActionMessage(
          //     true,
          //     "Success",
          //     `Country variation Created Successfully`
          //   );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
        }
      } catch {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
        });
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      const { eventValues, stepperCount, roundDisplayName } = this.state;
      this.setState({ isLoading: true, isEditMode: true });
      let teamsId = eventValues?.OutrightsTeam?.map((item) => {
        return item?.value;
      });


      let payload = {
        eventName: eventValues?.eventName,
        awayTeamId: eventValues?.awayTeamId ? eventValues?.awayTeamId : null,
        homeTeamId: eventValues?.homeTeamId ? eventValues?.homeTeamId : null,
        startTime: eventValues?.startTime,
        seasonId: eventValues?.seasonId,
        // endTime: eventValues?.endTime,
        rapidEventId: eventValues?.rapidEventId,
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
          ? 13
          : this.props.match.path?.includes("basketball")
          ? 10
          : this.props.match.path?.includes("afl")
          ? 15
          : this.props.match.path?.includes("australianrules")
          ? 9
          : this.props.match.path?.includes("golf")
          ? 16
          : this.props.match.path?.includes("tennis")
          ? 7
          : this.props.match.path?.includes("baseball")
          ? 11
          : this.props.match.path?.includes("icehockey")
          ? 17
          : this.props?.match?.path?.includes("boxing")
          ? 6
          : this.props?.match?.path?.includes("mma")
          ? 5
          : this.props?.match?.path?.includes("soccer")
          ? 8
          : 14,
        status: eventValues?.status,
        winnerCode: eventValues?.winnerCode,
      };
      if (this.props.match.path?.includes("cricket")) {
        payload = {
          ...payload,
          CricketTournamentId: eventValues?.CricketTournamentId,
          round: stepperCount || stepperCount == 0 ? stepperCount : null,
          displayName: roundDisplayName,
          eventType: eventValues?.isEventType,
          numberOfWeeks: eventValues?.isWeekType,
        };
      } else if (this.props.match.path?.includes("basketball")) {
        payload = {
          ...payload,
          NBATournamentId: eventValues?.NBATournamentId,
        };
      } else if (this.props.match.path?.includes("afl")) {
        payload = {
          ...payload,
          AFLTournamentId: eventValues?.AFLTournamentId,
        };
      } else if (this.props.match.path?.includes("australianrules")) {
        payload = {
          ...payload,
          ARTournamentId: eventValues?.ARTournamentId,
          round: stepperCount || stepperCount == 0 ? stepperCount : null,
          displayName: roundDisplayName,
          homeScore: eventValues?.homeScore
            ? {
                current: Number(eventValues?.homeScore),
                display: Number(eventValues?.homeScore),
              }
            : {},
          awayScore: eventValues?.awayScore
            ? {
                current: Number(eventValues?.awayScore),
                display: Number(eventValues?.awayScore),
              }
            : {},
          eventType: eventValues?.isEventType,
          numberOfWeeks: eventValues?.isWeekType,
        };
      } else if (this.props.match.path?.includes("golf")) {
        payload = {
          ...payload,
          GolfTournamentId: eventValues?.GolfTournamentId,
          teamIds: teamsId,
          outrights: eventValues?.eventType === "OUTRIGHTS" ? true : false,
        };
      } else if (this.props.match.path?.includes("tennis")) {
        payload = {
          ...payload,
          TennisTournamentId: eventValues?.TennisTournamentId,
        };
      } else if (this.props.match.path?.includes("baseball")) {
        payload = {
          ...payload,
          BaseballTournamentId: eventValues?.BaseballTournamentId,
        };
      } else if (this.props.match.path?.includes("icehockey")) {
        payload = {
          ...payload,
          IceHockeyTournamentId: eventValues?.IceHockeyTournamentId,
        };
      } else if (this.props.match.path?.includes("boxing")) {
        payload = {
          ...payload,
          BoxingTournamentId: eventValues?.BoxingTournamentId,
        };
      } else if (this.props.match.path?.includes("mma")) {
        payload = {
          ...payload,
          MMATournamentId: eventValues?.MMATournamentId,
        };
      } else if (this.props.match.path?.includes("soccer")) {
        payload = {
          ...payload,
          SoccerTournamentId: eventValues?.SoccerTournamentId,
        };
      } else {
        payload = {
          ...payload,
          RLTournamentId: eventValues?.RLTournamentId,
          round: stepperCount || stepperCount == 0 ? stepperCount : null,
          displayName: roundDisplayName,
          homeScore: eventValues?.homeScore
            ? {
                current: Number(eventValues?.homeScore),
                display: Number(eventValues?.homeScore),
              }
            : {},
          awayScore: eventValues?.awayScore
            ? {
                current: Number(eventValues?.awayScore),
                display: Number(eventValues?.awayScore),
              }
            : {},
          eventType: eventValues?.isEventType,
          numberOfWeeks: eventValues?.isWeekType,
        };
      }
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
        ? "nba"
        : this.props.match.path?.includes("afl")
        ? "afl"
        : this.props.match.path?.includes("australianrules")
        ? "ar"
        : this.props.match.path?.includes("golf")
        ? "golf"
        : this.props.match.path?.includes("tennis")
        ? "tennis"
        : this.props.match.path?.includes("baseball")
        ? "baseball"
        : this.props.match.path?.includes("icehockey")
        ? "icehockey"
        : this.props.match.path?.includes("boxing")
        ? "boxing"
        : this.props.match.path?.includes("mma")
        ? "mma"
        : this.props.match.path?.includes("soccer")
        ? "soccer"
        : "rls";
      try {
        const { status } = await axiosInstance.put(
          `${passApi}/event/${this.state.eventValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            eventValues: {
              ...eventValues,
              ARTournamentName: "",
            },
          });
          this.fetchAllEvent(
            this.state.offset,
            this.state?.selectTeam,
            this.state?.filterDate,
            this.state?.selectTournament,
            this.state?.search
          );
          //   this.setActionMessage(
          //     true,
          //     "Success",
          //     `Country variation Edited Successfully`
          //   );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
        }
      } catch {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
        });
      }
    }
  };

  async fetchAllCategory(CategoryPage, type) {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/category?limit=20&offset=${CategoryPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/category?limit=20&offset=${CategoryPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/category?limit=20&offset=${CategoryPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/category?limit=20&offset=${CategoryPage}`
      : `rls/category?limit=20&offset=${CategoryPage}&SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        let count = data?.result?.count / 20;
        let newdata = [];
        let categories = data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        if (type === "ExternalCategory") {
          let filterdata = newdata?.filter((item) => item?.value !== 0);
          let mergeData = _.unionBy(
            this.state?.externalCategoryData,
            filterdata
          );
          const sortedData = mergeData?.sort((a, b) => {
            return a?.label.localeCompare(b?.label);
          });
          let alldatas = sortedData?.unshift({
            label: "All Categories",
            value: 0,
          });
          let finalData = _.uniqBy(sortedData, function (e) {
            return e.value;
          });
          this.setState({
            externalCategoryData: finalData,
            categoryCount: Math.ceil(count),
          });
        }
      } else {
      }
    } catch (error) {}
  }

  handleOnScrollBottomCategory = (e, type) => {
    let {
      categoryCount,
      CategoryPage,
      isCategorySearch,
      searchCategoryCount,
      searchCategoryPage,
    } = this.state;
    if (
      isCategorySearch !== "" &&
      searchCategoryCount !== Math.ceil(searchCategoryPage / 20 + 1)
    ) {
      this.handleCategoryInputChange(searchCategoryPage + 20, isCategorySearch);
      this.setState({
        searchCategoryPage: searchCategoryPage + 20,
      });
    } else {
      if (
        categoryCount !==
          (categoryCount == 1 ? 1 : Math.ceil(CategoryPage / 20)) &&
        isCategorySearch == ""
      ) {
        this.fetchAllCategory(CategoryPage + 20, type);
        this.setState({
          CategoryPage: CategoryPage + 20,
        });
      }
    }
  };
  handleCategoryInputChange = _.debounce((page, value) => {
    // if (value === "") return;
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/category?limit=20&offset=${page}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/category?limit=20&offset=${page}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/category?limit=20&offset=${page}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/category?limit=20&offset=${page}&search=${value}`
      : `rls/category?limit=20&offset=${page}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchCategory, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Categories",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchCategory: finalData,
          searchCategoryCount: Math.ceil(count),
          isCategorySearch: value,
        });
      }
    });
  }, 300);

  handleSelectCategoryChange = (value) => {
    this.setState({
      selectCategory: value,
      selectTournament: "",
      externalTournamentData: [],
      externalTournamentCount: 0,
      searchExternalTournament: [],
      searchExternalTournamentCount: 0,
    });
    this.fetchAllExternalTournament(0, value);
  };

  async fetchAllTournament(TournamentPage) {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/tournament?limit=20&offset=${TournamentPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/tournament?limit=20&offset=${TournamentPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/tournament?limit=20&offset=${TournamentPage}`
      : `rls/tournament?limit=20&offset=${TournamentPage}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let tournament = data?.result?.rows?.map((item) => {
        newdata.push({
          label: this.props.match.path?.includes("basketball")
            ? item?.name + " " + item?.NBACategory?.name
            : item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.TournamentData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      finalData = finalData?.filter((item) => item?.value !== 0);
      this.setState({
        TournamentData: finalData,
        TournamentCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomTournament = (e, type) => {
    let {
      TournamentCount,
      TournamentPage,
      isTournamentSearch,
      searchTournamentCount,
      searchTournamentPage,
    } = this.state;
    if (
      isTournamentSearch !== "" &&
      searchTournamentCount !== Math.ceil(searchTournamentPage / 20 + 1)
    ) {
      this.handleTournamentInputChange(
        searchTournamentPage + 20,
        isTournamentSearch
      );
      this.setState({
        searchTournamentPage: searchTournamentPage + 20,
      });
    } else {
      if (
        TournamentCount !==
          (TournamentCount == 1 ? 1 : Math.ceil(TournamentPage / 20)) &&
        isTournamentSearch == ""
      ) {
        this.fetchAllTournament(TournamentPage + 20);
        this.setState({
          TournamentPage: TournamentPage + 20,
        });
      }
    }
  };
  handleTournamentInputChange = (TournamentPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/tournament?limit=20&offset=${TournamentPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/tournament?limit=20&offset=${TournamentPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/tournament?limit=20&offset=${TournamentPage}&search=${value}`
      : `rls/tournament?limit=20&offset=${TournamentPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: this.props.match.path?.includes("basketball")
              ? item?.name + " " + item?.NBACategory?.name
              : item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchTournament, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTournament: finalData,
          searchTournamentCount: Math.ceil(count),
          isTournamentSearch: value,
        });
      }
    });
  };
  fetchSelectedTournament = async (id, TournamentId) => {
    this.setState({
      isTournamentLoading: true,
    });
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/event/${id}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/event/${id}?SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/event/${id}?SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/event/${id}`
      : this.props.match.path?.includes("afl")
      ? `afl/event/${id}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/event/${id}`
      : this.props.match.path?.includes("golf")
      ? `golf/event/${id}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/event/${id}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/event/${id}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/event/${id}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/event/${id}`
      : this.props.match.path?.includes("mma")
      ? `mma/event/${id}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/event/${id}`
      : `rls/event/${id}?SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(`${passApi}`);
      if (status === 200) {
        let seletedTournament = [
          {
            label: this.props.match.path?.includes("cricket")
              ? data?.result?.CricketTournament?.name
              : this.props.match.path?.includes("basketball")
              ? data?.result?.NBATournament?.name +
                " " +
                data?.result?.NBATournament?.NBACategory?.name
              : this.props.match.path?.includes("afl")
              ? data?.result?.AFLTournament?.name
              : this.props.match.path?.includes("australianrules")
              ? data?.result?.ARTournament?.name
              : this.props.match.path?.includes("golf")
              ? data?.result?.GolfTournament?.name
              : this.props.match.path?.includes("tennis")
              ? data?.result?.TennisTournament?.name
              : this.props.match.path?.includes("baseball")
              ? data?.result?.BaseballTournament?.name
              : this.props.match.path?.includes("icehockey")
              ? data?.result?.IceHockeyTournament?.name
              : this.props.match.path?.includes("boxing")
              ? data?.result?.BoxingTournament?.name
              : this.props.match.path?.includes("mma")
              ? data?.result?.MMATournament?.name
              : this.props.match.path?.includes("soccer")
              ? data?.result?.SoccerTournament?.name
              : data?.result?.RLTournament?.name,
            value: TournamentId,
          },
        ];
        this.setState({
          isTournamentLoading: false,
          TournamentData: TournamentId
            ? _.uniqBy(
                [...seletedTournament, ...this.state.TournamentData],
                function (e) {
                  return e.value;
                }
              )
            : this.state.TournamentData,
        });
      } else {
        this.setState({
          isTournamentLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isTournamentLoading: false,
      });
    }
  };
  fetchAllTeam = async (ExternalTeamPage) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ExternalTeamPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ExternalTeamPage}`
      : `rls/team?limit=20&offset=${ExternalTeamPage}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterdata = newdata?.filter((item) => item?.value !== 0);
      let mergeData = _.unionBy(this.state?.externalTeamData, filterdata);
      const sortedData = mergeData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let alldatas = sortedData?.unshift({
        label: "All Teams",
        value: 0,
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        externalTeamData: finalData,
        externalTeamCount: Math.ceil(count),
      });
    }
  };
  handleOnScrollBottomExternalTeam = (e, type) => {
    let {
      externalTeamCount,
      ExternalTeamPage,
      isTeamSearch,
      searchTeamCount,
      searchTeamPage,
    } = this.state;
    if (
      isTeamSearch !== "" &&
      searchTeamCount !== Math.ceil(searchTeamPage / 20 + 1)
    ) {
      this.handleTeamInputChange(searchTeamPage + 20, isTeamSearch);
      this.setState({
        searchTeamPage: searchTeamPage + 20,
      });
    } else {
      if (
        externalTeamCount !== Math.ceil(ExternalTeamPage / 20) &&
        isTeamSearch == ""
      ) {
        this.fetchAllTeam(ExternalTeamPage + 20);
        this.setState({
          ExternalTeamPage: ExternalTeamPage + 20,
        });
      }
    }
  };
  handleTeamInputChange = (ExternalTeamPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ExternalTeamPage}&search=${value}`
      : `rls/team?limit=20&offset=${ExternalTeamPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(this.state?.searchTeam, filterdata);
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Teams",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchTeam: finalData,
          searchTeamCount: Math.ceil(count),
          isTeamSearch: value,
        });
      }
    });
  };
  handleExternalTeamChange = (e) => {
    this.setState({
      selectTeam: e.value,
      currentPage: 1,
    });
    this.fetchAllEvent(
      0,
      e?.value,
      this.state?.filterDate,
      this.state?.selectTournament,
      ""
    );
  };
  fetchAllExternalTournament = async (
    TournamentPage,
    selectedCategory = ""
  ) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/tournament?limit=20&offset=${TournamentPage}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/tournament?limit=20&offset=${TournamentPage}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("afl")
      ? `afl/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("golf")
      ? `golf/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("mma")
      ? `mma/tournament?limit=20&offset=${TournamentPage}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/tournament?limit=20&offset=${TournamentPage}${`&categoryId=${
          selectedCategory === 0 ? "" : selectedCategory
        }`}`
      : `rls/tournament?limit=20&offset=${TournamentPage}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: this.props.match.path?.includes("basketball")
            ? item?.name + " " + item?.NBACategory?.name
            : this.props.match.path?.includes("soccer")
            ? item?.name + " ( " + item?.SoccerCategory?.name + ")"
            : item?.name,
          value: item?.id,
        });
      });
      let filterdata = newdata?.filter((item) => item?.value !== 0);
      let mergeData = _.unionBy(this.state?.externalTournamentData, filterdata);
      const sortedData = mergeData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let alldatas = sortedData?.unshift({
        label: "All Tournaments",
        value: 0,
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        externalTournamentData: finalData,
        externalTournamentCount: Math.ceil(count),
      });
    }
  };
  handleOnScrollBottomExternalTournament = (e, type) => {
    let {
      externalTournamentCount,
      ExternalTournamentPage,
      isExternalTournamentSearch,
      searchExternalTournamentCount,
      searchExternalTournamentPage,
      selectCategory,
    } = this.state;
    if (
      isExternalTournamentSearch !== "" &&
      searchExternalTournamentCount !==
        Math.ceil(searchExternalTournamentPage / 20 + 1)
    ) {
      this.handleExternalTournamentInputChange(
        searchExternalTournamentPage + 20,
        isExternalTournamentSearch
      );
      this.setState({
        searchExternalTournamentPage: searchExternalTournamentPage + 20,
      });
    } else {
      if (
        externalTournamentCount !== Math.ceil(ExternalTournamentPage / 20) &&
        isExternalTournamentSearch == ""
      ) {
        this.fetchAllExternalTournament(
          ExternalTournamentPage + 20,
          selectCategory
        );
        this.setState({
          ExternalTournamentPage: ExternalTournamentPage + 20,
        });
      }
    }
  };
  handleExternalTournamentInputChange = (ExternalTournamentPage, value) => {
    const { selectCategory } = this.state;

    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}${`&categoryId=${
          selectCategory === 0 ? "" : selectCategory
        }`}`
      : `rls/tournament?limit=20&offset=${ExternalTournamentPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: this.props.match.path?.includes("basketball")
              ? item?.name + " " + item?.NBACategory?.name
              : this.props.match.path?.includes("soccer")
              ? item?.name + " ( " + item?.SoccerCategory?.name + ")"
              : item?.name,
            value: item?.id,
          });
        });
        let filterdata = newdata?.filter((item) => item?.value !== 0);
        let mergeData = _.unionBy(
          this.state?.searchExternalTournament,
          filterdata
        );
        const sortedData = mergeData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let alldatas = sortedData?.unshift({
          label: "All Tournaments",
          value: 0,
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchExternalTournament: finalData,
          searchExternalTournamentCount: Math.ceil(count),
          isExternalTournamentSearch: value,
        });
      }
    });
  };
  handleExternalTournamentChange = (e) => {
    console.log("objecteeee", e);
    this.setState({
      selectTournament: e.value,
      currentPage: 1,
    });
    this.fetchAllEvent(
      0,
      this.state?.selectTeam,
      this.state?.filterDate,
      e?.value,
      ""
    );
  };
  handleFilterDateChange = (e) => {
    this.setState({
      filterDate: e ? moment(e)?.format("YYYY-MM-DD") : null,
      currentPage: 1,
    });
    this.fetchAllEvent(
      0,
      this.state.selectTeam,
      e ? moment(e)?.format("YYYY-MM-DD") : null,
      this.state?.selectTournament,
      ""
    );
  };

  async fetchAllAwayTeam(Page) {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${Page}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${Page}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${Page}`
      : `rls/team?limit=20&offset=${Page}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let tournament = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.AwayTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      finalData = finalData?.filter((item) => item?.value !== 0);
      this.setState({
        AwayTeamData: finalData,
        AwayTeamCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomAwayTeam = (e, type) => {
    let {
      AwayTeamCount,
      AwayTeamPage,
      isAwayTeamSearch,
      searchAwayTeamCount,
      searchAwayTeamPage,
    } = this.state;
    if (
      isAwayTeamSearch !== "" &&
      searchAwayTeamCount !== Math.ceil(searchAwayTeamPage / 20 + 1)
    ) {
      this.handleAwayTeamInputChange(searchAwayTeamPage + 20, isAwayTeamSearch);
      this.setState({
        searchAwayTeamPage: searchAwayTeamPage + 20,
      });
    } else {
      if (
        AwayTeamCount !==
          (AwayTeamCount == 1 ? 1 : Math.ceil(AwayTeamPage / 20)) &&
        isAwayTeamSearch == ""
      ) {
        this.fetchAllAwayTeam(AwayTeamPage + 20);
        this.setState({
          AwayTeamPage: AwayTeamPage + 20,
        });
      }
    }
  };
  handleAwayTeamInputChange = (Page, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${Page}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${Page}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${Page}&search=${value}`
      : `rls/team?limit=20&offset=${Page}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchAwayTeam, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchAwayTeam: finalData,
          searchAwayTeamCount: Math.ceil(count),
          isAwayTeamSearch: value,
        });
      }
    });
  };
  fetchSelectedAwayTeam = async (id, AwayTeamId) => {
    this.setState({
      isAwayTeamLoading: true,
    });
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team/${AwayTeamId}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team/${AwayTeamId}?SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team/${AwayTeamId}?SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team/${AwayTeamId}`
      : this.props.match.path?.includes("afl")
      ? `afl/team/${AwayTeamId}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team/${AwayTeamId}`
      : this.props.match.path?.includes("golf")
      ? `golf/team/${AwayTeamId}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team/${AwayTeamId}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team/${AwayTeamId}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team/${AwayTeamId}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team/${AwayTeamId}`
      : this.props.match.path?.includes("mma")
      ? `mma/team/${AwayTeamId}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team/${AwayTeamId}`
      : `rls/team/${AwayTeamId}?SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(`${passApi}`);
      if (status === 200) {
        let seletedAwayTeam = [
          {
            label: data?.result?.name,
            value: AwayTeamId,
          },
        ];
        this.setState({
          isAwayTeamLoading: false,
          AwayTeamData: AwayTeamId
            ? _.uniqBy(
                [...seletedAwayTeam, ...this.state.AwayTeamData],
                function (e) {
                  return e.value;
                }
              )
            : this.state.AwayTeamData,
        });
      } else {
        this.setState({
          isAwayTeamLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isAwayTeamLoading: false,
      });
    }
  };

  async fetchAllHomeTeam(Page) {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${Page}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${Page}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${Page}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${Page}`
      : `rls/team?limit=20&offset=${Page}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let tournament = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.HomeTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      finalData = finalData?.filter((item) => item?.value !== 0);
      this.setState({
        HomeTeamData: finalData,
        HomeTeamCount: Math.ceil(count),
      });
    }
  }

  handleOnScrollBottomHomeTeam = (e, type) => {
    let {
      HomeTeamCount,
      HomeTeamPage,
      isHomeTeamSearch,
      searchHomeTeamCount,
      searchHomeTeamPage,
    } = this.state;
    if (
      isHomeTeamSearch !== "" &&
      searchHomeTeamCount !== Math.ceil(searchHomeTeamPage / 20 + 1)
    ) {
      this.handleHomeTeamInputChange(searchHomeTeamPage + 20, isHomeTeamSearch);
      this.setState({
        searchHomeTeamPage: searchHomeTeamPage + 20,
      });
    } else {
      if (
        HomeTeamCount !==
          (HomeTeamCount == 1 ? 1 : Math.ceil(HomeTeamPage / 20)) &&
        isHomeTeamSearch == ""
      ) {
        this.fetchAllHomeTeam(HomeTeamPage + 20);
        this.setState({
          HomeTeamPage: HomeTeamPage + 20,
        });
      }
    }
  };
  handleHomeTeamInputChange = (Page, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${Page}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${Page}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${Page}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${Page}&search=${value}`
      : `rls/team?limit=20&offset=${Page}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchHomeTeam, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchHomeTeam: finalData,
          searchHomeTeamCount: Math.ceil(count),
          isHomeTeamSearch: value,
        });
      }
    });
  };
  fetchSelectedHomeTeam = async (id, HomeTeamId) => {
    this.setState({
      isHomeTeamLoading: true,
    });
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team/${HomeTeamId}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team/${HomeTeamId}?SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team/${HomeTeamId}?SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team/${HomeTeamId}`
      : this.props.match.path?.includes("afl")
      ? `afl/team/${HomeTeamId}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team/${HomeTeamId}`
      : this.props.match.path?.includes("golf")
      ? `golf/team/${HomeTeamId}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team/${HomeTeamId}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team/${HomeTeamId}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team/${HomeTeamId}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team/${HomeTeamId}`
      : this.props.match.path?.includes("mma")
      ? `mma/team/${HomeTeamId}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team/${HomeTeamId}`
      : `rls/team/${HomeTeamId}?SportId=14`;
    try {
      const { status, data } = await axiosInstance.get(`${passApi}`);
      if (status === 200) {
        let seletedHomeTeam = [
          {
            label: data?.result?.name,
            value: HomeTeamId,
          },
        ];
        this.setState({
          isHomeTeamLoading: false,
          HomeTeamData: HomeTeamId
            ? _.uniqBy(
                [...seletedHomeTeam, ...this.state.HomeTeamData],
                function (e) {
                  return e.value;
                }
              )
            : this.state.HomeTeamData,
        });
      } else {
        this.setState({
          isHomeTeamLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isHomeTeamLoading: false,
      });
    }
  };

  fetchModalOutrightsTeam = async (ModalTeamPage, searchvalue) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&search=${searchvalue}&SportId=14`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalOutrightsTeamData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        ModalOutrightsTeamData: finalData,
        ModalOutrightsTeamCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomModalOutrightsTeam = (e, type) => {
    let {
      ModalOutrightsTeamCount,
      ModalOutrightsTeamPage,
      isModalOutrightsTeamSearch,
      searchModalOutrightsTeamCount,
      searchModalOutrightsTeamPage,
    } = this.state;
    if (
      isModalOutrightsTeamSearch !== "" &&
      searchModalOutrightsTeamCount !==
        Math.ceil(searchModalOutrightsTeamPage / 20 + 1)
    ) {
      this.handleModalOutrightsTeamInputChange(
        searchModalOutrightsTeamPage + 20,
        isModalOutrightsTeamSearch
      );
      this.setState({
        searchModalOutrightsTeamPage: searchModalOutrightsTeamPage + 20,
      });
    } else {
      if (
        ModalOutrightsTeamCount !==
          (ModalOutrightsTeamCount == 1
            ? 1
            : Math.ceil(ModalOutrightsTeamPage / 20)) &&
        isModalOutrightsTeamSearch == ""
      ) {
        this.fetchModalOutrightsTeam(
          ModalOutrightsTeamPage + 20,
          isModalOutrightsTeamSearch
        );
        this.setState({
          ModalOutrightsTeamPage: ModalOutrightsTeamPage + 20,
        });
      }
    }
  };
  handleModalOutrightsTeamInputChange = (ModalTeamPage, value) => {
    const passApi = this.props.match.path?.includes("cricket")
      ? `crickets/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("rugbyleague")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=12`
      : this.props.match.path?.includes("rugbyunion")
      ? `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=13`
      : this.props.match.path?.includes("basketball")
      ? `nba/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("afl")
      ? `afl/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("australianrules")
      ? `ar/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("golf")
      ? `golf/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("tennis")
      ? `tennis/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("baseball")
      ? `baseball/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("icehockey")
      ? `icehockey/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("boxing")
      ? `boxing/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("mma")
      ? `mma/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : this.props.match.path?.includes("soccer")
      ? `soccer/team?limit=20&offset=${ModalTeamPage}&search=${value}`
      : `rls/team?limit=20&offset=${ModalTeamPage}&search=${value}&SportId=14`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(
          this.state?.searchModalOutrightsTeam,
          newdata
        );
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchModalOutrightsTeam: finalData,
          searchModalOutrightsTeamCount: Math.ceil(count),
          isModalOutrightsTeamSearch: value,
        });
      }
    });
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleEventModal = () => {
    this.setState({
      isEventModalOpen: !this.state.isEventModalOpen,
      itemToDeleteEvent: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorAwayTeamId: "",
      errorHomeTeamId: "",
      errorStartTime: "",
      errorEndTime: "",
      errorTournament: "",
      errorOutrightsTeam: "",
      errorScore: "",
      errorIsEventType: "",
      TournamentPage: 0,
      eventValues: {
        ...this.state.eventValues,
        status: null,
        winnerCode: null,
        ARTournamentName: "",
        isEventType: null,
        isWeekType: null,
      },
      stepperCount: 0,
      roundDisplayName: "",
    });
  };

  inputModal = (item, type) => () => {
    const { eventValues } = this.state;
    this.fetchAllTournament(0);
    this.fetchAllAwayTeam(0);
    this.fetchAllHomeTeam(0);
    this.fetchModalOutrightsTeam(0, "");
    this.setState({ isInputModalOpen: true });

    let seasonId;
    const sportId = item?.SportId;

    switch (sportId) {
      case 4:
        seasonId = item?.CricketSeasonId;
        break;
      case 9:
        seasonId = item?.ARSeasonId;
        break;
      case 12:
        seasonId = item?.RLSeasonId;
        break;
      default:
        seasonId = item?.RLSeasonId;
        break;
    }

    if (type === "edit") {
      if (this.props.match.path?.includes("cricket")) {
        this.fetchSelectedTournament(item?.id, item?.CricketTournamentId);
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.Eid,
            id: item?.id,
            CricketTournamentId: item?.CricketTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            isEventType: item?.eventType,
            isWeekType: item?.numberOfWeeks,
            seasonId: seasonId,
          },
          isEditMode: true,
          stepperCount: item?.round || item?.round == 0 ? item?.round : null,
          roundDisplayName: item?.displayName,
        });
      } else if (this.props.match.path?.includes("basketball")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.NBATournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            NBATournamentId: item?.NBATournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("afl")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.AFLTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            AFLTournamentId: item?.AFLTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("australianrules")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.ARTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        let awayTeamScore = item?.ARScores?.filter(
          (team) => team?.teamId == item?.awayTeamId
        );
        let homeTeamScore = item?.ARScores?.filter(
          (team) => team?.teamId == item?.homeTeamId
        );
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            ARTournamentId: item?.ARTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            ARTournamentName: item?.ARTournament?.name,
            homeScore: homeTeamScore?.[0]?.score?.current,
            awayScore: awayTeamScore?.[0]?.score?.current,
            isEventType: item?.eventType,
            isWeekType: item?.numberOfWeeks,
            seasonId: seasonId,
          },
          isEditMode: true,
          stepperCount: item?.round || item?.round == 0 ? item?.round : null,
          roundDisplayName: item?.displayName,
        });
      } else if (this.props.match.path?.includes("golf")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.GolfTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        let teams = "";
        teams = item?.GolfOutRightTeams?.map((obj) => {
          return {
            value: obj?.teamId,
            label: obj?.GolfTeam?.name,
          };
        });
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            GolfTournamentId: item?.GolfTournamentId,
            OutrightsTeam: teams,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("tennis")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.TennisTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            TennisTournamentId: item?.TennisTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("baseball")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.BaseballTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            BaseballTournamentId: item?.BaseballTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("icehockey")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.IceHockeyTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            IceHockeyTournamentId: item?.IceHockeyTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("boxing")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.BoxingTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            BoxingTournamentId: item?.BoxingTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("mma")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.MMATournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            MMATournamentId: item?.MMATournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else if (this.props.match.path?.includes("soccer")) {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedTournament(item?.id, item?.SoccerTournamentId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            SoccerTournamentId: item?.SoccerTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            seasonId: seasonId,
          },
          isEditMode: true,
        });
      } else {
        this.fetchSelectedAwayTeam(item?.id, item?.awayTeamId);
        this.fetchSelectedHomeTeam(item?.id, item?.homeTeamId);
        this.fetchSelectedTournament(item?.id, item?.RLTournamentId);
        let awayTeamScore = item?.RLScores?.filter(
          (team) => team?.teamId == item?.awayTeamId
        );
        let homeTeamScore = item?.RLScores?.filter(
          (team) => team?.teamId == item?.homeTeamId
        );
        this.setState({
          eventValues: {
            ...eventValues,
            eventName: item?.eventName,
            awayTeamId: item?.awayTeamId,
            homeTeamId: item?.homeTeamId,
            startTime: item?.startTime,
            // endTime: item?.endTime,
            rapidEventId: item?.rapidEventId,
            id: item?.id,
            RLTournamentId: item?.RLTournamentId,
            eventType: item?.outrights ? "OUTRIGHTS" : "TEAM",
            status: item?.status,
            winnerCode: item?.winnerCode,
            homeScore: homeTeamScore?.[0]?.score?.current,
            awayScore: awayTeamScore?.[0]?.score?.current,
            isEventType: item?.eventType,
            isWeekType: item?.numberOfWeeks,
            seasonId: seasonId,
          },
          isEditMode: true,
          stepperCount: item?.round || item?.round == 0 ? item?.round : null,
          roundDisplayName: item?.displayName,
        });
      }
    } else {
      this.setState({
        eventValues: {
          eventName: "",
          awayTeamId: "",
          homeTeamId: "",
          startTime: new Date(),
          // endTime: new Date(),
          rapidEventId: "",
          id: "",
          RLTournamentId: "",
          CricketTournamentId: "",
          NBATournamentId: "",
          AFLTournamentId: "",
          ARTournamentId: "",
          ARTournamentName: "",
          GolfTournamentId: "",
          TennisTournamentId: "",
          BaseballTournamentId: "",
          IceHockeyTournamentId: "",
          eventType: "TEAM",
          OutrightsTeam: [],
          status: null,
          winnerCode: null,
          awayScore: "",
          homeScore: "",
          isEventType: null,
          isWeekType: null,
        },
        isEditMode: false,
        stepperCount: null,
        roundDisplayName: "",
      });
    }
  };

  inputConfigurationModal = (item) => {
    this.setState({
      eventConfigurationList: item,
      // eventConfigurationScreen: true,
      eventConfigurationModal: true,
    });
    this.fetchEventConfigurationsList(item);
  };

  toggleConfigurationModal = () => {
    this.setState({
      eventConfigurationList: {},
      // eventConfigurationScreen: false,
      eventConfigurationModal: false,
      eventConfigurationDetailsList: [],
    });
  };

  async fetchEventConfigurationsList(items) {
    this.setState({ configurationLoading: true });
    try {
      const { status, data } = await fantasyAxiosInstance.get(
        `/events/event-configure?eventId=${items?.id}&sportId=${items?.SportId}`
      );
      if (status === 200) {
        const response = data?.result;
        this.setState({
          configurationLoading: false,
          eventConfigurationDetailsList: response,
        });
      } else {
        this.setState({
          configurationLoading: false,
        });
      }
    } catch {
      this.setState({
        configurationLoading: false,
      });
    }
  }

  toggleConfigurationModalScreen = () => {
    this.setState({
      eventConfigurationScreen: false,
      eventConfigurationList: {},
      eventConfigurationModal: false,
      eventConfigurationDetailsList: [],
    });
  };

  inputEventConfigurationScreen = (item, type) => {
    this.setState({ eventConfigurationScreen: true });
    if (type === "edit") {
      this.setState({
        modalType: type,
        eventConfigurationId: item?.id,
      });
    } else {
      this.setState({
        modalType: type,
      });
    }
  };

  deleteEventItem = async () => {
    try {
      const passApi = `/events/event-configure/${this.state.itemToDeleteEvent}`;
      const { status } = await fantasyAxiosInstance.delete(passApi);
      if (status === 200) {
        const { eventConfigurationList } = this.state;
        this.setState({ itemToDeleteEvent: null, isEventModalOpen: false });
        this.fetchEventConfigurationsList(eventConfigurationList);
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      } else {
        this.setState({ itemToDeleteEvent: null, isEventModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDeleteEvent: null, isEventModalOpen: false });
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  setItemToDeleteEvent = (id) => () => {
    this.setState({ itemToDeleteEvent: id, isEventModalOpen: true });
  };

  deleteItem = async () => {
    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("rugbyleague")
        ? `rls/event/${this.state.itemToDelete}?SportId=12`
        : this.props.match.path?.includes("rugbyunion")
        ? `rls/event/${this.state.itemToDelete}?SportId=13`
        : this.props.match.path?.includes("basketball")
        ? `nba/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("afl")
        ? `afl/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("australianrules")
        ? `ar/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("golf")
        ? `golf/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("tennis")
        ? `tennis/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("baseball")
        ? `baseball/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("icehockey")
        ? `icehockey/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("boxing")
        ? `boxing/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("mma")
        ? `mma/event/${this.state.itemToDelete}`
        : this.props.match.path?.includes("soccer")
        ? `soccer/event/${this.state.itemToDelete}`
        : `rls/event/${this.state.itemToDelete}?SportId=14`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        const { offset, selectTeam, filterDate } = this.state;
        this.setState({ itemToDelete: null, isModalOpen: false });
        this.fetchAllEvent(
          offset,
          selectTeam,
          filterDate,
          this.state?.selectTournament,
          this.state?.search
        );
        // this.setActionMessage(true, "Success", "Sport Deleted Successfully!");
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      // this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  handleStepper = (value) => {
    const { stepperCount } = this.state;

    if (stepperCount >= 0) {
      return value === "left"
        ? stepperCount > 0
          ? this.setState({ stepperCount: stepperCount - 1 })
          : ""
        : stepperCount !== null
        ? this.setState({ stepperCount: stepperCount + 1 })
        : this.setState({ stepperCount: 0 });
    }
  };
  inputIdentifierModal = (data) => {
    this.setState({
      isIdentifierModal: true,
      isIdentifierEventId: data?.id,
      isIdentifierEventData: data,
    });
  };
  toggleIdentifierModal = () => {
    this.setState({
      isIdentifierModal: false,
      isIdentifierEventId: "",
      isIdentifierEventData: "",
    });
  };
  inputVariationModal = (item) => {
    this.setState({
      isVariationModalOpen: true,
      eventValues: {
        eventName: item?.name,
        id: item?.id,
      },
    });
  };
  toggleVariationModal = () => {
    this.setState({
      isVariationModalOpen: false,
      eventValues: {
        eventName: "",
        id: "",
      },
    });
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleCheckBoxChange = (e, item) => {
    const { value, checked } = e.target;
    const { checkBoxValues, selectedCheckBoxEvent } = this.state;
    if (checked) {
      let checkboxdata = [...checkBoxValues, Number(value)];
      const checkedEvents = [...selectedCheckBoxEvent, item];
      this.setState({
        checkBoxValues: checkboxdata,
        selectedCheckBoxEvent: checkedEvents,
      });
    } else {
      let checkboxdata = checkBoxValues?.filter(
        (element) => element !== Number(value)
      );
      const checkedEvents = selectedCheckBoxEvent?.filter(
        (item) => item?.id !== Number(value)
      );
      this.setState({
        checkBoxValues: checkboxdata,
        selectedCheckBoxEvent: checkedEvents,
      });
    }
  };
  deleteEventFromRound = (evenId) => {
    const { checkBoxValues, selectedCheckBoxEvent } = this.state;
    let checkboxdata = checkBoxValues?.filter(
      (element) => element !== Number(evenId)
    );
    const checkedEvents = selectedCheckBoxEvent?.filter(
      (item) => item?.id !== Number(evenId)
    );
    this.setState({
      checkBoxValues: checkboxdata,
      selectedCheckBoxEvent: checkedEvents,
    });
  };

  handleRoundModal = (data) => {
    this.setState({
      isRoundModal: true,
    });
  };
  toggleRoundModal = () => {
    this.setState({
      isRoundModal: false,
    });
  };
  resetModal = () => {
    this.setState({
      checkBoxValues: [],
      selectedCheckBoxEvent: [],
    });
  };

  handleKeyDown = (event) => {
    var { search, selectTeam, filterDate, selectTournament } = this.state;
    if (event.key === "Enter") {
      this.fetchAllEvent(0, selectTeam, filterDate, selectTournament, search);
      this.setState({ currentPage: 1 });
    }
  };

  getTournamentName(item) {
    const path = this.props.match.path;

    if (path.includes("cricket")) {
      return item?.CricketTournament?.name;
    }

    if (path.includes("basketball")) {
      const nba = item?.NBATournament;
      return nba?.NBACategory === null
        ? nba?.name
        : `${nba?.name} ${nba?.NBACategory?.name}`;
    }

    if (path.includes("afl")) {
      return item?.AFLTournament?.name;
    }

    if (path.includes("australianrules")) {
      return item?.ARTournament?.name;
    }

    if (path.includes("golf")) {
      return item?.GolfTournament?.name;
    }

    if (path.includes("tennis")) {
      return item?.TennisTournament?.name;
    }

    if (path.includes("baseball")) {
      return item?.BaseballTournament?.name;
    }

    if (path.includes("icehockey")) {
      return item?.IceHockeyTournament?.name;
    }

    if (path.includes("boxing")) {
      return item?.BoxingTournament?.name;
    }

    if (path.includes("mma")) {
      return item?.MMATournament?.name;
    }

    if (path.includes("soccer")) {
      return item?.SoccerTournament?.name;
    }

    return item?.RLTournament?.name;
  }

  getCategoryName(item) {
    const path = this.props.match.path;

    if (path.includes("cricket")) {
      return item?.CricketTournament?.CricketCategory?.name;
    }

    if (path.includes("basketball")) {
      const nba = item?.NBATournament;
      return ` ${nba?.NBACategory?.name}`;
    }

    if (path.includes("afl")) {
      return item?.AFLTournament?.AFLCategory?.name;
    }

    if (path.includes("australianrules")) {
      return item?.ARTournament?.ARCategory?.name;
    }

    if (path.includes("golf")) {
      return item?.GolfTournament?.GolfCategory?.name;
    }

    if (path.includes("tennis")) {
      return item?.TennisTournament?.TennisCategory?.name;
    }

    if (path.includes("baseball")) {
      return item?.BaseballTournament?.BaseballCategory?.name;
    }

    if (path.includes("icehockey")) {
      return item?.IceHockeyTournament?.IceHockeyCategory?.name;
    }

    if (path.includes("boxing")) {
      return item?.BoxingTournament?.BoxingCategory?.name;
    }

    if (path.includes("mma")) {
      return item?.MMATournament?.MMACategory?.name;
    }

    if (path.includes("soccer")) {
      return item?.SoccerTournament?.SoccerCategory?.name;
    }

    return item?.RLTournament?.RLCategory?.name;
  }

  goTOLineups = (data) => () => {
    this.props.navigate(`${this.props.match.path}/${data?.id}`);
  };

  render() {
    var {
      isModalOpen,
      isEventModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      eventValues,
      TournamentData,
      TournamentCount,
      TournamentPage,
      EventList,
      EventCount,
      errorName,
      errorAwayTeamId,
      errorHomeTeamId,
      errorStartTime,
      errorEndTime,
      errorTournament,
      errorOutrightsTeam,
      errorScore,
      stepperCount,
      externalTeamData,
      externalTeamCount,
      selectTeam,
      SelectedExternalTeamList,
      ExternalTeamPage,
      isExternalTournamentSearch,
      searchExternalTournament,
      externalTournamentData,
      selectTournament,
      filterDate,
      searchTeam,
      isTeamSearch,
      searchTournament,
      isTournamentSearch,
      AwayTeamData,
      AwayTeamCount,
      AwayTeamPage,
      searchAwayTeam,
      searchAwayTeamCount,
      searchAwayTeamPage,
      isAwayTeamSearch,
      HomeTeamData,
      HomeTeamCount,
      HomeTeamPage,
      searchHomeTeam,
      searchHomeTeamCount,
      searchHomeTeamPage,
      isHomeTeamSearch,
      isTournamentLoading,
      isAwayTeamLoading,
      isHomeTeamLoading,
      selectTournament,
      search,
      isIdentifierModal,
      isIdentifierEventId,
      isIdentifierEventData,
      isVariationModalOpen,
      isModalOutrightsTeamSearch,
      searchModalOutrightsTeam,
      ModalOutrightsTeamData,
      roundDisplayName,
      eventConfigurationList,
      eventConfigurationScreen,
      eventConfigurationModal,
      eventConfigurationDetailsList,
      configurationLoading,
      modalType,
      eventConfigurationId,
      errorIsEventType,
      WeekTypeData,
      selectCategory,
      isCategorySearch,
      externalCategoryData,
      searchCategory,
    } = this.state;
    const pageNumbers = [];

    if (EventCount > 0) {
      for (let i = 1; i <= Math.ceil(EventCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    console.log("this.props.match.path", this.props.match.path);
    return (
      <>
        {!eventConfigurationScreen ? (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              {/* <Paper className="pageWrapper"> */}
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>
                  <Link underline="hover" color="inherit">
                    {this.props.match.path?.includes("cricket")
                      ? "Cricket"
                      : this.props.match.path?.includes("rugbyleague")
                      ? "Rugby League"
                      : this.props.match.path?.includes("rugbyunion")
                      ? "Rugby Union"
                      : this.props.match.path?.includes("basketball")
                      ? "Basketball"
                      : this.props.match.path?.includes("afl")
                      ? "American Football"
                      : this.props.match.path?.includes("australianrules")
                      ? "Australian Rules"
                      : this.props.match.path?.includes("golf")
                      ? "Golf"
                      : this.props.match.path?.includes("tennis")
                      ? "Tennis"
                      : this.props.match.path?.includes("baseball")
                      ? "Baseball"
                      : this.props.match.path?.includes("icehockey")
                      ? "Ice Hockey"
                      : this.props.match.path?.includes("boxing")
                      ? "Boxing"
                      : this.props.match.path?.includes("mma")
                      ? "Mixed Martial Arts"
                      : this.props.match.path?.includes("soccer")
                      ? "Soccer"
                      : "Rugby Union Sevens"}
                  </Link>
                  <Typography className="active_p">Event</Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={3}>
                  <Typography variant="h1" align="left">
                    Event
                  </Typography>
                </Grid>
                <Grid item xs={9} className="admin-filter-wrap">
                  {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "20%", marginTop: "0px" }}
              >
                <option value="">Select Category</option>
                {CategoryData?.length > 0 &&
                  CategoryData?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.categoryName}
                    </option>
                  ))}
              </SelectBox> */}
                  {this.props.match.path?.includes("soccer") && (
                    <Select
                      className="React teamsport-select external-select"
                      classNamePrefix="select"
                      placeholder="Select Category"
                      onMenuScrollToBottom={(e) =>
                        this.handleOnScrollBottomCategory(e, "ExternalCategory")
                      }
                      onInputChange={(e) =>
                        this.handleCategoryInputChange(0, e)
                      }
                      value={
                        isCategorySearch
                          ? searchCategory?.find((item) => {
                              return item?.value == selectCategory;
                            })
                          : externalCategoryData?.find((item) => {
                              return item?.value == selectCategory;
                            })
                      }
                      onChange={(e) =>
                        this.handleSelectCategoryChange(e?.value)
                      }
                      options={
                        isCategorySearch ? searchCategory : externalCategoryData
                      }
                    />
                  )}
                  <Select
                    className="React teamsport-select external-select"
                    classNamePrefix="select"
                    placeholder="All Tournaments"
                    onMenuScrollToBottom={(e) =>
                      this.handleOnScrollBottomExternalTournament(e)
                    }
                    onInputChange={(e) =>
                      this.handleExternalTournamentInputChange(0, e)
                    }
                    value={
                      isExternalTournamentSearch
                        ? searchExternalTournament?.find((item) => {
                            return item?.value == selectTournament;
                          })
                        : externalTournamentData?.find((item) => {
                            return item?.value == selectTournament;
                          })
                    }
                    onChange={(e) => this.handleExternalTournamentChange(e)}
                    menuPosition="absolute"
                    options={
                      isExternalTournamentSearch
                        ? searchExternalTournament
                        : externalTournamentData
                    }
                  />
                  <Box className="date-time-picker-wrap filter-date-picker">
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DesktopDatePicker
                        variant="inline"
                        inputVariant="outlined"
                        ampm={false}
                        value={
                          filterDate
                            ? typeof filterDate === "string"
                              ? parseISO(filterDate)
                              : filterDate
                            : null
                        }
                        onChange={(e) => this.handleFilterDateChange(e)}
                        autoOk={true}
                        // disableToolbar
                        format="yyyy/MM/dd"
                        placeholder="All"
                        className="date-time-picker"
                        slots={{
                          openPickerIcon: TodayIcon,
                        }}
                        slotProps={{
                          field: {
                            clearable: true,
                          },
                        }}
                      />
                    </LocalizationProvider>
                  </Box>
                  <Select
                    className="React teamsport-select external-select"
                    classNamePrefix="select"
                    placeholder="All Teams"
                    onMenuScrollToBottom={(e) =>
                      this.handleOnScrollBottomExternalTeam(e)
                    }
                    onInputChange={(e) => this.handleTeamInputChange(0, e)}
                    value={
                      isTeamSearch
                        ? searchTeam?.find((item) => {
                            return item?.value == selectTeam;
                          })
                        : externalTeamData?.find((item) => {
                            return item?.value == selectTeam;
                          })
                    }
                    onChange={(e) => this.handleExternalTeamChange(e)}
                    menuPosition="absolute"
                    options={isTeamSearch ? searchTeam : externalTeamData}
                  />

                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      minWidth: "84px",
                    }}
                    onClick={this.inputModal(null, "create")}
                  >
                    Add New
                  </Button>
                </Grid>
                <Grid
                  item
                  xs={12}
                  className="admin-filter-wrap"
                  style={{
                    marginBottom: "10px",
                    justifyContent: "space-between",
                  }}
                >
                  {this.props.match.path?.includes("cricket") &&
                  this.state.selectTournament ? (
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "6px 10px",
                      }}
                      onClick={() => this.handleRoundModal()}
                    >
                      Add To Round
                    </Button>
                  ) : (
                    <span></span>
                  )}
                  <Box className="admin-filter-wrap">
                    <TextField
                      placeholder="Search "
                      size="small"
                      variant="outlined"
                      className="search-field txt-field-class"
                      onKeyDown={(e) => this.handleKeyDown(e)}
                      value={search}
                      onChange={(e) => {
                        this.setState({ search: e.target.value });
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <img src={SearchIcons} alt="icon" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            {search && (
                              <IconButton
                                onClick={() => this.handleClearClick()}
                                edge="end"
                                style={{ minWidth: "unset" }}
                                size="large"
                              >
                                <CancelIcon />
                              </IconButton>
                            )}
                          </InputAdornment>
                        ),
                      }}
                    />
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455c7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                      }}
                      onClick={() => {
                        this.fetchAllEvent(
                          0,
                          selectTeam,
                          filterDate,
                          selectTournament,
                          search
                        );
                        this.setState({ currentPage: 1 });
                      }}
                    >
                      Search
                    </Button>
                  </Box>
                </Grid>
              </Grid>

              {isLoading && <Loader />}
              {!isLoading && EventList?.length === 0 && (
                <p>No Data Available</p>
              )}

              {!isLoading && EventList?.length > 0 && (
                <>
                  <TableContainer component={Paper}>
                    <Table
                      className="listTable"
                      aria-label="simple table"
                      // style={{ minWidth: "max-content" }}
                    >
                      <TableHead className="tableHead-row">
                        <TableRow>
                          {this.props.match.path?.includes("cricket") &&
                          this.state?.selectTournament ? (
                            <TableCell style={{ width: "20px" }}>
                              Select
                            </TableCell>
                          ) : (
                            <></>
                          )}
                          <TableCell>DID</TableCell>
                          {/* <TableCell>rapidEventId</TableCell> */}
                          <TableCell>Event Name</TableCell>

                          {!this.props.match.path?.includes("golf") ? (
                            <>
                              <TableCell>Away Team</TableCell>
                              <TableCell>Home Team</TableCell>
                            </>
                          ) : (
                            <></>
                          )}
                          {this.props.match.path?.includes("rugbyleague") ||
                          this.props.match.path?.includes("rugbyunion") ||
                          this.props.match.path?.includes("australianrules") ||
                          this.props.match.path?.includes("cricket") ? (
                            <TableCell>Round</TableCell>
                          ) : (
                            <></>
                          )}
                          <TableCell>Tournament </TableCell>
                          <TableCell>Category</TableCell>
                          <TableCell style={{ width: "10%" }}>
                            View/Add Identifier
                          </TableCell>
                          {this.props.match.path?.includes("golf") && (
                            <TableCell style={{ width: "10%" }}>
                              Variation
                            </TableCell>
                          )}
                          <TableCell style={{ width: "10%" }}>
                            Start Time
                          </TableCell>
                          <TableCell style={{ width: "10%" }}>
                            Created At
                          </TableCell>

                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {EventList?.map((item) => {
                          return (
                            <TableRow className="table-rows listTable-Row">
                              {this.props.match.path?.includes("cricket") &&
                              this.state?.selectTournament ? (
                                <TableCell>
                                  <Checkbox
                                    className="mz-checkbox"
                                    checked={this.state.checkBoxValues?.includes(
                                      item?.id
                                    )}
                                    value={item?.id}
                                    onChange={(e) =>
                                      this.handleCheckBoxChange(e, item)
                                    }
                                  />
                                </TableCell>
                              ) : (
                                <></>
                              )}
                              <TableCell> {item?.id} </TableCell>
                              {/* <TableCell>{item?.rapidEventId}</TableCell> */}
                              <TableCell>{item?.eventName}</TableCell>

                              {!this.props.match.path?.includes("golf") ? (
                                <>
                                  <TableCell>{item?.awayTeam?.name}</TableCell>
                                  <TableCell>{item?.homeTeam?.name}</TableCell>
                                </>
                              ) : (
                                <></>
                              )}
                              {this.props.match.path?.includes("rugbyleague") ||
                              this.props.match.path?.includes("rugbyunion") ||
                              this.props.match.path?.includes(
                                "australianrules"
                              ) ||
                              this.props.match.path?.includes("cricket") ? (
                                <TableCell>
                                  {" "}
                                  {item?.round ?? "-"}{" "}
                                  {item?.displayName
                                    ? `(${item?.displayName})`
                                    : ""}{" "}
                                </TableCell>
                              ) : (
                                <></>
                              )}
                              <TableCell>
                                {this.getTournamentName(item)}
                              </TableCell>
                              <TableCell>
                                {this.getCategoryName(item)}
                              </TableCell>
                              <TableCell style={{ padding: "0px" }}>
                                <Button
                                  style={{
                                    fontSize: "14px",
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    fontWeight: "400",
                                    textTransform: "none",
                                    padding: "5px 6px",
                                    width: "max-content",
                                  }}
                                  onClick={() => {
                                    this.inputIdentifierModal(item);
                                  }}
                                >
                                  View/Add Identifier
                                </Button>
                              </TableCell>
                              {this.props.match.path?.includes("golf") && (
                                <TableCell>
                                  <Button
                                    variant="contained"
                                    style={{
                                      fontSize: "9px",
                                      backgroundColor: "#4455C7",
                                      color: "#fff",
                                      fontWeight: "400",
                                      textTransform: "none",
                                      padding: "5px 5px",
                                      width: "max-content",
                                    }}
                                    onClick={() => {
                                      this.inputVariationModal(item);
                                    }}
                                  >
                                    Add/Edit variation
                                  </Button>
                                </TableCell>
                              )}
                              <TableCell
                                style={{
                                  textAlign: !item?.startTime ? "center" : "",
                                }}
                              >
                                {item?.startTime
                                  ? moment(item?.startTime).format(
                                      "DD/MM/YYYY hh:mm:ss a"
                                    )
                                  : "-"}
                              </TableCell>
                              <TableCell
                                style={{
                                  textAlign: !item?.createdAt ? "center" : "",
                                }}
                              >
                                {item?.createdAt
                                  ? moment(item?.createdAt).format(
                                      "DD/MM/YYYY hh:mm:ss a"
                                    )
                                  : "-"}
                              </TableCell>

                              <TableCell
                                style={{
                                  display: "flex",
                                  borderBottom: "none",
                                }}
                              >
                                {(this.props.match.path?.includes("cricket") ||
                                  this.props.match.path?.includes(
                                    "rugbyleague"
                                  ) ||
                                  this.props.match.path?.includes(
                                    "australianrules"
                                  ) ||
                                  this.props.match.path?.includes(
                                    "soccer"
                                  )) && (
                                    <>
                                     <Button
                                   onClick={this.goTOLineups(item)}
                                    style={{
                                      cursor: "pointer",
                                      minWidth: "0px",
                                    }}
                                    className="table-btn info-btn"
                                  >
                                    Lineups
                                  </Button>
                                  <Button
                                    onClick={() =>
                                      this.inputConfigurationModal(item)
                                    }
                                    style={{
                                      cursor: "pointer",
                                      minWidth: "0px",
                                    }}
                                    className="table-btn info-btn"
                                  >
                                    configurations
                                  </Button>
                                  </>
                                )}
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  style={{ cursor: "pointer", minWidth: "0px" }}
                                  className="table-btn edit-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  style={{ cursor: "pointer", minWidth: "0px" }}
                                  className="table-btn delete-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  EventCount / rowPerPage > 1 ? false : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />
                              {/* <button
                            className={
                              sports.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              sports.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}
              {/* </Paper> */}

              <ShowModal
                isModalOpen={isModalOpen}
                onClose={this.toggleModal}
                Content="Are you sure you want to delete?"
                onOkayLabel="Yes"
                onOkay={this.deleteItem}
                onCancel={this.toggleModal}
              />

              <Modal
                className="modal modal-input"
                open={isInputModalOpen}
                onClose={this.toggleInputModal}
              >
                <div
                  className={"paper modal-show-scroll"}
                  style={{ position: "relative" }}
                >
                  <h3 className="text-center">
                    {!isEditMode ? "Create New Event" : "Edit Event"}
                  </h3>
                  <CancelIcon
                    className="admin-close-icon"
                    onClick={this.toggleInputModal}
                  />
                  <Grid container className="page-content adminLogin text-left">
                    <Grid item xs={12} className="pageWrapper api-provider">
                      {/* <Paper className="pageWrapper api-provider"> */}
                      {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                      <Grid container>
                        <Grid
                          item
                          xs={12}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label"> Event Name</label>
                          <TextField
                            className="teamsport-textfield eventname"
                            variant="outlined"
                            color="primary"
                            size="small"
                            placeholder="Event Name"
                            value={eventValues?.eventName}
                            onChange={(e) =>
                              this.setState({
                                eventValues: {
                                  ...eventValues,
                                  eventName: e.target.value,
                                },
                              })
                            }
                          />
                          {errorName ? (
                            <p
                              className="errorText"
                              style={{ margin: "-14px 0 0 0" }}
                            >
                              {errorName}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                        <Grid
                          item
                          xs={6}
                          style={{ display: "flex", flexDirection: "column" }}
                          className="teamsport-text"
                        >
                          <label className="modal-label">rapid Event Id</label>
                          <TextField
                            className="teamsport-textfield"
                            variant="outlined"
                            color="primary"
                            size="small"
                            placeholder="rapid Event Id"
                            value={eventValues?.rapidEventId}
                            onChange={(e) =>
                              this.setState({
                                eventValues: {
                                  ...eventValues,
                                  rapidEventId: e.target.value,
                                },
                              })
                            }
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <label className="modal-label"> Tournament </label>
                          <Select
                            className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                            classNamePrefix="select"
                            menuPosition="fixed"
                            placeholder="Tournament"
                            onMenuScrollToBottom={(e) =>
                              this.handleOnScrollBottomTournament(e)
                            }
                            onInputChange={(e) =>
                              this.handleTournamentInputChange(0, e)
                            }
                            isLoading={isTournamentLoading}
                            value={
                              isTournamentSearch
                                ? searchTournament?.find((item) => {
                                    return this.props.match.path?.includes(
                                      "cricket"
                                    )
                                      ? item?.value ==
                                          eventValues?.CricketTournamentId
                                      : this.props.match.path?.includes(
                                          "basketball"
                                        )
                                      ? item?.value ==
                                        eventValues?.NBATournamentId
                                      : this.props.match.path?.includes("afl")
                                      ? item?.value ==
                                        eventValues?.AFLTournamentId
                                      : this.props.match.path?.includes(
                                          "australianrules"
                                        )
                                      ? item?.value ==
                                        eventValues?.ARTournamentId
                                      : this.props.match.path?.includes("golf")
                                      ? item?.value ==
                                        eventValues?.GolfTournamentId
                                      : this.props.match.path?.includes(
                                          "tennis"
                                        )
                                      ? item?.value ==
                                        eventValues?.TennisTournamentId
                                      : this.props.match.path?.includes(
                                          "baseball"
                                        )
                                      ? item?.value ==
                                        eventValues?.BaseballTournamentId
                                      : this.props.match.path?.includes(
                                          "icehockey"
                                        )
                                      ? item?.value ==
                                        eventValues?.IceHockeyTournamentId
                                      : this.props.match.path?.includes(
                                          "boxing"
                                        )
                                      ? item?.value ==
                                        eventValues?.BoxingTournamentId
                                      : this.props.match.path?.includes("mma")
                                      ? item?.value ==
                                        eventValues?.MMATournamentId
                                      : this.props.match.path?.includes(
                                          "soccer"
                                        )
                                      ? item?.value ==
                                        eventValues?.SoccerTournamentId
                                      : item?.value ==
                                        eventValues?.RLTournamentId;
                                  })
                                : TournamentData?.find((item) => {
                                    return this.props.match.path?.includes(
                                      "cricket"
                                    )
                                      ? item?.value ==
                                          eventValues?.CricketTournamentId
                                      : this.props.match.path?.includes(
                                          "basketball"
                                        )
                                      ? item?.value ==
                                        eventValues?.NBATournamentId
                                      : this.props.match.path?.includes("afl")
                                      ? item?.value ==
                                        eventValues?.AFLTournamentId
                                      : this.props.match.path?.includes(
                                          "australianrules"
                                        )
                                      ? item?.value ==
                                        eventValues?.ARTournamentId
                                      : this.props.match.path?.includes("golf")
                                      ? item?.value ==
                                        eventValues?.GolfTournamentId
                                      : this.props.match.path?.includes(
                                          "tennis"
                                        )
                                      ? item?.value ==
                                        eventValues?.TennisTournamentId
                                      : this.props.match.path?.includes(
                                          "baseball"
                                        )
                                      ? item?.value ==
                                        eventValues?.BaseballTournamentId
                                      : this.props.match.path?.includes(
                                          "icehockey"
                                        )
                                      ? item?.value ==
                                        eventValues?.IceHockeyTournamentId
                                      : this.props.match.path?.includes(
                                          "boxing"
                                        )
                                      ? item?.value ==
                                        eventValues?.BoxingTournamentId
                                      : this.props.match.path?.includes("mma")
                                      ? item?.value ==
                                        eventValues?.MMATournamentId
                                      : this.props.match.path?.includes(
                                          "soccer"
                                        )
                                      ? item?.value ==
                                        eventValues?.SoccerTournamentId
                                      : item?.value ==
                                        eventValues?.RLTournamentId;
                                  })
                            }
                            options={
                              isTournamentSearch
                                ? searchTournament
                                : TournamentData
                            }
                            onChange={(e) =>
                              this.props.match.path?.includes("cricket")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      CricketTournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("basketball")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      NBATournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("afl")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      AFLTournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes(
                                    "australianrules"
                                  )
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      ARTournamentId: e?.value,
                                      ARTournamentName: e?.label,
                                    },
                                  })
                                : this.props.match.path?.includes("golf")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      GolfTournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("tennis")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      TennisTournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("baseball")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      BaseballTournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("icehockey")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      IceHockeyTournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("boxing")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      BoxingTournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("mma")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      MMATournamentId: e?.value,
                                    },
                                  })
                                : this.props.match.path?.includes("soccer")
                                ? this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      SoccerTournamentId: e?.value,
                                    },
                                  })
                                : this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      RLTournamentId: e?.value,
                                    },
                                  })
                            }
                          />
                          {errorTournament ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0 0 0" }}
                            >
                              {errorTournament}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                        {this.props.match.path?.includes("golf") ? (
                          <>
                            <Grid
                              item
                              xs={12}
                              className="radio-wrap"
                              style={{ marginBottom: "15px" }}
                            >
                              <FormControl component="fieldset">
                                <label className="modal-label">
                                  {" "}
                                  Event Type{" "}
                                </label>
                                <RadioGroup
                                  aria-label="EventType"
                                  name="EventType"
                                  className="gender"
                                  value={eventValues?.eventType}
                                  onChange={(e) =>
                                    this.setState({
                                      eventValues: {
                                        ...eventValues,
                                        eventType: e.target.value,
                                      },
                                    })
                                  }
                                >
                                  <FormControlLabel
                                    value="TEAM"
                                    control={
                                      <Radio
                                        color="primary"
                                        checked={
                                          eventValues?.eventType === "TEAM"
                                        }
                                      />
                                    }
                                    label="Teams"
                                  />
                                  <FormControlLabel
                                    value="OUTRIGHTS"
                                    control={
                                      <Radio
                                        color="primary"
                                        checked={
                                          eventValues?.eventType === "OUTRIGHTS"
                                        }
                                      />
                                    }
                                    label="Outrights"
                                  />
                                </RadioGroup>
                              </FormControl>
                            </Grid>
                            {eventValues?.eventType === "TEAM" ? (
                              <>
                                <Grid
                                  item
                                  xs={6}
                                  className="teamsport-text"
                                  style={{ marginBottom: "15px" }}
                                >
                                  <label className="modal-label">
                                    {" "}
                                    AwayTeam{" "}
                                  </label>
                                  {/* <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="AwayTeam Id"
                        value={eventValues?.awayTeamId}
                        onChange={(e) =>
                          this.setState({
                            eventValues: {
                              ...eventValues,
                              awayTeamId: e.target.value,
                            },
                          })
                        }
                      /> */}
                                  <Select
                                    className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                                    classNamePrefix="select"
                                    menuPosition="fixed"
                                    placeholder="Away Team"
                                    onMenuScrollToBottom={(e) =>
                                      this.handleOnScrollBottomAwayTeam(e)
                                    }
                                    onInputChange={(e) =>
                                      this.handleAwayTeamInputChange(0, e)
                                    }
                                    isLoading={isAwayTeamLoading}
                                    value={
                                      isAwayTeamSearch
                                        ? searchAwayTeam?.find((item) => {
                                            return (
                                              item?.value ==
                                              eventValues?.awayTeamId
                                            );
                                          })
                                        : AwayTeamData?.find((item) => {
                                            return (
                                              item?.value ==
                                              eventValues?.awayTeamId
                                            );
                                          })
                                    }
                                    options={
                                      isAwayTeamSearch
                                        ? searchAwayTeam
                                        : AwayTeamData
                                    }
                                    onChange={(e) =>
                                      this.setState({
                                        eventValues: {
                                          ...eventValues,
                                          awayTeamId: e?.value,
                                        },
                                      })
                                    }
                                  />
                                  {errorAwayTeamId ? (
                                    <p
                                      className="errorText"
                                      style={{ margin: "0px 0 0 0" }}
                                    >
                                      {errorAwayTeamId}
                                    </p>
                                  ) : (
                                    ""
                                  )}
                                </Grid>
                                <Grid
                                  item
                                  xs={6}
                                  className="teamsport-text"
                                  style={{ marginBottom: "15px" }}
                                >
                                  <label className="modal-label">
                                    {" "}
                                    HomeTeam{" "}
                                  </label>
                                  {/* <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="HomeTeam Id"
                        value={eventValues?.homeTeamId}
                        onChange={(e) =>
                          this.setState({
                            eventValues: {
                              ...eventValues,
                              homeTeamId: e.target.value,
                            },
                          })
                        }
                      /> */}
                                  <Select
                                    className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                                    classNamePrefix="select"
                                    menuPosition="fixed"
                                    placeholder="Home Team"
                                    onMenuScrollToBottom={(e) =>
                                      this.handleOnScrollBottomHomeTeam(e)
                                    }
                                    onInputChange={(e) =>
                                      this.handleHomeTeamInputChange(0, e)
                                    }
                                    isLoading={isHomeTeamLoading}
                                    value={
                                      isHomeTeamSearch
                                        ? searchHomeTeam?.find((item) => {
                                            return (
                                              item?.value ==
                                              eventValues?.homeTeamId
                                            );
                                          })
                                        : HomeTeamData?.find((item) => {
                                            return (
                                              item?.value ==
                                              eventValues?.homeTeamId
                                            );
                                          })
                                    }
                                    options={
                                      isHomeTeamSearch
                                        ? searchHomeTeam
                                        : HomeTeamData
                                    }
                                    onChange={(e) =>
                                      this.setState({
                                        eventValues: {
                                          ...eventValues,
                                          homeTeamId: e?.value,
                                        },
                                      })
                                    }
                                  />
                                  {errorHomeTeamId ? (
                                    <p
                                      className="errorText"
                                      style={{ margin: "0px 0 0 0" }}
                                    >
                                      {errorHomeTeamId}
                                    </p>
                                  ) : (
                                    ""
                                  )}
                                </Grid>
                              </>
                            ) : (
                              <Grid
                                item
                                xs={12}
                                style={{ marginBottom: "15px" }}
                              >
                                <label className="modal-label">
                                  {" "}
                                  Outrights Teams{" "}
                                </label>
                                <Select
                                  className="React teamsport-select teamsport-multiple-select"
                                  classNamePrefix="select"
                                  menuPosition="fixed"
                                  isMulti
                                  onMenuScrollToBottom={(e) =>
                                    this.handleOnScrollBottomModalOutrightsTeam(
                                      e
                                    )
                                  }
                                  onInputChange={(e) =>
                                    this.handleModalOutrightsTeamInputChange(
                                      0,
                                      e
                                    )
                                  }
                                  value={
                                    isModalOutrightsTeamSearch !== ""
                                      ? searchModalOutrightsTeam?.find(
                                          (item) => {
                                            return (
                                              item?.value ===
                                              eventValues?.OutrightsTeam
                                            );
                                          }
                                        )
                                      : eventValues?.OutrightsTeam
                                  }
                                  options={
                                    isModalOutrightsTeamSearch !== ""
                                      ? searchModalOutrightsTeam
                                      : ModalOutrightsTeamData
                                  }
                                  onChange={(e) =>
                                    this.setState({
                                      eventValues: {
                                        ...eventValues,
                                        OutrightsTeam: e,
                                      },
                                    })
                                  }
                                />
                                {errorOutrightsTeam ? (
                                  <p
                                    className="errorText"
                                    style={{ margin: "0px 0 0 0" }}
                                  >
                                    {errorOutrightsTeam}
                                  </p>
                                ) : (
                                  ""
                                )}
                              </Grid>
                            )}
                          </>
                        ) : (
                          <>
                            <Grid
                              item
                              xs={6}
                              className="teamsport-text"
                              style={{ marginBottom: "15px" }}
                            >
                              <label className="modal-label"> AwayTeam </label>
                              {/* <TextField
                  className="teamsport-textfield"
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder="AwayTeam Id"
                  value={eventValues?.awayTeamId}
                  onChange={(e) =>
                    this.setState({
                      eventValues: {
                        ...eventValues,
                        awayTeamId: e.target.value,
                      },
                    })
                  }
                /> */}
                              <Select
                                className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                placeholder="Away Team"
                                onMenuScrollToBottom={(e) =>
                                  this.handleOnScrollBottomAwayTeam(e)
                                }
                                onInputChange={(e) =>
                                  this.handleAwayTeamInputChange(0, e)
                                }
                                isLoading={isAwayTeamLoading}
                                value={
                                  isAwayTeamSearch
                                    ? searchAwayTeam?.find((item) => {
                                        return (
                                          item?.value == eventValues?.awayTeamId
                                        );
                                      })
                                    : AwayTeamData?.find((item) => {
                                        return (
                                          item?.value == eventValues?.awayTeamId
                                        );
                                      })
                                }
                                options={
                                  isAwayTeamSearch
                                    ? searchAwayTeam
                                    : AwayTeamData
                                }
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      awayTeamId: e?.value,
                                    },
                                  })
                                }
                              />
                              {errorAwayTeamId ? (
                                <p
                                  className="errorText"
                                  style={{ margin: "0px 0 0 0" }}
                                >
                                  {errorAwayTeamId}
                                </p>
                              ) : (
                                ""
                              )}
                            </Grid>
                            <Grid
                              item
                              xs={6}
                              className="teamsport-text"
                              style={{ marginBottom: "15px" }}
                            >
                              <label className="modal-label"> HomeTeam </label>
                              {/* <TextField
                  className="teamsport-textfield"
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder="HomeTeam Id"
                  value={eventValues?.homeTeamId}
                  onChange={(e) =>
                    this.setState({
                      eventValues: {
                        ...eventValues,
                        homeTeamId: e.target.value,
                      },
                    })
                  }
                /> */}
                              <Select
                                className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                placeholder="Home Team"
                                onMenuScrollToBottom={(e) =>
                                  this.handleOnScrollBottomHomeTeam(e)
                                }
                                onInputChange={(e) =>
                                  this.handleHomeTeamInputChange(0, e)
                                }
                                isLoading={isHomeTeamLoading}
                                value={
                                  isHomeTeamSearch
                                    ? searchHomeTeam?.find((item) => {
                                        return (
                                          item?.value == eventValues?.homeTeamId
                                        );
                                      })
                                    : HomeTeamData?.find((item) => {
                                        return (
                                          item?.value == eventValues?.homeTeamId
                                        );
                                      })
                                }
                                options={
                                  isHomeTeamSearch
                                    ? searchHomeTeam
                                    : HomeTeamData
                                }
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      homeTeamId: e?.value,
                                    },
                                  })
                                }
                              />
                              {errorHomeTeamId ? (
                                <p
                                  className="errorText"
                                  style={{ margin: "0px 0 0 0" }}
                                >
                                  {errorHomeTeamId}
                                </p>
                              ) : (
                                ""
                              )}
                            </Grid>
                          </>
                        )}
                        <Grid
                          item
                          xs={6}
                          className="date-time-picker-wrap"
                          style={{ marginBottom: "15px" }}
                        >
                          <label className="modal-label"> Start Time </label>
                          <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <MobileDateTimePicker
                              variant="inline"
                              inputVariant="outlined"
                              ampm={false}
                              value={
                                eventValues?.startTime
                                  ? typeof eventValues?.startTime === "string"
                                    ? parseISO(eventValues?.startTime)
                                    : eventValues?.startTime
                                  : null
                              }
                              slotProps={{
                                field: {
                                  placeholder: "YYYY/MM/DD HH:mm",
                                },
                              }}
                              onChange={(e) =>
                                this.setState({
                                  eventValues: {
                                    ...eventValues,
                                    startTime: e,
                                  },
                                })
                              }
                              autoOk={true}
                              format="yyyy/MM/dd HH:mm"
                              className="date-time-picker"
                            />
                          </LocalizationProvider>
                          {errorStartTime ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0 0 0" }}
                            >
                              {errorStartTime}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                        <Grid
                          item
                          xs={6}
                          className="date-time-picker-wrap"
                          style={{ marginBottom: "15px" }}
                        ></Grid>
                        {this.props.match.path?.includes("rugbyleague") ||
                        this.props.match.path?.includes("rugbyunion") ||
                        this.props.match.path?.includes("australianrules") ||
                        this.props.match.path?.includes("cricket") ? (
                          <>
                            <Grid
                              item
                              xs={6}
                              className="stepper-input-wrap"
                              style={{ marginBottom: "15px" }}
                            >
                              <label className="modal-label"> Round </label>
                              <Box className="stepper-input">
                                <Button
                                  className="stepper-input__button"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    this.handleStepper("left");
                                  }}
                                  disabled={stepperCount == 0}
                                >
                                  <LeftArrow />
                                </Button>

                                <div className="stepper-input__content">
                                  Round {stepperCount ?? ""}
                                </div>
                                <Button
                                  className="stepper-input__button"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    this.handleStepper("right");
                                  }}
                                >
                                  <RightArrow />
                                </Button>
                              </Box>
                            </Grid>
                            <Grid
                              item
                              xs={6}
                              style={{
                                display: "flex",
                                flexDirection: "column",
                              }}
                              className="teamsport-text"
                            >
                              <label className="modal-label">
                                Round Display Name
                              </label>
                              <TextField
                                className="teamsport-textfield"
                                variant="outlined"
                                color="primary"
                                size="small"
                                placeholder="Round Display Name"
                                value={roundDisplayName}
                                onChange={(e) =>
                                  this.setState({
                                    roundDisplayName: e.target.value,
                                  })
                                }
                                disabled={!stepperCount}
                              />
                            </Grid>
                          </>
                        ) : (
                          <></>
                        )}
                        {!this.props.match.path?.includes("golf") && (
                          <>
                            <Grid
                              item
                              xs={6}
                              className="national-select"
                              style={{ marginBottom: "15px" }}
                            >
                              <label className="modal-label"> Status </label>
                              <Select
                                className="React teamsport-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                placeholder="Status"
                                value={
                                  eventValues?.status &&
                                  statusData?.find((item) => {
                                    return item?.value === eventValues?.status;
                                  })
                                }
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      status: e.value,
                                      winnerCode: null,
                                      homeScore: "",
                                      awayScore: "",
                                    },
                                    errorScore: "",
                                  })
                                }
                                options={statusData}
                              />
                            </Grid>
                            <Grid
                              item
                              xs={6}
                              className="national-select"
                              style={{ marginBottom: "15px" }}
                            >
                              <label className="modal-label"> Winner </label>
                              <Select
                                className="React teamsport-select"
                                classNamePrefix="select"
                                placeholder="Winner"
                                menuPosition="fixed"
                                value={
                                  eventValues?.winnerCode &&
                                  winnerData?.find((item) => {
                                    return (
                                      item?.value === eventValues?.winnerCode
                                    );
                                  })
                                }
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      winnerCode: e.value,
                                    },
                                  })
                                }
                                isDisabled={eventValues?.status !== "finished"}
                                options={winnerData}
                              />
                            </Grid>
                          </>
                        )}
                        {(this.props.match.path?.includes("rugbyleague") ||
                          this.props.match.path?.includes(
                            "australianrules"
                          )) && (
                          <>
                            <Grid
                              item
                              xs={6}
                              style={{
                                display: "flex",
                                flexDirection: "column",
                              }}
                              className="teamsport-text"
                            >
                              <label className="modal-label">
                                Home Team Score
                              </label>
                              <TextField
                                className="teamsport-textfield"
                                variant="outlined"
                                color="primary"
                                size="small"
                                placeholder="Home Team Score"
                                value={eventValues?.homeScore}
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      homeScore: e.target.value,
                                    },
                                  })
                                }
                                disabled={
                                  !Boolean(eventValues?.status) ||
                                  eventValues?.status == "pending"
                                }
                                onKeyPress={(e) => {
                                  if (!/[0-9]/.test(e.key)) {
                                    e.preventDefault();
                                  }
                                }}
                              />
                            </Grid>
                            <Grid
                              item
                              xs={6}
                              style={{
                                display: "flex",
                                flexDirection: "column",
                              }}
                              className="teamsport-text"
                            >
                              <label className="modal-label">
                                Away Team Score
                              </label>
                              <TextField
                                className="teamsport-textfield"
                                variant="outlined"
                                color="primary"
                                size="small"
                                placeholder="Away Team Score"
                                value={eventValues?.awayScore}
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      awayScore: e.target.value,
                                    },
                                  })
                                }
                                disabled={
                                  !Boolean(eventValues?.status) ||
                                  eventValues?.status == "pending"
                                }
                                onKeyPress={(e) => {
                                  if (!/[0-9]/.test(e.key)) {
                                    e.preventDefault();
                                  }
                                }}
                              />
                            </Grid>
                            {errorScore ? (
                              <p
                                className="errorText"
                                style={{ margin: "-12px 0 0 0" }}
                              >
                                {errorScore}
                              </p>
                            ) : (
                              ""
                            )}
                          </>
                        )}
                        {(this.props.match.path?.includes("cricket") ||
                          this.props.match.path?.includes("rugbyleague") ||
                          this.props.match.path?.includes(
                            "australianrules"
                          )) && (
                          <>
                            <Grid
                              item
                              xs={6}
                              className="national-select"
                              style={{ marginBottom: "15px" }}
                            >
                              <label className="modal-label">
                                {" "}
                                Event Type{" "}
                              </label>
                              <Select
                                className="React teamsport-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                placeholder="Event Type"
                                value={
                                  eventValues?.isEventType &&
                                  EventTypeData?.find((item) => {
                                    return (
                                      item?.value === eventValues?.isEventType
                                    );
                                  })
                                }
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      isEventType: e.value,
                                    },
                                    errorIsEventType: e.value
                                      ? ""
                                      : errorIsEventType,
                                  })
                                }
                                options={EventTypeData}
                              />
                              {errorIsEventType ? (
                                <p
                                  className="errorText"
                                  style={{ margin: "0px 0 0 0" }}
                                >
                                  {errorIsEventType}
                                </p>
                              ) : (
                                ""
                              )}
                            </Grid>
                            <Grid
                              item
                              xs={6}
                              className="national-select"
                              style={{ marginBottom: "15px" }}
                            >
                              <label className="modal-label"> Week Type </label>
                              <Select
                                className="React teamsport-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                placeholder="Week Type"
                                value={
                                  eventValues?.isWeekType &&
                                  WeekTypeData?.find((item) => {
                                    return (
                                      item?.value === eventValues?.isWeekType
                                    );
                                  })
                                }
                                onChange={(e) =>
                                  this.setState({
                                    eventValues: {
                                      ...eventValues,
                                      isWeekType: e.value,
                                    },
                                  })
                                }
                                options={WeekTypeData}
                              />
                            </Grid>
                          </>
                        )}
                      </Grid>
                      <Grid container>
                        <Grid item xs={3}>
                          <div style={{ marginTop: "20px", display: "flex" }}>
                            {!isEditMode ? (
                              <ButtonComponent
                                className="mt-3 admin-btn-green"
                                onClick={this.handleSave}
                                color="primary"
                                value={!isLoading ? "Save" : "Loading..."}
                                disabled={isLoading}
                              />
                            ) : (
                              <ButtonComponent
                                className="mt-3 admin-btn-green"
                                onClick={this.handleUpdate}
                                color="secondary"
                                value={!isLoading ? "Update" : "Loading..."}
                                disabled={isLoading}
                              />
                            )}

                            <ButtonComponent
                              onClick={this.toggleInputModal}
                              className="mr-lr-30 back-btn-modal"
                              value="Back"
                            />
                          </div>
                        </Grid>
                      </Grid>
                      {/* </Paper> */}
                    </Grid>
                  </Grid>
                </div>
              </Modal>
              <Modal
                className="modal modal-input"
                open={isIdentifierModal}
                onClose={this.toggleIdentifierModal}
              >
                <div
                  className={"paper modal-show-scroll"}
                  style={{ position: "relative" }}
                >
                  <h3 className="text-center modal-head">
                    {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                    Identifier Details
                  </h3>
                  <CancelIcon
                    className="admin-close-icon"
                    onClick={this.toggleIdentifierModal}
                  />
                  <CreateIdentifierEvent
                    inputModal={this.toggleIdentifierModal}
                    IdentifierEventData={isIdentifierEventData}
                    IdentifierEventId={isIdentifierEventId}
                    pathName={this.props?.match?.path}
                  />
                </div>
              </Modal>
              <Modal
                className="modal modal-input"
                open={isVariationModalOpen}
                onClose={this.toggleVariationModal}
              >
                <div
                  className={"paper modal-show-scroll"}
                  style={{ position: "relative" }}
                >
                  <h3 className="text-center modal-head">
                    {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                    Variation Details
                  </h3>
                  <CancelIcon
                    className="admin-close-icon"
                    onClick={this.toggleVariationModal}
                  />
                  <CreateEventVariation
                    inputModal={this.toggleVariationModal}
                    eventValues={eventValues}
                    pathName={this.props?.match?.path}
                  />
                </div>
              </Modal>
              <Modal
                className="modal modal-input"
                open={this.state.isRoundModal}
                onClose={this.toggleRoundModal}
              >
                <div
                  className={"paper modal-show-scroll"}
                  style={{ position: "relative" }}
                >
                  <h3 className="text-center modal-head">
                    {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                    Round Details
                  </h3>
                  <CancelIcon
                    className="admin-close-icon"
                    onClick={this.toggleRoundModal}
                  />
                  <RoundModal
                    deleteEventFromRound={this.deleteEventFromRound}
                    pathName={this.props?.match?.path}
                    EventList={this.state.EventList}
                    checkBoxValues={this.state.checkBoxValues}
                    selectedCheckBoxEvent={this.state.selectedCheckBoxEvent}
                    toggleRoundModal={this.toggleRoundModal}
                    resetModal={() => this.resetModal()}
                    listingFunction={() =>
                      this.fetchAllEvent(
                        this.state.offset,
                        this.state?.selectTeam,
                        this.state?.filterDate,
                        this.state?.selectTournament,
                        this.state?.search
                      )
                    }
                    setActionMessage={this.setActionMessage}
                  />
                </div>
              </Modal>

              <Modal
                className="modal modal-input"
                open={eventConfigurationModal}
                onClose={this.toggleConfigurationModal}
              >
                <div
                  className={"paper modal-show-scroll"}
                  style={{ position: "relative" }}
                >
                  <h3 className="text-center modal-head">
                    {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                    Event configurations List
                  </h3>
                  <CancelIcon
                    className="admin-close-icon"
                    onClick={this.toggleConfigurationModal}
                  />
                  <Grid container className="page-content adminLogin text-left">
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "13px 24px 12px",
                        marginLeft: "auto",
                        marginBottom: "10px",
                      }}
                      onClick={() =>
                        this.inputEventConfigurationScreen(null, "create")
                      }
                    >
                      Add
                    </Button>
                    <Grid item xs={12}>
                      <Paper className="pageWrapper api-provider">
                        {messageBox.display && (
                          <ActionMessage
                            message={messageBox.message}
                            type={messageBox.type}
                            styleClass={messageBox.styleClass}
                          />
                        )}
                        {configurationLoading ? (
                          <Box className="message">
                            <Loader />
                          </Box>
                        ) : eventConfigurationDetailsList?.length > 0 ? (
                          <>
                            {" "}
                            <TableContainer>
                              <Table
                                className="listTable"
                                aria-label="simple table"
                              >
                                <TableHead className="tableHead-row">
                                  <TableRow className="table-rows listTable-Row">
                                    <TableCell>Id</TableCell>
                                    <TableCell>competitionName</TableCell>
                                    <TableCell>Type</TableCell>
                                    <TableCell>Action</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody className="table_body">
                                  <TableRow className="table_row">
                                    <TableCell
                                      colSpan={100}
                                      className="table-seprator"
                                    ></TableCell>
                                  </TableRow>
                                  {eventConfigurationDetailsList?.map(
                                    (data) => (
                                      <TableRow>
                                        <TableCell>{data?.id}</TableCell>
                                        <TableCell>
                                          {data?.competitionName}
                                        </TableCell>
                                        <TableCell
                                          style={{
                                            textTransform: "capitalize",
                                          }}
                                        >
                                          {data?.eventType}
                                        </TableCell>
                                        <TableCell>
                                          <Button
                                            onClick={() =>
                                              this.inputEventConfigurationScreen(
                                                data,
                                                "edit"
                                              )
                                            }
                                            style={{
                                              cursor: "pointer",
                                              minWidth: "0px",
                                            }}
                                            className="table-btn edit-btn"
                                          >
                                            Edit
                                          </Button>
                                          <Button
                                            onClick={this.setItemToDeleteEvent(
                                              data?.id
                                            )}
                                            style={{
                                              cursor: "pointer",
                                              minWidth: "0px",
                                            }}
                                            className="table-btn delete-btn"
                                          >
                                            Delete
                                          </Button>
                                        </TableCell>
                                      </TableRow>
                                    )
                                  )}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </>
                        ) : (
                          <Box className="message">
                            No EventConfiguration Data Available
                          </Box>
                        )}
                      </Paper>
                    </Grid>
                  </Grid>
                </div>
              </Modal>

              <ShowModal
                isModalOpen={isEventModalOpen}
                onClose={this.toggleEventModal}
                Content="Are you sure you want to delete?"
                onOkayLabel="Yes"
                onOkay={this.deleteEventItem}
                onCancel={this.toggleEventModal}
              />
            </Grid>
          </Grid>
        ) : (
          <ConfigurationsEvent
            eventConfigurationList={eventConfigurationList}
            inputConfigurationModal={this.inputConfigurationModal}
            toggleConfigurationModal={this.toggleConfigurationModalScreen}
            modalType={modalType}
            eventConfigurationId={eventConfigurationId}
          />
        )}
      </>
    );
  }
}
export default Event;
