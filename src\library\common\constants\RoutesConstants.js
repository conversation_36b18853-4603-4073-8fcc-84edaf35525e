import moment from "moment";

export const Routes = {
  //all racing screen
  AllRacing: `/allsports/racing`,

  //race runner list screen ( Odds Table )
  RunnerDetails: (race, sportId, raceId, trackId, date) =>
    `/allsports/racing/${race}/${sportId}/${raceId}/${trackId}?date=${moment(
      date
    ).format("YYYY-MM-DD")}`,

  //racing tracklist screen
  RacingTracklist: (race, sportId, date) =>
    `/allsports/racing/${race}/${sportId}?date=${moment(date).format(
      "YYYY-MM-DD"
    )}`,

  // Aus race runner list screen ( Odds Table )
  AusRunnerDetails: (race, sportId, raceId, trackId, date) =>
    `/australian-racing/racing/${race}/${sportId}/${raceId}/${trackId}?date=${moment(
      date
    ).format("YYYY-MM-DD")}`,

  // Aus racing tracklist screen
  AusRacingTracklist: (race, sportId, date) =>
    `/australian-racing/racing/${race}/${sportId}?date=${moment(date).format(
      "YYYY-MM-DD"
    )}`,

  // All-racing race runner list screen ( Odds Table )
  AllRunnerDetails: (race, sportId, raceId, trackId, date) =>
    `/all-racing/racing/${race}/${sportId}/${raceId}/${trackId}?date=${moment(
      date
    ).format("YYYY-MM-DD")}`,

  // All-racing racing tracklist screen
  AllRacingTracklist: (race, sportId, date) =>
    `/all-racing/racing/${race}/${sportId}?date=${moment(date).format(
      "YYYY-MM-DD"
    )}`,

  //click from all sports page to any other sports
  otherSportsAllLeague: (sport, id) => `/allsports/${sport}/${id}`,

  //mma fight details screen
  MmaEventDetail: (
    sportName,
    sportId,
    leagueId,
    leagueName,
    parent_event_id,
    id,
    type,
    year,
    month
  ) =>
    `/allsports/${sportName}/${sportId}/${leagueId}/${leagueName}/${parent_event_id}/${id}?type=${type}&year=${year}&month=${month}`,

  //mma card fights screen
  MmaEventCardsFight: (
    sportName,
    sportId,
    leagueId,
    leagueName,
    eventId,
    type,
    year,
    month
  ) =>
    `/allsports/${sportName}/${sportId}/${leagueId}/${leagueName}/${eventId}?type=${type}&year=${year}&month=${month}`,

  //rl ar match details screen
  RlEventDetail: (
    sportName,
    sportId,
    leagueId,
    leagueName,
    id,
    round,
    team,
    year,
    league
  ) =>
    `/allsports/${sportName}/${sportId}/${leagueId}/${leagueName}/${id}${
      round !== undefined ? `?round=${round}` : ""
    }${team ? `&team=${team}&year=${year}&league=${league}` : ""}`,

  //racing tracklist screen
  AusRules: (
    sportName,
    sportId,
    round,
    team,
    year,
    league,
    leagueId

  ) =>
    `/allsports/${sportName}/${sportId}/?team=${team}&year=${year}&league=${league}`,
};
