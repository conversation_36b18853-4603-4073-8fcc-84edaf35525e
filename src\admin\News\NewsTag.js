import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../src/images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { ActionMessage, Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import _ from "lodash";
import Select from "react-select";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../TeamSport/teamsport.scss";

class NewsTag extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      tagValues: {
        tagTitle: "",
        id: "",
      },
      tagList: [],
      tagCount: 0,
      errorTitle: "",
      search: "",
      isMergeModalOpen: false,
      ModalTagCount: 0,
      searchModalTag: [],
      searchModalTagCount: 0,
      searchModalTagPage: 0,
      isModalTagSearch: "",
      ModalTagData: [],
      ModalTagPage: 0,
      ModalChildTagCount: 0,
      searchModalChildTag: [],
      searchModalChildTagCount: 0,
      searchModalChildTagPage: 0,
      isModalChildTagSearch: "",
      ModalChildTagData: [],
      ModalChildTagPage: 0,
      parentTag: "",
      childTag: [],
      createError: "",
    };
  }

  componentDidMount() {
    this.fetchAllTag(0, "");
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllTag(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllTag(0, "");
      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
      });
    }
  }

  async fetchAllTag(page, searchvalue) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      let passApi = `v2/news/admin/tags?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          tagList: data?.result?.data,
          isLoading: false,
          tagCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }
  handalValidate = () => {
    let { tagValues } = this.state;

    let flag = true;
    if (tagValues?.tagTitle?.trim() === "") {
      flag = false;
      this.setState({
        errorTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTitle: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const payload = {
        title: this.state?.tagValues?.tagTitle,
      };
      try {
        const { status, data } = await axiosInstance.post(
          `v2/news/admin/tags`,
          payload
        );
        if (status === 200) {
          console.log("errerr", data);
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllTag(this.state.offset, this?.state?.search);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        title: this.state?.tagValues?.tagTitle,
      };
      try {
        const { status } = await axiosInstance.put(
          `v2/news/admin/tags/${this.state.tagValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllTag(this.state.offset, this?.state?.search);
          this.setActionMessage(
            true,
            "Success",
            `News Tag Updated Successfully`
          );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTitle: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        tagValues: {
          tagTitle: item?.title,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        tagValues: {
          tagTitle: "",
          id: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      const passApi = `v2/news/admin/tags/${this.state.itemToDelete}`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllTag(this.state.offset, this?.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          "News Tag Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, tagList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllTag(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  // merge News Tag Code

  mergeModal = (item) => () => {
    this.setState({
      isMergeModalOpen: true,
    });
    this.fetchModalParentTag(0, "");
    this.fetchModalChildTag(0, "");
    let Tag = "";
    Tag = {
      value: item?.id,
      label: item?.title,
    };
    this.setState({
      parentTag: Tag,
      childTag: [],
    });
  };

  toggleMergeModal = () => {
    this.setState({
      isMergeModalOpen: false,
      createError: "",

      parentTag: "",
      childTag: [],
    });
  };

  fetchModalParentTag = async (ModalTagPage, searchvalue) => {
    const passApi = `v2/news/admin/tags?limit=${this.state.rowPerPage}&offset=${ModalTagPage}&search=${searchvalue}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.data?.map((item) => {
        newdata.push({
          label: item?.title,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalTagData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        ModalTagData: finalData,
        ModalTagCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomModalParentTag = (e, type) => {
    let {
      ModalTagCount,
      ModalTagPage,
      isModalTagSearch,
      searchModalTagCount,
      searchModalTagPage,
    } = this.state;
    if (
      isModalTagSearch !== "" &&
      searchModalTagCount !== Math.ceil(searchModalTagPage / 20 + 1)
    ) {
      this.handleModalParentTagInputChange(
        searchModalTagPage + 20,
        isModalTagSearch
      );
      this.setState({
        searchModalTagPage: searchModalTagPage + 20,
      });
    } else {
      if (
        ModalTagCount !==
          (ModalTagCount == 1 ? 1 : Math.ceil(ModalTagPage / 20)) &&
        isModalTagSearch == ""
      ) {
        this.fetchModalParentTag(ModalTagPage + 20, isModalTagSearch);
        this.setState({
          ModalTagPage: ModalTagPage + 20,
        });
      }
    }
  };
  handleModalParentTagInputChange = (ModalTagPage, value) => {
    const passApi = `v2/news/admin/tags?limit=${this.state.rowPerPage}&offset=${ModalTagPage}&search=${value}`;

    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.data?.map((item) => {
          newdata.push({
            label: item?.title,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchModalTag, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchModalTag: finalData,
          searchModalTagCount: Math.ceil(count),
          isModalTagSearch: value,
        });
      }
    });
  };

  fetchModalChildTag = async (ModalTagPage, searchvalue) => {
    const passApi = `v2/news/admin/tags?limit=${this.state.rowPerPage}&offset=${ModalTagPage}&search=${searchvalue}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.data?.map((item) => {
        newdata.push({
          label: item?.title,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalChildTagData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      }).filter((Track) => Track?.value !== this.state.parentTag?.value);

      this.setState({
        ModalChildTagData: finalData,
        ModalChildTagCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomModalChildTag = (e, type) => {
    let {
      ModalChildTagCount,
      ModalChildTagPage,
      isModalChildTagSearch,
      searchModalChildTagCount,
      searchModalChildTagPage,
    } = this.state;
    if (
      isModalChildTagSearch !== "" &&
      searchModalChildTagCount !== Math.ceil(searchModalChildTagPage / 20 + 1)
    ) {
      this.handleModalChildTagInputChange(
        searchModalChildTagPage + 20,
        isModalChildTagSearch
      );
      this.setState({
        searchModalChildTagPage: searchModalChildTagPage + 20,
      });
    } else {
      if (
        ModalChildTagCount !==
          (ModalChildTagCount == 1 ? 1 : Math.ceil(ModalChildTagPage / 20)) &&
        isModalChildTagSearch == ""
      ) {
        this.fetchModalChildTag(ModalChildTagPage + 20, isModalChildTagSearch);
        this.setState({
          ModalChildTagPage: ModalChildTagPage + 20,
        });
      }
    }
  };
  handleModalChildTagInputChange = (ModalTagPage, value) => {
    const passApi = `v2/news/admin/tags?limit=${this.state.rowPerPage}&offset=${ModalTagPage}&search=${value}`;

    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.data?.map((item) => {
          newdata.push({
            label: item?.title,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchModalTag, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        })?.filter((Track) => Track?.value !== this.state.parentTag?.value);
        this.setState({
          searchModalChildTag: finalData,
          searchModalChildTagCount: Math.ceil(count),
          isModalChildTagSearch: value,
        });
      }
    });
  };

  handalMergeTrack = async () => {
    this.setState({ isLoading: true });
    let payload = {
      newsTagId: this.state?.parentTag?.value,
      childNewsTagId: this.state?.childTag?.map((item) => item?.value),
    };
    try {
      const passApi = `v2/news/admin/mergeNewsTag`;
      const { status, data } = await axiosInstance.post(passApi, payload);
      if (status === 200) {
        this.setActionMessage(true, "Success", data?.result);
        this.setState({
          isLoading: false,
          isMergeModalOpen: false,
        });
        this.fetchAllTag(this.state.offset, this?.state?.search);
      } else {
        console.log("errdata", data);
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ isLoading: false, createError: data?.message });
      }
    } catch (err) {
      console.log("err", err);
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({
        isLoading: false,
        createError: err?.response?.data?.message,
      });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      tagValues,
      tagList,
      tagCount,
      errorTitle,
      errorInitialTitle,
      errorHomeRelation,
      search,
      isMergeModalOpen,
      isModalTagSearch,
      searchModalTag,
      ModalTagData,
      isModalChildTagSearch,
      searchModalChildTag,
      ModalChildTagData,
      createError,
      parentTag,
      childTag,
    } = this.state;
    const pageNumbers = [];

    if (tagCount > 0) {
      for (let i = 1; i <= Math.ceil(tagCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  News
                </Link>
                <Typography className="active_p">News Tags</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={4}>
                <Typography variant="h1" align="left">
                  News Tags
                </Typography>
              </Grid>

              <Grid item xs={8} className="admin-filter-wrap">
                {/* <SelectBox
                onChange={(e) => this.setState({ sportType: e.target.value })}
                style={{ width: "60%" }}
              >
                <option value="">Select Sport Type</option>
                {allSportsType?.length > 0 &&
                  allSportsType?.map((obj, i) => (
                    <option key={i} value={obj?.id}>
                      {obj?.sportType}
                    </option>
                  ))}
              </SelectBox> */}
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllTag(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>

                {/* <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button> */}
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && tagList?.length === 0 && <p>No Data Available</p>}

            {!isLoading && tagList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell>DID</TableCell>
                      <TableCell style={{ width: "25%" }}> Title </TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {tagList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.title}</TableCell>
                          <TableCell>
                            {/* <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button> */}
                            {/* <Button
                              onClick={this.setItemToDelete(item?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button> */}
                            <Button
                              className="table-btn"
                              style={{
                                cursor: "pointer",
                                backgroundColor: "#4455C7",
                                color: "#fff",
                                borderRadius: "8px",
                                textTransform: "uppercase",
                              }}
                              onClick={this.mergeModal(item)}
                            >
                              Merge
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          {/* <button
                            className={
                              tagList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              tagList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={tagCount / rowPerPage > 1 ? false : true}
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                          {/* <button
                            className={
                              tagList.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              tagList.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New News Tag" : "Edit News Tag"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Title </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Tag Title"
                          value={tagValues?.tagTitle}
                          onChange={(e) =>
                            this.setState({
                              tagValues: {
                                ...tagValues,
                                tagTitle: e.target.value,
                              },
                            })
                          }
                        />
                        {errorTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={isMergeModalOpen}
              onClose={this.toggleMergeModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">Merge Tag</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleMergeModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12}>
                    <label className="modal-label"> Parent Tag </label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select mb15 merge-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      onMenuScrollToBottom={(e) =>
                        this.handleOnScrollBottomModalParentTag(e)
                      }
                      onInputChange={(e) =>
                        this.handleModalParentTagInputChange(0, e)
                      }
                      value={
                        isModalTagSearch
                          ? searchModalTag?.find((item) => {
                              return item?.value === parentTag;
                            })
                          : parentTag
                      }
                      options={isModalTagSearch ? searchModalTag : ModalTagData}
                      onChange={(e) =>
                        this.setState({
                          parentTag: e,
                        })
                      }
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <label className="modal-label"> Child Tag </label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select merge-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      isMulti
                      onMenuScrollToBottom={(e) =>
                        this.handleOnScrollBottomModalChildTag(e)
                      }
                      onInputChange={(e) =>
                        this.handleModalChildTagInputChange(0, e)
                      }
                      value={
                        isModalChildTagSearch !== ""
                          ? searchModalChildTag?.find((item) => {
                              return item?.value === childTag;
                            })
                          : childTag
                      }
                      options={
                        isModalChildTagSearch !== ""
                          ? searchModalChildTag
                          : ModalChildTagData
                      }
                      onChange={(e) =>
                        this.setState({
                          childTag: e,
                        })
                      }
                    />
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        className="mt-3 admin-btn-green"
                        onClick={this.handalMergeTrack}
                        color="primary"
                        value={!isLoading ? "Merge" : "Loading..."}
                        disabled={isLoading}
                      />
                    </div>
                    {createError ? (
                      <p
                        className="errorText"
                        style={{ margin: "0px 0 0 0", width: "300px" }}
                      >
                        {createError}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default NewsTag;
