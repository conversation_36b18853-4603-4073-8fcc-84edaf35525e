import React, { Component } from "react";
import { <PERSON>rid, Typo<PERSON>, <PERSON>, <PERSON>readcrum<PERSON>, Button } from "@mui/material";
import { Link } from "react-router-dom";
import axiosInstance from "../../../helpers/Axios";
import moment from "moment";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import { InputAdornment, IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import Select from "react-select";
import ActionMessage from "../../../library/common/components/ActionMessage";

class SyncSeasons extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startYear: null,
      endYear: null,
      isLoading: false,
      isLeagueLoading: false,
      LeagueOption: [],
      selectedLeague: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      errorStartYear: "",
      errorEndYear: "",
    };
  }
  handleStartYear = (year) => {
    this.setState({ startYear: year });
  };
  handleEndYear = (year) => {
    this.setState({ endYear: year });
  };
  handleValidate = () => {
    let { startYear, endYear } = this.state;
    let flag = true;
    if (!startYear) {
      flag = false;
      this.setState({
        errorStartYear: "This field is mandatory",
        isLoading: false,
      });
    } else {
      this.setState({
        errorStartYear: "",
      });
    }
    if (!endYear) {
      flag = false;
      this.setState({
        errorEndYear: "This field is mandatory",
        isLoading: false,
      });
    } else {
      const startYearValue = startYear?.getFullYear();
      const endYearValue = endYear?.getFullYear();

      if (endYearValue < startYearValue) {
        flag = false;
        this.setState({
          errorEndYear: "End year cannot be less than start year",
          isLoading: false,
        });
      } else {
        this.setState({
          errorEndYear: "",
        });
      }
    }
    return flag;
  };

  fetchLeague = async () => {
    this.setState({
      isLeagueLoading: true,
    });
    try {
      const { status, data } = await axiosInstance.get(`/soccer/sync/leagues`);
      if (status === 200) {
        let newdata = [];
        const filterData = data?.data?.leagues;
        let leagues = filterData?.map((item) => {
          newdata.push({
            label:
              item?.name + " " + item?.season + " " + "(" + item?.country + ")",
            value: item?.league_id,
          });
        });
        const sortedData = newdata?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });

        this.setState({
          LeagueOption: sortedData,
          isLeagueLoading: false,
        });
      } else {
        this.setState({
          isLeagueLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isLeagueLoading: false,
      });
    }
  };
  handlePastFixture = async () => {
    if (this.handleValidate()) {
      this.setState({ isLoading: true });
      const { startYear, endYear, selectedProvider, selectedLeague } =
        this.state;
      let payload = {
        startYear: startYear ? startYear.getFullYear().toString() : "2024",
        endYear: endYear ? endYear.getFullYear().toString() : "2025",
        leagueId: Number(selectedLeague),
      };
      try {
        const passApi = this.props?.match?.path?.includes("cricket")
          ? "crickets/sync/season"
          : this.props?.match?.path?.includes("rugbyleague")
          ? "rls/sync/season"
          : this.props?.match?.path?.includes("basketball")
          ? "nba/sync/season"
          : this.props?.match?.path?.includes("afl")
          ? "afl/sync/season"
          : this.props?.match?.path?.includes("australianrules")
          ? "ar/sync/season"
          : this.props?.match?.path?.includes("golf")
          ? "golf/sync/season"
          : this.props?.match?.path?.includes("tennis")
          ? "tennis/sync/season"
          : this.props?.match?.path?.includes("baseball")
          ? "baseball/sync/season"
          : this.props?.match?.path?.includes("icehockey")
          ? "icehockey/sync/season"
          : this.props?.match?.path?.includes("boxing")
          ? "boxing/sync/season"
          : this.props?.match?.path?.includes("mma")
          ? "mma/sync/season"
          : this.props?.match?.path?.includes("soccer")
          ? "soccer/sync/season"
          : "rls/sync/season";
        const { status, data } = await axiosInstance.post(passApi, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            startYear: null,
            endYear: null,
            selectedLeague: null,
          });
          this.setActionMessage(
            true,
            "Success",
            data?.result?.status
              ? "Future Fixture data import Process started"
              : "Right now Future Fixture process already in progress of last selected date so please try again once last process it completed"
          );
        } else {
          this.setState({
            isLoading: false,
            startYear: null,
            endYear: null,
            selectedLeague: null,
          });
        }
      } catch (error) {
        this.setState({
          isLoading: false,
          startYear: null,
          endYear: null,
          selectedLeague: null,
        });
      }
    }
  };
  // setActionMessage = (display = false, type = "", message = "") => {
  //   let setActionMessage = {
  //     display: display,
  //     type: type,
  //     message: message,
  //   };
  //   this.setState({ messageBox: setActionMessage });
  // };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  componentDidMount() {
    if (this.props.match.path.includes("soccer")) {
      this.fetchLeague();
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match.path !== this.props.match.path) {
      this.setState({
        startYear: null,
        endYear: null,
        errorStartYear: "",
        errorEndYear: "",
        selectedLeague: null,
        LeagueOption: [],
        isLeagueLoading: false,
      });

      if (this.props.match.path.includes("soccer")) {
        this.fetchLeague();
      }
    }
  }
  render() {
    const {
      startYear,
      endYear,
      messageBox,
      errorStartYear,
      errorEndYear,
      selectedLeague,
      LeagueOption,
      isLeagueLoading,
    } = this.state;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                    ? "Rugby Union"
                    : this.props.match.path?.includes("basketball")
                    ? "Basketball"
                    : this.props.match.path?.includes("afl")
                    ? "American Football"
                    : this.props.match.path?.includes("australianrules")
                    ? "Australian Rules"
                    : this.props.match.path?.includes("golf")
                    ? "Golf"
                    : this.props.match.path?.includes("tennis")
                    ? "Tennis"
                    : this.props.match.path?.includes("baseball")
                    ? "Baseball"
                    : this.props.match.path?.includes("icehockey")
                    ? "Ice Hockey"
                    : this.props.match.path?.includes("boxing")
                    ? "Boxing"
                    : this.props.match.path?.includes("mma")
                    ? "Mixed Martial Arts"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Rugby Union Sevens"}
                </Link>
                <Typography className="active_p">Sync Seasons</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Sync Seasons
                </Typography>
              </Grid>
              <Grid
                item
                xs={9}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "flex-start",
                }}
                className="admin-fixture-wrap future-fixture-wrap fixture-import-wrap"
              >
                <Box>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      autoOk
                      variant="inline"
                      format="yyyy"
                      views={["year"]}
                      placeholder="Start Year"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      value={
                        startYear
                          ? typeof startYear === "string"
                            ? parseISO(startYear)
                            : startYear
                          : null
                      }
                      slots={{
                        openPickerIcon: TodayIcon,
                      }}
                      slotProps={{
                        field: {
                          placeholder: "Start Year",
                        },
                      }}
                      onChange={(e) => this.handleStartYear(e)}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      className="date-picker-fixture"
                      style={{ margin: "0px 10px 0px 0px" }}
                    />
                  </LocalizationProvider>
                  {errorStartYear ? (
                    <p
                      className="errorText"
                      style={{ margin: "0px 0 0 0", textAlign: "left" }}
                    >
                      {errorStartYear}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>
                <Box>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      autoOk
                      variant="inline"
                      format="yyyy"
                      views={["year"]}
                      placeholder="End Year"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      value={
                        endYear
                          ? typeof endYear === "string"
                            ? parseISO(endYear)
                            : endYear
                          : null
                      }
                      minDate={
                        startYear
                          ? typeof startYear === "string"
                            ? parseISO(startYear)
                            : startYear
                          : null
                      }
                      slots={{
                        openPickerIcon: TodayIcon,
                      }}
                      slotProps={{
                        field: {
                          placeholder: "End Year",
                        },
                      }}
                      onChange={(e) => this.handleEndYear(e)}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      className="date-picker-fixture"
                      style={{ margin: "0px 10px 0px 0px" }}
                    />
                  </LocalizationProvider>
                  {errorEndYear ? (
                    <p
                      className="errorText"
                      style={{ margin: "0px 0 0 0", textAlign: "left" }}
                    >
                      {errorEndYear}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>

                {this.props.match.path.includes("soccer") && (
                  <Select
                    className="React teamsport-select  external-select"
                    classNamePrefix="select"
                    placeholder="Select League"
                    value={LeagueOption?.find((item) => {
                      return item?.value == selectedLeague;
                    })}
                    onChange={(e) =>
                      this.setState({
                        selectedLeague: e.value,
                      })
                    }
                    isLoading={isLeagueLoading}
                    options={LeagueOption}
                    style={{ margin: "0px 10px 0px 0px" }}
                  />
                )}
                <div>
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "8px 15px",
                      minWidth: "130px",
                    }}
                    onClick={() => this.handlePastFixture()}
                  >
                    Sync Seasons
                  </Button>
                </div>
              </Grid>
            </Grid>
            {/* <Grid container direction="row" alignItems="center">
            <Grid item xs={12} style={{ textAlign: "end" }}>
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "10px 15px",
                  marginTop: "5px",
                }}
                // onClick={() => this.handlePastFixture()}
              >
                Clear
              </Button>
            </Grid>
          </Grid> */}
          </Grid>
        </Grid>
      </>
    );
  }
}

export default SyncSeasons;
