import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Typography,
  Breadcrumbs,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import RateReviewIcon from "@mui/icons-material/RateReview";
// import ButtonComponent from "../../library/common/components/Button";
import { Editor } from "react-draft-wysiwyg";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import { Loader, ActionMessage } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import { config } from "../../../helpers/config";
import Pagination from "@mui/material/Pagination";
import SearchIcons from "../../../images/searchIcon.svg";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../../helpers/common";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../../library/common/components/Button";
import FileUploader from "../../../library/common/components/FileUploader";

class OurTeamPositions extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      ourTeamPositions: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      ourteamPositionValues: {
        categoryName: "",
        id: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      ourTeamPositionCount: 0,
      bookKeeperId: "",
      errorName: "",
      search: "",
    };
  }

  componentDidMount() {
    this.fetchAllOurPositionCategory("");
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllOurPositionCategory(this.state?.search);
    }
  }

  async fetchAllOurPositionCategory(searchvalue) {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        // `ourteam/all/category?limit=${rowPerPage}&offset=${page}&name=${searchvalue}`
        `ourteam/all/position?name=${searchvalue}&limit=${rowPerPage}&offset=${offset}`
      );
      if (status === 200) {
        this.setState({
          ourTeamPositions: data?.result,
          isLoading: false,
          ourTeamPositionCount: data?.count,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { ourteamPositionValues } = this.state;
    let flag = true;
    if (ourteamPositionValues?.categoryName?.trim() === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        name: this.state?.ourteamPositionValues?.categoryName.trim(),
      };
      let passApi = "/ourteam/position";
      try {
        const { status, data } = await axiosInstance.post(
          `${passApi}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.fetchAllOurPositionCategory(this.state?.search);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
        });
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        name: this.state?.ourteamPositionValues?.categoryName.trim(),
      };
      let passApi = "/ourteam/position";
      try {
        const { status, data } = await axiosInstance.put(
          `${passApi}/${this.state.ourteamPositionValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.fetchAllOurPositionCategory(this.state?.search);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        ourteamPositionValues: {
          categoryName: item?.name,
          id: item?.id,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        ourteamPositionValues: {
          categoryName: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllOurPositionCategory();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `/ourteam/position/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllOurPositionCategory(this.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          "Our Team Position Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handeModleLoading = (status) => {
    this.setState({
      isLoading: status,
    });
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllOurPositionCategory(search);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      ourTeamPositions,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      ourteamPositionValues,
      errorName,
      ourTeamPositionCount,
      search,
    } = this.state;

    const pageNumbers = [];
    if (ourTeamPositionCount > 0) {
      for (let i = 1; i <= Math.ceil(ourTeamPositionCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper > */}
            {messageBox?.display && (
              <ActionMessage
                message={messageBox?.message}
                type={messageBox?.type}
                styleClass={messageBox?.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Our Team Positions</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Our Team Positions
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllOurPositionCategory(search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && ourTeamPositions?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && ourTeamPositions?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {ourTeamPositions?.map((bookkeeper, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{bookkeeper?.id}</TableCell>
                          <TableCell>
                            <Box>{bookkeeper?.name}</Box>
                          </TableCell>

                          <TableCell>
                            <Box style={{ display: "flex" }}>
                              <Button
                                onClick={this.inputModal(bookkeeper, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={() =>
                                  this.setItemToDelete(bookkeeper.id)
                                }
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                ourTeamPositionCount / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Position" : "Edit Position"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleInputModal()}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">Our Team Position</label>
                      <TextField
                        className="teamsport-textfield eventname"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Our Team Position"
                        value={ourteamPositionValues?.categoryName}
                        onChange={(e) =>
                          this.setState({
                            ourteamPositionValues: {
                              ...ourteamPositionValues,
                              categoryName: e.target.value,
                            },
                          })
                        }
                      />
                      {!ourteamPositionValues?.categoryName && errorName ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorName}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default OurTeamPositions;
