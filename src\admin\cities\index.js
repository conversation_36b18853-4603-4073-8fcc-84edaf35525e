import React from "react";
import CreateCities from "./CreateCities";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Button,
  TextField,
  Box,
  Breadcrumbs,
  Typography,
  InputAdornment,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import { Link } from "react-router-dom";
import { MdKeyboardBackspace } from "react-icons/md";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import ButtonComponent from "../../library/common/components/Button";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
import SearchIcons from "../../images/searchIcon.svg";
// import { showVariations } from "../../helpers/common";

class Cities extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      cities: [],
      allStates: [],
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      searchInput: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      cityCount: 0,
    };
  }

  componentDidMount() {
    this.fetchAllCities();
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllCities();
    }
  }
  async fetchAllCities() {
    let { rowPerPage, offset, searchInput } = this.state;
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.cities +
        `/state/${this.props.match.params.id}?limit=${rowPerPage}&offset=${offset}&search=${searchInput}`
    );
    if (status === 200) {
      this.setState({
        cities: data?.result?.rows,
        isLoading: false,
        cityCount: data?.result?.count,
      });
      // this.fetchAllState();
    }
  }

  // async fetchAllState() {
  //   const { status, data } = await axiosInstance.get(URLS.state);
  //   if (status === 200) {
  //     this.setState({ allStates: data?.result?.rows });
  //   }
  // }

  getState = (id) => {
    let { allStates } = this.state;
    let state = allStates
      .filter((obj) => obj?.id === id)
      .map((object) => object?.state);
    return state;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllCities();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.cities}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllCities();
        });
        this.setActionMessage(true, "Success", "City Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  backToNavigatePage = () => {
    const { countryid } = this.props.match.params;
    this.props.navigate(
      `/countries/states/${countryid}/${this.props.match.params.countryname}`
    );
  };

  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      // offset
    } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, cities } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < cities?.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  render() {
    var {
      cities,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      searchInput,
      cityCount,
      offset,
    } = this.state;
    const pageNumbers = [];
    // searchInput !== "" &&
    //   (cities = cities?.filter(
    //     (obj) =>
    //       obj?.cityName
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase()) ||
    //       obj?.variation
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase())
    //   ));

    let currentPageRow = cities;

    // if (cities?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = cities.slice(indexOfFirstTodo, indexOfLastTodo);

    //   for (let i = 1; i <= Math.ceil(cities.length / rowPerPage); i++) {
    //     pageNumbers.push(i);
    //   }
    // }
    if (cityCount > 0) {
      for (let i = 1; i <= Math.ceil(cityCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Master Data
                </Link>
                <Link underline="hover" color="inherit" to="/countries">
                  Country
                </Link>
                <Link
                  underline="hover"
                  color="inherit"
                  onClick={this.backToNavigatePage}
                >
                  State
                </Link>
                <Typography className="active_p">Cities</Typography>
              </Breadcrumbs>
            </Box>
            <Grid
              container
              direction="row"
              alignItems="center"
              style={{ marginBottom: "10px" }}
            >
              <Grid item xs={4}>
                <Button
                  className="admin-btn-back"
                  onClick={this.backToNavigatePage}
                >
                  <MdKeyboardBackspace />
                </Button>
                {/* <h3
                  className="text-left admin-page-heading"
                  style={{ margin: "5px 0px 0px 5px" }}
                >
                  Cities
                  {` ( ${this.getState(Number(this.props.match.params.id))} )`}
                </h3> */}
                <Typography variant="h1" align="left">
                  Cities({this.props.match.params.stateName})
                  {/* {` ( ${this.getState(Number(this.props.match.params.id))} )`} */}
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{ display: "flex", justifyContent: "flex-end" }}
              >
                <TextField
                  className="textfield-tracks search-track"
                  style={{ width: "478px", color: "#D4D6D8" }}
                  variant="outlined"
                  color="primary"
                  size="small"
                  placeholder="Search City"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                        {/* <SearchIcon /> */}
                      </InputAdornment>
                    ),
                  }}
                  // label="Search"
                  value={searchInput}
                  onChange={(e) =>
                    this.setState({
                      ...this.state.searchInput,
                      searchInput: e.target.value,
                    })
                  }
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "10px 15px",
                    marginLeft: "10px",
                  }}
                  onClick={(e) => this.fetchAllCities(e)}
                >
                  Search
                </Button>
                {/* &nbsp;&nbsp;&nbsp; */}
                <div>
                  {/* <ButtonComponent
                    className="admin-btn-green"
                    onClick={this.inputModal(null, "create")}
                    color="primary"
                    value="Add New"
                  /> */}
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "11px 15px",
                      marginLeft: "10px",
                    }}
                    onClick={this.inputModal(null, "create")}
                  >
                    Add New
                  </Button>
                </div>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && cities?.length === 0 && <p>No Data Available</p>}
            {!isLoading && cities?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>State</TableCell>
                        <TableCell>Cities</TableCell>
                        {/* <TableCell style={{ width: "25%" }}>
                          Variation
                        </TableCell> */}
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((city, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{city.id}</TableCell>
                          <TableCell>
                            {/* {this.getState(city.stateId)} */}
                            {this.props.match.params?.stateName}
                          </TableCell>
                          <TableCell>{city?.cityName}</TableCell>
                          {/* <TableCell>{city.variation}</TableCell> */}
                          <TableCell>
                            {/* <EditIcon
                              onClick={this.inputModal(city.id, "edit")}
                              color="primary"
                              className="mr10 cursor iconBtn admin-btn-green"
                            /> */}
                            <Button
                              onClick={this.inputModal(city?.id, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                            {/* <DeleteOutlineIcon
                              onClick={this.setItemToDelete(city.id)}
                              color="secondary"
                              className="cursor iconBtn admin-btn-orange"
                            /> */}
                            <Button
                              onClick={this.setItemToDelete(city?.id)}
                              className="table-btn delete-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                cities?.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                cities?.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={cityCount > 0 ? false : true}
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                cities?.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                cities?.length / rowPerPage > 1 ? false : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Cities" : "Edit Cities"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateCities
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  stateId={this.props.match.params?.id}
                  countryId={this.props.match.params?.countryid}
                  isEditMode={isEditMode}
                  fetchAllCities={this.afterChangeRefresh}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Cities;
