.min-w {
  min-width: unset !important;
}

.ml-20 {
  margin-left: 20px;
}

.modal-items {
  align-items: center;
}

.file-upload-modal {
  max-width: 599px;
}

.c-pointer {
  cursor: pointer;
  padding-left: 6px;
}

.file-uploader {
  width: 100%;

  .dropzone {
    min-height: 100px;
    border: dashed 1px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    p {
      margin-bottom: 0;
      margin-top: 0;
    }
  }
}

.d-flex {
  display: flex;
  flex-direction: column;
}

.import-user-details {
  .details-status-wrap {
    width: 100%;
    margin-bottom: 12px;

    .label {
      font-size: 16px;
      line-height: 18px;
      text-transform: uppercase;
      font-weight: 600;
      font-family: "Inter", sans-serif;
      margin-bottom: 12px;
    }

    .email-details {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .email {
        font-size: 14px;
        line-height: 16px;
        letter-spacing: 0px;
        font-weight: 500;
        font-family: "Inter", sans-serif;
      }

      .reason {
        font-size: 14px;
        line-height: 16px;
        letter-spacing: 0px;
        font-weight: 400;
        font-family: "Inter", sans-serif;
      }

      .faild {
        color: #f44336;
      }

      .success {
        color: #00833e;
      }

      .warning {
        color: #ff6b00;
      }
    }
  }
}

.user-modal {
  .modal-label {
    display: inline-block;
  }

  .teamsport-dob-wrap {
    .details-runner-picker {
      margin-top: 3px;
      margin-bottom: 15px;
    }
  }

  .cricket-select {
    margin-top: 3px;
    margin-bottom: 15px;
  }

  .admin-btn-green {
    border-radius: 8px;
  }

  .btn-back {
    min-width: 104px;
    border-radius: 8px;
  }
}

.notification-wrap {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  row-gap: 18px;
  width: 100%;
  column-gap: 10px;

  @media (max-width: 1023px) {
    grid-template-columns: auto auto;
  }

  @media (max-width: 799px) {
    width: 100%;
  }

  @media (max-width: 599px) {
    grid-template-columns: auto;
  }

  .parent-checkbox {
    text-align: left;
    width: 100%;

    .p-25 {
      padding-left: 25px;
    }

    .checkmark {
      width: 15px;
      height: 15px;
    }

    .sport-icon-wrap {
      display: flex;
      align-items: center;

      .sports-name {
        color: #003764;
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;

        @media (max-width: 479px) {
          font-size: 11.42px;
          line-height: 14px;
        }
      }

      .sport-icon {
        display: flex;
        padding-left: 3px;

        .storke-svg {
          path {
            stroke: #003764;
          }
        }
      }
    }

    .child-wrap {
      display: flex;
      flex-wrap: wrap;
      column-gap: 9px;
      row-gap: 6px;
    }

    .child-checkbox {
      font-size: 16px;
      line-height: 20px;
      font-weight: 400;
      background-color: #ffffff;
      color: #191919;
      padding: 8px 16px;
      border-radius: 18px;
      cursor: pointer;
      border: 1px solid #191919;

      @media (max-width: 479px) {
        font-size: 11.42px;
        line-height: 14px;
      }
    }

    .active-label {
      background-color: #4b53c5;
      color: #ffffff;
      border: 1px solid #4b53c5;
    }

    .MuiButtonBase-root {
      min-width: auto;
    }
  }
}

.justify-center {
  justify-content: center;
}

.bank-details-modal {
  max-width: 559px !important;

  .mb-8 {
    margin-bottom: 8px;
  }
}