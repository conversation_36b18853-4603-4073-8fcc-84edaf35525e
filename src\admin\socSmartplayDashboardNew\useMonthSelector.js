import { useState, useMemo, useEffect } from "react";

export const monthNames = [
  { label: "Jan", value: 1, valueShort: "Jan", fullName: "January" },
  { label: "Feb", value: 2, valueShort: "Feb", fullName: "February" },
  { label: "Mar", value: 3, valueShort: "Mar", fullName: "March" },
  { label: "Apr", value: 4, valueShort: "Apr", fullName: "April" },
  { label: "May", value: 5, valueShort: "May", fullName: "May" },
  { label: "Jun", value: 6, valueShort: "Jun", fullName: "June" },
  { label: "Jul", value: 7, valueShort: "Jul", fullName: "July" },
  { label: "Aug", value: 8, valueShort: "Aug", fullName: "August" },
  { label: "Sep", value: 9, valueShort: "Sep", fullName: "September" },
  { label: "Oct", value: 10, valueShort: "Oct", fullName: "October" },
  { label: "Nov", value: 11, valueShort: "Nov", fullName: "November" },
  { label: "Dec", value: 12, valueShort: "Dec", fullName: "December" },
];

/**
 * Custom hook to manage month selection logic for dashboard cards.
 * @param {number} selectedYear
 * @param {number} numSelectors - number of month selectors needed
 * @returns { monthOptions, selectedMonths, setSelectedMonth }
 */
export function useMonthSelector(selectedYear, numSelectors) {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;

  const monthOptions = useMemo(() => {
    if (selectedYear === currentYear) {
      return monthNames.slice(0, currentMonth);
    }
    return monthNames;
  }, [selectedYear, currentYear, currentMonth]);

  // Array of selected months for each selector
  const [selectedMonths, setSelectedMonths] = useState(
    Array(numSelectors).fill(null)
  );

  // Set default month on mount and when selectedYear changes
  useEffect(() => {
    const defaultMonth =
      selectedYear === currentYear ? monthNames[currentMonth - 1] : monthNames[0];
    setSelectedMonths(Array(numSelectors).fill(defaultMonth));
  }, [selectedYear, currentYear, currentMonth, numSelectors]);

  // Reset selectedMonth if it becomes invalid
  useEffect(() => {
    setSelectedMonths((prev) =>
      prev.map((m) =>
        m && !monthOptions.find((opt) => opt.value === m.value) ? null : m
      )
    );
  }, [selectedYear, monthOptions, numSelectors]);

  // Helper to set a specific selector's month
  const setSelectedMonth = (idx, value) => {
    setSelectedMonths((prev) => {
      const arr = [...prev];
      arr[idx] = value;
      return arr;
    });
  };

  return { monthOptions, selectedMonths, setSelectedMonth };
} 