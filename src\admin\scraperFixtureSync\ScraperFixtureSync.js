import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Typography,
  Breadcrumbs,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Checkbox,
} from "@mui/material";
import { Editor } from "react-draft-wysiwyg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import { Loader, ActionMessage } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import { config } from "../../helpers/config";
import Pagination from "@mui/material/Pagination";
import SearchIcons from "../../images/searchIcon.svg";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../library/common/components/Button";
import FileUploader from "../../library/common/components/FileUploader";
import moment from "moment";
import Select from "react-select";

const BooleanOption = [
  {
    label: "True",
    value: "true",
  },
  {
    label: "False",
    value: "false",
  },
];
class ScraperFixtureSync extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      ScraperFixtureSync: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      labelValues: {
        id: "",
        name: "",
        server: "",
        providerId: null,
        url: "",
        status: null,
        bookkeeperId: null,
        model: "",
        timezone: "",
        timeInterval: "",
        fixtureLog: null,
        oddLog: null,
        qaCommnet: "",
        proxy: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      ScraperFixtureSyncCount: 0,
      bookKeeperId: "",
      errorName: "",
      errorServer: "",
      errorProviderId: "",
      errorUrl: "",
      errorStatus: "",
      errorBookkeeperId: "",
      errorModel: "",
      errorTimezone: "",
      errorTimeInterval: "",
      errorFixtureLog: "",
      errorOddLog: "",
      search: "",
      bookkeeperList: [],
      apiProviderList: [],
      selectAllChecked: false,
      checkBoxValues: [],
    };
  }

  componentDidMount() {
    this.fetchAllScraperFixtureSync(0, "");
    this.fetchBookKeeper();
    this.fetchApiProvider();
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllScraperFixtureSync(this.state.offset, this.state?.search);
    }
  }

  async fetchAllScraperFixtureSync(page, searchvalue) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `adminNotification/getAllScrapperProvider?limit=${rowPerPage}&offset=${page}&search=${searchvalue}`
      );
      if (status === 200) {
        this.setState({
          ScraperFixtureSync: data?.result,
          isLoading: false,
          ScraperFixtureSyncCount: data?.count,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  fetchBookKeeper = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/bookKeepers`
      );
      if (status === 200) {
        let newdata = [];
        let bookkeeperData = data.result?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        this.setState({
          bookkeeperList: newdata,
        });
      } else {
      }
    } catch (err) {}
  };

  fetchApiProvider = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `apiProviders/apiProvider`
      );
      if (status === 200) {
        let newdata = [];
        let bookkeeperData = data.result?.map((item) => {
          newdata.push({
            label: item?.providerName,
            value: item?.id,
          });
        });
        this.setState({
          apiProviderList: newdata,
        });
      } else {
      }
    } catch (err) {}
  };

  handalValidate = () => {
    let { labelValues, logo } = this.state;

    let flag = true;
    if (!labelValues?.name?.trim()) {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (!labelValues?.server?.trim()) {
      flag = false;
      this.setState({
        errorServer: "This field is mandatory",
      });
    } else {
      this.setState({
        errorServer: "",
      });
    }
    if (!labelValues?.providerId) {
      flag = false;
      this.setState({
        errorProviderId: "This field is mandatory",
      });
    } else {
      this.setState({
        errorProviderId: "",
      });
    }
    if (!labelValues?.url?.trim()) {
      flag = false;
      this.setState({
        errorUrl: "This field is mandatory",
      });
    } else {
      this.setState({
        errorUrl: "",
      });
    }
    if (!labelValues?.status) {
      flag = false;
      this.setState({
        errorStatus: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStatus: "",
      });
    }
    if (!labelValues?.bookkeeperId) {
      flag = false;
      this.setState({
        errorBookkeeperId: "This field is mandatory",
      });
    } else {
      this.setState({
        errorBookkeeperId: "",
      });
    }
    if (!labelValues?.model?.trim()) {
      flag = false;
      this.setState({
        errorModel: "This field is mandatory",
      });
    } else {
      this.setState({
        errorModel: "",
      });
    }
    if (!labelValues?.timezone?.trim()) {
      flag = false;
      this.setState({
        errorTimezone: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTimezone: "",
      });
    }
    if (!labelValues?.timeInterval?.trim()) {
      flag = false;
      this.setState({
        errorTimeInterval: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTimeInterval: "",
      });
    }
    if (!labelValues?.fixtureLog) {
      flag = false;
      this.setState({
        errorFixtureLog: "This field is mandatory",
      });
    } else {
      this.setState({
        errorFixtureLog: "",
      });
    }
    if (!labelValues?.oddLog) {
      flag = false;
      this.setState({
        errorOddLog: "This field is mandatory",
      });
    } else {
      this.setState({
        errorOddLog: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    const { labelValues } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });

      let payload = {
        name: labelValues?.name,
        server: labelValues?.server,
        providerId: labelValues?.providerId,
        url: labelValues?.url,
        status:
          labelValues?.status === "true"
            ? true
            : labelValues?.status === "false"
            ? false
            : null,
        bookkeeperId: labelValues?.bookkeeperId,
        model: Number(labelValues?.model),
        timezone: labelValues?.timezone,
        timeInterval: Number(labelValues?.timeInterval),
        fixtureLog:
          labelValues?.fixtureLog === "true"
            ? true
            : labelValues?.fixtureLog === "false"
            ? false
            : null,
        oddLog:
          labelValues?.oddLog === "true"
            ? true
            : labelValues?.oddLog === "false"
            ? false
            : null,
        qaCommnet: labelValues?.qaCommnet,
        proxy: labelValues?.proxy,
      };
      let passApi = "/adminNotification/createScrapperProvider";
      try {
        const { status, data } = await axiosInstance.post(
          `${passApi}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.fetchAllScraperFixtureSync(
            this.state.offset,
            this.state?.search
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);

          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
        });
      }
    }
  };
  handleUpdate = async () => {
    const { labelValues } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });

      let payload = {
        name: labelValues?.name,
        server: labelValues?.server,
        providerId: labelValues?.providerId,
        url: labelValues?.url,
        status:
          labelValues?.status === "true"
            ? true
            : labelValues?.status === "false"
            ? false
            : null,
        bookkeeperId: labelValues?.bookkeeperId,
        model: Number(labelValues?.model),
        timezone: labelValues?.timezone,
        timeInterval: Number(labelValues?.timeInterval),
        fixtureLog:
          labelValues?.fixtureLog === "true"
            ? true
            : labelValues?.fixtureLog === "false"
            ? false
            : null,
        oddLog:
          labelValues?.oddLog === "true"
            ? true
            : labelValues?.oddLog === "false"
            ? false
            : null,
        qaCommnet: labelValues?.qaCommnet,
        proxy: labelValues?.proxy,
      };

      let passApi = `adminNotification/updateScrapperProvider/${labelValues?.id}`;
      try {
        const { status, data } = await axiosInstance.put(passApi, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
          });
          this.fetchAllScraperFixtureSync(
            this.state.offset,
            this.state?.search
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorServer: "",
      errorProviderId: "",
      errorUrl: "",
      errorStatus: "",
      errorBookkeeperId: "",
      errorModel: "",
      errorTimezone: "",
      errorTimeInterval: "",
      errorFixtureLog: "",
      errorOddLog: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        labelValues: {
          id: item?.id,
          name: item?.name,
          server: item?.server,
          providerId: item?.providerId,
          url: item?.url,
          status:
            item?.status === true
              ? "true"
              : item?.status === false
              ? "false"
              : null,
          bookkeeperId: item?.bookkeeperId,
          model: item?.model,
          timezone: item?.timezone,
          timeInterval: item?.timeInterval,
          fixtureLog:
            item?.fixtureLog === true
              ? "true"
              : item?.fixtureLog === false
              ? "false"
              : null,
          oddLog:
            item?.oddLog === true
              ? "true"
              : item?.oddLog === false
              ? "false"
              : null,
          qaCommnet: item?.qaCommnet,
          proxy: item?.proxy,
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        labelValues: {
          labelName: "",
        },
        logo: [],
        uploadLogo: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllScraperFixtureSync();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `advertiselogo/advertiselogo/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllScraperFixtureSync(
            this.state.offset,
            this.state?.search
          );
        });
        this.setActionMessage(
          true,
          "Success",
          "Bookkeeper Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handleClearClick = () => {
    this.setState({ search: "" });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllScraperFixtureSync(0, search);
      this.setState({ currentPage: 1 });
    }
  };

  handleCheckBoxChange(e, item) {
    const { value, checked } = e.target;
    const numValue = Number(value);
    const { checkBoxValues } = this.state;

    if (checked) {
      const checkboxdata = [...checkBoxValues, numValue];
      this.setState({
        checkBoxValues: checkboxdata,
      });
    } else {
      const checkboxdata = checkBoxValues?.filter(
        (element) => element !== numValue
      );
      this.setState({
        checkBoxValues: checkboxdata,
      });
    }
  }

  async syncOdd(item) {
    const passEndpoint = `adminNotification/scrapFixtureSync`;
    let payload;

    payload = { bookkeeperId: [item?.bookkeeperId] };

    try {
      const { status } = await axiosInstance.post(passEndpoint, payload);
      if (status === 200) {
        this.setActionMessage(true, "Success", "Fixture Sync Successfully!");
        this.setState({
          checkBoxValues: [],
        });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An Error Occurred");
      this.setState({
        checkBoxValues: [],
      });
    }
  }

  async syncSelectedOdds() {
    const passEndpoint = `adminNotification/scrapFixtureSync`;

    let payload = { bookkeeperId: this.state.checkBoxValues };

    try {
      const { status } = await axiosInstance.post(passEndpoint, payload);
      if (status === 200) {
        this.setActionMessage(true, "Success", "Fixture Sync Successfully!");
        this.setState({
          checkBoxValues: [],
          selectAllChecked: false,
        });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An Error Occurred");
      this.setState({
        checkBoxValues: [],
        selectAllChecked: false,
      });
    }
  }

  handleSelectAll(e) {
    const selectAllChecked = e.target.checked;
    this.setState({ selectAllChecked });

    if (selectAllChecked) {
      const bookkeeperIds = [];
      const scrapData = this.state.ScraperFixtureSync?.map((item) =>
        bookkeeperIds.push(item?.bookkeeperId)
      );
      this.setState({
        checkBoxValues: bookkeeperIds,
      });
    } else {
      this.setState({
        checkBoxValues: [],
      });
    }
  }

  render() {
    var {
      ScraperFixtureSync,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      labelValues,
      ScraperFixtureSyncCount,
      search,
      errorName,
      errorServer,
      errorProviderId,
      errorUrl,
      errorStatus,
      errorBookkeeperId,
      errorModel,
      errorTimezone,
      errorTimeInterval,
      errorFixtureLog,
      errorOddLog,
      bookkeeperList,
      apiProviderList,
      checkBoxValues,
      selectAllChecked,
    } = this.state;
    const pageNumbers = [];

    if (ScraperFixtureSyncCount > 0) {
      for (
        let i = 1;
        i <= Math.ceil(ScraperFixtureSyncCount / rowPerPage);
        i++
      ) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper > */}
            {messageBox?.display && (
              <ActionMessage
                message={messageBox?.message}
                type={messageBox?.type}
                styleClass={messageBox?.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  Scraper Fixture Config
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                {/* <h3 className="text-left">Scraper Provider</h3> */}
                <Typography variant="h1" align="left">
                  Scraper Fixture Config
                </Typography>
              </Grid>
              {/* <Grid item xs={2} style={{ textAlign: "end" }}>
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "6px 10px",
                  marginTop: "5px",
                }}
                onClick={this.inputModal(null, "create")}
              >
                Add New
              </Button>
            </Grid> */}
              <Grid item xs={9} className="admin-filter-wrap">
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllScraperFixtureSync(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Button
                onClick={() => this.syncSelectedOdds()}
                className="info-btn"
                style={{ cursor: "pointer" }}
                disabled={checkBoxValues?.length == 0}
              >
                {" "}
                Sync Selected
              </Button>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && ScraperFixtureSync?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && ScraperFixtureSync?.length > 0 && (
              <>
                <TableContainer component={Paper} style={{ marginTop: "10px" }}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>
                          {" "}
                          <Checkbox
                            className="mz-checkbox"
                            checked={selectAllChecked}
                            value={selectAllChecked}
                            onChange={(e) => this.handleSelectAll(e)}
                            style={{ padding: "0px" }}
                          />
                          Select
                        </TableCell>
                        <TableCell>DID</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>BookkeeperId</TableCell>

                        <TableCell>ProviderId</TableCell>
                        <TableCell>Server</TableCell>
                        <TableCell>URL</TableCell>
                        <TableCell>Status</TableCell>

                        <TableCell>Model</TableCell>
                        <TableCell>Timezone</TableCell>
                        <TableCell>TimeInterval</TableCell>
                        <TableCell>FixtureLog</TableCell>
                        <TableCell>OddLog</TableCell>
                        <TableCell style={{ minWidth: "250px" }}>
                          QAComment
                        </TableCell>
                        <TableCell>Proxy</TableCell>
                        <TableCell>Created At</TableCell>
                        <TableCell>Updated At</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {ScraperFixtureSync?.map((item, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>
                            <Checkbox
                              className="mz-checkbox"
                              checked={checkBoxValues?.includes(
                                item?.bookkeeperId
                              )}
                              value={item?.bookkeeperId}
                              onChange={(e) =>
                                this.handleCheckBoxChange(e, item)
                              }
                            />
                          </TableCell>
                          <TableCell>{item?.id}</TableCell>
                          <TableCell>{item?.name}</TableCell>
                          <TableCell>{item?.bookkeeperId}</TableCell>
                          <TableCell>{item?.providerId}</TableCell>
                          <TableCell>{item?.server}</TableCell>

                          <TableCell>
                            <a href={item?.url} target="_blank">
                              URL
                            </a>
                          </TableCell>
                          <TableCell>
                            {item?.status ? "true" : "false"}
                          </TableCell>

                          <TableCell>{item?.model}</TableCell>
                          <TableCell>{item?.timezone}</TableCell>
                          <TableCell>{item?.timeInterval}</TableCell>
                          <TableCell>
                            {item?.fixtureLog ? "true" : "false"}
                          </TableCell>
                          <TableCell>
                            {item?.oddLog ? "true" : "false"}
                          </TableCell>
                          <TableCell>{item?.qaCommnet}</TableCell>
                          <TableCell>{item?.proxy}</TableCell>
                          <TableCell>
                            {item.createdAt
                              ? moment(item.createdAt).format(
                                  "DD/MM/YYYY h:mm:ss a"
                                )
                              : ""}
                          </TableCell>
                          <TableCell>
                            {item.updatedAt
                              ? moment(item.updatedAt).format(
                                  "DD/MM/YYYY h:mm:ss a"
                                )
                              : ""}
                          </TableCell>

                          <TableCell>
                            <Box style={{ display: "flex" }}>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={() => this.syncOdd(item)}
                                className="info-btn"
                                style={{ cursor: "pointer" }}
                              >
                                {" "}
                                Sync
                              </Button>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                ScraperFixtureSyncCount / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input view-identifier-modal"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Scraper Fixture Config"
                    : "Edit Scraper Fixture Config"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Name </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Name"
                          style={{ marginTop: "0px" }}
                          value={labelValues?.name}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                name: e.target.value,
                              },
                              errorName: e?.target?.value ? "" : errorName,
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">Bookkeeper</label>
                        <Select
                          className="React teamsport-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={bookkeeperList?.find((op) => {
                            return op?.value === labelValues?.bookkeeperId;
                          })}
                          onChange={(e) => {
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                bookkeeperId: e?.value,
                              },
                              errorBookkeeperId: e?.value ? "" : e?.value,
                            });
                          }}
                          options={bookkeeperList}
                        />
                        {errorBookkeeperId ? (
                          <p className="errorText" style={{ margin: "0px" }}>
                            {errorBookkeeperId}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">Api Provider</label>
                        <Select
                          className="React teamsport-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          //   onMenuScrollToBottom={(e) =>
                          //     this.handleOnScrollBottomCategory(e)
                          //   }
                          //   onInputChange={(e) =>
                          //     this.handleCategoryInputChange(0, e)
                          //   }
                          value={apiProviderList?.find((op) => {
                            return op?.value === labelValues?.providerId;
                          })}
                          onChange={(e) => {
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                providerId: e?.value,
                                errorProviderId: e?.value
                                  ? ""
                                  : errorProviderId,
                              },
                            });
                          }}
                          options={apiProviderList}
                        />
                        {errorProviderId ? (
                          <p className="errorText" style={{ margin: "0px" }}>
                            {errorProviderId}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">Status</label>
                        <Select
                          className="React teamsport-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={BooleanOption?.find((op) => {
                            return op?.value === labelValues?.status;
                          })}
                          onChange={(e) => {
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                status: e?.value,
                                errorStatus: e?.value ? "" : errorStatus,
                              },
                            });
                          }}
                          options={BooleanOption}
                        />
                        {errorStatus ? (
                          <p className="errorText" style={{ margin: "0px" }}>
                            {errorStatus}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Server </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Server"
                          style={{ marginTop: "0px", width: "97.2%" }}
                          value={labelValues?.server}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                server: e.target.value,
                              },
                              errorServer: e?.target?.value ? "" : errorServer,
                            })
                          }
                        />
                        {errorServer ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorServer}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> URL </label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="URL"
                          style={{ marginTop: "0px", width: "97.2%" }}
                          value={labelValues?.url}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                url: e.target.value,
                              },
                              errorUrl: e?.target?.value ? "" : errorUrl,
                            })
                          }
                        />
                        {errorUrl ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorUrl}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Model </label>
                        <TextField
                          type="number"
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Model"
                          style={{ marginTop: "0px" }}
                          value={labelValues?.model}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                model: e.target.value,
                              },
                              errorModel: e?.target?.value ? "" : errorModel,
                            })
                          }
                        />
                        {errorModel ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorModel}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Timezone </label>
                        <TextField
                          type="text"
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Timezone"
                          style={{ marginTop: "0px" }}
                          value={labelValues?.timezone}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                timezone: e.target.value,
                              },
                              errorTimezone: e?.target?.value
                                ? ""
                                : errorTimezone,
                            })
                          }
                        />
                        {errorTimezone ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTimezone}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Time Interval </label>
                        <TextField
                          type="number"
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Time Interval"
                          style={{ marginTop: "0px" }}
                          value={labelValues?.timeInterval}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                timeInterval: e.target.value,
                              },
                              errorTimeInterval: e?.target?.value
                                ? ""
                                : errorTimeInterval,
                            })
                          }
                        />
                        {errorTimeInterval ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTimeInterval}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">Fixture Log</label>
                        <Select
                          className="React teamsport-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={BooleanOption?.find((op) => {
                            return op?.value === labelValues?.fixtureLog;
                          })}
                          onChange={(e) => {
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                fixtureLog: e?.value,
                                errorFixtureLog: e?.value
                                  ? ""
                                  : errorFixtureLog,
                              },
                            });
                          }}
                          options={BooleanOption}
                        />
                        {errorFixtureLog ? (
                          <p className="errorText" style={{ margin: "0px" }}>
                            {errorFixtureLog}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid item xs={6} style={{ marginBottom: "15px" }}>
                        <label className="modal-label">Odd Log</label>
                        <Select
                          className="React teamsport-select event-Tournament-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          value={BooleanOption?.find((op) => {
                            return op?.value === labelValues?.oddLog;
                          })}
                          onChange={(e) => {
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                oddLog: e?.value,
                                errorOddLog: e?.value ? "" : errorOddLog,
                              },
                            });
                          }}
                          options={BooleanOption}
                        />
                        {errorOddLog ? (
                          <p className="errorText" style={{ margin: "0px" }}>
                            {errorOddLog}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Proxy </label>
                        <TextField
                          type="text"
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Proxy"
                          style={{ marginTop: "0px" }}
                          value={labelValues?.proxy}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                proxy: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text sync-fixture-textarea"
                      >
                        <label className="modal-label"> QA comment</label>

                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          type="textarea"
                          multiline
                          maxRows={3}
                          color="primary"
                          size="small"
                          placeholder="QA comment"
                          value={labelValues?.qaCommnet}
                          style={{ width: "97.2%" }}
                          onChange={(e) =>
                            this.setState({
                              labelValues: {
                                ...labelValues,
                                qaCommnet: e?.target?.value,
                              },
                            })
                          }
                        />
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default ScraperFixtureSync;
