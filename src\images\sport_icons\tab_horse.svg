<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="35.11" height="21.552" viewBox="0 0 35.11 21.552">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_1577" data-name="Path 1577" d="M306.828,480.807a6.337,6.337,0,0,0-.977-1.273c-.337-.266-.443-.482-.6-.5s-.308.078-.409-.043-.15-.323-.322-.338a.3.3,0,0,0-.259.072.28.28,0,0,1-.323-.043c-.144-.1-.616-.237-.746-.323s-.063.15-.014.193-.066.014-.3-.043-.415-.043-.207.159.317.41.467.482.035.15-.216.265-2.179,1.446-2.931,1.885a21.1,21.1,0,0,0-2.081,1.356c-.417.336-4.219.384-4.862.275s-3.729-.424-4.5-.548a5.726,5.726,0,0,0-3.044-.041c-1.1.418-2.628,1.215-2.846,2.685s-.24,2.348,1.03,3.2,3.2,1.667,3.219,2.721a13.2,13.2,0,0,1-.11,1.887c-.022.372.022.853.526,1.007a17.9,17.9,0,0,1,2.911,1.272c.438.331,1.751,1.25,1.84,1.4a1.216,1.216,0,0,1,.066.549c0,.132.352.328.548.417s.479.24.633.306.22-.262.2-.612a1.4,1.4,0,0,0-.482-1.142,5.915,5.915,0,0,1-.724-.745,1.49,1.49,0,0,0-.677-.5,15.194,15.194,0,0,1-2.365-1.228,1.353,1.353,0,0,1-.611-1.272c0-.659.107-2,.151-2.3a3.655,3.655,0,0,0,.132-.811c-.022-.284.13-.177.328.022a9.981,9.981,0,0,0,1.949,1.117,21.871,21.871,0,0,0,2.167.571c.592.11,1.049.177,1.181.221s.11.265.132.417.419.309.592.417a15.146,15.146,0,0,0,1.641,1.446,7.923,7.923,0,0,1,1.291.9c.154.132.066.35.066.417s-.22.224-.636.331a6.855,6.855,0,0,1-1.424.218,1.913,1.913,0,0,1-1.027-.2c-.22-.11-.022-.306-.11-.439a.968.968,0,0,0-.876-.439,1.12,1.12,0,0,0-.721.24c-.11.044-.157.174.152.395s.595.549.768.593a1.582,1.582,0,0,1,.7.372c.217.218.5.615.9.549s1.446-.461,1.8-.571a3.522,3.522,0,0,1,.744-.177.555.555,0,0,0,.636-.262c.24-.372.306-.441.328-.615s.174-.5-.154-.745a7.881,7.881,0,0,1-.964-1.448c-.173-.24-.655-1.1-.655-1.1a5.355,5.355,0,0,0,.722-.306c.33-.177.766-.637.942-.659a2.934,2.934,0,0,0,.788-.35,2.254,2.254,0,0,0,.746-1.054,7.26,7.26,0,0,0,.46-1.885c.044-.549.085-.921.085-.921a1.372,1.372,0,0,1,.242-.52,11.214,11.214,0,0,0,.977-1.5,2.111,2.111,0,0,1,.66-.892,1.231,1.231,0,0,1,.76-.208c.259.029.475.193.711.237s1.132.612,1.463.7.668.208.726.352.049.338.207.338.611.072.64-.029.092-.17.207-.121.467-.1.5-.26-.058-.367.086-.468.259-.375.115-.525-.294-.274-.409-.41-1-1.351-1.193-1.6A3.09,3.09,0,0,1,306.828,480.807Z" transform="translate(-282.537 -478.344)" fill="#fff" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_1576" data-name="Path 1576" d="M306.828,480.807a6.337,6.337,0,0,0-.977-1.273c-.337-.266-.443-.482-.6-.5s-.308.078-.409-.043-.15-.323-.322-.338a.3.3,0,0,0-.259.072.28.28,0,0,1-.323-.043c-.144-.1-.616-.237-.746-.323s-.063.15-.014.193-.066.014-.3-.043-.415-.043-.207.159.317.41.467.482.035.15-.216.265-2.179,1.446-2.931,1.885a21.1,21.1,0,0,0-2.081,1.356c-.417.336-4.219.384-4.862.275s-3.729-.424-4.5-.548a5.726,5.726,0,0,0-3.044-.041c-1.1.418-2.628,1.215-2.846,2.685s-.24,2.348,1.03,3.2,3.2,1.667,3.219,2.721a13.2,13.2,0,0,1-.11,1.887c-.022.372.022.853.526,1.007a17.9,17.9,0,0,1,2.911,1.272c.438.331,1.751,1.25,1.84,1.4a1.216,1.216,0,0,1,.066.549c0,.132.352.328.548.417s.479.24.633.306.22-.262.2-.612a1.4,1.4,0,0,0-.482-1.142,5.915,5.915,0,0,1-.724-.745,1.49,1.49,0,0,0-.677-.5,15.194,15.194,0,0,1-2.365-1.228,1.353,1.353,0,0,1-.611-1.272c0-.659.107-2,.151-2.3a3.655,3.655,0,0,0,.132-.811c-.022-.284.13-.177.328.022a9.981,9.981,0,0,0,1.949,1.117,21.871,21.871,0,0,0,2.167.571c.592.11,1.049.177,1.181.221s.11.265.132.417.419.309.592.417a15.146,15.146,0,0,0,1.641,1.446,7.923,7.923,0,0,1,1.291.9c.154.132.066.35.066.417s-.22.224-.636.331a6.855,6.855,0,0,1-1.424.218,1.913,1.913,0,0,1-1.027-.2c-.22-.11-.022-.306-.11-.439a.968.968,0,0,0-.876-.439,1.12,1.12,0,0,0-.721.24c-.11.044-.157.174.152.395s.595.549.768.593a1.582,1.582,0,0,1,.7.372c.217.218.5.615.9.549s1.446-.461,1.8-.571a3.522,3.522,0,0,1,.744-.177.555.555,0,0,0,.636-.262c.24-.372.306-.441.328-.615s.174-.5-.154-.745a7.881,7.881,0,0,1-.964-1.448c-.173-.24-.655-1.1-.655-1.1a5.355,5.355,0,0,0,.722-.306c.33-.177.766-.637.942-.659a2.934,2.934,0,0,0,.788-.35,2.254,2.254,0,0,0,.746-1.054,7.26,7.26,0,0,0,.46-1.885c.044-.549.085-.921.085-.921a1.372,1.372,0,0,1,.242-.52,11.214,11.214,0,0,0,.977-1.5,2.111,2.111,0,0,1,.66-.892,1.231,1.231,0,0,1,.76-.208c.259.029.475.193.711.237s1.132.612,1.463.7.668.208.726.352.049.338.207.338.611.072.64-.029.092-.17.207-.121.467-.1.5-.26-.058-.367.086-.468.259-.375.115-.525-.294-.274-.409-.41-1-1.351-1.193-1.6A3.09,3.09,0,0,1,306.828,480.807Z" transform="translate(-282.537 -478.344)" fill="#fff"/>
    </clipPath>
  </defs>
  <g id="Group_24112" data-name="Group 24112" transform="translate(0)">
    <g id="Group_6616" data-name="Group 6616" transform="translate(22.102 11.986)">
      <path id="Path_1510" data-name="Path 1510" d="M496.006,642.106c-.082.154-.1.174-.225.236a.433.433,0,0,1-.175.031c-.072-.01.071-.041.143-.113S496.087,641.952,496.006,642.106Z" transform="translate(-495.586 -642.055)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6617" data-name="Group 6617" transform="translate(21.632 12.414)">
      <path id="Path_1511" data-name="Path 1511" d="M488.628,648.945c-.02.041-.173.236-.266.257a.759.759,0,0,1-.257.052c-.143,0,.07-.022.164-.083s-.274.194-.164.021a1.391,1.391,0,0,1,.226-.246C488.382,648.894,488.648,648.9,488.628,648.945Z" transform="translate(-488.057 -648.91)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6618" data-name="Group 6618" transform="translate(21.256 12.451)">
      <path id="Path_1512" data-name="Path 1512" d="M482.5,649.531a.706.706,0,0,1-.215.215.469.469,0,0,1-.257.031c-.051,0,.04-.021.174-.1S482.553,649.439,482.5,649.531Z" transform="translate(-482.015 -649.503)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6619" data-name="Group 6619" transform="translate(19.393 12.793)">
      <path id="Path_1513" data-name="Path 1513" d="M453.274,655.006a2.451,2.451,0,0,1-.616.143,1.6,1.6,0,0,1-.41-.051c-.1-.031-.2-.031.082-.041s.564-.041.739-.051A.813.813,0,0,1,453.274,655.006Z" transform="translate(-452.146 -655.002)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6620" data-name="Group 6620" transform="translate(18.875 12.376)">
      <path id="Path_1514" data-name="Path 1514" d="M444.278,648.4c-.257-.01-.288-.1-.359-.092s-.165-.021.1.1a.712.712,0,0,0,.482.1C444.616,648.49,444.535,648.408,444.278,648.4Z" transform="translate(-443.842 -648.304)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6621" data-name="Group 6621" transform="translate(18.875 11.718)">
      <path id="Path_1515" data-name="Path 1515" d="M443.873,637.8a1.4,1.4,0,0,0,.441.278c.123.041.04-.011-.175-.155S443.791,637.7,443.873,637.8Z" transform="translate(-443.849 -637.755)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6622" data-name="Group 6622" transform="translate(19.526 10.787)">
      <path id="Path_1516" data-name="Path 1516" d="M454.375,622.884c.133.124.235.175.256.247s-.06.031-.2-.092S454.242,622.759,454.375,622.884Z" transform="translate(-454.294 -622.838)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6623" data-name="Group 6623" transform="translate(18.767 10.581)">
      <path id="Path_1517" data-name="Path 1517" d="M442.186,619.581c.155.174.268.2.411.37s-.2-.03-.318-.154S442.032,619.407,442.186,619.581Z" transform="translate(-442.117 -619.524)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6624" data-name="Group 6624" transform="translate(20.155 10.722)">
      <path id="Path_1518" data-name="Path 1518" d="M464.5,621.823a2.266,2.266,0,0,1,.339.258c.082.082.052.072-.164-.01s-.267-.165-.3-.227S464.425,621.782,464.5,621.823Z" transform="translate(-464.367 -621.794)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6625" data-name="Group 6625" transform="translate(20.31 11.052)">
      <path id="Path_1519" data-name="Path 1519" d="M466.878,627.086c.093.051.225,0,.329.1s.164.207-.041.134S466.784,627.034,466.878,627.086Z" transform="translate(-466.852 -627.078)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6626" data-name="Group 6626" transform="translate(20.027 11.099)">
      <path id="Path_1520" data-name="Path 1520" d="M462.456,627.911a1.556,1.556,0,0,0,.36.3c.215.123-.032.051-.175-.02s-.174-.165-.266-.258S462.312,627.787,462.456,627.911Z" transform="translate(-462.321 -627.838)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6627" data-name="Group 6627" transform="translate(19.427 10.301)">
      <path id="Path_1521" data-name="Path 1521" d="M452.909,615.138c.286.186.206.3-.031.124S452.623,614.952,452.909,615.138Z" transform="translate(-452.698 -615.048)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6628" data-name="Group 6628" transform="translate(19.858 10.64)">
      <path id="Path_1522" data-name="Path 1522" d="M459.765,620.54c.153.175.071.124.164.216s.114.165-.082.041-.277-.339-.206-.278S459.611,620.365,459.765,620.54Z" transform="translate(-459.615 -620.47)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6634" data-name="Group 6634" transform="translate(16.737 7.508)">
      <path id="Path_1532" data-name="Path 1532" d="M417.884,570.267a2.836,2.836,0,0,0-1.26.8,7.072,7.072,0,0,1-1.44,1.234,6.4,6.4,0,0,1-1.407.8,2.243,2.243,0,0,1-.745-.11,1.135,1.135,0,0,1-.464-.427c-.034-.086-.012.109.089.217a1.183,1.183,0,0,0,.516.313c.179.033.325.087.4.087s-.213.1-.325.1a2.166,2.166,0,0,1-.684-.4c-.123-.119-.1-.032.011.076a1.494,1.494,0,0,0,.247.2,1.817,1.817,0,0,1-.471-.217,3.282,3.282,0,0,0-.705-.282,6.483,6.483,0,0,0-.762-.108c-.135,0-.672.022-.773.043s.1.054.268.032a3.477,3.477,0,0,1,.594-.011c.235.011.348.162.258.119s-.582-.109-.37-.032.28.109.112.152a1.263,1.263,0,0,0-.37.162.55.55,0,0,1-.392.043c-.146-.022-.012.066.145.109a1.27,1.27,0,0,1,.37.173,4.1,4.1,0,0,0,1.031.347c.224.011.212.065.437.065s.191.075-.112.086a2.8,2.8,0,0,1-.863-.076,3.21,3.21,0,0,0-.56-.162c-.134-.022-.156-.055.236.119s-.112.033-.28-.021a1.831,1.831,0,0,0-.5-.086c-.135,0,.1.032.257.065s.729.3.919.368a1.674,1.674,0,0,0,.37.065c.168.032-.449,0-.617-.054s-.649-.282-.829-.314a2.646,2.646,0,0,0-.3-.032c-.078-.011.113.065.269.086a3.018,3.018,0,0,1,.627.25,1.569,1.569,0,0,0,.46.162,2.021,2.021,0,0,1-.516-.032c-.168-.043-.494-.173-.673-.238s-.359-.032-.112.032a8.887,8.887,0,0,0,1.009.314c.179,0,.47.022.235.108a1.854,1.854,0,0,1-.8-.033,2.567,2.567,0,0,1-.381-.228c-.123-.065-.135-.043.045.076a1.944,1.944,0,0,0,.807.281s.247-.022.067.032a3.829,3.829,0,0,0-.875.369,3.23,3.23,0,0,0-.482.434c-.067.108.068,0,.213-.131a3.242,3.242,0,0,1,.549-.39c.135-.065.393-.141.5-.184s.369-.076-.034.108a4.8,4.8,0,0,0-.952.563c.2-.087.561-.314.706-.358a2.724,2.724,0,0,1,.549-.184c.146,0,.313.119.448.119a5.443,5.443,0,0,0,1.076.022,3.037,3.037,0,0,0,.964-.423c.157-.108.213-.033.056.1a2.382,2.382,0,0,1-.561.347,1.241,1.241,0,0,1-.56.076c-.247-.011-.672-.076-.4.021a1.35,1.35,0,0,0,.493.1c.067,0-.191.108-.347.141a11.352,11.352,0,0,0-1.143.26,1.291,1.291,0,0,0-.37.184c-.157.108-.034.12.067.043a.985.985,0,0,1,.4-.185c.19-.043.875-.14,1.176-.162a4.979,4.979,0,0,0,1.053-.3,2.291,2.291,0,0,0,.661-.325c.123-.119.19-.129.123-.011s-.256.206-.347.282.19,0,.268-.086.247-.227.325-.336.448-.4.347-.184a2.494,2.494,0,0,1-.65.628,2.419,2.419,0,0,1-.672.293,1.817,1.817,0,0,1-.415.054c-.168.011.145.054.292.022a2.848,2.848,0,0,0,.717-.228c.236-.13.415-.249.415-.249a2.2,2.2,0,0,1-.325.3,1.608,1.608,0,0,1-.459.217c-.224.054-.034.086.134.022a1.22,1.22,0,0,0,.494-.239,3.647,3.647,0,0,0,.414-.379c.067-.108.213-.422.347-.606a2.826,2.826,0,0,1,.448-.564.884.884,0,0,1,.314-.151c.056,0,.023.228-.1.4a4.958,4.958,0,0,1-.527.7,2.132,2.132,0,0,1-.381.249c-.134.076-.279.174-.168.152a1.038,1.038,0,0,0,.381-.12c.112-.076.044.055-.079.163s-.324.27-.168.205.213-.021.415-.249.438-.639.594-.8.348-.466.448-.542.516-.1.65-.173a3.591,3.591,0,0,0,.515-.4c.056-.065-.112-1.137-.011-1.506S417.884,570.267,417.884,570.267Z" transform="translate(-409.58 -570.267)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6635" data-name="Group 6635" transform="translate(18.471 10.108)">
      <path id="Path_1533" data-name="Path 1533" d="M438.249,612.326a1.677,1.677,0,0,0-.582-.325c-.258-.076-.438-.087-.169.065a2.272,2.272,0,0,0,.526.26A.669.669,0,0,0,438.249,612.326Z" transform="translate(-437.365 -611.947)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6636" data-name="Group 6636" transform="translate(17.701 10.358)">
      <path id="Path_1534" data-name="Path 1534" d="M426.976,616.381c.145.011-.526-.109-.717-.174a5.561,5.561,0,0,0-.617-.216,3.159,3.159,0,0,0-.481-.043c-.179,0-.191.043.078.076a2.218,2.218,0,0,1,.617.141,3.264,3.264,0,0,0,.593.228C426.628,616.414,426.831,616.37,426.976,616.381Z" transform="translate(-425.031 -615.948)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6637" data-name="Group 6637" transform="translate(18.449 11.123)">
      <path id="Path_1535" data-name="Path 1535" d="M438.293,628.677c-.078-.022-.538-.25-.695-.325a2.349,2.349,0,0,0-.549-.13c-.124-.022.135.054.336.13s.426.228.537.271a.868.868,0,0,0,.37.119,1.764,1.764,0,0,0,.605-.119c.123-.065-.089-.022-.291.022S438.372,628.7,438.293,628.677Z" transform="translate(-437.018 -628.219)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6638" data-name="Group 6638" transform="translate(22.192 10.377)">
      <path id="Path_1536" data-name="Path 1536" d="M497.749,616.367a2.931,2.931,0,0,0-.481.574c-.123.2-.167.281-.224.368s0,.023.213-.227a6.809,6.809,0,0,1,.515-.6C497.94,616.335,497.973,616.151,497.749,616.367Z" transform="translate(-497.023 -616.268)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6641" data-name="Group 6641" transform="translate(19.049 4.806)">
      <path id="Path_1542" data-name="Path 1542" d="M448.114,526.952c-.076.019-1.377.571-1.377.571l-.095.209s.779-.276.95-.352l.811-.36Z" transform="translate(-446.642 -526.952)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6642" data-name="Group 6642" transform="translate(18.304 5.143)">
      <path id="Path_1543" data-name="Path 1543" d="M437.626,532.345a5.522,5.522,0,0,1-.836.447,4.454,4.454,0,0,1-1.026.191c-.237.019-.408.019-.408.019l-.085.181s.4-.057.532-.057a6.467,6.467,0,0,0,.95-.181,3.468,3.468,0,0,1,.361-.114s-.219.923-.437,1.532a2.988,2.988,0,0,1-.959,1.38,6.153,6.153,0,0,1-.731.314.8.8,0,0,1-.275-.019l-.019.19a.7.7,0,0,0,.294,0,6.741,6.741,0,0,0,1.244-.6,8.962,8.962,0,0,0,.636-1.285c.076-.209.389-1.494.465-1.589a3.9,3.9,0,0,1,.6-.375C438.023,532.322,437.626,532.345,437.626,532.345Z" transform="translate(-434.692 -532.34)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6643" data-name="Group 6643" transform="translate(8.813 1.775)">
      <g id="Group_6593" data-name="Group 6593" transform="translate(0 0)">
        <g id="Group_6592" data-name="Group 6592" clip-path="url(#clip-path)">
          <g id="Group_6591" data-name="Group 6591">
            <g id="Group_6590" data-name="Group 6590">
              <g id="Group_6589" data-name="Group 6589" clip-path="url(#clip-path-2)">
                <g id="Group_6588" data-name="Group 6588" transform="translate(-14.847 -5.606)">
                  <path id="Path_1544" data-name="Path 1544" d="M308.832,478.344h-26.3v19.462h26.3V478.344Z" transform="translate(-267.689 -472.738)" fill="#fff"/>
                  <ellipse id="Ellipse_65" data-name="Ellipse 65" cx="9.481" cy="12.302" rx="9.481" ry="12.302" transform="matrix(0.351, -0.936, 0.936, 0.351, 0, 17.755)" fill="#fff"/>
                  <ellipse id="Ellipse_66" data-name="Ellipse 66" cx="9.38" cy="12.172" rx="9.38" ry="12.172" transform="translate(0.158 17.707) rotate(-69.445)" fill="#fff"/>
                  <ellipse id="Ellipse_67" data-name="Ellipse 67" cx="9.279" cy="12.041" rx="9.279" ry="12.041" transform="translate(0.315 17.657) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_68" data-name="Ellipse 68" cx="9.178" cy="11.91" rx="9.178" ry="11.91" transform="translate(0.475 17.61) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_69" data-name="Ellipse 69" cx="9.077" cy="11.779" rx="9.077" ry="11.779" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.632, 17.562)" fill="#fff"/>
                  <ellipse id="Ellipse_70" data-name="Ellipse 70" cx="8.976" cy="11.648" rx="8.976" ry="11.648" transform="translate(0.79 17.513) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_71" data-name="Ellipse 71" cx="8.876" cy="11.517" rx="8.876" ry="11.517" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.948, 17.465)" fill="#fff"/>
                  <ellipse id="Ellipse_72" data-name="Ellipse 72" cx="8.775" cy="11.386" rx="8.775" ry="11.386" transform="translate(1.105 17.417) rotate(-69.445)" fill="#fff"/>
                  <ellipse id="Ellipse_73" data-name="Ellipse 73" cx="8.674" cy="11.255" rx="8.674" ry="11.255" transform="translate(1.265 17.368) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_74" data-name="Ellipse 74" cx="8.573" cy="11.125" rx="8.573" ry="11.125" transform="translate(1.422 17.32) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_75" data-name="Ellipse 75" cx="8.472" cy="10.994" rx="8.472" ry="10.994" transform="translate(1.58 17.272) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_76" data-name="Ellipse 76" cx="8.371" cy="10.863" rx="8.371" ry="10.863" transform="translate(1.738 17.223) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_77" data-name="Ellipse 77" cx="8.27" cy="10.732" rx="8.27" ry="10.732" transform="translate(1.895 17.175) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_78" data-name="Ellipse 78" cx="8.17" cy="10.601" rx="8.17" ry="10.601" transform="translate(2.055 17.127) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_79" data-name="Ellipse 79" cx="8.069" cy="10.47" rx="8.069" ry="10.47" transform="translate(2.212 17.076) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_80" data-name="Ellipse 80" cx="7.968" cy="10.339" rx="7.968" ry="10.339" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.37, 17.028)" fill="#fff"/>
                  <ellipse id="Ellipse_81" data-name="Ellipse 81" cx="7.867" cy="10.208" rx="7.867" ry="10.208" transform="translate(2.527 16.979) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_82" data-name="Ellipse 82" cx="7.766" cy="10.077" rx="7.766" ry="10.077" transform="translate(2.685 16.931) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_83" data-name="Ellipse 83" cx="7.665" cy="9.947" rx="7.665" ry="9.947" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.845, 16.883)" fill="#fff"/>
                  <ellipse id="Ellipse_84" data-name="Ellipse 84" cx="7.564" cy="9.816" rx="7.564" ry="9.816" transform="translate(3.002 16.834) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_85" data-name="Ellipse 85" cx="7.464" cy="9.685" rx="7.464" ry="9.685" transform="translate(3.16 16.786) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_86" data-name="Ellipse 86" cx="7.363" cy="9.554" rx="7.363" ry="9.554" transform="translate(3.317 16.738) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_87" data-name="Ellipse 87" cx="7.262" cy="9.423" rx="7.262" ry="9.423" transform="translate(3.475 16.689) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_88" data-name="Ellipse 88" cx="7.161" cy="9.292" rx="7.161" ry="9.292" transform="translate(3.635 16.641) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_89" data-name="Ellipse 89" cx="7.06" cy="9.162" rx="7.06" ry="9.162" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.792, 16.593)" fill="#fff"/>
                  <ellipse id="Ellipse_90" data-name="Ellipse 90" cx="6.959" cy="9.031" rx="6.959" ry="9.031" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.95, 16.544)" fill="#fff"/>
                  <ellipse id="Ellipse_91" data-name="Ellipse 91" cx="6.858" cy="8.9" rx="6.858" ry="8.9" transform="translate(4.107 16.496) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_92" data-name="Ellipse 92" cx="6.758" cy="8.769" rx="6.758" ry="8.769" transform="translate(4.265 16.446) rotate(-69.444)" fill="#fff"/>
                  <ellipse id="Ellipse_93" data-name="Ellipse 93" cx="6.657" cy="8.638" rx="6.657" ry="8.638" transform="translate(4.424 16.397) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_94" data-name="Ellipse 94" cx="6.556" cy="8.507" rx="6.556" ry="8.507" transform="translate(4.582 16.349) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_95" data-name="Ellipse 95" cx="6.455" cy="8.376" rx="6.455" ry="8.376" transform="translate(4.74 16.3) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_96" data-name="Ellipse 96" cx="6.354" cy="8.245" rx="6.354" ry="8.245" transform="translate(4.897 16.252) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_97" data-name="Ellipse 97" cx="6.253" cy="8.114" rx="6.253" ry="8.114" transform="translate(5.055 16.204) rotate(-69.445)" fill="#fff"/>
                  <ellipse id="Ellipse_98" data-name="Ellipse 98" cx="6.152" cy="7.984" rx="6.152" ry="7.984" transform="translate(5.214 16.155) rotate(-69.449)" fill="#fff"/>
                  <ellipse id="Ellipse_99" data-name="Ellipse 99" cx="6.052" cy="7.853" rx="6.052" ry="7.853" transform="matrix(0.351, -0.936, 0.936, 0.351, 5.372, 16.107)" fill="#fff"/>
                  <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="5.951" cy="7.722" rx="5.951" ry="7.722" transform="translate(5.529 16.06) rotate(-69.446)" fill="#fff"/>
                  <path id="Path_1545" data-name="Path 1545" d="M163.947,507.439c0-3.315,3.312-5.536,7.4-4.962s7.4,3.727,7.4,7.042-3.312,5.536-7.4,4.962S163.947,510.754,163.947,507.439Z" transform="translate(-156.497 -495.281)" fill="#fff"/>
                  <ellipse id="Ellipse_101" data-name="Ellipse 101" cx="5.749" cy="7.46" rx="5.749" ry="7.46" transform="translate(5.845 15.962) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_102" data-name="Ellipse 102" cx="5.648" cy="7.329" rx="5.648" ry="7.329" transform="translate(6.004 15.915) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_103" data-name="Ellipse 103" cx="5.547" cy="7.198" rx="5.547" ry="7.198" transform="translate(6.162 15.865) rotate(-69.449)" fill="#fff"/>
                  <ellipse id="Ellipse_104" data-name="Ellipse 104" cx="5.446" cy="7.067" rx="5.446" ry="7.067" transform="translate(6.319 15.818) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_105" data-name="Ellipse 105" cx="5.346" cy="6.936" rx="5.346" ry="6.936" transform="translate(6.477 15.766) rotate(-69.448)" fill="#fff"/>
                  <ellipse id="Ellipse_106" data-name="Ellipse 106" cx="5.245" cy="6.806" rx="5.245" ry="6.806" transform="translate(6.635 15.718) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_107" data-name="Ellipse 107" cx="5.144" cy="6.675" rx="5.144" ry="6.675" transform="translate(6.794 15.671) rotate(-69.447)" fill="#fff"/>
                  <ellipse id="Ellipse_108" data-name="Ellipse 108" cx="5.043" cy="6.544" rx="5.043" ry="6.544" transform="translate(6.952 15.621) rotate(-69.449)" fill="#fff"/>
                  <path id="Path_1546" data-name="Path 1546" d="M182.362,521.809c0-2.8,2.8-4.677,6.251-4.192s6.25,3.149,6.25,5.949-2.8,4.677-6.251,4.192S182.362,524.609,182.362,521.809Z" transform="translate(-173.763 -509.49)" fill="#fff"/>
                  <ellipse id="Ellipse_109" data-name="Ellipse 109" cx="4.841" cy="6.282" rx="4.841" ry="6.282" transform="translate(7.267 15.526) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_110" data-name="Ellipse 110" cx="4.74" cy="6.151" rx="4.74" ry="6.151" transform="matrix(0.351, -0.936, 0.936, 0.351, 7.425, 15.476)" fill="#fff"/>
                  <ellipse id="Ellipse_111" data-name="Ellipse 111" cx="4.639" cy="6.02" rx="4.639" ry="6.02" transform="translate(7.582 15.429) rotate(-69.446)" fill="#fff"/>
                  <ellipse id="Ellipse_112" data-name="Ellipse 112" cx="4.539" cy="5.89" rx="4.539" ry="5.89" transform="translate(7.742 15.381) rotate(-69.447)" fill="#fff"/>
                  <path id="Path_1547" data-name="Path 1547" d="M192.59,529.8c0-2.515,2.513-4.2,5.613-3.764s5.613,2.828,5.613,5.342-2.513,4.2-5.613,3.764S192.59,532.313,192.59,529.8Z" transform="translate(-183.353 -517.389)" fill="#fff"/>
                  <ellipse id="Ellipse_113" data-name="Ellipse 113" cx="4.337" cy="5.628" rx="4.337" ry="5.628" transform="translate(8.057 15.284) rotate(-69.448)" fill="#fff"/>
                  <path id="Path_1548" data-name="Path 1548" d="M196.668,533.021c0-2.4,2.4-4.009,5.358-3.593s5.357,2.7,5.357,5.1-2.4,4.009-5.358,3.593S196.668,535.422,196.668,533.021Z" transform="translate(-187.177 -520.575)" fill="#fff"/>
                  <ellipse id="Ellipse_114" data-name="Ellipse 114" cx="4.135" cy="5.366" rx="4.135" ry="5.366" transform="translate(8.372 15.186) rotate(-69.448)" fill="#fff"/>
                  <path id="Path_1549" data-name="Path 1549" d="M200.778,536.178c0-2.286,2.285-3.818,5.1-3.422s5.1,2.571,5.1,4.856-2.285,3.818-5.1,3.422S200.778,538.464,200.778,536.178Z" transform="translate(-191.031 -523.698)" fill="#fff"/>
                  <ellipse id="Ellipse_115" data-name="Ellipse 115" cx="3.934" cy="5.104" rx="3.934" ry="5.104" transform="translate(8.689 15.088) rotate(-69.449)" fill="#fff"/>
                  <path id="Path_1550" data-name="Path 1550" d="M204.856,539.367c0-2.172,2.17-3.627,4.847-3.251s4.847,2.442,4.847,4.614-2.17,3.627-4.847,3.251S204.856,541.539,204.856,539.367Z" transform="translate(-194.854 -526.852)" fill="#fff"/>
                  <ellipse id="Ellipse_116" data-name="Ellipse 116" cx="3.732" cy="4.842" rx="3.732" ry="4.842" transform="translate(9.004 14.992) rotate(-69.445)" fill="#fff"/>
                  <path id="Path_1551" data-name="Path 1551" d="M208.934,542.556c0-2.058,2.056-3.437,4.592-3.08s4.592,2.313,4.592,4.371-2.056,3.436-4.592,3.08S208.934,544.614,208.934,542.556Z" transform="translate(-198.678 -530.005)" fill="#fff"/>
                  <ellipse id="Ellipse_117" data-name="Ellipse 117" cx="3.53" cy="4.581" rx="3.53" ry="4.581" transform="translate(9.322 14.895) rotate(-69.449)" fill="#fff"/>
                  <ellipse id="Ellipse_118" data-name="Ellipse 118" cx="3.429" cy="4.45" rx="3.429" ry="4.45" transform="translate(9.479 14.847) rotate(-69.447)" fill="#fff"/>
                  <path id="Path_1552" data-name="Path 1552" d="M215.084,547.357c0-1.886,1.885-3.15,4.21-2.823s4.209,2.12,4.209,4.006-1.885,3.15-4.21,2.823S215.084,549.243,215.084,547.357Z" transform="translate(-204.444 -534.751)" fill="#fff"/>
                  <ellipse id="Ellipse_119" data-name="Ellipse 119" cx="3.227" cy="4.188" rx="3.227" ry="4.188" transform="translate(9.794 14.75) rotate(-69.448)" fill="#fff"/>
                  <path id="Path_1553" data-name="Path 1553" d="M219.162,550.58c0-1.771,1.771-2.959,3.954-2.652s3.954,1.992,3.954,3.764-1.771,2.959-3.954,2.652S219.162,552.352,219.162,550.58Z" transform="translate(-208.268 -537.937)" fill="#fff"/>
                  <ellipse id="Ellipse_120" data-name="Ellipse 120" cx="3.026" cy="3.926" rx="3.026" ry="3.926" transform="translate(10.112 14.653) rotate(-69.452)" fill="#fff"/>
                  <ellipse id="Ellipse_121" data-name="Ellipse 121" cx="2.925" cy="3.796" rx="2.925" ry="3.796" transform="matrix(0.351, -0.936, 0.936, 0.351, 10.269, 14.605)" fill="#fff"/>
                  <ellipse id="Ellipse_122" data-name="Ellipse 122" cx="2.824" cy="3.665" rx="2.824" ry="3.665" transform="translate(10.427 14.557) rotate(-69.447)" fill="#fff"/>
                  <path id="Path_1554" data-name="Path 1554" d="M227.35,556.958c0-1.543,1.542-2.577,3.444-2.31s3.444,1.735,3.444,3.278-1.542,2.577-3.444,2.31S227.35,558.5,227.35,556.958Z" transform="translate(-215.945 -544.243)" fill="#fff"/>
                  <ellipse id="Ellipse_123" data-name="Ellipse 123" cx="2.622" cy="3.403" rx="2.622" ry="3.403" transform="translate(10.742 14.458) rotate(-69.445)" fill="#fff"/>
                  <path id="Path_1555" data-name="Path 1555" d="M231.46,560.115c0-1.429,1.428-2.386,3.189-2.139s3.189,1.607,3.189,3.035-1.428,2.386-3.189,2.139S231.46,561.544,231.46,560.115Z" transform="translate(-219.799 -547.367)" fill="#fff"/>
                  <ellipse id="Ellipse_124" data-name="Ellipse 124" cx="2.421" cy="3.141" rx="2.421" ry="3.141" transform="translate(11.059 14.361) rotate(-69.45)" fill="#fff"/>
                  <ellipse id="Ellipse_125" data-name="Ellipse 125" cx="2.32" cy="3.01" rx="2.32" ry="3.01" transform="matrix(0.351, -0.936, 0.936, 0.351, 11.216, 14.313)" fill="#fff"/>
                  <path id="Path_1556" data-name="Path 1556" d="M237.578,564.916c0-1.257,1.256-2.1,2.806-1.882a3.075,3.075,0,0,1,2.806,2.671c0,1.258-1.257,2.1-2.807,1.882A3.075,3.075,0,0,1,237.578,564.916Z" transform="translate(-225.535 -552.113)" fill="#fff"/>
                  <path id="Path_1557" data-name="Path 1557" d="M239.617,566.527c0-1.2,1.2-2,2.679-1.8a2.935,2.935,0,0,1,2.679,2.55c0,1.2-1.2,2-2.679,1.8A2.935,2.935,0,0,1,239.617,566.527Z" transform="translate(-227.447 -553.706)" fill="#fff"/>
                  <path id="Path_1558" data-name="Path 1558" d="M241.688,568.139c0-1.143,1.142-1.909,2.551-1.711a2.8,2.8,0,0,1,2.551,2.428c0,1.143-1.142,1.909-2.552,1.711A2.8,2.8,0,0,1,241.688,568.139Z" transform="translate(-229.389 -555.299)" fill="#fff"/>
                  <path id="Path_1559" data-name="Path 1559" d="M243.727,569.716c0-1.086,1.085-1.814,2.424-1.625a2.655,2.655,0,0,1,2.424,2.307c0,1.086-1.085,1.813-2.424,1.625A2.656,2.656,0,0,1,243.727,569.716Z" transform="translate(-231.301 -556.859)" fill="#fff"/>
                  <ellipse id="Ellipse_126" data-name="Ellipse 126" cx="1.815" cy="2.356" rx="1.815" ry="2.356" transform="translate(12.006 14.071) rotate(-69.451)" fill="#fff"/>
                  <path id="Path_1560" data-name="Path 1560" d="M247.805,572.94c0-.972.971-1.623,2.169-1.454a2.376,2.376,0,0,1,2.168,2.064c0,.972-.971,1.623-2.169,1.454A2.376,2.376,0,0,1,247.805,572.94Z" transform="translate(-235.124 -560.045)" fill="#fff"/>
                  <ellipse id="Ellipse_127" data-name="Ellipse 127" cx="1.614" cy="2.094" rx="1.614" ry="2.094" transform="translate(12.322 13.974) rotate(-69.453)" fill="#fff"/>
                  <path id="Path_1561" data-name="Path 1561" d="M251.915,576.128c0-.857.857-1.432,1.914-1.283a2.1,2.1,0,0,1,1.913,1.821c0,.857-.857,1.432-1.914,1.283A2.1,2.1,0,0,1,251.915,576.128Z" transform="translate(-238.978 -563.198)" fill="#fff"/>
                  <path id="Path_1562" data-name="Path 1562" d="M253.954,577.74c0-.8.8-1.336,1.786-1.2a1.957,1.957,0,0,1,1.786,1.7c0,.8-.8,1.336-1.786,1.2A1.957,1.957,0,0,1,253.954,577.74Z" transform="translate(-240.89 -564.791)" fill="#fff"/>
                  <path id="Path_1563" data-name="Path 1563" d="M255.993,579.285c0-.743.743-1.241,1.659-1.112a1.817,1.817,0,0,1,1.658,1.578c0,.743-.742,1.241-1.658,1.112A1.817,1.817,0,0,1,255.993,579.285Z" transform="translate(-242.802 -566.321)" fill="#fff"/>
                  <path id="Path_1564" data-name="Path 1564" d="M258.033,580.9c0-.686.685-1.146,1.531-1.027a1.677,1.677,0,0,1,1.531,1.457c0,.686-.686,1.146-1.531,1.027A1.677,1.677,0,0,1,258.033,580.9Z" transform="translate(-244.714 -567.915)" fill="#fff"/>
                  <path id="Path_1565" data-name="Path 1565" d="M260.072,582.474c0-.629.628-1.05,1.4-.941a1.537,1.537,0,0,1,1.4,1.335c0,.629-.628,1.05-1.4.941A1.537,1.537,0,0,1,260.072,582.474Z" transform="translate(-246.626 -569.474)" fill="#fff"/>
                  <path id="Path_1566" data-name="Path 1566" d="M262.143,584.086c0-.572.571-.955,1.276-.855a1.4,1.4,0,0,1,1.276,1.214c0,.571-.571.954-1.276.855A1.4,1.4,0,0,1,262.143,584.086Z" transform="translate(-248.568 -571.068)" fill="#fff"/>
                  <path id="Path_1567" data-name="Path 1567" d="M264.182,585.7c0-.514.514-.859,1.148-.77a1.258,1.258,0,0,1,1.148,1.093c0,.514-.514.859-1.148.77A1.258,1.258,0,0,1,264.182,585.7Z" transform="translate(-250.48 -572.661)" fill="#fff"/>
                  <path id="Path_1568" data-name="Path 1568" d="M266.221,587.275c0-.457.457-.764,1.021-.684a1.118,1.118,0,0,1,1.02.971c0,.457-.457.764-1.021.684A1.118,1.118,0,0,1,266.221,587.275Z" transform="translate(-252.392 -574.221)" fill="#fff"/>
                  <path id="Path_1569" data-name="Path 1569" d="M268.26,588.886c0-.4.4-.668.893-.6a.978.978,0,0,1,.893.85c0,.4-.4.668-.893.6A.978.978,0,0,1,268.26,588.886Z" transform="translate(-254.303 -575.814)" fill="#fff"/>
                  <path id="Path_1570" data-name="Path 1570" d="M270.3,590.5c0-.343.343-.572.766-.513a.839.839,0,0,1,.765.729c0,.343-.343.573-.766.513A.838.838,0,0,1,270.3,590.5Z" transform="translate(-256.215 -577.406)" fill="#fff"/>
                  <path id="Path_1571" data-name="Path 1571" d="M272.37,592.076c0-.286.286-.477.638-.428a.7.7,0,0,1,.638.607c0,.286-.286.477-.638.428A.7.7,0,0,1,272.37,592.076Z" transform="translate(-258.157 -578.968)" fill="#fff"/>
                  <path id="Path_1572" data-name="Path 1572" d="M274.41,593.686c0-.229.229-.382.51-.342a.559.559,0,0,1,.51.486c0,.229-.229.382-.511.342A.559.559,0,0,1,274.41,593.686Z" transform="translate(-260.07 -580.56)" fill="#fff"/>
                  <path id="Path_1573" data-name="Path 1573" d="M276.449,595.3c0-.172.172-.286.383-.256a.419.419,0,0,1,.383.364c0,.171-.172.286-.383.256A.419.419,0,0,1,276.449,595.3Z" transform="translate(-261.982 -582.153)" fill="#fff"/>
                  <path id="Path_1574" data-name="Path 1574" d="M278.488,596.875c0-.114.114-.191.255-.171a.28.28,0,0,1,.255.243c0,.114-.114.191-.255.171A.279.279,0,0,1,278.488,596.875Z" transform="translate(-263.894 -583.713)" fill="#fff"/>
                  <path id="Path_1575" data-name="Path 1575" d="M280.527,598.487c0-.057.057-.1.128-.085a.14.14,0,0,1,.127.121c0,.057-.057.1-.128.085A.14.14,0,0,1,280.527,598.487Z" transform="translate(-265.805 -585.306)" fill="#fff"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_6644" data-name="Group 6644" transform="translate(23.811 13.21)">
      <path id="Path_1578" data-name="Path 1578" d="M525.829,661.672a3.976,3.976,0,0,0,1.244.738c.713.3,1.174.536,1.36.6a.841.841,0,0,1,.531.5,2.349,2.349,0,0,1,.275,1.064,17.018,17.018,0,0,0-.182,2.123,2.721,2.721,0,0,1-.14,1.087,8.039,8.039,0,0,0-.438,1.246c0,.163-.042.233-.3.578s-.279.551-.578.159a1.456,1.456,0,0,1-.321-1.13c.065-.3.317-.275.5-.345a.483.483,0,0,0,.233-.626,1.293,1.293,0,0,1,.07-.761c.046-.187.159-1.689.159-2.058a.975.975,0,0,0-.205-.714,1.474,1.474,0,0,0-.829-.346c-.233,0-1.039-.07-1.407-.07s-1.155-.14-1.476-.14a2.446,2.446,0,0,1-1.081-.3c-.438-.21-.3-.112.093-.322S525.829,661.672,525.829,661.672Z" transform="translate(-522.978 -661.672)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6645" data-name="Group 6645" transform="translate(24.712 2.613)">
      <path id="Path_1579" data-name="Path 1579" d="M541.869,492.041a.659.659,0,0,0-.382-.2c-.19-.055-.521-.083-.452,0s.066.218-.152.218-.435-.028-.435.069a.426.426,0,0,1-.3.37c-.2.041-.631.262-.4.276s-.162.149-.369.149-.348.009-.524.009-1.074-.16-.908-.077.085.178-.067.137-.462-.055-.272.028.171.219.033.274-.3-.069-.164,0,.466.277.273.318-.411.119-.2.2.272.287.341.384.127.207.3.124,2.23-.868,2.489-1a12.919,12.919,0,0,0,1.107-1.177C541.852,492.11,541.976,492.162,541.869,492.041Z" transform="translate(-537.426 -491.785)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6646" data-name="Group 6646" transform="translate(17.592 5.75)">
      <path id="Path_1580" data-name="Path 1580" d="M424.069,542.143a6.038,6.038,0,0,0-.644-.052c-.128-.034-.2.045-.111.156a1.514,1.514,0,0,0,.6.421c.241.069,1.493.284,1.631.284s1.1,0,1.589-.035.352-.525.352-.525S424.345,542.212,424.069,542.143Z" transform="translate(-423.273 -542.083)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6647" data-name="Group 6647" transform="translate(17.736 2.536)">
      <path id="Path_1581" data-name="Path 1581" d="M426.938,490.568c.086.052.167.418.4.625s.514.37.531.49.145.111.386.2,1.8.525,2.13.663.969.38,1.055.594a1.037,1.037,0,0,1-.2.988c-.276.259-.507.2-.61.311a2.116,2.116,0,0,1-.507.266l-.725-.353s-.237-.194-1.113-.177a3.09,3.09,0,0,1-1.914-.537c-.379-.238-.824-.55-.79-1.109a1.687,1.687,0,0,1,.441-1.192c.3-.328.373-.408.528-.58S426.852,490.516,426.938,490.568Z" transform="translate(-425.583 -490.551)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6648" data-name="Group 6648" transform="translate(18.683 1.508)">
      <path id="Path_1582" data-name="Path 1582" d="M446.7,474.138a2,2,0,0,1,.465,1.137c.055.646.787.475.542.527s-1.007.067-.966.161a1.661,1.661,0,0,1,.028.67c-.041.245-.2.3.041.508s.245.328.369.273a7.55,7.55,0,0,0,.834-.6c.217-.18.344-.342.534-.466s.162-.135.314-.014a1.324,1.324,0,0,1,.272.411c.069.107.155.094-.093.3s-.41.483-.6.673a10.7,10.7,0,0,1-.958.929c-.176.1-.476.4-.669.331a2.21,2.21,0,0,1-.645-.646c-.083-.138-.273-.532-.314-.629a3.693,3.693,0,0,1-.248-.453c-.052-.166-.135-.179-.3-.152a1.461,1.461,0,0,1-.507.041c-.217-.028-2.591-.4-2.784-.486a1.048,1.048,0,0,1-.416-.428,5.622,5.622,0,0,0-.774-.7c-.152-.11.071-.32.36-.527a7.684,7.684,0,0,1,2.055-.795,11.686,11.686,0,0,1,1.779-.138c.327.014,1.055.014,1.286.042S446.648,474.1,446.7,474.138Z" transform="translate(-440.778 -474.066)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6649" data-name="Group 6649" transform="translate(18.337 5.915)">
      <path id="Path_1583" data-name="Path 1583" d="M438.875,544.733s-.462.165-.841.328-.979.594-1.451.878a2.033,2.033,0,0,0-.7.619c-.093.121-.241.249-.42.439a1.053,1.053,0,0,0-.248.559c0,.069.111.1.266.173s.4.266.472.214.13.017.293.086.514.421.669.439.7.034.824.034.093-.214-.1-.284a.733.733,0,0,1-.48-.4,1.2,1.2,0,0,1-.138-.636c.034-.155.241-.249.438-.37s.935-.439,1.193-.577a6.772,6.772,0,0,1,.738-.318c.138-.052-.025-.362-.214-.715A2.613,2.613,0,0,0,438.875,544.733Z" transform="translate(-435.217 -544.733)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6650" data-name="Group 6650" transform="translate(0 7.29)">
      <path id="Path_1584" data-name="Path 1584" d="M150.784,566.762a3.247,3.247,0,0,0-1.448.954,8.222,8.222,0,0,1-1.655,1.47,7.294,7.294,0,0,1-1.617.95,2.493,2.493,0,0,1-.856-.131,1.323,1.323,0,0,1-.534-.509c-.039-.1-.014.13.1.258a1.353,1.353,0,0,0,.593.373c.206.04.374.1.464.1s-.245.116-.374.116a2.475,2.475,0,0,1-.786-.477c-.141-.142-.116-.039.013.09a1.748,1.748,0,0,0,.284.233,2.057,2.057,0,0,1-.542-.259,3.686,3.686,0,0,0-.81-.335,7.143,7.143,0,0,0-.876-.129c-.155,0-.772.026-.888.052s.115.064.308.038a3.876,3.876,0,0,1,.683-.013c.27.013.4.193.3.142s-.669-.13-.425-.039.322.13.129.181a1.434,1.434,0,0,0-.425.193.609.609,0,0,1-.451.052c-.168-.026-.014.078.167.13a1.447,1.447,0,0,1,.425.206,4.6,4.6,0,0,0,1.185.413c.257.013.244.077.5.077s.219.089-.128.1a3.094,3.094,0,0,1-.992-.09,3.61,3.61,0,0,0-.643-.193c-.154-.026-.18-.065.271.142s-.129.039-.322-.026a2.034,2.034,0,0,0-.579-.1c-.155,0,.115.039.3.077s.838.362,1.057.439a1.863,1.863,0,0,0,.425.077c.193.039-.516,0-.709-.064s-.746-.336-.953-.374a2.973,2.973,0,0,0-.348-.039c-.09-.013.129.077.309.1a3.388,3.388,0,0,1,.721.3,1.763,1.763,0,0,0,.529.193,2.23,2.23,0,0,1-.593-.039c-.193-.051-.567-.206-.773-.283s-.412-.039-.128.039a9.966,9.966,0,0,0,1.159.374c.206,0,.54.026.27.129a2.059,2.059,0,0,1-.914-.039,2.921,2.921,0,0,1-.438-.271c-.141-.077-.155-.051.051.091a2.184,2.184,0,0,0,.928.335s.284-.026.077.039a4.32,4.32,0,0,0-1.005.439,3.764,3.764,0,0,0-.553.517c-.077.129.078,0,.245-.155a3.711,3.711,0,0,1,.631-.465c.155-.077.452-.167.58-.219s.424-.09-.039.129a5.467,5.467,0,0,0-1.094.671c.231-.1.644-.374.811-.426a3.046,3.046,0,0,1,.631-.219c.168,0,.36.142.515.142a6.034,6.034,0,0,0,1.236.026,3.433,3.433,0,0,0,1.108-.5c.18-.129.244-.039.064.116a2.736,2.736,0,0,1-.644.413,1.381,1.381,0,0,1-.643.091c-.284-.013-.773-.09-.464.026a1.5,1.5,0,0,0,.566.116c.077,0-.219.129-.4.167a12.7,12.7,0,0,0-1.314.31,1.456,1.456,0,0,0-.425.22c-.18.129-.039.142.077.051a1.112,1.112,0,0,1,.464-.22c.218-.052,1.005-.167,1.352-.193a5.554,5.554,0,0,0,1.21-.362,2.606,2.606,0,0,0,.76-.387c.141-.142.219-.155.141-.013s-.295.245-.4.336.218,0,.308-.1.284-.271.374-.4.515-.478.4-.22a2.91,2.91,0,0,1-.747.749,2.732,2.732,0,0,1-.772.349,2.02,2.02,0,0,1-.477.064c-.193.013.167.065.335.026a3.2,3.2,0,0,0,.824-.271c.271-.155.476-.3.476-.3a2.549,2.549,0,0,1-.374.36,1.811,1.811,0,0,1-.528.258c-.257.064-.039.1.154.026a1.383,1.383,0,0,0,.567-.284,4.226,4.226,0,0,0,.476-.452c.077-.129.245-.5.4-.722a3.343,3.343,0,0,1,.515-.671.994.994,0,0,1,.361-.18c.064,0,.027.271-.116.477a5.879,5.879,0,0,1-.606.839,2.431,2.431,0,0,1-.438.3c-.154.091-.321.207-.193.181a1.159,1.159,0,0,0,.438-.143c.128-.09.05.065-.091.194s-.373.322-.193.245.244-.025.476-.3.5-.761.683-.955.4-.556.515-.646.593-.115.747-.206a4.152,4.152,0,0,0,.592-.477c.064-.078-.129-1.355-.013-1.793S150.784,566.762,150.784,566.762Z" transform="translate(-141.241 -566.762)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6651" data-name="Group 6651" transform="translate(6.268 10.707)">
      <path id="Path_1588" data-name="Path 1588" d="M242.573,621.665a3.46,3.46,0,0,0-.553.683c-.141.233-.192.335-.257.439s0,.027.244-.27a8.01,8.01,0,0,1,.592-.71C242.792,621.627,242.83,621.408,242.573,621.665Z" transform="translate(-241.738 -621.548)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6652" data-name="Group 6652" transform="translate(7.543 10.962)">
      <path id="Path_1589" data-name="Path 1589" d="M262.855,625.721c-.245.168-.831.359-.644.31a1.354,1.354,0,0,0,.567-.18C263.1,625.67,263.1,625.553,262.855,625.721Z" transform="translate(-262.175 -625.64)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6653" data-name="Group 6653" transform="translate(14.652 11.868)">
      <path id="Path_1590" data-name="Path 1590" d="M377.726,641.225c0,.474.156,2.455.156,3.036a3.588,3.588,0,0,0,.755,1.9c.3.262,2.832,2.25,3.185,2.406a2.736,2.736,0,0,1,1.278.842c.388.524.226.439-.367.439a4.608,4.608,0,0,1-1.029-.028c-.091-.053-.212-.206-.166-.326a.236.236,0,0,0-.068-.264,3.239,3.239,0,0,0-.279-.2c-.053-.031-4.726-3.758-4.954-3.917s.023-2.7.047-3.439.047-1.265.047-1.4.019-.145.275.112A12.959,12.959,0,0,0,377.726,641.225Z" transform="translate(-376.141 -640.165)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6654" data-name="Group 6654" transform="translate(26.373 6.334)">
      <path id="Path_1591" data-name="Path 1591" d="M571.324,552.651s-2.364-.363-2.766-.432-2.715-.464-3.118-.521-.676-.137-1-.194-.368-.069-.368-.069l-.011.16s.114.043.46.149,1.2.209,1.5.278,1.382.266,1.819.335,1.861.232,2.058.289.9.172,1.014.183.4.028.4.028Z" transform="translate(-564.061 -551.435)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6655" data-name="Group 6655" transform="translate(24.53 1.559)">
      <path id="Path_1592" data-name="Path 1592" d="M536.825,474.98a.456.456,0,0,1-.1.334c-.067.043-.066.07,0,.222s.091.224.115.289-.006.131-.113.115-.17-.012-.173.081,0,.137-.048.147-.079.036-.045.079-.022.063-.051.111,0,.148,0,.184a.138.138,0,0,1-.153.111c-.1-.012-.329-.047-.4-.047a4.783,4.783,0,0,0-.48-.009c-.068.016-.33-.349-.5-.509a1.962,1.962,0,0,1-.3-.63c-.176-.367.046-.237.209-.342a.65.65,0,0,1,.558-.129c.242.058.62-.1.85-.1A3.371,3.371,0,0,1,536.825,474.98Z" transform="translate(-534.519 -474.885)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6656" data-name="Group 6656" transform="translate(24.123)">
      <path id="Path_1593" data-name="Path 1593" d="M530.555,450.409a1.573,1.573,0,0,0-1.088-.515,1.528,1.528,0,0,0-.767.216,1.22,1.22,0,0,0-.5.65c-.1.3-.03.37-.134.592s-.132.194.061.318.257.229.378.13a.933.933,0,0,1,.53-.211.854.854,0,0,0,.477.029,3.157,3.157,0,0,1,.866-.022c.146.021.432.027.474.04a2.453,2.453,0,0,0-.046-.682A1.128,1.128,0,0,0,530.555,450.409Z" transform="translate(-527.988 -449.893)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_6657" data-name="Group 6657" transform="translate(26.226 0.753)">
      <path id="Path_1594" data-name="Path 1594" d="M562.483,462.007a.834.834,0,0,1,.1.491c-.037.163-.078.154-.181.14a2.653,2.653,0,0,1-.554-.106c-.111-.056-.223-.217-.088-.342a.859.859,0,0,1,.459-.22C562.359,461.956,562.437,461.941,562.483,462.007Z" transform="translate(-561.695 -461.958)" fill="#fff" fill-rule="evenodd"/>
    </g>
  </g>
</svg>
