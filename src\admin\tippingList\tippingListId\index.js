import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  //   InputAdornment,
  //   IconButton,
} from "@mui/material";
// import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
// import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select, { components } from "react-select";
import { MdKeyboardBackspace } from "react-icons/md";
import "../tippingList.scss";
import _ from "lodash";

const statusOption = [
  {
    label: "Paid",
    value: "paid",
  },
  {
    label: "Not Paid",
    value: "notPaid",
  },
];

class TippingTipsterList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      tipsterValues: {
        amount: "",
        description: "",
        paymentStatus: null,
      },
      TipsterList: [],
      TipsterCount: 0,
      isSearch: "",
      selectedTipsterList: {},
      errorAmount: "",
    };
  }

  componentDidMount() {
    let propsdata = this.props.match.params;
    let id = propsdata?.id;
    this.fetchTipsterList(1, "", id);
  }

  componentDidUpdate(prevProps, prevState) {
    let propsdata = this.props.match.params;
    let compId = propsdata?.id;
    const { currentPage, isSearch } = this.state;
    if (prevState.currentPage !== this.state.currentPage) {
      this.fetchTipsterList(currentPage, isSearch, compId);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchTipsterList(1, "", compId);
      this.setState({
        offset: 0,
        currentPage: 1,
        isSearch: "",
      });
    }
  }

  async fetchTipsterList(page, search, compId) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/tipping/tipsterList?competitionId=${compId}&limit=${rowPerPage}&page=${page}&search=${search}`
      );
      if (status === 200) {
        this.setState({
          TipsterList: data?.result,
          isLoading: false,
          TipsterCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { tipsterValues } = this.state;
    let flag = true;
    if (
      tipsterValues?.amount?.trim() === "" ||
      tipsterValues?.amount === null
    ) {
      flag = false;
      this.setState({
        errorAmount: "This field is mandatory",
      });
    } else {
      this.setState({
        errorAmount: "",
      });
    }
    return flag;
  };

  //   handleSave = async () => {
  //     if (this.handalValidate()) {
  //       const {
  //         tipsterValues,
  //         currentPage,
  //         isSearch,
  //       } = this.state;
  //       this.setState({ isLoading: true, isEditMode: false });
  //       const payload = {
  //         questions: tipsterValues?.amount,
  //         answer: tipsterValues?.description,
  //       };

  //       try {
  //         const { status, data } = await axiosInstance.post(
  //           `/tippingFaqs/faqs`,
  //           payload
  //         );
  //         if (status === 200) {
  //           this.setState({ isLoading: false, isInputModalOpen: false });
  //           this.fetchTipsterList(
  //             currentPage,
  //             isSearch,
  //             compId
  //           );
  //           this.setActionMessage(true, "Success", data?.message);
  //         } else {
  //           this.setState({ isLoading: false, isInputModalOpen: false });
  //           this.setActionMessage(true, "Error", data?.message);
  //         }
  //       } catch (err) {
  //         this.setState({ isLoading: false, isInputModalOpen: false });
  //         this.setActionMessage(true, "Error", err?.response?.data?.message);
  //       }
  //     }
  //   };

  handleUpdate = async () => {
    const { tipsterValues, selectedTipsterList, currentPage, isSearch } =
      this.state;
    let propsdata = this.props.match.params;
    let compId = propsdata?.id;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        price: tipsterValues?.amount,
        paymentDescription: tipsterValues?.description,
        UserId: selectedTipsterList?.UserId,
        competitionId: selectedTipsterList?.competitionId,
        status: tipsterValues?.paymentStatus === "paid" ? "success" : "failed",
      };

      try {
        const { status, data } = await axiosInstance.post(
          `/tipping/addPayment`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchTipsterList(currentPage, isSearch, compId);
          this.setActionMessage(true, "Success", data?.message);
          this.toggleInputModal();
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      tipsterValues: {
        amount: "",
        description: "",
        paymentStatus: null,
      },
      errorAmount: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        isEditMode: true,
        selectedTipsterList: item,
      });
    } else {
      this.setState({
        tipsterValues: {
          amount: "",
          description: "",
          paymentStatus: null,
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  //   deleteItem = async () => {
  //     const { itemToDelete, currentPage, isSearch,  } =
  //       this.state;
  //       let propsdata = this.props.match.params;
  //       let compId = propsdata?.id;
  //     try {
  //       const { status, data } = await axiosInstance.delete(
  //         `/tippingFaqs/faqs/${itemToDelete}`
  //       );
  //       if (status === 200) {
  //         this.setState({ itemToDelete: null, isModalOpen: false }, () => {
  //           this.fetchTipsterList(currentPage, isSearch,compId);
  //         });
  //         this.setActionMessage(true, "Success", data?.message);
  //       }
  //     } catch (err) {
  //       this.setActionMessage(true, "Error", err?.response?.data?.message);
  //     }
  //   };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, TipsterList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  //   handleClearClick = () => {
  //     // this.fetchTipsterList(1, "");
  //     this.setState({
  //       offset: 0,
  //       currentPage: 1,
  //       isSearch: "",
  //     });
  //   };

  DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        {/* <SelectIndicator /> */}
      </components.DropdownIndicator>
    );
  };

  backToNavigatePage = () => {
    this.props.navigate(`/tipping-list`);
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      //   offset,
      tipsterValues,
      TipsterList,
      TipsterCount,
      errorAmount,
      //   isSearch,
    } = this.state;
    const pageNumbers = [];

    if (TipsterCount > 0) {
      for (let i = 1; i <= Math.ceil(TipsterCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    let propsdata = this.props.match.params;
    let compName = propsdata?.compName;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Tipster List</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={8}>
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    columnGap: "20px",
                  }}
                >
                  <Button
                    className="admin-btn-margin admin-btn-back"
                    onClick={this.backToNavigatePage}
                  >
                    <MdKeyboardBackspace />
                  </Button>
                  <Typography variant="h1" align="left">
                    {compName}
                  </Typography>
                </Box>
              </Grid>

              <Grid
                item
                xs={4}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                {/* <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() =>
                    this.fetchTipsterList(
                      currentPage,
                      isSearch,
                    )
                  }
                >
                  Search
                </Button> */}
              </Grid>
              {/* <Grid
              item
              xs={12}
              className="admin-filter-wrap admin-fixture-wrap"
              style={{ marginBottom: "10px", marginTop: "12px" }}
            >
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "6px 10px",
                }}
                onClick={this.inputModal(null, "create")}
              >
                Add New
              </Button>
            </Grid> */}
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TipsterList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TipsterList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell style={{ cursor: "pointer" }}>ID</TableCell>
                      <TableCell>User Id</TableCell>
                      <TableCell>User Name</TableCell>
                      <TableCell>Amount</TableCell>
                      <TableCell>Payment Type</TableCell>
                      <TableCell>Payment Status</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {TipsterList?.map((item, index) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={index}
                        >
                          <TableCell>{item?.rank} </TableCell>
                          <TableCell>{item?.UserId}</TableCell>
                          <TableCell>
                            {item?.firstName + " " + item?.lastName}
                          </TableCell>
                          <TableCell>
                            {item?.amount ? "$" + item?.amount : "-"}
                          </TableCell>
                          <TableCell>{item?.paymentType ?? "-"}</TableCell>
                          <TableCell>{item?.status}</TableCell>
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className={`table-btn edit-btn ${
                                item?.status === "Not Paid"
                                  ? "not-paid-btn"
                                  : "paid-btn"
                              }`}
                              disabled={
                                item?.status === "Not Paid" ? false : true
                              }
                            >
                              {item?.status === "Not Paid"
                                ? "Not Paid"
                                : "Paid"}
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              TipsterCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Tipster List"
                    : "Edit Tipster List"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">
                          {" "}
                          Amount <span className="color-red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Amount"
                          value={tipsterValues?.amount}
                          onChange={(e) =>
                            this.setState({
                              tipsterValues: {
                                ...tipsterValues,
                                amount: e?.target?.value,
                              },
                              errorAmount: e?.target?.value ? "" : errorAmount,
                            })
                          }
                        />
                        {errorAmount ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorAmount}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Description</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          multiline
                          rows={4}
                          placeholder="Description"
                          value={tipsterValues?.description}
                          onChange={(e) =>
                            this.setState({
                              tipsterValues: {
                                ...tipsterValues,
                                description: e?.target?.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Status</label>
                        <Select
                          className="React teamsport-select event-Tournament-select premium-key-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Status"
                          value={
                            tipsterValues?.paymentStatus &&
                            statusOption?.find((item) => {
                              return (
                                item?.value === tipsterValues?.paymentStatus
                              );
                            })
                          }
                          options={statusOption}
                          onChange={(e) => {
                            this.setState({
                              tipsterValues: {
                                ...tipsterValues,
                                paymentStatus: e?.value,
                              },
                            });
                          }}
                        />
                      </Grid>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default TippingTipsterList;
