import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TextField,
  Button,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  InputAdornment,
} from "@mui/material";
import { Loader } from "../../../library/common/components";
import { <PERSON> } from "react-router-dom";
import Pagination from "@mui/material/Pagination";
import TrackRaceResult from "./TrackRaceResult";
import TrackListCountdown from "./TrackListCountdown";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
import { URLS } from "../../../library/common/constants";
import axiosInstance from "../../../helpers/Axios";
import moment from "moment";
import CancelIcon from "@mui/icons-material/Cancel";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import CreateEvents from "../../events/CreateEvents/index";
import FixturesFilter from "./Filters/FixturesFilter";
import CreateApiEventIdentifire from "../../apiEventIdentifire/CreateApiEventIdentifire";
// import CreateRaceTable from "../../raceTable/CreateRaceTable";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import SearchIcons from "../../../images/searchIcon.svg";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import DateFnsUtils from "@date-io/date-fns";
import _ from "lodash";
let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
class RacingMeetings extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      meetingsDetails: [],
      identifireDetails: [],
      raceDetail: [],
      isLoading: false,
      isLoadings: false,
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      sportCount: null,
      inputModalOpen: false,
      identifireModalOpen: false,
      isInputFormModalOpen: false,
      isRaceDetailModal: false,
      inputRaceFormOpen: false,
      isDeleteModalOpen: false,
      isDeleteRaceModalOpen: false,
      isDeleteIdentifireModalOpen: false,
      isEditMode: false,
      isYesterday: false,
      // isRaceEdit: false,
      idToSend: "",
      searchInput: "",
      // sportAll: [],
      locationAll: [],
      weatherAll: [],
      trackAll: [],
      allEvents: [],
      allProvider: [],
      // distanceAll: [],
      eventIdToSend: null,
      itemToDelete: null,
      selectedEvent: "",
      searchIdentifire: "",
      selectedDate: null,
      isDeleteLoading: "",
    };
  }

  componentDidMount() {
    let id = this.props.match.params.id;
    if (this.props.match.params.name === "all") {
      this.fetchTrackList(
        "1,2,3",
        "Aus/NZ,Intl",
        moment().utc().format("YYYY-MM-DD")
      );
    } else {
      this.fetchAllEvents(id);
    }
    this.fetchAllBookkeepers();
  }

  componentDidUpdate(prevProps, prevState) {
    let id = this.props.match.params.id;
    if (prevProps.match !== this.props.match) {
      if (this.state.offset === 0) {
        // this.fetchAllEvents(id);
        if (this.props.match.params.name === "all") {
          this.fetchTrackList(
            "1,2,3",
            "Aus/NZ,Intl",
            moment().utc().format("YYYY-MM-DD")
          );
        } else {
          this.fetchAllEvents(id);
        }
      } else {
        this.setState({ offset: 0, currentPage: 1, searchInput: "" });
      }
      this.setState({ searchInput: "" });
    }
    if (prevState.offset !== this.state.offset) {
      // this.fetchAllEvents(id);
      if (this.props.match.params.name === "all") {
        this.fetchTrackList(
          "1,2,3",
          "Aus/NZ,Intl",
          moment().utc().format("YYYY-MM-DD")
        );
      } else {
        this.fetchAllEvents(id);
      }
    }
  }

  fetchAllEvents = async () => {
    let id = this.props.match.params.id;
    let { rowPerPage, offset, searchInput, selectedDate } = this.state;
    let date_pass =
      selectedDate !== null
        ? moment.utc(selectedDate).format("YYYY-MM-DD")
        : "";
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      `/events/sportId/${id}?limit=${rowPerPage}&offset=${offset}&matchString=${searchInput}&todate=${date_pass}`
    );
    if (status === 200) {
      this.setState({
        meetingsDetails: data.events.rows,
        isLoading: false,
        sportCount: data.count,
      });
    }
    // this.fetchAllSport();
    this.fetchAllLocation();
    // this.fetchAllWeather();
    this.fetchAllTrack();

    // this.fetchAllDistance();
  };
  fetchTrackList = async (ids, countries, todate) => {
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `events/trackList/?sportId=${ids}&MeetingState=${countries}&todate=${todate}&countryId=${""}&stateId=${""}&timezone=${timezone}`
      );
      if (status === 200) {
        this.setState({
          meetingsDetails: data.events,
          sportCount: data.count,
          isLoading: false,
        });
      }
    } catch (err) {}
    this.fetchAllLocation();
    // this.fetchAllWeather();
    this.fetchAllTrack();
  };
  // fetchFilterEvents = async () => {
  //   let id = this.props.match.params.id;
  //   let { rowPerPage, offset, searchInput, selectedDate } = this.state;
  //   this.setState({ isLoading: true });
  //   const { status, data } = await axiosInstance.get(
  //     `/events/sportId/${id}?limit=${rowPerPage}&offset=${offset}&matchString=${searchInput}`
  //   );
  //   if (status === 200) {
  //     let data_array = data.events;
  //     if (selectedDate !== null) {
  //       data_array = data.events?.filter(
  //         (obj) =>
  //           moment(obj.eventToDate).format("YYYY/MM/DD") ==
  //           moment(selectedDate).format("YYYY/MM/DD")
  //       );
  //     }
  //     this.setState({
  //       meetingsDetails: data_array,
  //       isLoading: false,
  //       sportCount: data.count,
  //     });
  //   }
  // };

  // fetchAllSport = async (id) => {
  //   const { status, data } = await axiosInstance.get(URLS.sports);
  //   if (status === 200) {
  //     this.setState({ sportAll: data.result });
  //   }
  // };

  fetchAllLocation = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.location);
    if (status === 200) {
      let data_obj = _.orderBy(data?.result, ["venueName"], ["asc"]);
      this.setState({ locationAll: data_obj });
    }
  };

  // fetchAllWeather = async (id) => {
  //   const { status, data } = await axiosInstance.get(URLS.weather);
  //   if (status === 200) {
  //     this.setState({ weatherAll: data.result });
  //   }
  // };

  fetchAllTrack = async () => {
    const { status, data } = await axiosInstance.get(URLS.track);
    if (status === 200) {
      let data_obj = _.orderBy(data?.result?.rows, ["name"], ["asc"]);
      this.setState({ trackAll: data_obj });
    }
  };

  async fetchEvents() {
    const { status, data } = await axiosInstance.get(
      URLS.events + `/${this.props.match.params.id}`
    );
    if (status === 200) {
      this.setState({ allEvents: data.result });
    }
  }

  async fetchAllBookkeepers() {
    const { status, data } = await axiosInstance.get(URLS.apiProvider);
    if (status === 200) {
      let data_obj = _.orderBy(data?.result, ["providerName"], ["asc"]);
      this.setState({ allProvider: data_obj });
    }
  }

  // fetchAllDistance = async () => {
  //   const { status, data } = await axiosInstance.get(URLS.distance);
  //   if (status === 200) {
  //     this.setState({ distanceAll: data.result });
  //   }
  // };

  getProvider = (id) => {
    let { allProvider } = this.state;
    let provider = allProvider
      .filter((obj) => obj.id === id)
      .map((object) => object.providerName);
    return provider;
  };

  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      //  offset
    } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  toggleInputModal = () => {
    this.setState({ inputModalOpen: false });
  };

  inputModal = (id) => () => {
    this.setState({ inputModalOpen: true, idToSend: id });
  };

  toggleIdentifireModal = () => {
    this.setState({
      identifireModalOpen: !this.state.identifireModalOpen,
      searchIdentifire: "",
    });
  };

  toggleInputFormModal = () => {
    this.setState({ isInputFormModalOpen: !this.state.isInputFormModalOpen });
  };

  toggleRaceDetailModal = () => {
    this.setState({ isRaceDetailModal: !this.state.isRaceDetailModal });
  };

  inputFormModal = (id, type) => {
    this.setState({
      isInputFormModalOpen: true,
      isEditMode: type,
      searchIdentifire: "",
    });
  };

  showIdentifire = async (id) => {
    let { eventIdToSend } = this.state;
    this.setState({
      identifireModalOpen: true,
      isLoadings: true,
    });
    let getId = eventIdToSend === null ? id : eventIdToSend;
    try {
      const { status, data } = await axiosInstance.get(
        `/event/identifier/eventid/${getId}`
      );
      if (status === 200) {
        this.setState({
          identifireDetails: data.eventIdentifiers,
          isLoadings: false,
        });
      }
    } catch (err) {
      this.setState({ isLoadings: false });
    }
  };

  showRaceModel = async (id) => {
    this.setState({
      isRaceDetailModal: true,
      isLoading: true,
      eventIdToSend: id,
    });
    try {
      const { status, data } = await axiosInstance.get(`/events/race/${id}`);
      if (status === 200) {
        this.setState({
          raceDetail: data.result,
          isLoading: false,
        });
      }
    } catch (err) {
      this.setState({ isLoading: false });
    }
  };

  // toggleRaceFormModal = () => {
  //   this.setState({ inputRaceFormOpen: !this.state.inputRaceFormOpen });
  // };

  // inputRaceFormModal = (id, type) => {
  //   this.setState({ inputRaceFormOpen: true, idToSend: id, isRaceEdit: type });
  // };

  afterChangeRefresh = async () => {
    // let id = this.props.match.params.id;
    // this.fetchAllEvents(id);
    this.fetchTrackList(
      "1,2,3",
      "Aus/NZ,Intl",
      moment().utc().format("YYYY-MM-DD")
    );
  };

  handleEventsDelete = async () => {
    let { itemToDelete } = this.state;
    this.setState({ isDeleteLoading: "eventDelete", isDeleteModalOpen: false });
    try {
      const { status } = await axiosInstance.delete(
        `events/event/${itemToDelete}`
      );
      if (status === 200) {
        this.afterChangeRefresh();
        this.setState({ isDeleteLoading: "", itemToDelete: null });
      }
    } catch (err) {}
  };

  handleIdentifireDelete = async () => {
    let { itemToDelete } = this.state;
    this.setState({
      isDeleteLoading: "identifireDelete",
      isDeleteIdentifireModalOpen: false,
    });
    try {
      const { status } = await axiosInstance.delete(
        `/event/identifier/${itemToDelete}`
      );
      if (status === 200) {
        this.showIdentifire();
        this.setState({ isDeleteLoading: "", itemToDelete: null });
      }
    } catch (err) {}
  };

  handleRaceDelete = async () => {
    let { itemToDelete } = this.state;
    try {
      const { status } = await axiosInstance.delete(
        `races/race/${itemToDelete}`
      );
      if (status === 200) {
        this.afterChangeRefresh();
      }
    } catch (err) {}
  };

  setEventToDelete = (id) => {
    this.setState({ itemToDelete: id, isDeleteModalOpen: true });
  };

  toggleDeleteModal = () => {
    this.setState({
      isDeleteModalOpen: !this.state.isDeleteModalOpen,
      itemToDelete: null,
    });
  };

  setRaceToDelete = (id) => {
    this.setState({ itemToDelete: id, isDeleteRaceModalOpen: true });
  };

  toggleRaceDeleteModal = () => {
    this.setState({
      isDeleteRaceModalOpen: !this.state.isDeleteRaceModalOpen,
      itemToDelete: null,
    });
  };

  setIdentifireToDelete = (id) => {
    this.setState({ itemToDelete: id, isDeleteIdentifireModalOpen: true });
  };

  toggleIdentifireDeleteModal = () => {
    this.setState({
      isDeleteIdentifireModalOpen: !this.state.isDeleteIdentifireModalOpen,
      itemToDelete: null,
    });
  };

  handleDateChange = (date) => {
    this.setState({ selectedDate: date });
  };

  handlePaginationButtonClick = (navDirection) => {
    let {
      currentPage,
      //  rowPerPage,
      offset,
    } = this.state;
    // if (navDirection === "prev") {
    //   if (currentPage > 1) {
    //     this.setState({ currentPage: currentPage - 1 });
    //   }
    // } else {
    //   if (currentPage < meetingsDetails.length / rowPerPage)
    //     this.setState({ currentPage: currentPage + 1 });
    // }

    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  checkRaceMinCell = (time, type) => {
    let current_time_string = moment().format("YYYY/MM/DD HH:mm:ss");
    let end_time_string = moment(time).format("YYYY/MM/DD HH:mm:ss");
    let diff_sec = moment(end_time_string).diff(current_time_string, "second");
    let class_to_pass = "";
    if (type === "desktop") {
      class_to_pass = !isNaN(diff_sec)
        ? // ? diff_sec > 1800
          //   ? "interim"
          diff_sec <= 1800 && diff_sec > 300
          ? "close-secondary"
          : diff_sec <= 300 && diff_sec > 0
          ? "close"
          : diff_sec < 0
          ? "upcoming_race_cell_close interim"
          : ""
        : "";
    } else {
      class_to_pass = !isNaN(diff_sec)
        ? diff_sec <= 600 && diff_sec > 0
          ? "upcoming_race_mobile"
          : ""
        : "";
    }
    return class_to_pass;
  };

  racevalue = (item, rno, meetingsDetails) => {
    var raceItem = item.find((value) => value.raceNumber === rno);

    if (
      raceItem?.startTimeDate !== null &&
      moment(new Date(raceItem?.startTimeDate)).isBefore(new Date())
    ) {
      return (
        <TrackRaceResult // if race is close
          race={item}
          race_obj={raceItem}
          meetingsDetails={meetingsDetails}
          // key={i}
          intl={true}
          // raceData={
          //   item?.RaceData
          //     ?.intlData
          // }
          isMobile={false}
        />
      );
    } else {
      return (
        <TrackListCountdown // if race is upcoming
          race={item}
          race_obj={raceItem}
          meetingsDetails={meetingsDetails}
          // key={i}
          intl={true}
          // raceData={
          //   item?.RaceData
          //     ?.intlData
          // }
          expiryTimestamp={new Date(
            new Date(raceItem?.startTimeDate).toUTCString()
          ).getTime()}
          checkRaceMinCell={this.checkRaceMinCell}
          isMobile={false}
        />
      );
    }
  };
  render() {
    const { match } = this.props;
    let {
      meetingsDetails,
      identifireDetails,
      isLoading,
      rowPerPage,
      currentPage,
      searchInput,
      inputModalOpen,
      identifireModalOpen,
      isInputFormModalOpen,
      // isRaceDetailModal,
      // inputRaceFormOpen,
      locationAll,
      weatherAll,
      trackAll,
      // sportAll,
      allEvents,
      allProvider,
      // distanceAll,
      isEditMode,
      // isRaceEdit,
      idToSend,
      eventIdToSend,
      // offset,
      sportCount,
      // raceDetail,
      // itemToDelete,
      isDeleteModalOpen,
      isDeleteIdentifireModalOpen,
      // isDeleteRaceModalOpen,
      selectedEvent,
      searchIdentifire,
      selectedDate,
      isDeleteLoading,
      isLoadings,
    } = this.state;
    const pageNumbers = [];

    // searchInput !== "" &&
    //   (meetingsDetails = meetingsDetails?.filter(
    //     (obj) =>
    //       obj?.eventName
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase()) ||
    //       obj?.variation
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase())
    //   ));

    searchIdentifire !== "" &&
      (identifireDetails = identifireDetails?.filter(
        (obj) =>
          obj?.apiEventId
            ?.toString()
            .toLowerCase()
            .includes(searchIdentifire.toString().toLowerCase()) ||
          obj?.id
            ?.toString()
            .toLowerCase()
            .includes(searchIdentifire.toString().toLowerCase())
      ));

    // let currentPageRow = meetingsDetails;

    if (sportCount > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = meetingsDetails.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin pageWrapper">
          <Grid item xs={12}>
            {/* <Paper className="pageWrapper"> */}
            {isDeleteLoading === "eventDelete" && (
              <div class="admin-delete-loader">
                <Loader />
              </div>
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  racing
                </Link>
                <Link underline="hover" color="inherit">
                  {/* Horse Racing*/}
                  {this.props.match.params.name}
                </Link>
                <Typography className="active_p">Fixtures</Typography>
              </Breadcrumbs>
            </Box>
            <Grid
              container
              direction="row"
              alignItems="space-around"
              style={{ alignItems: "center" }}
            >
              <Grid item xs={4}>
                {/* <h3 className="text-left">Event Details</h3> */}
                <Typography variant="h1" align="left">
                  Event Details
                </Typography>
              </Grid>
              {this.props.match.params.name !== "all" && (
                <>
                  <Grid item xs={4} className="admin-filter-wrap">
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <Grid container style={{ justifyContent: "end" }}>
                        <DesktopDatePicker
                          disableToolbar
                          variant="inline"
                          format="MM/dd/yyyy"
                          placeholder="MM/DD/YYY"
                          margin="normal"
                          id="date-picker-inline"
                          inputVariant="outlined"
                          value={selectedDate ? parseISO(selectedDate) : null}
                          onChange={this.handleDateChange}
                          KeyboardButtonProps={{
                            "aria-label": "change date",
                          }}
                          style={{ marginRight: 5 }}
                          className="details-search-picker"
                        />
                      </Grid>
                    </LocalizationProvider>
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      className="textfield-tracks"
                      variant="outlined"
                      color="primary"
                      size="small"
                      style={{
                        width: "370px",
                        color: "#D4D6D8",
                      }}
                      // label="Search"
                      placeholder="Search (searches tracks)"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <img src={SearchIcons} alt="icon" />
                            {/* <SearchIcon /> */}
                          </InputAdornment>
                        ),
                      }}
                      value={searchInput}
                      onChange={(e) =>
                        this.setState({
                          ...this.state.searchInput,
                          searchInput: e.target.value,
                        })
                      }
                    />
                  </Grid>
                  <Grid item xs={1}>
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "13px 24px 12px",
                      }}
                      onClick={() => this.fetchAllEvents()}
                    >
                      Search
                    </Button>
                  </Grid>
                </>
              )}
            </Grid>
            <Grid container>
              <Grid item xs={12}>
                {this.props.match.params.name === "all" && (
                  <FixturesFilter fetchTrackList={this.fetchTrackList} />
                )}
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && meetingsDetails.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && meetingsDetails.length > 0 && (
              <>
                <TableContainer>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                    style={{ minWidth: "max-content" }}
                  >
                    <TableHead>
                      <TableRow className="tableHead-row">
                        <TableCell>DID</TableCell>
                        <TableCell>
                          {/* Event Name */}
                          Track
                        </TableCell>
                        <TableCell style={{ padding: "0px" }}>
                          Event Date
                        </TableCell>
                        {/* <TableCell>
                          Track ID
                        </TableCell> */}
                        {/* <TableCell>Variation</TableCell>
                          <TableCell>Description</TableCell> */}
                        {/* <TableCell>Race Details</TableCell> */}
                        <TableCell>Action</TableCell>
                        <TableCell style={{ padding: "0px" }}>
                          View/Add Identifier
                        </TableCell>
                        {/* <TableCell>Created At</TableCell> */}
                        <TableCell className="race-no">R1</TableCell>
                        <TableCell className="race-no">R2</TableCell>
                        <TableCell className="race-no">R3</TableCell>
                        <TableCell className="race-no">R4</TableCell>
                        <TableCell className="race-no">R5</TableCell>
                        <TableCell className="race-no">R6</TableCell>
                        <TableCell className="race-no">R7</TableCell>
                        <TableCell className="race-no">R8</TableCell>
                        <TableCell className="race-no">R9</TableCell>
                        <TableCell className="race-no">R10</TableCell>
                        <TableCell className="race-no">R11</TableCell>
                        <TableCell className="race-no">R12</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {meetingsDetails.map((meetingsDetails, i) => (
                        <>
                          <TableRow key={i} className="listTable-Row">
                            <TableCell>{meetingsDetails.id}</TableCell>
                            <TableCell>{meetingsDetails.eventName}</TableCell>
                            <TableCell
                              style={{ minWidth: "200px", padding: "0px" }}
                            >
                              {moment(meetingsDetails.eventToDate).format(
                                "DD/MM/YYYY hh:mm:ss a"
                              )}
                            </TableCell>
                            {/* <TableCell>{meetingsDetails.trackId}</TableCell> */}
                            {/* <TableCell>{meetingsDetails.variation}</TableCell>
                              <TableCell>{meetingsDetails.description}</TableCell> */}
                            {/* <TableCell>
                              <Button
                                style={{
                                  fontSize: "9px",
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  fontWeight: "400",
                                  textTransform: "none",
                                  padding: "5px 0",
                                }}
                                onClick={() =>
                                  // this.showRaceModel(meetingsDetails?.id)
                                  this.props.navigate(
                                    `/racing/${match?.params?.name}/${match?.params?.id}/${meetingsDetails?.id}/${meetingsDetails.eventName}`
                                  )
                                }
                              >
                                View
                              </Button>
                            </TableCell> */}
                            <TableCell>
                              <Button
                                onClick={this.inputModal(meetingsDetails.id)}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={() =>
                                  this.setEventToDelete(meetingsDetails?.id)
                                }
                                style={{ cursor: "pointer" }}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                            <TableCell style={{ padding: "0px" }}>
                              <Button
                                style={{
                                  fontSize: "9px",
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  fontWeight: "400",
                                  textTransform: "none",
                                  padding: "5px 5px",
                                }}
                                onClick={() => {
                                  this.setState(
                                    {
                                      selectedEvent: meetingsDetails.eventName,
                                      eventIdToSend: meetingsDetails?.id,
                                    },
                                    () =>
                                      this.showIdentifire(meetingsDetails?.id)
                                  );
                                }}
                              >
                                View/Add Identifier
                              </Button>
                            </TableCell>
                            {/* <TableCell style={{ minWidth: "200px" }}>
                              {moment(meetingsDetails.createdAt).format(
                                "DD/MM/YYYY hh:mm:ss a"
                              )}
                            </TableCell> */}
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  1,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  2,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  3,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  4,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  5,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  6,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  7,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  8,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  9,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  10,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  11,
                                  meetingsDetails
                                )}
                            </TableCell>
                            <TableCell className="race-no">
                              {meetingsDetails.race &&
                                this.racevalue(
                                  meetingsDetails.race,
                                  12,
                                  meetingsDetails
                                )}
                            </TableCell>
                          </TableRow>
                        </>
                      ))}
                      {this.props.match.params.name !== "all" && (
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                                    className={
                                      offset >= 20
                                        ? "btn-navigation"
                                        : "btn-navigation-disabled"
                                    }
                                    disabled={offset >= 20 ? false : true}
                                    // disabled={
                                    //   meetingsDetails.length / rowPerPage > 1 ? false : true
                                    // }
                                    onClick={() =>
                                      this.handlePaginationButtonClick("prev")
                                    }
                                  >
                                    <ReactSVG src={arrowLeft} />
                                  </button> */}
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={sportCount > 0 ? false : true}
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />
                              {/* <button
                                    className={
                                      rowPerPage + offset < sportCount
                                        ? "btn-navigation"
                                        : "btn-navigation-disabled"
                                    }
                                    disabled={
                                      rowPerPage + offset < sportCount ? false : true
                                    }
                                    onClick={() =>
                                      this.handlePaginationButtonClick("next")
                                    }
                                  >
                                    <ReactSVG src={arrowRight} />
                                  </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            {/* </Paper> */}
            <Modal
              className="modal modal-input"
              open={inputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {`Edit ${match.params.name} Details`}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateEvents
                  inputModal={this.toggleInputModal}
                  id={idToSend}
                  isEditMode={true}
                  fetchAllEvents={this.afterChangeRefresh}
                  sportId={match.params.id}
                  // sportAll={sportAll}
                  locationAll={locationAll}
                  weatherAll={weatherAll}
                  trackAll={trackAll}
                  isOtherSport={false}
                />
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={identifireModalOpen}
              onClose={this.toggleIdentifireModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <Grid container item xs={12}>
                  <Grid item xs={12}>
                    <h3 className="text-center">{`Bookmaker Feed Identifiers (${selectedEvent})`}</h3>
                  </Grid>
                </Grid>
                <Grid container item xs={12}>
                  <div
                    style={{
                      margin: "15px 0px ",
                      width: "100%",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <Grid item xs={9}>
                      {/* <input
                        type="text"
                        placeholder="search"
                        value={searchIdentifire}
                        onChange={(e) => {
                          this.setState({
                            ...this.state.searchIdentifire,
                            searchIdentifire: e.target.value,
                          });
                        }}
                        style={{
                          fontSize: "16px",
                          borderRadius: "3px",
                          minHeight: "30px",
                          border: "1px solid #ddd",
                          // marginRight: "15px",
                          width: "100%",
                        }}
                      /> */}
                      <TextField
                        className="textfield-tracks"
                        variant="outlined"
                        color="primary"
                        size="small"
                        style={{
                          fontSize: "16px",
                          borderRadius: "3px",
                          minHeight: "45px",

                          // marginRight: "15px",
                          width: "100%",
                        }}
                        // label="Search"
                        placeholder="Search "
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <img src={SearchIcons} alt="icon" />
                              {/* <SearchIcon /> */}
                            </InputAdornment>
                          ),
                        }}
                        value={searchIdentifire}
                        onChange={(e) => {
                          this.setState({
                            ...this.state.searchIdentifire,
                            searchIdentifire: e.target.value,
                          });
                        }}
                      />
                    </Grid>
                    <Grid
                      item
                      xs={3}
                      style={{
                        display: "flex",
                        justifyContent: "flex-end",
                        alignItems: "center",
                      }}
                    >
                      {/* <Button
                        style={{
                          textTransform: "none",
                          height: "28px",
                          fontSize: "12px",
                          backgroundColor: "RGB(68, 85, 199)",
                          color: "#fff",
                          fontWeight: "400",
                        }}
                        onClick={() => {
                          this.inputFormModal(null, false);
                        }}
                      >
                        Add Identifier
                      </Button> */}
                      <Button
                        variant="contained"
                        style={{
                          backgroundColor: "#4455C7",
                          color: "#fff",
                          borderRadius: "8px",
                          textTransform: "capitalize",
                          padding: "8px 10px",
                        }}
                        onClick={() => {
                          this.inputFormModal(null, false);
                        }}
                      >
                        Add Identifier
                      </Button>
                    </Grid>
                  </div>
                </Grid>

                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleIdentifireModal}
                />
                {isDeleteLoading === "identifireDelete" && (
                  <div class="admin-delete-modal-loader">
                    <Loader />
                  </div>
                )}
                {isLoadings && (
                  <div style={{ display: "flex", justifyContent: "center" }}>
                    <Loader />
                  </div>
                )}
                {!isLoadings && identifireDetails.length === 0 && (
                  <p>No Data Available</p>
                )}
                {!isLoadings && identifireDetails.length > 0 && (
                  <>
                    <TableContainer component={Paper}>
                      <Table
                        className="listTable api-provider-listTable"
                        aria-label="simple table"
                        style={{
                          minWidth: "max-content",
                        }}
                      >
                        <TableHead>
                          <TableRow className="tableHead-row">
                            <TableCell>DID</TableCell>
                            <TableCell>Meeting Id</TableCell>
                            <TableCell>Bookmaker</TableCell>
                            <TableCell>CreatedAt</TableCell>
                            <TableCell>Action</TableCell>
                            {/* <TableCell>Edit</TableCell>
                            <TableCell>Delete</TableCell> */}
                          </TableRow>
                        </TableHead>
                        <TableBody className="table_body">
                          <TableRow className="table_row">
                            <TableCell
                              colSpan={100}
                              className="table-seprator"
                            ></TableCell>
                          </TableRow>
                          {identifireDetails?.map((api, i) => (
                            <TableRow key={i}>
                              <TableCell>{api.id}</TableCell>
                              <TableCell>{api.apiEventId}</TableCell>
                              <TableCell>
                                {this.getProvider(api.providerId)}
                              </TableCell>
                              <TableCell>
                                {moment(api.createdAt).format(
                                  "DD/MM/YYYY hh:mm:ss a"
                                )}
                              </TableCell>
                              <TableCell>
                                <Button
                                  onClick={() => {
                                    this.inputFormModal(api.id, true);
                                    this.setState({ idToSend: api.id });
                                  }}
                                  className="table-btn edit-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={() =>
                                    this.setIdentifireToDelete(api?.id)
                                  }
                                  className="table-btn delete-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                )}
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={isInputFormModalOpen}
              onClose={this.toggleInputFormModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create Bookmaker Feed Identifiers"
                    : "Edit Bookmaker Feed Identifier"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputFormModal}
                />
                <CreateApiEventIdentifire
                  inputModal={this.toggleInputFormModal}
                  id={idToSend}
                  eventIdToSend={eventIdToSend}
                  isEditMode={isEditMode}
                  fetchAllEventIdentifire={this.showIdentifire}
                  isMeetings={true}
                  allEvents={allEvents}
                  allProvider={allProvider}
                  isOtherSport={true}
                  otherSportName={"Meeting"}
                />
              </div>
            </Modal>

            <ShowModal
              isModalOpen={isDeleteModalOpen}
              onClose={this.toggleDeleteModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.handleEventsDelete}
              onCancel={this.toggleDeleteModal}
            />

            <ShowModal
              isModalOpen={isDeleteIdentifireModalOpen}
              onClose={this.toggleIdentifireDeleteModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.handleIdentifireDelete}
              onCancel={this.toggleIdentifireDeleteModal}
            />

            {/* <Modal
              className='modal modal-input'
              open={isRaceDetailModal}
              onClose={this.toggleRaceDetailModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <h3 className='text-center'>{`Race Details`}</h3>
                  <Button
                    style={{
                      textTransform: "none",
                      height: "28px",
                      marginTop: "15px",
                      fontSize: "12px",
                      backgroundColor: "#ff6b00",
                      color: "#fff",
                      fontWeight: "400",
                    }}
                    onClick={() => this.inputRaceFormModal(null, false)}
                  >
                    Add Race
                  </Button>
                </div>
                <CancelIcon
                  className='admin-close-icon'
                  onClick={this.toggleRaceDetailModal}
                />

                {isLoading && (
                  <div style={{ display: "flex", justifyContent: "center" }}>
                    <Loader />
                  </div>
                )}
                {!isLoading && raceDetail.length === 0 && (
                  <p>No Data Available</p>
                )}
                {!isLoading && raceDetail.length > 0 && (
                  <>
                    <TableContainer component={Paper}>
                      <Table
                        className='listTable api-provider-listTable'
                        aria-label='simple table'
                      >
                        <TableHead>
                          <TableRow>
                            <TableCell>DID</TableCell>
                            <TableCell>Race Name</TableCell>
                            <TableCell>Race Number</TableCell>
                            <TableCell>Distance</TableCell>
                            <TableCell>Track</TableCell>
                            <TableCell>Weather</TableCell>
                            <TableCell>Race Date</TableCell>
                            <TableCell>Edit</TableCell>
                            <TableCell>Delete</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {raceDetail?.map((api, i) => (
                            <TableRow key={i}>
                              <TableCell>{api?.id}</TableCell>
                              <TableCell>{api?.raceName}</TableCell>
                              <TableCell>{api?.raceNumber}</TableCell>
                              <TableCell>{api?.distance?.name}</TableCell>
                              <TableCell>{api?.trackId?.name}</TableCell>
                              <TableCell>{api?.weather?.name}</TableCell>
                              <TableCell>
                                {moment(api.startDate).format(
                                  "DD/MM/YYYY hh:mm:ss a"
                                )}
                              </TableCell>
                              <TableCell>
                                <EditIcon
                                  onClick={() =>
                                    this.inputRaceFormModal(api.id, true)
                                  }
                                  color='primary'
                                  className='mr10 cursor iconBtn admin-btn-green'
                                />
                              </TableCell>
                              <TableCell>
                                <DeleteOutlineIcon
                                  onClick={() => this.handleRaceDelete(api?.id)}
                                  color='primary'
                                  className='mr10 cursor iconBtn admin-btn-orange'
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                )}
              </div>
            </Modal> */}

            {/* <Modal
              className='modal modal-input'
              open={inputRaceFormOpen}
              onClose={this.toggleRaceFormModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className='text-center'>
                  {isRaceEdit
                    ? `Edit ${match.params.name} Details`
                    : `Add ${match.params.name} Details`}
                </h3>
                <CancelIcon
                  className='admin-close-icon'
                  onClick={this.toggleRaceFormModal}
                />

                <CreateRaceTable
                  inputModal={this.toggleRaceFormModal}
                  id={idToSend}
                  isEditMode={isRaceEdit}
                  fetchAllRace={this.afterChangeRefresh}
                  eventsAll={allEvents}
                  distanceAll={distanceAll}
                  weatherAll={weatherAll}
                  allSports={sportAll}
                />
              </div>
            </Modal> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default RacingMeetings;
