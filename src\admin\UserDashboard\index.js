import React, { useEffect, useState } from "react";
import { <PERSON>, Breadcrumbs, <PERSON>rid, IconButton, Typography } from "@mui/material";
import { Link } from "react-router-dom";
import { Loader } from "../../library/common/components";
import {
  <PERSON><PERSON>hart,
  Bar,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import Select from "react-select";
import "./userdashboard.scss";
import moment from "moment";
import ArrowBack from "@mui/icons-material/ArrowBack";
import ArrowForward from "@mui/icons-material/ArrowForward";
import axiosInstance from "../../helpers/Axios";

const barDataOption = [
  {
    label: "Weekly",
    value: 0,
  },

  {
    label: "Monthly",
    value: 1,
  },
  {
    label: "Yearly",
    value: 2,
  },
];

// const initialData = [
//   { date: "2023-08-31", label: "Thursday", userCount: 2 },
//   { date: "2023-09-01", label: "Friday", userCount: 6 },
//   { date: "2023-09-02", label: "Saturday", userCount: 9 },
//   { date: "2023-09-29", label: "Friday", userCount: 5 },
//   { date: "2023-09-28", label: "Thursday", userCount: 2 },
// ];

const UserDashboard = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentWeek, setCurrentWeek] = useState(moment().week());
  const [initialData, setInitialData] = useState([]);
  const [selectedOption, setSelectedOption] = useState("Weekly");
  const [currentStartDate, setCurrentStartDate] = useState(
    moment(Date()).startOf("week").format("YYYY-MM-DD")
  );

  const [filteredData, setFilteredData] = useState([]);
  const [weeklyStartDate, setWeeklyStartDate] = useState("");
  const [monthlyStartDate, setMonthlyStartDate] = useState("");
  const [yearlyStartDate, setYearlyStartDate] = useState("");
  const [weeklyEndDate, setWeeklyEndDate] = useState("");
  const [monthlyEndDate, setMonthlyEndDate] = useState("");
  const [yearlyEndDate, setYearlyEndDate] = useState("");
  const [totalUserCount, setTotalUserCount] = useState(0);
  const [currentUserCount, setCurrentUserCount] = useState(0);

  const fetchChartData = async (startDate, endDate, week, month, day, year) => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        `/user/user/barchrat?timezone=Asia/Calcutta&startDate=${startDate}&endDate=${endDate}&week=${week}&month=${month}&day=${day}&year=${year}`
      );

      if (status === 200) {
        const formattedData = data?.result?.map((item) => ({
          ...item,
          date: moment(item.date).format("DD-MM-YYYY"),
          UserCount: item?.userCount,
        }));

        const currentUsers = formattedData.reduce(
          (acc, curr) => acc + curr.userCount,
          0
        );
        setCurrentUserCount(currentUsers);
        setTotalUserCount(data?.totalUser);
        setInitialData(formattedData);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    let startDate, endDate;
    startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
    setWeeklyStartDate(startDate);
    endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
    setWeeklyEndDate(endDate);
    fetchChartData(startDate, endDate, "", "", true, "");
  }, []);

  const handleSelectOptionChnage = (option) => {
    let startDate, endDate;
    switch (option) {
      case "Weekly":
        startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
        setWeeklyStartDate(startDate);
        setWeeklyEndDate(endDate);
        setMonthlyStartDate("");
        setMonthlyEndDate("");
        setYearlyStartDate("");
        setYearlyEndDate("");
        setSelectedOption(option);
        fetchChartData(startDate, endDate, "", "", true, "");
        setCurrentStartDate(startDate);
        break;
      case "Monthly":
        startDate = moment(Date()).startOf("month").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("month").format("YYYY-MM-DD");
        setWeeklyStartDate("");
        setWeeklyEndDate("");
        let monthYear = moment(startDate).year();
        const monthName = moment(startDate).format("MMMM");
        setMonthlyStartDate(monthName);
        setMonthlyEndDate(monthYear);
        setYearlyStartDate("");
        setYearlyEndDate("");
        setSelectedOption(option);
        fetchChartData(startDate, "", "", "", true, "");
        setCurrentStartDate(startDate);
        break;
      case "Yearly":
        startDate = moment(Date()).startOf("year").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("year").format("YYYY-MM-DD");
        setWeeklyStartDate("");
        setWeeklyEndDate("");
        setMonthlyStartDate("");
        setMonthlyEndDate("");
        let year = moment(startDate).year();
        setYearlyStartDate(year);
        // setYearlyEndDate(year);
        setSelectedOption(option);
        fetchChartData(startDate, "", "", true, "", "");
        setCurrentStartDate(startDate);
        break;
      default:
        startDate = moment(Date()).startOf("week").format("YYYY-MM-DD");
        endDate = moment(Date()).endOf("week").format("YYYY-MM-DD");
        fetchChartData(startDate, endDate, "", "", true, "");
        setCurrentStartDate(startDate);
    }
  };

  const handlePrevious = () => {
    let newStartDate, newEndDate;
    switch (selectedOption) {
      case "Weekly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "weeks")
          .startOf("weeks")
          .format("YYYY-MM-DD");
        setWeeklyStartDate(newStartDate);

        newEndDate = moment(currentStartDate)
          .subtract(1, "weeks")
          .endOf("weeks")
          .format("YYYY-MM-DD");
        setWeeklyEndDate(newEndDate);
        fetchChartData(newStartDate, newEndDate, "", "", true, "");
        setCurrentStartDate(newStartDate);
        break;
      case "Monthly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "months")
          .startOf("isoMonth")
          .format("YYYY-MM-DD");

        const monthName = moment(newStartDate).format("MMMM");
        setMonthlyStartDate(monthName);

        newEndDate = moment(currentStartDate)
          .subtract(1, "months")
          .endOf("isoMonth")
          .format("YYYY-MM-DD");
        let monthYear = moment(newEndDate).year();
        setMonthlyEndDate(monthYear);
        fetchChartData(newStartDate, "", "", "", true, "");
        setCurrentStartDate(newStartDate);
        break;
      case "Yearly":
        newStartDate = moment(currentStartDate)
          .subtract(1, "years")
          .startOf("isoYear")
          .format("YYYY-MM-DD");
        let year = moment(newStartDate).year();
        setYearlyStartDate(year);
        setYearlyEndDate(newEndDate);
        fetchChartData(newStartDate, "", "", true, "", "");
        setCurrentStartDate(newStartDate);
        break;
      default:
        break;
    }
  };

  const handleNext = () => {
    let newStartDate, newEndDate;

    switch (selectedOption) {
      case "Weekly":
        newStartDate = moment(currentStartDate)
          .add(1, "weeks")
          .startOf("weeks")
          .format("YYYY-MM-DD");

        setWeeklyStartDate(newStartDate);

        newEndDate = moment(currentStartDate)
          .add(1, "weeks")
          .endOf("weeks")
          .format("YYYY-MM-DD");
        setWeeklyEndDate(newEndDate);
        fetchChartData(newStartDate, newEndDate, "", "", true, "");
        break;
      case "Monthly":
        newStartDate = moment(currentStartDate)
          .add(1, "months")
          .endOf("isoMonth")
          .format("YYYY-MM-DD");

        const monthName = moment(newStartDate).format("MMMM");
        setMonthlyStartDate(monthName);
        newEndDate = moment(currentStartDate)
          .subtract(1, "months")
          .endOf("isoMonth")
          .format("YYYY-MM-DD");
        let monthYear = moment(newEndDate).year();
        setMonthlyEndDate(monthYear);
        fetchChartData(newStartDate, "", "", "", true, "");
        break;
      case "Yearly":
        newStartDate = moment(currentStartDate)
          .add(1, "years")
          .startOf("isoYear")
          .format("YYYY-MM-DD");

        let year = moment(newStartDate).year();
        setYearlyStartDate(year);
        fetchChartData(newStartDate, "", "", true, "", "");
        break;
      default:
        break;
    }

    setCurrentStartDate(newStartDate);
  };

  const getTimePeriod = () => {
    const timePeriod =
      selectedOption === "Weekly"
        ? "Week"
        : selectedOption === "Monthly"
        ? "Month"
        : selectedOption === "Yearly"
        ? "Year"
        : "";
    return timePeriod;
  };

  return (
    <>
      <Grid container className="page-content adminLogin">
        <Grid item xs={12} className="pageWrapper">
          <Box className="bredcrumn-wrap">
            <Breadcrumbs
              separator="/"
              aria-label="breadcrumb"
              className="breadcrumb"
            >
              <Link underline="hover" color="inherit" to="/dashboard">
                Home
              </Link>
              <Link underline="hover" color="inherit">
                User Management
              </Link>
              <Typography className="active_p">User Dashboard</Typography>
            </Breadcrumbs>
          </Box>
          <Grid container direction="row" alignItems="center">
            <Grid item xs={3}>
              <Typography variant="h1" align="left">
                User Dashboard
              </Typography>
            </Grid>
            <Grid item xs={9} className="admin-filter-wrap">
              <Select
                className="React cricket-select external-select"
                classNamePrefix="select"
                placeholder="Select Status"
                options={barDataOption}
                value={barDataOption.find(
                  (option) => option?.label === selectedOption
                )}
                onChange={(option) => {
                  handleSelectOptionChnage(option?.label);
                }}
              />
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Typography>
                Total Users: {totalUserCount && totalUserCount}
              </Typography>
              <Typography>
                Current Users: {currentUserCount && currentUserCount}{" "}
              </Typography>
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap"
              style={{ marginBottom: "10px" }}
            >
              <Typography align="center">
                {weeklyStartDate && weeklyEndDate
                  ? moment(weeklyStartDate).format("DD-MM-YYYY") +
                    " - " +
                    moment(weeklyEndDate).format("DD-MM-YYYY")
                  : ""}
                {monthlyStartDate && monthlyEndDate
                  ? monthlyStartDate + " - " + monthlyEndDate
                  : ""}
                {yearlyStartDate ? yearlyStartDate : ""}
              </Typography>
            </Grid>
          </Grid>
          {isLoading && <Loader />}
          {!isLoading && initialData?.length === 0 && (
            <p className="text-center NoDataPadding">No data available.</p>
          )}
          <Box className="dashboard-box">
            <Box className="bar-chart-box">
              {!isLoading && initialData?.length > 0 && (
                <>
                  <Typography variant="h6" align="center">
                    User Data
                  </Typography>

                  <Box className="mt-22">
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart data={initialData && initialData}>
                        <XAxis
                          dataKey={
                            selectedOption === "Monthly" ? "date" : "label"
                          }
                        />
                        <YAxis allowDecimals={false} interval={1} />
                        <Tooltip cursor={{ fill: "transparent" }} />
                        <Legend />
                        <Bar dataKey="UserCount" fill="#4455C7" />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </>
              )}
              {!isLoading && initialData.length > 0 ? (
                <Box className="arrow-box mt-22 text-center">
                  <Box
                    className="flex mr-30 arrow"
                    onClick={() => handlePrevious()}
                  >
                    <ArrowBack />
                    <Typography>{`Prev ${getTimePeriod()}`}</Typography>
                  </Box>

                  <Box className="flex arrow" onClick={() => handleNext()}>
                    <Typography>{`Next ${getTimePeriod()}`}</Typography>
                    <ArrowForward />
                  </Box>
                </Box>
              ) : (
                <></>
              )}
            </Box>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default UserDashboard;
