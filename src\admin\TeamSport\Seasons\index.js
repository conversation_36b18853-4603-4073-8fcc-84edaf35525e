import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import ActionMessage from "../../../library/common/components/ActionMessage";
import { ActionMessage, Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Select from "react-select";
import _ from "lodash";
import Pagination from "@mui/material/Pagination";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import "./seasons.scss";
import CreateSeasonsVariation from "./createSeasonsVariation/CreateSeasonsVariation";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";
import { URLS } from "../../../library/common/constants";
import { MdKeyboardBackspace } from "react-icons/md";
import moment from "moment-timezone";
import { parseISO } from "date-fns";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class Seasons extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      TournamentValues: {
        TournamentName: "",
        salaryCap: "",
        playerBaseSalary: "",
        rapidSeasonId: "",
        RLCategoryId: "",
        CricketTournamentId: "",
        ARCategoryId: "",
        id: "",
        startTime: null,
        endTime: null,
        year: null,
      },
      selectCategory: "",
      CategoryPage: 0,
      categoryData: [],
      externalCategoryData: [],
      categoryCount: 0,
      TournamentCount: 0,
      TournamentList: [],
      SelectedTournamentList: [],
      errorRequire: "",
      errorSalaryCap: "",
      errorPlayerBaseSalary: "",
      searchCategory: [],
      searchCategoryCount: 0,
      searchCategoryPage: 0,
      isCategorySearch: "",
      search: "",
      isVariationModalOpen: false,
      errorYear: "",
      errorStartDate: "",
      errorEndDate: "",
      sortStartDate: true,
      sortEndDate: true,
      sortDate: null,
      filterEndDate: null,
      startDateOpen: false,
      endDateOpen: false,
    };
  }

  componentDidMount() {
    this.fetchAllSeasons(0, "");
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      this.fetchAllSeasons(this.state.offset, this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllSeasons(0, "");
      this.setState({
        offset: 0,
        selectCategory: "",
        currentPage: 1,
        externalCategoryData: [],
        CategoryPage: 0,
        search: "",
      });
    }
  }
  async fetchAllSeasons(page, searchvalue) {
    let { rowPerPage, offset } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = `allsport/season?SportId=${this.props.match.params.sportId}&tournamentId=${this.props.match.params.tournamentId}&limit=${rowPerPage}&offset=${page}&search=${searchvalue}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          TournamentList: data?.result?.rows,
          isLoading: false,
          TournamentCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  fetchModalSelectedCategory = (CategoryId, CategoryName) => {
    let seletedCategory = [
      {
        label: CategoryName,
        value: CategoryId,
      },
    ];

    this.setState({
      categoryData: CategoryId ? seletedCategory : this.state.categoryData,
    });
  };

  handalValidate = () => {
    let { TournamentValues } = this.state;

    let flag = true;
    if (TournamentValues?.TournamentName === "") {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    if (TournamentValues?.salaryCap === "") {
      flag = false;
      this.setState({
        errorSalaryCap: "This field is mandatory",
      });
    } else {
      this.setState({
        errorSalaryCap: "",
      });
    }
    if (TournamentValues?.playerBaseSalary === "") {
      flag = false;
      this.setState({
        errorPlayerBaseSalary: "This field is mandatory",
      });
    } else {
      this.setState({
        errorPlayerBaseSalary: "",
      });
    }
    if (TournamentValues?.year === null) {
      flag = false;
      this.setState({
        errorYear: "This field is mandatory",
      });
    } else {
      this.setState({
        errorYear: "",
      });
    }
    if (TournamentValues?.startTime === null) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    if (TournamentValues?.endTime === null) {
      flag = false;
      this.setState({
        errorEndDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorEndDate: "",
      });
    }

    return flag;
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      try {
        const sportId = this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("soccer")
          ? 8
          : 9;

        let payload = {
          name: this.state?.TournamentValues?.TournamentName,
          fantasy_sport_salary_cap: this.state?.TournamentValues?.salaryCap,
          player_base_salary: this.state?.TournamentValues?.playerBaseSalary,
          SportId: sportId,
          // rapidSeasonId: Number(this.state?.TournamentValues?.rapidSeasonId),
          year: moment(this.state?.TournamentValues?.year)
            .tz(timezone)
            .format("YYYY"),
          startDate: moment(this.state?.TournamentValues?.startTime)
            .tz(timezone)
            .format("YYYY-MM-DD hh:mm:ss"),
          endDate: moment(this.state?.TournamentValues?.endTime)
            .tz(timezone)
            .format("YYYY-MM-DD hh:mm:ss"),
        };

        if (this.props.match.path?.includes("cricket")) {
          payload = {
            ...payload,
            CricketTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        } else if (this.props.match.path?.includes("australianrules")) {
          payload = {
            ...payload,
            ARTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        } else if (this.props.match.path?.includes("soccer")) {
          payload = {
            ...payload,
            SoccerTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        } else {
          payload = {
            ...payload,
            RLTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        }

        const { status, data } = await axiosInstance.post(
          `/allsport/season?SportId=${sportId}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllSeasons(this.state.offset, this.state?.search);
          this.setActionMessage(true, "Success", `Season Created Successfully`);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (error) {
        this.setActionMessage(true, "Error", error?.response?.data?.message);
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };
  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      try {
        const sportId = this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("soccer")
          ? 8
          : 9;

        let payload = {
          name: this.state?.TournamentValues?.TournamentName,
          fantasy_sport_salary_cap: this.state?.TournamentValues?.salaryCap,
          player_base_salary: this.state?.TournamentValues?.playerBaseSalary,
          SportId: sportId,
          // rapidSeasonId: Number(this.state?.TournamentValues?.rapidSeasonId),
          year: moment(this.state?.TournamentValues?.year)
            .tz(timezone)
            .format("YYYY"),

          startDate: moment(this.state?.TournamentValues?.startTime)
            .tz(timezone)
            .format("YYYY-MM-DD hh:mm:ss"),
          endDate: moment(this.state?.TournamentValues?.endTime)
            .tz(timezone)
            .format("YYYY-MM-DD hh:mm:ss"),
        };

        if (this.props.match.path?.includes("cricket")) {
          payload = {
            ...payload,
            CricketTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        } else if (this.props.match.path?.includes("australianrules")) {
          payload = {
            ...payload,
            ARTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        } else if (this.props.match.path?.includes("soccer")) {
          payload = {
            ...payload,
            SoccerTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        } else {
          payload = {
            ...payload,
            RLTournamentId: this.props.match.params.tournamentId
              ? Number(this.props.match.params.tournamentId)
              : null,
          };
        }

        const { status, data } = await axiosInstance.put(
          `/allsport/season/${this.state.TournamentValues?.id}?SportId=${sportId}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllSeasons(this.state.offset, this.state?.search);
          this.setActionMessage(true, "Success", `Season Edited Successfully`);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };
  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
      errorSalaryCap: "",
      errorPlayerBaseSalary: "",
      errorYear: "",
      errorStartDate: "",
      errorEndDate: "",
    });
  };

  inputModal = (item, type) => () => {
    const { TournamentValues } = this.state;
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        TournamentValues: {
          ...TournamentValues,
          TournamentName: item?.name,
          salaryCap: item?.fantasy_sport_salary_cap,
          playerBaseSalary: item?.player_base_salary,
          // rapidSeasonId: item?.rapidSeasonId,
          id: item?.id,
          year: moment(item?.year).tz(timezone).format("YYYY-MM-DD"),
          startTime: moment(item?.startDate).tz(timezone).format("YYYY-MM-DD"),
          endTime: moment(item?.endDate).tz(timezone).format("YYYY-MM-DD"),
        },
        isEditMode: true,
      });
    } else {
      this.setState({
        TournamentValues: {
          TournamentName: "",
          salaryCap: "",
          playerBaseSalary: "",
          rapidTournamentId: "",
          id: "",
          SportKey: "",
          startTime: moment().tz(timezone).format("YYYY-MM-DD"),
          endTime: moment().tz(timezone).format("YYYY-MM-DD"),
          year: moment().tz(timezone).format("YYYY"),
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    this.setState({ isLoading: true });
    try {
      const sportId = this.props.match.path?.includes("cricket")
        ? 4
        : this.props.match.path?.includes("rugbyleague")
        ? 12
        : this.props.match.path?.includes("soccer")
        ? 8
        : 9;
      const { status, data } = await axiosInstance.delete(
        `/allsport/season/${this.state.itemToDelete}?SportId=${sportId}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllSeasons(this.state.offset, this.state?.search);
        });
        this.setActionMessage(true, "Success", "Season Deleted Successfully!");
      } else {
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ isLoading: false });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({ isLoading: false });
    }
  };
  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  inputVariationModal = (item) => {
    this.setState({
      isVariationModalOpen: true,
      TournamentValues: {
        TournamentName: item?.name,
        id: item?.id,
      },
    });
  };
  toggleVariationModal = () => {
    this.setState({
      isVariationModalOpen: false,
      TournamentValues: {
        TournamentName: "",
        id: "",
      },
    });
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };

  handleClearClick = () => {
    this.fetchAllSeasons(0, "");
    this.setState({ search: "" });
  };

  backToNavigatePage = () => {
    const page = this.props.match.path?.includes("cricket")
      ? "cricket"
      : this.props.match.path?.includes("rugbyleague")
      ? "rugbyleague"
      : this.props.match.path?.includes("soccer")
      ? "soccer"
      : "australianrules";
    this.props.navigate(`/${page}/tournaments`);
  };

  bulkConfigurationEvent = async (item) => {
    const tournamentId = this.props.match.path?.includes("cricket")
      ? item?.CricketTournamentId
      : this.props.match.path?.includes("rugbyleague")
      ? item?.RLTournamentId
      : this.props.match.path?.includes("soccer")
      ? item?.SoccerTournamentId
      : item?.ARTournamentId;
    const sportId = item?.SportId;
    const seasonId = item?.id;

    try {
      let payload = {
        sportId: sportId,
        tournamentId: tournamentId,
        seasonId: seasonId,
      };

      const { status, data } = await axiosInstance.post(
        `/allsport/events/fantasy/bulk-generate`,
        payload
      );
      if (status === 200) {
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (error) {
      this.setActionMessage(true, "Error", error?.response?.data?.message);
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      TournamentValues,
      selectCategory,
      TournamentList,
      TournamentCount,
      errorRequire,
      errorSalaryCap,
      search,
      isVariationModalOpen,
      errorYear,
      errorStartDate,
      errorEndDate,
      errorPlayerBaseSalary,
    } = this.state;
    const pageNumbers = [];

    if (TournamentCount > 0) {
      for (let i = 1; i <= Math.ceil(TournamentCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    let sportId = this.props.match.path?.includes("cricket")
      ? 4
      : this.props.match.path?.includes("rugbyleague")
      ? 12
      : this.props.match.path?.includes("soccer")
      ? 8
      : 9;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  {this.props.match.path?.includes("cricket")
                    ? "Cricket"
                    : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("soccer")
                    ? "Soccer"
                    : "Australian Rules"}
                </Link>
                <Typography className="active_p">Seasons</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Box className="back-btn">
                  <Button
                    className="admin-btn-margin admin-btn-back"
                    onClick={this.backToNavigatePage}
                  >
                    <MdKeyboardBackspace />
                  </Button>
                  <Typography variant="h1" align="left">
                    Seasons
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllSeasons(0, search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && TournamentList?.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && TournamentList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable"
                    aria-label="simple table"
                    style={{ minWidth: "max-content" }}
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        {/* <TableCell style={{ width: "200px" }}>
                          Rapid Season Id
                        </TableCell> */}
                        <TableCell
                        // style={{ width: "15%" }}
                        >
                          Season Name
                        </TableCell>
                        <TableCell>Salary Cap</TableCell>
                        <TableCell>Player Base Salary</TableCell>
                        <TableCell
                        // style={{ width: "17%" }}
                        >
                          variation
                        </TableCell>
                        {/* <TableCell style={{ width: "25%" }}>
                        variation
                      </TableCell> */}
                        {/* <TableCell style={{ width: "25%" }}>variation</TableCell> */}

                        <TableCell>Year</TableCell>
                        <TableCell>Start Date</TableCell>
                        <TableCell>End Date</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {TournamentList?.length > 0 ? (
                        TournamentList?.map((item) => {
                          return (
                            <TableRow
                              className="table-rows listTable-Row"
                              key={item?.id}
                            >
                              <TableCell> {item?.id} </TableCell>
                              {/* <TableCell> {item?.rapidSeasonId}</TableCell> */}
                              <TableCell>{item?.name}</TableCell>
                              <TableCell>
                                {item?.fantasy_sport_salary_cap}
                              </TableCell>
                              <TableCell>{item?.player_base_salary}</TableCell>
                              <TableCell>
                                <Button
                                  variant="contained"
                                  style={{
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    borderRadius: "8px",
                                    textTransform: "capitalize",
                                    // padding: "13px 24px 12px",
                                    marginLeft: "15px",
                                  }}
                                  onClick={() => {
                                    this.inputVariationModal(item);
                                  }}
                                >
                                  Add/Edit variation
                                </Button>
                              </TableCell>
                              <TableCell>{item?.year}</TableCell>
                              <TableCell>
                                {item?.startDate &&
                                  moment(item?.startDate)
                                    .tz(timezone)
                                    .format("DD-MM-YYYY")}
                              </TableCell>
                              <TableCell>
                                {item?.endDate &&
                                  moment(item?.endDate)
                                    .tz(timezone)
                                    .format("DD-MM-YYYY")}
                              </TableCell>
                              <TableCell>
                                {(this.props.match.path?.includes("cricket") ||
                                  this.props.match.path?.includes(
                                    "rugbyleague"
                                  ) ||
                                  this.props.match.path?.includes(
                                    "australianrules"
                                  ) ||
                                  this.props.match.path?.includes(
                                    "soccer"
                                  )) && (
                                  <Button
                                    onClick={() =>
                                      this.bulkConfigurationEvent(item)
                                    }
                                    style={{
                                      cursor: "pointer",
                                      minWidth: "0px",
                                    }}
                                    className="table-btn info-btn"
                                  >
                                    Bulk Event Configurations
                                  </Button>
                                )}
                                <Button
                                  onClick={this.inputModal(item, "edit")}
                                  style={{ cursor: "pointer" }}
                                  className="table-btn edit-btn"
                                >
                                  Edit
                                </Button>
                                <Button
                                  onClick={this.setItemToDelete(item?.id)}
                                  style={{ cursor: "pointer" }}
                                  className="table-btn delete-btn"
                                >
                                  Delete
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      {!selectCategory ? (
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={
                                  TournamentCount / rowPerPage > 1
                                    ? false
                                    : true
                                }
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        <></>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}
            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />
            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Season" : "Edit Season"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    {/* {messageBox.display && (
                    <ActionMessage
                      message={messageBox.message}
                      type={messageBox.type}
                      styleClass={messageBox.styleClass}
                    />
                  )} */}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          Season Name <span className="color-red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Season Name"
                          value={TournamentValues?.TournamentName}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                TournamentName: e.target.value,
                              },
                            })
                          }
                        />
                        {errorRequire ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorRequire}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>

                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          Fantasy Sport Salary Cap{" "}
                          <span className="color-red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          type="number"
                          placeholder="Fantasy Sport Salary Cap"
                          value={TournamentValues?.salaryCap}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                salaryCap: e.target.value,
                              },
                              errorSalaryCap: e?.target?.value
                                ? ""
                                : errorSalaryCap,
                            })
                          }
                        />
                        {errorSalaryCap ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorSalaryCap}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          Player Base Salary{" "}
                          <span className="color-red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          type="number"
                          placeholder="Player Base Salary"
                          value={TournamentValues?.playerBaseSalary}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                playerBaseSalary: e.target.value,
                              },
                              errorPlayerBaseSalary: e?.target?.value
                                ? ""
                                : errorPlayerBaseSalary,
                            })
                          }
                        />
                        {errorPlayerBaseSalary ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorPlayerBaseSalary}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* <Grid
                        item
                        xs={6}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">rapid Season Id</label>
                        <TextField
                          className="teamsport-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          type="number"
                          placeholder="rapid Season Id"
                          value={TournamentValues?.rapidSeasonId}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                rapidSeasonId: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid> */}
                      <Grid
                        item
                        xs={12}
                        className="teamsport-textfield date-time-picker-wrap teamsport-text"
                        style={{ display: "flex", flexDirection: "column" }}
                      >
                        <label className="modal-label">
                          {" "}
                          Year <span className="color-red">*</span>
                        </label>
                        {/* <MuiPickersUtilsProvider  utils={DateFnsUtils}>
                        <KeyboardDatePicker
                          variant="inline"
                          inputVariant="outlined"
                          ampm={false}
                          value={moment(TournamentValues?.year)
                            .tz(timezone)
                            .format("YYYY-MM-DD")}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                year: e,
                              },
                            })
                          }
                          views={["year"]}
                          placeholder="Year"
                          autoOk={true}
                          format="yyyy"
                          className="date-time-picker"
                        />
                      </MuiPickersUtilsProvider> */}
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            // disableToolbar
                            variant="inline"
                            format="yyyy"
                            placeholder="Year"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            views={["year"]}
                            value={
                              TournamentValues?.year
                                ? typeof TournamentValues?.year === "string"
                                  ? parseISO(
                                      moment(TournamentValues?.year)
                                        ?.tz(timezone)
                                        ?.format("YYYY-MM-DD")
                                    )
                                  : TournamentValues?.year
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                TournamentValues: {
                                  ...TournamentValues,
                                  year: e,
                                },
                              })
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal"
                          />
                        </LocalizationProvider>
                        {errorYear ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorYear}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={6}
                        className="teamsport-textfield date-time-picker-wrap teamsport-text"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label">
                          {" "}
                          Start Date <span className="color-red">*</span>
                        </label>
                        {/* <MuiPickersUtilsProvider utils={DateFnsUtils}>
                        <KeyboardDatePicker
                          variant="inline"
                          inputVariant="outlined"
                          ampm={false}
                          value={moment(TournamentValues?.startTime)
                            .tz(timezone)
                            .format("YYYY-MM-DD")}
                          onChange={(e) =>
                            this.setState({
                              TournamentValues: {
                                ...TournamentValues,
                                startTime: e,
                              },
                            })
                          }
                          autoOk={true}
                          format="yyyy/MM/dd"
                          className="date-time-picker"
                        />
                      </MuiPickersUtilsProvider> */}
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            // disableToolbar
                            variant="inline"
                            format="yyyy/MM/dd"
                            placeholder="Start Date"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              TournamentValues?.startTime
                                ? typeof TournamentValues?.startTime ===
                                  "string"
                                  ? parseISO(
                                      moment(TournamentValues?.startTime)
                                        ?.tz(timezone)
                                        ?.format("YYYY-MM-DD")
                                    )
                                  : TournamentValues?.startTime
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                TournamentValues: {
                                  ...TournamentValues,
                                  startTime: e,
                                },
                              })
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal"
                          />
                        </LocalizationProvider>
                        {errorStartDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorStartDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    ></Grid> */}
                      <Grid
                        item
                        xs={6}
                        className="date-time-picker-wrap teamsport-text teamsport-textfield"
                        style={{
                          marginTop: "15px",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <label className="modal-label">
                          {" "}
                          End Date <span className="color-red">*</span>
                        </label>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DesktopDatePicker
                            autoOk
                            // disableToolbar
                            variant="inline"
                            format="yyyy/MM/dd"
                            placeholder="Start Date"
                            margin="normal"
                            id="date-picker-inline"
                            inputVariant="outlined"
                            value={
                              TournamentValues?.endTime
                                ? typeof TournamentValues?.endTime === "string"
                                  ? parseISO(
                                      moment(TournamentValues?.endTime)
                                        ?.tz(timezone)
                                        ?.format("YYYY-MM-DD")
                                    )
                                  : TournamentValues?.endTime
                                : null
                            }
                            onChange={(e) =>
                              this.setState({
                                TournamentValues: {
                                  ...TournamentValues,
                                  endTime: e,
                                },
                              })
                            }
                            KeyboardButtonProps={{
                              "aria-label": "change date",
                            }}
                            className="date-picker-sponsored date-picker-fixture-modal"
                          />
                        </LocalizationProvider>
                        {errorEndDate ? (
                          <p
                            className="errorText"
                            style={{ margin: "4px 0 0 0" }}
                          >
                            {errorEndDate}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isVariationModalOpen}
              onClose={this.toggleVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Variation Details
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleVariationModal}
                />
                <CreateSeasonsVariation
                  inputModal={this.toggleVariationModal}
                  TournamentValues={TournamentValues}
                  pathName={this.props?.match?.path}
                  sportId={sportId}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Seasons;
