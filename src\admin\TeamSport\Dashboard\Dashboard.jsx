import React, { useState } from "react";
import {
  Box,
  Tabs,
  Tab,
  Grid,
  Checkbox,
  Breadcrumbs,
  Typography,
  Modal,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  Button,
} from "@mui/material";
import { <PERSON>, useParams, useNavigate } from "react-router-dom";
import moment from "moment";
import axiosInstance from "../../../helpers/Axios";
import ActionMessage from "../../../library/common/components/ActionMessage";
import ButtonComponent from "../../../library/common/components/Button";
import CancelIcon from "@mui/icons-material/Cancel";
import { Loader } from "../../../library/common/components";
import "./Dashboard.scss";
import DashboardCollapase from "./DashboardCollapase";
import DateFnsUtils from "@date-io/date-fns";
import Select from "react-select";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
const day = [
  {
    id: 0,
    name: "Yesterday",
    date: moment().utc().subtract(1, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().subtract(1, "days").format("dddd"),
  },
  {
    id: 1,
    name: "Today",
    date: moment().utc().format("YYYY-MM-DD"),
    dayName: moment().utc().format("dddd"),
  },
  {
    id: 2,
    name: "Tomorrow",
    date: moment().utc().add(1, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(1, "days").format("dddd"),
  },
  {
    id: 3,
    name: "Wednesday",
    date: moment().utc().add(2, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(2, "days").format("dddd"),
  },
  {
    id: 4,
    name: "Thursday",
    date: moment().utc().add(3, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(3, "days").format("dddd"),
  },
  {
    id: 5,
    name: "Friday",
    date: moment().utc().add(4, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(4, "days").format("dddd"),
  },
  {
    id: 6,
    name: "Saturday",
    date: moment().utc().add(5, "days").format("YYYY-MM-DD"),
    dayName: moment().utc().add(5, "days").format("dddd"),
  },
  {
    id: 7,
    name: "Futures",
    // dayName: "Futures",
    date: moment().utc().add(1, "days").format("YYYY-MM-DD"),
  },
  {
    id: 8,
    name: "Archive",
    // dayName: "Archive",
    date: moment().utc().subtract(1, "days").format("YYYY-MM-DD"),
  },
];

const Dashboard = () => {
  const TeamSportType = window?.location?.pathname;
  // const params = useParams()
  const [value, setValue] = useState(1);
  const [apimessage, setApiMessage] = useState(1);
  const [providersDetails, setProvidersDetails] = useState([]);
  const [selectedDate, setselectedDate] = useState(
    moment().format("YYYY-MM-DD")
  );
  const [isRefetch, setIsRefetch] = useState(false);
  const [isLoading, setisLoading] = useState(false);
  const [bookmakerModalOpen, setBookmakerModalOpen] = useState(false);
  const [seletedProvider, setseletedProvider] = useState("");

  const [sportId, setsportId] = useState("");

  const handleDateChange = (date) => {
    setselectedDate(moment(date).format("YYYY-MM-DD"));
  };
  // Change Day Tab
  const handleChange = (event, value) => {
    setValue(value);
    let SelectDate = day.filter((item) => {
      return item.id === value;
    });
    setselectedDate(
      SelectDate?.map((item) => {
        return item?.date;
      })?.[0]
    );
  };

  const setActionMessage = (display = false, type = "", message = "") => {
    let setMessage = {
      display: display,
      type: type,
      message: message,
    };
    setApiMessage(setMessage);
    setTimeout(() => {
      let setBlankMessage = {
        display: false,
        type: "",
        message: "",
      };
      setApiMessage(setBlankMessage);
    }, 3000);
  };

  const reFetch = async () => {
    setisLoading(true);
    let params = {};
    params = {
      ProviderId: seletedProvider,
      sportId: TeamSportType?.includes("cricket")
        ? 4
        : TeamSportType?.includes("rugbyleague")
          ? 12
          : TeamSportType?.includes("rugbyunion")
            ? 13
            : TeamSportType?.includes("basketball")
              ? 10
              : TeamSportType?.includes("afl")
                ? 15
                : TeamSportType?.includes("australianrules")
                  ? 9
                  : TeamSportType?.includes("golf")
                    ? 16
                    : TeamSportType?.includes("tennis")
                      ? 7
                      : TeamSportType?.includes("baseball")
                        ? 11
                        : TeamSportType?.includes("icehockey")
                          ? 17
                          : TeamSportType?.includes("boxing")
                            ? 6
                            : TeamSportType?.includes("mma")
                              ? 5
                              : TeamSportType?.includes("soccer")
                                ? 8
                                : 14,
    };

    try {
      let passApi = TeamSportType?.includes("cricket")
        ? `crickets/sync/fixture`
        : TeamSportType?.includes("basketball")
          ? `nba/sync/fixture`
          : TeamSportType?.includes("afl")
            ? `afl/sync/fixture`
            : TeamSportType?.includes("australianrules")
              ? `ar/sync/fixture`
              : TeamSportType?.includes("golf")
                ? `golf/sync/fixture`
                : TeamSportType?.includes("tennis")
                  ? `tennis/sync/fixture`
                  : TeamSportType?.includes("baseball")
                    ? `baseball/sync/fixture`
                    : TeamSportType?.includes("icehockey")
                      ? `icehockey/sync/fixture`
                      : TeamSportType?.includes("boxing")
                        ? `boxing/sync/fixture`
                        : TeamSportType?.includes("mma")
                          ? `mma/sync/fixture`
                          : TeamSportType?.includes("soccer")
                            ? `soccer/sync/fixture`
                            : `rls/sync/fixture`;
      const { status } = await axiosInstance.post(passApi, params);
      if (status === 200) {
        setActionMessage(true, "Success", "sync process start");
        setisLoading(false);
        setIsRefetch(true);
      } else {
        setisLoading(false);
      }
    } catch (err) {
      setisLoading(false);
    }
  };
  // const handleSave = async () => {
  //   setisLoading(true);
  //   let params = {
  //     providerId: "6",
  //     sportId: sportId,
  //   };

  //   try {
  //     const { status } = await axiosInstance.post(`sync/fixture`, params);
  //     if (status === 200) {
  //       setActionMessage(true, "Success", "sync process start");
  //       setisLoading(false);
  //       setIsRefetch(true);
  //     }
  //   } catch (err) { }
  // };
  const bookMakerModel = () => {
    setBookmakerModalOpen(!bookmakerModalOpen);
    fetchProviders();
  };
  const fetchProviders = async () => {
    setisLoading(true);
    try {
      let SportId = TeamSportType?.includes("cricket")
        ? 4
        : TeamSportType?.includes("rugbyleague")
          ? 12
          : TeamSportType?.includes("rugbyunion")
            ? 13
            : TeamSportType?.includes("basketball")
              ? 10
              : TeamSportType?.includes("afl")
                ? 15
                : TeamSportType?.includes("australianrules")
                  ? 9
                  : TeamSportType?.includes("golf")
                    ? 16
                    : TeamSportType?.includes("tennis")
                      ? 7
                      : TeamSportType?.includes("baseball")
                        ? 11
                        : TeamSportType?.includes("icehockey")
                          ? 17
                          : TeamSportType?.includes("boxing")
                            ? 6
                            : TeamSportType?.includes("mma")
                              ? 5
                              : TeamSportType?.includes("soccer")
                                ? 8
                                : 14;
      const { status, data } = await axiosInstance.get(
        `apiProviders/apiprovidersports?SportId=${SportId}`
      );
      if (status === 200) {

        let FilteredData = data?.result?.filter((item) => { return (!(item?.ApiProviderId === 16 || item?.ApiProviderId === 26)) })
        setProvidersDetails(FilteredData);
        setisLoading(false);
      }
    } catch (err) {
      setisLoading(false);
    }
  };

  const togglebookmakerModal = () => {
    setBookmakerModalOpen(!bookmakerModalOpen);
    setseletedProvider("");
  };
  const handleChangeProvider = (e) => {
    if (e.target.checked) {
      setseletedProvider(e.target.value);
    } else {
      setseletedProvider("");
    }
  };
  const Today = moment().utc().format("YYYY-MM-DD");
  const Tommorow = moment().utc().add(1, "days").format("YYYY-MM-DD");
  const yesterDay = moment().utc().subtract(1, "days").format("YYYY-MM-DD");
  return (
    <>
      <Grid container className="page-content adminLogin">
        <Grid item xs={12}>
          <Box className="pageWrapper">
            {apimessage.display && (
              <ActionMessage
                message={apimessage.message}
                type={apimessage.type}
                styleClass={apimessage.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  {TeamSportType?.includes("cricket")
                    ? "Cricket"
                    : TeamSportType?.includes("rugbyleague")
                      ? "RugbyLeague"
                      : TeamSportType?.includes("rugbyunion")
                        ? "RugbyUnion"
                        : TeamSportType?.includes("basketball")
                          ? "BasketBall"
                          : TeamSportType?.includes("afl")
                            ? "American Football"
                            : TeamSportType?.includes("australianrules")
                              ? "Australian Rules"
                              : TeamSportType?.includes("golf")
                                ? "Golf"
                                : TeamSportType?.includes("tennis")
                                  ? "Tennis"
                                  : TeamSportType?.includes("baseball")
                                    ? "Baseball"
                                    : TeamSportType?.includes("icehockey")
                                      ? "Ice Hockey"
                                      : TeamSportType?.includes("boxing")
                                        ? "Boxing"
                                        : TeamSportType?.includes("mma")
                                          ? "Mixed Martial Arts"
                                          : TeamSportType?.includes("soccer")
                                            ? "Soccer"
                                            : "RugbyUnionSevens"}
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="space-around">
              <Grid item xs={10}>
                <h1 className="text-left">
                  {" "}
                  {TeamSportType?.includes("cricket")
                    ? "Cricket"
                    : TeamSportType?.includes("rugbyleague")
                      ? "Rugby League"
                      : TeamSportType?.includes("rugbyunion")
                        ? "Rugby Union"
                        : TeamSportType?.includes("basketball")
                          ? "Basketball"
                          : TeamSportType?.includes("afl")
                            ? "American Football"
                            : TeamSportType?.includes("australianrules")
                              ? "Australian Rules"
                              : TeamSportType?.includes("golf")
                                ? "Golf"
                                : TeamSportType?.includes("tennis")
                                  ? "Tennis"
                                  : TeamSportType?.includes("baseball")
                                    ? "Baseball"
                                    : TeamSportType?.includes("icehockey")
                                      ? "Ice Hockey"
                                      : TeamSportType?.includes("boxing")
                                        ? "Boxing"
                                        : TeamSportType?.includes("mma")
                                          ? "Mixed Martial Arts"
                                          : TeamSportType?.includes("soccer")
                                            ? "Soccer"
                                            : "Rugby Union Sevens"}{" "}
                </h1>
              </Grid>
              <Grid item xs={2}>
                <ButtonComponent
                  className="addButton admin-btn-green btn"
                  onClick={() => {
                    bookMakerModel();
                  }}
                  color="primary"
                  value="Refetch"
                />
              </Grid>
            </Grid>

            <Box className="sport-tab">
              <Tabs
                value={value}
                indicatorColor="primary"
                textColor="primary"
                className="racing-tab-detail"
                disableRipple
                disableFocusRipple
              >
                {day?.map((item, index) => {
                  return (
                    <Box>
                      <Tab
                        disableRipple
                        disableFocusRipple
                        //   label={item?.dayName}
                        label={
                          item?.date == Today
                            ? "Today"
                            : item?.name == "Archive"
                              ? "Archive"
                              : item?.name == "Futures"
                                ? "Futures"
                                : item?.date == Tommorow
                                  ? "Tommorow"
                                  : item?.date == yesterDay
                                    ? "Yesterday"
                                    : item?.dayName
                        }
                        value={item?.id}
                        className={item?.id == value ? "active" : ""}
                        onChange={(event, newValue) =>
                          handleChange(event, item?.id)
                        }
                      />
                    </Box>
                  );
                })}
              </Tabs>
              <Box className="Filteritemlist-wrap">
                {value == 8 || value == 7 ? (
                  <Box className="Filteritemlist-datepicker">
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <Grid container style={{ justifyContent: "end" }}>
                        <DesktopDatePicker
                          autoOk
                          disableToolbar
                          variant="inline"
                          format="dd/MM/yyyy"
                          placeholder="DD/MM//YYYY"
                          margin="normal"
                          id="date-picker-inline"
                          inputVariant="outlined"
                          value={selectedDate ? parseISO(selectedDate) : null}
                          onChange={(e) => handleDateChange(e)}
                          KeyboardButtonProps={{
                            "aria-label": "change date",
                          }}
                          disableFuture={value == 8}
                          disablePast={value == 7}
                          // style={{ marginRight: 5 }}
                          className="details-search-picker"
                        />
                      </Grid>
                    </LocalizationProvider>
                  </Box>
                ) : (
                  ""
                )}
              </Box>
            </Box>
            <DashboardCollapase
              selectedDate={selectedDate}
              isRefetch={isRefetch}
            />
            <Box className="fixture-info">
              <ul>
                <li>
                  <span className="sqare fixture"></span> Found Fixture
                </li>
                <li>
                  <span className="sqare notfixture"> </span> Can't Find Fixture
                </li>
                <li>
                  <span className="sqare ignore"> </span> Marked by User as
                  Ignore
                </li>
              </ul>
            </Box>
          </Box>
        </Grid>
      </Grid>
      <Modal
        className="modal modal-input"
        open={bookmakerModalOpen}
        onClose={togglebookmakerModal}
      >
        <Box
          className={"paper modal-show-scroll"}
          style={{ position: "relative" }}
        >
          <Grid container>
            <Grid item xs={12}>
              <h3 className="text-center">Providers</h3>
            </Grid>
          </Grid>
          <Grid
            item
            xs={12}
            style={{ display: "flex", justifyContent: "flex-end" }}
          >
            <Button
              variant="contained"
              disabled={seletedProvider == "" ? true : false}
              className="fetch-btn"
              onClick={() => {
                reFetch();
                togglebookmakerModal();
              }}
            >
              Refetch
            </Button>
          </Grid>
          <CancelIcon
            className="admin-close-icon"
            onClick={togglebookmakerModal}
          />

          {isLoading && (
            <Box style={{ display: "flex", justifyContent: "center" }}>
              <Loader />
            </Box>
          )}
          {!isLoading && providersDetails.length === 0 && (
            <p>No Data Available</p>
          )}
          {!isLoading && providersDetails.length > 0 && (
            <>
              <TableContainer component={Paper}>
                <Table
                  className="listTable api-provider-listTable"
                  aria-label="simple table"
                >
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell></TableCell>
                      <TableCell>Provider Id</TableCell>
                      <TableCell>API Provider</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {providersDetails?.map((item, i) => (
                      <TableRow key={i}>
                        <TableCell style={{ width: "20px" }}>
                          <Checkbox
                            className="mz-checkbox"
                            checked={
                              item?.ApiProviderId == Number(seletedProvider)
                            }
                            value={item?.ApiProviderId}
                            onChange={(e) => handleChangeProvider(e)}
                          />
                        </TableCell>
                        <TableCell>{item.ApiProviderId}</TableCell>
                        <TableCell>{item.ApiProvider?.providerName}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}
        </Box>
      </Modal>
    </>
  );
};

export default Dashboard;
