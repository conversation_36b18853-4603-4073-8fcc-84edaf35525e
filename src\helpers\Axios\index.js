import Axios from "axios";
import { errorHandler, fetchFromStorage } from "../../library/utilities";
import { identifiers } from "../../library/common/constants";
import { config } from "../config";
import { EventEmitter } from "../../services/event";

const axiosInstance = Axios.create({
  baseURL: config.baseUrl,
  headers: { "Content-Type": "application/json" },
});

const customAxios = Axios.create({
  baseURL: config.baseUrl,
  headers: { "Content-Type": "application/json" },
});

const isMaintenancePage = (data) => {
  EventEmitter.dispatch("onmaintenance", data);
};

axiosInstance.interceptors.request.use((config) => {
  const token = fetchFromStorage(identifiers.token);
  const clonedConfig = config;

  if (token) {
    clonedConfig.headers = {
      Authorization: `Bearer ${token.access_token}`,
    };
  }

  return clonedConfig;
});

axiosInstance.interceptors.response.use(
  (config) => {
    updateHeaderResponse(config);
    return config;
  },
  (error) => {
    errorHandler(error);
    updateHeaderResponse(error?.response);
    return Promise.reject(error);
  }
);
let maintenanceModeEmitted = false;
const updateHeaderResponse = (response) => {
  const maintenance = response?.headers?.["x-maintenance"];
  if (!maintenanceModeEmitted) {
    isMaintenancePage(maintenance);
    maintenanceModeEmitted = true;
  }
};
export default axiosInstance;
