import React, { createRef } from "react";
import { Grid } from "@mui/material";
import { countryFormModel } from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
import { setValidation } from "../../../helpers/common";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";

let countryFormModelArray = countryFormModel;

class CreateCountry extends React.Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      values: {
        country: "",
        variation: null,
        updateRequired: "0",
        countryCode: "",
        phoneCode: "",
      },
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
      flag: [],
    };
  }

  componentDidMount() {
    if (this.props.isEditMode) {
      this.fetchCurrentCountry(this.props.id);
    }
  }

  componentWillUnmount() {
    countryFormModelArray = countryFormModelArray.map((fieldItem) => {
      return { ...fieldItem, errorMessage: "" };
    });
  }

  fetchCurrentCountry = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.country + `/${id}`);
    if (status === 200) {
      this.setState((prevState) => ({
        values: {
          ...data.result,
          updateRequired: data.result.updateRequired === true ? "1" : "0",
        },
      }));
      // let variationArray = JSON.parse("[" + data.result.variation + "]");
      // if (variationArray.length > 0) {
      //   this.setState(() => {
      //     return {
      //       values: {
      //         ...this.state.values,
      //         ["variation"]: variationArray[0],
      //       },
      //     };
      //   });
      // }
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } });
  };

  validate = () => {
    let { country, updateRequired, countryCode } = this.state.values;
    let flag = true;

    if (
      country?.trim() === "" ||
      updateRequired === "" ||
      countryCode?.trim() === ""
    ) {
      flag = false;
      this.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      this.setActionMessage(false);
    }

    return flag;
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handleSave = async () => {
    const { flag } = this.state;
    const { isEditMode } = this.props;
    this.setState({ isLoading: true });
    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode
        ? `${URLS.country}/${this.props.id}`
        : URLS.country;

      const values = removeErrorFieldsFromValues(form.formData);

      countryFormModelArray = countryFormModelArray?.map((fieldItem) => {
        return setValidation(fieldItem, values);
      });

      if (this.validate()) {
        if (flag.length > 0) {
          let fileData = await this.setMedia(flag[0]);
          if (fileData) {
            values["country_flag"] = fileData?.image?.filePath;
          }
        }
        const { status } = await axiosInstance[method](url, values);
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          this.props.fetchAllCountry();
          this.setActionMessage(
            true,
            "Success",
            `Country ${isEditMode ? "Edited" : "Created"} Successfully`
          );
        }
      }
    } catch (err) {
      this.setState({ isLoading: false });
      this.setActionMessage(
        true,
        "Error",
        err.hasOwnProperty("response") && err.response.status === 500
          ? err?.response?.data?.message
          : `An error occurred while ${
              isEditMode ? "editing" : "creating"
            } Country`
      );
    }
  };

  handleChange = (field, value) => {
    let values = { ...this.state.values, [field]: value };
    this.setState({ values: values });
    countryFormModelArray = countryFormModelArray?.map((fieldItem) => {
      if (field === fieldItem?.field) {
        return setValidation(fieldItem, values);
      } else {
        return fieldItem;
      }
    });
    this.setActionMessage(false);
  };
  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };

  render() {
    var { values, messageBox, isLoading, flag } = this.state;
    var { isEditMode } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12}>
            {/* <Paper className="pageWrapper api-provider"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}

            <Form
              values={values}
              model={countryFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            />

            <div className="blog-file-upload">
              <h6>Flag</h6>
              <FileUploader
                onDrop={(flag) => this.handleFileUpload("flag", flag)}
              />
              <div className="logocontainer">
                {flag?.length > 0
                  ? flag.map((file, index) => (
                      <img
                        className="auto-width"
                        key={index}
                        src={file.preview}
                        alt="file"
                      />
                    ))
                  : values?.country_flag &&
                    values?.country_flag !== "" &&
                    (values?.country_flag?.includes("uploads") ? (
                      <img
                        className="auto-width"
                        src={config.mediaUrl + values?.country_flag}
                        alt="file"
                      />
                    ) : (
                      <img
                        className="auto-width"
                        src={values?.country_flag}
                        alt="file"
                      />
                    ))}
              </div>
            </div>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-purple"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-purple"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30 admin-btn-outlined"
                    variant="outlined"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateCountry;
