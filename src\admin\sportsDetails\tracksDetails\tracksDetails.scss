@import "../../../assets/scss/variables.scss";

.textfield-tracks {
  .MuiOutlinedInput-adornedStart {
    background-color: #ffffff;
  }

  .MuiOutlinedInput-inputMarginDense {
    padding-top: 14px;
    padding-bottom: 15px;
  }
  .MuiOutlinedInput-inputMultiline {
    padding: 10.5px;
  }
  .MuiOutlinedInput-root {
    background-color: #ffffff;
  }
}

.search-track {
  .MuiOutlinedInput-root {
    border-radius: 8px;
    background-color: #ffffff;
    min-height: 40px;
  }
}

.listTable {
  .MuiTableHead-root {
    box-shadow: 0px 3px 9px 0px #0000000d;

    .race-no {
      padding: 0px;
      min-width: 60px;
      text-align: center;
    }
  }

  .tableHead-row {
    border-bottom: 2px solid #4455c7;
  }

  .table_body {
    background: #ffffff;
    box-shadow: 0px 3px 9px 0px #0000000d;
  }

  .table-seprator {
    padding: 0px !important;
    height: 18px;
    background: #f7f7f7;
  }

  .MuiTableCell-head {
    font-size: 16px;
    font-family: "VeneerClean-Soft" !important;
    font-weight: 400;
  }

  .listTable-Row {
    .MuiTableCell-body.race-no {
      padding: 0px;
      text-align: center;
      max-width: 60px;

      .MuiChip-labelSmall {
        padding: 0px !important;
      }
    }

    .MuiTableCell-body {
      font-family: $regulerFont;
      font-size: 16px;
      padding: 18px 15px;
    }
    .banner-image-wrap {
      width: 100px;
      height: 70px;
      .featureImage {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  .listTable-btn {
    text-transform: capitalize;
    max-width: 30px !important;
  }
}

.pagination {
  text-align: center !important;

  .tablePagination {
    .Mui-selected {
      border: 1px solid #4455c7 !important;
      background-color: #ffffff !important;
    }

    .MuiPaginationItem-page {
      font-size: 16px;
      font-family: "Inter", sans-serif !important;
    }
  }
}

.input-field {
  .react-datepicker-wrapper {
    width: 95%;
  }
}

.track-date-picker {
  width: 95%;
  label {
    font-size: 16px;
    font-family: "Inter" !important;
    font-weight: 600;
  }

  .MuiFormControl-marginNormal {
    margin-top: 4px;

    .MuiOutlinedInput-root {
      border-radius: 8px;
      background-color: #ffffff;

      .MuiOutlinedInput-input {
        padding: 14.5px 0px 14.5px 12px;
      }
    }
  }
  .track-picker {
    .MuiInputBase-input {
      padding: 8.5px 14px;
      background-color: #ffffff;
      border-radius: 8px;
    }
  }
}

.select-box {
  .React {
    width: 95%;
    margin-top: 5px;
    font-size: 16px;
    border: 1px solid #d4d6d8;
    margin-bottom: 15px;
    border-radius: 8px;

    .select__control {
      min-height: 45px;
      border-radius: 8px;
    }
  }
}
.merge-select {
  .select__menu-list {
    text-transform: none !important;
  }
}
