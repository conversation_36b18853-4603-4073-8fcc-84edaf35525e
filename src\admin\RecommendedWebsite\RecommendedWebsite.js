import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
  TableSortLabel,
} from "@mui/material";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../../src/images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import Select from "react-select";
// import { URLS } from "../../library/common/constants";
// import ActionMessage from "../../library/common/components/ActionMessage";
import { ActionMessage, Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import { fetchFromStorage } from "../../library/utilities";
import { identifiers } from "../../library/common/constants";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";
import { Editor } from "react-draft-wysiwyg";
import {
  EditorState,
  convertToRaw,
  ContentState,
  AtomicBlockUtils,
  convertFromHTML,
} from "draft-js";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import _ from "lodash";
import FileUploader from "../../library/common/components/FileUploader";
import { config } from "../../helpers/config";
import { URLS } from "../../library/common/constants";

import "../TeamSport/teamsport.scss";
import "../News/news.scss";
import "./recommendedWebsite.scss";
import ImageUploader from "../News/ImageUploader";
import he from "he";
import { SortTwoTone } from "@mui/icons-material";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class RecommendedWebsite extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      articleValues: {
        articleTitle: "",
        articleSubTitle: "",
        url: "",
        body: "",
        id: "",
        mediaId: "",
      },
      errorTitle: "",
      errorInitialTitle: "",
      errorUrl: "",
      errorImage: "",
      errorDes: "",
      search: "",
      editorState: EditorState.createEmpty(),
      image: [],
      uploadImage: "",
      sortLabelid: false,
      draggedItem: null,
      isSortChange: false,
      content: "",
    };
  }

  componentDidMount() {
    this.fetchAllArticles("");
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state.offset) {
      // let sortData = this?.state?.sortLabelid;

      this.fetchAllArticles(this.state?.search);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllArticles("");

      this.setState({
        offset: 0,
        currentPage: 1,
        search: "",
        filterDate: null,
        selectCategory: "",
      });
    }
  }
  handleEditorChange = (editorState) => {
    const { articleValues } = this.state;
    this.setState({ editorState });
    let string = draftToHtml(convertToRaw(editorState.getCurrentContent()));
    this.setState({
      articleValues: {
        ...articleValues,
        body: string,
      },
    });
  };

  handleChange = (content) => {
    // this.setState({ content: content });
    const { articleValues } = this.state;
    // this.setState({ content });
    // let string = draftToHtml(convertToRaw(content.getCurrentContent()));
    this.setState({
      articleValues: {
        ...articleValues,
        body: content,
      },
    });
  };

  fetchAllArticles = async (searchvalue) => {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      let passApi = `websites?search=${searchvalue}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          articlelist: data?.result,
          isLoading: false,
          articleCount: data?.count,
          // offset: page,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };
  handalValidate = () => {
    let { articleValues, uploadImage, image } = this.state;

    const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
    let flag = true;
    if (articleValues?.articleTitle?.trim() === "") {
      flag = false;
      this.setState({
        errorTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTitle: "",
      });
    }
    if (articleValues?.articleSubTitle?.trim() === "") {
      flag = false;
      this.setState({
        errorInitialTitle: "This field is mandatory",
      });
    } else {
      this.setState({
        errorInitialTitle: "",
      });
    }
    if (articleValues?.url?.trim() === "") {
      flag = false;
      this.setState({
        errorUrl: "This field is mandatory",
      });
    } else if (
      articleValues?.url &&
      articleValues?.url?.trim() !== "" &&
      !urlRegex.test(articleValues?.url)
    ) {
      flag = false;
      this.setState({
        errorUrl: "Please enter a valid URL",
      });
    } else {
      this.setState({
        errorUrl: "",
      });
    }
    if (
      (uploadImage && uploadImage !== "") ||
      (image && image?.length !== 0 && image?.[0]?.preview !== "")
    ) {
      this.setState({
        errorImage: "",
      });
    } else {
      flag = false;
      this.setState({
        errorImage: "This field is mandatory",
      });
    }
    // if (articleValues?.body === "") {
    //   flag = false;
    //   this.setState({
    //     errorDes: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorDes: "",
    //   });
    // }
    return flag;
  };
  handeModleLoading = (status) => {
    this.setState({
      isLoading: status,
    });
  };
  handleSave = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });
      const { image, uploadImage, articleValues, sortLabelid } = this.state;

      let sortData = sortLabelid;

      let payload = {
        title: articleValues?.articleTitle,
        short_desc: articleValues?.articleSubTitle,
        url: articleValues?.url,
        desc: articleValues?.body,
      };
      if (image?.length > 0) {
        let fileData = await this.setMedia(image[0]);
        if (fileData) {
          payload = {
            ...payload,
            mediaId: fileData?.image?.id,
          };
          this.setState({
            uploadImage: fileData?.image?.filePath,
          });
        }
      }

      try {
        const { status } = await axiosInstance.post(`/websites`, payload);
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            uploadImage: "",
            content: "",
          });
          this.fetchAllArticles(this?.state?.search);
          this.setActionMessage(
            true,
            "Success",
            `Recommanded Website Created Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            uploadImage: "",
          });
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          image: [],
          uploadImage: "",
        });
      }
    }
  };

  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const { image, uploadImage, articleValues, sortLabelid } = this.state;
      let sortData = sortLabelid;

      let payload = {
        title: articleValues?.articleTitle,
        short_desc: articleValues?.articleSubTitle,
        url: articleValues?.url,
        desc: articleValues?.body,
      };
      if (image?.length > 0) {
        let fileData = await this.setMedia(image[0]);

        if (fileData) {
          payload = {
            ...payload,
            mediaId: fileData?.image?.id,
          };
          this.setState({
            uploadImage: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          mediaId: articleValues?.mediaId,
        };
      }

      try {
        const { status } = await axiosInstance.put(
          `websites/${this.state.articleValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            uploadImage: "",
            content: "",
          });
          this.fetchAllArticles(this?.state?.search);
          this.setActionMessage(
            true,
            "Success",
            `Recommended Website Updated Successfully`
          );
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            image: [],
            uploadImage: "",
          });
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          image: [],
          uploadImage: "",
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      articleValues: {
        articleTitle: "",
        articleSubTitle: "",
        url: "",
        id: "",
        body: "",
      },
      uploadImage: "",
      isInputModalOpen: !this.state.isInputModalOpen,
      errorTitle: "",
      errorInitialTitle: "",
      errorUrl: "",
      errorImage: "",
      errorDes: "",
      editorState: EditorState.createEmpty(),
      image: [],
      content: "",
    });
  };

  inputModal = (item, type) => async () => {
    // this.fetchAllCategory(0);
    // this.fetchAllTag(0);
    // this.fetchAllRelatedCategory(0);

    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      const htmlString = he.decode(String(item?.desc));

      // const contentBlock = htmlToDraft(htmlString);

      if (typeof htmlString === "string") {
        this.setState({
          content: htmlString,
        });
      }

      // const contentBlock = htmlToDraft(htmlString);
      // if (contentBlock) {
      //   const contentState = ContentState.createFromBlockArray(
      //     contentBlock.contentBlocks
      //   );
      //   const editorState = EditorState.createWithContent(contentState);
      //   this.setState({ editorState });
      // }

      this.setState({
        articleValues: {
          // articleTitle: {
          //   dangerouslySetInnerHTML: { __html: item?.title },
          // },
          articleTitle: item?.title,
          articleSubTitle: item?.short_desc,
          url: item?.url,
          body: item?.desc,
          mediaId: item?.mediaId,
          id: item?.id,
        },
        uploadImage: item?.Medium?.filePath,
        isEditMode: true,
      });
    } else {
      this.setState({
        articleValues: {
          articleTitle: "",
          articleSubTitle: "",
          url: "",
          id: "",
          body: "",
        },
        uploadImage: "",
        isEditMode: false,
        editorState: EditorState.createEmpty(),
        image: [],
        content: "",
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const { articleValues, sortLabelid } = this.state;
    let sortData = sortLabelid;

    try {
      const passApi = `websites/${this.state.itemToDelete}`;
      const { status } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllArticles(this?.state?.search);
        });
        this.setActionMessage(
          true,
          "Success",
          "Recommended Website Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage, offset, search, sortLabelid } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
    // this.fetchAllArticles(
    //   offset,
    //   search,
    //   filterDate,
    //   selectCategory,
    //   selectedNewsType,
    //   selectedNewsStatus,
    //   sortType,
    //   sortLabelid
    // );
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, articlelist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files, errorImage: "" });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handleImageUpload = (imageUrl) => {
    const { editorState } = this.state;
    const contentState = editorState.getCurrentContent();
    const contentStateWithEntity = contentState.createEntity(
      "IMAGE",
      "IMMUTABLE",
      { src: imageUrl }
    );
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
    const newEditorState = EditorState.set(editorState, {
      currentContent: contentStateWithEntity,
    });
    this.setState({
      editorState: AtomicBlockUtils.insertAtomicBlock(
        newEditorState,
        entityKey,
        " "
      ),
    });
  };

  decodeHTMLEntities(encodedString) {
    const element = document.createElement("input");
    element.innerHTML = encodedString;
    return element.textContent;
  }

  inputViewImgModal = (data) => {
    this.setState({
      isViewImgModalOpen: true,
      isViewImgData: data,
    });
  };
  toggleViewImgModal = () => {
    this.setState({
      isViewImgModalOpen: false,
      isViewImgData: {},
    });
  };
  // sortLabelHandler = (type) => {
  //   const { sortLabelid, offset, search } = this.state;

  //   this.fetchAllArticles(0, search, !sortLabelid);
  //   this.setState({
  //     sortLabelid: !sortLabelid,
  //     currentPage: 1,
  //   });
  // };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  // handleDragStart = (item) => {
  //   this.setState({ draggedItem: item });
  // };

  // // Function to handle the drop and reorder the items

  // handleDrop = (targetIndex) => {
  //   const { articlelist, draggedItem } = this.state;

  //   if (draggedItem) {
  //     const updatedItems = [...articlelist];
  //     const draggedIndex = articlelist.indexOf(draggedItem);

  //     // Remove the dragged item from the original position
  //     updatedItems.splice(draggedIndex, 1);

  //     // Insert the dragged item at the new position
  //     updatedItems.splice(targetIndex, 0, draggedItem);

  //     this.setState({
  //       articlelist: updatedItems,
  //       draggedItem: null,
  //       isSortChange: true,
  //     });
  //   }
  // };

  handleOrderChange = async () => {
    const { offset, search, articlelist, sortLabelid } = this.state;
    let sortData = sortLabelid;

    let newdata = [];
    let categories = articlelist?.map((item) => {
      newdata.push(item?.id);
    });
    try {
      const { status, data } = await axiosInstance.put(
        `websites/update/order`,
        newdata
      );
      if (status === 200) {
        this.setState({ isSortChange: false });
        this.fetchAllArticles(search);
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isSortChange: false });
      }
    } catch (err) {
      this.setState({ isSortChange: false });
    }
  };
  handleDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    const updatedList = Array.from(this.state?.articlelist);
    const [draggedItem] = updatedList.splice(result.source.index, 1);
    updatedList.splice(result.destination.index, 0, draggedItem);
    this.setState({ articlelist: updatedList, isSortChange: true });
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllArticles(search);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      articleValues,
      articlelist,
      articleCount,
      errorTitle,
      errorInitialTitle,
      errorUrl,
      errorDes,
      errorImage,
      search,
      editorState,
      image,
      uploadImage,
      sortLabelid,
      isViewImgModalOpen,
      isViewImgData,
      draggedItem,
      content,
    } = this.state;
    const pageNumbers = [];
    if (articleCount > 0) {
      for (let i = 1; i <= Math.ceil(articleCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">
                  Recommended Website
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Typography variant="h1" align="left">
                  Recommended Website
                </Typography>
              </Grid>

              <Grid item xs={7} className="admin-filter-wrap">
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  className="txt-field-class"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  onChange={(e) => {
                    this.setState({ search: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchAllArticles(search);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    minWidth: "90px",
                    // marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap"
                style={{ marginBottom: "10px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: this.state.isSortChange
                      ? "#4455c7"
                      : "rgba(0, 0, 0, 0.12)",
                    color: this.state.isSortChange
                      ? "#fff"
                      : "rgba(0, 0, 0, 0.26)",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  disabled={!this.state.isSortChange}
                  onClick={() => {
                    this.handleOrderChange();
                  }}
                >
                  Save Order
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && articlelist?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && articlelist?.length > 0 && (
              <DragDropContext onDragEnd={this.handleDragEnd}>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable"
                    aria-label="simple table"
                    style={{ minWidth: "max-content" }}
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          // onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer" }}
                        >
                          DID
                          {/* <TableSortLabel
                        active={true}
                        direction={sortLabelid ? "asc" : "desc"}
                      /> */}
                        </TableCell>
                        <TableCell>Title</TableCell>
                        <TableCell>Website Image</TableCell>
                        <TableCell
                          style={{
                            // cursor: "pointer",
                            width: "200px",
                          }}
                          // onClick={() => this.sortLabelHandler("title")}
                        >
                          URL
                          {/* <TableSortLabel
                        active={true}
                        direction={
                          sortType === "title"
                            ? sortTitle
                              ? "asc"
                              : "desc"
                            : "desc"
                        }
                      /> */}
                        </TableCell>

                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <Droppable droppableId="your-droppable-id">
                      {(provided, snapshot) => (
                        <TableBody
                          className="table_body"
                          ref={provided.innerRef}
                        >
                          <TableRow className="table_row">
                            <TableCell
                              colSpan={100}
                              className="table-seprator"
                            ></TableCell>
                          </TableRow>
                          {articlelist?.map((item, index) => {
                            return (
                              <Draggable
                                key={item?.id}
                                draggableId={`draggable-${item?.id}`}
                                index={index}
                              >
                                {(provided, snapshot) => (
                                  <TableRow
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    className="table-rows listTable-Row"
                                    style={{
                                      cursor: "all-scroll",
                                      ...provided.draggableProps.style,
                                    }}
                                    key={item?.id}
                                  >
                                    <TableCell> {item?.id} </TableCell>
                                    <TableCell
                                      style={{
                                        maxWidth: "300px",
                                        wordWrap: "break-word",
                                      }}
                                    >
                                      {item?.title}
                                    </TableCell>
                                    <TableCell className="feature-img">
                                      <Box
                                        onClick={() => {
                                          this.inputViewImgModal(item);
                                        }}
                                        style={{ cursor: "pointer" }}
                                      >
                                        {item?.Medium?.filePath ? (
                                          <img
                                            className="auto-width"
                                            src={
                                              config.mediaUrl +
                                              item?.Medium?.filePath
                                            }
                                            alt="featureImage"
                                          />
                                        ) : (
                                          ""
                                        )}
                                      </Box>
                                    </TableCell>
                                    <TableCell>
                                      <a href={item?.url} target="_blank">
                                        <span
                                          // dangerouslySetInnerHTML={{
                                          //   __html: item?.title,
                                          // }}
                                          style={{
                                            color: "blue",
                                            textDecoration: "underline",
                                          }}
                                        >
                                          {item?.url}
                                        </span>
                                      </a>
                                      {/* {decodeURIComponent(tilte)} */}
                                    </TableCell>

                                    <TableCell>
                                      <Button
                                        onClick={this.inputModal(item, "edit")}
                                        className={
                                          // !item?.userId && item?.NewsProviderId === 1
                                          //   ? "table-btn edit-btn disabled-btn"
                                          // :
                                          "table-btn edit-btn"
                                        }
                                        // disabled={
                                        //   !item?.userId && item?.NewsProviderId === 1
                                        // }
                                      >
                                        Edit
                                      </Button>
                                      <Button
                                        onClick={this.setItemToDelete(item?.id)}
                                        className="table-btn delete-btn"
                                      >
                                        Delete
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                )}
                              </Draggable>
                            );
                          })}
                          {/* <TableRow>
                    <TableCell colSpan={100} className="pagination">
                      <div className="tablePagination">
                        {/* <button
                            className={
                              articlelist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              articlelist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                          {/* <Pagination
                          hideNextButton
                          hidePrevButton
                          disabled={
                            articleCount / rowPerPage > 1 ? false : true
                          }
                          page={currentPage}
                          onChange={this.handlePaginationClick}
                          count={pageNumbers[pageNumbers?.length - 1]}
                          siblingCount={2}
                          boundaryCount={1}
                          size="small"
                        /> */}
                          {/* <button
                            className={
                              articlelist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              articlelist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> /}
                      </div>
                    </TableCell>
                  </TableRow> */}
                        </TableBody>
                      )}
                    </Droppable>
                  </Table>
                </TableContainer>
              </DragDropContext>
            )}

            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="news-modal recommended-modal"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={
                  isLoading ? "paper modal-disable" : "paper modal-show-scroll"
                }
              >
                {isLoading && (
                  <Box className="modal-loader">
                    <Loader />
                  </Box>
                )}
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Recommended Website"
                    : "Edit Recommended Website"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <Paper className="pageWrapper api-provider"> */}
                    <Box
                      style={{
                        position: "fixed",
                        top: "50px",
                        backgroundColor: "transparent",
                        zIndex: "2",
                        width: "100%",
                        maxWidth: "985px",
                      }}
                    >
                      {messageBox?.display && (
                        <ActionMessage
                          message={messageBox?.message}
                          type={messageBox?.type}
                          styleClass={messageBox?.styleClass}
                        />
                      )}
                    </Box>
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Title <span className="red">*</span>
                        </label>
                        <TextField
                          className="teamsport-textfield recommended-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Website Title"
                          value={articleValues?.articleTitle}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                articleTitle: e.target.value,
                              },
                              errorTitle: e?.target?.value ? "" : errorTitle,
                            })
                          }
                        />
                        {errorTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text recommended-textarea"
                      >
                        <label className="modal-label">
                          {" "}
                          Short Description <span className="red">*</span>
                        </label>

                        <TextField
                          className="teamsport-textfield rec recommended-textfield"
                          variant="outlined"
                          type="textarea"
                          multiline
                          maxRows={3}
                          color="primary"
                          size="small"
                          placeholder="Short Description"
                          value={articleValues?.articleSubTitle}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                articleSubTitle: e?.target?.value,
                              },
                              errorInitialTitle: e?.target?.value
                                ? ""
                                : errorInitialTitle,
                            })
                          }
                        />
                        {errorInitialTitle ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorInitialTitle}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label">
                          {" "}
                          Website Link <span className="red">*</span>{" "}
                        </label>
                        <TextField
                          className="teamsport-textfield recommended-textfield"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Website Link"
                          value={articleValues?.url}
                          onChange={(e) =>
                            this.setState({
                              articleValues: {
                                ...articleValues,
                                url: e.target.value,
                              },
                              errorUrl: e?.target?.value ? "" : errorUrl,
                            })
                          }
                        />
                        {errorUrl ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorUrl}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <div
                        className="blog-file-upload"
                        style={{ width: "97%", marginTop: "10px" }}
                      >
                        <label className="modal-label">
                          {" "}
                          Website Image <span className="red">*</span>{" "}
                        </label>
                        <FileUploader
                          onDrop={(image) =>
                            this.handleFileUpload("image", image)
                          }
                          style={{ marginTop: "5px" }}
                        />
                        <div className="logocontainer">
                          {image?.length > 0
                            ? image?.map((file, index) => (
                                <img
                                  className="auto-width"
                                  key={index}
                                  src={file.preview}
                                  alt="player"
                                />
                              ))
                            : uploadImage &&
                              uploadImage !== "" && (
                                <img
                                  className="auto-width"
                                  src={config?.mediaUrl + uploadImage}
                                  alt="player"
                                />
                              )}
                        </div>
                        {errorImage ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorImage}
                          </p>
                        ) : (
                          ""
                        )}
                      </div>
                    </Grid>

                    <div className="suneditor-wrap">
                      <label className="modal-label"> Description</label>
                      {/* <Editor
                      editorState={editorState}
                      onEditorStateChange={this.handleEditorChange}
                    /> */}
                      {/* <ImageUploader
                      onImageUpload={(image) => this.handleImageUpload(image)}
                      handeModleLoading={(status) =>
                        this.handeModleLoading(status)
                      }
                    /> */}
                      {/* {errorDes ? (
                      <p className="errorText" style={{ margin: "0 0 0 0" }}>
                        {errorDes}
                      </p>
                    ) : (
                      ""
                    )} */}
                      <SunEditor
                        onChange={this.handleChange}
                        setContents={content}
                        setOptions={{
                          buttonList: [
                            ["undo", "redo"],
                            ["font", "fontSize", "formatBlock"],
                            [
                              "bold",
                              "underline",
                              "italic",
                              "strike",
                              "subscript",
                              "superscript",
                            ],
                            ["removeFormat"],
                            ["outdent", "indent"],
                            ["align", "horizontalRule", "list", "table"],
                            ["link", "image", "video"],
                            ["fullScreen", "showBlocks", "codeView"],
                          ],
                        }}
                      />
                    </div>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={isViewImgModalOpen}
              onClose={this.toggleViewImgModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">View Image</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleViewImgModal}
                />
                <Grid item xs={12} className="runnerInfo">
                  <Grid
                    item
                    xs={12}
                    className="runnerInfo-text"
                    style={{ display: "flex", flexDirection: "column" }}
                  >
                    <Box style={{ margin: "0 auto" }}>
                      {isViewImgData?.Medium?.filePath ? (
                        <img
                          className="auto-width"
                          src={
                            config.mediaUrl + isViewImgData?.Medium?.filePath
                          }
                          alt="featureImage"
                        />
                      ) : (
                        ""
                      )}
                    </Box>
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        onClick={this.toggleViewImgModal}
                        // className="mr-lr-30"
                        value="Back"
                        style={{ minWidth: "auto" }}
                      />
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default RecommendedWebsite;
