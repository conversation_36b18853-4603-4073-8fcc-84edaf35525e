<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="29.939" height="18.378" viewBox="0 0 29.939 18.378">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_1577" data-name="Path 1577" d="M303.251,480.444a5.4,5.4,0,0,0-.833-1.085c-.287-.226-.378-.411-.513-.423s-.263.066-.349-.037-.128-.276-.275-.288a.251.251,0,0,0-.221.062.239.239,0,0,1-.275-.037c-.123-.086-.526-.2-.636-.276s-.054.128-.012.165-.057.012-.258-.037-.354-.037-.177.135.27.349.4.411.029.128-.184.226-1.858,1.233-2.5,1.607a18,18,0,0,0-1.775,1.156,14.82,14.82,0,0,1-4.146.235c-.549-.093-3.18-.362-3.839-.467a4.883,4.883,0,0,0-2.6-.035c-.94.357-2.241,1.036-2.427,2.29s-.2,2,.878,2.731,2.726,1.421,2.745,2.32a11.264,11.264,0,0,1-.094,1.609c-.019.318.019.727.448.859a15.266,15.266,0,0,1,2.482,1.085c.373.282,1.493,1.066,1.569,1.2a1.036,1.036,0,0,1,.056.468c0,.113.3.28.467.355s.409.2.54.261.188-.224.169-.522a1.2,1.2,0,0,0-.411-.974,5.029,5.029,0,0,1-.617-.635,1.272,1.272,0,0,0-.578-.431,12.942,12.942,0,0,1-2.017-1.047,1.154,1.154,0,0,1-.521-1.085c0-.562.092-1.7.129-1.964a3.119,3.119,0,0,0,.113-.692c-.019-.242.11-.151.28.019a8.511,8.511,0,0,0,1.662.953,18.688,18.688,0,0,0,1.848.487c.5.094.895.151,1.007.188s.094.226.113.355.357.263.5.355a12.9,12.9,0,0,0,1.4,1.233,6.758,6.758,0,0,1,1.1.767c.131.113.056.3.056.355s-.188.191-.542.282a5.842,5.842,0,0,1-1.214.186,1.632,1.632,0,0,1-.876-.167c-.188-.094-.019-.261-.094-.374a.826.826,0,0,0-.747-.374.954.954,0,0,0-.615.2c-.094.038-.134.148.129.336s.507.468.655.506a1.349,1.349,0,0,1,.6.318c.185.186.43.525.766.468s1.233-.393,1.531-.487a2.993,2.993,0,0,1,.634-.151.474.474,0,0,0,.542-.223c.2-.318.261-.376.279-.525s.148-.43-.132-.635a6.716,6.716,0,0,1-.822-1.235c-.148-.2-.559-.934-.559-.934a4.569,4.569,0,0,0,.615-.261c.282-.151.653-.544.8-.562a2.5,2.5,0,0,0,.672-.3,1.923,1.923,0,0,0,.636-.9,6.193,6.193,0,0,0,.392-1.607c.038-.468.073-.786.073-.786a1.17,1.17,0,0,1,.206-.444,9.585,9.585,0,0,0,.833-1.282,1.8,1.8,0,0,1,.562-.76,1.049,1.049,0,0,1,.648-.177c.221.025.405.165.607.2s.965.522,1.248.6.57.177.619.3.042.288.177.288.521.062.545-.025.078-.145.177-.1.4-.086.422-.222-.049-.312.074-.4.221-.32.1-.448-.251-.234-.349-.349-.853-1.152-1.017-1.361A2.629,2.629,0,0,1,303.251,480.444Z" transform="translate(-282.537 -478.344)" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_1576" data-name="Path 1576" d="M303.251,480.444a5.4,5.4,0,0,0-.833-1.085c-.287-.226-.378-.411-.513-.423s-.263.066-.349-.037-.128-.276-.275-.288a.251.251,0,0,0-.221.062.239.239,0,0,1-.275-.037c-.123-.086-.526-.2-.636-.276s-.054.128-.012.165-.057.012-.258-.037-.354-.037-.177.135.27.349.4.411.029.128-.184.226-1.858,1.233-2.5,1.607a18,18,0,0,0-1.775,1.156,14.82,14.82,0,0,1-4.146.235c-.549-.093-3.18-.362-3.839-.467a4.883,4.883,0,0,0-2.6-.035c-.94.357-2.241,1.036-2.427,2.29s-.2,2,.878,2.731,2.726,1.421,2.745,2.32a11.264,11.264,0,0,1-.094,1.609c-.019.318.019.727.448.859a15.266,15.266,0,0,1,2.482,1.085c.373.282,1.493,1.066,1.569,1.2a1.036,1.036,0,0,1,.056.468c0,.113.3.28.467.355s.409.2.54.261.188-.224.169-.522a1.2,1.2,0,0,0-.411-.974,5.029,5.029,0,0,1-.617-.635,1.272,1.272,0,0,0-.578-.431,12.942,12.942,0,0,1-2.017-1.047,1.154,1.154,0,0,1-.521-1.085c0-.562.092-1.7.129-1.964a3.119,3.119,0,0,0,.113-.692c-.019-.242.11-.151.28.019a8.511,8.511,0,0,0,1.662.953,18.688,18.688,0,0,0,1.848.487c.5.094.895.151,1.007.188s.094.226.113.355.357.263.5.355a12.9,12.9,0,0,0,1.4,1.233,6.758,6.758,0,0,1,1.1.767c.131.113.056.3.056.355s-.188.191-.542.282a5.842,5.842,0,0,1-1.214.186,1.632,1.632,0,0,1-.876-.167c-.188-.094-.019-.261-.094-.374a.826.826,0,0,0-.747-.374.954.954,0,0,0-.615.2c-.094.038-.134.148.129.336s.507.468.655.506a1.349,1.349,0,0,1,.6.318c.185.186.43.525.766.468s1.233-.393,1.531-.487a2.993,2.993,0,0,1,.634-.151.474.474,0,0,0,.542-.223c.2-.318.261-.376.279-.525s.148-.43-.132-.635a6.716,6.716,0,0,1-.822-1.235c-.148-.2-.559-.934-.559-.934a4.569,4.569,0,0,0,.615-.261c.282-.151.653-.544.8-.562a2.5,2.5,0,0,0,.672-.3,1.923,1.923,0,0,0,.636-.9,6.193,6.193,0,0,0,.392-1.607c.038-.468.073-.786.073-.786a1.17,1.17,0,0,1,.206-.444,9.585,9.585,0,0,0,.833-1.282,1.8,1.8,0,0,1,.562-.76,1.049,1.049,0,0,1,.648-.177c.221.025.405.165.607.2s.965.522,1.248.6.57.177.619.3.042.288.177.288.521.062.545-.025.078-.145.177-.1.4-.086.422-.222-.049-.312.074-.4.221-.32.1-.448-.251-.234-.349-.349-.853-1.152-1.017-1.361A2.629,2.629,0,0,1,303.251,480.444Z" transform="translate(-282.537 -478.344)"/>
    </clipPath>
  </defs>
  <g id="Group_24281" data-name="Group 24281" transform="translate(1332.343 -1776.481)">
    <g id="Group_6616" data-name="Group 6616" transform="translate(-1313.496 1786.702)">
      <path id="Path_1510" data-name="Path 1510" d="M495.944,642.1c-.07.131-.086.149-.192.2a.37.37,0,0,1-.149.026c-.061-.009.06-.035.122-.1S496.013,641.967,495.944,642.1Z" transform="translate(-495.586 -642.055)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6617" data-name="Group 6617" transform="translate(-1313.897 1787.067)">
      <path id="Path_1511" data-name="Path 1511" d="M488.544,648.94c-.018.035-.147.2-.227.219a.646.646,0,0,1-.219.045c-.122,0,.06-.018.14-.071s-.234.166-.14.018a1.186,1.186,0,0,1,.193-.21C488.334,648.9,488.561,648.9,488.544,648.94Z" transform="translate(-488.057 -648.91)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6618" data-name="Group 6618" transform="translate(-1314.218 1787.098)">
      <path id="Path_1512" data-name="Path 1512" d="M482.43,649.527a.6.6,0,0,1-.183.184.4.4,0,0,1-.219.026c-.044,0,.034-.017.148-.087S482.474,649.448,482.43,649.527Z" transform="translate(-482.015 -649.503)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6619" data-name="Group 6619" transform="translate(-1315.807 1787.391)">
      <path id="Path_1513" data-name="Path 1513" d="M453.108,655.005a2.092,2.092,0,0,1-.526.122,1.369,1.369,0,0,1-.35-.044c-.087-.026-.167-.026.07-.035s.481-.035.63-.044A.7.7,0,0,1,453.108,655.005Z" transform="translate(-452.146 -655.002)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6620" data-name="Group 6620" transform="translate(-1316.248 1787.034)">
      <path id="Path_1514" data-name="Path 1514" d="M444.213,648.384c-.219-.009-.245-.087-.306-.079s-.141-.018.087.087a.608.608,0,0,0,.411.087C444.5,648.462,444.433,648.392,444.213,648.384Z" transform="translate(-443.842 -648.304)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6621" data-name="Group 6621" transform="translate(-1316.248 1786.473)">
      <path id="Path_1515" data-name="Path 1515" d="M443.869,637.79a1.2,1.2,0,0,0,.376.237c.1.035.034-.01-.149-.132S443.8,637.712,443.869,637.79Z" transform="translate(-443.849 -637.755)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6622" data-name="Group 6622" transform="translate(-1315.693 1785.68)">
      <path id="Path_1516" data-name="Path 1516" d="M454.363,622.877c.113.106.2.15.218.211s-.051.026-.175-.079S454.25,622.771,454.363,622.877Z" transform="translate(-454.294 -622.838)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6623" data-name="Group 6623" transform="translate(-1316.34 1785.504)">
      <path id="Path_1517" data-name="Path 1517" d="M442.176,619.573c.132.149.229.167.351.316s-.175-.025-.272-.131S442.044,619.424,442.176,619.573Z" transform="translate(-442.117 -619.524)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6624" data-name="Group 6624" transform="translate(-1315.157 1785.624)">
      <path id="Path_1518" data-name="Path 1518" d="M464.477,621.818a1.949,1.949,0,0,1,.289.22c.07.07.045.061-.14-.009s-.228-.141-.254-.193S464.416,621.784,464.477,621.818Z" transform="translate(-464.367 -621.794)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6625" data-name="Group 6625" transform="translate(-1315.024 1785.905)">
      <path id="Path_1519" data-name="Path 1519" d="M466.874,627.085c.079.044.192,0,.28.087s.14.177-.035.115S466.794,627.041,466.874,627.085Z" transform="translate(-466.852 -627.078)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6626" data-name="Group 6626" transform="translate(-1315.265 1785.946)">
      <path id="Path_1520" data-name="Path 1520" d="M462.436,627.9a1.328,1.328,0,0,0,.306.254c.183.1-.027.044-.149-.018s-.148-.141-.227-.22S462.314,627.794,462.436,627.9Z" transform="translate(-462.321 -627.838)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6627" data-name="Group 6627" transform="translate(-1315.777 1785.266)">
      <path id="Path_1521" data-name="Path 1521" d="M452.878,615.124c.244.158.176.256-.026.106S452.634,614.966,452.878,615.124Z" transform="translate(-452.698 -615.048)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6628" data-name="Group 6628" transform="translate(-1315.409 1785.554)">
      <path id="Path_1522" data-name="Path 1522" d="M459.742,620.53c.131.149.061.106.14.185s.1.141-.07.035-.237-.289-.176-.237S459.611,620.38,459.742,620.53Z" transform="translate(-459.615 -620.47)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6634" data-name="Group 6634" transform="translate(-1318.071 1782.884)">
      <path id="Path_1532" data-name="Path 1532" d="M416.661,570.267a2.418,2.418,0,0,0-1.074.683A6.038,6.038,0,0,1,414.358,572a5.461,5.461,0,0,1-1.2.68,1.915,1.915,0,0,1-.635-.094.969.969,0,0,1-.4-.365c-.029-.074-.01.093.076.185a1.009,1.009,0,0,0,.44.267c.152.028.277.074.344.074s-.182.083-.277.083a1.849,1.849,0,0,1-.583-.342c-.1-.1-.086-.028.009.064a1.278,1.278,0,0,0,.21.167,1.552,1.552,0,0,1-.4-.185,2.8,2.8,0,0,0-.6-.24,5.528,5.528,0,0,0-.65-.092c-.115,0-.573.018-.659.037s.085.046.229.028a2.951,2.951,0,0,1,.507-.009c.2.009.3.138.22.1s-.5-.093-.315-.028.239.093.1.13a1.078,1.078,0,0,0-.315.138.468.468,0,0,1-.334.037c-.125-.018-.01.056.124.093a1.087,1.087,0,0,1,.315.148,3.493,3.493,0,0,0,.879.3c.191.009.181.055.372.055s.163.064-.1.074a2.38,2.38,0,0,1-.736-.065,2.739,2.739,0,0,0-.477-.138c-.114-.018-.133-.047.2.1s-.1.028-.239-.018a1.565,1.565,0,0,0-.43-.074c-.115,0,.085.028.219.055s.622.259.784.314a1.424,1.424,0,0,0,.315.055c.143.028-.383,0-.526-.046s-.554-.24-.707-.268a2.292,2.292,0,0,0-.258-.028c-.067-.009.1.055.23.074a2.57,2.57,0,0,1,.535.213,1.333,1.333,0,0,0,.392.138,1.726,1.726,0,0,1-.44-.028c-.143-.037-.421-.148-.574-.2s-.306-.028-.1.028a7.576,7.576,0,0,0,.86.268c.153,0,.4.018.2.092a1.584,1.584,0,0,1-.678-.028,2.172,2.172,0,0,1-.325-.194c-.1-.055-.115-.036.038.065a1.659,1.659,0,0,0,.689.24s.21-.019.057.028a3.266,3.266,0,0,0-.746.314,2.754,2.754,0,0,0-.411.37c-.057.092.058,0,.182-.111a2.748,2.748,0,0,1,.468-.333c.115-.055.335-.12.431-.157s.315-.064-.029.092a4.092,4.092,0,0,0-.812.48c.172-.074.478-.268.6-.3a2.321,2.321,0,0,1,.468-.157c.125,0,.267.1.382.1a4.644,4.644,0,0,0,.917.018,2.587,2.587,0,0,0,.822-.36c.134-.092.181-.028.048.083a2.034,2.034,0,0,1-.478.3,1.056,1.056,0,0,1-.477.065c-.21-.009-.573-.065-.344.018a1.151,1.151,0,0,0,.42.083c.057,0-.163.092-.3.12a9.67,9.67,0,0,0-.975.222,1.1,1.1,0,0,0-.315.157c-.134.092-.029.1.057.037a.838.838,0,0,1,.345-.157c.162-.037.746-.12,1-.138a4.233,4.233,0,0,0,.9-.259,1.953,1.953,0,0,0,.564-.277c.1-.1.162-.111.1-.009s-.219.176-.3.24.162,0,.229-.074.21-.194.277-.286.382-.342.3-.157a2.128,2.128,0,0,1-.554.536,2.062,2.062,0,0,1-.573.25,1.553,1.553,0,0,1-.354.046c-.143.009.124.046.249.018a2.434,2.434,0,0,0,.612-.194c.2-.111.353-.212.353-.212a1.869,1.869,0,0,1-.277.258,1.368,1.368,0,0,1-.392.185c-.191.046-.029.074.114.018a1.04,1.04,0,0,0,.421-.2,3.093,3.093,0,0,0,.353-.323c.057-.092.182-.36.3-.517a2.413,2.413,0,0,1,.382-.481.753.753,0,0,1,.268-.129c.048,0,.02.194-.086.342a4.237,4.237,0,0,1-.449.6,1.807,1.807,0,0,1-.325.212c-.115.065-.238.148-.143.13a.886.886,0,0,0,.325-.1c.1-.064.038.047-.067.139s-.277.231-.143.175.181-.018.353-.212.373-.545.507-.683.3-.4.382-.462.44-.082.554-.148a3.055,3.055,0,0,0,.439-.342c.048-.056-.1-.97-.01-1.284S416.661,570.267,416.661,570.267Z" transform="translate(-409.58 -570.267)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6635" data-name="Group 6635" transform="translate(-1316.593 1785.101)">
      <path id="Path_1533" data-name="Path 1533" d="M438.119,612.27a1.426,1.426,0,0,0-.5-.277c-.22-.065-.373-.074-.144.055a1.936,1.936,0,0,0,.449.222A.575.575,0,0,0,438.119,612.27Z" transform="translate(-437.365 -611.947)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6636" data-name="Group 6636" transform="translate(-1317.249 1785.313)">
      <path id="Path_1534" data-name="Path 1534" d="M426.689,616.318c.124.009-.449-.093-.612-.148a4.7,4.7,0,0,0-.526-.184,2.69,2.69,0,0,0-.411-.037c-.153,0-.163.037.067.065a1.893,1.893,0,0,1,.526.12,2.783,2.783,0,0,0,.506.194C426.393,616.345,426.565,616.308,426.689,616.318Z" transform="translate(-425.031 -615.948)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6637" data-name="Group 6637" transform="translate(-1316.611 1785.966)">
      <path id="Path_1535" data-name="Path 1535" d="M438.105,628.61c-.067-.018-.459-.213-.592-.277a2.007,2.007,0,0,0-.468-.111c-.106-.018.115.046.287.111s.363.194.458.231a.741.741,0,0,0,.315.1,1.5,1.5,0,0,0,.516-.1c.1-.055-.076-.019-.249.018S438.172,628.628,438.105,628.61Z" transform="translate(-437.018 -628.219)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6638" data-name="Group 6638" transform="translate(-1313.42 1785.33)">
      <path id="Path_1536" data-name="Path 1536" d="M497.642,616.353a2.5,2.5,0,0,0-.411.489c-.1.167-.142.24-.191.314s0,.019.181-.194a5.772,5.772,0,0,1,.439-.508C497.8,616.325,497.833,616.168,497.642,616.353Z" transform="translate(-497.023 -616.268)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6641" data-name="Group 6641" transform="translate(-1316.099 1780.58)">
      <path id="Path_1542" data-name="Path 1542" d="M447.9,526.952c-.065.016-1.174.487-1.174.487l-.081.178s.664-.235.81-.3l.692-.307Z" transform="translate(-446.642 -526.952)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6642" data-name="Group 6642" transform="translate(-1316.735 1780.867)">
      <path id="Path_1543" data-name="Path 1543" d="M437.194,532.344a4.72,4.72,0,0,1-.712.381,3.792,3.792,0,0,1-.875.162c-.2.016-.348.016-.348.016l-.073.154s.34-.049.453-.049a5.527,5.527,0,0,0,.81-.154,2.951,2.951,0,0,1,.308-.1s-.186.787-.373,1.306a2.549,2.549,0,0,1-.818,1.176,5.276,5.276,0,0,1-.624.268.678.678,0,0,1-.235-.016l-.016.162a.6.6,0,0,0,.251,0,5.731,5.731,0,0,0,1.061-.511,7.656,7.656,0,0,0,.543-1.1c.065-.178.332-1.274.4-1.355a3.324,3.324,0,0,1,.509-.32C437.533,532.325,437.194,532.344,437.194,532.344Z" transform="translate(-434.692 -532.34)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6643" data-name="Group 6643" transform="translate(-1324.828 1777.995)">
      <g id="Group_6593" data-name="Group 6593" transform="translate(0 0)">
        <g id="Group_6592" data-name="Group 6592" clip-path="url(#clip-path)">
          <g id="Group_6591" data-name="Group 6591">
            <g id="Group_6590" data-name="Group 6590">
              <g id="Group_6589" data-name="Group 6589" clip-path="url(#clip-path-2)">
                <g id="Group_6588" data-name="Group 6588" transform="translate(-12.66 -4.78)">
                  <path id="Path_1544" data-name="Path 1544" d="M304.959,478.344H282.536v16.6h22.423v-16.6Z" transform="translate(-269.876 -473.564)"/>
                  <ellipse id="Ellipse_65" data-name="Ellipse 65" cx="8.084" cy="10.491" rx="8.084" ry="10.491" transform="matrix(0.351, -0.936, 0.936, 0.351, 0, 15.14)"/>
                  <ellipse id="Ellipse_66" data-name="Ellipse 66" cx="7.998" cy="10.379" rx="7.998" ry="10.379" transform="translate(0.134 15.099) rotate(-69.445)"/>
                  <ellipse id="Ellipse_67" data-name="Ellipse 67" cx="7.912" cy="10.267" rx="7.912" ry="10.267" transform="translate(0.269 15.057) rotate(-69.446)"/>
                  <ellipse id="Ellipse_68" data-name="Ellipse 68" cx="7.826" cy="10.156" rx="7.826" ry="10.156" transform="translate(0.405 15.016) rotate(-69.447)"/>
                  <ellipse id="Ellipse_69" data-name="Ellipse 69" cx="7.74" cy="10.044" rx="7.74" ry="10.044" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.539, 14.975)"/>
                  <ellipse id="Ellipse_70" data-name="Ellipse 70" cx="7.654" cy="9.933" rx="7.654" ry="9.933" transform="translate(0.674 14.933) rotate(-69.447)"/>
                  <ellipse id="Ellipse_71" data-name="Ellipse 71" cx="7.568" cy="9.821" rx="7.568" ry="9.821" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.808, 14.893)"/>
                  <ellipse id="Ellipse_72" data-name="Ellipse 72" cx="7.482" cy="9.709" rx="7.482" ry="9.709" transform="translate(0.942 14.852) rotate(-69.445)"/>
                  <ellipse id="Ellipse_73" data-name="Ellipse 73" cx="7.396" cy="9.598" rx="7.396" ry="9.598" transform="translate(1.079 14.81) rotate(-69.447)"/>
                  <ellipse id="Ellipse_74" data-name="Ellipse 74" cx="7.31" cy="9.486" rx="7.31" ry="9.486" transform="translate(1.213 14.769) rotate(-69.447)"/>
                  <ellipse id="Ellipse_75" data-name="Ellipse 75" cx="7.224" cy="9.375" rx="7.224" ry="9.375" transform="translate(1.347 14.728) rotate(-69.446)"/>
                  <ellipse id="Ellipse_76" data-name="Ellipse 76" cx="7.138" cy="9.263" rx="7.138" ry="9.263" transform="translate(1.482 14.686) rotate(-69.447)"/>
                  <ellipse id="Ellipse_77" data-name="Ellipse 77" cx="7.052" cy="9.151" rx="7.052" ry="9.151" transform="translate(1.616 14.645) rotate(-69.446)"/>
                  <ellipse id="Ellipse_78" data-name="Ellipse 78" cx="6.966" cy="9.04" rx="6.966" ry="9.04" transform="translate(1.752 14.605) rotate(-69.447)"/>
                  <ellipse id="Ellipse_79" data-name="Ellipse 79" cx="6.88" cy="8.928" rx="6.88" ry="8.928" transform="translate(1.886 14.561) rotate(-69.447)"/>
                  <ellipse id="Ellipse_80" data-name="Ellipse 80" cx="6.794" cy="8.817" rx="6.794" ry="8.817" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.021, 14.52)"/>
                  <ellipse id="Ellipse_81" data-name="Ellipse 81" cx="6.708" cy="8.705" rx="6.708" ry="8.705" transform="translate(2.155 14.478) rotate(-69.447)"/>
                  <ellipse id="Ellipse_82" data-name="Ellipse 82" cx="6.622" cy="8.593" rx="6.622" ry="8.593" transform="translate(2.289 14.437) rotate(-69.446)"/>
                  <ellipse id="Ellipse_83" data-name="Ellipse 83" cx="6.536" cy="8.482" rx="6.536" ry="8.482" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.426, 14.397)"/>
                  <ellipse id="Ellipse_84" data-name="Ellipse 84" cx="6.45" cy="8.37" rx="6.45" ry="8.37" transform="translate(2.56 14.355) rotate(-69.448)"/>
                  <ellipse id="Ellipse_85" data-name="Ellipse 85" cx="6.364" cy="8.259" rx="6.364" ry="8.259" transform="translate(2.694 14.314) rotate(-69.447)"/>
                  <ellipse id="Ellipse_86" data-name="Ellipse 86" cx="6.278" cy="8.147" rx="6.278" ry="8.147" transform="translate(2.829 14.273) rotate(-69.446)"/>
                  <ellipse id="Ellipse_87" data-name="Ellipse 87" cx="6.192" cy="8.035" rx="6.192" ry="8.035" transform="translate(2.963 14.231) rotate(-69.447)"/>
                  <ellipse id="Ellipse_88" data-name="Ellipse 88" cx="6.106" cy="7.924" rx="6.106" ry="7.924" transform="translate(3.099 14.19) rotate(-69.447)"/>
                  <ellipse id="Ellipse_89" data-name="Ellipse 89" cx="6.02" cy="7.812" rx="6.02" ry="7.812" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.234, 14.15)"/>
                  <ellipse id="Ellipse_90" data-name="Ellipse 90" cx="5.934" cy="7.701" rx="5.934" ry="7.701" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.368, 14.107)"/>
                  <ellipse id="Ellipse_91" data-name="Ellipse 91" cx="5.848" cy="7.589" rx="5.848" ry="7.589" transform="translate(3.502 14.067) rotate(-69.447)"/>
                  <ellipse id="Ellipse_92" data-name="Ellipse 92" cx="5.762" cy="7.477" rx="5.762" ry="7.477" transform="translate(3.637 14.024) rotate(-69.444)"/>
                  <ellipse id="Ellipse_93" data-name="Ellipse 93" cx="5.676" cy="7.366" rx="5.676" ry="7.366" transform="translate(3.773 13.982) rotate(-69.447)"/>
                  <ellipse id="Ellipse_94" data-name="Ellipse 94" cx="5.59" cy="7.254" rx="5.59" ry="7.254" transform="translate(3.907 13.942) rotate(-69.446)"/>
                  <ellipse id="Ellipse_95" data-name="Ellipse 95" cx="5.504" cy="7.143" rx="5.504" ry="7.143" transform="translate(4.042 13.899) rotate(-69.448)"/>
                  <ellipse id="Ellipse_96" data-name="Ellipse 96" cx="5.418" cy="7.031" rx="5.418" ry="7.031" transform="translate(4.176 13.859) rotate(-69.447)"/>
                  <ellipse id="Ellipse_97" data-name="Ellipse 97" cx="5.332" cy="6.919" rx="5.332" ry="6.919" transform="translate(4.31 13.818) rotate(-69.445)"/>
                  <ellipse id="Ellipse_98" data-name="Ellipse 98" cx="5.246" cy="6.808" rx="5.246" ry="6.808" transform="translate(4.446 13.776) rotate(-69.449)"/>
                  <ellipse id="Ellipse_99" data-name="Ellipse 99" cx="5.16" cy="6.696" rx="5.16" ry="6.696" transform="matrix(0.351, -0.936, 0.936, 0.351, 4.581, 13.735)"/>
                  <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="5.074" cy="6.585" rx="5.074" ry="6.585" transform="translate(4.715 13.694) rotate(-69.446)"/>
                  <path id="Path_1545" data-name="Path 1545" d="M163.947,506.695c0-2.827,2.825-4.721,6.309-4.231s6.309,3.178,6.309,6-2.825,4.721-6.309,4.231S163.947,509.522,163.947,506.695Z" transform="translate(-157.594 -496.328)"/>
                  <ellipse id="Ellipse_101" data-name="Ellipse 101" cx="4.902" cy="6.361" rx="4.902" ry="6.361" transform="translate(4.984 13.611) rotate(-69.447)"/>
                  <ellipse id="Ellipse_102" data-name="Ellipse 102" cx="4.816" cy="6.25" rx="4.816" ry="6.25" transform="translate(5.12 13.571) rotate(-69.447)"/>
                  <ellipse id="Ellipse_103" data-name="Ellipse 103" cx="4.73" cy="6.138" rx="4.73" ry="6.138" transform="translate(5.254 13.529) rotate(-69.449)"/>
                  <ellipse id="Ellipse_104" data-name="Ellipse 104" cx="4.644" cy="6.027" rx="4.644" ry="6.027" transform="translate(5.389 13.488) rotate(-69.448)"/>
                  <ellipse id="Ellipse_105" data-name="Ellipse 105" cx="4.558" cy="5.915" rx="4.558" ry="5.915" transform="translate(5.523 13.444) rotate(-69.448)"/>
                  <ellipse id="Ellipse_106" data-name="Ellipse 106" cx="4.472" cy="5.803" rx="4.472" ry="5.803" transform="translate(5.657 13.403) rotate(-69.446)"/>
                  <ellipse id="Ellipse_107" data-name="Ellipse 107" cx="4.386" cy="5.692" rx="4.386" ry="5.692" transform="translate(5.793 13.363) rotate(-69.447)"/>
                  <ellipse id="Ellipse_108" data-name="Ellipse 108" cx="4.3" cy="5.58" rx="4.3" ry="5.58" transform="translate(5.928 13.321) rotate(-69.449)"/>
                  <path id="Path_1546" data-name="Path 1546" d="M182.362,521.18c0-2.388,2.386-3.988,5.33-3.575s5.33,2.685,5.33,5.073-2.386,3.988-5.33,3.575S182.362,523.568,182.362,521.18Z" transform="translate(-175.03 -510.676)"/>
                  <ellipse id="Ellipse_109" data-name="Ellipse 109" cx="4.128" cy="5.357" rx="4.128" ry="5.357" transform="translate(6.197 13.239) rotate(-69.446)"/>
                  <ellipse id="Ellipse_110" data-name="Ellipse 110" cx="4.042" cy="5.245" rx="4.042" ry="5.245" transform="matrix(0.351, -0.936, 0.936, 0.351, 6.331, 13.197)"/>
                  <ellipse id="Ellipse_111" data-name="Ellipse 111" cx="3.956" cy="5.134" rx="3.956" ry="5.134" transform="translate(6.465 13.156) rotate(-69.446)"/>
                  <ellipse id="Ellipse_112" data-name="Ellipse 112" cx="3.87" cy="5.022" rx="3.87" ry="5.022" transform="translate(6.601 13.116) rotate(-69.447)"/>
                  <path id="Path_1547" data-name="Path 1547" d="M192.59,529.234c0-2.144,2.143-3.581,4.786-3.21s4.786,2.411,4.786,4.555-2.143,3.582-4.786,3.21S192.59,531.378,192.59,529.234Z" transform="translate(-184.714 -518.652)"/>
                  <ellipse id="Ellipse_113" data-name="Ellipse 113" cx="3.698" cy="4.799" rx="3.698" ry="4.799" transform="translate(6.87 13.033) rotate(-69.448)"/>
                  <path id="Path_1548" data-name="Path 1548" d="M196.668,532.483c0-2.047,2.045-3.419,4.569-3.064s4.568,2.3,4.568,4.348-2.045,3.419-4.569,3.064S196.668,534.529,196.668,532.483Z" transform="translate(-188.575 -521.869)"/>
                  <ellipse id="Ellipse_114" data-name="Ellipse 114" cx="3.526" cy="4.576" rx="3.526" ry="4.576" transform="translate(7.139 12.95) rotate(-69.448)"/>
                  <path id="Path_1549" data-name="Path 1549" d="M200.778,535.665c0-1.949,1.948-3.256,4.351-2.918s4.351,2.192,4.351,4.141-1.948,3.256-4.351,2.918S200.778,537.615,200.778,535.665Z" transform="translate(-192.466 -525.023)"/>
                  <ellipse id="Ellipse_115" data-name="Ellipse 115" cx="3.354" cy="4.353" rx="3.354" ry="4.353" transform="translate(7.409 12.865) rotate(-69.449)"/>
                  <path id="Path_1550" data-name="Path 1550" d="M204.856,538.88c0-1.852,1.851-3.093,4.134-2.772s4.133,2.082,4.133,3.934-1.851,3.093-4.134,2.772S204.856,540.732,204.856,538.88Z" transform="translate(-196.327 -528.207)"/>
                  <ellipse id="Ellipse_116" data-name="Ellipse 116" cx="3.182" cy="4.129" rx="3.182" ry="4.129" transform="translate(7.678 12.784) rotate(-69.445)"/>
                  <path id="Path_1551" data-name="Path 1551" d="M208.934,542.095c0-1.754,1.753-2.93,3.916-2.626s3.916,1.973,3.916,3.727-1.753,2.93-3.916,2.626S208.934,543.849,208.934,542.095Z" transform="translate(-200.188 -531.392)"/>
                  <ellipse id="Ellipse_117" data-name="Ellipse 117" cx="3.01" cy="3.906" rx="3.01" ry="3.906" transform="translate(7.949 12.701) rotate(-69.449)"/>
                  <ellipse id="Ellipse_118" data-name="Ellipse 118" cx="2.924" cy="3.795" rx="2.924" ry="3.795" transform="translate(8.083 12.66) rotate(-69.447)"/>
                  <path id="Path_1552" data-name="Path 1552" d="M215.084,546.934c0-1.608,1.607-2.686,3.59-2.407s3.589,1.808,3.589,3.416-1.607,2.686-3.59,2.407S215.084,548.542,215.084,546.934Z" transform="translate(-206.011 -536.184)"/>
                  <ellipse id="Ellipse_119" data-name="Ellipse 119" cx="2.752" cy="3.571" rx="2.752" ry="3.571" transform="translate(8.352 12.578) rotate(-69.448)"/>
                  <path id="Path_1553" data-name="Path 1553" d="M219.162,550.182c0-1.511,1.51-2.523,3.372-2.261s3.372,1.7,3.372,3.209-1.51,2.523-3.372,2.261S219.162,551.693,219.162,550.182Z" transform="translate(-209.872 -539.401)"/>
                  <ellipse id="Ellipse_120" data-name="Ellipse 120" cx="2.58" cy="3.348" rx="2.58" ry="3.348" transform="translate(8.622 12.495) rotate(-69.452)"/>
                  <ellipse id="Ellipse_121" data-name="Ellipse 121" cx="2.494" cy="3.237" rx="2.494" ry="3.237" transform="matrix(0.351, -0.936, 0.936, 0.351, 8.757, 12.454)"/>
                  <ellipse id="Ellipse_122" data-name="Ellipse 122" cx="2.408" cy="3.125" rx="2.408" ry="3.125" transform="translate(8.891 12.413) rotate(-69.447)"/>
                  <path id="Path_1554" data-name="Path 1554" d="M227.35,556.611c0-1.316,1.315-2.2,2.937-1.97a3.218,3.218,0,0,1,2.937,2.8c0,1.316-1.315,2.2-2.937,1.97A3.218,3.218,0,0,1,227.35,556.611Z" transform="translate(-217.625 -545.77)"/>
                  <ellipse id="Ellipse_123" data-name="Ellipse 123" cx="2.236" cy="2.902" rx="2.236" ry="2.902" transform="translate(9.16 12.329) rotate(-69.445)"/>
                  <path id="Path_1555" data-name="Path 1555" d="M231.46,559.794c0-1.218,1.218-2.035,2.72-1.824a2.98,2.98,0,0,1,2.719,2.588c0,1.218-1.218,2.035-2.72,1.824A2.98,2.98,0,0,1,231.46,559.794Z" transform="translate(-221.516 -548.924)"/>
                  <ellipse id="Ellipse_124" data-name="Ellipse 124" cx="2.064" cy="2.679" rx="2.064" ry="2.679" transform="translate(9.43 12.246) rotate(-69.45)"/>
                  <ellipse id="Ellipse_125" data-name="Ellipse 125" cx="1.978" cy="2.567" rx="1.978" ry="2.567" transform="matrix(0.351, -0.936, 0.936, 0.351, 9.564, 12.205)"/>
                  <path id="Path_1556" data-name="Path 1556" d="M237.578,564.633c0-1.072,1.071-1.791,2.393-1.6a2.622,2.622,0,0,1,2.393,2.277c0,1.072-1.072,1.791-2.393,1.605A2.622,2.622,0,0,1,237.578,564.633Z" transform="translate(-227.309 -553.716)"/>
                  <path id="Path_1557" data-name="Path 1557" d="M239.617,566.258c0-1.023,1.023-1.709,2.284-1.532a2.5,2.5,0,0,1,2.284,2.174c0,1.023-1.023,1.709-2.284,1.532A2.5,2.5,0,0,1,239.617,566.258Z" transform="translate(-229.239 -555.325)"/>
                  <path id="Path_1558" data-name="Path 1558" d="M241.688,567.882c0-.975.974-1.628,2.176-1.459a2.384,2.384,0,0,1,2.175,2.071c0,.975-.974,1.628-2.176,1.459A2.384,2.384,0,0,1,241.688,567.882Z" transform="translate(-231.2 -556.933)"/>
                  <path id="Path_1559" data-name="Path 1559" d="M243.727,569.472c0-.926.925-1.547,2.067-1.386a2.264,2.264,0,0,1,2.067,1.967c0,.926-.925,1.546-2.067,1.386A2.264,2.264,0,0,1,243.727,569.472Z" transform="translate(-233.131 -558.509)"/>
                  <ellipse id="Ellipse_126" data-name="Ellipse 126" cx="1.548" cy="2.009" rx="1.548" ry="2.009" transform="translate(10.238 11.999) rotate(-69.451)"/>
                  <path id="Path_1560" data-name="Path 1560" d="M247.8,572.721c0-.828.828-1.383,1.849-1.24a2.026,2.026,0,0,1,1.849,1.76c0,.828-.828,1.384-1.849,1.24A2.026,2.026,0,0,1,247.8,572.721Z" transform="translate(-236.992 -561.726)"/>
                  <ellipse id="Ellipse_127" data-name="Ellipse 127" cx="1.376" cy="1.786" rx="1.376" ry="1.786" transform="translate(10.507 11.916) rotate(-69.453)"/>
                  <path id="Path_1561" data-name="Path 1561" d="M251.915,575.936c0-.731.731-1.221,1.632-1.094a1.788,1.788,0,0,1,1.632,1.553c0,.731-.731,1.221-1.632,1.094A1.788,1.788,0,0,1,251.915,575.936Z" transform="translate(-240.883 -564.91)"/>
                  <path id="Path_1562" data-name="Path 1562" d="M253.954,577.56c0-.682.682-1.139,1.523-1.021A1.669,1.669,0,0,1,257,577.988c0,.682-.682,1.14-1.523,1.021A1.669,1.669,0,0,1,253.954,577.56Z" transform="translate(-242.814 -566.518)"/>
                  <path id="Path_1563" data-name="Path 1563" d="M255.993,579.118c0-.634.633-1.058,1.414-.948a1.55,1.55,0,0,1,1.414,1.346c0,.633-.633,1.058-1.414.948A1.549,1.549,0,0,1,255.993,579.118Z" transform="translate(-244.744 -568.063)"/>
                  <path id="Path_1564" data-name="Path 1564" d="M258.033,580.743c0-.585.585-.977,1.305-.875a1.43,1.43,0,0,1,1.305,1.242c0,.585-.585.977-1.305.875A1.43,1.43,0,0,1,258.033,580.743Z" transform="translate(-246.676 -569.673)"/>
                  <path id="Path_1565" data-name="Path 1565" d="M260.072,582.332c0-.536.536-.9,1.2-.8a1.311,1.311,0,0,1,1.2,1.139c0,.536-.536.9-1.2.8A1.311,1.311,0,0,1,260.072,582.332Z" transform="translate(-248.606 -571.248)"/>
                  <path id="Path_1566" data-name="Path 1566" d="M262.143,583.957c0-.487.487-.814,1.088-.729a1.192,1.192,0,0,1,1.088,1.035c0,.487-.487.814-1.088.729A1.192,1.192,0,0,1,262.143,583.957Z" transform="translate(-250.567 -572.857)"/>
                  <path id="Path_1567" data-name="Path 1567" d="M264.182,585.582c0-.439.438-.733.979-.656a1.073,1.073,0,0,1,.979.932c0,.439-.438.732-.979.656A1.073,1.073,0,0,1,264.182,585.582Z" transform="translate(-252.498 -574.465)"/>
                  <path id="Path_1568" data-name="Path 1568" d="M266.221,587.172c0-.39.39-.651.87-.583a.953.953,0,0,1,.87.828c0,.39-.39.651-.87.584A.953.953,0,0,1,266.221,587.172Z" transform="translate(-254.429 -576.041)"/>
                  <path id="Path_1569" data-name="Path 1569" d="M268.26,588.8c0-.341.341-.57.762-.51a.834.834,0,0,1,.761.725c0,.341-.341.57-.762.511A.834.834,0,0,1,268.26,588.8Z" transform="translate(-256.359 -577.65)"/>
                  <path id="Path_1570" data-name="Path 1570" d="M270.3,590.42c0-.292.292-.488.653-.438a.715.715,0,0,1,.653.621c0,.292-.292.488-.653.438A.715.715,0,0,1,270.3,590.42Z" transform="translate(-258.29 -579.257)"/>
                  <path id="Path_1571" data-name="Path 1571" d="M272.37,592.011c0-.244.244-.407.544-.365a.6.6,0,0,1,.544.518c0,.244-.244.407-.544.365A.6.6,0,0,1,272.37,592.011Z" transform="translate(-260.25 -580.834)"/>
                  <path id="Path_1572" data-name="Path 1572" d="M274.41,593.635c0-.195.195-.326.435-.292a.477.477,0,0,1,.435.414c0,.195-.195.326-.435.292A.477.477,0,0,1,274.41,593.635Z" transform="translate(-262.182 -582.442)"/>
                  <path id="Path_1573" data-name="Path 1573" d="M276.449,595.26c0-.146.146-.244.327-.219a.358.358,0,0,1,.326.311c0,.146-.146.244-.327.219A.357.357,0,0,1,276.449,595.26Z" transform="translate(-264.113 -584.051)"/>
                  <path id="Path_1574" data-name="Path 1574" d="M278.488,596.85c0-.1.1-.163.218-.146a.238.238,0,0,1,.217.207c0,.1-.1.163-.218.146A.238.238,0,0,1,278.488,596.85Z" transform="translate(-266.043 -585.626)"/>
                  <path id="Path_1575" data-name="Path 1575" d="M280.527,598.475c0-.049.049-.081.109-.073a.119.119,0,0,1,.109.1c0,.049-.049.081-.109.073A.119.119,0,0,1,280.527,598.475Z" transform="translate(-267.974 -587.235)"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_6644" data-name="Group 6644" transform="translate(-1312.039 1787.745)">
      <path id="Path_1578" data-name="Path 1578" d="M525.409,661.672a3.389,3.389,0,0,0,1.061.629c.608.255,1,.458,1.16.513a.718.718,0,0,1,.453.43,2,2,0,0,1,.234.907,14.477,14.477,0,0,0-.155,1.811,2.321,2.321,0,0,1-.119.927,6.844,6.844,0,0,0-.373,1.063c0,.139-.036.2-.254.493s-.238.47-.492.135a1.242,1.242,0,0,1-.274-.963c.056-.259.27-.235.429-.294a.412.412,0,0,0,.2-.533,1.1,1.1,0,0,1,.059-.649c.04-.159.135-1.441.135-1.755a.832.832,0,0,0-.175-.609,1.258,1.258,0,0,0-.707-.294c-.2,0-.886-.06-1.2-.06s-.985-.119-1.259-.119a2.086,2.086,0,0,1-.921-.255c-.373-.179-.254-.1.079-.275S525.409,661.672,525.409,661.672Z" transform="translate(-522.978 -661.672)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6645" data-name="Group 6645" transform="translate(-1311.271 1778.709)">
      <path id="Path_1579" data-name="Path 1579" d="M541.215,492a.563.563,0,0,0-.326-.174c-.162-.047-.444-.071-.385,0s.056.186-.129.186-.371-.023-.371.059a.363.363,0,0,1-.258.315c-.174.035-.538.224-.338.235s-.138.127-.315.127-.3.008-.447.008-.916-.136-.775-.065.072.152-.057.117-.394-.047-.232.024.146.187.028.234-.255-.059-.14,0,.4.236.233.271-.351.1-.174.172.232.245.291.327.108.177.258.106,1.9-.74,2.122-.855a10.993,10.993,0,0,0,.944-1C541.2,492.062,541.306,492.107,541.215,492Z" transform="translate(-537.426 -491.785)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6646" data-name="Group 6646" transform="translate(-1317.342 1781.385)">
      <path id="Path_1580" data-name="Path 1580" d="M423.952,542.134a5.146,5.146,0,0,0-.549-.044c-.109-.029-.174.038-.094.133a1.29,1.29,0,0,0,.512.359c.206.059,1.273.242,1.391.242s.938,0,1.355-.029.3-.448.3-.448S424.187,542.193,423.952,542.134Z" transform="translate(-423.273 -542.083)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6647" data-name="Group 6647" transform="translate(-1317.219 1778.644)">
      <path id="Path_1581" data-name="Path 1581" d="M426.738,490.566c.073.044.142.357.34.533s.438.315.453.418.124.095.329.168,1.531.448,1.816.565.826.324.9.507a.885.885,0,0,1-.168.843c-.235.221-.432.17-.52.265a1.8,1.8,0,0,1-.432.227l-.618-.3s-.2-.165-.949-.151a2.636,2.636,0,0,1-1.632-.458c-.323-.2-.7-.469-.673-.946a1.439,1.439,0,0,1,.376-1.016c.256-.28.318-.348.45-.495S426.665,490.521,426.738,490.566Z" transform="translate(-425.583 -490.551)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6648" data-name="Group 6648" transform="translate(-1316.411 1777.767)">
      <path id="Path_1582" data-name="Path 1582" d="M445.831,474.127a1.7,1.7,0,0,1,.4.969c.047.551.671.4.462.449s-.859.057-.824.137a1.415,1.415,0,0,1,.023.572c-.035.209-.174.259.035.433s.209.28.315.233a6.42,6.42,0,0,0,.711-.513c.185-.153.294-.292.456-.4s.138-.115.268-.012a1.127,1.127,0,0,1,.232.351c.059.091.132.08-.079.256s-.35.412-.514.574a9.13,9.13,0,0,1-.817.793c-.15.082-.406.341-.57.282a1.886,1.886,0,0,1-.55-.551c-.07-.118-.232-.454-.268-.536a3.166,3.166,0,0,1-.211-.386c-.044-.141-.115-.153-.256-.13a1.242,1.242,0,0,1-.432.035c-.185-.023-2.209-.344-2.374-.414a.893.893,0,0,1-.354-.365,4.8,4.8,0,0,0-.66-.6c-.129-.094.061-.272.307-.449a6.551,6.551,0,0,1,1.752-.678,9.934,9.934,0,0,1,1.517-.118c.279.012.9.012,1.1.035S445.784,474.092,445.831,474.127Z" transform="translate(-440.778 -474.066)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6649" data-name="Group 6649" transform="translate(-1316.707 1781.526)">
      <path id="Path_1583" data-name="Path 1583" d="M438.336,544.733s-.394.141-.717.279-.835.506-1.238.748a1.733,1.733,0,0,0-.594.528c-.08.1-.206.212-.359.374a.9.9,0,0,0-.212.477c0,.059.094.082.226.147s.344.227.4.183.111.015.25.073.438.359.57.374.6.029.7.029.08-.183-.088-.242a.625.625,0,0,1-.409-.338,1.021,1.021,0,0,1-.117-.542c.029-.132.206-.212.373-.315s.8-.374,1.017-.492a5.748,5.748,0,0,1,.629-.271c.117-.044-.021-.309-.182-.61A2.218,2.218,0,0,0,438.336,544.733Z" transform="translate(-435.217 -544.733)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6650" data-name="Group 6650" transform="translate(-1332.343 1782.697)">
      <path id="Path_1584" data-name="Path 1584" d="M149.379,566.762a2.769,2.769,0,0,0-1.235.814,7.012,7.012,0,0,1-1.411,1.254,6.223,6.223,0,0,1-1.379.81,2.131,2.131,0,0,1-.73-.111,1.13,1.13,0,0,1-.455-.434c-.033-.088-.012.111.088.22a1.153,1.153,0,0,0,.506.319c.175.034.319.089.4.089s-.209.1-.319.1a2.107,2.107,0,0,1-.67-.407c-.121-.121-.1-.033.011.077a1.472,1.472,0,0,0,.242.2,1.752,1.752,0,0,1-.462-.22,3.145,3.145,0,0,0-.691-.286,6.139,6.139,0,0,0-.747-.11c-.132,0-.658.022-.758.044s.1.055.263.033a3.281,3.281,0,0,1,.582-.011c.23.011.341.165.253.121s-.571-.11-.362-.033.275.111.11.154a1.222,1.222,0,0,0-.362.165.52.52,0,0,1-.384.044c-.143-.022-.012.067.142.11a1.229,1.229,0,0,1,.362.176,3.917,3.917,0,0,0,1.01.352c.219.011.208.066.428.066s.187.076-.11.088a2.646,2.646,0,0,1-.846-.077,3.057,3.057,0,0,0-.549-.165c-.131-.022-.153-.056.231.121s-.11.033-.275-.022a1.737,1.737,0,0,0-.494-.088c-.132,0,.1.033.252.066s.715.308.9.374a1.589,1.589,0,0,0,.362.066c.164.033-.44,0-.6-.055s-.636-.286-.812-.319a2.573,2.573,0,0,0-.3-.033c-.077-.011.11.066.264.088a2.888,2.888,0,0,1,.614.253,1.5,1.5,0,0,0,.451.165,1.9,1.9,0,0,1-.506-.033c-.164-.044-.484-.176-.659-.242s-.351-.033-.11.033a8.485,8.485,0,0,0,.989.319c.175,0,.46.022.23.11a1.756,1.756,0,0,1-.78-.033,2.49,2.49,0,0,1-.373-.231c-.121-.066-.132-.043.044.078a1.863,1.863,0,0,0,.791.285s.242-.023.066.033a3.682,3.682,0,0,0-.857.374,3.2,3.2,0,0,0-.472.441c-.066.11.067,0,.209-.132a3.151,3.151,0,0,1,.538-.4c.132-.066.385-.143.495-.187s.362-.077-.034.11a4.67,4.67,0,0,0-.933.572c.2-.088.549-.319.692-.363a2.6,2.6,0,0,1,.538-.187c.143,0,.307.121.439.121a5.147,5.147,0,0,0,1.054.022,2.928,2.928,0,0,0,.945-.429c.153-.11.208-.034.055.1a2.325,2.325,0,0,1-.549.352,1.175,1.175,0,0,1-.549.078c-.242-.011-.659-.077-.4.022a1.281,1.281,0,0,0,.483.1c.066,0-.187.11-.34.143a10.821,10.821,0,0,0-1.12.264,1.242,1.242,0,0,0-.362.187c-.153.11-.034.122.066.044a.948.948,0,0,1,.4-.187c.186-.044.857-.143,1.153-.165a4.748,4.748,0,0,0,1.032-.308,2.219,2.219,0,0,0,.648-.33c.121-.121.186-.132.121-.011s-.251.209-.34.286.186,0,.263-.088.242-.231.319-.341.439-.408.34-.187a2.483,2.483,0,0,1-.637.638,2.331,2.331,0,0,1-.658.3,1.726,1.726,0,0,1-.407.055c-.164.011.142.055.286.022a2.732,2.732,0,0,0,.7-.231c.231-.132.406-.253.406-.253a2.178,2.178,0,0,1-.319.307,1.548,1.548,0,0,1-.45.22c-.219.055-.034.088.132.022a1.178,1.178,0,0,0,.484-.242,3.606,3.606,0,0,0,.406-.385c.066-.11.209-.428.34-.616a2.858,2.858,0,0,1,.439-.572.85.85,0,0,1,.308-.154c.055,0,.023.231-.1.407a5.023,5.023,0,0,1-.517.715,2.066,2.066,0,0,1-.373.253c-.132.078-.274.176-.164.154a.992.992,0,0,0,.373-.122c.11-.077.043.056-.077.166s-.318.274-.164.208.208-.021.406-.253.429-.649.582-.814.34-.474.439-.551.506-.1.637-.176a3.519,3.519,0,0,0,.5-.407c.055-.067-.11-1.155-.011-1.529S149.379,566.762,149.379,566.762Z" transform="translate(-141.241 -566.762)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6651" data-name="Group 6651" transform="translate(-1326.998 1785.611)">
      <path id="Path_1588" data-name="Path 1588" d="M242.45,621.648a2.955,2.955,0,0,0-.472.583c-.121.2-.164.286-.219.374s0,.023.208-.231a6.841,6.841,0,0,1,.5-.605C242.637,621.615,242.669,621.428,242.45,621.648Z" transform="translate(-241.738 -621.548)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6652" data-name="Group 6652" transform="translate(-1325.911 1785.829)">
      <path id="Path_1589" data-name="Path 1589" d="M262.755,625.709c-.209.143-.708.307-.549.264a1.154,1.154,0,0,0,.484-.154C262.964,625.665,262.964,625.566,262.755,625.709Z" transform="translate(-262.175 -625.64)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6653" data-name="Group 6653" transform="translate(-1319.849 1786.602)">
      <path id="Path_1590" data-name="Path 1590" d="M377.492,641.068c0,.4.133,2.094.133,2.589a3.06,3.06,0,0,0,.644,1.617c.253.223,2.415,1.918,2.716,2.051a2.331,2.331,0,0,1,1.09.718c.331.447.193.374-.313.374a3.945,3.945,0,0,1-.877-.023c-.077-.045-.181-.176-.142-.278a.2.2,0,0,0-.058-.226,2.8,2.8,0,0,0-.238-.174c-.045-.026-4.03-3.2-4.225-3.34a13.624,13.624,0,0,1,.04-2.933c.02-.629.04-1.079.04-1.2s.016-.123.234.1A11.023,11.023,0,0,0,377.492,641.068Z" transform="translate(-376.141 -640.165)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6654" data-name="Group 6654" transform="translate(-1309.854 1781.882)">
      <path id="Path_1591" data-name="Path 1591" d="M570.255,552.472s-2.016-.31-2.359-.368-2.315-.4-2.659-.444-.577-.117-.852-.166-.314-.059-.314-.059l-.01.137s.1.036.392.127,1.02.178,1.276.237,1.178.227,1.551.286,1.587.2,1.755.246.767.146.864.156.345.024.345.024Z" transform="translate(-564.061 -551.435)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6655" data-name="Group 6655" transform="translate(-1311.425 1777.811)">
      <path id="Path_1592" data-name="Path 1592" d="M536.485,474.966a.388.388,0,0,1-.083.285c-.057.037-.057.06,0,.19s.077.191.1.247-.005.112-.1.1-.145-.01-.148.07,0,.117-.041.125-.067.031-.038.068-.018.054-.043.094,0,.126,0,.157a.118.118,0,0,1-.131.095c-.081-.01-.28-.04-.344-.04a4.041,4.041,0,0,0-.409-.007c-.058.013-.281-.3-.424-.434a1.669,1.669,0,0,1-.252-.537c-.151-.313.039-.2.178-.292a.554.554,0,0,1,.476-.11c.207.05.529-.087.725-.088A2.875,2.875,0,0,1,536.485,474.966Z" transform="translate(-534.519 -474.885)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6656" data-name="Group 6656" transform="translate(-1311.773 1776.481)">
      <path id="Path_1593" data-name="Path 1593" d="M530.177,450.333a1.342,1.342,0,0,0-.928-.44,1.3,1.3,0,0,0-.654.184,1.041,1.041,0,0,0-.425.554c-.089.253-.026.315-.114.5s-.113.166.052.271.219.2.322.111a.8.8,0,0,1,.452-.18.728.728,0,0,0,.407.025,2.7,2.7,0,0,1,.738-.019c.124.018.369.023.4.034a2.1,2.1,0,0,0-.039-.582A.961.961,0,0,0,530.177,450.333Z" transform="translate(-527.988 -449.893)" fill-rule="evenodd"/>
    </g>
    <g id="Group_6657" data-name="Group 6657" transform="translate(-1309.98 1777.123)">
      <path id="Path_1594" data-name="Path 1594" d="M562.367,462a.711.711,0,0,1,.082.418c-.032.139-.067.131-.154.119a2.274,2.274,0,0,1-.473-.09c-.094-.048-.19-.185-.075-.292a.732.732,0,0,1,.392-.187C562.262,461.956,562.327,461.944,562.367,462Z" transform="translate(-561.695 -461.958)" fill-rule="evenodd"/>
    </g>
  </g>
</svg>
