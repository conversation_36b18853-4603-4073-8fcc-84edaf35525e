import React, { Component } from "react";
import {
  Button,
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Box,
  Breadcrumbs,
  Modal,
} from "@mui/material";
import { Link } from "react-router-dom";
import moment from "moment";
import { Loader } from "../../../library/common/components";
import { MdKeyboardBackspace } from "react-icons/md";
// import ShowModal from "../../../components/common/ShowModal/ShowModal";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import ButtonComponent from "../../../library/common/components/Button";
import axiosInstance from "../../../helpers/Axios";

const StatusOptions = [
  { label: "open", value: "open" },
  { label: "completed", value: "completed" },
  { label: "inprogress", value: "inprogress" },
];

export class ErrorDetailsPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      searchVal: "",
      meetingId: "",
      meetingStatusModal: false,
      ismeetingDeleteModal: false,
      meetingData: {},
      seletedValue: "",
      errormettingData: [],
    };
  }
  componentDidMount = () => {
    this.fetchAllErrorsLogs();
  };

  fetchAllErrorsLogs = async () => {
    this.setState({ isLoading: true });
    let { selectedDate } = this.state;

    let date =
      selectedDate === null
        ? moment().format("YYYY-MM-DD")
        : moment(selectedDate).format("YYYY-MM-DD");

    this.setState({ eventLoader: true });
    let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    try {
      const { status, data } = await axiosInstance.get(
        // `/adminNotification/error?date=${date}`
        `/adminNotification/reports?&trackOnly=1&timezone=${timezone}&limit=${this.props?.history?.location?.state?.rowPerPage}&offset=${this.props?.history?.location?.state?.offset}`
      );

      if (status === 200) {
        const reportMeetingsData = data?.errors?.rows?.map((item) => {
          let datas = {
            ...item,
            ReportMeetings: item?.ReportMeetings?.map((obj) => {
              let errorData = {
                ...obj,
                errors:
                  typeof JSON.parse(obj?.errors) == "object"
                    ? Array.isArray(JSON.parse(obj?.errors))
                      ? JSON.parse(obj?.errors)
                      : [JSON.parse(obj?.errors)]
                    : [{ message: JSON.parse(obj?.errors) }],
              };

              return errorData;
            }),
          };
          return datas;
        });

        let newErrorData = reportMeetingsData
          ?.map((obj) => {
            if (obj?.id == this.props?.match?.params?.id) {
              return obj;
            }
          })
          .filter((x) => x !== undefined);

        this.setState({
          errormettingData: newErrorData,
          isLoading: false,
        });
      }
    } catch (error) {
      this.setState({ eventLoader: false });
    }
  };

  backToNavigatePage = () => {
    this.props.navigate(
      `/racing/errorLogs?errorTab=${this.props?.location?.state?.errorType}&selectType=${this.props?.location?.state?.selectType}`
    );
  };
  handleErrorDelateMeetingModal = () => {
    this.setState({ ismeetingDeleteModal: true });
  };
  togglehandleErrorDelateMeetingModal = () => {
    this.setState({ ismeetingDeleteModal: false });
  };
  hadleMeetingStaus = (data) => {
    this.setState({
      meetingStatusModal: true,
      meetingData: data,
      seletedValue: data?.meetingStatus,
      meetingId: data?.id,
    });
  };
  togglehadleMeetingStaus = () => {
    this.setState({ meetingStatusModal: false });
  };
  errorSearch = () => {
    const errLogDetailsData = this.state?.errormettingData.map((data) => {
      let datas = {
        ...data,
        ReportMeetings: data?.ReportMeetings?.filter((item) => {
          if (this.state?.searchVal === "") return item;
          else if (
            item?.name
              .toString()
              .toLowerCase()

              .includes(
                this.state?.searchVal.toString().toLowerCase().trim()
              ) ||
            item?.errors?.[0]?.message
              .toString()
              .toLowerCase()
              .includes(this.state?.searchVal.toString().toLowerCase().trim())
          ) {
            return item;
          }
        }),
      };
      return datas;
    });
    return errLogDetailsData;
  };

  handleSave = async () => {
    let payload = {
      meetingStatus: this.state?.seletedValue,
    };

    this.setState({ isLoading: true });
    const { status } = await axiosInstance.put(
      `adminNotification/reports/update/${this.state?.meetingId}`,
      payload
    );
    if (status === 200) {
      this.setState({ isLoading: false, meetingStatusModal: false });
      this.fetchAllErrorsLogs();
      // this.props.navigate(`/racing/errorLogs`);
    }
  };
  render() {
    const {
      isLoading,
      searchVal,
      // ismeetingDeleteModal,
      meetingStatusModal,
      meetingData,
      seletedValue,
      // meetingId,
      errormettingData,
    } = this.state;
    let errLogDetailsDatas = this.errorSearch();
    // const errorDetailsData = this.props?.history?.location?.state?.detail;
    const newData = searchVal ? errLogDetailsDatas : errormettingData;
    return (
      <>
        <Grid container className="page-content adminLogin sports_details">
          <Grid item xs={12} className="pageWrapper">
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  racing
                </Link>
                <Link underline="hover" color="inherit" to="/racing/errorLogs">
                  Error Logs
                </Link>
                <Typography className="active_p">Error Details</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={6}>
                <Button
                  className="admin-btn-back"
                  onClick={this.backToNavigatePage}
                >
                  <MdKeyboardBackspace />
                </Button>
                <Typography variant="h1" align="left">
                  Error Logs Details
                </Typography>
              </Grid>
              <Grid
                item
                xs={6}
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                }}
              >
                <input
                  type="text"
                  className="error-search"
                  placeholder="search"
                  value={searchVal}
                  onChange={(e) => {
                    this.setState({ searchVal: e.target.value });
                  }}
                  style={{
                    fontSize: "16px",
                    borderRadius: "3px",
                    minHeight: "40px",
                    border: "1px solid #ddd",
                    paddingLeft: "10px",
                    marginTop: "4px",
                    color: "#000000",
                    width: "60%",
                  }}
                />
              </Grid>
            </Grid>
            <Box>
              {isLoading && <Loader />}
              {!isLoading && newData.length === 0 && <p>No Data Available</p>}
              {!isLoading && newData.length > 0 && (
                <>
                  <TableContainer component={Paper}>
                    <Table
                      className="listTable api-provider-listTable"
                      aria-label="simple table"
                    >
                      <TableHead>
                        <TableRow className="tableHead-row">
                          <TableCell>ProviderId</TableCell>
                          <TableCell>Meeting Name</TableCell>

                          <TableCell>Description</TableCell>
                          <TableCell>Error date</TableCell>
                          <TableCell style={{ minWidth: "100px" }}>
                            User Name
                          </TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Update</TableCell>
                          {/* <TableCell>Action</TableCell> */}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {newData?.map((obj) => {
                          return (
                            <>
                              {obj?.ReportMeetings?.length === 0 ? (
                                <TableRow className="table_row">
                                  <TableCell colSpan={100}>
                                    <Box className="message">
                                      No Error Data Avilable
                                    </Box>
                                  </TableCell>
                                </TableRow>
                              ) : (
                                obj?.ReportMeetings?.map((item) => {
                                  return (
                                    <>
                                      {item?.errors?.map((data) => {
                                        return (
                                          <>
                                            <TableRow className="listTable-Row">
                                              <TableCell>
                                                {obj?.providerId}
                                              </TableCell>
                                              <TableCell className="pre-whitespace">
                                                {item?.name}
                                              </TableCell>
                                              <TableCell className="pre-whitespace">
                                                {data?.message}
                                              </TableCell>
                                              <TableCell
                                                style={{ minWidth: "185px" }}
                                              >
                                                {" "}
                                                {moment(item.createdAt).format(
                                                  "DD/MM/YYYY h:mm:ss a"
                                                )}
                                              </TableCell>
                                              <TableCell>
                                                {item?.User?.username}
                                              </TableCell>
                                              <TableCell>
                                                <Button
                                                  variant="contained"
                                                  color="primary"
                                                >
                                                  {item?.meetingStatus}
                                                </Button>
                                              </TableCell>
                                              <TableCell>
                                                <Button
                                                  className="table-btn edit-btn c-pointer"
                                                  onClick={() => {
                                                    this.hadleMeetingStaus(
                                                      item
                                                    );
                                                  }}
                                                  // style={{ cursor: "pointer" }}
                                                >
                                                  Edit
                                                </Button>
                                              </TableCell>
                                              {/* <TableCell>
                                                <Button
                                                  style={{ cursor: "pointer" }}
                                                  onClick={() =>
                                                    this.handleErrorDelateMeetingModal()
                                                  }
                                                  className="table-btn delete-btn"
                                                >
                                                  Delete
                                                </Button>
                                              </TableCell> */}
                                            </TableRow>{" "}
                                          </>
                                        );
                                      })}
                                    </>
                                  );
                                })
                              )}
                            </>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}
            </Box>
          </Grid>
        </Grid>
        <Modal
          className="modal modal-input"
          open={meetingStatusModal}
          onClose={this.togglehadleMeetingStaus}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">
              {meetingData?.name} Update Status
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.togglehadleMeetingStaus}
            />
            <Box>
              <Grid item xs={12}>
                <Box className="select-box">
                  <label className="modal-label">Status</label>
                  <Select
                    className="React "
                    classNamePrefix="select"
                    menuPosition="fixed"
                    value={StatusOptions?.find((op) => {
                      return op?.value === seletedValue;
                    })}
                    onChange={(e) =>
                      this.setState({
                        seletedValue: e?.value,
                      })
                    }
                    options={StatusOptions && StatusOptions}
                  />
                </Box>
              </Grid>
              <Grid container>
                <Grid item xs={3}>
                  <div style={{ marginTop: "20px", display: "flex" }}>
                    <ButtonComponent
                      className="mt-3 purple"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Updating..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />

                    <Button
                      className="mr-lr-30 outlined"
                      variant="outlined"
                      // color="primary"
                      onClick={this.togglehadleMeetingStaus}
                      style={{ minWidth: "auto" }}
                    >
                      Back
                    </Button>
                  </div>
                </Grid>
              </Grid>
            </Box>
          </div>
        </Modal>
        {/* <ShowModal
          isModalOpen={ismeetingDeleteModal}
          onClose={this.togglehandleErrorDelateMeetingModal}
          Content="Are you sure you want to delete?"
          onOkayLabel="Yes"
          // onOkay={() => {
          //   this.handleErrorDelateReport();
          // }}
          onCancel={this.togglehandleErrorDelateMeetingModal}
        /> */}
      </>
    );
  }
}

export default ErrorDetailsPage;
