import { Validators } from "../../../library/utilities/Validators";

export const countryFormModel = [
  {
    label: "Country*",
    value: "",
    type: "text",
    placeholder: "Country",
    field: "country",
    validators: [{ check: Validators.required }],
    required: true,
    className: "12",
  },
  {
    label: "Country Code*",
    value: "",
    type: "text",
    placeholder: "Country Code",
    field: "countryCode",
    validators: [{ check: Validators.required }],
    required: true,
    className: "12",
  },
  {
    label: "Phone Code",
    value: "",
    type: "number",
    placeholder: "Phone Code",
    field: "phoneCode",
    required: false,
    className: "12",
  },
  // {
  //   label: "Variation",
  //   value: "",
  //   type: "text",
  //   placeholder: "Variation",
  //   field: "variation",
  //   required: false,
  //   className: "12",
  // },
  {
    label: "Update Required*",
    value: "",
    type: "dropdown",
    placeholder: "Update Required",
    field: "updateRequired",
    validators: [{ check: Validators.required }],
    required: true,
    className: "12",
    options: [
      { id: 1, value: "1", label: "Yes" },
      { id: 2, value: "0", label: "No" },
    ],
  },
];
