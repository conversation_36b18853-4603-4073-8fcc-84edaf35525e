import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Button,
  Breadcrumbs,
  Typography,
  Box,
} from "@mui/material";
import { MdKeyboardBackspace } from "react-icons/md";
import { <PERSON> } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import ButtonComponent from "../../../library/common/components/Button";
import axiosInstance from "../../../helpers/Axios";
import { URLS } from "../../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import CreateApiProviderApisFeildMapping from "./CreateApiProviderApisFeildMapping";
import Pagination from "@mui/material/Pagination";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";

class ApiProviderApisFeildMapping extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentProviderData: {},
      currentApiProviderUpdateData: {},
      apisFeildMapping: [],
      allApiProvider: [],
      allSportsType: [],
      allKeyIdentifire: [],
      dbtable: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
    };
  }

  componentDidMount() {
    this.fetchAllFieldMapping();
    this.fetchAllDBTable();
  }

  async fetchAllDBTable() {
    const { status, data } = await axiosInstance.get(URLS.dbtable);
    if (status === 200) {
      this.setState({ dbtable: data.tables });
    }
  }

  async fetchAllFieldMapping() {
    let id = this.props.match.params.id;
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.apiFieldMappingByProviderUpdateId(id)
    );
    if (status === 200) {
      this.setState({ apisFeildMapping: data.result, isLoading: false });
      this.fetchAllApiProvider();
      this.fetchAllSportType();
      this.fetchAllKeyIdentifire();
    }
  }

  async fetchAllApiProvider() {
    const { providerid, id } = this.props.match.params;
    await axiosInstance.get(`${URLS.apiProvider}/${providerid}`).then((res) => {
      this.setState({ currentProviderData: res?.data?.result[0] });
    });
    await axiosInstance.get(`${URLS.apiProviderUpdate}/${id}`).then((res) => {
      this.setState({ currentApiProviderUpdateData: res?.data?.result[0] });
    });

    const { status, data } = await axiosInstance.get(URLS.apiProviderUpdate);
    if (status === 200) {
      this.setState({ allApiProvider: data.result });
    }
  }

  getApiProvider = async (id) => {
    let { allApiProvider } = this.state;
    let apiProvider = allApiProvider
      .filter((obj) => obj.id === id)
      .map((object) => object.tableNameToUpdate);
    return apiProvider;
  };

  async fetchAllSportType() {
    const { status, data } = await axiosInstance.get(URLS.sportType);
    if (status === 200) {
      this.setState({ allSportsType: data.result });
    }
  }

  getSportType = (id) => {
    let { allSportsType } = this.state;
    let sportType = allSportsType
      .filter((obj) => obj.id === id)
      .map((object) => object.sportType);
    return sportType;
  };

  async fetchAllKeyIdentifire() {
    const { status, data } = await axiosInstance.get(URLS.apiKeyIdentifire);
    if (status === 200) {
      this.setState({ allKeyIdentifire: data.result });
    }
  }

  getKey = (id) => {
    let { allKeyIdentifire } = this.state;
    let key = allKeyIdentifire
      .filter((obj) => obj.id === id)
      .map((object) => object.key);
    return key;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllFieldMapping();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.apiFieldMapping}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllFieldMapping();
        });
        this.setActionMessage(
          true,
          "Success",
          "Api provider Api Feild Mapping Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, apisFeildMapping } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < apisFeildMapping.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  backToNavigatePage = () => {
    const { providerid } = this.props.match.params;
    this.props.navigate(`/apiprovider/apiproviderupdate/${providerid}`);
  };

  render() {
    var {
      apisFeildMapping,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      currentProviderData,
      currentApiProviderUpdateData,
    } = this.state;
    const pageNumbers = [];
    let currentPageRow = apisFeildMapping;
    const { providerid } = this.props.match.params;
    if (apisFeildMapping?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = apisFeildMapping.slice(
        indexOfFirstTodo,
        indexOfLastTodo
      );

      for (
        let i = 1;
        i <= Math.ceil(apisFeildMapping.length / rowPerPage);
        i++
      ) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit"></Link>
                <Link underline="hover" color="inherit" to="/apiprovider">
                  Api Provider
                </Link>
                <Link
                  underline="hover"
                  color="inherit"
                  to={`/apiprovider/apiproviderupdate/${providerid}`}
                >
                  Api Provider Update
                </Link>
                <Typography className="active_p">
                  Api Provider Apis Field Mapping
                </Typography>
              </Breadcrumbs>
            </Box>
            <Grid container>
              <Grid item xs={9}>
                <Button
                  className="admin-btn-back"
                  onClick={this.backToNavigatePage}
                >
                  <MdKeyboardBackspace />
                </Button>
                {/* <h3 className="text-left admin-page-heading">
                  Api Provider Apis Field Mapping
                </h3> */}
                <Typography variant="h1" align="left">
                  Api Provider Apis Field Mapping
                </Typography>
              </Grid>
              <Grid item xs={3} style={{ textAlign: "end" }}>
                {/* <ButtonComponent
                  className="addButton admin-btn-green"
                  onClick={this.inputModal(null, "create")}
                  color="primary"
                  value="Add New"
                /> */}
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    marginTop: "5px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Grid item xs={12} lg={12} md={12}>
                <p className="text-left">
                  <strong>Provider: </strong>{" "}
                  {currentProviderData?.providerName}
                  <br />
                  <strong>Provider Update Table: </strong>{" "}
                  {currentApiProviderUpdateData?.tableNameToUpdate}
                </p>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && apisFeildMapping.length === 0 && (
              <p>No Data Available</p>
            )}
            {!isLoading && apisFeildMapping.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Sport Type</TableCell>
                        <TableCell>Api Identifier</TableCell>
                        <TableCell>Is Element</TableCell>
                        <TableCell>Api Identifier Value</TableCell>
                        <TableCell>Value Type</TableCell>
                        <TableCell>Table Field</TableCell>
                        <TableCell>Level</TableCell>
                        <TableCell>Insert Check Flag</TableCell>
                        <TableCell>Is Reference</TableCell>
                        <TableCell>Table Name</TableCell>
                        <TableCell>Column Name</TableCell>
                        <TableCell style={{ minWidth: "155px" }}>
                          Action
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((api, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{api.id}</TableCell>
                          <TableCell>
                            {this.getSportType(api.sportTypeId)}
                          </TableCell>
                          <TableCell>
                            {this.getKey(api.apiIdentifier)}
                          </TableCell>
                          <TableCell>
                            {api.isElement === true ? "Yes" : "No"}
                          </TableCell>
                          <TableCell>{api.apiIdentifierValue}</TableCell>
                          <TableCell>{api.valueType}</TableCell>
                          <TableCell>{api.tableFeild}</TableCell>
                          <TableCell>{api.level}</TableCell>
                          <TableCell>
                            {api.insertCheckFlag ? "Yes" : "No"}
                          </TableCell>
                          <TableCell>
                            {api.isReference === true || api.isReference === "1"
                              ? "yes"
                              : "no"}
                          </TableCell>
                          <TableCell>{api.tableName}</TableCell>
                          <TableCell>{api.columnName}</TableCell>
                          <TableCell>
                            {/* <EditIcon
                              onClick={this.inputModal(api.id, "edit")}
                              color="primary"
                              className="mr10 cursor iconBtn admin-btn-green"
                            /> */}
                            <Button
                              onClick={this.inputModal(api.id, "edit")}
                              className="table-btn"
                            >
                              Edit
                            </Button>
                            {/* <DeleteOutlineIcon
                              onClick={this.setItemToDelete(api.id)}
                              color="secondary"
                              className="mr10 cursor iconBtn admin-btn-orange"
                            /> */}
                            <Button
                              onClick={this.setItemToDelete(api.id)}
                              className="table-btn"
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                apisFeildMapping.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                apisFeildMapping.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                apisFeildMapping.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                              className={
                                apisFeildMapping.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                apisFeildMapping.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div className={"paper"} style={{ position: "relative" }}>
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Api provider Api Field Mapping"
                    : "Edit Api provider Api Field Mapping"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateApiProviderApisFeildMapping
                  apisFeildMapping={apisFeildMapping}
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllFieldMapping={this.afterChangeRefresh}
                  allApiProvider={this.state.allApiProvider}
                  allSportsType={this.state.allSportsType}
                  allKeyIdentifire={this.state.allKeyIdentifire}
                  apiProviderUpdateId={this.props.match.params.id}
                  currentApiProviderUpdateData={currentApiProviderUpdateData}
                  dbtable={this.state.dbtable}
                />
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default ApiProviderApisFeildMapping;
