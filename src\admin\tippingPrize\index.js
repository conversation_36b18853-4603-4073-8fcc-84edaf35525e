import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import Select, { components } from "react-select";
import moment from "moment-timezone";
import "./tippingPrize.scss";
import _ from "lodash";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

class TippingPrize extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      tippingPrizeValues: {
        selectedWeeklyPrize: "",
        selectedMajorPrize: "",
      },
      prizes: "",
      prizesArray: [],
      TippingPrizeList: [],
      TippingPrizeCount: 0,
      errorWeeklyPrize: "",
      errorMajorPrize: "",
      isSearch: "",
      selectedTippingPrizeID: "",
      sports: [],
      selectedSports: null,
      OrgAll: [],
      selectedOrg: null,
      OrgApiCount: 0,
      isOrgSearch: "",
      countOrg: 0,
      searchOrg: [],
      searchOrgCount: 0,
      SearchOrgpage: 0,
      pageOrg: 0,
    };
  }

  componentDidMount() {
    this.fetchTippingPrize(1, "", null, null);
    this.fetchSportData();
  }

  componentDidUpdate(prevProps, prevState) {
    const { currentPage, isSearch, selectedSports, selectedOrg } = this.state;
    if (prevState.currentPage !== this.state.currentPage) {
      this.fetchTippingPrize(
        currentPage,
        isSearch,
        selectedSports,
        selectedOrg
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchTippingPrize(1, "", null, null);
      this.setState({
        offset: 0,
        currentPage: 1,
        isSearch: "",
        selectedSports: null,
        selectedOrg: null,
      });
    }
  }

  fetchSportData = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/sports/sport/?sportTypeId=${2}`
      );
      if (status === 200) {
        const sportsdata = data?.result.map((s) => ({
          ...s,
          label: s?.sportName,
          value: s?.id,
        }));

        const sdata = _.orderBy(sportsdata, ["label"], ["asc"])?.filter(
          (item) => [9, 4, 12]?.includes(item?.id)
        );
        let alldatas = sdata?.unshift({
          label: "All Sports",
          value: 0,
        });

        this.setState({ sports: sdata, selectedSports: sdata?.[0]?.id });
      }
    } catch (err) {
      console.error(err);
    }
  };

  fetchOrgData = async (page, sID, OrgAll) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/tournament?SportId=${sID}&offset=${page}&limit=${20}`
      );
      if (status === 200) {
        const newdata = data?.result?.rows.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));

        const filterData = _.unionBy(OrgAll, newdata)?.sort((a, b) =>
          a?.label.localeCompare(b?.label)
        );
        const unique = _.uniqBy(filterData, "value");
        let alldatas = unique?.unshift({
          label: "All Tournaments",
          value: 0,
        });

        this.setState((prevState) => ({
          OrgApiCount: prevState.OrgApiCount + 1,
          countOrg: Math.ceil(data?.result?.count / 20),
          OrgAll: unique,
          selectedOrg: unique?.[0]?.value,
        }));

        this.fetchPublicComp(sID, unique?.[0]?.value, 1, []);
      }
    } catch (err) {
      console.error(err);
    }
  };

  handleOrgInputChange = async (page, value, sid) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/tournament?SportId=${sid}&limit=${20}&offset=${page}&search=${value}`
      );
      if (status === 200) {
        const response = data?.result?.rows;
        const newdata = response.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));

        const mergeData = _.unionBy(this.state.searchOrg, newdata);
        const filterData = _.uniqBy(mergeData, "value");

        this.setState({
          searchOrg: filterData,
          isOrgSearch: value,
          searchOrgCount: Math.ceil(data?.result?.count / 20),
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  handleInputChangeOrg = (newValue, actionMeta) => {
    if (actionMeta.action === "input-change") {
      this.handleOrgInputChange(0, newValue, this.state.selectedSports);
    }
  };

  handleOnScrollBottomOrg = () => {
    if (
      this.state.isOrgSearch !== "" &&
      this.state.searchOrgCount !== Math.ceil(this.state.SearchOrgpage / 20)
    ) {
      this.handleOrgInputChange(
        this.state.SearchOrgpage + 20,
        this.state.isOrgSearch,
        this.state.selectedSports
      );

      this.setState((prevState) => ({
        SearchOrgpage: prevState.SearchOrgpage + 20,
      }));
    } else {
      if (
        this.state.countOrg !== 0 &&
        this.state.countOrg !== Math.ceil(this.state.pageOrg / 20 + 1)
      ) {
        this.fetchOrgData(
          this.state.pageOrg + 20,
          this.state.selectedSports,
          this.state.OrgAll
        );
        this.setState((prevState) => ({ pageOrg: prevState.pageOrg + 20 }));
      }
    }
  };

  async fetchTippingPrize(page, search, sportId, tournamentId) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/tipping/competition/list?SportId=${
          sportId ? sportId : ""
        }&tournamentId=${
          tournamentId ? tournamentId : ""
        }&limit=${rowPerPage}&page=${page}&search=${search}&isAdmin=true`
      );
      if (status === 200) {
        this.setState({
          TippingPrizeList: data?.result,
          isLoading: false,
          TippingPrizeCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  // handalValidate = () => {
  //   let { tippingPrizeValues } = this.state;
  //   let flag = true;
  //   if (
  //     tippingPrizeValues?.selectedWeeklyPrize?.trim() === "" ||
  //     tippingPrizeValues?.selectedWeeklyPrize === null
  //   ) {
  //     flag = false;
  //     this.setState({
  //       errorWeeklyPrize: "This field is mandatory",
  //     });
  //   } else {
  //     this.setState({
  //       errorWeeklyPrize: "",
  //     });
  //   }

  //   if (
  //     tippingPrizeValues?.selectedMajorPrize?.trim() === "" ||
  //     tippingPrizeValues?.selectedMajorPrize === null
  //   ) {
  //     flag = false;
  //     this.setState({
  //       errorMajorPrize: "This field is mandatory",
  //     });
  //   } else {
  //     this.setState({
  //       errorMajorPrize: "",
  //     });
  //   }

  //   return flag;
  // };

  handleSave = async () => {
    // if (this.handalValidate()) {
    const {
      tippingPrizeValues,
      currentPage,
      isSearch,
      selectedSports,
      selectedOrg,
    } = this.state;
    this.setState({ isLoading: true, isEditMode: false });
    const payload = {
      questions: tippingPrizeValues?.selectedWeeklyPrize,
      answer: tippingPrizeValues?.selectedMajorPrize,
    };

    try {
      const { status, data } = await axiosInstance.post(
        `/tippingFaqs/faqs`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchTippingPrize(
          currentPage,
          isSearch,
          selectedSports,
          selectedOrg
        );
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setState({ isLoading: false, isInputModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
    // }
  };

  handleUpdate = async () => {
    const {
      tippingPrizeValues,
      selectedTippingPrizeID,
      currentPage,
      isSearch,
      selectedSports,
      selectedOrg,
      prizes,
      prizesArray,
    } = this.state;

    // if (this.handalValidate()) {
    this.setState({ isLoading: true, isEditMode: true });
    const payload = {
      // weeklyPrize: tippingPrizeValues?.selectedWeeklyPrize,
      // majorPrize: tippingPrizeValues?.selectedMajorPrize,
      prizes: prizes,
      prizeMoney: prizes ? prizesArray : [],
    };

    try {
      const { status, data } = await axiosInstance.put(
        `/tipping/admin/competition/prize/${selectedTippingPrizeID}`,
        payload
      );
      if (status === 200) {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.fetchTippingPrize(
          currentPage,
          isSearch,
          selectedSports,
          selectedOrg
        );
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isLoading: false, isInputModalOpen: false });
        this.setActionMessage(true, "Error", data?.message);
      }
    } catch (err) {
      this.setState({ isLoading: false, isInputModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
    // }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorWeeklyPrize: "",
      errorMajorPrize: "",
      prizes: "",
      prizesArray: [],
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        // tippingPrizeValues: {
        //   // selectedMajorPrize: item?.majorPrize,
        //   // selectedWeeklyPrize: item?.weeklyPrize,
        // },
        prizes: item?.prizes,
        prizesArray:
          item?.prizeMoney?.length > 0
            ? item?.prizeMoney
            : [{ position: 1, prize: "", label: "", description: "" }],
        isEditMode: true,
        selectedTippingPrizeID: item?.id,
      });
    } else {
      this.setState({
        // tippingPrizeValues: {
        //   selectedWeeklyPrize: "",
        //   selectedMajorPrize: "",
        // },
        prizes: item?.prizes,
        prizesArray:
          item?.prizeMoney?.length > 1
            ? item?.prizeMoney
            : [{ position: 1, prize: "", label: "", description: "" }],
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const { itemToDelete, currentPage, isSearch, selectedSports, selectedOrg } =
      this.state;

    try {
      const { status, data } = await axiosInstance.delete(
        `/tippingFaqs/faqs/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchTippingPrize(
            currentPage,
            isSearch,
            selectedSports,
            selectedOrg
          );
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, TippingPrizeList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  handleClearClick = () => {
    const { selectedSports, selectedOrg } = this.state;
    this.fetchTippingPrize(1, "", selectedSports, selectedOrg);
    this.setState({
      offset: 0,
      currentPage: 1,
      isSearch: "",
    });
  };

  DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        {/* <SelectIndicator /> */}
      </components.DropdownIndicator>
    );
  };

  handlePrizeChange = (index, value) => {
    this.setState((prevState) => {
      const updatedPrizes = [...prevState.prizesArray];
      updatedPrizes[index].prize = value;
      return { prizesArray: updatedPrizes };
    });
  };

  handleLabelChange = (index, value) => {
    this.setState((prevState) => {
      const updatedPrizes = [...prevState.prizesArray];
      updatedPrizes[index].label = value;
      return { prizesArray: updatedPrizes };
    });
  };

  handleDescChange = (index, value) => {
    this.setState((prevState) => {
      const updatedPrizes = [...prevState.prizesArray];
      updatedPrizes[index].description = value;
      return { prizesArray: updatedPrizes };
    });
  };

  // addPosition = () => {
  //   this.setState((prevState) => ({
  //     prizesArray: [
  //       ...prevState.prizesArray,
  //       { position: prevState.prizesArray.length + 1, prize: "" },
  //     ],
  //   }));
  // };

  addPosition = () => {
    this.setState((prevState) => {
      const maxPosition =
        prevState.prizesArray?.length > 0
          ? Math?.max(...prevState.prizesArray?.map((p) => p?.position))
          : 0;
      return {
        prizesArray: [
          ...prevState.prizesArray,
          { position: maxPosition + 1, prize: "", label: "", description: "" },
        ],
      };
    });
  };

  removePosition = (index) => {
    this.setState((prevState) => {
      const updatedPrizes = prevState.prizesArray
        .filter((_, i) => i !== index)
        .map((item, i) => ({ ...item, position: i + 1 })); // Renumber positions
      return { prizesArray: updatedPrizes };
    });
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      tippingPrizeValues,
      TippingPrizeList,
      TippingPrizeCount,
      errorWeeklyPrize,
      errorMajorPrize,
      isSearch,
      sports,
      selectedSports,
      OrgAll,
      selectedOrg,
      OrgApiCount,
      isOrgSearch,
      countOrg,
      searchOrg,
      searchOrgCount,
      SearchOrgpage,
      pageOrg,
      prizes,
      prizesArray,
    } = this.state;
    const pageNumbers = [];

    if (TippingPrizeCount > 0) {
      for (let i = 1; i <= Math.ceil(TippingPrizeCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Tipping Prize</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Tipping Prize
                </Typography>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                <Select
                  className="React teamsport-select external-select sort-select"
                  value={
                    selectedSports &&
                    sports?.find((item) => item?.value === selectedSports)
                  }
                  onChange={(e) => {
                    this.setState({
                      selectedSports: e?.value === 0 ? "" : e?.value,
                      OrgAll: [],
                      selectedOrg: null,
                      currentPage: 1,
                    });
                    if (e?.value) {
                      this.fetchOrgData(0, e?.value, []);
                    }
                    this.fetchTippingPrize(1, isSearch, e?.value, null);
                  }}
                  options={sports}
                  classNamePrefix="select"
                  placeholder="Select sport"
                />
                <Select
                  className={`React sort-select teamsport-select external-select ${
                    selectedSports ? "" : "disable-state"
                  }`}
                  onMenuScrollToBottom={this.handleOnScrollBottomOrg}
                  onInputChange={this.handleInputChangeOrg}
                  onChange={(e) => {
                    this.setState({
                      selectedOrg: e?.value,
                      OrgApiCount: 0,
                      pageOrg: 0,
                      currentPage: 1,
                    });

                    this.fetchTippingPrize(
                      1,
                      isSearch,
                      selectedSports,
                      e?.value
                    );
                  }}
                  value={
                    selectedOrg &&
                    (isOrgSearch
                      ? searchOrg?.find((item) => item?.value === selectedOrg)
                      : OrgAll?.find((item) => item?.value === selectedOrg))
                  }
                  options={isOrgSearch ? searchOrg : OrgAll}
                  classNamePrefix="select"
                  placeholder="Select league"
                  isDisabled={!selectedSports}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() =>
                    this.fetchTippingPrize(
                      currentPage,
                      isSearch,
                      selectedSports,
                      selectedOrg
                    )
                  }
                >
                  Search
                </Button>
              </Grid>
              {/* <Grid
              item
              xs={12}
              className="admin-filter-wrap admin-fixture-wrap"
              style={{ marginBottom: "10px", marginTop: "12px" }}
            >
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "6px 10px",
                }}
                onClick={this.inputModal(null, "create")}
              >
                Add New
              </Button>
            </Grid> */}
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TippingPrizeList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TippingPrizeList?.length > 0 && (
              <TableContainer component={Paper}>
                <Table className="listTable" aria-label="simple table">
                  <TableHead className="tableHead-row">
                    <TableRow>
                      <TableCell style={{ cursor: "pointer" }}>ID</TableCell>
                      <TableCell>Competition Name</TableCell>
                      <TableCell>Tournament</TableCell>
                      {/* <TableCell>Weekly Prize</TableCell>
                      <TableCell>Major Prize</TableCell> */}
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody className="table_body">
                    <TableRow className="table_row">
                      <TableCell
                        colSpan={100}
                        className="table-seprator"
                      ></TableCell>
                    </TableRow>
                    {TippingPrizeList?.map((item) => {
                      return (
                        <TableRow
                          className="table-rows listTable-Row"
                          key={item?.id}
                        >
                          <TableCell> {item?.id} </TableCell>
                          <TableCell>{item?.competitionName}</TableCell>
                          <TableCell>{item?.Tournament?.name}</TableCell>
                          {/* <TableCell>{item?.weeklyPrize}</TableCell>
                          <TableCell>{item?.majorPrize}</TableCell> */}
                          <TableCell>
                            <Button
                              onClick={this.inputModal(item, "edit")}
                              className="table-btn edit-btn"
                            >
                              Edit
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    <TableRow>
                      <TableCell colSpan={100} className="pagination">
                        <div className="tablePagination">
                          <Pagination
                            hideNextButton
                            hidePrevButton
                            disabled={
                              TippingPrizeCount / rowPerPage > 1 ? false : true
                            }
                            page={currentPage}
                            onChange={this.handlePaginationClick}
                            count={pageNumbers[pageNumbers?.length - 1]}
                            siblingCount={2}
                            boundaryCount={1}
                            size="small"
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Tipping Prize"
                    : "Edit Tipping Prize"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      {/* <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Weekly Prize</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Weekly Prize"
                          value={tippingPrizeValues?.selectedWeeklyPrize}
                          onChange={(e) =>
                            this.setState({
                              tippingPrizeValues: {
                                ...tippingPrizeValues,
                                selectedWeeklyPrize: e?.target?.value,
                              },
                              errorWeeklyPrize: e?.target?.value
                                ? ""
                                : errorWeeklyPrize,
                            })
                          }
                        />
                        {errorWeeklyPrize ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorWeeklyPrize}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Major Prize</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Major Prize"
                          value={tippingPrizeValues?.selectedMajorPrize}
                          onChange={(e) =>
                            this.setState({
                              tippingPrizeValues: {
                                ...tippingPrizeValues,
                                selectedMajorPrize: e?.target?.value,
                              },
                              errorMajorPrize: e?.target?.value
                                ? ""
                                : errorMajorPrize,
                            })
                          }
                        />
                        {errorMajorPrize ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorMajorPrize}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid> */}
                      <Grid item xs={12} className="radio-wrap">
                        <FormControl component="fieldset">
                          <label className="modal-label"> Prizes </label>
                          <RadioGroup
                            aria-label="gender"
                            name="gender"
                            className="gender"
                            value={prizes?.toString()}
                            onChange={(e) =>
                              this.setState({
                                prizes: e?.target?.value === "true", // Convert string to boolean
                              })
                            }
                          >
                            <FormControlLabel
                              value="true"
                              control={
                                <Radio
                                  color="primary"
                                  checked={prizes === true}
                                />
                              }
                              label="Yes"
                            />
                            <FormControlLabel
                              value="false"
                              control={
                                <Radio
                                  color="primary"
                                  checked={prizes === false}
                                />
                              }
                              label="No"
                            />
                          </RadioGroup>
                        </FormControl>
                      </Grid>
                      {prizes === true && (
                        <Grid
                          item
                          xs={12}
                          className="national-select"
                          style={{ marginBottom: "15px" }}
                        >
                          <Box className="prize-info-container">
                            <Box className="prize-header-section">
                              <Typography className="w-80 cutoff-txt">
                                Position
                              </Typography>
                              <Typography className="cutoff-txt">
                                Prize money
                              </Typography>
                            </Box>
                            {prizesArray?.map((prize, index) => (
                              <Box
                                key={index}
                                sx={{ gap: 2, mb: 2 }}
                                className="prize-info-item"
                              >
                                <Box className="w-80">
                                  <Typography className="prize-position">
                                    {prize?.position}
                                  </Typography>
                                </Box>
                                <Box className="field-container">
                                  <TextField
                                    className="teamsport-textfield rec FAQ-textfield"
                                    variant="outlined"
                                    type="number"
                                    color="primary"
                                    size="small"
                                    placeholder="Prize Money"
                                    onChange={(e) =>
                                      this.handlePrizeChange(
                                        index,
                                        e.target.value
                                      )
                                    }
                                    value={prize?.prize}
                                  />
                                  <TextField
                                    className="teamsport-textfield rec FAQ-textfield"
                                    variant="outlined"
                                    type="text"
                                    color="primary"
                                    size="small"
                                    placeholder="Prize Label"
                                    onChange={(e) =>
                                      this.handleLabelChange(
                                        index,
                                        e.target.value
                                      )
                                    }
                                    value={prize?.label}
                                  />
                                  <TextField
                                    className="teamsport-textfield rec FAQ-textfield"
                                    variant="outlined"
                                    type="text"
                                    color="primary"
                                    size="small"
                                    placeholder="Prize description"
                                    onChange={(e) =>
                                      this.handleDescChange(
                                        index,
                                        e.target.value
                                      )
                                    }
                                    value={prize?.description}
                                  />
                                </Box>
                                <IconButton
                                  color="error"
                                  onClick={() => this.removePosition(index)}
                                  disabled={prizesArray.length === 1}
                                  className="delete-icon"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Box>
                            ))}
                            <Button
                              variant="contained"
                              startIcon={<AddIcon />}
                              onClick={this.addPosition}
                              className="add-position-btn"
                            >
                              Add position
                            </Button>
                          </Box>
                        </Grid>
                      )}
                      <span style={{ fontSize: "12px", color: "red" }}>
                        {" "}
                        Note: Need to add $ sign before amount and write text as
                        'prize'/'price'/'Prize'/'price' and then (Add your
                        text). For example sentence must be start with e.g. $500
                        prize (Add your text).{" "}
                      </span>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default TippingPrize;
