import { Validators } from "../../../library/utilities/Validators";

export const ClientsFormModel = [
  {
    label: "Client Name",
    value: "",
    type: "text",
    placeholder: "Client Name",
    field: "name",
    validators: [{ check: Validators.required }],
    required: true,
    className: "12",
  },
  {
    label: "Contact Name",
    value: "",
    type: "text",
    placeholder: "Contact Name",
    field: "contact_name",
    validators: [{ check: Validators.required }],
    required: true,
    className: "12",
  },
  {
    label: "Email",
    value: "",
    type: "text",
    placeholder: "Email",
    field: "email",
    validators: [
      { check: Validators.required },
      { check: Validators.email, message: "Provide valid email" },
    ],
    required: true,
    className: "12",
  },
  {
    label: "Website",
    value: "",
    type: "text",
    placeholder: "Website",
    field: "website",
    validators: [{ check: Validators.required }],
    required: true,
    className: "12",
  },
  {
    label: "Phone Number",
    value: "",
    type: "text",
    placeholder: "Number",
    field: "phone_number",
    validators: [
      { check: Validators.required },
      {
        check: Validators.mobile,
        message: "Your Number Must be Between 10 - 12 Digits",
      },
    ],
    required: true,
    className: "12",
  },
];
