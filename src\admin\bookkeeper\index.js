import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Typography,
  Breadcrumbs,
  Button,
  Checkbox,
} from "@mui/material";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
// import RateReviewIcon from "@mui/icons-material/RateReview";
// import ButtonComponent from "../../library/common/components/Button";
import { Editor } from "react-draft-wysiwyg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import { Loader, ActionMessage } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import CreateBookkeeper from "./CreateBookkeeper";
import { config } from "../../helpers/config";
import Pagination from "@mui/material/Pagination";
import {
  EditorState,
  convertToRaw,
  ContentState,
  AtomicBlockUtils,
  convertFromHTML,
} from "draft-js";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import ImageUploader from "./ImageUploader";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import ButtonComponent from "../../library/common/components/Button";
import ReviewTableModal from "./ReviewTableModal";
import { ReactComponent as Unchecked } from "../../images/uncheck-star.svg";
import { ReactComponent as Checked } from "../../images/check-star.svg";
import Select from "react-select";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";

const featureBookMakerOption = [
  {
    label: "All",
    value: 0,
  },

  {
    label: "Featured",
    value: 1,
  },
];
class Bookkeeper extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      bookkeeper: [],
      defaultLayout: {},
      allApiProvider: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      isCMSModalOpen: false,
      isReviewModalOpen: false,
      isAddToCMSInfoMode: false,
      isDetailsAvailable: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      cmsData: {
        id: "",
        details: "",
      },
      uploadImage: "",
      authorUploadImage: "",
      editorState: EditorState.createEmpty(),
      image: [],
      authorImage: [],
      currentPage: 1,
      rowPerPage: 20,
      bookKeeperId: "",
      draggedItem: null,
      isSortChange: false,
      checkBoxValues: [],
      selectedFeatureBookMaker: null,
      content: "",
    };
  }

  componentDidMount() {
    this.fetchAllBookkeeper();
  }

  async fetchAllBookkeeper(isFeatured) {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      `apiProviders/bookKeeper?status=all&isFeatured=${isFeatured ? true : ""}`
    );
    if (status === 200) {
      const sortedData = data?.result?.sort(
        (a, b) => a.featured_order - b.featured_order
      );

      this.fetchDefaultLayout(sortedData);
      this.fetchAllApiProvider();
      let newdata = [];
      const filteredData = sortedData?.filter(
        (item) => item?.isFeatured === true
      );
      let categories = filteredData?.map((item) => {
        newdata.push(item?.id);
      });
      this.setState({ checkBoxValues: newdata });
    }
  }

  async fetchDefaultLayout(sortedData) {
    // this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(`layout/getone`);
    let sortedDataTemp = [];
    if (status === 200) {
      const oddWin = data?.result?.oddWin;
      sortedDataTemp = sortedData?.map((item) => {
        const isMatch = oddWin.some(
          (item1) => item1?.bookkeepersId === item?.id
        );
        return { ...item, isEnable: isMatch };
      });
      this.setState({
        bookkeeper: sortedDataTemp,
        isLoading: false,
        defaultLayout: data?.result,
      });
    }
  }

  async fetchAllApiProvider() {
    const { status, data } = await axiosInstance.get(URLS.apiProvider);
    if (status === 200) {
      this.setState({ allApiProvider: data.result });
    }
  }

  getApiProvider = (id) => {
    let { allApiProvider } = this.state;
    let apiProvider = allApiProvider
      .filter((obj) => obj.id === id)
      .map((object) => object.providerName);
    return apiProvider;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  inputModal = (id, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({ idToSend: id, isEditMode: true });
    } else {
      this.setState({ isEditMode: false });
    }
  };

  openCMSModal = async (item) => {
    this.setState({ isCMSModalOpen: true, bookKeeperId: item?.id });

    const { status, data } = await axiosInstance.get(
      `/bookkeepercms/bookkeepers/${item?.id}`
    );
    if (status === 200) {
      const htmlString = data?.data?.details?.details;
      if (htmlString.length > 0) {
        this.setState({
          isDetailsAvailable: true,
        });
      } else {
        this.setState({
          isDetailsAvailable: false,
        });
      }
      // const contentBlock = htmlToDraft(htmlString);
      // if (contentBlock) {
      //   const contentState = ContentState.createFromBlockArray(
      //     contentBlock.contentBlocks
      //   );
      //   const editorState = EditorState.createWithContent(contentState);
      //   this.setState({ editorState });
      // }

      if (typeof htmlString === "string") {
        this.setState({
          content: htmlString,
        });
      }
    }
  };

  handleSave = async () => {
    const { bookKeeperId, cmsData } = this.state;
    var payload = {
      details: {
        details: cmsData?.details,
      },
      bookKeeperId: bookKeeperId,
    };

    this.setState({ isLoading: true, isEditMode: false });
    try {
      const { status } = await axiosInstance.post(`/bookkeepercms`, payload);
      if (status === 201 || status === 200) {
        this.setState({
          isLoading: false,
          isCMSModalOpen: false,
          bookKeeperId: "",
          content: "",
        });

        this.setActionMessage(
          true,
          "Success",
          `CMS Content Added Successfully`
        );
      } else {
        this.setState({
          isLoading: false,
          isCMSModalOpen: false,
          bookKeeperId: "",
        });
      }
    } catch (err) {
      this.setState({
        isLoading: false,
        isCMSModalOpen: false,
        bookKeeperId: "",
      });
    }
  };

  toggleCMSModal = () => {
    this.setState({
      uploadImage: "",
      authorUploadImage: "",
      isCMSModalOpen: !this.state.isCMSModalOpen,
      editorState: EditorState.createEmpty(),
      image: [],
      selectedNewsType: null,
      selectedFeatureBookMaker: null,
      authorImage: [],
      content: "",
    });
  };

  openReviewModal = (item) => {
    this.setState({ isReviewModalOpen: true, bookKeeperId: item?.id });
  };

  toggleReviewModal = () => {
    this.setState({ isReviewModalOpen: !this.state.isReviewModalOpen });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  // Function to convert ContentState to HTML
  convertContentStateToHTML = (contentState) => {
    const contentStateWithHTML = ContentState.createFromBlockArray(
      contentState.getBlockMap()
    );

    return contentStateWithHTML.getPlainText();
  };

  afterChangeRefresh = () => {
    this.fetchAllBookkeeper();
  };

  deleteItem = async () => {
    try {
      const { status } = await axiosInstance.delete(
        `${URLS.bookkeeper}/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllBookkeeper();
        });
        this.setActionMessage(
          true,
          "Success",
          "Bookkeeper Deleted Successfully!"
        );
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, bookkeeper } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < bookkeeper.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  handleEditorChange = (editorState) => {
    const { cmsData } = this.state;
    this.setState({ editorState });
    let string = draftToHtml(convertToRaw(editorState.getCurrentContent()));
    this.setState({
      cmsData: {
        ...cmsData,
        details: string,
      },
    });
  };

  handleImageUpload = (imageUrl) => {
    const { editorState } = this.state;
    const contentState = editorState.getCurrentContent();
    const contentStateWithEntity = contentState.createEntity(
      "IMAGE",
      "IMMUTABLE",
      { src: imageUrl }
    );
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();
    const newEditorState = EditorState.set(editorState, {
      currentContent: contentStateWithEntity,
    });
    this.setState({
      editorState: AtomicBlockUtils.insertAtomicBlock(
        newEditorState,
        entityKey,
        " "
      ),
    });
  };

  handeModleLoading = (status) => {
    this.setState({
      isLoading: status,
    });
  };

  handleDragStart = (item) => {
    this.setState({ draggedItem: item });
  };

  // Function to handle the drop and reorder the items
  handleDrop = (targetIndex) => {
    const { bookkeeper, draggedItem } = this.state;
    if (draggedItem) {
      const updatedItems = bookkeeper ? [...bookkeeper] : [];
      const draggedIndex = bookkeeper.indexOf(draggedItem);

      // Remove the dragged item from the original position
      updatedItems.splice(draggedIndex, 1);

      // Insert the dragged item at the new position
      updatedItems.splice(targetIndex, 0, draggedItem);
      this.setState({
        bookkeeper: updatedItems,
        draggedItem: null,
        isSortChange: true,
      });
    }
  };
  handleOrderChange = async () => {
    const { bookkeeper } = this.state;

    let newdata = [];
    let categories = bookkeeper?.map((item) => {
      newdata.push(item?.id);
    });
    try {
      const { status, data } = await axiosInstance.put(
        `apiProviders/featuredOrder/bookKeeper`,
        {
          featuredOrder: newdata,
        }
      );
      if (status === 200) {
        this.setState({ isSortChange: false });
        this.fetchAllBookkeeper();
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isSortChange: false });
      }
    } catch (err) {
      this.setState({ isSortChange: false });
    }
  };
  handleChangeFeatureBookMaker = async (isFeatured, id) => {
    try {
      const { status, data } = await axiosInstance.put(
        `apiProviders/featuredOrder/bookKeeper`,
        {
          isFeatured: isFeatured,
          BookKeeperId: id,
        }
      );
      if (status === 200) {
        this.setActionMessage(true, "Success", data?.message);
      } else {
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err.response.data.message);
    }
  };
  handleCheckBoxChange = (e) => {
    const { value, checked } = e.target;
    const { checkBoxValues } = this.state;
    if (checked) {
      if (checkBoxValues?.length < 6) {
        let checkboxdata = [...checkBoxValues, Number(value)];
        this.setState({
          checkBoxValues: checkboxdata,
        });
      }
      this.handleChangeFeatureBookMaker(true, Number(value));
    } else {
      let checkboxdata = checkBoxValues?.filter(
        (element) => element !== Number(value)
      );
      this.setState({
        checkBoxValues: checkboxdata,
      });
      this.handleChangeFeatureBookMaker(false, Number(value));
    }
  };
  handleFeatureBookMakerChange = (e) => {
    this.setState({
      selectedFeatureBookMaker: e.value,
    });
    const passData = e.value == 0 ? false : true;
    this.fetchAllBookkeeper(passData);
  };

  handleChange = (content) => {
    const { cmsData } = this.state;
    this.setState({
      cmsData: {
        ...cmsData,
        details: content,
      },
    });
  };

  navigateToReview = (item) => {
    this.props.navigate("/bookkeeper-review", {
      name: item?.name,
      bookkeeperId: item?.id,
    });
  };

  layoutIsEnable = async (item, isEnable) => {
    const { defaultLayout } = this.state;
    const oddsWinArray = defaultLayout?.oddWin;
    let removeSelectdId = [];
    if (isEnable) {
      removeSelectdId = oddsWinArray?.filter(
        (rItem) => rItem?.bookkeepersId !== item?.id
      );
    }

    const maxOrder = Math.max(...oddsWinArray.map((maxItem) => maxItem?.order));

    const types = ["oddWin", "oddPlace"];
    const newBookkeeperObjects = types?.map((type, index) => ({
      type,
      order: maxOrder + index + 1,
      fontSize: "14px",
      fontColor: "#191919",
      fontweight: "400",
      columnColor: "#ffffff",
      bookkeepersId: item?.id,
    }));

    const typesTab = [
      { type: "oddWin" },
      { type: "oddToteWin", key: "NSW_TOTE_WIN" },
      { type: "oddToteWin", key: "QLD_TOTE_WIN" },
      { type: "oddToteWin", key: "VIC_TOTE_WIN" },
      { type: "oddPlace" },
      { type: "oddTotePlace", key: "NSW_TOTE_PLACE" },
      { type: "oddTotePlace", key: "QLD_TOTE_PLACE" },
      { type: "oddTotePlace", key: "VIC_TOTE_PLACE" },
    ];

    const newTabBookkeeperObjects = typesTab?.map((itemTab, index) => {
      const baseObject = {
        type: itemTab?.type,
        order: maxOrder + index + 1, // Dynamically assign `order`
        fontSize: "14px",
        fontColor: "#191919",
        fontweight: "400",
        columnColor: "#ffffff",
        bookkeepersId: item?.id, // Add bookkeepersId dynamically
      };

      // Add the `key` property only if it exists
      if (itemTab?.key) {
        baseObject.key = itemTab?.key;
      }

      return baseObject;
    });

    const updatedArray = [
      ...oddsWinArray,
      ...(item?.id === 21 ? newTabBookkeeperObjects : newBookkeeperObjects),
    ];

    const payload = {
      layoutName: defaultLayout?.layoutName,
      staticName: defaultLayout?.staticName,
      oddWin: isEnable ? removeSelectdId : updatedArray,
    };
    try {
      const { status } = await axiosInstance.post(
        `/layout/create?default=true`,
        payload
      );
      if (status === 200 || status === 201) {
        this.fetchAllBookkeeper();
      } else {
      }
    } catch (err) {}
  };
  render() {
    var {
      bookkeeper,
      isLoading,
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isEditMode,
      rowPerPage,
      currentPage,
      isCMSModalOpen,
      isReviewModalOpen,
      editorState,
      bookKeeperId,
      isDetailsAvailable,
      checkBoxValues,
      selectedFeatureBookMaker,
      content,
    } = this.state;
    const pageNumbers = [];
    // if (bookkeeper?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   bookkeeper = bookkeeper?.slice(indexOfFirstTodo, indexOfLastTodo);

    //   for (let i = 1; i <= Math.ceil(bookkeeper?.length / rowPerPage); i++) {
    //     pageNumbers.push(i);
    //   }
    // }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper > */}
            <Box
              style={{
                position: "fixed",
                top: "62px",
                backgroundColor: "transparent",
                zIndex: "1",
                width: "100%",
              }}
            >
              {messageBox?.display && (
                <ActionMessage
                  message={messageBox?.message}
                  type={messageBox?.type}
                  styleClass={messageBox?.styleClass}
                />
              )}
            </Box>
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Bookkeeper</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                {/* <h3 className="text-left">Bookkeeper</h3> */}
                <Typography variant="h1" align="left">
                  Bookkeeper
                </Typography>
              </Grid>
              <Grid item xs={9} className="admin-filter-wrap">
                {/* <ButtonComponent
                  className="addButton admin-btn-green"
                  onClick={this.inputModal(null, "create")}
                  color="primary"
                  value="Add New"
                /> */}
                <Select
                  className="React cricket-select text-capitalize  external-select"
                  classNamePrefix="select"
                  placeholder="Select Status"
                  value={featureBookMakerOption?.find((item) => {
                    return item?.value == selectedFeatureBookMaker;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleFeatureBookMakerChange(e)}
                  options={featureBookMakerOption}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap"
                style={{ marginBottom: "10px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: this.state.isSortChange
                      ? "#4455c7"
                      : "rgba(0, 0, 0, 0.12)",
                    color: this.state.isSortChange
                      ? "#fff"
                      : "rgba(0, 0, 0, 0.26)",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  disabled={!this.state.isSortChange}
                  onClick={() => {
                    this.handleOrderChange();
                  }}
                >
                  Save Order
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && bookkeeper.length === 0 && <p>No Data Available</p>}
            {!isLoading && bookkeeper.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable api-provider-listTable"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Feature</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Api provider</TableCell>
                        <TableCell>Order</TableCell>
                        {/* <TableCell style={{ width: "10%" }}>Link</TableCell> */}
                        <TableCell>Small Logo</TableCell>
                        <TableCell>Long Logo</TableCell>
                        <TableCell>Ourpartner</TableCell>
                        <TableCell>Feature Logo</TableCell>
                        <TableCell>Email Logo</TableCell>
                        <TableCell>Graph Logo</TableCell>
                        <TableCell>Current Best Logo</TableCell>
                        <TableCell>BookMaker List Logo</TableCell>
                        {/* <TableCell>Variation</TableCell> */}
                        <TableCell>Total Review</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell style={{ textAlign: "center" }}>
                          Action
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {bookkeeper?.map((bookkeeper, i) => (
                        <TableRow
                          key={i}
                          className="table-rows listTable-Row"
                          draggable
                          onDragStart={() => this.handleDragStart(bookkeeper)}
                          onDragOver={(e) => e.preventDefault()}
                          onDrop={() => this.handleDrop(i)}
                        >
                          <TableCell>{bookkeeper?.id}</TableCell>
                          <TableCell>
                            <Checkbox
                              disableRipple
                              disableFocusRipple
                              disableTouchRipple
                              className="filter-racing"
                              icon={<Unchecked />}
                              checkedIcon={<Checked />}
                              name="filter"
                              value={bookkeeper?.id}
                              onChange={(event) => {
                                this.handleCheckBoxChange(
                                  event,
                                  bookkeeper?.id
                                );
                              }}
                              checked={checkBoxValues?.includes(bookkeeper?.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <a
                              href={bookkeeper?.affiliate_link}
                              target="_blank"
                            >
                              <Box
                                style={{
                                  whiteSpace: "pre-wrap",
                                  wordWrap: "break-word",
                                  color: "blue",
                                  textDecoration: "underline",
                                  cursor: "pointer",
                                }}
                              >
                                {bookkeeper?.name}
                              </Box>
                            </a>
                          </TableCell>
                          <TableCell>
                            <Box
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                            >
                              {this.getApiProvider(bookkeeper?.apiProviderId)}
                            </Box>
                          </TableCell>
                          <TableCell>{bookkeeper?.featured_order}</TableCell>
                          {/* <TableCell>
                            {" "}
                            <Box
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                                width: "140px",
                              }}
                            >
                              {bookkeeper?.affiliate_link}
                            </Box>
                          </TableCell> */}
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.small_logo ? (
                                <img
                                  src={config.mediaUrl + bookkeeper?.small_logo}
                                  alt={bookkeeper?.name}
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  style={{ textAlign: "center" }}
                                >
                                  -
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.long_logo ? (
                                <img
                                  src={config.mediaUrl + bookkeeper?.long_logo}
                                  alt={bookkeeper?.name}
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  textAlign="center"
                                >
                                  -
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.logo ? (
                                <img
                                  src={config.mediaUrl + bookkeeper?.logo}
                                  alt={bookkeeper?.name}
                                />
                              ) : (
                                <div className="imagePlaceholder"> -</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.featured_logo ? (
                                <img
                                  src={
                                    config.mediaUrl + bookkeeper?.featured_logo
                                  }
                                  alt={bookkeeper?.name}
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  style={{ textAlign: "center" }}
                                >
                                  -
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.em_logo ? (
                                <img
                                  src={config.mediaUrl + bookkeeper?.em_logo}
                                  alt={bookkeeper?.name}
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  style={{ textAlign: "center" }}
                                >
                                  -
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.graph_logo ? (
                                <img
                                  src={config.mediaUrl + bookkeeper?.graph_logo}
                                  alt={bookkeeper?.name}
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  style={{ textAlign: "center" }}
                                >
                                  -
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.currentBest_logo ? (
                                <img
                                  src={
                                    config.mediaUrl +
                                    bookkeeper?.currentBest_logo
                                  }
                                  alt={bookkeeper?.name}
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  style={{ textAlign: "center" }}
                                >
                                  -
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className="logocontainer"
                              style={{ textAlign: "center" }}
                            >
                              {bookkeeper?.bookMakerList_logo ? (
                                <img
                                  src={
                                    config.mediaUrl +
                                    bookkeeper?.bookMakerList_logo
                                  }
                                  alt={bookkeeper?.name}
                                  width="45px"
                                />
                              ) : (
                                <div
                                  className="imagePlaceholder"
                                  style={{ textAlign: "center" }}
                                >
                                  -
                                </div>
                              )}
                            </div>
                          </TableCell>
                          {/* <TableCell>
                            {showVariations(bookkeeper?.variation)}
                          </TableCell> */}
                          <TableCell>{bookkeeper?.Total_Reviews}</TableCell>
                          <TableCell>{bookkeeper.status}</TableCell>
                          <TableCell>
                            <Box style={{ display: "flex" }}>
                              {/* <EditIcon
                              onClick={this.inputModal(bookkeeper.id, "edit")}
                              color="primary"
                              className="mr10 cursor iconBtn admin-btn-green"
                            /> */}
                              <Button
                                style={{ width: "max-content" }}
                                className={`table-btn ${
                                  bookkeeper?.isEnable
                                    ? "delete-btn"
                                    : "info-btn"
                                } `}
                                onClick={() =>
                                  this.layoutIsEnable(
                                    bookkeeper,
                                    bookkeeper?.isEnable
                                  )
                                }
                              >
                                SOC{" "}
                                {bookkeeper?.isEnable ? "disable" : "Enable"}
                              </Button>
                              <Button
                                onClick={this.inputModal(bookkeeper.id, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              {/* <DeleteOutlineIcon
                              onClick={this.setItemToDelete(bookkeeper.id)}
                              color="secondary"
                              className="mr10 cursor iconBtn admin-btn-orange"
                            /> */}
                              <Button
                                onClick={() =>
                                  this.setItemToDelete(bookkeeper.id)
                                }
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                              <Button
                                className="table-btn info-btn"
                                onClick={() => this.openCMSModal(bookkeeper)}
                              >
                                info
                              </Button>
                              <Button
                                className="table-btn info-btn"
                                onClick={() =>
                                  this.navigateToReview(bookkeeper)
                                }
                              >
                                Review
                              </Button>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                              className={
                                bookkeeper.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                bookkeeper.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                            {/* <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                bookkeeper.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            /> */}
                            {/* <button
                              className={
                                bookkeeper.length / rowPerPage > 1
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                bookkeeper.length / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input bookkeeper-modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Bookkeeper" : "Edit Bookkeeper"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleInputModal()}
                />
                <CreateBookkeeper
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllBookkeeper={this.afterChangeRefresh}
                  allApiProvider={this.state.allApiProvider}
                  setActionMessage={this.setActionMessage}
                />
              </div>
            </Modal>

            <Modal
              className="news-modal"
              open={isCMSModalOpen}
              onClose={this.toggleCMSModal}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">
                  {isDetailsAvailable === false
                    ? "Create CMS Data"
                    : "Edit CMS Data"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleCMSModal()}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {/* <div className="Editor">
                      <Editor
                        editorState={editorState}
                        onEditorStateChange={this.handleEditorChange}
                      />
                      <ImageUploader
                        onImageUpload={(image) => this.handleImageUpload(image)}
                        handeModleLoading={(status) =>
                          this.handeModleLoading(status)
                        }
                      />
                    </div> */}
                    <div className="suneditor-wrap">
                      {/* <label>123456</label> */}
                      <SunEditor
                        onChange={this.handleChange}
                        setContents={content}
                        setOptions={{
                          buttonList: [
                            ["undo", "redo"],
                            ["font", "fontSize", "formatBlock"],
                            [
                              "bold",
                              "underline",
                              "italic",
                              "strike",
                              "subscript",
                              "superscript",
                            ],
                            ["removeFormat"],
                            ["outdent", "indent"],
                            ["align", "horizontalRule", "list", "table"],
                            ["link", "image", "video"],
                            ["fullScreen", "showBlocks", "codeView"],
                          ],
                        }}
                      />
                    </div>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {/* {isDetailsAvailable === false ? ( */}
                          <ButtonComponent
                            className="mt-3 admin-btn-green"
                            onClick={() => this.handleSave()}
                            color="primary"
                            value={
                              isDetailsAvailable === true ? "Update" : "Save"
                            }
                            disabled={isLoading}
                          />
                          {/* // ) : (
                          //   <ButtonComponent
                          //     className="mt-3 admin-btn-green"
                          //     onClick={() => this.handleSave()}
                          //     color="secondary"
                          //     value={!isLoading ? "Update" : "Loading..."}
                          //     disabled={isLoading}
                          //   />
                          // )} */}

                          <ButtonComponent
                            onClick={() => this.toggleCMSModal()}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="news-modal"
              open={isReviewModalOpen}
              onClose={this.toggleReviewModal}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">Reviews</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleReviewModal()}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <ReviewTableModal bookKeeperId={bookKeeperId} />
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Bookkeeper;
