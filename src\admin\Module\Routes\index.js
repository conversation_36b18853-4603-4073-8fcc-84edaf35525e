import { connect } from "react-redux";
import React from "react";
import {
  Navigate,
  Route,
  Routes as RootRoutes,
  useLocation,
} from "react-router-dom";
import PrivateRoute from "./PrivateRoute";
import AdminLogin from "../../adminlogin/AdminLogin";

import { routes } from "./routes-list";

const Routes = (props) => {
  const { isLoggedIn } = props;
  const location = useLocation();
  const { from } = location.state || { from: { pathname: "/dashboard" } };
  return (
    <div>
      <RootRoutes>
        <Route
          exact
          path="/"
          render={() => {
            if (isLoggedIn && from) {
              return <Navigate to={from} />;
            } else {
              return <Navigate to="/login" />;
            }
          }}
        />

        <Route exact path="/login" component={AdminLogin} />

        {routes.map((item, index) => (
          <PrivateRoute
            exact
            key={index}
            isLoggedIn={isLoggedIn}
            path={item.path}
            component={item.component}
          />
        ))}
      </RootRoutes>
    </div>
  );
};

const mapStateToProps = ({ authReducer }) => {
  return {
    isLoggedIn: authReducer.isLoggedIn,
  };
};

export default Routes;
