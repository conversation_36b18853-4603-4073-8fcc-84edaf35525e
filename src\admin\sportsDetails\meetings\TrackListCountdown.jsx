import React, { useState } from "react";
import { Chip, TableCell } from "@mui/material";
import withStyles from '@mui/styles/withStyles';
import { useTimer } from "react-timer-hook";
// import moment from "moment";
import { Link } from "react-router-dom";
// import { Routes } from "../../../../../helpers/constants/routeConstants";
const StyledTableCell = withStyles((theme) => ({
  body: {
    cursor: "pointer",
    borderBottom: "none",
    padding: "0px !important",
  },
}))(TableCell);

const TrackListCountdown = ({
  expiryTimestamp,
  race,
  race_obj,
  checkRaceMinCell,
  isMobile,
  props,
  meetingsDetails,
  //   raceData,
  intl,
}) => {
  //   const navigate = useNavigate();
  const [isRaceFinished, setIsRaceFinished] = useState(false);
  const { days, seconds, minutes, hours } = useTimer({
    expiryTimestamp,
    onExpire: () => setIsRaceFinished(true),
  });

  var sportsName =
    race_obj?.sportId === 1
      ? "Horse Racing"
      : race_obj?.sportId === 2
      ? "Harness Racing"
      : race_obj?.sportId === 3
      ? "Greyhound Racing"
      : "";
  var navigatePath =
    race_obj &&
    meetingsDetails &&
    `/${sportsName}/${race_obj?.sportId}/${meetingsDetails?.eventName}/${race_obj?.eventId}/runners/${race_obj.id}`;

  return (
    <>
      {isMobile === false ? (
        <>
          <Link to={navigatePath}>
            <StyledTableCell // Desktop View
              align="center"
              className={
                isRaceFinished
                  ? "upcoming_race_cell_close interim"
                  : checkRaceMinCell(race_obj?.startTimeDate, "desktop")
              }

              //   onClick={
              //     race_obj?.startTimeDate === null
              //       ? () => {}
              //       : () =>
              //           navigate(
              //             Routes.RunnerDetails(
              //               race_obj?.sportId === 1
              //                 ? "horse"
              //                 : race_obj?.sportId === 2
              //                 ? "harness"
              //                 : "greyhounds",
              //               race_obj?.sportId,
              //               race_obj?.id,
              //               race?.trackId,
              //               race?.id,
              //               race_obj?.startTimeDate,
              //               intl
              //             ),
              //             {
              //               state: {
              //                 raceData: raceData,
              //                 CurrentData: race,
              //                 SelectedRaceid: race_obj?.id,
              //               },
              //             }
              //           )
              //   }
            >
              {race_obj?.startTimeDate !== null ? (
                <Chip
                  className={"singlerace-count-chip"} // Race startDateTime countdown
                  style={{
                    backgroundColor: "transparent",
                    cursor: "pointer",
                  }}
                  size="small"
                  label={
                    hours === 0 && minutes === 0 && seconds === 0 ? (
                      <span style={{ color: "red" }}>closed</span>
                    ) : (
                      <span>
                        {days > 0 ? days + "d" : ""}{" "}
                        {days > 0 ? hours + "h" : hours > 0 ? hours + "h" : ""}{" "}
                        {days === 0 ? (minutes > 0 ? minutes + "m" : "0m") : ""}{" "}
                        {days === 0 && hours === 0 && minutes <= 5
                          ? seconds > 0
                            ? seconds + "s"
                            : "0s"
                          : ""}
                      </span>
                    )
                  }
                />
              ) : (
                <span style={{ textAlign: "center" }}>-</span>
              )}
            </StyledTableCell>
          </Link>
        </>
      ) : (
        <>
          <Link to={navigatePath}>
            <td // Mobile View
              style={{ width: 80, textAlign: "right" }}
              className={
                isRaceFinished
                  ? ""
                  : checkRaceMinCell(race?.race[0].startTimeDate, "mobile")
              }
            >
              {race?.race?.length > 0 &&
              race?.race[0].startTimeDate !== null ? (
                <Chip // Race StartDateTime countdown
                  className={"singlerace-count-chip"}
                  style={{
                    backgroundColor: "transparent",
                    cursor: "pointer",
                  }}
                  size="small"
                  label={
                    hours === 0 && minutes === 0 && seconds === 0 ? (
                      <span style={{ color: "red" }}>closed</span>
                    ) : (
                      <span>
                        {days > 0 ? days + "d" : ""}{" "}
                        {days > 0 ? hours + "h" : hours > 0 ? hours + "h" : ""}{" "}
                        {days === 0 ? (minutes > 0 ? minutes + "m" : "0m") : ""}{" "}
                        {days === 0 && hours === 0 && minutes <= 5
                          ? seconds > 0
                            ? seconds + "s"
                            : "0s"
                          : ""}
                      </span>
                    )
                  }
                />
              ) : (
                "-"
              )}
            </td>
          </Link>
        </>
      )}
    </>
  );
};
export default TrackListCountdown;
