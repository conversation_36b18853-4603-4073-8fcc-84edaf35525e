import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  TableSortLabel,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import { config } from "../../helpers/config";
import FileUploader from "../../library/common/components/FileUploader";
import DeleteIcon from "@mui/icons-material/Delete";
// import "../teamsport.scss";
import "./expertTips.scss";
import moment from "moment-timezone";
import { LocalizationProvider, DesktopDatePicker } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { parseISO } from "date-fns";
import { IconButton } from "@mui/material";
import TodayIcon from "@mui/icons-material/Today";
import DateFnsUtils from "@date-io/date-fns";
import { identifiers } from "../../library/common/constants";
import _, { includes } from "lodash";
import { fetchFromStorage } from "../../library/utilities";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import he from "he";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
const statusOption = [
  {
    label: "draft",
    value: "draft",
  },
  {
    label: "published",
    value: "published",
  },
];
class ExpertTips extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      expertTipsValues: {
        startDate: moment(Date()).format("YYYY-MM-DD"),
        modalTrackId: null,
        keyComments: "",
        bestBet: "",
        betprice: "",
        modalTrackBetRace: null,
        modalBetRunnerId: null,
        bestEachWay: "",
        eachWayPrice: "",
        modalTrackEachWayRace: null,
        modalTrackWayRunnerId: null,
        bestLay: "",
        layPrice: "",
        modalTrackLayRace: null,
        modalTrackLayRunnerId: null,
        wpCategoryId: [],
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      ExpertTipsList: [],
      ExpertTipsCount: 0,
      providersDetails: [],
      selectedProvider: 0,
      serachValue: "",
      SelectedSport: null,
      AllSportsOption: [],
      SelectedUsers: null,
      AllUserOption: [],
      sortDate: null,
      startDateOpen: false,
      selectedModalSport: null,
      modalSportsOption: [],
      selectedSportObj: null,
      isTrackLoading: false,
      isTrackRaceLoading: false,
      isTrackAllRaceLoading: false,
      isBestBetRaceRunnerLoading: false,
      isEachWayRaceRunnerLoading: false,
      isLayRaceRunnerLoading: false,
      TrackData: [],
      TrackAllRaceData: [],
      TrackRaceData: [],
      BetRunnerData: [],
      EachWayRunnerData: [],
      FilteredEachWayRunnerData: [],
      LayRunnerData: [],
      FilteredLayRunnerData: [],
      selectedValues: {},
      SelectedId: "",
      errorStartDate: "",
      errorModalSport: "",
      errorTrack: "",
      errorKeyComments: "",
      errorBestBet: "",
      errorBetprice: "",
      errorBetRace: "",
      errorBetRaceRunner: "",
      errorBestEachWay: "",
      errorEachWayPrice: "",
      errorEachWayRace: "",
      errorEachWayRaceRunner: "",
      errorBestLay: "",
      errorLayPrice: "",
      errorLayRace: "",
      errorLayRaceRunner: "",
      errorRace: "",
      errorWpCreds: "",
      errorStateCode: "",
      tipsModalDetailsOpen: false,
      tipsModalDetails: {},
      tipsModalDetailsIsLoading: false,
      sortType: "id",
      sortId: false,
      sortEventName: true,
      sortEventDate: true,
      sortSportType: true,
      sortUser: true,
      defaultImage: [],
      defaultUploadImage: "",
      wpCategoryLoading: false,
      wpCategoryData: [],
      errorModalwpCategory: "",
      selectedStatus: "draft",
      runnerError: "",
      runnerErrorObject: {},
      runnerErrorCheckbox: [],
    };
  }

  // handleChange = (raceId, selectedOptions) => {
  //   this.setState((prevState) => ({
  //     selectedValues: {
  //       ...prevState?.selectedValues,
  //       [raceId]: selectedOptions?.map((option) => option?.value),
  //     },
  //   }));
  // };

  handleChange = (raceId, selectedOptions) => {
    this.setState((prevState) => {
      const updatedSelectedValues = { ...prevState?.selectedValues };
      if (selectedOptions && selectedOptions?.length > 0) {
        updatedSelectedValues[raceId] = selectedOptions?.map(
          (option) => option?.value
        );
      } else {
        delete updatedSelectedValues[raceId];
      }
      return {
        selectedValues: updatedSelectedValues,
      };
    });
  };

  componentDidMount() {
    this.fetchAllSports();
    this.fetchAllUser();
    this.fetchAllEvent(0, null, null, "", this.state?.sortType, false, null);
    this.handleWpError();
  }

  componentDidUpdate(prevProps, prevState) {
    let {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      selectedValues,
      BetRunnerData,
      expertTipsValues,
      EachWayRunnerData,
      LayRunnerData,
    } = this.state;
    if (prevState.offset !== offset) {
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        sortType,
        sortData,
        SelectedUsers
      );
    }
    if (prevState.selectedValues !== selectedValues) {
      if (BetRunnerData?.length > 0 && expertTipsValues?.modalTrackBetRace) {
        this.setState({
          expertTipsValues: {
            ...expertTipsValues,
            modalBetRunnerId:
              selectedValues[expertTipsValues?.modalTrackBetRace]?.[0],
          },
        });
      }
      if (
        EachWayRunnerData?.length > 0 &&
        expertTipsValues?.modalTrackEachWayRace
      ) {
        const filterData = EachWayRunnerData?.filter((runner) =>
          selectedValues[expertTipsValues?.modalTrackEachWayRace]?.includes(
            runner?.value
          )
        );
        this.setState({
          FilteredEachWayRunnerData: filterData,
        });
      }
      if (LayRunnerData?.length > 0 && expertTipsValues?.modalTrackLayRace) {
        const filterData = LayRunnerData?.filter(
          (runner) =>
            runner?.value !==
            selectedValues[expertTipsValues?.modalTrackLayRace]?.[0]
        );
        this.setState({
          FilteredLayRunnerData: filterData,
        });
      }
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllEvent(0, null, null, "", "id", false, null);
      this.fetchAllSports();
      this.setState({
        offset: 0,
        currentPage: 1,
      });
    }
  }
  handleWpError = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/expertTips/errorHandled`
      );
      if (status === 200) {
        this.setState({
          errorWpCreds: "",
        });
      } else {
      }
    } catch (err) {
      this.setState({
        errorWpCreds: err?.response?.data?.message,
      });
      this.setActionMessage(
        true,
        "Error",
        err?.response?.data?.message,
        "wordpresserror"
      );
    }
  };
  handleStateCodeError = async (trackId) => {
    try {
      const { status, data } = await axiosInstance.get(
        `/expertTips/errorHandled?trackId=${trackId}`
      );
      if (status === 200) {
        this.setState({
          errorStateCode: "",
        });
      } else {
      }
    } catch (err) {
      this.setState({
        errorStateCode: err?.response?.data?.message,
      });
    }
  };
  fetchAllEvent = async (page, date, sportId, search, type, order, userId) => {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const passApi = `/expertTips/getAllTips?limit=${rowPerPage}&offset=${page}&timezone=${timezone}&date=${
        date ? date : ""
      }&SportId=${sportId ? sportId : ""}&search=${search}&orderBy=${
        type ? type : ""
      }&sort=${order ? "ASC" : "DESC"}&UserId=${
        userId && userId != null ? userId : ""
      }`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          ExpertTipsList: data?.result?.rows,
          isLoading: false,
          ExpertTipsCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  };

  fetchSingleEvent = async (id, eachWayRaceId, layRaceId) => {
    this.setState({ tipsModalDetailsIsLoading: true });
    try {
      const passApi = `/expertTips/admin/get/${id}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          tipsModalDetailsIsLoading: false,
        });
        let newdata = [];
        let TrackAllRaceObject = (data?.result?.races || []).reduce(
          (acc, item) => {
            const key = item?.RaceId;
            const value = item?.runners?.map((option) => option?.id) || [];
            if (key !== undefined) {
              acc[key] = value;
            }
            return acc;
          },
          {}
        );
        this.setState({
          selectedValues: TrackAllRaceObject,
          tipsModalDetails: data?.result,
        });
        this.eachWayRaceRunner(eachWayRaceId, TrackAllRaceObject);
        this.layRaceRunner(layRaceId, TrackAllRaceObject);
      } else {
        this.setState({
          isLoading: false,
          tipsModalDetailsIsLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
        tipsModalDetailsIsLoading: false,
      });
    }
  };

  async fetchAllSports() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      URLS.sports + `?sportTypeId=1`
    );
    if (status === 200) {
      let newdata = [];
      let sportData = data.result?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All Races",
        value: 0,
      };
      const idOrder = { 1: 0, 2: 2, 3: 1 };
      this.setState({
        AllSportsOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),
        modalSportsOption: newdata?.sort((a, b) => {
          return (idOrder[a?.value] || 0) - (idOrder[b?.value] || 0);
        }),
        // isLoading: false,
      });
    }
  }

  async fetchAllUser() {
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(`/expertTips/users`);
    if (status === 200) {
      let newdata = [];
      let sportData = data.users?.map((item) => {
        newdata.push({
          label: item?.firstName + " " + item?.lastName,
          value: item?.id,
        });
      });
      let alldatas = {
        label: "All User",
        value: 0,
      };
      let alldata = [alldatas, ...newdata];
      this.setState({
        AllUserOption: [alldatas, ...newdata]?.sort((a, b) => {
          return a.label > b.label ? 1 : -1;
        }),

        // isLoading: false,
      });
    }
  }

  handlesportchange = (e) => {
    const {
      offset,
      sortDate,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      SelectedSport: e?.value,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      0,
      sortDate,
      e?.value,
      serachValue,
      sortType,
      sortData,
      SelectedUsers
    );
  };

  handleUserchange = (e) => {
    const {
      offset,
      sortDate,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedSport,
    } = this.state;
    this.setState({
      SelectedUsers: e?.value,
      currentPage: 1,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      0,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortData,
      e?.value
    );
  };

  handalValidate = () => {
    let {
      expertTipsValues,
      selectedModalSport,
      selectedValues,
      TrackAllRaceData,
      errorStateCode,
      runnerError,
    } = this.state;
    let flag = true;

    if (
      expertTipsValues?.startDate === "" ||
      expertTipsValues?.startDate === null
    ) {
      flag = false;
      this.setState({
        errorStartDate: "This field is mandatory",
      });
    } else {
      this.setState({
        errorStartDate: "",
      });
    }
    if (selectedModalSport === null) {
      flag = false;
      this.setState({
        errorModalSport: "This field is mandatory",
      });
    } else {
      this.setState({
        errorModalSport: "",
      });
    }
    // if (expertTipsValues?.wpCategoryId?.length === 0) {
    //   flag = false;
    //   this.setState({
    //     errorModalwpCategory: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorModalwpCategory: "",
    //   });
    // }
    if (expertTipsValues?.modalTrackId === null) {
      flag = false;
      this.setState({
        errorTrack: "This field is mandatory",
      });
    } else {
      this.setState({
        errorTrack: "",
      });
    }
    // if (Boolean(errorStateCode)) {
    //   flag = false;
    // }
    if (
      expertTipsValues?.keyComments?.trim() === "" ||
      expertTipsValues?.keyComments === null ||
      expertTipsValues?.keyComments?.trim() === "<p><br></p>"
    ) {
      flag = false;
      this.setState({
        errorKeyComments: "This field is mandatory",
      });
    } else {
      this.setState({
        errorKeyComments: "",
      });
    }
    if (
      expertTipsValues?.bestBet?.trim() === "" ||
      expertTipsValues?.bestBet === null ||
      expertTipsValues?.bestBet?.trim() === "<p><br></p>"
    ) {
      flag = false;
      this.setState({
        errorBestBet: "This field is mandatory",
      });
    } else {
      this.setState({
        errorBestBet: "",
      });
    }
    // if (expertTipsValues?.betprice === "") {
    //   flag = false;
    //   this.setState({
    //     errorBetprice: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorBetprice: "",
    //   });
    // }
    if (
      expertTipsValues?.bestBet?.trim()?.length > 0 ||
      !expertTipsValues?.bestBet
    ) {
      if (expertTipsValues?.modalTrackBetRace === null) {
        flag = false;
        this.setState({
          errorBetRace: "This field is mandatory",
        });
      } else {
        this.setState({
          errorBetRace: "",
        });
      }
      if (expertTipsValues?.modalTrackBetRace) {
        if (expertTipsValues?.modalBetRunnerId === null) {
          flag = false;
          this.setState({
            errorBetRaceRunner: "This field is mandatory",
          });
        } else {
          this.setState({
            errorBetRaceRunner: "",
          });
        }
      }
    }

    // if (expertTipsValues?.bestEachWay === "") {
    //   flag = false;
    //   this.setState({
    //     errorBestEachWay: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorBestEachWay: "",
    //   });
    // }
    // if (expertTipsValues?.eachWayPrice === "") {
    //   flag = false;
    //   this.setState({
    //     errorEachWayPrice: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorEachWayPrice: "",
    //   });
    // }
    if (expertTipsValues?.bestEachWay?.trim()?.length > 0) {
      if (expertTipsValues?.modalTrackEachWayRace === null) {
        flag = false;
        this.setState({
          errorEachWayRace: "This field is mandatory",
        });
      } else {
        this.setState({
          errorEachWayRace: "",
        });
      }
      if (expertTipsValues?.modalTrackEachWayRace) {
        if (expertTipsValues?.modalTrackWayRunnerId === null) {
          flag = false;
          this.setState({
            errorEachWayRaceRunner: "This field is mandatory",
          });
        } else {
          this.setState({
            errorEachWayRaceRunner: "",
          });
        }
      }
    }

    // if (expertTipsValues?.bestLay === "") {
    //   flag = false;
    //   this.setState({
    //     errorBestLay: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorBestLay: "",
    //   });
    // }
    // if (expertTipsValues?.layPrice === "") {
    //   flag = false;
    //   this.setState({
    //     errorLayPrice: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorLayPrice: "",
    //   });
    // }
    if (expertTipsValues?.bestLay?.trim()?.length > 0) {
      if (expertTipsValues?.modalTrackLayRace === null) {
        flag = false;
        this.setState({
          errorLayRace: "This field is mandatory",
        });
      } else {
        this.setState({
          errorLayRace: "",
        });
      }
      if (expertTipsValues?.modalTrackLayRace) {
        if (expertTipsValues?.modalTrackLayRunnerId === null) {
          flag = false;
          this.setState({
            errorLayRaceRunner: "This field is mandatory",
          });
        } else {
          this.setState({
            errorLayRaceRunner: "",
          });
        }
      }
    }

    if (Object.keys(selectedValues)?.length === 0) {
      flag = false;
      this.setState({
        errorRace: "This field is mandatory",
      });
    } else if (
      Object.keys(selectedValues)?.length !== TrackAllRaceData?.length
    ) {
      flag = false;
      this.setState({
        errorRace: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRace: "",
      });
    }
    const runnerErrorObj = this.fetchRunnerLengthError();
    const isRunnerError = Object.values(runnerErrorObj)?.some(
      (value) => value === true
    );
    this.setState({
      runnerErrorObject: runnerErrorObj,
    });

    if (isRunnerError) {
      flag = false;
      this.setState({
        runnerError:
          "You have selected less than 4 runners. Tick to continue - there are less than 4 runners for this race",
      });
    } else {
      this.setState({
        runnerError: "",
      });
    }

    return flag;
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      selectedModalSport: null,
      TrackData: [],
      TrackRaceData: [],
      BetRunnerData: [],
      EachWayRunnerData: [],
      FilteredEachWayRunnerData: [],
      LayRunnerData: [],
      FilteredLayRunnerData: [],
      TrackAllRaceData: [],
      selectedValues: {},
      expertTipsValues: {
        startDate: moment(Date()).format("YYYY-MM-DD"),
        modalTrackId: null,
        keyComments: "",
        bestBet: "",
        betprice: "",
        modalTrackBetRace: null,
        modalBetRunnerId: null,
        bestEachWay: "",
        eachWayPrice: "",
        modalTrackEachWayRace: null,
        modalTrackWayRunnerId: null,
        bestLay: "",
        layPrice: "",
        modalTrackLayRace: null,
        modalTrackLayRunnerId: null,
        wpCategoryId: [],
      },
      errorStartDate: "",
      errorModalSport: "",
      errorTrack: "",
      errorKeyComments: "",
      errorBestBet: "",
      errorBetprice: "",
      errorBetRace: "",
      errorBetRaceRunner: "",
      errorBestEachWay: "",
      errorEachWayPrice: "",
      errorEachWayRace: "",
      errorEachWayRaceRunner: "",
      errorBestLay: "",
      errorLayPrice: "",
      errorLayRace: "",
      errorLayRaceRunner: "",
      errorRace: "",
      errorStateCode: "",
      defaultImage: [],
      defaultUploadImage: "",
      errorModalwpCategory: "",
      runnerError: "",
      runnerErrorObject: {},
      runnerErrorCheckbox: [],
      keyCommentContent: "",
      bestBestCommentContent: "",
    });
  };

  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    // this.wpCategory(item);
    if (type === "edit") {
      this.fetchSingleEvent(item?.id, item?.wayRaceId, item?.layRaceId);
      this.fetchAllTrack(
        moment(item?.Event?.eventDate).tz(timezone).format("YYYY-MM-DD"),
        item?.Sport?.id
      );
      this.eventRaceList(item?.Event?.id);
      this.eventAllRaceList(item?.Event?.id);
      this.bestBetRaceRunner(item?.betRaceId, "first");

      this.layRaceRunner(item?.layRaceId);
      const htmlKeyCommentString = he.decode(String(item?.keyComment));
      if (typeof htmlKeyCommentString === "string") {
        this.setState({
          keyCommentContent: htmlKeyCommentString,
        });
      }

      const htmlBestBetCommentString = he.decode(String(item?.betComment));
      if (typeof htmlBestBetCommentString === "string") {
        this.setState({
          bestBestCommentContent: htmlBestBetCommentString,
        });
      }

      this.setState({
        SelectedId: item?.id,
        isEditMode: true,
        selectedModalSport: item?.Sport?.id,
        defaultUploadImage: item?.banner_image,
        selectedStatus: item?.status,
        expertTipsValues: {
          startDate: moment(item?.Event?.eventDate)
            .tz(timezone)
            .format("YYYY-MM-DD"),
          modalTrackId: item?.Event?.id,
          keyComments: item?.keyComment,
          bestBet: item?.betComment,
          betprice: item?.betPrice,
          modalTrackBetRace: item?.betRaceId,
          modalBetRunnerId: item?.betParticipantId,
          bestEachWay: item?.wayComment,
          eachWayPrice: item?.wayPrice,
          modalTrackEachWayRace: item?.wayRaceId,
          modalTrackWayRunnerId: item?.wayParticipantId,
          bestLay: item?.layComment,
          layPrice: item?.layPrice,
          modalTrackLayRace: item?.layRaceId,
          modalTrackLayRunnerId: item?.layParticipantId,
          // wpCategoryId: item?.wpCategoryIds.split(",")?.map(Number),
        },
      });
    } else {
      this.fetchAllTrack(moment().tz(timezone).format("YYYY-MM-DD"), 1);
      this.setState({
        isEditMode: false,
        selectedModalSport: 1,
        selectedValues: {},
        defaultUploadImage: "",
        errorStateCode: "",
        expertTipsValues: {
          startDate: moment(Date()).format("YYYY-MM-DD"),
          modalTrackId: null,
          keyComments: "",
          bestBet: "",
          betprice: "",
          modalTrackBetRace: null,
          modalBetRunnerId: null,
          bestEachWay: "",
          eachWayPrice: "",
          modalTrackEachWayRace: null,
          modalTrackWayRunnerId: null,
          bestLay: "",
          layPrice: "",
          modalTrackLayRace: null,
          modalTrackLayRunnerId: null,
          wpCategoryId: [],
          keyCommentContent: "",
          bestBestCommentContent: "",
        },
      });
    }
  };

  tipsDetailsModalOpen = (item) => {
    this.setState({ tipsModalDetailsOpen: true });
    this.fetchSingleEvent(item?.id);
  };

  toggleTipsDetailsModalOpen = () => {
    this.setState({ tipsModalDetailsOpen: false, tipsModalDetails: {} });
  };

  setActionMessage = (display = false, type = "", message = "", isWperror) => {
    const clearMessageBox = () =>
      this.setState({
        messageBox: { display: false, type: "", message: "" },
      });
    this.setState(
      {
        messageBox: { display, type, message },
      },
      isWperror
        ? () => {}
        : () => {
            setTimeout(clearMessageBox, 3000);
          }
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    try {
      const passApi = `/expertTips/delete/${this.state.itemToDelete}?isPostTowp=1`;
      const { status, data } = await axiosInstance.delete(passApi);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
        });
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, ExpertTipsList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };

  clearStartDate = () => {
    const {
      offset,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      sortDate: null,
      startDateOpen: false,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    this.fetchAllEvent(
      offset,
      null,
      SelectedSport,
      serachValue,
      sortType,
      sortData,
      SelectedUsers
    );
  };

  handleSortStartDate = (date) => {
    const {
      offset,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({
      sortDate: date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
    });
    let sortData =
      sortType === "id"
        ? sortId
        : sortType === "eventName"
        ? sortEventName
        : sortType === "eventDate"
        ? sortEventDate
        : sortType === "SportId"
        ? sortSportType
        : sortUser;
    if (
      date === null ||
      moment(date).tz(timezone).format("YYYY-MM-DD") !== "Invalid date"
    ) {
      this.fetchAllEvent(
        0,
        date ? moment(date).tz(timezone).format("YYYY-MM-DD") : null,
        SelectedSport,
        serachValue,
        sortType,
        sortData,
        SelectedUsers
      );
    }
  };

  handleModalsportchange = (e) => {
    const { expertTipsValues, errorModalSport } = this.state;
    this.setState({
      selectedModalSport: e.value,
      errorModalSport: e.value ? "" : errorModalSport,
      TrackData: [],
      TrackRaceData: [],
      BetRunnerData: [],
      EachWayRunnerData: [],
      FilteredEachWayRunnerData: [],
      LayRunnerData: [],
      FilteredLayRunnerData: [],
      TrackAllRaceData: [],
      selectedValues: {},
      errorStateCode: "",
      expertTipsValues: {
        ...this.state.expertTipsValues,
        modalTrackId: null,
        modalTrackBetRace: null,
        modalBetRunnerId: null,
        modalTrackEachWayRace: null,
        modalTrackWayRunnerId: null,
        modalTrackLayRace: null,
        modalTrackLayRunnerId: null,
      },
    });
    // this.setState({
    //   selectedModalSportObj: e.value,
    // });
    this.fetchAllTrack(expertTipsValues?.startDate, e.value);
  };

  async fetchAllTrack(date, sportId) {
    this.setState({ isTrackLoading: true });
    const passApi = `expertTips/getDropDown?sportId=${sportId}&date=${date}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackLoading: false });
      let newdata = [];
      let Track = data?.result?.map((item) => {
        newdata.push({
          label: item?.eventName,
          value: item?.id,
          trackId: item?.trackId,
        });
      });
      let filterData = _.unionBy(this.state?.TrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });
      this.setState({
        TrackData: finalData,
      });
    } else {
      this.setState({ isTrackLoading: false });
    }
  }

  async eventRaceList(eventId) {
    this.setState({ isTrackRaceLoading: true });
    const passApi = `expertTips/getDropDown?eventId=${eventId}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackRaceLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label: "R" + item?.raceNumber + " " + item?.raceName,
          value: item?.id,
        });
      });

      this.setState({
        TrackRaceData: newdata,
      });
    } else {
      this.setState({ isTrackRaceLoading: false });
    }
  }

  async eventAllRaceList(eventId) {
    this.setState({ isTrackAllRaceLoading: true });
    const passApi = `expertTips/getDropDown?eventId=${eventId}&type=race&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isTrackAllRaceLoading: false });

      let newdata = [];
      const sortingRace = data?.result?.sort(
        (a, b) => a.raceNumber - b.raceNumber
      );
      let TrackAllRace = data?.result?.map((item) => {
        newdata.push({
          raceId: item?.id,
          raceNumber: "Race" + item?.raceNumber,
          runner: item?.runner?.map((obj) => {
            return {
              label:
                obj?.runnerNumber +
                "." +
                " " +
                obj?.animal?.name +
                " " +
                "(" +
                obj?.barrierNumber +
                ")",
              value: obj?.id,
            };
          }),
        });
      });
      this.setState({
        TrackAllRaceData: newdata,
      });
    } else {
      this.setState({ isTrackAllRaceLoading: false });
    }
  }

  async bestBetRaceRunner(RaceId, Isfirst) {
    const { selectedValues, expertTipsValues } = this.state;
    this.setState({ isBestBetRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isBestBetRaceRunnerLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      if (Isfirst == "first") {
        this.setState({
          BetRunnerData: newdata,
        });
      } else {
        this.setState({
          BetRunnerData: newdata,
          expertTipsValues: {
            ...expertTipsValues,
            modalBetRunnerId: selectedValues[RaceId]?.[0],
            modalTrackBetRace: RaceId,
            modalTrackEachWayRace: null,
            modalTrackLayRace: null,
            modalTrackWayRunnerId: null,
            modalTrackLayRunnerId: null,
          },
        });
      }
    } else {
      this.setState({ isBestBetRaceRunnerLoading: false });
    }
  }

  async eachWayRaceRunner(RaceId, selectedRaceValue) {
    const { selectedValues } = this.state;
    this.setState({ isEachWayRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isEachWayRaceRunnerLoading: false });

      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      let filterData;
      if (selectedRaceValue) {
        filterData = newdata?.filter((runner) =>
          selectedRaceValue[RaceId]?.includes(runner?.value)
        );
      } else {
        filterData = newdata?.filter((runner) =>
          selectedValues[RaceId]?.includes(runner?.value)
        );
      }
      this.setState({
        EachWayRunnerData: newdata,
        FilteredEachWayRunnerData: filterData,
      });
    } else {
      this.setState({ isEachWayRaceRunnerLoading: false });
    }
  }

  async layRaceRunner(RaceId, selectedRaceValue) {
    const { selectedValues } = this.state;
    this.setState({ isLayRaceRunnerLoading: true });
    const passApi = `expertTips/getDropDown?raceId=${RaceId}&timezone=${timezone}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ isLayRaceRunnerLoading: false });
      let newdata = [];
      let TrackRace = data?.result?.map((item) => {
        newdata.push({
          label:
            item?.runnerNumber +
            "." +
            " " +
            item?.runnerName +
            " " +
            "(" +
            item?.barrierNumber +
            ")",
          value: item?.id,
        });
      });
      let filterData;
      if (selectedRaceValue) {
        filterData = newdata?.filter(
          (runner) => selectedRaceValue[RaceId]?.[0] !== runner?.value
        );
      } else {
        filterData = newdata?.filter(
          (runner) => selectedValues[RaceId]?.[0] !== runner?.value
        );
      }
      this.setState({
        LayRunnerData: newdata,
        FilteredLayRunnerData: filterData,
      });
    } else {
      this.setState({ isLayRaceRunnerLoading: false });
    }
  }

  async wpCategory() {
    this.setState({ wpCategoryLoading: true });
    const passApi = `/expertTips/getWpCategories`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      this.setState({ wpCategoryLoading: false });
      let newdata = [];
      let categoryId = data?.result?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      this.setState({
        wpCategoryData: newdata,
      });
    } else {
      this.setState({ wpCategoryLoading: false });
    }
  }

  handleSave = async (saveType, statusType) => {
    const {
      expertTipsValues,
      selectedModalSport,
      TrackAllRaceData,
      selectedValues,
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      defaultImage,
      selectedStatus,
    } = this.state;
    const isValidationChecked = saveType === 1 ? this.handalValidate() : true;
    if (isValidationChecked) {
      this.setState({ isLoading: true, isEditMode: false });
      const filterResultData = TrackAllRaceData?.map((race) => ({
        raceId: race?.raceId,
        runnerIds: selectedValues[race?.raceId] || [],
      }));
      // .filter((item) => item?.runnerIds?.length > 0);
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      let payload = {
        EventId: expertTipsValues?.modalTrackId,
        sportId: selectedModalSport,
        betRaceId: expertTipsValues?.modalTrackBetRace,
        betParticipantId: expertTipsValues?.modalBetRunnerId,
        betComment: expertTipsValues?.bestBet
          ? expertTipsValues?.bestBet.trim()
          : null,
        // betPrice: Number(expertTipsValues?.betprice.trim()),
        wayRaceId: expertTipsValues?.modalTrackEachWayRace,
        wayParticipantId: expertTipsValues?.modalTrackWayRunnerId,
        wayComment: expertTipsValues?.bestEachWay
          ? expertTipsValues?.bestEachWay.trim()
          : null,
        // wayPrice: Number(expertTipsValues?.eachWayPrice.trim()),
        layRaceId: expertTipsValues?.modalTrackLayRace,
        layParticipantId: expertTipsValues?.modalTrackLayRunnerId,
        layComment: expertTipsValues?.bestLay
          ? expertTipsValues?.bestLay.trim()
          : null,
        // layPrice: Number(expertTipsValues?.layPrice.trim()),
        keyComment: expertTipsValues?.keyComments
          ? expertTipsValues?.keyComments.trim()
          : null,
        races: filterResultData,
        status: statusType,
        // wpCategoryIds: expertTipsValues?.wpCategoryId?.join(","),
      };
      // if (defaultImage?.length > 0) {
      //   let fileData = await this.setMedia(defaultImage[0]);
      //   if (fileData) {
      //     payload = {
      //       ...payload,
      //       banner_image: config.mediaUrl + fileData?.image?.filePath,
      //     };
      //     this.setState({
      //       defaultUploadImage: config.mediaUrl + fileData?.image?.filePath,
      //     });
      //   }
      // }
      try {
        const { status, data } = await axiosInstance.post(
          `expertTips/create?isPostTowp=${saveType}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
            errorStateCode: "",
            errorStartDate: "",
            errorModalSport: "",
            errorTrack: "",
            errorKeyComments: "",
            errorBestBet: "",
            errorBetprice: "",
            errorBetRace: "",
            errorBetRaceRunner: "",
            errorBestEachWay: "",
            errorEachWayPrice: "",
            errorEachWayRace: "",
            errorEachWayRaceRunner: "",
            errorBestLay: "",
            errorLayPrice: "",
            errorLayRace: "",
            errorLayRaceRunner: "",
            errorRace: "",
            errorStateCode: "",
            runnerError: "",
            runnerErrorObject: {},
            runnerErrorCheckbox: [],
            bestBestCommentContent: "",
            keyCommentContent: "",
          });
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
            errorStateCode: "",
            bestBestCommentContent: "",
            keyCommentContent: "",
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          // isInputModalOpen: false,
          defaultImage: [],
          defaultUploadImage: "",
          errorStateCode: err?.response?.data?.message,
          bestBestCommentContent: "",
          keyCommentContent: "",
        });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async (saveType, statusType) => {
    const {
      expertTipsValues,
      selectedModalSport,
      TrackAllRaceData,
      selectedValues,
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      defaultImage,
      selectedStatus,
    } = this.state;
    const isValidationChecked = saveType === 1 ? this.handalValidate() : true;
    if (isValidationChecked) {
      this.setState({ isLoading: true, isEditMode: true });
      const filterResultData = TrackAllRaceData?.map((race) => ({
        raceId: race?.raceId,
        runnerIds: selectedValues[race?.raceId] || [],
      }));
      let sortData =
        sortType === "id"
          ? sortId
          : sortType === "eventName"
          ? sortEventName
          : sortType === "eventDate"
          ? sortEventDate
          : sortType === "SportId"
          ? sortSportType
          : sortUser;
      let payload = {
        EventId: expertTipsValues?.modalTrackId,
        sportId: selectedModalSport,
        betRaceId: expertTipsValues?.modalTrackBetRace,
        betParticipantId: expertTipsValues?.modalBetRunnerId,
        betComment: expertTipsValues?.bestBet
          ? expertTipsValues?.bestBet.trim()
          : null,
        // betPrice: Number(expertTipsValues?.betprice),
        wayRaceId: expertTipsValues?.modalTrackEachWayRace,
        wayParticipantId: expertTipsValues?.modalTrackWayRunnerId,
        wayComment: expertTipsValues?.bestEachWay
          ? expertTipsValues?.bestEachWay.trim()
          : null,
        // wayPrice: Number(expertTipsValues?.eachWayPrice),
        layRaceId: expertTipsValues?.modalTrackLayRace,
        layParticipantId: expertTipsValues?.modalTrackLayRunnerId,
        layComment: expertTipsValues?.bestLay
          ? expertTipsValues?.bestLay.trim()
          : null,
        // layPrice: Number(expertTipsValues?.layPrice),
        keyComment: expertTipsValues?.keyComments
          ? expertTipsValues?.keyComments.trim()
          : null,
        races: filterResultData,
        status: statusType,
        // wpCategoryIds: expertTipsValues?.wpCategoryId?.join(","),
      };
      // if (defaultImage?.length > 0) {
      //   let fileData = await this.setMedia(defaultImage[0]);
      //   if (fileData) {
      //     payload = {
      //       ...payload,
      //       banner_image: config.mediaUrl + fileData?.image?.filePath,
      //     };
      //     this.setState({
      //       defaultUploadImage: config.mediaUrl + fileData?.image?.filePath,
      //     });
      //   }
      // } else {
      //   payload = {
      //     ...payload,
      //     banner_image:
      //       this.state.defaultUploadImage &&
      //       this.state.defaultUploadImage !== ""
      //         ? this.state.defaultUploadImage
      //         : null,
      //   };
      // }
      try {
        const { status, data } = await axiosInstance.put(
          `/expertTips/update/${SelectedId}?isPostTowp=${saveType}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
            errorStartDate: "",
            errorModalSport: "",
            errorTrack: "",
            errorKeyComments: "",
            errorBestBet: "",
            errorBetprice: "",
            errorBetRace: "",
            errorBetRaceRunner: "",
            errorBestEachWay: "",
            errorEachWayPrice: "",
            errorEachWayRace: "",
            errorEachWayRaceRunner: "",
            errorBestLay: "",
            errorLayPrice: "",
            errorLayRace: "",
            errorLayRaceRunner: "",
            errorRace: "",
            errorStateCode: "",
            runnerError: "",
            runnerErrorObject: {},
            runnerErrorCheckbox: [],
            bestBestCommentContent: "",
            keyCommentContent: "",
          });
          this.fetchAllEvent(
            offset,
            sortDate,
            SelectedSport,
            serachValue,
            sortType,
            sortData,
            SelectedUsers
          );
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          // isInputModalOpen: false,
          defaultImage: [],
          defaultUploadImage: "",
          errorStateCode: err?.response?.data?.message,
        });
        this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleClearClick = () => {
    this.setState({ serachValue: "" });
  };

  sortLabelHandler = (type) => {
    const {
      offset,
      sortDate,
      SelectedSport,
      serachValue,
      SelectedId,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
    } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortId,
        SelectedUsers
      );
      this.setState({
        sortId: !sortId,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "eventName") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortEventName,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: !sortEventName,
        sortEventDate: true,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "eventDate") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortEventDate,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: !sortEventDate,
        sortSportType: true,
        sortUser: true,
        currentPage: 1,
      });
    } else if (type === "SportId") {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortSportType,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: !sortSportType,
        sortUser: true,
        currentPage: 1,
      });
    } else {
      this.fetchAllEvent(
        offset,
        sortDate,
        SelectedSport,
        serachValue,
        type,
        !sortUser,
        SelectedUsers
      );
      this.setState({
        sortId: false,
        sortEventName: true,
        sortEventDate: true,
        sortSportType: true,
        sortUser: !sortUser,
        currentPage: 1,
      });
    }
  };

  handleKeyDown = (event) => {
    var { sortDate, SelectedSport, serachValue, sortType, SelectedUsers } =
      this.state;
    if (event.key === "Enter") {
      this.fetchAllEvent(
        0,
        sortDate,
        SelectedSport,
        serachValue,
        sortType,
        false,
        SelectedUsers
      );
      this.setState({ currentPage: 1 });
    }
  };

  fetchRunnerLengthError = () => {
    const { selectedValues, runnerErrorCheckbox } = this.state;
    let errorObj = {};
    Object.keys(selectedValues).forEach(function (key, index) {
      errorObj[key] =
        selectedValues[key]?.length > 0 && selectedValues[key]?.length < 4
          ? runnerErrorCheckbox?.includes(Number(key))
            ? false
            : true
          : false;
    });
    return errorObj;
  };

  handleKeyCommentContentChange = (content) => {
    const { expertTipsValues, errorRaceDetail } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        keyComments: content,
      },
      errorKeyComments: content?.trim() == "" ? errorRaceDetail : "",
    });
  };

  handleBestBetCommentContentChange = (content) => {
    const { expertTipsValues, errorBestBet } = this.state;
    this.setState({
      expertTipsValues: {
        ...expertTipsValues,
        bestBet: content,
      },
      errorBestBet: content?.trim() == "" ? errorBestBet : "",
    });
  };
  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      ExpertTipsList,
      ExpertTipsCount,
      serachValue,
      AllSportsOption,
      SelectedSport,
      sortDate,
      startDateOpen,
      expertTipsValues,
      modalSportsOption,
      selectedModalSport,
      isTrackLoading,
      isTrackRaceLoading,
      isBestBetRaceRunnerLoading,
      isEachWayRaceRunnerLoading,
      isLayRaceRunnerLoading,
      TrackData,
      TrackRaceData,
      BetRunnerData,
      EachWayRunnerData,
      FilteredEachWayRunnerData,
      LayRunnerData,
      FilteredLayRunnerData,
      TrackAllRaceData,
      selectedValues,
      errorStartDate,
      errorModalSport,
      errorTrack,
      errorKeyComments,
      errorBestBet,
      errorBetprice,
      errorBetRace,
      errorBetRaceRunner,
      errorBestEachWay,
      errorEachWayPrice,
      errorEachWayRace,
      errorEachWayRaceRunner,
      errorBestLay,
      errorLayPrice,
      errorLayRace,
      errorLayRaceRunner,
      errorRace,
      tipsModalDetailsOpen,
      tipsModalDetails,
      tipsModalDetailsIsLoading,
      sortType,
      sortId,
      sortEventName,
      sortEventDate,
      sortSportType,
      sortUser,
      SelectedUsers,
      AllUserOption,
      defaultImage,
      defaultUploadImage,
      wpCategoryLoading,
      wpCategoryData,
      errorModalwpCategory,
      errorStateCode,
      selectedStatus,
      runnerError,
      runnerErrorObject,
      runnerErrorCheckbox,
      keyCommentContent,
      bestBestCommentContent,
    } = this.state;
    const pageNumbers = [];
    if (ExpertTipsCount > 0) {
      for (let i = 1; i <= Math.ceil(ExpertTipsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    const renderError = (race) => {
      if (errorRace && errorRace !== "") {
        if (Object.keys(selectedValues)?.length == 0) {
          return (
            <p className="errorText" style={{ margin: "0px" }}>
              {errorRace}
            </p>
          );
        } else if (
          Object.keys(selectedValues).includes(race?.raceId?.toString())
        ) {
          return null;
        } else {
          return (
            <p className="errorText" style={{ margin: "0px" }}>
              {errorRace}
            </p>
          );
        }
      } else {
        return (
          <p className="errorText" style={{ margin: "0px" }}>
            {""}
          </p>
        );
      }

      // if (Object.keys(selectedValues).includes(race?.raceId)) {
      //   return (
      //     <p className="errorText" style={{ margin: "0px 0 0 0" }}>
      //       {""}
      //     </p>
      //   );
      // } else {
      //   return (
      //     <p className="errorText" style={{ margin: "0px 0 0 0" }}>
      //       {"errorRace"}
      //     </p>
      //   );
      // }
    };

    const user = fetchFromStorage(identifiers.user);
    const runnerErrorObj = this.fetchRunnerLengthError();
    return (
      <>
        {!isInputModalOpen ? (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              {/* <Paper className="pageWrapper"> */}
              {messageBox?.display && (
                <ActionMessage
                  message={messageBox?.message}
                  type={messageBox?.type}
                  styleClass={messageBox?.styleClass}
                />
              )}
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>

                  <Typography className="active_p">Expert Tips</Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={3}>
                  <Typography variant="h1" align="left">
                    Expert Tips
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={9}
                  className="admin-filter-wrap admin-fixture-wrap"
                >
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopDatePicker
                      clearable
                      // onClear={() => this.clearStartDate()}
                      // open={startDateOpen}
                      // onOpen={() => this.setState({ startDateOpen: true })}
                      // onClose={() => this.setState({ startDateOpen: false })}
                      autoOk
                      // disableToolbar
                      // variant="inline"
                      format="dd/MM/yyyy"
                      placeholder="Start Date"
                      margin="normal"
                      id="date-picker-inline"
                      inputVariant="outlined"
                      value={sortDate ? parseISO(sortDate) : null}
                      slots={{
                        openPickerIcon: TodayIcon,
                      }}
                      slotProps={{
                        field: {
                          placeholder: "DD/MM/YYYY",
                          clearable: true,
                          // onClear: () => this.clearStartDate(),
                        },
                      }}
                      onChange={(e) => this.handleSortStartDate(e)}
                      KeyboardButtonProps={{
                        "aria-label": "change date",
                      }}
                      className="date-picker-fixture expert-tips-date-picker"
                      style={{ margin: "0px 10px 0px 0px", width: "24%" }}
                    />
                  </LocalizationProvider>
                  <Select
                    className="React teamsport-select external-select"
                    classNamePrefix="select"
                    placeholder="Select Race"
                    value={AllSportsOption?.find((item) => {
                      return item?.value == SelectedSport;
                    })}
                    //   isLoading={isLoading}
                    onChange={(e) => this.handlesportchange(e)}
                    options={AllSportsOption}
                  />
                  <TextField
                    placeholder="Search "
                    size="small"
                    variant="outlined"
                    className="event-search"
                    onKeyDown={(e) => this.handleKeyDown(e)}
                    value={serachValue}
                    onChange={(e) => {
                      this.setState({ serachValue: e.target.value });
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <img src={SearchIcons} alt="icon" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          {serachValue && (
                            <IconButton
                              onClick={() => this.handleClearClick()}
                              edge="end"
                              style={{ minWidth: "unset" }}
                              size="large"
                            >
                              <CancelIcon />
                            </IconButton>
                          )}
                        </InputAdornment>
                      ),
                    }}
                    style={{
                      background: "#ffffff",
                    }}
                  />
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455c7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                    }}
                    onClick={() => {
                      this.fetchAllEvent(
                        0,
                        sortDate,
                        SelectedSport,
                        serachValue,
                        sortType,
                        false,
                        SelectedUsers
                      );
                      this.setState({ currentPage: 1 });
                    }}
                  >
                    Search
                  </Button>

                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    className={
                      Boolean(this.state.errorWpCreds) ? "disabled-btn" : ""
                    }
                    onClick={this.inputModal(null, "create")}
                    disabled={Boolean(this.state.errorWpCreds)}
                  >
                    Add New
                  </Button>
                </Grid>
              </Grid>
              {user?.role !== "expertTip" ? (
                <Grid container direction="row" alignItems="center">
                  <Grid item xs={12} className="user-select-wrap cg-15">
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "6px 10px",
                        // marginTop: "5px",
                      }}
                      onClick={() => this.props.navigate(`/tips-of-the-day`)}
                    >
                      Select Tip Of The Day
                    </Button>
                    <Select
                      className="React teamsport-select external-select"
                      classNamePrefix="select"
                      placeholder="Select User"
                      value={AllUserOption?.find((item) => {
                        return item?.value == SelectedUsers;
                      })}
                      //   isLoading={isLoading}
                      onChange={(e) => this.handleUserchange(e)}
                      options={AllUserOption}
                    />
                  </Grid>
                </Grid>
              ) : (
                <></>
              )}

              {isLoading && <Loader />}
              {!isLoading && ExpertTipsList?.length === 0 && (
                <p>No Data Available</p>
              )}

              {!isLoading && ExpertTipsList?.length > 0 && (
                <TableContainer component={Paper}>
                  <Table
                    className="listTable market-error-table"
                    aria-label="simple table"
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ cursor: "pointer" }}
                        >
                          DID
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortId
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("eventName")}
                          style={{ cursor: "pointer" }}
                        >
                          Event Name
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "eventName"
                                ? sortEventName
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("eventDate")}
                          style={{ cursor: "pointer" }}
                        >
                          Event Date
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "eventDate"
                                ? sortEventDate
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        <TableCell>BAW Status</TableCell>
                        <TableCell
                          onClick={() => this.sortLabelHandler("SportId")}
                          style={{ cursor: "pointer" }}
                        >
                          Sport Type
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "SportId"
                                ? sortSportType
                                  ? "asc"
                                  : "desc"
                                : "asc"
                            }
                          />
                        </TableCell>
                        {user?.role !== "expertTip" ? (
                          <TableCell
                            onClick={() => this.sortLabelHandler("UserId")}
                            style={{ cursor: "pointer" }}
                          >
                            User
                            <TableSortLabel
                              active={true}
                              direction={
                                sortType === "UserId"
                                  ? sortUser
                                    ? "asc"
                                    : "desc"
                                  : "asc"
                              }
                            />
                          </TableCell>
                        ) : (
                          <></>
                        )}
                        <TableCell>Tips Details</TableCell>
                        <TableCell style={{ width: "15%" }}>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {ExpertTipsList?.map((item) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={item?.id}
                          >
                            <TableCell>{item?.id} </TableCell>
                            <TableCell>{item?.Event?.eventName}</TableCell>
                            <TableCell>
                              {moment(item?.Event?.eventDate).format(
                                "DD/MM/YYYY hh:mm:ss a"
                              )}
                            </TableCell>
                            <TableCell style={{ textTransform: "capitalize" }}>
                              {item?.customStatus}
                            </TableCell>
                            <TableCell>{item?.Sport?.sportName}</TableCell>
                            {user?.role !== "expertTip" ? (
                              <TableCell>
                                {item?.User?.firstName +
                                  " " +
                                  item?.User?.lastName}
                              </TableCell>
                            ) : (
                              <></>
                            )}
                            <TableCell>
                              <Button
                                className="table-btn"
                                variant="contained"
                                style={{
                                  fontSize: "14px",
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  fontWeight: "400",
                                  textTransform: "none",
                                  width: "max-content",
                                }}
                                onClick={() => {
                                  this.tipsDetailsModalOpen(item);
                                }}
                              >
                                Tips Details
                              </Button>
                            </TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className={`table-btn edit-btn ${
                                  moment(item?.Event?.eventDate).isBefore(
                                    moment(),
                                    "day"
                                  ) || Boolean(this.state.errorWpCreds)
                                    ? "disabled-btn"
                                    : ""
                                }`}
                                disabled={
                                  moment(item?.Event?.eventDate).isBefore(
                                    moment(),
                                    "day"
                                  ) || Boolean(this.state.errorWpCreds)
                                }
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(item?.id)}
                                style={{ cursor: "pointer", minWidth: "0px" }}
                                className={
                                  Boolean(this.state.errorWpCreds)
                                    ? "disabled-btn table-btn delete-btn"
                                    : "table-btn delete-btn"
                                }
                                disabled={Boolean(this.state.errorWpCreds)}
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                ExpertTipsCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              {/* </Paper> */}

              <ShowModal
                isModalOpen={isModalOpen}
                onClose={this.toggleModal}
                Content="Are you sure you want to delete?"
                onOkayLabel="Yes"
                onOkay={this.deleteItem}
                onCancel={this.toggleModal}
              />
            </Grid>
          </Grid>
        ) : (
          <Grid container className="page-content adminLogin">
            <Grid item xs={12} className="pageWrapper">
              <Box className="bredcrumn-wrap">
                <Breadcrumbs
                  separator="/"
                  aria-label="breadcrumb"
                  className="breadcrumb"
                >
                  <Link underline="hover" color="inherit" to="/dashboard">
                    Home
                  </Link>
                  <Typography className="active_p">Expert Tips</Typography>
                </Breadcrumbs>
              </Box>
              <Grid container direction="row" alignItems="center">
                <Grid item xs={4}>
                  <Typography variant="h1" align="left">
                    {!isEditMode
                      ? "Create New Expert Tips"
                      : "Edit Expert Tips"}
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={8}
                  style={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="contained"
                    style={{
                      backgroundColor: "#4455C7",
                      color: "#fff",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      padding: "6px 10px",
                      // marginTop: "5px",
                    }}
                    onClick={this.toggleInputModal}
                  >
                    Back
                  </Button>
                </Grid>
              </Grid>
              <Box className="expert-tips-modal">
                <Grid container>
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={4}
                      className="date-time-picker-wrap"
                      style={{ marginBottom: "15px" }}
                    >
                      <label className="modal-label">
                        {" "}
                        Select Date <span className="color-red">*</span>
                      </label>
                      <LocalizationProvider dateAdapter={AdapterDateFns}>
                        <DesktopDatePicker
                          variant="inline"
                          inputVariant="outlined"
                          ampm={false}
                          value={
                            expertTipsValues?.startDate
                              ? parseISO(
                                  moment(expertTipsValues?.startDate)
                                    .tz(timezone)
                                    .format("YYYY-MM-DD")
                                )
                              : null
                          }
                          onChange={(e) => {
                            this.fetchAllTrack(
                              moment(e).tz(timezone).format("YYYY-MM-DD"),
                              1
                            );
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                startDate: moment(e)
                                  .tz(timezone)
                                  .format("YYYY-MM-DD"),
                                modalTrackId: null,
                                modalTrackBetRace: null,
                                modalTrackEachWayRace: null,
                                modalTrackLayRace: null,
                                modalBetRunnerId: null,
                                modalTrackWayRunnerId: null,
                                modalTrackLayRunnerId: null,
                              },
                              errorStateCode: "",
                              selectedModalSport: 1,
                              TrackData: [],
                              TrackRaceData: [],
                              BetRunnerData: [],
                              EachWayRunnerData: [],
                              FilteredEachWayRunnerData: [],
                              LayRunnerData: [],
                              FilteredLayRunnerData: [],
                              TrackAllRaceData: [],
                              selectedValues: {},
                            });
                          }}
                          autoOk={true}
                          format="yyyy/MM/dd"
                          className="date-time-picker"
                          disablePast={!isEditMode}
                        />
                      </LocalizationProvider>
                      {errorStartDate ? (
                        <p
                          className="errorText"
                          style={{ margin: "4px 0 0 0" }}
                        >
                          {errorStartDate}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid item xs={4} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        {" "}
                        Race Type <span className="color-red">*</span>
                      </label>
                      <Select
                        className="React teamsport-select external-select"
                        classNamePrefix="select"
                        placeholder="Select Race"
                        menuPosition="fixed"
                        value={
                          selectedModalSport &&
                          modalSportsOption?.find((item) => {
                            return item?.value == selectedModalSport;
                          })
                        }
                        //   isLoading={isLoading}
                        onChange={(e) => this.handleModalsportchange(e)}
                        options={modalSportsOption}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorModalSport ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorModalSport}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid item xs={4} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        {" "}
                        Track <span className="color-red">*</span>
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Track"
                        isDisabled={!selectedModalSport}
                        isLoading={isTrackLoading}
                        value={
                          expertTipsValues?.modalTrackId &&
                          TrackData?.find((item) => {
                            return (
                              item?.value == expertTipsValues?.modalTrackId
                            );
                          })
                        }
                        options={TrackData}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalTrackId: e?.value,
                              modalTrackBetRace: null,
                              modalTrackEachWayRace: null,
                              modalTrackLayRace: null,
                              modalBetRunnerId: null,
                              modalTrackWayRunnerId: null,
                              modalTrackLayRunnerId: null,
                            },
                            TrackRaceData: [],
                            BetRunnerData: [],
                            EachWayRunnerData: [],
                            FilteredEachWayRunnerData: [],
                            LayRunnerData: [],
                            FilteredLayRunnerData: [],
                            TrackAllRaceData: [],
                            selectedValues: {},
                            errorTrack: e?.value ? "" : errorTrack,
                            errorStateCode: "",
                          });
                          this.eventRaceList(e?.value);
                          this.eventAllRaceList(e?.value);
                          // this.state.selectedStatus === "published" &&
                          //   this.handleStateCodeError(e?.trackId);
                        }}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorTrack ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorTrack}
                        </p>
                      ) : (
                        ""
                      )}
                      {/* {errorStateCode ? (
                      <p
                        className="errorText"
                        style={{ margin: "0px 0 0 0" }}
                      >
                        {errorStateCode}
                      </p>
                    ) : (
                      ""
                    )} */}
                    </Grid>
                    {/* </Box>
                <Box className="race-runner-wrap mb-8"> */}
                    {/* <Grid item xs={4} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">Status</label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        placeholder="Select Status"
                        value={statusOption?.find((item) => {
                          return item?.label == selectedStatus;
                        })}
                        //   isLoading={isLoading}
                        onChange={(e) =>
                          this.setState({
                            selectedStatus: e?.value,
                            // errorStateCode: "",
                            // expertTipsValues: {
                            //   ...expertTipsValues,
                            //   modalTrackId: null,
                            //   modalTrackBetRace: null,
                            //   modalTrackEachWayRace: null,
                            //   modalTrackLayRace: null,
                            //   modalBetRunnerId: null,
                            //   modalTrackWayRunnerId: null,
                            //   modalTrackLayRunnerId: null,
                            // },
                            // TrackRaceData: [],
                            // BetRunnerData: [],
                            // EachWayRunnerData: [],
                            // FilteredEachWayRunnerData: [],
                            // FilteredLayRunnerData: [],
                            // LayRunnerData: [],
                            // TrackAllRaceData: [],
                            // selectedValues: {},
                          })
                        }
                        options={statusOption}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    </Grid> */}
                  </Box>

                  {/* <Grid
                    item
                    xs={12}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text mb-8"
                  >
                    <label className="modal-label">
                      {" "}
                      Key Comments <span className="color-red">*</span>
                    </label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="textarea"
                      multiline
                      maxRows={2}
                      color="primary"
                      size="small"
                      placeholder="Key Comments"
                      value={expertTipsValues?.keyComments}
                      onChange={(e) =>
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            keyComments: e?.target?.value,
                          },
                          errorKeyComments: e?.target?.value
                            ? ""
                            : errorKeyComments,
                        })
                      }
                    />
                    {errorKeyComments ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorKeyComments}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid> */}
                  <Grid
                    item
                    xs={12}
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      textAlign: "left",
                    }}
                    className="teamsport-text mb-8"
                  >
                    <label className="modal-label">
                      {" "}
                      Key Comments <span className="color-red">*</span>
                    </label>

                    <div className="featured-race-editor">
                      <SunEditor
                        onChange={this.handleKeyCommentContentChange}
                        setContents={keyCommentContent}
                        setOptions={{
                          buttonList: [
                            ["undo", "redo"],
                            ["font", "fontSize", "formatBlock"],
                            [
                              "bold",
                              "underline",
                              "italic",
                              "strike",
                              "subscript",
                              "superscript",
                            ],
                            ["removeFormat"],
                            ["outdent", "indent"],
                            ["align", "horizontalRule", "list", "table"],
                            ["link"],
                            ["fullScreen", "showBlocks", "codeView"],
                          ],
                          defaultStyle: "font-size: 16px;",
                        }}
                      />
                    </div>
                    {errorKeyComments ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorKeyComments}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                  <Box className="race-all-runner">
                    {TrackAllRaceData?.map((race) => {
                      return (
                        <>
                          <Grid item xs={12} style={{ marginBottom: "15px" }}>
                            <>
                              <label className="modal-label">
                                {race?.raceNumber}{" "}
                                <span className="color-red">*</span>
                              </label>
                              <Select
                                isMulti
                                className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                                classNamePrefix="select"
                                menuPosition="fixed"
                                placeholder={race?.raceNumber}
                                options={race?.runner?.map((runner) => ({
                                  value: runner?.value,
                                  label: runner?.label,
                                }))}
                                isOptionDisabled={(option) =>
                                  selectedValues[race?.raceId]?.includes(
                                    option.value
                                  ) ||
                                  (selectedValues[race?.raceId]?.length || 0) >=
                                    4
                                }
                                value={(
                                  selectedValues[race?.raceId] || []
                                )?.map((runnerId) => ({
                                  value: runnerId,
                                  label: race?.runner?.find(
                                    (runner) => runner?.value === runnerId
                                  )?.label,
                                }))}
                                onChange={(selectedOptions) =>
                                  this.handleChange(
                                    race?.raceId,
                                    selectedOptions
                                  )
                                }
                                styles={{
                                  menuPortal: (base) => ({
                                    ...base,
                                    zIndex: 9999,
                                  }),
                                }}
                              />
                              {renderError(race)}
                              {runnerError && runnerErrorObj[race?.raceId] ? (
                                <Box
                                  style={{
                                    display: "flex",
                                    columnGap: "5px",
                                    alignItems: "center",
                                  }}
                                >
                                  <Checkbox
                                    checked={runnerErrorCheckbox?.includes(
                                      race?.raceId
                                    )}
                                    onClick={(e) => {
                                      if (e.target.checked) {
                                        const addItem = [
                                          ...runnerErrorCheckbox,
                                          race?.raceId,
                                        ];
                                        this.setState({
                                          runnerErrorCheckbox: addItem,
                                        });
                                      } else {
                                        const removeItem =
                                          runnerErrorCheckbox?.filter(
                                            (eItem) => eItem !== race?.raceId
                                          );
                                        this.setState({
                                          runnerErrorCheckbox: removeItem,
                                        });
                                      }
                                    }}
                                    style={{ padding: "0px" }}
                                  />
                                  <p
                                    className="errorText"
                                    style={{ margin: "0px 0 0 0" }}
                                  >
                                    {runnerError}
                                  </p>
                                </Box>
                              ) : (
                                ""
                              )}
                            </>
                          </Grid>
                        </>
                      );
                    })}
                  </Box>
                  <Box className="race-runner-wrap mb-8">
                    {/* <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">
                        {" "}
                        Best Bet Comments
                        <span className="color-red">*</span>{" "}
                      </label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        type="textarea"
                        multiline
                        maxRows={2}
                        color="primary"
                        size="small"
                        placeholder="Best Bet"
                        value={expertTipsValues?.bestBet}
                        onChange={(e) =>
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              bestBet: e?.target?.value,
                            },
                            errorBestBet: e?.target?.value ? "" : errorBestBet,
                          })
                        }
                        disabled={
                          TrackAllRaceData?.length !==
                            Object?.keys(selectedValues).length ||
                          !Boolean(expertTipsValues?.modalTrackId)
                        }
                      />
                      {errorBestBet ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorBestBet}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid> */}
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        textAlign: "left",
                      }}
                      className="teamsport-text mb-8"
                    >
                      <label className="modal-label">
                        {" "}
                        Best Bet Comments <span className="color-red">*</span>
                      </label>

                      <div className="featured-race-editor">
                        <SunEditor
                          onChange={this.handleBestBetCommentContentChange}
                          setContents={bestBestCommentContent}
                          setOptions={{
                            buttonList: [
                              ["undo", "redo"],
                              ["font", "fontSize", "formatBlock"],
                              [
                                "bold",
                                "underline",
                                "italic",
                                "strike",
                                "subscript",
                                "superscript",
                              ],
                              ["removeFormat"],
                              ["outdent", "indent"],
                              ["align", "horizontalRule", "list", "table"],
                              ["link"],
                              ["fullScreen", "showBlocks", "codeView"],
                            ],
                            defaultStyle: "font-size: 16px;",
                          }}
                          disable={
                            TrackAllRaceData?.length !==
                              Object?.keys(selectedValues).length ||
                            !Boolean(expertTipsValues?.modalTrackId)
                          }
                        />
                      </div>
                      {errorBestBet ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorBestBet}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  <Box className="race-runner-wrap mb-8">
                    <Grid item xs={6} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Best Bet Race
                        {expertTipsValues?.bestBet?.trim()?.length > 0 ? (
                          <span className="color-red">*</span>
                        ) : (
                          ""
                        )}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Best Bet Race"
                        isDisabled={
                          expertTipsValues?.bestBet == null
                            ? true
                            : !expertTipsValues?.modalTrackId ||
                              !expertTipsValues?.bestBet?.trim()?.length > 0
                        }
                        isLoading={isTrackRaceLoading}
                        value={
                          expertTipsValues?.modalTrackBetRace &&
                          TrackRaceData?.find((item) => {
                            return (
                              item?.value == expertTipsValues?.modalTrackBetRace
                            );
                          })
                        }
                        options={TrackRaceData}
                        onChange={(e) => {
                          this.setState({
                            errorBetRace: e?.value ? "" : errorBetRace,
                            BetRunnerData: [],
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalTrackEachWayRace: null,
                              modalTrackLayRace: null,
                              modalTrackWayRunnerId: null,
                              modalTrackLayRunnerId: null,
                            },
                          });
                          this.bestBetRaceRunner(e?.value);
                        }}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorBetRace ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorBetRace}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid item xs={6} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Best Bet Runner
                        {expertTipsValues?.bestBet?.trim()?.length > 0 ? (
                          <span className="color-red">*</span>
                        ) : (
                          ""
                        )}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Best Bet Runner"
                        isDisabled={
                          // !expertTipsValues?.modalTrackBetRace ||
                          // !expertTipsValues?.bestBet?.length > 0
                          true
                        }
                        isLoading={isBestBetRaceRunnerLoading}
                        value={
                          expertTipsValues?.modalBetRunnerId &&
                          BetRunnerData?.find((item) => {
                            return (
                              item?.value == expertTipsValues?.modalBetRunnerId
                            );
                          })
                        }
                        options={BetRunnerData}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalBetRunnerId: e?.value,
                            },
                            errorBetRaceRunner: e?.value
                              ? ""
                              : errorBetRaceRunner,
                          });
                        }}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorBetRaceRunner ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorBetRaceRunner}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">
                        {" "}
                        Best Each Way Comments
                        {/* <span className="color-red">*</span> */}
                      </label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        type="textarea"
                        multiline
                        maxRows={2}
                        color="primary"
                        size="small"
                        placeholder="Best Each Way"
                        value={expertTipsValues?.bestEachWay}
                        disabled={
                          TrackAllRaceData?.length !==
                            Object?.keys(selectedValues).length ||
                          !Boolean(expertTipsValues?.modalTrackId)
                        }
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              bestEachWay: e?.target?.value,
                            },

                            // errorBestEachWay: e?.target?.value
                            //   ? ""
                            //   : errorBestEachWay,
                          });
                          if (!e?.target?.value) {
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                modalTrackEachWayRace: null,
                                modalTrackWayRunnerId: null,
                                bestEachWay: "",
                              },
                            });
                          }
                        }}
                      />
                      {/* {errorBestEachWay ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorBestEachWay}
                      </p>
                    ) : (
                      ""
                    )} */}
                    </Grid>
                    {/* <Grid
                    item
                    xs={6}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text"
                  >
                    <label className="modal-label">
                      Each Way Price <span className="color-red">*</span>
                    </label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="text"
                      color="primary"
                      size="small"
                      placeholder="Each Way Price"
                      value={expertTipsValues?.eachWayPrice}
                      onChange={(e) => {
                        const input = e?.target?.value;
                        const sanitizedInput = input?.replace(
                          /\.+/g,
                          (match, offset, original) =>
                            offset === original?.indexOf(".") ? "." : ""
                        );
                        const numericInput = sanitizedInput?.replace(
                          /[^0-9.]/g,
                          ""
                        );
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            eachWayPrice: numericInput,
                          },
                          errorEachWayPrice: numericInput
                            ? ""
                            : errorEachWayPrice,
                        });
                      }}
                    />
                    {errorEachWayPrice ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorEachWayPrice}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid> */}
                  </Box>
                  <Box className="race-runner-wrap mb-8">
                    <Grid item xs={6} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Each Way Race{" "}
                        {expertTipsValues?.bestEachWay?.trim()?.length > 0 ? (
                          <span className="color-red">*</span>
                        ) : (
                          ""
                        )}
                        {/* <span className="color-red">*</span> */}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Each Way Race"
                        isDisabled={
                          expertTipsValues?.bestEachWay == null
                            ? true
                            : !expertTipsValues?.modalTrackId ||
                              !expertTipsValues?.bestEachWay?.trim()?.length > 0
                        }
                        isLoading={isTrackRaceLoading}
                        value={
                          expertTipsValues?.modalTrackEachWayRace &&
                          TrackRaceData?.find((item) => {
                            return (
                              item?.value ==
                              expertTipsValues?.modalTrackEachWayRace
                            );
                          })
                        }
                        options={TrackRaceData?.filter(
                          (item) =>
                            item?.value !== expertTipsValues?.modalTrackBetRace
                        ).filter(
                          (item) =>
                            item?.value !== expertTipsValues?.modalTrackLayRace
                        )}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalTrackEachWayRace: e?.value,
                              modalTrackWayRunnerId: null,
                            },
                            errorEachWayRace: e?.value ? "" : errorEachWayRace,
                          });
                          this.eachWayRaceRunner(e?.value);
                        }}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorEachWayRace ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorEachWayRace}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid item xs={6} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Each Way Runner
                        {expertTipsValues?.bestEachWay?.trim()?.length > 0 ? (
                          <span className="color-red">*</span>
                        ) : (
                          ""
                        )}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Each Way Runner"
                        isDisabled={
                          expertTipsValues?.bestEachWay == null
                            ? true
                            : !expertTipsValues?.modalTrackEachWayRace ||
                              !expertTipsValues?.bestEachWay?.trim().length > 0
                        }
                        isLoading={isEachWayRaceRunnerLoading}
                        value={
                          expertTipsValues?.modalTrackWayRunnerId &&
                          FilteredEachWayRunnerData?.find((item) => {
                            return (
                              item?.value ==
                              expertTipsValues?.modalTrackWayRunnerId
                            );
                          })
                        }
                        options={FilteredEachWayRunnerData}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalTrackWayRunnerId: e?.value,
                            },
                            errorEachWayRaceRunner: e?.value
                              ? ""
                              : errorEachWayRaceRunner,
                          });
                        }}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorEachWayRaceRunner ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorEachWayRaceRunner}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  <Box className="race-runner-wrap mb-8">
                    <Grid
                      item
                      xs={12}
                      style={{ display: "flex", flexDirection: "column" }}
                      className="teamsport-text"
                    >
                      <label className="modal-label">
                        {" "}
                        Best Lay Comments
                        {/* <span className="color-red">*</span> */}
                      </label>
                      <TextField
                        className="teamsport-textfield"
                        variant="outlined"
                        type="textarea"
                        multiline
                        maxRows={2}
                        color="primary"
                        size="small"
                        placeholder="Best Lay"
                        value={expertTipsValues?.bestLay}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              bestLay: e?.target?.value,
                            },
                            // errorBestLay: e?.target?.value ? "" : errorBestLay,
                          });
                          if (!e?.target?.value) {
                            this.setState({
                              expertTipsValues: {
                                ...expertTipsValues,
                                modalTrackLayRace: null,
                                modalTrackLayRunnerId: null,
                                bestLay: "",
                              },
                            });
                          }
                        }}
                      />
                      {/* {errorBestLay ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorBestLay}
                      </p>
                    ) : (
                      ""
                    )} */}
                    </Grid>
                    {/* <Grid
                    item
                    xs={6}
                    style={{ display: "flex", flexDirection: "column" }}
                    className="teamsport-text"
                  >
                    <label className="modal-label">
                      Lay Price <span className="color-red">*</span>
                    </label>
                    <TextField
                      className="teamsport-textfield"
                      variant="outlined"
                      type="text"
                      color="primary"
                      size="small"
                      placeholder="Lay Price"
                      value={expertTipsValues?.layPrice}
                      onChange={(e) => {
                        const input = e?.target?.value;
                        const sanitizedInput = input?.replace(
                          /\.+/g,
                          (match, offset, original) =>
                            offset === original?.indexOf(".") ? "." : ""
                        );
                        const numericInput = sanitizedInput?.replace(
                          /[^0-9.]/g,
                          ""
                        );
                        this.setState({
                          expertTipsValues: {
                            ...expertTipsValues,
                            layPrice: numericInput,
                          },
                          errorLayPrice: numericInput ? "" : errorLayPrice,
                        });
                      }}
                    />
                    {errorLayPrice ? (
                      <p
                        className="errorText"
                        style={{ margin: "-14px 0 0 0" }}
                      >
                        {errorLayPrice}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid> */}
                  </Box>
                  <Box className="race-runner-wrap mb-8">
                    <Grid item xs={6} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Lay Race
                        {expertTipsValues?.bestLay?.trim()?.length > 0 ? (
                          <span className="color-red">*</span>
                        ) : (
                          ""
                        )}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Lay Race"
                        isDisabled={
                          expertTipsValues?.bestLay == null
                            ? true
                            : !expertTipsValues?.modalTrackId ||
                              !expertTipsValues?.bestLay?.trim()?.length > 0
                        }
                        isLoading={isTrackRaceLoading}
                        value={
                          expertTipsValues?.modalTrackLayRace &&
                          TrackRaceData?.find((item) => {
                            return (
                              item?.value == expertTipsValues?.modalTrackLayRace
                            );
                          })
                        }
                        options={TrackRaceData?.filter(
                          (item) =>
                            item?.value !== expertTipsValues?.modalTrackBetRace
                        )?.filter(
                          (item) =>
                            item?.value !==
                            expertTipsValues?.modalTrackEachWayRace
                        )}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalTrackLayRace: e?.value,
                              modalTrackLayRunnerId: null,
                            },
                            LayRunnerData: [],
                            errorLayRace: e?.value ? "" : errorLayRace,
                          });
                          this.layRaceRunner(e?.value);
                        }}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorLayRace ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorLayRace}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                    <Grid item xs={6} style={{ marginBottom: "15px" }}>
                      <label className="modal-label">
                        Lay Runner
                        {expertTipsValues?.bestLay?.trim()?.length > 0 ? (
                          <span className="color-red">*</span>
                        ) : (
                          ""
                        )}
                      </label>
                      <Select
                        className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Lay Runner"
                        isDisabled={
                          expertTipsValues?.bestLay == null
                            ? true
                            : !expertTipsValues?.modalTrackLayRace ||
                              !expertTipsValues?.bestLay?.trim()?.length > 0
                        }
                        isLoading={isLayRaceRunnerLoading}
                        value={
                          expertTipsValues?.modalTrackLayRunnerId &&
                          FilteredLayRunnerData?.find((item) => {
                            return (
                              item?.value ==
                              expertTipsValues?.modalTrackLayRunnerId
                            );
                          })
                        }
                        options={FilteredLayRunnerData}
                        onChange={(e) => {
                          this.setState({
                            expertTipsValues: {
                              ...expertTipsValues,
                              modalTrackLayRunnerId: e?.value,
                            },
                            errorLayRaceRunner: e?.value
                              ? ""
                              : errorLayRaceRunner,
                          });
                        }}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                      {errorLayRaceRunner ? (
                        <p className="errorText" style={{ margin: "0px" }}>
                          {errorLayRaceRunner}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid>
                  </Box>
                  {/* <Box className="race-runner-wrap mb-8">
                  <Grid item xs={6} style={{ marginBottom: "15px" }}>
                    <label className="modal-label">Status</label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select event-Tournament-select"
                      classNamePrefix="select"
                      placeholder="Select Status"
                      value={statusOption?.find((item) => {
                        return item?.label == selectedStatus;
                      })}
                      //   isLoading={isLoading}
                      onChange={(e) =>
                        this.setState({
                          selectedStatus: e?.value,
                        })
                      }
                      options={statusOption}
                    />
                  </Grid>
                </Box> */}
                  {/* <div
                  className="blog-file-upload"
                  style={{ width: "100%", marginTop: "10px" }}
                >
                  <label className="modal-label"> Banner Image </label>
                  <FileUploader
                    onDrop={(image) =>
                      this.handleFileUpload("defaultImage", image)
                    }
                    style={{ marginTop: "5px" }}
                  />
                  <Box
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <div className="logocontainer">
                      {defaultImage?.length > 0
                        ? defaultImage?.map((file, index) => (
                            <img
                              className="auto-width"
                              key={index}
                              src={file.preview}
                              alt="player"
                            />
                          ))
                        : defaultUploadImage &&
                          defaultUploadImage !== "" && (
                            <img
                              className="auto-width"
                              src={
                                defaultUploadImage?.includes("uploads")
                                  ? defaultUploadImage
                                  : defaultUploadImage
                              }
                              alt="player"
                            />
                          )}
                    </div>
                    {(defaultImage?.length > 0 ||
                      (defaultUploadImage && defaultUploadImage !== "")) && (
                      <Box className="delete-icon-wrap">
                        <DeleteIcon
                          className="delete-icon"
                          onClick={() => this.handleFeatureLogoRemove()}
                          style={{ cursor: "pointer" }}
                        />
                      </Box>
                    )}
                  </Box>
                </div> */}
                </Grid>

                <Grid container>
                  <Grid item xs={2}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      {!isEditMode ? (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleSave(0, "draft")}
                          color="primary"
                          value={
                            !isLoading ? "Save as SmartB draft" : "Loading..."
                          }
                          disabled={isLoading}
                        />
                      ) : (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleUpdate(0, "draft")}
                          color="secondary"
                          value={
                            !isLoading ? "Save as SmartB draft" : "Loading..."
                          }
                          disabled={isLoading}
                        />
                      )}
                      {!isEditMode ? (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleSave(1, "draft")}
                          color="primary"
                          value={
                            !isLoading ? "Save As BAW Draft" : "Loading..."
                          }
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      ) : (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleUpdate(1, "draft")}
                          color="secondary"
                          value={
                            !isLoading ? "Save As BAW Draft" : "Loading..."
                          }
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      )}

                      {!isEditMode ? (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleSave(1, "published")}
                          color="primary"
                          value={!isLoading ? "Publish on BAW" : "Loading..."}
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      ) : (
                        <ButtonComponent
                          className="mt-3 admin-btn-green"
                          onClick={() => this.handleUpdate(1, "published")}
                          color="secondary"
                          value={!isLoading ? "Publish on BAW" : "Loading..."}
                          disabled={isLoading}
                          style={{ marginLeft: "30px" }}
                        />
                      )}

                      <ButtonComponent
                        onClick={this.toggleInputModal}
                        className="mr-lr-30 back-btn"
                        value="Back"
                      />
                    </div>
                    {errorStateCode ? (
                      <p
                        className="errorText"
                        style={{ margin: "4px 0px 4px" }}
                      >
                        {errorStateCode}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        )}
        <Modal
          className="modal modal-input tips-modal-details"
          open={tipsModalDetailsOpen}
          onClose={this.toggleTipsDetailsModalOpen}
        >
          <div
            className={"paper modal-show-scroll"}
            style={{ position: "relative" }}
          >
            <h3 className="text-center modal-head">
              {tipsModalDetails?.tips?.Event?.eventName} Tips Details
            </h3>
            <CancelIcon
              className="admin-close-icon"
              onClick={this.toggleTipsDetailsModalOpen}
            />
            {tipsModalDetailsIsLoading ? (
              <Box className="modal-loader">
                <Loader />
              </Box>
            ) : (
              <Box className="tips-details">
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Key Comments :
                  </Typography>
                  <Typography
                    className="details-para"
                    dangerouslySetInnerHTML={{
                      __html: tipsModalDetails?.tips?.keyComment,
                    }}
                  ></Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Bet Comments :
                  </Typography>
                  <Typography
                    className="details-para"
                    dangerouslySetInnerHTML={{
                      __html: tipsModalDetails?.tips?.betComment,
                    }}
                  ></Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.betPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">Bet Race :</Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.betRace
                      ? "R" +
                        tipsModalDetails?.tips?.betRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.betRace?.raceName
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.betParticipant
                      ? tipsModalDetails?.tips?.betParticipant?.runnerNumber +
                        "." +
                        tipsModalDetails?.tips?.betParticipant?.animal?.name +
                        " (" +
                        tipsModalDetails?.tips?.betParticipant?.barrierNumber +
                        ")"
                      : "-"}
                    {/* {tipsModalDetails?.tips?.betParticipant?.runnerNumber}
                  {"."} {tipsModalDetails?.tips?.betParticipant?.animal?.name}{" "}
                  ({tipsModalDetails?.tips?.betParticipant?.barrierNumber}) */}
                  </Typography>
                </Box>

                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Each Way Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.wayComment
                      ? tipsModalDetails?.tips?.wayComment
                      : "-"}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Each Way Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.wayPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Each Way Race :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.wayRace
                      ? "R" +
                        tipsModalDetails?.tips?.wayRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.wayRace?.raceName
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Each Way Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {/* {tipsModalDetails?.tips?.wayParticipant?.runnerNumber}
                  {"."} {tipsModalDetails?.tips?.wayParticipant?.animal?.name}{" "}
                  ({tipsModalDetails?.tips?.wayParticipant?.barrierNumber}) */}

                    {tipsModalDetails?.tips?.wayParticipant
                      ? tipsModalDetails?.tips?.wayParticipant?.runnerNumber +
                        "." +
                        tipsModalDetails?.tips?.wayParticipant?.animal?.name +
                        " (" +
                        tipsModalDetails?.tips?.wayParticipant?.barrierNumber +
                        ")"
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Best Lay Comments :
                  </Typography>
                  <Typography className="details-para">
                    {tipsModalDetails?.tips?.layComment
                      ? tipsModalDetails?.tips?.layComment
                      : "-"}
                  </Typography>
                </Box>
                {/* <Box className="d-flex align-item-baseline col-35 mb-18 details">
                <Typography className="detsils-header">
                  Bet Lay Price :
                </Typography>
                <Typography className="details-para">
                  {tipsModalDetails?.tips?.layPrice}
                </Typography>
              </Box> */}
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Lay Race :
                  </Typography>
                  <Typography className="details-para">
                    {/* {"R" + tipsModalDetails?.tips?.layRace?.raceNumber}{" "}
                  {tipsModalDetails?.tips?.layRace?.raceName} */}
                    {tipsModalDetails?.tips?.layRace
                      ? "R" +
                        tipsModalDetails?.tips?.layRace?.raceNumber +
                        " " +
                        tipsModalDetails?.tips?.layRace?.raceName
                      : "-"}
                  </Typography>
                </Box>
                <Box className="d-flex align-item-baseline col-35 mb-18 details">
                  <Typography className="detsils-header">
                    Bet Lay Race Runner :
                  </Typography>
                  <Typography className="details-para">
                    {/* {tipsModalDetails?.tips?.layParticipant?.runnerNumber}
                  {"."} {tipsModalDetails?.tips?.layParticipant?.animal?.name}{" "}
                  ({tipsModalDetails?.tips?.layParticipant?.barrierNumber}) */}
                    {tipsModalDetails?.tips?.layParticipant
                      ? tipsModalDetails?.tips?.layParticipant?.runnerNumber +
                        "." +
                        tipsModalDetails?.tips?.layParticipant?.animal?.name +
                        " (" +
                        tipsModalDetails?.tips?.layParticipant?.barrierNumber +
                        ")"
                      : "-"}
                  </Typography>
                </Box>

                {tipsModalDetails?.races?.map((item, index) => {
                  return (
                    <>
                      <Box
                        className="d-flex align-item-baseline col-35 mb-18 details"
                        key={index}
                      >
                        <Typography className="detsils-header">
                          {"Race " + item?.raceNumber}:
                        </Typography>
                        <Box className="w-60">
                          {item?.runners?.map((obj) => (
                            <Typography className="details-para">
                              {obj?.runnerNumber}
                              {"."} {obj?.animal?.name} ({obj?.barrierNumber})
                            </Typography>
                          ))}
                        </Box>
                      </Box>
                    </>
                  );
                })}
              </Box>
            )}
            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  <ButtonComponent
                    onClick={this.toggleTipsDetailsModalOpen}
                    // className="mr-lr-30"
                    value="Back"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </Modal>
      </>
    );
  }
}
export default ExpertTips;
