.api-provider-listTable {
  .MuiTableCell-root {
    padding: 18px 19px;
  }
}

.api-provider {
  min-height: auto !important;

  .input-field {
    input {
      width: 95%;
    }
  }

  .app-dropdown .dropdown {
    width: 97.5%;
    min-height: 47px;
    margin-bottom: 12px;
    margin-top: 4px;
  }

  .app-dropdown span {
    color: red;
  }

  .MuiButtonBase-root {
    min-width: 100%;
  }
}



.modal-input {
  position: fixed;
  z-index: 1300;
  inset: 0px;
  top: 5% !important;
  bottom: 5% !important;
  // left: 35% !important;
  margin: 0px auto !important;
  width: 586px !important;
}

.bookkeeper-modal-input {
  width: 768px !important;

  .upload-grid {
    display: grid;
    grid-template-columns: auto auto;
    column-gap: 18px;

    h6 {
      margin: 0px 0px 12px;
    }

    p {
      text-align: center;
    }
  }
}



.select-box {
  .select-box-manual {
    font-size: 16px;
    border-radius: 8px;
    min-height: 45px;
    border: 1px solid #D4D6D8;
    padding-left: 10px;
    width: 97.5%;
    margin-bottom: 15px;
    margin-top: 4px;
  }

  .select-box-arrow {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background-image: url(../../images/dronArrow.svg);
    background-repeat: no-repeat;
    background-position: calc(100% - 13.8px);
    background-size: 10px;

  }
}

.blog-file-upload img.auto-width {
  width: auto;
  height: auto;
}

.logocontainer {
  width: 78px;

  img {
    max-width: 100%;
  }
}

.admin-close-icon {
  font-size: 30px;
  cursor: pointer;
  position: absolute;
  top: 5px;
  right: 8px;
}

.apiprovider-select {
  .MuiButtonBase-root {
    min-width: auto;
  }

  .MuiInput-underline:after {
    border-bottom: none;
  }

  .MuiInput-underline:hover:not(.Mui-disabled):before {
    border-bottom: none;
  }

  .MuiInput-underline:before {
    border-bottom: none;
  }

  .MuiFormControl-root {
    font-size: 16px;
    border-radius: 8px;
    min-height: 38px;
    border: 1px solid #ddd;
    padding-left: 10px;
    width: 93.5%;
    margin-bottom: 15px;
    background-color: #ffffff;
    margin-top: 5px;
  }

  .MuiAutocomplete-inputRoot[class*="MuiInput-root"] .MuiAutocomplete-input:first-child {
    padding: 9px 5px;
  }
}