pipeline {
    agent any

    environment {
        DOCKER_REGISTRY = '*************:5000'
        SONAR_HOME = tool(name: 'sonar') // SonarQube tool
        SONAR_TOKEN = credentials('sonar') // SonarQube credentials
        SERVICE_NAME = 'smartb-admin-ui'
        SONAR_HOST_URL= 'http://sonarqube:9000'

    }

    stages {
    
    stage('Set Environment Variables') {
            steps {
                script {
                    // Set environment variables dynamically based on the branch
                    if (env.BRANCH_NAME == 'staging') {
                        env.REACT_APP_API_BASE_URL = 'https://staging.smartb.au.sydney.digiground.com.au/api/'
                        env.REACT_APP_BASE_NAME = '/admin'
                        env.REACT_APP_BASE_URL = 'https://staging.smartb.au.sydney.digiground.com.au/admin/'
                        env.REACT_APP_WP_BASE_URL = '#'
                        env.REACT_APP_RELEASE = 'AU'
                        env.REACT_APP_API_BASE_URL_FANTASY = 'https://staging.smartb.au.sydney.digiground.com.au/fantasy-api/fantasy'
                        env.REACT_APP_MEDIA_URL = 'https://media.staging.smartb.au.sydney.digiground.com.au/'
                    } else if (env.BRANCH_NAME == 'testing') {
                        env.REACT_APP_API_BASE_URL = 'https://testing.smartb.au.sydney.digiground.com.au/api/'
                        env.REACT_APP_BASE_NAME = '/admin'
                        env.REACT_APP_BASE_URL = 'https://testing.smartb.au.sydney.digiground.com.au/admin/'
                        env.REACT_APP_WP_BASE_URL = '#'
                        env.REACT_APP_RELEASE = 'AU'
                        env.REACT_APP_API_BASE_URL_FANTASY = 'https://testing.smartb.au.sydney.digiground.com.au/fantasy-api/fantasy'
                        env.REACT_APP_MEDIA_URL = 'https://media.staging.smartb.au.sydney.digiground.com.au/smartb-testing/'
                    } else if (env.BRANCH_NAME == 'master') {
                        env.REACT_APP_API_BASE_URL = 'https://smartb.com.au/api/'
                        env.REACT_APP_BASE_NAME = '/admin'
                        env.REACT_APP_BASE_URL = 'https://smartb.com.au/admin/'
                        env.REACT_APP_WP_BASE_URL = '#'
                        env.REACT_APP_RELEASE = 'AU'
                        env.REACT_APP_API_BASE_URL_FANTASY = 'https://smartb.com.au/fantasy-api/fantasy'
                        env.REACT_APP_MEDIA_URL = 'https://media.smartb.com.au/'
                    } else {
                        error "Unsupported branch: ${env.BRANCH_NAME}. Pipeline aborted."
                    }

                    echo "Branch: ${env.BRANCH_NAME}"
                    echo "Environment variables set for branch: ${env.BRANCH_NAME}"
                }
            }
        }
        stage('Build and Push Docker Image') {
            when {
                anyOf {
                    branch 'staging'
                    branch 'testing'
                    branch 'master'
                }
            }
            steps {
                script {
                    // Use branch name and service name for the image
                    def imageName = "${BRANCH_NAME}/${SERVICE_NAME}"

                    echo "Building and pushing Docker image: ${DOCKER_REGISTRY}/${imageName}:${BUILD_NUMBER}"

                    sh """
                    docker build \
                        --build-arg REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL} \
                        --build-arg REACT_APP_BASE_NAME=${REACT_APP_BASE_NAME} \
                        --build-arg REACT_APP_BASE_URL=${REACT_APP_BASE_URL} \
                        --build-arg REACT_APP_WP_BASE_URL=${REACT_APP_WP_BASE_URL} \
                        --build-arg REACT_APP_RELEASE=${REACT_APP_RELEASE} \
                        --build-arg REACT_APP_API_BASE_URL_FANTASY=${REACT_APP_API_BASE_URL_FANTASY} \
                        --build-arg REACT_APP_MEDIA_URL=${REACT_APP_MEDIA_URL} \
                        -t ${DOCKER_REGISTRY}/${imageName}:${BUILD_NUMBER} .
                    docker push ${DOCKER_REGISTRY}/${imageName}:${BUILD_NUMBER}
                    """
                }
            }
        }

        stage('Deploy to Server') {
            when {
                anyOf {
                    branch 'staging'
                    branch 'testing'
                    branch 'master'
                }
            }
            steps {
                script {
                    // Retrieve credentials securely
                    withCredentials([usernamePassword(credentialsId: 'smartb-ssh', usernameVariable: 'SSH_USER', passwordVariable: 'SSH_PASS')]) {
                        // Define branch-specific deployment details
                        def serverHost 
                        def serverPort = 22
                        def composeDir

                        if (env.BRANCH_NAME == 'staging') {
                        serverHost = '************'
                            composeDir = '/home/<USER>/smartb/staging'
                        } else if (env.BRANCH_NAME == 'testing') {
                        serverHost = '************'
                            composeDir = '/home/<USER>/smartb/testing'
                        } else if (env.BRANCH_NAME == 'master') {
                        serverHost = '************'
                            composeDir = '/home/<USER>/smartb/production'
                        } else {
                            error "Unsupported branch: ${env.BRANCH_NAME}. Deployment aborted."
                        }

                        echo "Deploying ${SERVICE_NAME} to ${env.BRANCH_NAME} environment on server ${serverHost}:${serverPort}."

                        // Deployment logic integrated directly into the pipeline
                        sh """
                        sshpass -p '${SSH_PASS}' ssh -o StrictHostKeyChecking=no -p ${serverPort} ${SSH_USER}@${serverHost} << EOF
                        set -e
                        cd ${composeDir}

                        echo "Updating docker-compose.yml for service ${SERVICE_NAME} with image tag ${BUILD_NUMBER}..."
                        sed -i "/${SERVICE_NAME}:/,/image:/s|\\(image:.*:\\).*|\\1${BUILD_NUMBER}|" docker-compose.yml

                        echo "Pulling updated image and restarting the service..."
                        docker-compose pull ${SERVICE_NAME}
                        docker-compose up -d ${SERVICE_NAME}

                        echo "Deployment of ${SERVICE_NAME} with image tag ${BUILD_NUMBER} completed successfully."
EOF
                        """
                    }
                }
            }
        }
    }

    post {
        success {
            echo "Pipeline completed successfully!"
        }
        failure {
            echo "Pipeline failed!"
        }
    }
}



