.FAQ-textfield {
  .MuiInputBase-root {
    border-radius: 8px;
  }
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  display: none;
}

.paid-btn {
  background-color: #1c9a6c !important;
}

// .prize-info-container {
//   background-color: #FFFFFF;
//   border-radius: 8px;
//   padding: 18px;
//   margin-top: 7px;

//   @media (max-width: 799px) {
//     padding: 12px;
//   }

//   .prize-header-section {
//     display: flex;
//     align-items: center;
//   }

//   .cutoff-txt {
//     font-size: 16px;
//     line-height: 19px;
//     font-weight: 600;
//     letter-spacing: 0px;
//     color: #191919 !important;
//     margin-bottom: 17px;

//     @media (max-width: 799px) {
//       font-size: 14px;
//       line-height: 16px;
//       margin-bottom: 13px;
//     }
//   }

//   .prize-position {
//     font-size: 16px;
//     line-height: 19px;
//     font-weight: 400;
//     letter-spacing: 0px;
//     color: #191919 !important;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//   }

//   .w-80 {
//     min-width: 80px;

//     @media (max-width: 799px) {
//       min-width: 55px;
//     }
//   }

//   .prize-info-item {
//     display: flex;
//     align-items: center;

//     .field-container {
//       width: 300px;

//       .teamsport-textfield {
//         width: 100%;
//       }
//     }

//     .delete-icon {
//       padding: 0px;
//       min-width: auto;

//       @media (max-width: 799px) {
//         svg {
//           width: 32px;
//           height: 32px;
//         }
//       }
//     }
//   }

//   .add-position-btn {
//     border-radius: 8px;
//     background-color: #1c9a6c;
//     font-size: 16px;
//     line-height: 19px;
//     font-weight: 400;
//     letter-spacing: 0px;
//     color: #FFFFFF;
//     text-transform: capitalize;
//     padding: 9px;
//     min-width: auto
//   }
// }