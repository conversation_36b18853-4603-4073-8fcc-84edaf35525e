import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
import { Link } from "react-router-dom";
import CSVExport from "../csvExport/CSVExport";
import moment from "moment-timezone";
import SearchIcons from "../../images/searchIcon.svg";
import DetailsTableModal from "./CreditInfoModal";
import CopyToClipboard from "react-copy-to-clipboard";
import { config } from "../../helpers/config";
import "./usersReferral.scss";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
const users = [
  {
    id: 1,
    firstName: "Dan",
    lastName: "She",
    credit: 50,
    code: "21bk34",
    creditInfo: [
      {
        id: 1,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
      {
        id: 2,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
      {
        id: 3,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
    ],
  },
  {
    id: 2,
    firstName: "Bill",
    lastName: "Cosby",
    credit: 11,
    code: "1146abc",
    creditInfo: [
      {
        id: 1,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
      {
        id: 2,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
      {
        id: 3,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
    ],
  },
  {
    id: 3,
    firstName: "Test",
    lastName: "Test",
    credit: 6,
    code: "90rggba",
    creditInfo: [
      {
        id: 1,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
      {
        id: 2,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
      {
        id: 3,
        today: 30,
        thisWeek: 20,
        thisMonth: 11,
        lastMonth: 10,
        allTime: 100,
      },
    ],
  },
  {
    id: 4,
    firstName: "User",
    lastName: "Admin",
    credit: null,
    code: "lka1122",
    creditInfo: [],
  },
];

class UsersReferral extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // users: [],
      isInputModalOpen: false,
      isActivityModalOpen: false,
      isDetailsModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      userId: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      search: "",
      currentPage: 1,
      rowPerPage: 20,
      csvListData: [],
      userDetail: [],
    };
  }

  // componentDidMount() {
  //   this.fetchAllUsers();
  // }

  // async fetchAllUsers() {
  //   let { search } = this.state;
  //   this.setState({ isLoading: true });
  //   const { status, data } = await axiosInstance.get(
  //     URLS.users + `?search=${search}`
  //   );
  //   if (status === 200) {
  //     this.setState({ users: data, isLoading: false });
  //   }
  // }

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };

  openDetailsModal = (item) => {
    this.setState({
      isDetailsModalOpen: true,
      userId: item?.id,
      userDetail: item,
    });
  };

  toggleDetailsModal = () => {
    this.setState({ isDetailsModalOpen: !this.state.isDetailsModalOpen });
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  afterChangeRefresh = () => {
    this.fetchAllUsers();
  };

  // deleteItem = async () => {
  //   try {
  //     const { status } = await axiosInstance.delete(
  //       `${URLS.users}/${this.state.itemToDelete}`
  //     );
  //     if (status === 200) {
  //       this.setState({ itemToDelete: null, isModalOpen: false }, () => {
  //         this.fetchAllUsers();
  //       });
  //       this.setActionMessage(true, "Success", "Users Deleted Successfully!");
  //     }
  //   } catch (err) {
  //     this.setActionMessage(true, "Error", "An error occurred while deleting.");
  //   }
  // };

  handlePaginationClick = (event, page) => {
    this.setState({
      currentPage: Number(page),
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, users } = this.state;
    if (navDirection === "prev") {
      if (currentPage > 1) {
        this.setState({ currentPage: currentPage - 1 });
      }
    } else {
      if (currentPage < users.length / rowPerPage)
        this.setState({ currentPage: currentPage + 1 });
    }
  };

  handleClearClick = () => {
    this.setState({ search: "" });
    this.fetchAllUsers();
  };

  handleKeyDown = (event) => {
    var { search } = this.state;
    if (event.key === "Enter") {
      this.fetchAllUsers();
      this.setState({ currentPage: 1 });
    }
  };

  handleCopy = () => {
    this.setActionMessage(
      true,
      "Success",
      "Referral link copied successfully!"
    );
  };

  // referralLink = (code) => {
  //   const link = config.baseUrl.includes("testing")
  //     ? `https://testing.smartb.com.au/&code=${code}`
  //     : config.baseUrl.includes("staging")
  //     ? `https://staging.smartb.com.au/&code=${code}`
  //     : `https://smartb.com.au/&code=${code}`;
  //   return link;
  // };
  // referralLinkCopy = (code, id, index) => {
  //   if (id === index) {
  //     const link = config.baseUrl.includes("testing")
  //       ? `https://testing.smartb.com.au/&code=${code}`
  //       : config.baseUrl.includes("staging")
  //       ? `https://staging.smartb.com.au/&code=${code}`
  //       : `https://smartb.com.au/&code=${code}`;

  //     return link;
  //   }
  // };

  render() {
    var {
      // users,
      isModalOpen,
      rowPerPage,
      currentPage,
      messageBox,
      isLoading,
      isInputModalOpen,
      isEditMode,
      isActivityModalOpen,
      isDetailsModalOpen,
      userId,
      csvListData,
      search,
      userDetail,
    } = this.state;
    const pageNumbers = [];
    let currentPageRow = users;

    if (users?.length > 0) {
      const indexOfLastTodo = currentPage * rowPerPage;
      const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      currentPageRow = users.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(users.length / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    // const link = config.baseUrl.includes("testing", "")
    //   ? `https://testing.smartb.com.au/&code=${item?.code}`
    //   : config.baseUrl.includes("staging", "")
    //   ? `https://staging.smartb.com.au/&code=${item?.code}`
    //   : `https://smartb.com.au/&code=${item?.code}`;

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {/* <Paper className="pageWrapper"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                {/* <Link
                underline="hover"
                color="inherit"
              >
                User Management
              </Link> */}
                <Typography className="active_p">User Referral</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="space-around">
              <Grid item xs={4}>
                {/* <h3 className="text-left">Users</h3> */}
                <Typography variant="h1" align="left">
                  User Referral
                </Typography>
              </Grid>
              <Grid
                item
                xs={8}
                style={{
                  display: "flex",
                  justifyContent: "end",
                  alignItems: "center",
                }}
                // className="admin-filter-wrap"
              >
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  className="event-search"
                  onKeyDown={(e) => this.handleKeyDown(e)}
                  value={search}
                  onChange={(e) => {
                    this.setState({
                      ...this.state.search,
                      search: e.target.value,
                    });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                    // marginTop: "5px",
                    marginLeft: "10px",
                  }}
                  onClick={(e) => {
                    this.fetchAllUsers(e);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
              </Grid>
            </Grid>
            {isLoading && <Loader />}
            {!isLoading && users.length === 0 && <p>No Data Available</p>}
            {!isLoading && users.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table
                    className="listTable"
                    aria-label="simple table"
                    style={{
                      minWidth: "max-content",
                    }}
                  >
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>DID</TableCell>
                        <TableCell>Full Name</TableCell>
                        <TableCell>Credit</TableCell>
                        <TableCell>Refereal Link</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {currentPageRow?.map((user, i) => (
                        <TableRow key={i} className="table-rows listTable-Row">
                          <TableCell>{user?.id}</TableCell>
                          <TableCell>
                            {user?.firstName} {user?.lastName}
                          </TableCell>
                          <TableCell>
                            {user?.credit ? user?.credit : "-"}
                          </TableCell>
                          <TableCell>
                            <CopyToClipboard
                              text={
                                config?.baseUrl?.replace("/api", "") +
                                `?Referral=${user?.code}`
                              }
                              onCopy={() => this.handleCopy()}
                            >
                              <Box className="cursor-pointer">
                                <Typography className="option-name">
                                  {/* {this.referralLink(user?.code)} */}
                                  {config?.baseUrl?.replace("/api", "") +
                                    `?Referral=${user?.code}`}
                                </Typography>
                              </Box>
                            </CopyToClipboard>
                          </TableCell>
                          <TableCell>
                            <Button
                              onClick={() => this.openDetailsModal(user)}
                              className="table-btn info-btn"
                            >
                              Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            {/* <button
                            className={
                              users.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              users.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                users.length / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                            {/* <button
                            className={
                              users.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              users.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
            {/* </Paper> */}

            {/* <ShowModal
            isModalOpen={isModalOpen}
            onClose={this.toggleModal}
            Content="Are you sure you want to delete?"
            onOkayLabel="Yes"
            onOkay={this.deleteItem}
            onCancel={this.toggleModal}
          /> */}

            <Modal
              className="news-modal"
              open={isDetailsModalOpen}
              onClose={() => this.toggleDetailsModal()}
            >
              <div className={"paper modal-show-scroll"}>
                <h3 className="text-center">User Referral Details</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={() => this.toggleDetailsModal()}
                />

                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <DetailsTableModal
                      userId={userId}
                      userDetail={userDetail}
                    />
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default UsersReferral;
