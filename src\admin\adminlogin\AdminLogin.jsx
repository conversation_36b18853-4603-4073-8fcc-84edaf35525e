import React, { useState, useEffect, useRef } from "react";
import "./login.scss";
import Button from "../../library/common/components/Button";
import { Card, CardContent, Container, Grid } from "@mui/material";
import { Link, Navigate, useNavigate } from "react-router-dom";
import Form from "../../library/common/components/Form";
import { loginFormModel } from "./constants";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import { connect } from "react-redux";
import { getAuth, setAuthentication } from "../../library/common/actions";
import { Loader } from "../../library/common/components";
import { saveToStorage } from "../../library/utilities";
import { jwtDecode } from "jwt-decode";
import logo from "../../images/SmartB_Logo.svg";

const AdminLogin = ({
  isLoggedIn: initialIsLoggedIn,
  getAuth,
  setAuthentication,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(initialIsLoggedIn);

  const formRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    getAuth();
  }, [getAuth]);

  const handleLogin = async (e) => {
    e.preventDefault();
    const values = formRef.current.getFormData();
    const { formData, isFormValid } = values;

    if (isFormValid) {
      setIsLoading(true);

      const config = {
        auth: {
          username: formData.username,
          password: formData.password,
        },
      };
      const data = {
        username: formData.username,
        password: formData.password,
      };

      try {
        const response = await axiosInstance.post(URLS.login, data, config);
        const { status, data: responseData } = response;

        if (status === 200) {
          let user_obj = jwtDecode(responseData.access_token);
          setAuthentication(responseData, user_obj);
          saveToStorage("user", user_obj);
          setIsLoading(false);

          if (user_obj?.role === "admin" || user_obj?.role === "expertTip") {
            window.location.href = "/admin/dashboard";
          } else {
            setLoginError("You are not eligible for this access");
            setIsLoggedIn(false);
          }
        }
      } catch (err) {
        if (err?.response?.status === 401 || err?.response?.status === 403) {
          setLoginError(err?.response?.data?.message);
        } else {
          setLoginError("Something went wrong. Please try again");
        }
        setIsLoading(false);
      }
    } else {
      setLoginError("Please check email or password");
    }
  };

  // isLoggedIn State is true without checking roles and redirect to /dashboard where there is not component mount on absoluter url  that's why a black screen shown

  // if (isLoggedIn) {
  //   return <Navigate to="/dashboard" />;
  // }

  return (
    <div className="page-content adminLogin">
      <Card>
        <div className="loginWrapper">
          <CardContent>
            <Container>
              <Grid container spacing={3}>
                <Grid item lg={6} sm={12} xs={4}>
                  <div className="leftSide">
                    <p className="welcomeText">
                      welcome to <br />
                      smartb
                    </p>
                  </div>
                </Grid>
                <Grid item lg={6} sm={12} xs={4}>
                  <div className="right-side">
                    <form onSubmit={handleLogin} className="text-center">
                      <div className="mb-30">
                        <Link to="/" className="logo">
                          <img src={logo} alt="Smartb" />
                        </Link>
                      </div>
                      {!!loginError && (
                        <p className="text-left errorText">{loginError}</p>
                      )}
                      {isLoading && <Loader />}
                      <Form ref={formRef} model={loginFormModel} />
                      <Button
                        className="loginBtn"
                        type="submit"
                        disabled={isLoading}
                        onClick={handleLogin}
                        value={isLoading ? "Loading" : "Login"}
                        color="primary"
                      />
                    </form>
                  </div>
                </Grid>
              </Grid>
            </Container>
          </CardContent>
        </div>
      </Card>
    </div>
  );
};

const mapStateToProps = ({ authReducer }) => {
  return {
    isLoggedIn: authReducer.isLoggedIn,
  };
};

export default connect(mapStateToProps, { getAuth, setAuthentication })(
  AdminLogin
);
