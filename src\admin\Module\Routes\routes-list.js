import Dashboard from "../../dashBoard/Dashboard";
import Sports from "../../sports";
import CreateSports from "../../sports/createSports";
import SportType from "../../sportType";
import CreateSportType from "../../sportType/createSportType";
import ApiProvider from "../../APIprovider";
import ApiProviderUpdate from "../../APIprovider/APIproviderUpdate";
import ApiProviderApisFeildMapping from "../../APIprovider/apiProviderApisFeildMapping";

import Users from "../../Users";
import Players from "../../players";
import Country from "../../country";
import States from "../../states";

import Bookkeeper from "../../bookkeeper";

import Events from "../../events";

import RaceTable from "../../raceTable";
import Cities from "../../cities";
import UserDashboard from "../../UserDashboard";

export const routes = [
  { path: "/dashboard", component: Dashboard },
  {
    path: "/sports",
    component: Sports,
  },
  {
    path: "/sporttype",
    component: SportType,
  },
  {
    path: "/apiprovider",
    component: ApiProvider,
  },
  {
    path: "/apiprovider/apifieldmapping/:providerid/:id",
    component: ApiProviderApisFeildMapping,
  },
  {
    path: "/apiprovider/apiproviderupdate/:id",
    component: ApiProviderUpdate,
  },

  {
    path: "/users",
    component: Users,
  },
  {
    path: "/players",
    component: Players,
  },

  {
    path: "/countries",
    component: Country,
  },
  {
    path: "/states/:id",
    component: States,
  },
  {
    path: "/cities/:id",
    component: Cities,
  },

  {
    path: "/bookkeeper",
    component: Bookkeeper,
  },


 

 
  {
    path: "/events",
    component: Events,
  },
  {
    path: "/racetable",
    component: RaceTable,
  },

 
 
];
