import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { <PERSON> } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import fantasyAxiosInstance from "../../helpers/Axios/fantasyAxios";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import "../tippingPremiumPrize/tippingPremiumPrize.scss";

class FantasyUserCoins extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      fantasyUserCoinsValues: {
        userCoins: "",
      },
      FantasyUserCoinsList: [],
      FantasyUserCoinsCount: 0,
      // errorUserCoins: "",
      errorCoinPrice: "",
      errorCoinName: "",
      isSearch: "",
      selectedEmailID: "",
      selectedExternalStatus: null,
      errorCreate: "",
    };
  }

  componentDidMount() {
    this.fetchFantasyUserCoins(0, "");
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset, isSearch } = this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchFantasyUserCoins(offset, isSearch);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchFantasyUserCoins(0, "");

      this.setState({
        offset: 0,
        currentPage: 1,
        isSearch: "",
      });
    }
  }

  async fetchFantasyUserCoins(page, search) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await fantasyAxiosInstance.get(
        `/user/admin/list?limit=${rowPerPage}&offset=${page}&search=${search}`
      );
      if (status === 200) {
        this.setState({
          FantasyUserCoinsList: data?.result,
          isLoading: false,
          FantasyUserCoinsCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    // let { fantasyUserCoinsValues } = this.state;
    let flag = true;
    // if (
    //   fantasyUserCoinsValues?.userCoins?.trim() === "" ||
    //   fantasyUserCoinsValues?.userCoins === null
    // ) {
    //   flag = false;
    //   this.setState({
    //     errorUserCoins: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorUserCoins: "",
    //   });
    // }

    return flag;
  };

  handleUpdate = async () => {
    const { fantasyUserCoinsValues, selectedEmailID, offset, isSearch } =
      this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        coins: +fantasyUserCoinsValues?.userCoins,
        email: selectedEmailID,
      };

      try {
        const { status, data } = await fantasyAxiosInstance.put(
          `/user/admin/update`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
            fantasyUserCoinsValues: {
              userCoins: "",
            },
          });
          this.fetchFantasyUserCoins(offset, isSearch);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      // errorUserCoins: "",
      errorCreate: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        // fantasyUserCoinsValues: {
        //   userCoins: item?.coins,
        // },

        isEditMode: true,
        selectedEmailID: item?.email,
      });
    } else {
      this.setState({
        fantasyUserCoinsValues: {
          userCoins: "",
        },
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  //   deleteItem = async () => {
  //     const { itemToDelete, offset } = this.state;

  //     try {
  //       const { status, data } = await fantasyAxiosInstance.delete(
  //         `/coins/delete/${itemToDelete}`
  //       );
  //       if (status === 200) {
  //         this.setState({ itemToDelete: null, isModalOpen: false }, () => {
  //           this.fetchFantasyUserCoins(offset);
  //         });
  //         this.setActionMessage(true, "Success", data?.message);
  //       }
  //     } catch (err) {
  //       this.setActionMessage(true, "Error", err?.response?.data?.message);
  //     }
  //   };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= rowPerPage) {
        this.setState({
          offset: offset - rowPerPage,
          currentPage: currentPage - 1,
        });
      }
    } else {
      this.setState({
        offset: offset + rowPerPage,
        currentPage: currentPage + 1,
      });
    }
  };

  handleClearClick = () => {
    this.fetchFantasyUserCoins(0, "");
    this.setState({
      offset: 0,
      currentPage: 1,
      isSearch: "",
    });
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      fantasyUserCoinsValues,
      FantasyUserCoinsList,
      FantasyUserCoinsCount,
      // errorUserCoins,
      isSearch,
      offset,
      //   selectedExternalStatus,
      errorCreate,
    } = this.state;
    const pageNumbers = [];

    if (FantasyUserCoinsCount > 0) {
      for (let i = 1; i <= Math.ceil(FantasyUserCoinsCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Fantasy User Coins</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Fantasy User Coins
                </Typography>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => this.fetchFantasyUserCoins(offset, isSearch)}
                >
                  Search
                </Button>
                {/* <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button> */}
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && FantasyUserCoinsList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && FantasyUserCoinsList?.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell>ID</TableCell>
                        <TableCell>User ID</TableCell>
                        <TableCell>User</TableCell>
                        <TableCell>Nick Name</TableCell>
                        <TableCell>Email</TableCell>
                        <TableCell>Coins</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>

                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {FantasyUserCoinsList?.map((item, index) => {
                        return (
                          <TableRow
                            className="table-rows listTable-Row"
                            key={index}
                          >
                            <TableCell> {item?.id} </TableCell>
                            <TableCell> {item?.userId} </TableCell>
                            <TableCell style={{ textTransform: "capitalize" }}>
                              {item?.firstName} {item?.lastName}
                            </TableCell>
                            <TableCell>{item?.email}</TableCell>
                            <TableCell>{item?.nickName ?? ""}</TableCell>
                            <TableCell>{item?.coins}</TableCell>
                            <TableCell>{item?.status}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(item, "edit")}
                                className="table-btn edit-btn"
                              >
                                Add Coins
                              </Button>
                              {/* <Button
                                onClick={this.setItemToDelete(item?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button> */}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                FantasyUserCoinsCount / rowPerPage > 1
                                  ? false
                                  : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={pageNumbers[pageNumbers?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode
                    ? "Create New Fantasy User Coins"
                    : "Edit Fantasy User Coins"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Coins</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="number"
                          color="primary"
                          size="small"
                          placeholder="Coins"
                          value={fantasyUserCoinsValues?.userCoins}
                          onChange={(e) =>
                            this.setState({
                              fantasyUserCoinsValues: {
                                ...fantasyUserCoinsValues,
                                userCoins: e?.target?.value,
                              },
                              //   errorUserCoins: e?.target?.value
                              //     ? ""
                              //     : errorUserCoins,
                            })
                          }
                        />
                        {/* {errorUserCoins ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorUserCoins}
                          </p>
                        ) : (
                          ""
                        )} */}
                      </Grid>

                      <span style={{ fontSize: "12px", color: "red" }}>
                        {errorCreate ? errorCreate : ""}
                      </span>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default FantasyUserCoins;
