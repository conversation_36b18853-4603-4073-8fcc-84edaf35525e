import React, { createRef } from "react";
import "../apiprovider.scss";
import { Grid } from "@mui/material";
import { apiProviderFormModel } from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
import FileUploader from "../../../library/common/components/FileUploader";
import { config } from "../../../helpers/config";
import { setValidation } from "../../../helpers/common";

let apiProviderFormModelArray = apiProviderFormModel;

class CreateApiProvider extends React.Component {
  formRef = createRef();

  constructor(props) {
    super(props);
    this.state = {
      values: {
        providerName: "",
        apiKeyId: "",
        apiAccessKey: "",
        status: "active",
        displayOrder: "",
        responseType: "json",
        dateType: "UTC",
      },
      logo: [],
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
    };
  }

  componentDidMount() {
    if (this.props.isEditMode) {
      this.fetchCurrentApiProvider(this.props.id);
    }
  }
  componentWillUnmount() {
    apiProviderFormModelArray = apiProviderFormModelArray.map((fieldItem) => {
      return { ...fieldItem, errorMessage: "" };
    });
  }

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } });
  };

  fetchCurrentApiProvider = async (id) => {
    const { status, data } = await axiosInstance.get(
      URLS.apiProvider + `/${id}`
    );
    if (status === 200) {
      this.setState({ values: data.result[0] });
    }
  };

  validate = () => {
    let {
      providerName,
      apiKeyId,
      apiAccessKey,
      status,
      displayOrder,
      responseType,
      dateType,
    } = this.state.values;
    let flag = true;
    if (
      providerName === "" ||
      apiKeyId === "" ||
      apiAccessKey === "" ||
      status === "" ||
      displayOrder === "" ||
      responseType === "" ||
      dateType === ""
    ) {
      flag = false;
      // this.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      //this.setActionMessage(false);
    }
    // if (flag === false) {
    //   this.setState({ isLoading: false });
    // }
    return flag;
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handleSave = async () => {
    const { isEditMode } = this.props;
    const { logo } = this.state;
    this.setState({ isLoading: true });

    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode
        ? `${URLS.apiProvider}/${this.props.id}`
        : URLS.apiProvider;

      const values = removeErrorFieldsFromValues(form.formData);

      apiProviderFormModelArray = apiProviderFormModelArray?.map(
        (fieldItem) => {
          return setValidation(fieldItem, values);
        }
      );

      if (this.validate()) {
        if (logo.length > 0) {
          let fileData = await this.setMedia(logo[0]);
          if (fileData) {
            values["logo"] = fileData?.image?.filePath;
          }
        }
        const { status } = await axiosInstance[method](url, values);
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          this.props.fetchAllApiProvider();
          this.setActionMessage(
            true,
            "Success",
            `Api Provider ${isEditMode ? "Edited" : "Created"} Successfully`
          );
        }
      }
    } catch (err) {
      this.setState({ isLoading: false });
      this.setActionMessage(
        true,
        "Error",
        `An error occurred while ${
          isEditMode ? "editing" : "creating"
        } Api Provider`
      );
    }
  };

  handleChange = (field, value) => {
    let values = { ...this.state.values, [field]: value };
    this.setState({ values: values });
    apiProviderFormModelArray = apiProviderFormModelArray?.map((fieldItem) => {
      if (field === fieldItem?.field) {
        return setValidation(fieldItem, values);
      } else {
        return fieldItem;
      }
    });
    this.setActionMessage(false);
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };

  render() {
    var { values, messageBox, isLoading, logo } = this.state;
    var { isEditMode } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            {/* <Paper > */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Form
              values={values}
              model={apiProviderFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            />
            <div className="blog-file-upload">
              <h6>Logo</h6>
              <FileUploader
                onDrop={(logo) => this.handleFileUpload("logo", logo)}
              />
              <div className="logocontainer">
                {logo?.length > 0
                  ? logo.map((file, index) => (
                      <img
                        className="auto-width"
                        key={index}
                        src={file.preview}
                        alt="file"
                      />
                    ))
                  : values?.logo &&
                    values?.logo !== "" && (
                      <img
                        className="auto-width"
                        src={config.mediaUrl + values?.logo}
                        alt="file"
                      />
                    )}
              </div>
            </div>

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                      style={{ minWidth: "auto" }}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Cancle"
                    style={{ minWidth: "auto" }}
                  />
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateApiProvider;
