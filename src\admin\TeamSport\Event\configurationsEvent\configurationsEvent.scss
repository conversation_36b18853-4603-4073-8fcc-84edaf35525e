.configuration-event-screen {
    text-align: left;

    .first-row-container {
        margin-bottom: 18px;

        .event-type-select {
            width: 98%;

            .select__control {
                margin-top: 0px;
            }
        }

        .teamsport-textfield {
            width: 98%;
        }
    }

    .prizePool-header-container {
        margin-bottom: 18px;

        p {
            font-size: 22.4px;
            line-height: 26px;
            font-weight: 600;
            color: #191919;
        }

    }

    .prizePool-row-container {
        display: flex;
        align-items: flex-start;
        column-gap: 18px;

        &:not(:last-child) {
            margin-bottom: 18px;
        }

        .prizePool-col {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .remove-icon {
            min-width: 30px !important;
            padding: 0px;
        }
    }

    .draw-pool-container {
        display: flex;
        align-items: flex-end;
        width: 100%;
        margin-bottom: 18px;
    }

}