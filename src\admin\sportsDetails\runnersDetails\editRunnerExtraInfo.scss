.runnerInfo-contanier {
  .runnerInfo {
    display: flex;

    .runnerInfo-text {
      display: flex;
      flex-direction: column;

      .textfield-tracks {
        width: 95%;
        margin-bottom: 15px;
        margin-top: 3px;

        .MuiOutlinedInput-root {
          border-radius: 8px;

          .MuiOutlinedInput-inputMarginDense {
            padding: 10.5px;
            background-color: #ffffff;
          }
          .MuiOutlinedInput-inputMultiline {
            padding: 10.5px;
          }
        }
      }
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }
}

.details-runner-picker {
  width: 95%;
  margin-top: 20px;

  .MuiOutlinedInput-root {
    border-radius: 8px;

    .MuiOutlinedInput-input {
      padding: 10.5px;
      background-color: #ffffff;
      border-radius: 8px;
    }
  }
}
