import React, { useEffect, Suspense, useMemo } from "react";
import { Route, Routes, Navigate, useLocation } from "react-router-dom";
import PropTypes from "prop-types";
import ReactGA from "react-ga";

import { adminRoutes } from "./adminRoutes";
import { publicRoutes } from "./publicRoutes";
import { secureRoutes } from "./secureRoutes";
import NotFound from "../404/404";
import Layout from "../layouts/layout/layout";
import DefaultLayout from "../../admin/Module/DefaultLayout";
import BlankLayout from "../layouts/blanklayout/blanklayout";
import withRouter from './withRouter'; // Import the withRouter HOC

const AppRoutes = ({ isLoggedIn, loginuser, ...props }) => {
  const location = useLocation();

  useEffect(() => {
    ReactGA.initialize("UA-85164364-32");
    ReactGA.pageview(location.pathname);
  }, []);

  useEffect(() => {
    ReactGA.pageview(location.pathname);
    window.scrollTo(0, 0);
  }, [location.pathname]);

  const routes = useMemo(() => {
    let routesArr = [...publicRoutes];

    if (isLoggedIn) {
      routesArr = [...secureRoutes, ...publicRoutes];
      if (loginuser?.role === "admin") {
        routesArr = [...secureRoutes, ...adminRoutes];
      } else if (loginuser?.role === "expertTip") {
        routesArr = [...secureRoutes, ...adminRoutes].filter(
          (item) =>
            item?.path === "/expert-tips" ||
            item?.path === "/sport-expert-tips" ||
            item?.path === "/backawinner/featured-race" ||
            item?.path === "/backawinner/weekly-newsletter" ||
            item?.path === "*" ||
            item?.path === "/dashboard"
        );
      }
    }

    return routesArr;
  }, [isLoggedIn, loginuser]);

  const renderRoutes = useMemo(() => {
    return routes.map((route, i) => {
      const RouteComponent = withRouter(route.component); // Wrap component with withRouter
      return (
        <Route
          key={i + route.index}
          path={route.path}
          element={
            route?.adminroute ? (
              <DefaultLayout>
                <RouteComponent {...props} isLoggedIn={isLoggedIn} loginuser={loginuser} />
              </DefaultLayout>
            ) : route.blankLayout ? (
              <BlankLayout>
                <RouteComponent {...props} isLoggedIn={isLoggedIn} loginuser={loginuser} />
              </BlankLayout>
            ) : (
              <Layout>
                <RouteComponent {...props} isLoggedIn={isLoggedIn} loginuser={loginuser} />
              </Layout>
            )
          }
          exact={route.exact}
        />
      );
    });
  }, [routes, isLoggedIn, loginuser]);

  return (
    <Suspense fallback={<>Loading...</>}>
      <Routes>
        {renderRoutes}
        <Route
          path="/"
          exact
          element={<Navigate to={isLoggedIn === true ? "/dashboard" : "/login"} />}
        />
        {isLoggedIn === false && (
          <Route
            path="*"
            element={
              <Layout>
                <NotFound isLoggedIn={isLoggedIn} loginuser={loginuser} />
              </Layout>
            }
          />
        )}
      </Routes>
    </Suspense>
  );
};

AppRoutes.propTypes = {
  isLoggedIn: PropTypes.bool,
  loginuser: PropTypes.object,
};

AppRoutes.defaultProps = {
  isLoggedIn: false,
  loginuser: {},
};

export default AppRoutes;
