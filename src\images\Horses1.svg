<svg id="Component_1_1" data-name="Component 1 – 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="33.396" height="20.5" viewBox="0 0 33.396 20.5">
  <defs>
    <style>
      .cls-1, .cls-2, .cls-3 {
        fill: #fff;
      }

      .cls-1 {
        clip-rule: evenodd;
      }

      .cls-3 {
        fill-rule: evenodd;
      }

      .cls-4 {
        clip-path: url(#clip-path);
      }

      .cls-5 {
        clip-path: url(#clip-path-2);
      }
    </style>
    <clipPath id="clip-path">
      <path id="Path_1577" data-name="Path 1577" class="cls-1" d="M305.642,480.687a6.022,6.022,0,0,0-.929-1.211c-.32-.253-.422-.458-.573-.472s-.293.074-.389-.041-.142-.307-.307-.321a.28.28,0,0,0-.247.069.266.266,0,0,1-.307-.041c-.137-.1-.586-.225-.71-.307s-.06.143-.014.184-.063.014-.288-.041-.394-.041-.2.151.3.39.444.458.033.143-.206.253-2.072,1.375-2.788,1.793a20.087,20.087,0,0,0-1.98,1.29c-.4.319-4.013.366-4.625.262s-3.547-.4-4.282-.521a5.446,5.446,0,0,0-2.9-.039c-1.048.4-2.5,1.155-2.707,2.554s-.228,2.233.98,3.047,3.041,1.585,3.062,2.588a12.562,12.562,0,0,1-.1,1.8c-.021.354.021.811.5.958a17.031,17.031,0,0,1,2.769,1.21c.416.315,1.666,1.189,1.75,1.336a1.156,1.156,0,0,1,.063.522c0,.126.335.312.521.4s.456.228.6.291.21-.249.189-.583a1.335,1.335,0,0,0-.458-1.086,5.609,5.609,0,0,1-.689-.709,1.418,1.418,0,0,0-.644-.48,14.442,14.442,0,0,1-2.25-1.168,1.287,1.287,0,0,1-.582-1.21c0-.627.1-1.9.144-2.191a3.475,3.475,0,0,0,.126-.772c-.021-.27.123-.168.312.021a9.5,9.5,0,0,0,1.854,1.063,20.826,20.826,0,0,0,2.061.543c.563.105,1,.168,1.124.21s.1.252.126.4.4.294.563.4a14.4,14.4,0,0,0,1.561,1.375,7.546,7.546,0,0,1,1.228.855c.147.126.063.333.063.4s-.21.213-.605.315a6.519,6.519,0,0,1-1.354.207,1.819,1.819,0,0,1-.977-.186c-.21-.1-.021-.291-.1-.417a.922.922,0,0,0-.833-.417,1.064,1.064,0,0,0-.686.228c-.1.042-.149.165.144.375s.566.522.731.564a1.505,1.505,0,0,1,.665.354c.207.207.479.585.854.522s1.375-.438,1.708-.543a3.342,3.342,0,0,1,.707-.168.528.528,0,0,0,.605-.249c.228-.354.291-.42.312-.585s.165-.48-.147-.709a7.487,7.487,0,0,1-.917-1.378c-.165-.228-.623-1.042-.623-1.042a5.1,5.1,0,0,0,.686-.291c.314-.168.728-.606.9-.627a2.789,2.789,0,0,0,.749-.333,2.145,2.145,0,0,0,.71-1,6.909,6.909,0,0,0,.437-1.793c.042-.522.081-.876.081-.876a1.305,1.305,0,0,1,.23-.495,10.686,10.686,0,0,0,.929-1.43,2.008,2.008,0,0,1,.627-.848,1.171,1.171,0,0,1,.723-.2c.247.027.452.184.677.225s1.077.582,1.392.664.636.2.69.335.046.321.2.321.581.069.608-.027.088-.162.2-.115.444-.1.471-.247-.055-.349.082-.445.247-.357.11-.5-.279-.261-.389-.39-.951-1.285-1.134-1.518A2.934,2.934,0,0,1,305.642,480.687Z" transform="translate(-282.537 -478.344)"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_1576" data-name="Path 1576" class="cls-2" d="M305.642,480.687a6.022,6.022,0,0,0-.929-1.211c-.32-.253-.422-.458-.573-.472s-.293.074-.389-.041-.142-.307-.307-.321a.28.28,0,0,0-.247.069.266.266,0,0,1-.307-.041c-.137-.1-.586-.225-.71-.307s-.06.143-.014.184-.063.014-.288-.041-.394-.041-.2.151.3.39.444.458.033.143-.206.253-2.072,1.375-2.788,1.793a20.087,20.087,0,0,0-1.98,1.29c-.4.319-4.013.366-4.625.262s-3.547-.4-4.282-.521a5.446,5.446,0,0,0-2.9-.039c-1.048.4-2.5,1.155-2.707,2.554s-.228,2.233.98,3.047,3.041,1.585,3.062,2.588a12.562,12.562,0,0,1-.1,1.8c-.021.354.021.811.5.958a17.031,17.031,0,0,1,2.769,1.21c.416.315,1.666,1.189,1.75,1.336a1.156,1.156,0,0,1,.063.522c0,.126.335.312.521.4s.456.228.6.291.21-.249.189-.583a1.335,1.335,0,0,0-.458-1.086,5.609,5.609,0,0,1-.689-.709,1.418,1.418,0,0,0-.644-.48,14.442,14.442,0,0,1-2.25-1.168,1.287,1.287,0,0,1-.582-1.21c0-.627.1-1.9.144-2.191a3.475,3.475,0,0,0,.126-.772c-.021-.27.123-.168.312.021a9.5,9.5,0,0,0,1.854,1.063,20.826,20.826,0,0,0,2.061.543c.563.105,1,.168,1.124.21s.1.252.126.4.4.294.563.4a14.4,14.4,0,0,0,1.561,1.375,7.546,7.546,0,0,1,1.228.855c.147.126.063.333.063.4s-.21.213-.605.315a6.519,6.519,0,0,1-1.354.207,1.819,1.819,0,0,1-.977-.186c-.21-.1-.021-.291-.1-.417a.922.922,0,0,0-.833-.417,1.064,1.064,0,0,0-.686.228c-.1.042-.149.165.144.375s.566.522.731.564a1.505,1.505,0,0,1,.665.354c.207.207.479.585.854.522s1.375-.438,1.708-.543a3.342,3.342,0,0,1,.707-.168.528.528,0,0,0,.605-.249c.228-.354.291-.42.312-.585s.165-.48-.147-.709a7.487,7.487,0,0,1-.917-1.378c-.165-.228-.623-1.042-.623-1.042a5.1,5.1,0,0,0,.686-.291c.314-.168.728-.606.9-.627a2.789,2.789,0,0,0,.749-.333,2.145,2.145,0,0,0,.71-1,6.909,6.909,0,0,0,.437-1.793c.042-.522.081-.876.081-.876a1.305,1.305,0,0,1,.23-.495,10.686,10.686,0,0,0,.929-1.43,2.008,2.008,0,0,1,.627-.848,1.171,1.171,0,0,1,.723-.2c.247.027.452.184.677.225s1.077.582,1.392.664.636.2.69.335.046.321.2.321.581.069.608-.027.088-.162.2-.115.444-.1.471-.247-.055-.349.082-.445.247-.357.11-.5-.279-.261-.389-.39-.951-1.285-1.134-1.518A2.934,2.934,0,0,1,305.642,480.687Z" transform="translate(-282.537 -478.344)"/>
    </clipPath>
  </defs>
  <g id="Group_6616" data-name="Group 6616" transform="translate(21.023 11.401)">
    <path id="Path_1510" data-name="Path 1510" class="cls-3" d="M495.985,642.1c-.078.146-.1.166-.214.224a.412.412,0,0,1-.167.029c-.068-.01.067-.039.136-.107S496.063,641.957,495.985,642.1Z" transform="translate(-495.586 -642.055)"/>
  </g>
  <g id="Group_6617" data-name="Group 6617" transform="translate(20.576 11.808)">
    <path id="Path_1511" data-name="Path 1511" class="cls-3" d="M488.6,648.943c-.02.039-.164.224-.253.244a.72.72,0,0,1-.244.05c-.136,0,.067-.021.156-.079s-.261.185-.156.02a1.32,1.32,0,0,1,.215-.234C488.366,648.895,488.62,648.9,488.6,648.943Z" transform="translate(-488.057 -648.91)"/>
  </g>
  <g id="Group_6618" data-name="Group 6618" transform="translate(20.218 11.843)">
    <path id="Path_1512" data-name="Path 1512" class="cls-3" d="M482.478,649.53a.671.671,0,0,1-.2.2.446.446,0,0,1-.244.029c-.049,0,.038-.019.165-.1S482.527,649.442,482.478,649.53Z" transform="translate(-482.015 -649.502)"/>
  </g>
  <g id="Group_6619" data-name="Group 6619" transform="translate(18.446 12.169)">
    <path id="Path_1513" data-name="Path 1513" class="cls-3" d="M453.219,655.006a2.333,2.333,0,0,1-.586.137,1.528,1.528,0,0,1-.39-.049c-.1-.029-.186-.029.078-.039s.536-.039.7-.049A.78.78,0,0,1,453.219,655.006Z" transform="translate(-452.146 -655.002)"/>
  </g>
  <g id="Group_6620" data-name="Group 6620" transform="translate(17.953 11.771)">
    <path id="Path_1514" data-name="Path 1514" class="cls-3" d="M444.256,648.393c-.244-.01-.274-.1-.342-.088s-.157-.02.1.1a.678.678,0,0,0,.458.1C444.579,648.48,444.5,648.4,444.256,648.393Z" transform="translate(-443.842 -648.304)"/>
  </g>
  <g id="Group_6621" data-name="Group 6621" transform="translate(17.953 11.146)">
    <path id="Path_1515" data-name="Path 1515" class="cls-3" d="M443.872,637.794a1.334,1.334,0,0,0,.42.264c.117.039.038-.011-.167-.147S443.794,637.707,443.872,637.794Z" transform="translate(-443.849 -637.755)"/>
  </g>
  <g id="Group_6622" data-name="Group 6622" transform="translate(18.573 10.261)">
    <path id="Path_1516" data-name="Path 1516" class="cls-3" d="M454.371,622.881c.127.118.224.167.243.235s-.057.029-.195-.088S454.245,622.763,454.371,622.881Z" transform="translate(-454.294 -622.838)"/>
  </g>
  <g id="Group_6623" data-name="Group 6623" transform="translate(17.851 10.064)">
    <path id="Path_1517" data-name="Path 1517" class="cls-3" d="M442.183,619.579c.147.166.255.186.392.352s-.195-.028-.3-.146S442.036,619.413,442.183,619.579Z" transform="translate(-442.117 -619.524)"/>
  </g>
  <g id="Group_6624" data-name="Group 6624" transform="translate(19.171 10.199)">
    <path id="Path_1518" data-name="Path 1518" class="cls-3" d="M464.49,621.821a2.164,2.164,0,0,1,.322.245c.078.078.05.068-.156-.01s-.254-.157-.283-.216S464.422,621.782,464.49,621.821Z" transform="translate(-464.367 -621.794)"/>
  </g>
  <g id="Group_6625" data-name="Group 6625" transform="translate(19.318 10.512)">
    <path id="Path_1519" data-name="Path 1519" class="cls-3" d="M466.876,627.085c.089.049.214,0,.313.1s.156.2-.039.128S466.787,627.036,466.876,627.085Z" transform="translate(-466.852 -627.078)"/>
  </g>
  <g id="Group_6626" data-name="Group 6626" transform="translate(19.049 10.557)">
    <path id="Path_1520" data-name="Path 1520" class="cls-3" d="M462.449,627.907a1.483,1.483,0,0,0,.342.284c.2.117-.03.049-.167-.02s-.165-.157-.253-.245S462.313,627.789,462.449,627.907Z" transform="translate(-462.321 -627.838)"/>
  </g>
  <g id="Group_6627" data-name="Group 6627" transform="translate(18.478 9.798)">
    <path id="Path_1521" data-name="Path 1521" class="cls-3" d="M452.9,615.133c.273.177.2.285-.029.118S452.626,614.957,452.9,615.133Z" transform="translate(-452.698 -615.048)"/>
  </g>
  <g id="Group_6628" data-name="Group 6628" transform="translate(18.889 10.12)">
    <path id="Path_1522" data-name="Path 1522" class="cls-3" d="M459.757,620.537c.146.167.068.118.156.206s.108.157-.078.039-.264-.323-.2-.264S459.611,620.37,459.757,620.537Z" transform="translate(-459.615 -620.47)"/>
  </g>
  <g id="Group_6634" data-name="Group 6634" transform="translate(15.92 7.142)">
    <path id="Path_1532" data-name="Path 1532" class="cls-3" d="M417.478,570.267a2.7,2.7,0,0,0-1.2.762,6.729,6.729,0,0,1-1.37,1.174,6.094,6.094,0,0,1-1.338.759,2.138,2.138,0,0,1-.709-.1,1.081,1.081,0,0,1-.442-.407c-.032-.082-.011.1.085.206a1.125,1.125,0,0,0,.491.3c.17.032.309.083.384.083s-.2.093-.309.093a2.063,2.063,0,0,1-.65-.381c-.117-.113-.1-.031.011.072a1.424,1.424,0,0,0,.235.186,1.732,1.732,0,0,1-.448-.206,3.119,3.119,0,0,0-.671-.268,6.156,6.156,0,0,0-.725-.1c-.128,0-.639.021-.735.041s.095.051.255.031a3.3,3.3,0,0,1,.565-.01c.223.01.331.154.245.113s-.554-.1-.352-.031.267.1.106.145a1.2,1.2,0,0,0-.352.154.522.522,0,0,1-.373.041c-.139-.021-.011.062.138.1a1.208,1.208,0,0,1,.352.165,3.9,3.9,0,0,0,.981.33c.213.01.2.062.416.062s.182.071-.106.082a2.656,2.656,0,0,1-.821-.072,3.048,3.048,0,0,0-.533-.154c-.128-.021-.149-.052.224.113s-.106.031-.267-.021a1.746,1.746,0,0,0-.479-.082c-.128,0,.095.031.245.062s.694.289.874.35a1.594,1.594,0,0,0,.352.062c.159.031-.427,0-.587-.051s-.618-.268-.789-.3a2.555,2.555,0,0,0-.288-.031c-.074-.01.107.062.256.082a2.867,2.867,0,0,1,.6.237,1.489,1.489,0,0,0,.438.154,1.922,1.922,0,0,1-.491-.031c-.16-.041-.469-.165-.64-.226s-.341-.031-.106.031a8.448,8.448,0,0,0,.959.3c.17,0,.447.021.223.1a1.765,1.765,0,0,1-.757-.031,2.433,2.433,0,0,1-.362-.217c-.117-.062-.128-.04.043.073a1.85,1.85,0,0,0,.768.267s.235-.021.064.031a3.642,3.642,0,0,0-.832.351,3.065,3.065,0,0,0-.458.413c-.064.1.065,0,.2-.124a3.073,3.073,0,0,1,.522-.371c.128-.062.374-.134.48-.175s.351-.072-.033.1a4.568,4.568,0,0,0-.906.536c.191-.082.533-.3.672-.34a2.589,2.589,0,0,1,.522-.175c.139,0,.3.113.426.113a5.183,5.183,0,0,0,1.023.021,2.889,2.889,0,0,0,.917-.4c.149-.1.2-.031.053.093a2.266,2.266,0,0,1-.533.33,1.178,1.178,0,0,1-.533.073c-.235-.01-.64-.072-.384.021a1.284,1.284,0,0,0,.469.093c.064,0-.182.1-.33.134a10.776,10.776,0,0,0-1.087.248,1.225,1.225,0,0,0-.352.175c-.149.1-.033.114.064.041a.935.935,0,0,1,.384-.176c.181-.041.832-.134,1.119-.154a4.732,4.732,0,0,0,1-.289,2.177,2.177,0,0,0,.629-.309c.117-.113.181-.123.117-.01s-.244.2-.33.268.181,0,.255-.082.235-.216.309-.319.426-.382.331-.176a2.373,2.373,0,0,1-.618.6,2.305,2.305,0,0,1-.639.278,1.735,1.735,0,0,1-.395.051c-.16.01.138.051.277.021a2.71,2.71,0,0,0,.682-.217c.224-.123.394-.237.394-.237A2.086,2.086,0,0,1,414,575a1.523,1.523,0,0,1-.437.206c-.213.051-.033.082.128.021a1.16,1.16,0,0,0,.469-.227,3.458,3.458,0,0,0,.394-.361c.064-.1.2-.4.331-.577a2.687,2.687,0,0,1,.426-.536.839.839,0,0,1,.3-.144c.053,0,.022.217-.1.381a4.716,4.716,0,0,1-.5.67,2.018,2.018,0,0,1-.362.237c-.128.073-.266.165-.16.145a.988.988,0,0,0,.362-.114c.106-.072.042.052-.075.155s-.308.257-.16.2.2-.02.394-.237.416-.607.565-.762.33-.444.426-.516.491-.092.618-.165a3.4,3.4,0,0,0,.49-.381c.053-.062-.106-1.082-.011-1.432S417.478,570.267,417.478,570.267Z" transform="translate(-409.58 -570.267)"/>
  </g>
  <g id="Group_6635" data-name="Group 6635" transform="translate(17.569 9.614)">
    <path id="Path_1533" data-name="Path 1533" class="cls-3" d="M438.206,612.308a1.591,1.591,0,0,0-.554-.309c-.245-.072-.416-.083-.16.062a2.164,2.164,0,0,0,.5.248A.641.641,0,0,0,438.206,612.308Z" transform="translate(-437.365 -611.947)"/>
  </g>
  <g id="Group_6636" data-name="Group 6636" transform="translate(16.837 9.852)">
    <path id="Path_1534" data-name="Path 1534" class="cls-3" d="M426.881,616.36c.138.01-.5-.1-.682-.165a5.245,5.245,0,0,0-.586-.206,3,3,0,0,0-.458-.041c-.17,0-.182.041.074.072a2.109,2.109,0,0,1,.586.134,3.1,3.1,0,0,0,.564.217C426.55,616.391,426.743,616.35,426.881,616.36Z" transform="translate(-425.031 -615.948)"/>
  </g>
  <g id="Group_6637" data-name="Group 6637" transform="translate(17.548 10.58)">
    <path id="Path_1535" data-name="Path 1535" class="cls-3" d="M438.231,628.655c-.075-.021-.512-.237-.661-.309a2.239,2.239,0,0,0-.522-.123c-.118-.021.128.051.32.123s.4.217.511.258a.827.827,0,0,0,.352.113,1.679,1.679,0,0,0,.576-.113c.117-.062-.085-.021-.277.021S438.305,628.675,438.231,628.655Z" transform="translate(-437.018 -628.219)"/>
  </g>
  <g id="Group_6638" data-name="Group 6638" transform="translate(21.108 9.871)">
    <path id="Path_1536" data-name="Path 1536" class="cls-3" d="M497.713,616.362a2.784,2.784,0,0,0-.458.546c-.117.186-.159.267-.213.35s0,.021.2-.216a6.438,6.438,0,0,1,.49-.567C497.9,616.332,497.926,616.157,497.713,616.362Z" transform="translate(-497.023 -616.268)"/>
  </g>
  <g id="Group_6641" data-name="Group 6641" transform="translate(18.119 4.572)">
    <path id="Path_1542" data-name="Path 1542" class="cls-3" d="M448.042,526.952c-.072.018-1.31.543-1.31.543l-.09.2s.741-.262.9-.335l.772-.343Z" transform="translate(-446.642 -526.952)"/>
  </g>
  <g id="Group_6642" data-name="Group 6642" transform="translate(17.41 4.892)">
    <path id="Path_1543" data-name="Path 1543" class="cls-3" d="M437.483,532.345a5.263,5.263,0,0,1-.795.425,4.233,4.233,0,0,1-.976.181c-.226.018-.388.018-.388.018l-.081.172s.379-.054.506-.054a6.16,6.16,0,0,0,.9-.172,3.3,3.3,0,0,1,.343-.109s-.208.878-.416,1.457a2.844,2.844,0,0,1-.912,1.312,5.9,5.9,0,0,1-.7.3.755.755,0,0,1-.262-.018l-.018.181a.669.669,0,0,0,.28,0,6.393,6.393,0,0,0,1.183-.57,8.539,8.539,0,0,0,.605-1.222c.072-.2.37-1.421.443-1.511a3.711,3.711,0,0,1,.567-.357C437.861,532.323,437.483,532.345,437.483,532.345Z" transform="translate(-434.692 -532.34)"/>
  </g>
  <g id="Group_6643" data-name="Group 6643" transform="translate(8.383 1.688)">
    <g id="Group_6593" data-name="Group 6593" transform="translate(0 0)">
      <g id="Group_6592" data-name="Group 6592" class="cls-4">
        <g id="Group_6591" data-name="Group 6591">
          <g id="Group_6590" data-name="Group 6590">
            <g id="Group_6589" data-name="Group 6589" class="cls-5">
              <g id="Group_6588" data-name="Group 6588" transform="translate(-14.122 -5.332)">
                <path id="Path_1544" data-name="Path 1544" class="cls-2" d="M307.549,478.344H282.536v18.512h25.013V478.344Z" transform="translate(-268.414 -473.012)"/>
                <ellipse id="Ellipse_65" data-name="Ellipse 65" class="cls-2" cx="9.018" cy="11.702" rx="9.018" ry="11.702" transform="matrix(0.351, -0.936, 0.936, 0.351, 0, 16.888)"/>
                <ellipse id="Ellipse_66" data-name="Ellipse 66" class="cls-2" cx="8.922" cy="11.577" rx="8.922" ry="11.577" transform="translate(0.15 16.842) rotate(-69.445)"/>
                <ellipse id="Ellipse_67" data-name="Ellipse 67" class="cls-2" cx="8.826" cy="11.453" rx="8.826" ry="11.453" transform="translate(0.3 16.795) rotate(-69.446)"/>
                <ellipse id="Ellipse_68" data-name="Ellipse 68" class="cls-2" cx="8.73" cy="11.328" rx="8.73" ry="11.328" transform="translate(0.452 16.75) rotate(-69.447)"/>
                <ellipse id="Ellipse_69" data-name="Ellipse 69" class="cls-2" cx="8.634" cy="11.204" rx="8.634" ry="11.204" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.601, 16.705)"/>
                <ellipse id="Ellipse_70" data-name="Ellipse 70" class="cls-2" cx="8.538" cy="11.079" rx="8.538" ry="11.079" transform="translate(0.751 16.658) rotate(-69.447)"/>
                <ellipse id="Ellipse_71" data-name="Ellipse 71" class="cls-2" cx="8.442" cy="10.955" rx="8.442" ry="10.955" transform="matrix(0.351, -0.936, 0.936, 0.351, 0.901, 16.612)"/>
                <ellipse id="Ellipse_72" data-name="Ellipse 72" class="cls-2" cx="8.346" cy="10.83" rx="8.346" ry="10.83" transform="translate(1.051 16.567) rotate(-69.445)"/>
                <ellipse id="Ellipse_73" data-name="Ellipse 73" class="cls-2" cx="8.25" cy="10.706" rx="8.25" ry="10.706" transform="translate(1.203 16.52) rotate(-69.447)"/>
                <ellipse id="Ellipse_74" data-name="Ellipse 74" class="cls-2" cx="8.154" cy="10.582" rx="8.154" ry="10.582" transform="translate(1.353 16.474) rotate(-69.447)"/>
                <ellipse id="Ellipse_75" data-name="Ellipse 75" class="cls-2" cx="8.059" cy="10.457" rx="8.059" ry="10.457" transform="translate(1.503 16.429) rotate(-69.446)"/>
                <ellipse id="Ellipse_76" data-name="Ellipse 76" class="cls-2" cx="7.963" cy="10.332" rx="7.963" ry="10.332" transform="translate(1.653 16.382) rotate(-69.447)"/>
                <ellipse id="Ellipse_77" data-name="Ellipse 77" class="cls-2" cx="7.867" cy="10.208" rx="7.867" ry="10.208" transform="translate(1.803 16.336) rotate(-69.446)"/>
                <ellipse id="Ellipse_78" data-name="Ellipse 78" class="cls-2" cx="7.771" cy="10.084" rx="7.771" ry="10.084" transform="translate(1.954 16.291) rotate(-69.447)"/>
                <ellipse id="Ellipse_79" data-name="Ellipse 79" class="cls-2" cx="7.675" cy="9.959" rx="7.675" ry="9.959" transform="translate(2.104 16.242) rotate(-69.447)"/>
                <ellipse id="Ellipse_80" data-name="Ellipse 80" class="cls-2" cx="7.579" cy="9.835" rx="7.579" ry="9.835" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.254, 16.197)"/>
                <ellipse id="Ellipse_81" data-name="Ellipse 81" class="cls-2" cx="7.483" cy="9.71" rx="7.483" ry="9.71" transform="translate(2.404 16.15) rotate(-69.447)"/>
                <ellipse id="Ellipse_82" data-name="Ellipse 82" class="cls-2" cx="7.387" cy="9.586" rx="7.387" ry="9.586" transform="translate(2.554 16.104) rotate(-69.446)"/>
                <ellipse id="Ellipse_83" data-name="Ellipse 83" class="cls-2" cx="7.291" cy="9.461" rx="7.291" ry="9.461" transform="matrix(0.351, -0.936, 0.936, 0.351, 2.706, 16.059)"/>
                <ellipse id="Ellipse_84" data-name="Ellipse 84" class="cls-2" cx="7.195" cy="9.337" rx="7.195" ry="9.337" transform="translate(2.856 16.012) rotate(-69.448)"/>
                <ellipse id="Ellipse_85" data-name="Ellipse 85" class="cls-2" cx="7.099" cy="9.212" rx="7.099" ry="9.212" transform="translate(3.005 15.967) rotate(-69.447)"/>
                <ellipse id="Ellipse_86" data-name="Ellipse 86" class="cls-2" cx="7.003" cy="9.088" rx="7.003" ry="9.088" transform="translate(3.155 15.921) rotate(-69.446)"/>
                <ellipse id="Ellipse_87" data-name="Ellipse 87" class="cls-2" cx="6.907" cy="8.963" rx="6.907" ry="8.963" transform="translate(3.305 15.874) rotate(-69.447)"/>
                <ellipse id="Ellipse_88" data-name="Ellipse 88" class="cls-2" cx="6.811" cy="8.839" rx="6.811" ry="8.839" transform="translate(3.457 15.829) rotate(-69.447)"/>
                <ellipse id="Ellipse_89" data-name="Ellipse 89" class="cls-2" cx="6.715" cy="8.714" rx="6.715" ry="8.714" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.607, 15.783)"/>
                <ellipse id="Ellipse_90" data-name="Ellipse 90" class="cls-2" cx="6.619" cy="8.59" rx="6.619" ry="8.59" transform="matrix(0.351, -0.936, 0.936, 0.351, 3.757, 15.736)"/>
                <ellipse id="Ellipse_91" data-name="Ellipse 91" class="cls-2" cx="6.524" cy="8.465" rx="6.524" ry="8.465" transform="translate(3.907 15.691) rotate(-69.447)"/>
                <ellipse id="Ellipse_92" data-name="Ellipse 92" class="cls-2" cx="6.428" cy="8.341" rx="6.428" ry="8.341" transform="translate(4.056 15.644) rotate(-69.444)"/>
                <ellipse id="Ellipse_93" data-name="Ellipse 93" class="cls-2" cx="6.332" cy="8.216" rx="6.332" ry="8.216" transform="translate(4.208 15.597) rotate(-69.447)"/>
                <ellipse id="Ellipse_94" data-name="Ellipse 94" class="cls-2" cx="6.236" cy="8.092" rx="6.236" ry="8.092" transform="translate(4.358 15.551) rotate(-69.446)"/>
                <ellipse id="Ellipse_95" data-name="Ellipse 95" class="cls-2" cx="6.14" cy="7.967" rx="6.14" ry="7.967" transform="translate(4.508 15.504) rotate(-69.448)"/>
                <ellipse id="Ellipse_96" data-name="Ellipse 96" class="cls-2" cx="6.044" cy="7.843" rx="6.044" ry="7.843" transform="translate(4.658 15.459) rotate(-69.447)"/>
                <ellipse id="Ellipse_97" data-name="Ellipse 97" class="cls-2" cx="5.948" cy="7.718" rx="5.948" ry="7.718" transform="translate(4.808 15.413) rotate(-69.445)"/>
                <ellipse id="Ellipse_98" data-name="Ellipse 98" class="cls-2" cx="5.852" cy="7.594" rx="5.852" ry="7.594" transform="translate(4.96 15.367) rotate(-69.449)"/>
                <ellipse id="Ellipse_99" data-name="Ellipse 99" class="cls-2" cx="5.756" cy="7.469" rx="5.756" ry="7.469" transform="matrix(0.351, -0.936, 0.936, 0.351, 5.11, 15.321)"/>
                <ellipse id="Ellipse_100" data-name="Ellipse 100" class="cls-2" cx="5.66" cy="7.345" rx="5.66" ry="7.345" transform="translate(5.259 15.276) rotate(-69.446)"/>
                <path id="Path_1545" data-name="Path 1545" class="cls-2" d="M163.947,507.192c0-3.153,3.151-5.266,7.037-4.72s7.037,3.545,7.037,6.7-3.151,5.266-7.037,4.72S163.947,510.345,163.947,507.192Z" transform="translate(-156.861 -495.628)"/>
                <ellipse id="Ellipse_101" data-name="Ellipse 101" class="cls-2" cx="5.468" cy="7.096" rx="5.468" ry="7.096" transform="translate(5.559 15.183) rotate(-69.447)"/>
                <ellipse id="Ellipse_102" data-name="Ellipse 102" class="cls-2" cx="5.372" cy="6.971" rx="5.372" ry="6.971" transform="translate(5.711 15.138) rotate(-69.447)"/>
                <ellipse id="Ellipse_103" data-name="Ellipse 103" class="cls-2" cx="5.276" cy="6.847" rx="5.276" ry="6.847" transform="translate(5.861 15.091) rotate(-69.449)"/>
                <ellipse id="Ellipse_104" data-name="Ellipse 104" class="cls-2" cx="5.18" cy="6.722" rx="5.18" ry="6.722" transform="translate(6.011 15.045) rotate(-69.448)"/>
                <ellipse id="Ellipse_105" data-name="Ellipse 105" class="cls-2" cx="5.085" cy="6.598" rx="5.085" ry="6.598" transform="translate(6.161 14.997) rotate(-69.448)"/>
                <ellipse id="Ellipse_106" data-name="Ellipse 106" class="cls-2" cx="4.989" cy="6.473" rx="4.989" ry="6.473" transform="translate(6.311 14.951) rotate(-69.446)"/>
                <ellipse id="Ellipse_107" data-name="Ellipse 107" class="cls-2" cx="4.893" cy="6.349" rx="4.893" ry="6.349" transform="translate(6.462 14.906) rotate(-69.447)"/>
                <ellipse id="Ellipse_108" data-name="Ellipse 108" class="cls-2" cx="4.797" cy="6.224" rx="4.797" ry="6.224" transform="translate(6.612 14.859) rotate(-69.449)"/>
                <path id="Path_1546" data-name="Path 1546" class="cls-2" d="M182.362,521.6c0-2.664,2.662-4.449,5.946-3.987s5.945,3,5.945,5.659-2.662,4.449-5.946,3.987S182.362,524.264,182.362,521.6Z" transform="translate(-174.183 -509.883)"/>
                <ellipse id="Ellipse_109" data-name="Ellipse 109" class="cls-2" cx="4.605" cy="5.975" rx="4.605" ry="5.975" transform="translate(6.912 14.768) rotate(-69.446)"/>
                <ellipse id="Ellipse_110" data-name="Ellipse 110" class="cls-2" cx="4.509" cy="5.851" rx="4.509" ry="5.851" transform="matrix(0.351, -0.936, 0.936, 0.351, 7.062, 14.721)"/>
                <ellipse id="Ellipse_111" data-name="Ellipse 111" class="cls-2" cx="4.413" cy="5.726" rx="4.413" ry="5.726" transform="translate(7.212 14.675) rotate(-69.446)"/>
                <ellipse id="Ellipse_112" data-name="Ellipse 112" class="cls-2" cx="4.317" cy="5.602" rx="4.317" ry="5.602" transform="translate(7.364 14.63) rotate(-69.447)"/>
                <path id="Path_1547" data-name="Path 1547" class="cls-2" d="M192.59,529.611c0-2.392,2.39-3.995,5.339-3.581s5.339,2.689,5.339,5.081-2.39,3.995-5.339,3.581S192.59,532,192.59,529.611Z" transform="translate(-183.804 -517.808)"/>
                <ellipse id="Ellipse_113" data-name="Ellipse 113" class="cls-2" cx="4.125" cy="5.353" rx="4.125" ry="5.353" transform="translate(7.664 14.538) rotate(-69.448)"/>
                <path id="Path_1548" data-name="Path 1548" class="cls-2" d="M196.668,532.843c0-2.283,2.282-3.813,5.1-3.418s5.1,2.567,5.1,4.85-2.282,3.813-5.1,3.418S196.668,535.126,196.668,532.843Z" transform="translate(-187.64 -521.004)"/>
                <ellipse id="Ellipse_114" data-name="Ellipse 114" class="cls-2" cx="3.933" cy="5.104" rx="3.933" ry="5.104" transform="translate(7.963 14.445) rotate(-69.448)"/>
                <path id="Path_1549" data-name="Path 1549" class="cls-2" d="M200.778,536.008c0-2.174,2.173-3.632,4.854-3.255s4.853,2.445,4.853,4.619-2.173,3.632-4.854,3.255S200.778,538.183,200.778,536.008Z" transform="translate(-191.507 -524.138)"/>
                <ellipse id="Ellipse_115" data-name="Ellipse 115" class="cls-2" cx="3.742" cy="4.855" rx="3.742" ry="4.855" transform="translate(8.265 14.351) rotate(-69.449)"/>
                <path id="Path_1550" data-name="Path 1550" class="cls-2" d="M204.856,539.206c0-2.066,2.064-3.45,4.611-3.092s4.611,2.323,4.611,4.388-2.064,3.45-4.611,3.092S204.856,541.272,204.856,539.206Z" transform="translate(-195.343 -527.301)"/>
                <ellipse id="Ellipse_116" data-name="Ellipse 116" class="cls-2" cx="3.55" cy="4.606" rx="3.55" ry="4.606" transform="translate(8.565 14.26) rotate(-69.445)"/>
                <path id="Path_1551" data-name="Path 1551" class="cls-2" d="M208.934,542.4c0-1.957,1.956-3.269,4.368-2.93s4.368,2.2,4.368,4.157-1.956,3.269-4.368,2.93S208.934,544.36,208.934,542.4Z" transform="translate(-199.179 -530.465)"/>
                <ellipse id="Ellipse_117" data-name="Ellipse 117" class="cls-2" cx="3.358" cy="4.357" rx="3.358" ry="4.357" transform="translate(8.866 14.168) rotate(-69.449)"/>
                <ellipse id="Ellipse_118" data-name="Ellipse 118" class="cls-2" cx="3.262" cy="4.233" rx="3.262" ry="4.233" transform="translate(9.016 14.122) rotate(-69.447)"/>
                <path id="Path_1552" data-name="Path 1552" class="cls-2" d="M215.084,547.217c0-1.794,1.793-3,4-2.685s4,2.017,4,3.811-1.793,3-4,2.685S215.084,549.01,215.084,547.217Z" transform="translate(-204.964 -535.226)"/>
                <ellipse id="Ellipse_119" data-name="Ellipse 119" class="cls-2" cx="3.07" cy="3.984" rx="3.07" ry="3.984" transform="translate(9.316 14.03) rotate(-69.448)"/>
                <path id="Path_1553" data-name="Path 1553" class="cls-2" d="M219.162,550.448c0-1.685,1.684-2.815,3.761-2.522s3.761,1.895,3.761,3.58-1.684,2.815-3.761,2.522S219.162,552.133,219.162,550.448Z" transform="translate(-208.8 -538.422)"/>
                <ellipse id="Ellipse_120" data-name="Ellipse 120" class="cls-2" cx="2.878" cy="3.735" rx="2.878" ry="3.735" transform="translate(9.618 13.938) rotate(-69.452)"/>
                <ellipse id="Ellipse_121" data-name="Ellipse 121" class="cls-2" cx="2.782" cy="3.61" rx="2.782" ry="3.61" transform="matrix(0.351, -0.936, 0.936, 0.351, 9.768, 13.892)"/>
                <ellipse id="Ellipse_122" data-name="Ellipse 122" class="cls-2" cx="2.686" cy="3.486" rx="2.686" ry="3.486" transform="translate(9.918 13.847) rotate(-69.447)"/>
                <path id="Path_1554" data-name="Path 1554" class="cls-2" d="M227.35,556.843c0-1.468,1.467-2.451,3.276-2.2s3.276,1.65,3.276,3.118-1.467,2.451-3.276,2.2S227.35,558.311,227.35,556.843Z" transform="translate(-216.502 -544.749)"/>
                <ellipse id="Ellipse_123" data-name="Ellipse 123" class="cls-2" cx="2.494" cy="3.237" rx="2.494" ry="3.237" transform="translate(10.217 13.752) rotate(-69.445)"/>
                <path id="Path_1555" data-name="Path 1555" class="cls-2" d="M231.46,560.009c0-1.359,1.358-2.27,3.034-2.034a3.324,3.324,0,0,1,3.033,2.887c0,1.359-1.358,2.27-3.034,2.034A3.324,3.324,0,0,1,231.46,560.009Z" transform="translate(-220.368 -547.883)"/>
                <ellipse id="Ellipse_124" data-name="Ellipse 124" class="cls-2" cx="2.302" cy="2.988" rx="2.302" ry="2.988" transform="translate(10.519 13.66) rotate(-69.45)"/>
                <ellipse id="Ellipse_125" data-name="Ellipse 125" class="cls-2" cx="2.207" cy="2.863" rx="2.207" ry="2.863" transform="matrix(0.351, -0.936, 0.936, 0.351, 10.669, 13.615)"/>
                <path id="Path_1556" data-name="Path 1556" class="cls-2" d="M237.578,564.822c0-1.2,1.2-2,2.669-1.79a2.925,2.925,0,0,1,2.669,2.541c0,1.2-1.2,2-2.67,1.79A2.925,2.925,0,0,1,237.578,564.822Z" transform="translate(-226.123 -552.645)"/>
                <path id="Path_1557" data-name="Path 1557" class="cls-2" d="M239.617,566.438c0-1.141,1.141-1.906,2.548-1.709a2.792,2.792,0,0,1,2.548,2.425c0,1.142-1.141,1.907-2.548,1.709A2.792,2.792,0,0,1,239.617,566.438Z" transform="translate(-228.041 -554.243)"/>
                <path id="Path_1558" data-name="Path 1558" class="cls-2" d="M241.688,568.054c0-1.087,1.087-1.816,2.427-1.627a2.659,2.659,0,0,1,2.427,2.31c0,1.087-1.087,1.816-2.427,1.627A2.659,2.659,0,0,1,241.688,568.054Z" transform="translate(-229.989 -555.841)"/>
                <path id="Path_1559" data-name="Path 1559" class="cls-2" d="M243.727,569.635c0-1.033,1.032-1.725,2.306-1.546a2.526,2.526,0,0,1,2.305,2.194c0,1.033-1.032,1.725-2.306,1.546A2.526,2.526,0,0,1,243.727,569.635Z" transform="translate(-231.907 -557.406)"/>
                <ellipse id="Ellipse_126" data-name="Ellipse 126" class="cls-2" cx="1.727" cy="2.241" rx="1.727" ry="2.241" transform="translate(11.42 13.384) rotate(-69.451)"/>
                <path id="Path_1560" data-name="Path 1560" class="cls-2" d="M247.805,572.867c0-.924.924-1.543,2.063-1.383a2.26,2.26,0,0,1,2.063,1.963c0,.924-.924,1.543-2.063,1.383A2.26,2.26,0,0,1,247.805,572.867Z" transform="translate(-235.743 -560.602)"/>
                <ellipse id="Ellipse_127" data-name="Ellipse 127" class="cls-2" cx="1.535" cy="1.992" rx="1.535" ry="1.992" transform="translate(11.72 13.292) rotate(-69.453)"/>
                <path id="Path_1561" data-name="Path 1561" class="cls-2" d="M251.915,576.064c0-.815.815-1.362,1.82-1.22a1.994,1.994,0,0,1,1.82,1.732c0,.815-.815,1.362-1.82,1.22A1.994,1.994,0,0,1,251.915,576.064Z" transform="translate(-239.61 -563.766)"/>
                <path id="Path_1562" data-name="Path 1562" class="cls-2" d="M253.954,577.68c0-.761.761-1.271,1.7-1.139a1.862,1.862,0,0,1,1.7,1.617c0,.761-.761,1.271-1.7,1.139A1.862,1.862,0,0,1,253.954,577.68Z" transform="translate(-241.528 -565.364)"/>
                <path id="Path_1563" data-name="Path 1563" class="cls-2" d="M255.993,579.229c0-.707.706-1.18,1.578-1.058a1.728,1.728,0,0,1,1.577,1.5c0,.707-.706,1.18-1.578,1.058A1.728,1.728,0,0,1,255.993,579.229Z" transform="translate(-243.446 -566.899)"/>
                <path id="Path_1564" data-name="Path 1564" class="cls-2" d="M258.033,580.846c0-.652.652-1.089,1.456-.976a1.6,1.6,0,0,1,1.456,1.386c0,.652-.652,1.089-1.456.976A1.6,1.6,0,0,1,258.033,580.846Z" transform="translate(-245.365 -568.497)"/>
                <path id="Path_1565" data-name="Path 1565" class="cls-2" d="M260.072,582.427c0-.6.6-1,1.335-.9a1.462,1.462,0,0,1,1.335,1.27c0,.6-.6,1-1.335.9A1.462,1.462,0,0,1,260.072,582.427Z" transform="translate(-247.283 -570.062)"/>
                <path id="Path_1566" data-name="Path 1566" class="cls-2" d="M262.143,584.043c0-.544.543-.908,1.214-.814a1.329,1.329,0,0,1,1.213,1.155c0,.544-.543.908-1.214.814A1.329,1.329,0,0,1,262.143,584.043Z" transform="translate(-249.231 -571.661)"/>
                <path id="Path_1567" data-name="Path 1567" class="cls-2" d="M264.182,585.659c0-.489.489-.817,1.092-.732a1.2,1.2,0,0,1,1.092,1.039c0,.489-.489.817-1.092.732A1.2,1.2,0,0,1,264.182,585.659Z" transform="translate(-251.149 -573.259)"/>
                <path id="Path_1568" data-name="Path 1568" class="cls-2" d="M266.221,587.241c0-.435.435-.726.971-.651a1.063,1.063,0,0,1,.971.924c0,.435-.435.726-.971.651A1.064,1.064,0,0,1,266.221,587.241Z" transform="translate(-253.067 -574.825)"/>
                <path id="Path_1569" data-name="Path 1569" class="cls-2" d="M268.26,588.857c0-.381.38-.636.849-.57a.931.931,0,0,1,.849.808c0,.38-.38.635-.85.57A.931.931,0,0,1,268.26,588.857Z" transform="translate(-254.985 -576.422)"/>
                <path id="Path_1570" data-name="Path 1570" class="cls-2" d="M270.3,590.472c0-.326.326-.544.728-.488a.8.8,0,0,1,.728.693c0,.326-.326.545-.728.488A.8.8,0,0,1,270.3,590.472Z" transform="translate(-256.903 -578.02)"/>
                <path id="Path_1571" data-name="Path 1571" class="cls-2" d="M272.37,592.054c0-.272.272-.454.607-.407a.665.665,0,0,1,.607.577c0,.272-.272.454-.607.407A.665.665,0,0,1,272.37,592.054Z" transform="translate(-258.851 -579.586)"/>
                <path id="Path_1572" data-name="Path 1572" class="cls-2" d="M274.41,593.669c0-.217.217-.363.486-.325a.532.532,0,0,1,.485.462c0,.217-.217.363-.486.325A.532.532,0,0,1,274.41,593.669Z" transform="translate(-260.77 -581.183)"/>
                <path id="Path_1573" data-name="Path 1573" class="cls-2" d="M276.449,595.286c0-.163.163-.272.364-.244a.4.4,0,0,1,.364.346c0,.163-.163.272-.364.244A.4.4,0,0,1,276.449,595.286Z" transform="translate(-262.688 -582.782)"/>
                <path id="Path_1574" data-name="Path 1574" class="cls-2" d="M278.488,596.867c0-.109.109-.181.243-.163a.266.266,0,0,1,.243.231c0,.109-.109.182-.243.163A.266.266,0,0,1,278.488,596.867Z" transform="translate(-264.606 -584.347)"/>
                <path id="Path_1575" data-name="Path 1575" class="cls-2" d="M280.527,598.483c0-.054.054-.091.122-.081a.133.133,0,0,1,.121.115c0,.054-.055.091-.121.081A.133.133,0,0,1,280.527,598.483Z" transform="translate(-266.524 -585.946)"/>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
  <g id="Group_6644" data-name="Group 6644" transform="translate(22.648 12.564)">
    <path id="Path_1578" data-name="Path 1578" class="cls-3" d="M525.69,661.672a3.78,3.78,0,0,0,1.183.7c.678.284,1.117.51,1.294.573a.8.8,0,0,1,.505.479,2.233,2.233,0,0,1,.262,1.012,16.162,16.162,0,0,0-.173,2.02,2.59,2.59,0,0,1-.133,1.034,7.633,7.633,0,0,0-.416,1.185c0,.155-.04.222-.284.55s-.266.524-.549.151a1.385,1.385,0,0,1-.306-1.074c.062-.288.3-.262.479-.328a.459.459,0,0,0,.222-.595,1.231,1.231,0,0,1,.066-.724c.044-.178.151-1.607.151-1.958a.928.928,0,0,0-.2-.679,1.4,1.4,0,0,0-.789-.328c-.221,0-.988-.067-1.338-.067s-1.1-.133-1.4-.133a2.327,2.327,0,0,1-1.028-.284c-.417-.2-.284-.107.089-.306S525.69,661.672,525.69,661.672Z" transform="translate(-522.978 -661.672)"/>
  </g>
  <g id="Group_6645" data-name="Group 6645" transform="translate(23.505 2.485)">
    <path id="Path_1579" data-name="Path 1579" class="cls-3" d="M541.652,492.028a.628.628,0,0,0-.364-.194c-.181-.053-.5-.079-.43,0s.063.207-.144.207-.413-.026-.413.066a.405.405,0,0,1-.288.352c-.194.039-.6.25-.377.263s-.154.141-.351.141-.331.009-.5.009-1.021-.152-.864-.073.081.169-.064.13-.44-.052-.259.026.162.208.031.261-.284-.066-.156,0,.444.263.26.3-.391.113-.194.192.259.273.325.365.121.2.288.118,2.121-.825,2.367-.954a12.263,12.263,0,0,0,1.053-1.12C541.636,492.094,541.754,492.144,541.652,492.028Z" transform="translate(-537.426 -491.785)"/>
  </g>
  <g id="Group_6646" data-name="Group 6646" transform="translate(16.733 5.469)">
    <path id="Path_1580" data-name="Path 1580" class="cls-3" d="M424.03,542.14a5.729,5.729,0,0,0-.613-.049c-.122-.033-.194.042-.105.148a1.439,1.439,0,0,0,.571.4c.229.066,1.42.27,1.551.27s1.046,0,1.511-.033.335-.5.335-.5S424.293,542.206,424.03,542.14Z" transform="translate(-423.273 -542.083)"/>
  </g>
  <g id="Group_6647" data-name="Group 6647" transform="translate(16.87 2.412)">
    <path id="Path_1581" data-name="Path 1581" class="cls-3" d="M426.872,490.567c.082.049.159.4.379.595s.489.352.505.466.138.106.367.188,1.708.5,2.026.631.922.361,1,.565a.987.987,0,0,1-.187.94c-.262.246-.482.19-.58.3a2.011,2.011,0,0,1-.482.253l-.689-.336s-.225-.185-1.058-.168a2.94,2.94,0,0,1-1.82-.511c-.36-.226-.784-.523-.751-1.055A1.605,1.605,0,0,1,426,491.3c.285-.312.355-.388.5-.552S426.79,490.518,426.872,490.567Z" transform="translate(-425.583 -490.551)"/>
  </g>
  <g id="Group_6648" data-name="Group 6648" transform="translate(17.771 1.434)">
    <path id="Path_1582" data-name="Path 1582" class="cls-3" d="M446.414,474.134a1.9,1.9,0,0,1,.443,1.081c.052.614.748.451.515.5s-.958.064-.919.153a1.578,1.578,0,0,1,.026.638c-.039.233-.194.289.039.483s.233.312.351.26a7.166,7.166,0,0,0,.794-.572c.207-.171.328-.325.508-.444s.154-.128.3-.013a1.259,1.259,0,0,1,.259.391c.066.1.147.089-.089.286s-.39.46-.574.641a10.2,10.2,0,0,1-.912.884c-.167.092-.453.381-.636.315a2.1,2.1,0,0,1-.613-.614c-.079-.131-.259-.506-.3-.6a3.534,3.534,0,0,1-.236-.43c-.049-.158-.128-.171-.285-.144a1.386,1.386,0,0,1-.482.039c-.207-.026-2.464-.383-2.648-.462a1,1,0,0,1-.4-.407,5.355,5.355,0,0,0-.736-.664c-.144-.105.067-.3.343-.5a7.308,7.308,0,0,1,1.954-.756,11.082,11.082,0,0,1,1.692-.131c.312.013,1,.013,1.223.039S446.362,474.095,446.414,474.134Z" transform="translate(-440.778 -474.066)"/>
  </g>
  <g id="Group_6649" data-name="Group 6649" transform="translate(17.441 5.626)">
    <path id="Path_1583" data-name="Path 1583" class="cls-3" d="M438.7,544.733s-.44.157-.8.312-.931.565-1.38.835a1.933,1.933,0,0,0-.662.588c-.089.115-.229.237-.4.417a1,1,0,0,0-.236.532c0,.066.105.091.253.164s.384.253.449.2.124.016.278.082.489.4.636.417.669.033.784.033.089-.2-.1-.27a.7.7,0,0,1-.456-.377,1.139,1.139,0,0,1-.131-.6c.033-.148.229-.237.417-.352s.889-.417,1.135-.549a6.387,6.387,0,0,1,.7-.3c.131-.049-.023-.345-.2-.68A2.475,2.475,0,0,0,438.7,544.733Z" transform="translate(-435.217 -544.733)"/>
  </g>
  <g id="Group_6650" data-name="Group 6650" transform="translate(0 6.934)">
    <path id="Path_1584" data-name="Path 1584" class="cls-3" d="M150.318,566.762a3.087,3.087,0,0,0-1.377.908,7.821,7.821,0,0,1-1.574,1.4,6.945,6.945,0,0,1-1.538.9,2.376,2.376,0,0,1-.815-.124,1.261,1.261,0,0,1-.508-.484c-.037-.1-.013.123.1.246a1.286,1.286,0,0,0,.564.355c.2.038.355.1.441.1s-.233.11-.355.11a2.351,2.351,0,0,1-.747-.454c-.135-.135-.11-.037.012.086a1.652,1.652,0,0,0,.27.221,1.955,1.955,0,0,1-.515-.246,3.515,3.515,0,0,0-.771-.319,6.851,6.851,0,0,0-.833-.122c-.148,0-.734.024-.845.049s.109.061.293.037a3.674,3.674,0,0,1,.65-.012c.257.012.381.184.282.135s-.636-.123-.4-.037.306.123.122.172a1.361,1.361,0,0,0-.4.184.58.58,0,0,1-.429.049c-.16-.025-.013.074.159.123a1.371,1.371,0,0,1,.4.2,4.373,4.373,0,0,0,1.127.393c.245.012.232.073.478.073s.209.085-.122.1a2.951,2.951,0,0,1-.944-.086,3.405,3.405,0,0,0-.612-.184c-.147-.024-.171-.062.258.135s-.122.037-.306-.025a1.94,1.94,0,0,0-.551-.1c-.148,0,.109.037.281.073s.8.344,1,.417a1.774,1.774,0,0,0,.4.073c.183.037-.491,0-.674-.061s-.71-.319-.906-.356a2.846,2.846,0,0,0-.331-.037c-.085-.012.123.073.294.1a3.223,3.223,0,0,1,.685.283,1.67,1.67,0,0,0,.5.184,2.121,2.121,0,0,1-.564-.037c-.183-.049-.54-.2-.735-.269s-.392-.037-.122.037a9.46,9.46,0,0,0,1.1.356c.2,0,.513.025.257.122a1.96,1.96,0,0,1-.87-.037,2.777,2.777,0,0,1-.416-.258c-.135-.073-.148-.048.049.087a2.078,2.078,0,0,0,.883.318s.27-.025.073.037a4.1,4.1,0,0,0-.956.417,3.569,3.569,0,0,0-.526.492c-.073.122.074,0,.233-.148a3.52,3.52,0,0,1,.6-.442c.148-.073.43-.159.552-.208s.4-.086-.038.122a5.205,5.205,0,0,0-1.041.638c.22-.1.613-.356.772-.405a2.894,2.894,0,0,1,.6-.208c.16,0,.342.135.49.135a5.741,5.741,0,0,0,1.176.024,3.266,3.266,0,0,0,1.054-.478c.171-.122.232-.038.061.11a2.594,2.594,0,0,1-.613.393,1.309,1.309,0,0,1-.612.087c-.27-.012-.735-.086-.441.024a1.429,1.429,0,0,0,.539.11c.073,0-.209.123-.38.159a12.062,12.062,0,0,0-1.249.295,1.387,1.387,0,0,0-.4.209c-.171.122-.038.136.073.049a1.058,1.058,0,0,1,.442-.209c.208-.049.956-.159,1.286-.184a5.29,5.29,0,0,0,1.151-.344,2.472,2.472,0,0,0,.723-.368c.135-.135.208-.147.135-.012s-.28.234-.379.319.208,0,.293-.1.27-.257.355-.381.49-.455.38-.209a2.769,2.769,0,0,1-.711.712,2.6,2.6,0,0,1-.734.332,1.922,1.922,0,0,1-.454.061c-.183.012.159.061.319.025a3.044,3.044,0,0,0,.784-.258c.258-.147.453-.282.453-.282a2.431,2.431,0,0,1-.355.343,1.728,1.728,0,0,1-.5.246c-.244.061-.037.1.147.025a1.314,1.314,0,0,0,.54-.27,4.029,4.029,0,0,0,.453-.43c.073-.122.233-.478.38-.687a3.185,3.185,0,0,1,.49-.639.949.949,0,0,1,.343-.171c.061,0,.025.258-.11.454a5.6,5.6,0,0,1-.576.8,2.307,2.307,0,0,1-.416.282c-.147.087-.306.2-.183.172a1.1,1.1,0,0,0,.416-.135c.122-.086.048.062-.086.185s-.355.306-.183.233.232-.024.453-.282.478-.723.65-.908.38-.528.49-.614.564-.109.711-.2A3.928,3.928,0,0,0,149.9,570c.061-.074-.122-1.289-.012-1.706S150.318,566.762,150.318,566.762Z" transform="translate(-141.241 -566.762)"/>
  </g>
  <g id="Group_6651" data-name="Group 6651" transform="translate(5.962 10.184)">
    <path id="Path_1588" data-name="Path 1588" class="cls-3" d="M242.532,621.66a3.3,3.3,0,0,0-.526.65c-.134.221-.183.318-.244.417s0,.025.232-.257a7.613,7.613,0,0,1,.563-.675C242.74,621.623,242.776,621.415,242.532,621.66Z" transform="translate(-241.738 -621.547)"/>
  </g>
  <g id="Group_6652" data-name="Group 6652" transform="translate(7.175 10.427)">
    <path id="Path_1589" data-name="Path 1589" class="cls-3" d="M262.822,625.717c-.233.16-.79.342-.613.295a1.287,1.287,0,0,0,.54-.171C263.055,625.668,263.055,625.557,262.822,625.717Z" transform="translate(-262.175 -625.64)"/>
  </g>
  <g id="Group_6653" data-name="Group 6653" transform="translate(13.937 11.289)">
    <path id="Path_1590" data-name="Path 1590" class="cls-3" d="M377.648,641.173c0,.451.148,2.336.148,2.887a3.413,3.413,0,0,0,.719,1.8c.282.249,2.694,2.14,3.03,2.288a2.6,2.6,0,0,1,1.216.8c.369.5.215.417-.349.417a4.4,4.4,0,0,1-.978-.026c-.086-.051-.2-.2-.158-.31a.225.225,0,0,0-.065-.251,3.107,3.107,0,0,0-.265-.194c-.05-.029-4.5-3.574-4.712-3.725s.022-2.57.044-3.272.044-1.2.044-1.336.018-.138.261.107A12.3,12.3,0,0,0,377.648,641.173Z" transform="translate(-376.141 -640.165)"/>
  </g>
  <g id="Group_6654" data-name="Group 6654" transform="translate(25.086 6.024)">
    <path id="Path_1591" data-name="Path 1591" class="cls-3" d="M570.97,552.591s-2.248-.345-2.631-.411-2.583-.441-2.966-.5-.643-.13-.95-.185-.35-.065-.35-.065l-.011.152s.109.04.437.141,1.138.2,1.423.264,1.315.253,1.73.318,1.77.22,1.958.275.856.163.964.174.385.027.385.027Z" transform="translate(-564.061 -551.435)"/>
  </g>
  <g id="Group_6655" data-name="Group 6655" transform="translate(23.333 1.483)">
    <path id="Path_1592" data-name="Path 1592" class="cls-3" d="M536.713,474.975a.433.433,0,0,1-.093.318c-.064.041-.063.067,0,.212s.086.213.109.275-.006.125-.108.109-.162-.011-.165.078,0,.13-.046.14-.075.035-.043.076-.021.06-.048.105,0,.141,0,.175a.131.131,0,0,1-.146.106c-.09-.011-.313-.044-.383-.045a4.515,4.515,0,0,0-.457-.008c-.065.015-.314-.333-.473-.484a1.86,1.86,0,0,1-.281-.6c-.168-.349.043-.225.2-.326a.617.617,0,0,1,.531-.123c.231.055.59-.1.809-.1A3.206,3.206,0,0,1,536.713,474.975Z" transform="translate(-534.519 -474.885)"/>
  </g>
  <g id="Group_6656" data-name="Group 6656" transform="translate(22.945)">
    <path id="Path_1593" data-name="Path 1593" class="cls-3" d="M530.43,450.383a1.5,1.5,0,0,0-1.035-.49,1.451,1.451,0,0,0-.729.205,1.161,1.161,0,0,0-.474.618c-.1.282-.029.352-.127.563s-.126.185.058.3.245.218.359.124a.887.887,0,0,1,.5-.2.812.812,0,0,0,.453.028,3.006,3.006,0,0,1,.824-.021c.139.02.411.026.451.038a2.345,2.345,0,0,0-.043-.649A1.071,1.071,0,0,0,530.43,450.383Z" transform="translate(-527.988 -449.893)"/>
  </g>
  <g id="Group_6657" data-name="Group 6657" transform="translate(24.945 0.716)">
    <path id="Path_1594" data-name="Path 1594" class="cls-3" d="M562.445,462a.793.793,0,0,1,.091.467c-.035.155-.074.146-.172.133a2.528,2.528,0,0,1-.527-.1c-.105-.054-.212-.207-.084-.325a.817.817,0,0,1,.437-.209C562.327,461.956,562.4,461.942,562.445,462Z" transform="translate(-561.695 -461.958)"/>
  </g>
</svg>
