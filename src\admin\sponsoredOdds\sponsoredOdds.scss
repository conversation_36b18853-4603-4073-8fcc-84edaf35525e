.admin-filter-wrap {
  align-items: center;

  .sponsored-select {
    width: 30%;

    .select__value-container {
      padding: 5px;
      text-align: left;
    }
  }
}

.date-picker-sponsored {
  margin: 0px !important;
  width: 30%;

  .MuiOutlinedInput-input {
    padding: 8px 14px;
    min-height: 25px;
    background-color: #ffffff;
  }

  .MuiOutlinedInput-adornedEnd {
    padding-right: 0px;
    border-radius: 8px;
    background-color: #ffffff;

    // .MuiOutlinedInput-input {
    //     padding: 14.5px 0px 14.5px 12px;
    // }
  }

  .MuiInputAdornment-root {
    .MuiButtonBase-root {
      min-width: 0px;
    }
  }
}

.date-picker-fixture-modal {
  width: 100%;
  margin: 0px !important;

  .MuiInputBase-root {
    min-height: 45px;
    width: 97%;
    border-radius: 8px;
  }
}

.sponsored-select-modal {
  width: 100%;

  .select__control {
    margin: 0px;
    min-height: 45px;
  }
}

.modal-label {
  margin-bottom: 5px;
}
