import { Validators } from "../../../library/utilities/Validators";

export const runnerFormModel = [
  {
    label: "Horse Name*",
    value: "",
    type: "text",
    placeholder: "Horse Name",
    field: "particiantId",
    validators: [
      { check: Validators.required },
      { check: Validators.number, message: "Insert Number Only" },
    ],
    required: true,
    className: "6",
  },
  {
    label: "Jockey Name*",
    value: "",
    type: "dropdown",
    placeholder: "Jockey Name",
    field: "jockeyId",
    required: true,
    validators: [
      { check: Validators.required },
      // { check: Validators.number, message: "Insert Number Only" },
    ],
    className: "6",
    options: [],
  },
  {
    label: "Trainer Name",
    value: "",
    type: "dropdown",
    placeholder: "Trainer Name",
    field: "trainerId",
    required: false,
    // validators: [
    //   { check: Validators.required },
    //   // { check: Validators.number, message: "Insert Number Only" },
    // ],
    className: "6",
    options: [],
  },
  {
    label: "Barrier Number*",
    value: "",
    type: "text",
    placeholder: "Barrier Number",
    field: "barrierNumber",
    required: true,
    validators: [{ check: Validators.required }],
    className: "6",
  },
  {
    label: "Runner Number*",
    value: "",
    type: "text",
    placeholder: "Runner Number",
    field: "runnerNumber",
    required: true,
    validators: [
      { check: Validators.required },
      { check: Validators.number, message: "Insert Number Only" },
    ],
    className: "6",
  },
];

export const runnerHorseFormModel = [
  {
    label: "Horse Name*",
    value: "",
    type: "text",
    placeholder: "Horse Name",
    field: "particiantId",
    validators: [
      { check: Validators.required },
      { check: Validators.number, message: "Insert Number Only" },
    ],
    required: true,
    className: "6",
  },
  {
    label: "Weight*",
    value: "",
    type: "text",
    placeholder: "Weight",
    field: "JockeyWeight",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
  },
  {
    label: "Jockey Name*",
    value: "",
    type: "dropdown",
    placeholder: "Jockey Name",
    field: "jockeyId",
    required: true,
    validators: [
      { check: Validators.required },
      // { check: Validators.number, message: "Insert Number Only" },
    ],
    className: "6",
    options: [],
  },
  {
    label: "Trainer Name",
    value: "",
    type: "dropdown",
    placeholder: "Trainer Name",
    field: "trainerId",
    required: false,
    // validators: [
    //   { check: Validators.required },
    //   // { check: Validators.number, message: "Insert Number Only" },
    // ],
    className: "6",
    options: [],
  },
  {
    label: "Barrier Number*",
    value: "",
    type: "text",
    placeholder: "Barrier Number",
    field: "barrierNumber",
    required: true,
    validators: [{ check: Validators.required }],
    className: "6",
  },
  {
    label: "Runner Number*",
    value: "",
    type: "text",
    placeholder: "Runner Number",
    field: "runnerNumber",
    required: true,
    validators: [
      { check: Validators.required },
      { check: Validators.number, message: "Insert Number Only" },
    ],
    className: "6",
  },
];

export const raceTableFormModel = [
  // {
  //   label: "Event*",
  //   value: "",
  //   type: "dropdown",
  //   placeholder: "Event",
  //   field: "eventId",
  //   validators: [{ check: Validators.required }],
  //   required: true,
  //   className: "6",
  //   options: [],
  // },
  // {
  //   label: "Race Id*",
  //   value: "",
  //   type: "text",
  //   placeholder: "Race Id",
  //   field: "raceId",
  //   validators: [
  //     { check: Validators.required },
  //     { check: Validators.number, message: "Enter Number Only" },
  //   ],
  //   required: true,
  //   className: "6",
  // },
  {
    label: "Race Name*",
    value: "",
    type: "text",
    placeholder: "Race Name",
    field: "raceName",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
  },
  {
    label: "Description",
    value: "",
    type: "text",
    placeholder: "Description",
    field: "description",
    required: false,
    className: "6",
  },
  {
    label: "Distance*",
    value: "",
    type: "dropdown",
    placeholder: "Distance",
    field: "distance",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
    options: [],
  },
  // {
  //   label: "Track",
  //   value: "",
  //   type: "dropdown",
  //   placeholder: "Track",
  //   field: "trackId",
  //   required: false,
  //   className: "6",
  //   options: [
  //     { value: 1, label: 1 },
  //     { value: 2, label: 2 },
  //     { value: 3, label: 3 },
  //     { value: 4, label: 4 },
  //   ],
  // },
  {
    label: "Sport*",
    value: "",
    type: "dropdown",
    placeholder: "Sport",
    field: "sportId",
    validators: [{ check: Validators.required }],
    required: true,
    className: "6",
    options: [],
  },
  {
    label: "Comment",
    value: "",
    type: "text",
    placeholder: "Comment",
    field: "comment",
    required: false,
    className: "6",
  },
  {
    label: "Weather",
    value: null,
    type: "dropdown",
    placeholder: "Weather",
    field: "weather",
    required: false,
    className: "6",
    options: [],
  },
];
