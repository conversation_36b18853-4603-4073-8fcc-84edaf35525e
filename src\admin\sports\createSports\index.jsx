import React, { createRef } from "react";
import { Grid } from "@mui/material";
import { sportsFormModel } from "./form-constant";
import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
import { setValidation } from "../../../helpers/common";

let sportsFormModelArray = sportsFormModel;

class CreateSports extends React.Component {
  formRef = createRef();

  constructor(props) {
    super(props);
    this.state = {
      values: {
        sportName: "",
        variation: "",
        sportTypeId: "",
        sportFinalCount: "",
      },
      isLoading: false,
      sportsForm: [],
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
    };
  }

  componentDidMount() {
    if (this.props.isEditMode) {
      this.fetchCurrentSports(this.props.id);
    }

    const { allSportsType } = this.props;
    this.setState((prevState) => {
      return {
        values: {
          ...prevState.values,
          sportTypeId: allSportsType?.length > 0 ? allSportsType[0].id : "",
        },
      };
    });
    sportsFormModelArray = sportsFormModelArray?.map((fieldItem) => {
      if (fieldItem?.field === "sportTypeId") {
        return {
          ...fieldItem,
          type: "dropdown",
          options: [
            ...allSportsType?.map((tablecol, i) => {
              return {
                id: i,
                value: tablecol?.id,
                label: tablecol?.sportType,
              };
            }),
          ],
        };
      }
      return fieldItem;
    });
  }

  componentWillUnmount() {
    sportsFormModelArray = sportsFormModelArray.map((fieldItem) => {
      return { ...fieldItem, errorMessage: "" };
    });
  }

  fetchCurrentSports = async (id) => {
    const { status, data } = await axiosInstance.get(URLS.sports + `/${id}`);
    if (status === 200) {
      this.setState({
        values: data.result,
      });
      // let variationArray = JSON.parse("[" + data.result.variation + "]");
      // if (variationArray.length > 0) {
      //   this.setState(() => {
      //     return {
      //       values: {
      //         ...this.state.values,
      //         variation: variationArray[0],
      //       },
      //     };
      //   });
      // }
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } });
  };

  validate = () => {
    let { sportName, sportTypeId } = this.state.values;
    let flag = true;

    if (sportName?.trim() === "" || sportTypeId === "") {
      flag = false;
      this.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      this.setActionMessage(false);
    }

    return flag;
  };

  handleSave = async () => {
    const { isEditMode } = this.props;
    this.setState({ isLoading: true });
    try {
      const { current } = this.formRef;
      const form = current.getFormData();

      const method = isEditMode ? "put" : "post";
      const url = isEditMode ? `${URLS.sports}/${this.props.id}` : URLS.sports;

      const values = removeErrorFieldsFromValues(form.formData);
      sportsFormModelArray = sportsFormModelArray?.map((fieldItem) => {
        return setValidation(fieldItem, values);
      });
      console.log("valuesvaluesvalues", values);
      if (this.validate()) {
        const { status } = await axiosInstance[method](url, values);
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          this.props.fetchAllSports();
          this.setActionMessage(
            true,
            "Success",
            `Sports ${isEditMode ? "Edited" : "Created"} Successfully`
          );
        }
      }
    } catch (err) {
      this.setActionMessage(
        true,
        "Error",
        `An error occurred while ${isEditMode ? "editing" : "creating"} Sports`
      );
    }
  };

  handleChange = (field, value) => {
    console.log("objectfieldvalue", field, value);
    let values = { ...this.state.values, [field]: value };
    this.setState({ values: values });
    sportsFormModelArray = sportsFormModelArray?.map((fieldItem) => {
      if (field === fieldItem?.field) {
        return setValidation(fieldItem, values);
      } else {
        return fieldItem;
      }
    });
    this.setActionMessage(false);
  };

  render() {
    var { values, messageBox, isLoading } = this.state;
    var { isEditMode } = this.props;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            {/* <Paper className="pageWrapper api-provider"> */}
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}

            <Form
              values={values}
              model={sportsFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            />

            <Grid container>
              <Grid item xs={3}>
                <div style={{ marginTop: "20px", display: "flex" }}>
                  {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                    />
                  ) : (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="secondary"
                      value={!isLoading ? "Update" : "Loading..."}
                      disabled={isLoading}
                    />
                  )}

                  <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30"
                    value="Back"
                  />
                </div>
              </Grid>
            </Grid>
            {/* </Paper> */}
          </Grid>
        </Grid>
      </>
    );
  }
}
export default CreateSports;
