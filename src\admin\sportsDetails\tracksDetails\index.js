import React from "react";
import {
  <PERSON><PERSON>,
  Grid,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TextField,
  Modal,
  Typography,
  Box,
  Breadcrumbs,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Link } from "react-router-dom";
import { Loader } from "../../../library/common/components";
// import ButtonComponent from "../../../library/common/components/Button";
import Pagination from "@mui/material/Pagination";
import CancelIcon from "@mui/icons-material/Cancel";
// import arrowLeft from "../../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { URLS } from "../../../library/common/constants";
import axiosInstance from "../../../helpers/Axios";
// import EditIcon from "@mui/icons-material/Edit";
// import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import CreateTrack from "../../track/CreateTrack";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import SearchIcons from "../../../images/searchIcon.svg";
import "./tracksDetails.scss";
import CreateTrackVariation from "./createTrackVariation";
import CreateTrackDateVariation from "./createTrackDateVariation";
import { config } from "../../../helpers/config";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import _ from "lodash";
import Select from "react-select";

class TracksDetails extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      tracksDetails: [],
      isLoading: false,
      isInputModalOpen: false,
      isDeleteModalOpen: false,
      itemToDelete: null,
      idToSend: null,
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      sportCount: null,
      isEditMode: false,
      searchInput: "",
      rowToPass: {},
      isDeleteLoading: "",
      isTrakVariationModalOpen: false,
      trackId: "",
      trackName: "",
      trackVariation: "",
      trackVariationsData: [],
      isTrakDateVariationModalOpen: false,
      trackDateVariationsData: [],
      isViewImgModalOpen: false,
      isViewImgData: {},
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isMergeModalOpen: false,
      searchTrack: [],
      searchTrackCount: 0,
      searchTrackPage: 0,
      isTrackSearch: "",
      ModalTrackCount: 0,
      searchModalTrack: [],
      searchModalTrackCount: 0,
      searchModalTrackPage: 0,
      isModalTrackSearch: "",
      ModalTrackData: [],
      ModalTrackPage: 0,
      ModalChildTrackCount: 0,
      searchModalChildTrack: [],
      searchModalChildTrackCount: 0,
      searchModalChildTrackPage: 0,
      isModalChildTrackSearch: "",
      ModalChildTrackData: [],
      ModalChildTrackPage: 0,
      ModalChildTrackSearch: "",
      ModalParentTrackSearch: "",
      parentTrack: "",
      childTrack: [],
      createError: "",
    };
  }
  componentDidMount() {
    this.fetchAllTracks();
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.match !== this.props.match) {
      this.fetchAllTracks();
    }
    if (prevState.offset !== this.state.offset) {
      this.fetchAllTracks();
    }
  }

  fetchAllTracks = async () => {
    let { rowPerPage, offset, searchInput } = this.state;
    this.setState({ isLoading: true });
    const { status, data } = await axiosInstance.get(
      `track?limit=${rowPerPage}&offset=${offset}&matchString=${searchInput}`
    );
    if (status === 200) {
      const check = data?.result?.rows.sort(function (a, b) {
        // return a.name.
        return (
          a.name.toLowerCase().indexOf(searchInput.toLowerCase()) -
          b.name.toLowerCase().indexOf(searchInput.toLowerCase())
        );
      });
      this.setState({
        tracksDetails: data?.result?.rows?.length ? check : data?.result?.rows,
        isLoading: false,
        // searchInput: "",
        sportCount: data?.result?.count,
      });
    }
  };
  afterChangeRefresh = () => {
    this.fetchAllTracks();
  };
  inputModal = (id) => {
    this.setState({ isInputModalOpen: true, idToSend: id });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
    });
  };
  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };
  mergeModal = (item) => () => {
    this.setState({
      isMergeModalOpen: true,
    });
    this.fetchModalParentTrack(0, "");
    this.fetchModalChildTrack(0, "");
    let Tracks = "";
    Tracks = {
      value: item?.id,
      label: item?.name,
    };
    this.setState({
      parentTrack: Tracks,
      childTrack: [],
    });
  };

  toggleMergeModal = () => {
    this.setState({
      isMergeModalOpen: false,
      createError: "",

      parentTrack: "",
      childTrack: [],
    });
  };

  fetchModalParentTrack = async (ModalTrackPage, searchvalue) => {
    const passApi = `track?limit=${this.state.rowPerPage}&offset=${ModalTrackPage}&matchString=${searchvalue}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      });

      this.setState({
        ModalTrackData: finalData,
        ModalTrackCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomModalParentTrack = (e, type) => {
    let {
      ModalTrackCount,
      ModalTrackPage,
      isModalTrackSearch,
      searchModalTrackCount,
      searchModalTrackPage,
    } = this.state;
    if (
      isModalTrackSearch !== "" &&
      searchModalTrackCount !== Math.ceil(searchModalTrackPage / 20 + 1)
    ) {
      this.handleModalParentTrackInputChange(
        searchModalTrackPage + 20,
        isModalTrackSearch
      );
      this.setState({
        searchModalTrackPage: searchModalTrackPage + 20,
      });
    } else {
      if (
        ModalTrackCount !==
          (ModalTrackCount == 1 ? 1 : Math.ceil(ModalTrackPage / 20)) &&
        isModalTrackSearch == ""
      ) {
        this.fetchModalParentTrack(ModalTrackPage + 20, isModalTrackSearch);
        this.setState({
          ModalTrackPage: ModalTrackPage + 20,
        });
      }
    }
  };
  handleModalParentTrackInputChange = (ModalTrackPage, value) => {
    const passApi = `track?limit=${this.state.rowPerPage}&offset=${ModalTrackPage}&matchString=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        });
        this.setState({
          searchModalTrack: finalData,
          searchModalTrackCount: Math.ceil(count),
          isModalTrackSearch: value,
        });
      }
    });
  };

  fetchModalChildTrack = async (ModalTrackPage, searchvalue) => {
    const passApi = `track?limit=${this.state.rowPerPage}&offset=${ModalTrackPage}&matchString=${searchvalue}`;
    const { status, data } = await axiosInstance.get(passApi);
    if (status === 200) {
      let count = data?.result?.count / 20;
      let newdata = [];
      let categories = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state?.ModalChildTrackData, newdata);
      const sortedData = filterData?.sort((a, b) => {
        return a?.label.localeCompare(b?.label);
      });
      let finalData = _.uniqBy(sortedData, function (e) {
        return e.value;
      }).filter((Track) => Track?.value !== this.state.parentTrack?.value);

      this.setState({
        ModalChildTrackData: finalData,
        ModalChildTrackCount: Math.ceil(count),
      });
    }
  };

  handleOnScrollBottomModalChildTrack = (e, type) => {
    let {
      ModalChildTrackCount,
      ModalChildTrackPage,
      isModalChildTrackSearch,
      searchModalChildTrackCount,
      searchModalChildTrackPage,
    } = this.state;
    if (
      isModalChildTrackSearch !== "" &&
      searchModalChildTrackCount !==
        Math.ceil(searchModalChildTrackPage / 20 + 1)
    ) {
      this.handleModalChildTrackInputChange(
        searchModalChildTrackPage + 20,
        isModalChildTrackSearch
      );
      this.setState({
        searchModalChildTrackPage: searchModalChildTrackPage + 20,
      });
    } else {
      if (
        ModalChildTrackCount !==
          (ModalChildTrackCount == 1
            ? 1
            : Math.ceil(ModalChildTrackPage / 20)) &&
        isModalChildTrackSearch == ""
      ) {
        this.fetchModalChildTrack(
          ModalChildTrackPage + 20,
          isModalChildTrackSearch
        );
        this.setState({
          ModalChildTrackPage: ModalChildTrackPage + 20,
        });
      }
    }
  };
  handleModalChildTrackInputChange = (ModalTrackPage, value) => {
    const passApi = `track?limit=${this.state.rowPerPage}&offset=${ModalTrackPage}&matchString=${value}`;
    axiosInstance.get(passApi).then((data) => {
      if (data?.status === 200) {
        let count = data?.data?.result?.count / 20;
        let newdata = [];
        let categories = data?.data?.result?.rows?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        let filterData = _.unionBy(this.state?.searchModalTrack, newdata);
        const sortedData = filterData?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        let finalData = _.uniqBy(sortedData, function (e) {
          return e.value;
        })?.filter((Track) => Track?.value !== this.state.parentTrack?.value);
        this.setState({
          searchModalChildTrack: finalData,
          searchModalChildTrackCount: Math.ceil(count),
          isModalChildTrackSearch: value,
        });
      }
    });
  };

  handalMergeTrack = async () => {
    this.setState({ isLoading: true });
    let payload = {
      parentTrackId: this.state?.parentTrack?.value,
      childTrackId: this.state?.childTrack?.map((item) => item?.value),
    };
    try {
      const passApi = `track/merge/track`;
      const { status, data } = await axiosInstance.post(passApi, payload);
      if (status === 200) {
        this.setActionMessage(true, "Success", data?.message);
        this.setState({
          isLoading: false,
          isMergeModalOpen: false,
        });
        this.fetchAllTracks(this.state.offset, this.state?.search);
      } else {
        this.setActionMessage(true, "Error", data?.message);
        this.setState({ isLoading: false, createError: data?.message });
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
      this.setState({
        isLoading: false,
        createError: err?.response?.data?.message,
      });
    }
  };
  handleTrackDelete = async () => {
    let { itemToDelete } = this.state;
    this.setState({
      isDeleteLoading: "trackDelete",
      isDeleteModalOpen: false,
    });
    try {
      const { status } = await axiosInstance.delete(`track/${itemToDelete}`);
      if (status === 200) {
        this.afterChangeRefresh();
        this.setState({
          isDeleteLoading: "",
          itemToDelete: null,
        });
      }
    } catch (err) {
      // console.log(err);
    }
  };

  setToDelete = (id) => {
    this.setState({ itemToDelete: id, isDeleteModalOpen: true });
  };

  toggleDeleteModal = () => {
    this.setState({
      isDeleteModalOpen: !this.state.isDeleteModalOpen,
      itemToDelete: null,
    });
  };

  afterChangeRefresh = () => {
    this.fetchAllTracks();
  };

  handlePaginationClick = (event, page) => {
    let {
      rowPerPage,
      // offset
    } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let {
      currentPage,
      //  rowPerPage,
      offset,
    } = this.state;
    // if (navDirection === "prev") {
    //   if (currentPage > 1) {
    //     this.setState({ currentPage: currentPage - 1 });
    //   }
    // } else {
    //   if (currentPage < tracksDetails.length / rowPerPage)
    //     this.setState({ currentPage: currentPage + 1 });
    // }

    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  inputTrackVariationModal = (id, name, variation, trackVariations) => {
    this.setState({
      isTrakVariationModalOpen: true,
      trackId: id,
      trackName: name,
      trackVariation: variation,
      trackVariationsData: trackVariations,
    });
  };
  toggleTrackVariationModal = () => {
    this.setState({
      isTrakVariationModalOpen: !this.state.isTrakVariationModalOpen,
    });
  };
  inputTrackDateVariationModal = (id, name, data) => {
    this.setState({
      isTrakDateVariationModalOpen: true,
      trackId: id,
      trackName: name,
      trackDateVariationsData: data,
    });
  };
  toggleTrackDateVariationModal = () => {
    this.setState({
      isTrakDateVariationModalOpen: !this.state.isTrakDateVariationModalOpen,
    });
  };

  handleKeyDown = (event) => {
    if (event.key === "Enter") {
      this.fetchAllTracks();
    }
  };

  handleClearClick = () => {
    this.setState({ searchInput: "" });
  };

  inputViewImgModal = (data) => {
    this.setState({
      isViewImgModalOpen: true,
      isViewImgData: data,
    });
  };
  toggleViewImgModal = () => {
    this.setState({
      isViewImgModalOpen: false,
      isViewImgData: {},
    });
  };

  render() {
    let {
      tracksDetails,
      isLoading,
      rowPerPage,
      isInputModalOpen,
      currentPage,
      // offset,
      sportCount,
      isDeleteModalOpen,
      isEditMode,
      searchInput,
      rowToPass,
      isDeleteLoading,
      isTrakVariationModalOpen,
      trackId,
      trackName,
      trackVariation,
      // trackVariationsData,
      // trackDateVariationsData,
      isTrakDateVariationModalOpen,
      isViewImgModalOpen,
      isViewImgData,
      messageBox,
      isMergeModalOpen,
      isModalTrackSearch,
      searchModalTrack,
      ModalTrackData,
      isModalChildTrackSearch,
      searchModalChildTrack,
      ModalChildTrackData,
      ModalChildTrackSearch,
      ModalParentTrackSearch,
      createError,
      parentTrack,
      childTrack,
    } = this.state;
    const pageNumbers = [];
    // let currentPageRow = tracksDetails;

    // searchInput !== "" &&
    //   (tracksDetails = tracksDetails?.filter(
    //     (obj) =>
    //       obj?.eventName
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase()) ||
    //       obj?.variation
    //         ?.toString()
    //         .toLowerCase()
    //         .includes(searchInput.toString().toLowerCase())
    //   ));

    if (sportCount > 0) {
      // const indexOfLastTodo = currentPage * rowPerPage;
      // const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
      // currentPageRow = tracksDetails.slice(indexOfFirstTodo, indexOfLastTodo);

      for (let i = 1; i <= Math.ceil(sportCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12}>
            <Box className="pageWrapper">
              {messageBox.display && (
                <ActionMessage
                  message={messageBox.message}
                  type={messageBox.type}
                  styleClass={messageBox.styleClass}
                />
              )}
              {isDeleteLoading === "trackDelete" && (
                <div class="admin-delete-loader">
                  <Loader />
                </div>
              )}
              <Grid container direction="row">
                <Box className="bredcrumn-wrap">
                  <Breadcrumbs
                    separator="/"
                    aria-label="breadcrumb"
                    className="breadcrumb"
                  >
                    <Link underline="hover" color="inherit" to="/dashboard">
                      Home
                    </Link>
                    <Link underline="hover" color="inherit">
                      racing
                    </Link>
                    <Typography className="active_p">tracks</Typography>
                  </Breadcrumbs>
                </Box>
                <Grid container direction="row" alignItems="center">
                  <Grid item xs={4}>
                    <Typography variant="h1" align="left">
                      RACING TRACKS
                    </Typography>
                  </Grid>
                  <Grid item xs={8} className="admin-filter-wrap">
                    <TextField
                      className="textfield-tracks search-track"
                      variant="outlined"
                      color="primary"
                      size="small"
                      style={{
                        width: "450px",
                        color: "#D4D6D8",
                      }}
                      // label="Search"
                      placeholder="Search (searches tracks)"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <img src={SearchIcons} alt="icon" />
                            {/* <SearchIcon /> */}
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            {searchInput && (
                              <IconButton
                                onClick={() => this.handleClearClick()}
                                edge="end"
                                style={{ minWidth: "unset" }}
                                size="large"
                              >
                                <CancelIcon />
                              </IconButton>
                            )}
                          </InputAdornment>
                        ),
                      }}
                      value={searchInput}
                      onChange={(e) =>
                        this.setState({
                          ...this.state.searchInput,
                          searchInput: e.target.value,
                        })
                      }
                      onKeyDown={(e) => this.handleKeyDown(e)}
                    />
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "5px 10px ",
                      }}
                      onClick={() => this.fetchAllTracks()}
                    >
                      Search
                    </Button>
                    <Button
                      variant="contained"
                      style={{
                        backgroundColor: "#4455C7",
                        color: "#fff",
                        borderRadius: "8px",
                        textTransform: "capitalize",
                        padding: "5px 10px ",
                      }}
                      onClick={() => {
                        this.inputModal();
                        this.setState({ isEditMode: false });
                      }}
                    >
                      Add Track
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
              {isLoading && <Loader />}
              {!isLoading && tracksDetails?.length === 0 && (
                <p>No Data Available</p>
              )}
              {!isLoading && tracksDetails?.length > 0 && (
                <>
                  <TableContainer>
                    <Table
                      className="listTable api-provider-listTable"
                      aria-label="simple table"
                    >
                      <TableHead>
                        <TableRow className="tableHead-row">
                          <TableCell>DID</TableCell>
                          <TableCell>Name</TableCell>
                          <TableCell>Feature Image</TableCell>
                          <TableCell
                          // style={{ width: "150px" }}
                          >
                            Race Type
                          </TableCell>
                          <TableCell style={{ minWidth: "170px" }}>
                            Variation
                          </TableCell>
                          <TableCell style={{ minWidth: "170px" }}>
                            Date Variation
                          </TableCell>
                          <TableCell>City</TableCell>
                          <TableCell>State</TableCell>
                          <TableCell>Country</TableCell>
                          <TableCell>Edit</TableCell>
                          <TableCell>Delete</TableCell>
                          <TableCell>Merge</TableCell>
                        </TableRow>
                      </TableHead>

                      <TableBody className="table_body">
                        <TableRow className="table_row">
                          <TableCell
                            colSpan={100}
                            className="table-seprator"
                          ></TableCell>
                        </TableRow>
                        {tracksDetails?.map((track, i) => {
                          let trackTypeAll = "";
                          let dummy =
                            track?.trackType?.length > 0
                              ? track?.trackType?.map((obj) => {
                                  let sportName =
                                    obj?.sportId === 1
                                      ? "Horse Racing"
                                      : obj?.sportId === 2
                                      ? "Harness Racing"
                                      : obj?.sportId === 3
                                      ? "Greyhound Racing"
                                      : "";
                                  trackTypeAll =
                                    trackTypeAll == ""
                                      ? sportName
                                      : trackTypeAll + " , " + sportName;
                                })
                              : "";
                          return (
                            <TableRow key={i} className="listTable-Row">
                              <TableCell>{track?.id}</TableCell>
                              <TableCell>{track?.name}</TableCell>
                              <TableCell>
                                {track?.banner_image && (
                                  <Box
                                    onClick={() => {
                                      this.inputViewImgModal(track);
                                    }}
                                    style={{ cursor: "pointer" }}
                                    className="banner-image-wrap"
                                  >
                                    {track?.banner_image ? (
                                      <img
                                        className="auto-width featureImage"
                                        src={
                                          config.mediaUrl + track?.banner_image
                                        }
                                        alt="featureImage"
                                      />
                                    ) : (
                                      ""
                                    )}
                                  </Box>
                                )}
                              </TableCell>
                              <TableCell>
                                {track?.sportId === 1
                                  ? "Horse Racing"
                                  : track?.sportId === 2
                                  ? "Harness Racing"
                                  : track?.sportId === 3
                                  ? "Greyhound Racing"
                                  : ""}
                              </TableCell>
                              <TableCell>
                                {" "}
                                <Button
                                  variant="contained"
                                  style={{
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    borderRadius: "8px",
                                    textTransform: "capitalize",
                                    // padding: "13px 24px 12px",
                                  }}
                                  onClick={() => {
                                    this.inputTrackVariationModal(
                                      track?.id,
                                      track?.name,
                                      track?.variation,
                                      track?.TrackVariations
                                    );
                                  }}
                                >
                                  Add/Edit variation
                                </Button>
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="contained"
                                  style={{
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    borderRadius: "8px",
                                    textTransform: "capitalize",
                                    // padding: "13px 24px 12px",
                                  }}
                                  onClick={() => {
                                    this.inputTrackDateVariationModal(
                                      track?.id,
                                      track?.name,
                                      track
                                    );
                                  }}
                                >
                                  Add/Edit variation
                                </Button>
                              </TableCell>
                              <TableCell>
                                {track?.City && track?.City?.cityName}
                              </TableCell>
                              <TableCell>
                                {track?.State && track?.State?.state}
                              </TableCell>
                              <TableCell>
                                {track?.Country && track?.Country?.country}
                              </TableCell>
                              <TableCell style={{ padding: "10px 2px" }}>
                                <Button
                                  className="table-btn edit-btn"
                                  onClick={() => {
                                    this.inputModal(track?.id);
                                    this.setState({
                                      isEditMode: true,

                                      rowToPass: track,
                                    });
                                  }}
                                  style={{ cursor: "pointer" }}
                                >
                                  Edit
                                </Button>
                              </TableCell>
                              <TableCell style={{ padding: "10px 2px" }}>
                                <Button
                                  className="table-btn delete-btn"
                                  style={{ cursor: "pointer" }}
                                  onClick={() => {
                                    this.setToDelete(track?.id);
                                  }}
                                >
                                  Delete
                                </Button>
                              </TableCell>
                              <TableCell style={{ padding: "10px 2px" }}>
                                <Button
                                  className="table-btn"
                                  style={{
                                    cursor: "pointer",
                                    backgroundColor: "#4455C7",
                                    color: "#fff",
                                    borderRadius: "8px",
                                    textTransform: "uppercase",
                                  }}
                                  onClick={this.mergeModal(track)}
                                >
                                  Merge
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                        <TableRow>
                          <TableCell colSpan={100} className="pagination">
                            <div className="tablePagination">
                              {/* <button
                              className={
                                offset >= 20
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={offset >= 20 ? false : true}
                              // disabled={
                              //   meetingsDetails.length / rowPerPage > 1 ? false : true
                              // }
                              onClick={() =>
                                this.handlePaginationButtonClick("prev")
                              }
                            >
                              <ReactSVG src={arrowLeft} />
                            </button> */}
                              <Pagination
                                hideNextButton
                                hidePrevButton
                                disabled={sportCount > 0 ? false : true}
                                page={currentPage}
                                onChange={this.handlePaginationClick}
                                count={pageNumbers[pageNumbers?.length - 1]}
                                siblingCount={2}
                                boundaryCount={1}
                                size="small"
                              />
                              {/* <button
                              className={
                                rowPerPage + offset < sportCount
                                  ? "btn-navigation"
                                  : "btn-navigation-disabled"
                              }
                              disabled={
                                rowPerPage + offset < sportCount
                                  ? false
                                  : true
                              }
                              onClick={() =>
                                this.handlePaginationButtonClick("next")
                              }
                            >
                              <ReactSVG src={arrowRight} />
                            </button> */}
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}
            </Box>

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {isEditMode === true ? "Edit Track " : "Add Track"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <CreateTrack
                  inputModal={this.toggleInputModal}
                  id={this.state.idToSend}
                  isEditMode={isEditMode}
                  fetchAllTrack={this.afterChangeRefresh}
                  rowToPass={rowToPass}
                />
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isTrakVariationModalOpen}
              onClose={this.toggleTrackVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Track Variation Details({trackName})
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleTrackVariationModal}
                />
                <CreateTrackVariation
                  inputModal={this.toggleTrackVariationModal}
                  id={trackId}
                  variation={trackVariation}
                  trackVariationsData={tracksDetails?.filter(
                    (item) => trackId == item.id
                  )}
                  fetchAllTracks={this.afterChangeRefresh}
                />
              </div>
            </Modal>
            <Modal
              className="modal modal-input"
              open={isTrakDateVariationModalOpen}
              onClose={this.toggleTrackDateVariationModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  Track Date Variation Details({trackName})
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleTrackDateVariationModal}
                />
                <CreateTrackDateVariation
                  inputModal={this.toggleTrackDateVariationModal}
                  id={trackId}
                  trackDateVariationsData={tracksDetails?.filter(
                    (item) => trackId == item.id
                  )}
                  fetchAllTracks={this.afterChangeRefresh}
                />
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={isViewImgModalOpen}
              onClose={this.toggleViewImgModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">View Image</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleViewImgModal}
                />
                <Grid item xs={12} className="runnerInfo">
                  <Grid
                    item
                    xs={12}
                    className="runnerInfo-text"
                    style={{ display: "flex", flexDirection: "column" }}
                  >
                    <Box style={{ margin: "0 auto" }}>
                      {isViewImgData?.banner_image ? (
                        <img
                          className="auto-width"
                          src={config.mediaUrl + isViewImgData?.banner_image}
                          alt="featureImage"
                        />
                      ) : (
                        ""
                      )}
                    </Box>
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        onClick={this.toggleViewImgModal}
                        // className="mr-lr-30"
                        value="Back"
                        style={{ minWidth: "auto" }}
                      />
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={isMergeModalOpen}
              onClose={this.toggleMergeModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">Merge Track</h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleMergeModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12}>
                    <label className="modal-label"> Parent Tracks </label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select mb15 merge-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      onMenuScrollToBottom={(e) =>
                        this.handleOnScrollBottomModalParentTrack(e)
                      }
                      onInputChange={(e) =>
                        this.handleModalParentTrackInputChange(0, e)
                      }
                      value={
                        isModalTrackSearch
                          ? searchModalTrack?.find((item) => {
                              return item?.value == parentTrack;
                            })
                          : parentTrack
                      }
                      options={
                        isModalTrackSearch ? searchModalTrack : ModalTrackData
                      }
                      onChange={(e) =>
                        this.setState({
                          parentTrack: e,
                        })
                      }
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <label className="modal-label"> Child Tracks </label>
                    <Select
                      className="React teamsport-select teamsport-multiple-select merge-select"
                      classNamePrefix="select"
                      menuPosition="fixed"
                      isMulti
                      onMenuScrollToBottom={(e) =>
                        this.handleOnScrollBottomModalChildTrack(e)
                      }
                      onInputChange={(e) =>
                        this.handleModalChildTrackInputChange(0, e)
                      }
                      value={
                        isModalChildTrackSearch !== ""
                          ? searchModalChildTrack?.find((item) => {
                              return item?.value === childTrack;
                            })
                          : childTrack
                      }
                      options={
                        isModalChildTrackSearch !== ""
                          ? searchModalChildTrack
                          : ModalChildTrackData
                      }
                      onChange={(e) =>
                        this.setState({
                          childTrack: e,
                        })
                      }
                    />
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        className="mt-3 admin-btn-green"
                        onClick={this.handalMergeTrack}
                        color="primary"
                        value={!isLoading ? "Merge" : "Loading..."}
                        disabled={isLoading}
                      />
                    </div>
                    {createError ? (
                      <p
                        className="errorText"
                        style={{ margin: "0px 0 0 0", width: "300px" }}
                      >
                        {createError}
                      </p>
                    ) : (
                      ""
                    )}
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <ShowModal
              isModalOpen={isDeleteModalOpen}
              onClose={this.toggleDeleteModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.handleTrackDelete}
              onCancel={this.toggleDeleteModal}
            />
          </Grid>
        </Grid>
      </>
    );
  }
}
export default TracksDetails;
