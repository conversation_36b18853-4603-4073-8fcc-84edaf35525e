@import "../../../assets/scss/variables.scss";

.sport-tab {
  padding: 19px 18px 9px 18px;
  box-shadow: 0px 3px 9px 0px #0000000d;
  background: #ffffff;

  .racing-tab-detail {
    border-bottom: 2px solid #4455c7;

    .MuiButtonBase-root.active {
      .MuiTab-wrapper {
        color: #003764;
      }
    }

    .MuiButtonBase-root {
      font-size: 16px;
      font-family: "VeneerClean-Soft" !important;
      color: #000000;
      line-height: 23px;
    }

    .MuiTab-root {
      min-width: 120px;
      opacity: 1;
    }

    .MuiTabs-indicator {
      height: 3px;
      background-color: #003764;
    }
  }

  .Filteritemlist-wrap {
    display: flex;
    align-items: center;
    margin-top: 9px;

    .Filteritemlist-racing {
      display: flex;
      list-style-type: none;
      padding: 0px;
      margin: 0px;

      li {
        margin-right: 14px;

        label {
          display: flex;
          column-gap: 5.2px;
          font-size: 12px;
          line-height: 15px;
        }

        .MuiButtonBase-root {
          padding: 0px;
        }
      }
    }

    .Filteritemlist-datepicker {
      display: contents;

      .MuiFormControl-marginNormal {
        margin: 0px;
      }

      .MuiOutlinedInput-input {
        padding: 10.5px 14px;
      }
    }
  }

  .select-wrap {
    max-width: 180px;
    width: 100%;

    .select__control {
      box-shadow: none;
      border: none !important;
      padding: 0px;
      margin: 0px;
    }

    .select__control,
    .react-select__control {
      &.select__control--is-disabled {
        // border-color: $border-color;
        .select__indicator-separator {
          //   background-color: $border-color;
        }
      }

      &.select__control--is-focused,
      &.react-select__control--is-focused {
        box-shadow: none;
        border: none;
      }

      &.select__control--menu-is-open {
        box-shadow: none;
        border: none;
      }

      .select__indicator svg {
        cursor: pointer;
      }

      .select__indicator-separator {
        display: none;
      }

      .select__single-value {
        border: none;
        font-size: 22.4px;
        line-height: 31.36px;
        color: #003764;
        font-family: #003764;
        outline: none;
        cursor: pointer;
        padding: 0px;
        margin: 0px;
      }

      .select__value-container {
        border: none;
        padding: 0px;
        margin: 0px;
        text-align: left;
      }

      .select__placeholder {
        border: none;
        font-size: 22.4px;
        line-height: 31.36px;
        color: #4455c7;
        font-family: #003764;
        outline: none;
        cursor: pointer;
      }
    }

    .select__menu {
      margin: 0px;
      border-radius: 0px;
      padding: 0px;
      z-index: 999;
    }

    .select__menu-list {
      padding: 0px;
    }

    // Select Menu
    .select__menu,
    .react-select__menu {
      .select__menu-list,
      .react-select__menu-list {
        .select__option,
        .react-select__option {
          cursor: pointer;
          font-size: 16px;
          color: #000000;
          font-family: "VeneerClean-Soft";
          line-height: 19px;
          padding: 11px;

          &.select__option--is-focused {
            background-color: #d4d6d8;
            color: #000;
          }

          &.select__option--is-selected {
            background-color: #d4d6d8;
            color: #000000;
          }
        }
      }

      .select__menu-list,
      .react-select__menu-list {
        .select__group {
          .select__group-heading {
            margin-bottom: 0.5rem;
            color: green;
            font-weight: bolder;
            font-size: inherit;
          }
        }
      }
    }

    // Multi Select
    .select__multi-value,
    .react-select__multi-value {
      color: #000000;

      margin: 0 0.7rem 0 0;

      .select__multi-value__label {
        color: #fff;
        font-size: 0.85rem;

        padding: 0.26rem 0.6rem;
      }

      .select__multi-value__remove {
        padding-left: 0;
        padding-right: 0.5rem;

        &:hover {
          background-color: inherit;
          color: inherit;
        }

        svg {
          height: 0.85rem;
          width: 0.85rem;

          &:hover {
            cursor: pointer;
          }
        }
      }
    }

    // Select Borderless
    .select-borderless {
      .select__control {
        border: 0;

        .select__indicators {
          display: none;
        }
      }
    }
  }

  .racing-tab {
    display: flex;
    align-items: center;

    @media (max-width: 1460px) {
      flex-wrap: wrap;
      justify-content: space-between;
    }

    .racing-location ul {
      margin: 0px;
      padding: 0px;
      display: flex;
      list-style-type: none;
      column-gap: 24px;
      span {
        max-width: 201px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      img {
        margin-right: 9px;
        object-fit: contain;
      }
    }

    li {
      position: relative;
      display: flex;
    }

    li:not(:last-child):after {
      content: "/";
      font-size: 16px;
      color: #000000;
      font-family: "VeneerClean-Soft";
      position: absolute;
      right: -14px;
    }

    .racing-track {
      display: flex;
      align-items: center;
      column-gap: 9px;
      margin-left: 62px;

      @media (max-width: 1460px) {
        margin-left: 20px;
      }

      .MuiRating-label {
        color: #4455c7;
      }

      .MuiRating-root {
        svg {
          fill: #4455c7;
        }
      }
    }

    .racing-Weather {
      display: flex;
      align-items: center;
      column-gap: 9px;
      margin-left: 63px;

      @media (max-width: 1460px) {
        margin-left: 20px;
      }

      .weather {
        display: flex;
        align-items: center;
        column-gap: 5px;
      }
    }

    .racing-Modal {
      margin-left: auto;
    }
  }

  .race-track-list {
    padding: 12px 0px 14px 0px;
    border-bottom: 1px solid #4455c7;
    min-height: auto;

    .MuiTabs-flexContainer {
      column-gap: 18px;
    }

    .race-track {
      min-height: auto;
    }

    .MuiTab-root {
      font-weight: 600;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border: 1px solid;
      color: #003764;
      background: transparent;
      border-color: #003764;
      font-size: 16px;
      min-width: auto;
      padding: 0px;
      min-height: auto;
      opacity: 1;
    }

    .Mui-selected {
      color: #fff;
      background-color: #4455c7;
      border-color: #4455c7;
    }

    .MuiTabs-indicator {
      display: none;
    }
  }

  .racing-detail-head {
    display: flex;
    column-gap: 45px;
    padding: 18px 0px;
    align-items: center;

    h6 {
      margin: 0px;
      font-size: 13px;
      line-height: 16px;
      color: #191919;
    }

    span {
      font-size: 13px;
      padding: 0px;
      font-weight: normal;
    }
  }

  .race-comment {
    padding: 19px 0px 10px 0px;
    text-align: left;

    h6 {
      margin: 0px 0px 9px 0px;
      font-size: 16px;
      line-height: 19px;
    }

    p {
      color: #8d8d8d;
      line-height: 16px;
    }
  }
}

.select__menu {
  z-index: 99 !important;
}

.editRunner-Wrap {
  display: grid;
  grid-template-columns: auto auto;
  column-gap: 20px;
  row-gap: 30px;
}
.view-identifier-modal {
  width: 1024px !important;
}
.bookkeeper-identifier-table {
  .MuiTableCell-root {
    padding: 8px 10px !important;
  }
}
.info-btn.Mui-disabled {
  opacity: 0.7;
}
