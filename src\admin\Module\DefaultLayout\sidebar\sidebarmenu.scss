.sidebar {
  ul {
    list-style-type: none;
    text-align: left;
    margin: 0;
    padding: 0;
    font-size: 16px;

    p,
    a {
      display: block;
      padding: 10px 15px;
      margin: 0;
      color: #fff;

      @media (max-width:1260px) {
        padding: 8px;
      }
    }

    .active {
      // background-color: #00833e;
      color: rgb(255, 255, 255);
      border-left: 4px solid #FC4714;
    }
  }

  .item-title {
    text-align: left;
  }

  .item {
    border-left: 4px solid transparent;
  }

  .menu-main {
    position: relative;
    display: inline-block;
    padding: 0;
    cursor: pointer;
    width: 100%;

    .MuiSvgIcon-root {
      vertical-align: middle;
      height: 20px;
      float: right;
    }

    span {
      display: inline-flex;

      svg {
        margin-right: 5px;
        width: 14px;
        height: 14px;
        vertical-align: middle;
      }

      svg * {
        fill: #ffffffb3;
      }
    }
  }

  .menu-sub {
    padding: 0;
    // background: #393940;
    width: 100%;

    a {
      color: #fff;
    }
  }

  // .menu-main:hover .menu-sub {
  //   visibility: visible;
  //   opacity: 1;
  //   position: unset;
  //   // transition-delay: 0s;
  //   transform: translateY(0%);
  //   transition-delay: 0s;
  // }
}

.menu-sub {
  .sports_menu_name {
    display: flex;

    svg * {
      fill: #ffffffb3;
    }

    svg {
      margin-right: 5px;
      width: 14px;
      height: 14px;
      vertical-align: middle;
    }
  }
}

// .bg-gray {
//   background-color: #393940 !important;

//   a {
//     color: #fff !important;
//   }
// }

// .bg-light-green {
//   background-color: #6abf4b !important;

//   a {
//     color: #fff !important;
//   }
// }

// .bg-orange {
//   background-color: #ff6500 !important;

//   a {
//     color: #fff !important;
//   }
// }

// .bg-yellow {
//   background-color: #ffa400 !important;

//   a {
//     color: #fff !important;
//   }
// }

.p50 {
  padding-left: 50px !important;
}

.p70 {
  padding-left: 70px !important;
}

.p90 {
  padding-left: 90px !important;
}

.p20 {
  padding-left: 20px !important;
}

.right-arrow {
  cursor: pointer;

  svg {
    float: right;
    vertical-align: middle;
  }
}