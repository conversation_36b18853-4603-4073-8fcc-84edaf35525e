import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import Select from "react-select";
import SearchIcons from "../../../images/searchIcon.svg";
import axiosInstance from "../../../helpers/Axios";
// import { URLS } from "../../library/common/constants";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { Loader } from "../../../library/common/components";
import ShowModal from "../../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../../library/common/components/Button";
import Pagination from '@mui/material/Pagination';
// import arrowLeft from "../../images/blog_img/paginationArrowLeft.svg";
// import arrowRight from "../../images/blog_img/paginationArrowRight.svg";
// import { ReactSVG } from "react-svg";
// import { showVariations } from "../../helpers/common";

import "../teamsport.scss";
import moment from "moment";
import TableCellWithBTags from "./ErrorHighlight";

const StatusOptions = [
  { label: "open", value: "open" },
  { label: "completed", value: "completed" },
  { label: "inprogress", value: "inprogress" },
];

const typeOptions = [
  {
    label: "All",
    value: 1,
  },
  {
    label: "Label",
    value: 2,
  },

  {
    label: "Market",
    value: 3,
  },
  {
    label: "Player",
    value: 4,
  },
  {
    label: "Team",
    value: 5,
  },
];

class MarketError extends React.Component {
  constructor(props) {
    super(props);
    // this.contentRef = React.createRef();
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      marketErrorValues: {
        marketErrorName: "",
        rapidId: "",
        id: "",
      },
      marketErrorlist: [],
      marketErrorCount: 0,
      errorRequire: "",
      pathName: "",
      teamsports: {
        name: "",
        api: "",
        key: "",
      },
      errorStatus: "",
      errorId: "",
      providersDetails: [],
      selectedProvider: 0,
      serachValue: "",
      checkBoxValues: [],
      isSelectedLogsOpen: false,
      isDeletedAllOpen: false,
      errorFilter: "",
    };
  }

  componentDidMount() {
    this.fetchAllMarketError(0, "", 0, "");
    this.fetchProviders();
    // this.attachEventListeners();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.offset !== this.state?.offset) {
      this.fetchAllMarketError(
        this.state?.offset,
        this.state?.serachValue,
        this.state?.selectedProvider,
        this.state?.errorFilter
      );
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchAllMarketError(0, "", 0, "");
      this.fetchProviders();
      this.setState({
        offset: 0,
        currentPage: 1,
        checkBoxValues: [],
        errorFilter: "",
      });
    }
    // this.attachEventListeners();
  }
  componentWillUnmount() {
    // this.removeEventListeners();
  }

  // attachEventListeners() {
  //   const bTags = this.contentRef.querySelectorAll("b.danger");
  //   for (let i = 0; i < bTags.length; i++) {
  //     bTags[i].addEventListener("click", this.handleBTagClick);
  //   }
  // }

  // removeEventListeners() {
  //   const bTags = this.contentRef.querySelectorAll("b.danger");
  //   for (let i = 0; i < bTags.length; i++) {
  //     bTags[i].removeEventListener("click", this.handleBTagClick);
  //   }
  // }
  // handleBTagClick = (event) => {
  //   const bTagContent = event.target.innerText;
  //   this.copyToClipboard(bTagContent);
  // };

  // copyToClipboard = (text) => {
  //   const tempInput = document.createElement("input");
  //   tempInput.value = text;
  //   document.body.appendChild(tempInput);
  //   tempInput.select();
  //   document.execCommand("copy");
  //   document.body.removeChild(tempInput);
  //   alert("Copied to clipboard: " + text);
  // };
  async fetchAllMarketError(page, search, apiProvider, filterType) {
    let { rowPerPage, offset, teamsports, selectedProvider } = this.state;
    this.setState({ isLoading: true });

    try {
      const passApi = this.props.match.path?.includes("cricket")
        ? `crickets/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
        }&type=${filterType === "All" ? "" : filterType}`
        : this.props.match.path?.includes("rugbyleague")
          ? `rls/error?limit=${rowPerPage}&offset=${page}&SportId=12&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
          }&type=${filterType === "All" ? "" : filterType}`
          : this.props.match.path?.includes("rugbyunion")
            ? `rls/error?limit=${rowPerPage}&offset=${page}&SportId=13&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
            }&type=${filterType === "All" ? "" : filterType}`
            : this.props.match.path?.includes("basketball")
              ? `nba/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
              }&type=${filterType === "All" ? "" : filterType}`
              : this.props.match.path?.includes("afl")
                ? `afl/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                }&type=${filterType === "All" ? "" : filterType}`
                : this.props.match.path?.includes("australianrules")
                  ? `ar/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                  }&type=${filterType === "All" ? "" : filterType}`
                  : this.props.match.path?.includes("golf")
                    ? `golf/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                    }&type=${filterType === "All" ? "" : filterType}`
                    : this.props.match.path?.includes("tennis")
                      ? `tennis/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                      }&type=${filterType === "All" ? "" : filterType}`
                      : this.props.match.path?.includes("baseball")
                        ? `baseball/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                        }&type=${filterType === "All" ? "" : filterType}`
                        : this.props.match.path?.includes("icehockey")
                          ? `icehockey/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                          }&type=${filterType === "All" ? "" : filterType}`
                          : this.props.match.path?.includes("boxing")
                            ? `boxing/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                            }&type=${filterType === "All" ? "" : filterType}`
                            : this.props.match.path?.includes("mma")
                              ? `mma/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                              }&type=${filterType === "All" ? "" : filterType}`
                              : this.props.match.path?.includes("soccer")
                                ? `soccer/error?limit=${rowPerPage}&offset=${page}&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                                }&type=${filterType === "All" ? "" : filterType}`
                                : `rls/error?limit=${rowPerPage}&offset=${page}&SportId=14&search=${search}&ApiProviderId=${apiProvider === 0 ? "" : apiProvider
                                }&type=${filterType === "All" ? "" : filterType}`;
      const { status, data } = await axiosInstance.get(passApi);
      if (status === 200) {
        this.setState({
          marketErrorlist: data?.result?.rows,
          isLoading: false,
          marketErrorCount: data?.result?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch (err) {
      this.setState({
        isLoading: false,
      });
    }
  }
  handalValidate = () => {
    let { errorStatus } = this.state;

    let flag = true;
    if (!errorStatus) {
      flag = false;
      this.setState({
        errorRequire: "This field is mandatory",
      });
    } else {
      this.setState({
        errorRequire: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    // if (this.handalValidate()) {
    //     this.setState({ isLoading: true, isEditMode: false });
    //     const payload = {
    //         name: this.state?.marketErrorValues?.marketErrorName,
    //         rapidId: this.state?.marketErrorValues?.rapidId,
    //         SportId: this.props.match.path?.includes("cricket")
    //             ? 4
    //             : this.props.match.path?.includes("rugbyleague")
    //                 ? 12
    //                 : this.props.match.path?.includes("rugbyunion")
    //                     ? 13
    //                     : 14,
    //     };
    //     const { status } = await axiosInstance.post(`rls/category`, payload);
    //     if (status === 200) {
    //         this.setState({ isLoading: false, isInputModalOpen: false });
    //         this.fetchAllMarketError(this.state?.offset);
    //         //   this.setActionMessage(
    //         //     true,
    //         //     "Success",
    //         //     `Country variation Created Successfully`
    //         //   );
    //     }
    // }
  };

  handleUpdate = async () => {
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      const payload = {
        errorStatus: this.state?.errorStatus,
        id: this.state.errorId,
        SportId: this.props.match.path?.includes("cricket")
          ? 4
          : this.props.match.path?.includes("rugbyleague")
            ? 12
            : this.props.match.path?.includes("rugbyunion")
              ? 13
              : this.props.match.path?.includes("basketball")
                ? 10
                : this.props.match.path?.includes("afl")
                  ? 15
                  : this.props.match.path?.includes("australianrules")
                    ? 9
                    : this.props.match.path?.includes("golf")
                      ? 16
                      : this.props.match.path?.includes("tennis")
                        ? 7
                        : this.props.match.path?.includes("baseball")
                          ? 11
                          : this.props.match.path?.includes("icehockey")
                            ? 17
                            : this.props.match.path?.includes("boxing")
                              ? 6
                              : this.props.match.path?.includes("mma")
                                ? 5
                                : this.props.match.path?.includes("soccer")
                                  ? 8
                                  : 14,
      };
      let passApi = this.props.match.path?.includes("cricket")
        ? "crickets"
        : this.props.match.path?.includes("basketball")
          ? "nba"
          : this.props.match.path?.includes("afl")
            ? "afl"
            : this.props.match.path?.includes("australianrules")
              ? "ar"
              : this.props.match.path?.includes("golf")
                ? "golf"
                : this.props.match.path?.includes("tennis")
                  ? "tennis"
                  : this.props.match.path?.includes("baseball")
                    ? "baseball"
                    : this.props.match.path?.includes("icehockey")
                      ? "icehockey"
                      : this.props.match.path?.includes("boxing")
                        ? "boxing"
                        : this.props.match.path?.includes("mma")
                          ? "mma"
                          : this.props.match.path?.includes("soccer")
                            ? "soccer"
                            : "rls";
      try {
        const { status } = await axiosInstance.put(`${passApi}/error`, payload);
        if (status === 200) {
          this.setState({ isLoading: false, isInputModalOpen: false });
          this.fetchAllMarketError(
            this.state?.offset,
            this.state?.serachValue,
            this.state?.selectedProvider,
            this.state?.errorFilter
          );
          this.setActionMessage(
            true,
            "Success",
            `Market Error Edited Successfully`
          );
        } else {
          this.setState({ isLoading: false, isInputModalOpen: false });
        }
      } catch (err) {
        this.setState({ isLoading: false, isInputModalOpen: false });
      }
    }
  };

  //   async fetchAllSportType() {
  //     const { status, data } = await axiosInstance.get(URLS.sportType);
  //     if (status === 200) {
  //       this.setState({ allSportsType: data.result });
  //     }
  //   }

  //   getSportType = (id) => {
  //     let { allSportsType } = this.state;
  //     let sportType = allSportsType
  //       .filter((obj) => obj.id === id)
  //       .map((object) => object.sportType);
  //     return sportType;
  //   };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };
  toggleSelectedLogModal = () => {
    this.setState({
      isSelectedLogsOpen: !this.state.isSelectedLogsOpen,
    });
  };
  toggleDeletedAllModal = () => {
    this.setState({
      isDeletedAllOpen: !this.state.isDeletedAllOpen,
    });
  };
  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorRequire: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        errorStatus: item?.status,
        errorId: item?.id,
        isEditMode: true,
      });
    } else {
      this.setState({
        errorStatus: "",
        errorId: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };
  setSelectedDelete = () => {
    this.setState({ isSelectedLogsOpen: true });
  };
  setDeleteAll = () => {
    this.setState({ isDeletedAllOpen: true });
  };
  afterChangeRefresh = () => {
    // this.fetchAllSports();
  };

  deleteItem = async () => {
    try {
      let passApi = this.props.match.path?.includes("cricket")
        ? `crickets/error/${this.state.itemToDelete}`
        : this.props.match.path?.includes("rugbyleague")
          ? `rls/error/${this.state.itemToDelete}?SportId=12`
          : this.props.match.path?.includes("rugbyunion")
            ? `rls/error/${this.state.itemToDelete}?SportId=13`
            : this.props.match.path?.includes("basketball")
              ? `nba/error/${this.state.itemToDelete}`
              : this.props.match.path?.includes("afl")
                ? `afl/error/${this.state.itemToDelete}`
                : this.props.match.path?.includes("australianrules")
                  ? `ar/error/${this.state.itemToDelete}`
                  : this.props.match.path?.includes("golf")
                    ? `golf/error/${this.state.itemToDelete}`
                    : this.props.match.path?.includes("tennis")
                      ? `tennis/error/${this.state.itemToDelete}`
                      : this.props.match.path?.includes("baseball")
                        ? `baseball/error/${this.state.itemToDelete}`
                        : this.props.match.path?.includes("icehockey")
                          ? `icehockey/error/${this.state.itemToDelete}`
                          : this.props.match.path?.includes("boxing")
                            ? `boxing/error/${this.state.itemToDelete}`
                            : this.props.match.path?.includes("mma")
                              ? `mma/error/${this.state.itemToDelete}`
                              : this.props.match.path?.includes("soccer")
                                ? `soccer/error/${this.state.itemToDelete}`
                                : `rls/error/${this.state.itemToDelete}?SportId=14`;
      const { status } = await axiosInstance.delete(`${passApi}`);
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchAllMarketError(
            this.state?.offset,
            this.state?.serachValue,
            this.state?.selectedProvider,
            this.state?.errorFilter
          );
        });
        this.setActionMessage(true, "Success", "Deleted Successfully!");
      } else {
        this.setState({ itemToDelete: null, isModalOpen: false });
      }
    } catch (err) {
      this.setState({ itemToDelete: null, isModalOpen: false });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handleCopyClick = (content) => {
    // Create a temporary input element to hold the content to be copied
    const tempInput = document.createElement("input");
    tempInput.value = content;
    document.body.appendChild(tempInput);

    // Select the text inside the temporary input element
    tempInput.select();
    tempInput.setSelectionRange(0, 99999); // For mobile devices

    // Execute the copy command
    document.execCommand("copy");

    // Remove the temporary input element from the DOM
    document.body.removeChild(tempInput);

    // Show a success message to the user
    // alert('Content copied to clipboard!');
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, marketErrorlist, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= 20) {
        this.setState({ offset: offset - 20, currentPage: currentPage - 1 });
      }
    } else {
      this.setState({ offset: offset + 20, currentPage: currentPage + 1 });
    }
  };
  fetchProviders = async () => {
    try {
      let SportId = this.props.match.path?.includes("cricket")
        ? 4
        : this.props.match.path?.includes("rugbyleague")
          ? 12
          : this.props.match.path?.includes("rugbyunion")
            ? 13
            : this.props.match.path?.includes("basketball")
              ? 10
              : this.props.match.path?.includes("afl")
                ? 15
                : this.props.match.path?.includes("australianrules")
                  ? 9
                  : this.props.match.path?.includes("golf")
                    ? 16
                    : this.props.match.path?.includes("tennis")
                      ? 7
                      : this.props.match.path?.includes("baseball")
                        ? 11
                        : this.props.match.path?.includes("icehockey")
                          ? 17
                          : this.props.match.path?.includes("boxing")
                            ? 6
                            : this.props.match.path?.includes("mma")
                              ? 5
                              : this.props.match.path?.includes("soccer")
                                ? 8
                                : 14;
      const { status, data } = await axiosInstance.get(
        `apiProviders/apiprovidersports?SportId=${SportId}`
      );
      if (status === 200) {
        let newdata = [];
        let providerData = data?.result?.map((item) => {
          newdata.push({
            label: item?.ApiProvider?.providerName,
            value: item?.ApiProviderId,
          });
        });
        const allproviderData = { label: "All", value: 0 };
        let allproviderDataList = [...newdata, allproviderData];
        this.setState({
          isLoading: false,
          providersDetails: allproviderDataList?.sort((a, b) => {
            return a.value > b.value ? 1 : -1;
          }),
        });
      }
    } catch (err) { }
  };
  bookmakerName = (bookmakerId) => {
    let { providersDetails } = this.state;
    let provider = providersDetails?.filter((item) => {
      return item?.value === bookmakerId;
    });
    return provider?.[0]?.label;
  };
  handleMarketError = (error) => {
    // <TableCellWithBTags apiContent={item?.error} />

    let Apicontent = (
      <span
        className="danger"
        onClick={() => this.handleCopyClick(error)}
      >
        {error}
      </span>
    );

    // let Apicontent = error?.map((item, index) => {
    //   return (
    //     <>
    //       <div key={index}>
    //         <span>{item.type} : </span>
    //         <span
    //           className="danger"
    //           onClick={() => this.handleCopyClick(item.error)}
    //         >
    //           {item.error}
    //         </span>
    //       </div>
    //       <br />
    //     </>
    //   );
    // });

    return Apicontent;
  };

  handleCheckBoxChange = (e) => {
    const { value, checked } = e.target;
    const { checkBoxValues } = this.state;
    if (checked) {
      let checkboxdata = [...checkBoxValues, Number(value)];
      this.setState({
        checkBoxValues: checkboxdata,
      });
    } else {
      let checkboxdata = checkBoxValues?.filter(
        (element) => element !== Number(value)
      );
      this.setState({
        checkBoxValues: checkboxdata,
      });
    }
  };
  handleDeleteSelected = async () => {
    try {
      let passApi = this.props.match.path?.includes("cricket")
        ? `crickets/error/all`
        : this.props.match.path?.includes("rugbyleague")
          ? `rls/error/all?SportId=12`
          : this.props.match.path?.includes("rugbyunion")
            ? `rls/error/all?SportId=13`
            : this.props.match.path?.includes("basketball")
              ? `nba/error/all`
              : this.props.match.path?.includes("afl")
                ? `afl/error/all`
                : this.props.match.path?.includes("australianrules")
                  ? `ar/error/all`
                  : this.props.match.path?.includes("golf")
                    ? `golf/error/all`
                    : this.props.match.path?.includes("tennis")
                      ? `tennis/error/all`
                      : this.props.match.path?.includes("baseball")
                        ? `baseball/error/all`
                        : this.props.match.path?.includes("icehockey")
                          ? `icehockey/error/all`
                          : this.props.match.path?.includes("boxing")
                            ? `boxing/error/all`
                            : this.props.match.path?.includes("mma")
                              ? `mma/error/all`
                              : this.props.match.path?.includes("soccer")
                                ? `soccer/error/all`
                                : `rls/error/all?SportId=14`;
      const payload = {
        ids: this.state.checkBoxValues,
      };
      const { status } = await axiosInstance.post(`${passApi}`, payload);
      if (status === 200) {
        this.setState({ checkBoxValues: [], isSelectedLogsOpen: false });
        this.fetchAllMarketError(
          this.state?.offset,
          this.state?.serachValue,
          this.state?.selectedProvider,
          this.state?.errorFilter
        );
        this.setActionMessage(true, "Success", "Deleted Successfully!");
      } else {
        this.setState({ checkBoxValues: [], isSelectedLogsOpen: false });
      }
    } catch (err) {
      this.setState({ checkBoxValues: [], isSelectedLogsOpen: false });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };
  handleDeleteAll = async () => {
    try {
      let passApi = this.props.match.path?.includes("cricket")
        ? `crickets/error/all?isAll=1`
        : this.props.match.path?.includes("rugbyleague")
          ? `rls/error/all?isAll=1&SportId=12`
          : this.props.match.path?.includes("rugbyunion")
            ? `rls/error/all?isAll=1&SportId=13`
            : this.props.match.path?.includes("basketball")
              ? `nba/error/all?isAll=1`
              : this.props.match.path?.includes("afl")
                ? `afl/error/all?isAll=1`
                : this.props.match.path?.includes("australianrules")
                  ? `ar/error/all?isAll=1`
                  : this.props.match.path?.includes("golf")
                    ? `golf/error/all?isAll=1`
                    : this.props.match.path?.includes("tennis")
                      ? `tennis/error/all?isAll=1`
                      : this.props.match.path?.includes("baseball")
                        ? `baseball/error/all?isAll=1`
                        : this.props.match.path?.includes("icehockey")
                          ? `icehockey/error/all?isAll=1`
                          : this.props.match.path?.includes("boxing")
                            ? `boxing/error/all?isAll=1`
                            : this.props.match.path?.includes("mma")
                              ? `mma/error/all?isAll=1`
                              : this.props.match.path?.includes("soccer")
                                ? `soccer/error/all?isAll=1`
                                : `rls/error/all?isAll=1&SportId=14`;
      const { status } = await axiosInstance.post(`${passApi}`);
      if (status === 200) {
        this.fetchAllMarketError(
          this.state?.offset,
          this.state?.serachValue,
          this.state?.selectedProvider,
          this.state?.errorFilter
        );
        this.setState({ checkBoxValues: [], isDeletedAllOpen: false });
        this.setActionMessage(true, "Success", "Deleted Successfully!");
      } else {
        this.setState({ checkBoxValues: [], isDeletedAllOpen: false });
      }
    } catch (err) {
      this.setState({ checkBoxValues: [], isDeletedAllOpen: false });
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
    }
  };

  handleErrorTypeChange = (e) => {
    this.setState({
      errorFilter: e?.label,
      currentPage: 1,
    });

    this.fetchAllMarketError(0, "", this.state?.selectedProvider, e?.label);
  };
  handleClearClick = () => {
    this.setState({ serachValue: "" });
  };

  handleKeyDown = (event) => {
    var { serachValue, selectedProvider, errorFilter } = this.state;
    if (event.key === "Enter") {
      this.fetchAllMarketError(0, serachValue, selectedProvider, errorFilter);
      this.setState({ currentPage: 1 });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      SportVariationModal,
      marketErrorValues,
      marketErrorlist,
      marketErrorCount,
      errorRequire,
      pathName,
      teamsports,
      errorStatus,
      providersDetails,
      selectedProvider,
      serachValue,
      checkBoxValues,
      isSelectedLogsOpen,
      isDeletedAllOpen,
      errorFilter,
    } = this.state;
    const pageNumbers = [];
    // sportType !== "" &&
    //   (marketErrorlist = marketErrorlist.filter((obj) => obj.sportTypeId == sportType));

    // let currentPageRow = marketErrorlist;

    // if (marketErrorlist?.length > 0) {
    //   const indexOfLastTodo = currentPage * rowPerPage;
    //   const indexOfFirstTodo = indexOfLastTodo - rowPerPage;
    //   currentPageRow = marketErrorlist.slice(indexOfFirstTodo, indexOfLastTodo);
    if (marketErrorCount > 0) {
      for (let i = 1; i <= Math.ceil(marketErrorCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (<>
      <Grid
        container
        className="page-content adminLogin"
      >
        <Grid
          item
          xs={12}
          className="pageWrapper"
        >
          {/* <Paper className="pageWrapper"> */}
          {messageBox.display && (
            <ActionMessage
              message={messageBox.message}
              type={messageBox.type}
              styleClass={messageBox.styleClass}
            />
          )}
          <Box className="bredcrumn-wrap">
            <Breadcrumbs
              separator="/"
              aria-label="breadcrumb"
              className="breadcrumb"
            >
              <Link
                underline="hover"
                color="inherit"
                to="/dashboard"
              >
                Home
              </Link>
              <Link
                underline="hover"
                color="inherit"
              >
                {this.props.match.path?.includes("cricket")
                  ? "Cricket"
                  : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                      ? "Rugby Union"
                      : this.props.match.path?.includes("basketball")
                        ? "Basketball"
                        : this.props.match.path?.includes("afl")
                          ? "American Football"
                          : this.props.match.path?.includes("australianrules")
                            ? "Australian Rules"
                            : this.props.match.path?.includes("golf")
                              ? "Golf"
                              : this.props.match.path?.includes("tennis")
                                ? "Tennis"
                                : this.props.match.path?.includes("baseball")
                                  ? "Baseball"
                                  : this.props.match.path?.includes("icehockey")
                                    ? "Ice Hockey"
                                    : this.props.match.path?.includes("boxing")
                                      ? "Boxing"
                                      : this.props.match.path?.includes("mma")
                                        ? "Mixed Martial Arts"
                                        : this.props.match.path?.includes("soccer")
                                          ? "Soccer"
                                          : "Rugby Union Sevens"}
              </Link>
              <Typography className="active_p">Market Error</Typography>
            </Breadcrumbs>
          </Box>
          <Grid
            container
            direction="row"
            alignItems="center"
          >
            <Grid
              item
              xs={5}
            >
              <Typography
                variant="h1"
                align="left"
              >
                {this.props.match.path?.includes("cricket")
                  ? "Cricket"
                  : this.props.match.path?.includes("rugbyleague")
                    ? "Rugby League"
                    : this.props.match.path?.includes("rugbyunion")
                      ? "Rugby Union"
                      : this.props.match.path?.includes("basketball")
                        ? "Basketball"
                        : this.props.match.path?.includes("afl")
                          ? "American Football"
                          : this.props.match.path?.includes("australianrules")
                            ? "Australian Rules"
                            : this.props.match.path?.includes("golf")
                              ? "Golf"
                              : this.props.match.path?.includes("tennis")
                                ? "Tennis"
                                : this.props.match.path?.includes("baseball")
                                  ? "Baseball"
                                  : this.props.match.path?.includes("icehockey")
                                    ? "Ice Hockey"
                                    : this.props.match.path?.includes("boxing")
                                      ? "Boxing"
                                      : this.props.match.path?.includes("mma")
                                        ? "Mixed Martial Arts"
                                        : this.props.match.path?.includes("soccer")
                                          ? "Soccer"
                                          : "Rugby Union Sevens"}{" "}
                Market Error
              </Typography>
            </Grid>

            <Grid
              item
              xs={7}
              className="admin-filter-wrap"
            >
              <Select
                className="React cricket-select external-select"
                classNamePrefix="select"
                placeholder="Select Type"
                value={
                  errorFilter &&
                  typeOptions?.find((item) => {
                    return item?.label == errorFilter;
                  })
                }
                // isLoading={isLoading}
                onChange={(e) => this.handleErrorTypeChange(e)}
                options={typeOptions}
              />
              <Select
                className="React cricket-select error-select"
                classNamePrefix="select"
                placeholder="provider id"
                menuPosition="fixed"
                value={providersDetails?.find((item) => {
                  return item?.value == selectedProvider;
                })}
                isLoading={isLoading}
                onChange={(e) => {
                  this.setState({
                    selectedProvider: e.value,
                    currentPage: 1,
                  });
                  this.fetchAllMarketError(
                    0,
                    this.state?.serachValue,
                    e.value,
                    errorFilter
                  );
                }}
                options={providersDetails}
              />
              <TextField
                placeholder="Search "
                size="small"
                variant="outlined"
                className="event-search"
                onKeyDown={(e) => this.handleKeyDown(e)}
                value={serachValue}
                onChange={(e) => {
                  this.setState({ serachValue: e.target.value });
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <img
                        src={SearchIcons}
                        alt="icon"
                      />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      {serachValue && (
                        <IconButton
                          onClick={() => this.handleClearClick()}
                          edge="end"
                          style={{ minWidth: "unset" }}
                          size="large">
                          <CancelIcon />
                        </IconButton>
                      )}
                    </InputAdornment>
                  ),
                }}
                style={{
                  background: "#ffffff",
                }}
              />
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455c7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                }}
                // onClick={() => fetchAnimal()}
                onClick={() => {
                  this.fetchAllMarketError(
                    0,
                    serachValue,
                    selectedProvider,
                    errorFilter
                  );
                  this.setState({ currentPage: 1 });
                }}
              >
                Search
              </Button>

              {/* <Button
                                  variant="contained"
                                  style={{
                                      backgroundColor: "#4455C7",
                                      color: "#fff",
                                      borderRadius: "8px",
                                      textTransform: "capitalize",
                                      padding: "6px 10px",
                                      // marginTop: "5px",
                                  }}
                                  onClick={this.inputModal(null, "create")}
                              >
                                  Add New
                              </Button> */}
            </Grid>
            <Grid
              item
              xs={12}
              className="admin-filter-wrap admin-fixture-wrap"
              style={{
                marginBottom: "10px",
                marginTop: "12px",
                justifyContent: "space-between",
              }}
            >
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "6px 10px",
                }}
                onClick={() => this.setSelectedDelete()}
              >
                Delete Selected
              </Button>
              <Button
                variant="contained"
                style={{
                  backgroundColor: "#4455C7",
                  color: "#fff",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  padding: "6px 10px",
                }}
                onClick={() => this.setDeleteAll()}
              >
                Delete All
              </Button>
            </Grid>
          </Grid>

          {isLoading && <Loader />}
          {!isLoading && marketErrorlist?.length === 0 && (
            <p>No Data Available</p>
          )}

          {!isLoading && marketErrorlist?.length > 0 && (
            <TableContainer component={Paper}>
              <Table
                className="listTable market-error-table"
                aria-label="simple table"
              >
                <TableHead className="tableHead-row">
                  <TableRow>
                    <TableCell style={{ width: "20px" }}>Select</TableCell>
                    <TableCell>DID</TableCell>
                    <TableCell style={{ width: "10%" }}>Event Name</TableCell>
                    <TableCell style={{ width: "30%" }}>Error</TableCell>
                    <TableCell>Error Date</TableCell>
                    {/* <TableCell>Status</TableCell> */}
                    <TableCell>Error Status</TableCell>
                    <TableCell>Type</TableCell>
                    {/* <TableCell>User Name</TableCell>
                    <TableCell>URL</TableCell> */}
                    <TableCell>EventID</TableCell>
                    <TableCell>Provider</TableCell>
                    <TableCell style={{ width: "15%" }}>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody className="table_body">
                  <TableRow className="table_row">
                    <TableCell
                      colSpan={100}
                      className="table-seprator"
                    ></TableCell>
                  </TableRow>
                  {marketErrorlist?.map((item) => {
                    return (
                      <TableRow
                        className="table-rows listTable-Row"
                        key={item?.id}
                      >
                        <TableCell>
                          <Checkbox
                            className="mz-checkbox"
                            checked={this.state.checkBoxValues?.includes(
                              item?.id
                            )}
                            value={item?.id}
                            onChange={(e) => this.handleCheckBoxChange(e)}
                          />
                        </TableCell>
                        <TableCell> {item?.id} </TableCell>
                        <TableCell>
                          {" "}
                          {this.props.match.path?.includes("cricket")
                            ? item?.CricketEvent?.eventName
                            : this.props.match.path?.includes("basketball")
                              ? item?.NBAEvent?.eventName
                              : this.props.match.path?.includes("afl")
                                ? item?.AFLEvent?.eventName
                                : this.props.match.path?.includes(
                                  "australianrules"
                                )
                                  ? item?.AREvent?.eventName
                                  : this.props.match.path?.includes("golf")
                                    ? item?.GolfEvent?.eventName
                                    : this.props.match.path?.includes("tennis")
                                      ? item?.TennisEvent?.eventName
                                      : this.props.match.path?.includes("baseball")
                                        ? item?.BaseballEvent?.eventName
                                        : this.props.match.path?.includes("icehockey")
                                          ? item?.IceHockeyEvent?.eventName
                                          : this.props.match.path?.includes("boxing")
                                            ? item?.BoxingEvent?.eventName
                                            : this.props.match.path?.includes("mma")
                                              ? item?.MMAEvent?.eventName
                                              : this.props.match.path?.includes("soccer")
                                                ? item?.SoccerEvent?.eventName
                                                : item?.RLEvent?.eventName}
                        </TableCell>
                        <TableCell
                        // ref={this.contentRef}
                        // dangerouslySetInnerHTML={{
                        //   __html: item?.error.replace(
                        //     /\n/g,
                        //     "<br /><br />"
                        //   ),
                        // }}
                        >
                          {/* <TableCellWithBTags apiContent={item?.error} /> */}

                          {item?.error
                            ? this.handleMarketError(item?.error)
                            : item?.error}
                          {/* {this.handleMarketError(item?.error)} */}
                        </TableCell>
                        <TableCell>
                          {item?.updatedAt
                            ? moment(item?.updatedAt).format(
                              "DD/MM/YYYY h:mm:ss a"
                            )
                            : ""}
                        </TableCell>
                        {/* <TableCell>{item?.status}</TableCell> */}
                        <TableCell>{item?.errorStatus}</TableCell>
                        <TableCell>{item?.type}</TableCell>
                        {/* <TableCell>{item?.User?.firstName}</TableCell> */}
                        {/* <TableCell>
                          <a href={item?.url} target="_blank">
                            URL
                          </a>
                        </TableCell> */}
                        <TableCell>
                          {this.props.match.path?.includes("cricket")
                            ? item?.CricketEventId
                            : this.props.match.path?.includes("basketball")
                              ? item?.NBAEventId
                              : this.props.match.path?.includes("afl")
                                ? item?.AFLEventId
                                : this.props.match.path?.includes(
                                  "australianrules"
                                )
                                  ? item?.AREventId
                                  : this.props.match.path?.includes("golf")
                                    ? item?.GolfEventId
                                    : this.props.match.path?.includes("tennis")
                                      ? item?.TennisEventId
                                      : this.props.match.path?.includes("baseball")
                                        ? item?.BaseballEventId
                                        : this.props.match.path?.includes("icehockey")
                                          ? item?.IceHockeyEventId
                                          : this.props.match.path?.includes("boxing")
                                            ? item?.BoxingEventId
                                            : this.props.match.path?.includes("mma")
                                              ? item?.MMAEventId
                                              : this.props.match.path?.includes("soccer")
                                                ? item?.SoccerEventId
                                                : item?.RLEventId}
                        </TableCell>
                        <TableCell>
                          {this.bookmakerName(item?.ApiProviderId)}
                        </TableCell>
                        <TableCell>
                          <Button
                            onClick={this.inputModal(item, "edit")}
                            style={{ cursor: "pointer", minWidth: "0px" }}
                            className="table-btn edit-btn"
                          >
                            Edit
                          </Button>
                          <Button
                            onClick={this.setItemToDelete(item?.id)}
                            style={{ cursor: "pointer", minWidth: "0px" }}
                            className="table-btn delete-btn"
                          >
                            Delete
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  <TableRow>
                    <TableCell
                      colSpan={100}
                      className="pagination"
                    >
                      <div className="tablePagination">
                        {/* <button
                            className={
                              marketErrorlist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              marketErrorlist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("prev")
                            }
                          >
                            <ReactSVG src={arrowLeft} />
                          </button> */}
                        <Pagination
                          hideNextButton
                          hidePrevButton
                          disabled={
                            marketErrorCount / rowPerPage > 1 ? false : true
                          }
                          page={currentPage}
                          onChange={this.handlePaginationClick}
                          count={pageNumbers[pageNumbers?.length - 1]}
                          siblingCount={2}
                          boundaryCount={1}
                          size="small"
                        />
                        {/* <button
                            className={
                              marketErrorlist.length / rowPerPage > 1
                                ? "btn-navigation"
                                : "btn-navigation-disabled"
                            }
                            disabled={
                              marketErrorlist.length / rowPerPage > 1 ? false : true
                            }
                            onClick={() =>
                              this.handlePaginationButtonClick("next")
                            }
                          >
                            <ReactSVG src={arrowRight} />
                          </button> */}
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* </Paper> */}

          <ShowModal
            isModalOpen={isModalOpen}
            onClose={this.toggleModal}
            Content="Are you sure you want to delete?"
            onOkayLabel="Yes"
            onOkay={this.deleteItem}
            onCancel={this.toggleModal}
          />
          <ShowModal
            isModalOpen={isSelectedLogsOpen}
            onClose={this.toggleSelectedLogModal}
            Content="Are you sure you want to delete?"
            onOkayLabel="Yes"
            onOkay={this.handleDeleteSelected}
            onCancel={this.toggleSelectedLogModal}
          />
          <ShowModal
            isModalOpen={isDeletedAllOpen}
            onClose={this.toggleDeletedAllModal}
            Content="Are you sure you want to delete?"
            onOkayLabel="Yes"
            onOkay={this.handleDeleteAll}
            onCancel={this.toggleDeletedAllModal}
          />
          <Modal
            className="modal modal-input"
            open={isInputModalOpen}
            onClose={this.toggleInputModal}
          >
            <div
              className={"paper modal-show-scroll"}
              style={{ position: "relative" }}
            >
              <h3 className="text-center modal-head">Error Update Status</h3>
              <CancelIcon
                className="admin-close-icon"
                onClick={this.toggleInputModal}
              />
              <Box>
                <Grid
                  item
                  xs={12}
                >
                  <Box className="select-box">
                    <label className="modal-label">Status</label>
                    <Select
                      className="React "
                      classNamePrefix="select"
                      menuPosition="fixed"
                      value={
                        // StatusOptions &&
                        StatusOptions?.find((op) => {
                          return op?.value === errorStatus;
                        })
                      }
                      onChange={(e) =>
                        this.setState({
                          errorStatus: e?.value,
                        })
                      }
                      options={StatusOptions && StatusOptions}
                    // options={StatusOptions}
                    />
                    {errorRequire ? (
                      <p
                        className="errorText"
                        style={{ margin: "0px 0 0 0" }}
                      >
                        {errorRequire}
                      </p>
                    ) : (
                      ""
                    )}
                  </Box>
                </Grid>
                <Grid container>
                  <Grid
                    item
                    xs={3}
                  >
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        className="mt-3 purple"
                        onClick={this.handleUpdate}
                        color="secondary"
                        value={!isLoading ? "Update" : "Updating..."}
                        disabled={isLoading}
                        style={{ minWidth: "auto" }}
                      />

                      <Button
                        className="mr-lr-30 outlined"
                        variant="outlined"
                        // color="primary"
                        onClick={this.toggleInputModal}
                        style={{ minWidth: "auto" }}
                      >
                        Back
                      </Button>
                    </div>
                  </Grid>
                </Grid>
              </Box>
            </div>
          </Modal>
        </Grid>
      </Grid>
    </>);
  }
}
export default MarketError;
