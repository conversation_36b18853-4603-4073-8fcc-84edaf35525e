import React, { Component, createRef } from "react";
import { Grid, Button, Box, TextField } from "@mui/material";
import { raceTableFormModel } from "./form-constant";
// import Form from "../../../library/common/components/Form";
import ButtonComponent from "../../../library/common/components/Button";
import ActionMessage from "../../../library/common/components/ActionMessage";
import { URLS } from "../../../library/common/constants";
// import { removeErrorFieldsFromValues } from "../../../library/utilities";
import axiosInstance from "../../../helpers/Axios";
// import { setValidation } from "../../../helpers/common";
import _ from "lodash";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
// import moment from "moment";
import Select from "react-select";
// let raceTableFormModelArray = raceTableFormModel;
class RunnerModal extends Component {
  formRef = createRef();
  constructor(props) {
    super(props);
    this.state = {
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      isLoading: false,
      values: {
        eventId: "",
        // raceId: "",
        raceName: "",
        distance: "",
        // trackId: null,
        sportId: null,
        description: "",
        comment: "",
        weather: "",
      },
      sportAll: [],
      weatherAll: [],
      distanceAll: [],
      raceTableForm: [],
      startTimeDate: new Date(),
      page: 0,
      count: "",
      pageWeather: 0,
      countWeather: "",
      searchWeather: [],
      searchWeatherCount: "",
      SearchWeatherPage: 0,
      isWeatherSearch: "",
      pageDistance: 0,
      countDistance: "",
      searchDistance: [],
      searchDistanceCount: "",
      SearchDistancepage: 0,
      isDistanceSearch: "",
      errorRace: "",
      errorDescription: "",
      errorDistance: "",
      errorSport: "",
      errorWeather: "",
      errorStartTime: "",
    };
  }
  componentDidMount() {
    this.fetchCurrentRace();
    this.fetchAllSport();
    // const { eventsAll } = this.state;
  }

  fetchAllDistance = async (page) => {
    const { status, data } = await axiosInstance.get(
      // `${URLS.distance}?size=20&page=${page}`
      `${URLS.distance}?limit=20&offset=${page}`
    );

    if (status === 200) {
      let Parray = [...this.state.distanceAll];

      let count = data?.result?.count / 20;

      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.name,
          value: item?.id,
        });
      });

      this.setState({
        distanceAll: _.unionBy(this.state.distanceAll, newdata),
        countDistance: Math.ceil(count),
      });
    }
  };
  fetchAllWeather = async (pageWeather) => {
    const { status, data } = await axiosInstance.get(
      `${URLS.weather}?limit=20&offset=${pageWeather}`
    );
    if (status === 200) {
      let Parray = [...this.state.weatherAll];

      let count = data?.result?.count / 20;

      let newdata = [];
      let track = data?.result?.rows?.map((item) => {
        newdata.push({
          label: item?.weatherType,
          value: item?.id,
        });
      });
      let filterData = _.unionBy(this.state.weatherAll, newdata);
      const finalData = filterData.sort((a, b) => {
        return a.label > b.label ? 1 : -1;
      });

      this.setState({
        weatherAll: _.uniqBy(finalData, function (e) {
          return e.value;
        }),
        countWeather: Math.ceil(count),
      });
    }
  };

  fetchAllSport = async () => {
    const { status, data } = await axiosInstance.get(URLS.sports);
    if (status === 200) {
      let data_obj = _.orderBy(data?.result, ["sportName"], ["asc"]);

      let newdata = [];
      let track = data_obj?.map((item) => {
        newdata.push({
          label: item?.sportName,
          value: item?.id,
        });
      });

      this.setState({ sportAll: newdata });
    }
  };

  fetchCurrentRace = async (id) => {
    const { Raceweather, Racedistance } = this.props;

    try {
      const { status, data } = await axiosInstance.get(
        `/races/race/${this.props?.modalId}`
      );
      if (status === 200) {
        let seletedWeather = [
          {
            label: Raceweather?.weatherType,
            value: Raceweather?.id,
          },
        ];
        let seletedDistance = [
          {
            label: Racedistance?.name,
            value: Racedistance?.id,
          },
        ];
        this.setState({
          values: data?.result,
          raceName: data?.result?.raceName,
          distance: data?.result?.distance,
          description: data?.result?.description,
          comment: data?.result?.comment,
          weatherAll: Raceweather?.id ? seletedWeather : [],
          weather: Raceweather?.id ? Raceweather?.id : "",
          distanceAll: seletedDistance,
          distance: Racedistance?.id,
          selectedSport: data?.result?.sportId,
          startDate: new Date(data.result.startDate),
          startTimeDate: new Date(data.result.startTimeDate),
        });
        this.fetchAllWeather(1);
        this.fetchAllDistance(1);
      }
    } catch (err) { }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } });
  };

  validate = () => {
    let {
      // eventId,
      // raceId,
      raceName,
      // description,
      distance,
      // comment,
      // weather,
    } = this.state.values;
    let { /*startDate,*/ startTimeDate } = this.state;
    let flag = true;

    if (
      // eventId === "" ||
      // raceId === "" ||
      raceName === "" ||
      // description === "" ||
      distance === "" ||
      // comment === "" ||
      // weather === null ||
      // startDate === "" ||
      startTimeDate === ""
    ) {
      flag = false;
      //this.setActionMessage(true, "Error", "Please Fill Details First");
      this.setState({ isLoading: false });
    } else {
      flag = true;
      //this.setActionMessage(false);
    }

    return flag;
  };
  handalValidate = () => {
    let {
      // errorRace,
      raceName,
      // description,
      distance,
      selectedSport,
      weather,
      startTimeDate,
    } = this.state;

    let flag = true;
    if (raceName == "") {
      flag = false;
      this.setState({
        errorRace: "This field is mandatory",
      });
    } else {
      // let flag = false;
      this.setState({
        errorRace: "",
      });
    }

    if (distance == "") {
      flag = false;
      this.setState({
        errorDistance: "This field is mandatory",
      });
    } else {
      // let flag = false;
      this.setState({
        errorDistance: "",
      });
    }
    if (selectedSport == "") {
      flag = false;
      this.setState({
        errorSport: "This field is mandatory",
      });
    } else {
      // let flag = false;
      this.setState({
        errorSport: "",
      });
    }
    if (startTimeDate == "") {
      flag = false;
      this.setState({
        errorStartTime: "This field is mandatory",
      });
    } else {
      // let flag = false;
      this.setState({
        errorStartTime: "",
      });
    }
    if (weather == "") {
      flag = false;
      this.setState({
        errorWeather: "This field is mandatory",
      });
    } else {
      // let flag = false;
      this.setState({
        errorWeather: "",
      });
    }
    return flag;
  };

  handleSave = async () => {
    const {
      values,
      raceName,
      description,
      distance,
      comment,
      weather,
      startTimeDate,
      selectedSport,
    } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true });

      try {
        // const url = `${URLS.races}/${this.props.id}`;

        const payload = {
          raceName: raceName,
          description: description,
          distance: distance,
          sportId: selectedSport,
          comment: comment,
          weather: weather,
          eventId: values?.eventId,
          startTimeDate: startTimeDate,
        };

        const { status } = await axiosInstance.put(
          `/races/race/${this.props.modalId}`,
          payload
        );
        if (status === 200) {
          this.setState({ isLoading: false });
          this.props.inputModal();
          this.props.fetchAllRace();
          this.setActionMessage(
            true,
            "Success",
            `Race Table Edited Successfully`
          );
        }
      } catch (err) {
        this.setState({ isLoading: false });
        this.setActionMessage(
          true,
          "Error",
          `An error occurred while editing
      Race Table`
        );
      }
    }
  };

  // handleChange = (field, value) => {
  //   let values = { ...this.state.values, [field]: value };
  //   this.setState({ values: values });
  //   raceTableFormModelArray = raceTableFormModelArray?.map((fieldItem) => {
  //     if (field === fieldItem?.field) {
  //       return setValidation(fieldItem, values);
  //     } else {
  //       return fieldItem;
  //     }
  //   });
  //   this.setActionMessage(false);
  // };
  handleStartTimeDate = (date) => {
    let selectedDate = new Date(date);
    this.setState({ startTimeDate: selectedDate });
  };

  handleOnScrollBottomWeather = () => {
    let {
      countWeather,
      pageWeather,
      isWeatherSearch,
      searchWeatherCount,
      SearchWeatherPage,
    } = this.state;
    if (
      isWeatherSearch !== "" &&
      searchWeatherCount !== Math.ceil(SearchWeatherPage / 10)
    ) {
      this.handleWeatherInputChange(SearchWeatherPage + 20, isWeatherSearch);
      this.setState({
        SearchWeatherPage: SearchWeatherPage + 20,
      });
    } else {
      if (
        countWeather !==
        (countWeather == 1 ? 1 : Math.ceil(pageWeather / 20)) &&
        isWeatherSearch == ""
      ) {
        this.fetchAllWeather(pageWeather + 20);
        this.setState({
          pageWeather: pageWeather + 20,
        });
      }
    }
  };

  handleOnScrollBottomDistance = () => {
    let {
      countDistance,
      pageDistance,
      isDistanceSearch,
      searchDistanceCount,
      SearchDistancepage,
    } = this.state;
    if (
      isDistanceSearch !== "" &&
      searchDistanceCount !== Math.ceil(SearchDistancepage / 20)
    ) {
      this.handleDistanceInputChange(SearchDistancepage + 20, isDistanceSearch);
      this.setState({
        SearchDistancepage: SearchDistancepage + 20,
      });
    } else {
      if (
        countDistance !== Math.ceil(pageDistance / 20) &&
        isDistanceSearch == ""
      ) {
        this.fetchAllDistance(pageDistance + 20);
        this.setState({
          pageDistance: pageDistance + 20,
        });
      }
    }
  };

  handleWeatherInputChange = (page, value) => {
    if (value.length > 2) {
      axiosInstance
        .get(`${URLS.weather}?limit=20&offset=${page}&search=${value}`)
        .then((res) => {
          if (res.status === 200) {
            let response = res?.data?.result?.rows;
            //     let Parray = [...this.state.weatherAll];
            let count = res?.data?.result?.count / 20;
            let newdata = [];
            let FinalData = response?.map((item) => {
              newdata.push({
                label: item?.weatherType,
                value: item?.id,
              });
            });
            let filterData = _.unionBy(this.state.searchWeather, newdata);

            this.setState({
              searchWeather: _.uniqBy(filterData, function (e) {
                return e.value;
              }),
              searchWeatherCount: Math.ceil(count),
              isWeatherSearch: value,
            });
          }
        });
    } else {
      this.setState({
        isWeatherSearch: "",
        SearchWeatherPage: 1,
        searchWeather: [],
      });
    }
  };

  handleDistanceInputChange = (page, value) => {
    if (value.length > 2) {
      axiosInstance
        .get(`${URLS.distance}?limit=20&offset=${page}&search=${value}`)
        .then((res) => {
          if (res.status === 200) {
            let response = res?.data?.result?.rows;
            //     let Parray = [...this.state.weatherAll];
            let count = res?.data?.result?.count / 20;
            let newdata = [];
            let FinalData = response?.map((item) => {
              newdata.push({
                label: item?.name,
                value: item?.id,
              });
            });
            let filterData = _.unionBy(this.state.searchDistance, newdata);

            this.setState({
              searchDistance: _.uniqBy(filterData, function (e) {
                return e.value;
              }),
              searchDistanceCount: Math.ceil(count),
              isDistanceSearch: value,
            });
          }
        });
    } else {
      this.setState({
        isDistanceSearch: "",
        SearchDistancepage: 1,
        searchDistance: [],
      });
    }
  };

  render() {
    var {
      messageBox,
      isLoading,
      values,
      weatherAll,
      sportAll,
      distanceAll,
      raceName,
      description,
      comment,
      selectedSport,
      isWeatherSearch,
      searchWeather,
      weather,
      isDistanceSearch,
      searchDistance,
      distance,
      errorRace,
      errorDistance,
      errorSport,
      errorWeather,
      errorStartTime,
    } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin text-left">
          <Grid item xs={12} className="pageWrapper api-provider">
            {/* <Paper className="pageWrapper api-provider"> */}
            {messageBox?.display && (
              <ActionMessage
                message={messageBox?.message}
                type={messageBox?.type}
                styleClass={messageBox?.styleClass}
              />
            )}

            {/* <Form
              values={values}
              model={raceTableFormModelArray}
              ref={this.formRef}
              onChange={this.handleChange}
            /> */}
            {/* <div style={{ display: "flex" }}> */}
            {/* <div className='input-field' style={{ width: "100%" }}>
                  <label>Start Date</label>
                  <br></br>
                  <DatePicker
                    selected={this.state.startDate}
                    onChange={this.handleStartDate}
                    timeInputLabel='Time:'
                    dateFormat='MM/dd/yyyy h:mm aa'
                    showTimeInput
                  />
                </div> */}
            {/* <div className="input-field" style={{ width: "100%" }}>
              <label>Start Time Date</label>
              <br></br>
              <DatePicker
                selected={this.state.startTimeDate}
                onChange={this.handleStartTimeDate}
                timeInputLabel="Time:"
                dateFormat="MM/dd/yyyy h:mm aa"
                showTimeInput
              />
            </div> */}
            {/* </div> */}

            {/* </Paper> */}
          </Grid>
          <Box className="EditraceWrap">
            {/* <h1>{values?.raceName}</h1> */}
            <Grid container item xs={12}>
              <Grid item xs={6}>
                <Box>
                  <label className="text-label">Race Name*</label>
                  <br />
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    // label="Race Name*"
                    placeholder="Race Name"
                    value={raceName}
                    onChange={(e) =>
                      this.setState({
                        // ...this.state.searchInput,
                        raceName: e.target.value,
                      })
                    }
                  />
                  {errorRace ? (
                    <p className="errorText" style={{ margin: "-14px 0 0 0" }}>
                      {errorRace}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box>
                  <label className="text-label">Description</label>
                  <br />
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    // label="Race Name*"
                    placeholder="Description"
                    value={description}
                    onChange={(e) =>
                      this.setState({
                        // ...this.state.searchInput,
                        description: e.target.value,
                      })
                    }
                  />
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box className="select-box">
                  <label className="modal-label">Distance*</label>
                  <Select
                    className="React "
                    classNamePrefix="select"
                    onMenuScrollToBottom={(e) =>
                      this.handleOnScrollBottomDistance(e)
                    }
                    onInputChange={(e) => this.handleDistanceInputChange(1, e)}
                    value={
                      isDistanceSearch
                        ? searchDistance?.find((item) => {
                          return item?.value == distance;
                        })
                        : distanceAll?.find((item) => {
                          return item?.value == distance;
                        })
                    }
                    onChange={(e) =>
                      this.setState({
                        distance: e.value,
                      })
                    }
                    options={isDistanceSearch ? searchDistance : distanceAll}
                  />
                  {errorDistance ? (
                    <p className="errorText" style={{ margin: "-14px 0 0 0" }}>
                      {errorDistance}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box className="select-box">
                  <label className="modal-label">Sport*</label>
                  <Select
                    className="React "
                    classNamePrefix="select"
                    // onMenuScrollToBottom={(e) => this.handleOnScrollBottom(e)}
                    // onInputChange={(e) => this.handleInputChange(e)}
                    value={sportAll?.find((item) => {
                      return item?.value == selectedSport;
                    })}
                    onChange={(e) =>
                      this.setState({
                        selectedSport: e.value,
                      })
                    }
                    defaultOptions
                    cacheOptions
                    options={sportAll}
                  // defaultMenuIsOpen
                  />
                  {errorSport ? (
                    <p className="errorText" style={{ margin: "-14px 0 0 0" }}>
                      {errorSport}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box>
                  <label className="text-label">Comment</label>
                  <br />
                  <TextField
                    className="textfield-tracks"
                    variant="outlined"
                    color="primary"
                    size="small"
                    // label="Race Name*"
                    placeholder="Description"
                    value={comment}
                    onChange={(e) =>
                      this.setState({
                        // ...this.state.searchInput,
                        comment: e.target.value,
                      })
                    }
                  />
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box className="select-box">
                  <label className="modal-label">Weather*</label>
                  <Select
                    className="React "
                    classNamePrefix="select"
                    onMenuScrollToBottom={(e) =>
                      this.handleOnScrollBottomWeather(e)
                    }
                    onInputChange={(e) => this.handleWeatherInputChange(1, e)}
                    value={
                      isWeatherSearch
                        ? searchWeather?.find((item) => {
                          return item?.value == weather;
                        })
                        : weatherAll?.find((item) => {
                          return item?.value == weather;
                        })
                    }
                    onChange={(e) =>
                      this.setState({
                        weather: e.value,
                      })
                    }
                    options={isWeatherSearch ? searchWeather : weatherAll}
                  />
                  {errorWeather ? (
                    <p className="errorText" style={{ margin: "-14px 0 0 0" }}>
                      {errorSport}
                    </p>
                  ) : (
                    ""
                  )}
                </Box>
              </Grid>
              <Grid item xs={6}>
                <div className="input-field" style={{ width: "100%" }}>
                  <label>Start Time Date*</label>
                  <br></br>
                  <DatePicker
                    selected={this.state.startTimeDate}
                    onChange={this.handleStartTimeDate}
                    timeInputLabel="Time:"
                    dateFormat="MM/dd/yyyy h:mm aa"
                    showTimeInput
                  />
                  {errorStartTime ? (
                    <p className="errorText" style={{ margin: "-14px 0 0 0" }}>
                      {errorSport}
                    </p>
                  ) : (
                    ""
                  )}
                </div>
              </Grid>
            </Grid>
          </Box>
          <Grid container>
            <Grid item xs={3}>
              <div style={{ marginTop: "20px", display: "flex" }}>
                {/* {!isEditMode ? (
                    <ButtonComponent
                      className="mt-3 admin-btn-green"
                      onClick={this.handleSave}
                      color="primary"
                      value={!isLoading ? "Save" : "Loading..."}
                      disabled={isLoading}
                    />
                  ) : ( */}
                <ButtonComponent
                  className="mt-3 admin-btn-purple"
                  onClick={this.handleSave}
                  color="secondary"
                  value={!isLoading ? "Update" : "Updating..."}
                  disabled={isLoading}
                  style={{ minWidth: "auto" }}
                />
                {/* )} */}

                {/* <ButtonComponent
                    onClick={this.props.inputModal}
                    className="mr-lr-30 admin-btn-outlined"
                    value="Back"
                  /> */}
                <Button
                  className="mr-lr-30 admin-btn-outlined"
                  variant="outlined"
                  // color="primary"
                  onClick={this.props.inputModal}
                  style={{ minWidth: "auto" }}
                >
                  Back
                </Button>
              </div>
            </Grid>
          </Grid>
        </Grid>
      </>
    );
  }
}

export default RunnerModal;
