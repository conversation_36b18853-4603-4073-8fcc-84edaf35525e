@import "../../assets/scss/variables";

.bar-chart-wrap {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 12px;
  width: 100%;

  .recharts-legend-wrapper {
    .recharts-legend-item {
      .recharts-legend-icon {
        border-radius: 3px;
      }
    }

    .recharts-legend-item-text {
      font-size: 11.42px;
      line-height: 14px;
      font-family: $regulerFont;
    }
  }

  .recharts-label-list {
    tspan {
      font-size: 11.42px;
      line-height: 14px;
      font-weight: 400;
      font-family: $regulerFont;
      color: #191919;
    }
  }
}

.custom-tooltip {
  background-color: $color-Primary;
  padding: 6px;
  border-radius: 6px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.17);
  width: 165px;
  padding: 12px 9px;
  position: relative;

  // Arrow pointing down
  // &::after {
  //   content: '';
  //   position: absolute;
  //   bottom: -8px;
  //   left: 50%;
  //   transform: translateX(-50%);
  //   width: 0;
  //   height: 0;
  //   border-left: 8px solid transparent;
  //   border-right: 8px solid transparent;
  //   border-top: 8px solid $color-Primary;
  // }

  .tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .tooltip-title {
    font-size: 14px;
    line-height: 16px;
    font-weight: 400;
    color: $color-White;
    font-family: $regulerFont;
    margin: 0px;
  }

  .tooltip-item {
    font-size: 12px;
    line-height: 15px;
    font-weight: 400;
    color: #bfccd8;
    font-family: $regulerFont;
    margin: 7px 0px 0px;
  }
}

.custom-legend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
  }

  .legend-label {
    font-size: 11.42px;
    line-height: 14px;
    color: #000000;
    font-family: $regulerFont;
    text-transform: capitalize;
  }
}
