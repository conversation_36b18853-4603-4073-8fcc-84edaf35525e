@import "../../assets/scss/variables";

.bar-chart-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  width: 100%;

  .recharts-legend-wrapper {
    .recharts-legend-item {
      .recharts-legend-icon {
        border-radius: 3px;
      }
    }

    .recharts-legend-item-text {
      font-size: 11.42px;
      line-height: 14px;
      font-family: $regulerFont;
    }
  }

  .recharts-label-list {
    tspan {
      font-size: 11.42px;
      line-height: 14px;
      font-weight: 400;
      font-family: $regulerFont;
      color: #191919;
    }
  }
}

.custom-tooltip {
  background-color: white;
  padding: 6px;
  border-radius: 6px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.17);

  .tooltip-title {
    font-size: 11.42px;
    line-height: 14px;
    font-weight: 500;
    color: #6e6e6e;
    font-family: $regulerFont;
  }

  .tooltip-item {
    font-size: 11.42px;
    line-height: 14px;
    font-weight: 400;
    color: #000000;
    font-family: $regulerFont;
  }
}

.custom-legend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
  }

  .legend-label {
    font-size: 11.42px;
    line-height: 14px;
    color: #000000;
    font-family: $regulerFont;
    text-transform: capitalize;
  }
}
