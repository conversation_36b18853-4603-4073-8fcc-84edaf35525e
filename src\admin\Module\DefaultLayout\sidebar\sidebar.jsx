import React, { useState } from "react";
import { NavLink, useNavigate, useLocation } from "react-router-dom";
import items from "./menuItems";
import "./sidebarmenu.scss";

import Collapse from "@mui/material/Collapse";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import { fetchFromStorage } from "../../../../library/utilities";
import { identifiers } from "../../../../library/common/constants";
import hourse_img_small from "../../../../images/hourse_img_small.svg";
import greys from "../../../../images/sport_icons/Greyhound.svg";
import harnes from "../../../../images/sport_icons/Harness.svg";
import rugby from "../../../../images/sport_icons/rugbyleague.svg";
import boxing from "../../../../images/menu_icons/Boxing.svg";
import soccer from "../../../../images/sport_icons/soccer.svg";
import cricket from "../../../../images/menu_icons/Cricket.svg";
import mma from "../../../../images/sport_icons/mma.svg";
import { ReactSVG } from "react-svg";

const Sidebar = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState("");
  const [isSubDropdownOpen, setIsSubDropdownOpen] = useState("");
  const [isSubMenuDropdownOpen, setIsSubMenuDropdownOpen] = useState("");
  const [isInnerDropdownOpen, setIsInnerDropdownOpen] = useState("");

  const navigate = useNavigate();
  const location = useLocation();

  const handleDropdown = (label) => {
    setIsDropdownOpen((prev) => (prev === label ? "" : label));
  };

  const handleSubDropdown = (label) => {
    setIsSubDropdownOpen((prev) => (prev === label ? "" : label));
  };

  const handleSubMenuDropdown = (label) => {
    setIsSubMenuDropdownOpen((prev) => (prev === label ? "" : label));
  };

  const handleInnerDropdown = (label) => {
    setIsInnerDropdownOpen((prev) => (prev === label ? "" : label));
  };

  const user = fetchFromStorage(identifiers.user);

  const newItems =
    user?.role === "expertTip"
      ? items?.filter(
          (item) =>
            item?.label === "Racing Expert Tips" ||
            item?.label === "Sport Expert Tips"
        )
      : items;
  return (
    <div className="sidebar">
      <ul>
        {newItems?.map((item, index) => {
          var is_expaned = isDropdownOpen === item.label;
          let currentitem = item.subMenu?.filter(
            (item) => item.value === location.pathname
          );
          if (currentitem?.length > 0) is_expaned = true;
          if (item.isDropdown) {
            if (item.label === "Sports Details") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li
                      button
                      className={
                        isSubDropdownOpen === "Racing"
                          ? "menu-main bg-yellow"
                          : "menu-main bg-gray"
                      }
                      onClick={() => handleSubDropdown("Racing")}
                    >
                      <p>
                        <span>
                          {/* <ReactSVG src={hourse_img_small} /> */}
                          Racing
                        </span>
                        {isSubDropdownOpen === "Racing" ? (
                          <ExpandLess />
                        ) : (
                          <ExpandMore />
                        )}
                      </p>
                    </li>
                    <Collapse
                      in={isSubDropdownOpen === "Racing"}
                      timeout="auto"
                      unmountOnExit
                    >
                      {/* <li className="menu-main bg-gray">
                          <NavLink to="/racing/dashboard">
                            <span className="p20">Race Dashboard</span>
                          </NavLink>
                        </li> */}
                      <li className="menu-main bg-gray">
                        <NavLink to="/racing/odds">
                          <span className="p20">Race odds</span>
                        </NavLink>
                      </li>
                      <li
                        className={
                          isSubMenuDropdownOpen === "all"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        //'menu-main bg-light-green'
                        onClick={() => handleSubMenuDropdown("all")}
                      >
                        <p>
                          <span className="p20">All</span>
                          {isSubMenuDropdownOpen === "all" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "all"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racing/all"
                            className="sports_menu_name p70"
                          >
                            Fixtures
                          </NavLink>
                        </li>
                        {/* <li className="menu-sub bg-gray">
                            <NavLink
                              to="/racingdetail/Horse Racing/1/animal/Horses/1"
                              className="sports_menu_name p70"
                            >
                              Horses
                            </NavLink>
                          </li>
                          <li className="menu-sub bg-gray">
                            <NavLink
                              to="/racingdetail/Horse Racing/1/Jockeys/1"
                              className="sports_menu_name p70"
                            >
                              Jockeys
                            </NavLink>
                          </li>
                          <li className="menu-sub bg-gray">
                            <NavLink
                              to="/racingdetail/Horse Racing/1/Trainers/5"
                              className="sports_menu_name p70"
                            >
                              Trainers
                            </NavLink> 
                          </li>
                          <li className="menu-sub bg-gray">
                            <NavLink
                              to="/racing/Horse Racing/1"
                              className="sports_menu_name p70"
                            >
                              Meetings
                            </NavLink>
                          </li>*/}
                      </Collapse>
                      <li
                        className={
                          isSubMenuDropdownOpen === "Horse Racing"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Horse Racing")}
                      >
                        <p>
                          <span className="p20">Horse Racing</span>
                          {isSubMenuDropdownOpen === "Horse Racing" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Horse Racing"}
                        timeout="auto"
                        unmountOnExit
                      >
                        {/* <li className="menu-sub bg-gray">
                             <NavLink
                               to="/racing/meetings/Horse Racing/1"
                               className="sports_menu_name p70"
                             >
                               Fixtures
                             </NavLink>
                           </li> */}
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Horse Racing/1/animal/Horses/1"
                            className="sports_menu_name p70"
                          >
                            Horses
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Horse Racing/1/Jockeys/1"
                            className="sports_menu_name p70"
                          >
                            Jockeys
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Horse Racing/1/Trainers/5"
                            className="sports_menu_name p70"
                          >
                            Trainers
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        className={
                          isSubMenuDropdownOpen === "Harness Racing"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Harness Racing")}
                      >
                        <p>
                          <span className="p20">Harness Racing</span>
                          {isSubMenuDropdownOpen === "Harness Racing" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Harness Racing"}
                        timeout="auto"
                        unmountOnExit
                      >
                        {/* <li className="menu-sub bg-gray">
                            <NavLink
                              to="/racing/meetings/Harness Racing/2"
                              className="sports_menu_name p70"
                            >
                              Fixtures
                            </NavLink>
                          </li> */}
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Harness Racing/2/animal/Horses/1"
                            className="sports_menu_name p70"
                          >
                            Horses
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Harness Racing/2/Driver/6"
                            className="sports_menu_name p70"
                          >
                            Drivers
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Harness Racing/2/Trainers/7"
                            className="sports_menu_name p70"
                          >
                            Trainers
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        className={
                          isSubMenuDropdownOpen === "Greyhound Racing"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() =>
                          handleSubMenuDropdown("Greyhound Racing")
                        }
                      >
                        <p>
                          <span className="p20">Greyhound Racing</span>
                          {isSubMenuDropdownOpen === "Greyhound Racing" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Greyhound Racing"}
                        timeout="auto"
                        unmountOnExit
                      >
                        {/* <li className="menu-sub bg-gray">
                            <NavLink
                              to="/racing/meetings/Greyhound Racing/3"
                              className="sports_menu_name p70"
                            >
                              Fixtures
                            </NavLink>
                          </li> */}
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Greyhound Racing/3/animal/Greyhounds/2"
                            className="sports_menu_name p70"
                          >
                            Greyhounds
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/racingdetail/Greyhound Racing/3/Trainers/8"
                            className="sports_menu_name p70"
                          >
                            Trainers
                          </NavLink>
                        </li>
                      </Collapse>

                      <li className="menu-main bg-gray">
                        <NavLink to="/racing/tracks">
                          <span className="p20">Tracks</span>
                        </NavLink>
                      </li>

                      <li className="menu-main bg-gray">
                        <NavLink to="/racing/errorLogs?errorTab=0&selectType=api">
                          <span className="p20">Error Logs</span>
                        </NavLink>
                      </li>
                      <li className="menu-main bg-gray">
                        <NavLink to="/racing/pastFixture">
                          <span className="p20">Past Fixture import</span>
                        </NavLink>
                      </li>
                      <li className="menu-main bg-gray">
                        <NavLink to="/automation">
                          <span className="p20">Automation</span>
                        </NavLink>
                      </li>
                      <li className="menu-main bg-gray">
                        <NavLink to="/featured-calender">
                          <span className="p20">Featured Calender</span>
                        </NavLink>
                      </li>
                      <li className="menu-main bg-gray">
                        <NavLink to="/scraper-fixture-config">
                          <span className="p20">Scraper Fixture Config</span>
                        </NavLink>
                      </li>

                      {/* <li className='menu-sub'>
                          <NavLink
                            to='/racing/Harness Racing/2'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={harnes} />
                            Harness Racing
                          </NavLink>
                        </li>
                        <li className='menu-sub'>
                          <NavLink
                            to='/racing/Greyhound Racing/3'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={greys} />
                            Greyhound Racing
                          </NavLink>
                        </li> */}
                    </Collapse>

                    <li
                      button
                      // className={
                      //   isSubDropdownOpen === "Sports"
                      //     ? "menu-main bg-yellow"
                      //     : "menu-main bg-gray"
                      // }
                      className={`menu-main ${
                        isSubDropdownOpen === "Sports" ? "bg-yellow" : "bg-gray"
                      }`}
                      onClick={() => handleSubDropdown("Sports")}
                    >
                      <p>
                        <span>
                          {/* <ReactSVG src={hourse_img_small} /> */}
                          Sports
                        </span>
                        {isSubDropdownOpen === "Sports" ? (
                          <ExpandLess />
                        ) : (
                          <ExpandMore />
                        )}
                      </p>
                    </li>
                    <Collapse
                      in={isSubDropdownOpen === "Sports"}
                      timeout="auto"
                      unmountOnExit
                    >
                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "American Football"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() =>
                          handleSubMenuDropdown("American Football")
                        }
                      >
                        <p>
                          <span>American Football</span>
                          {isSubMenuDropdownOpen === "American Football" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "American Football"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/matchup"
                            className="sports_menu_name p50"
                          >
                            Matchup Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/afl/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Austrailian Rules"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() =>
                          handleSubMenuDropdown("Austrailian Rules")
                        }
                      >
                        <p>
                          <span>Australian Rules</span>
                          {isSubMenuDropdownOpen === "Austrailian Rules" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Austrailian Rules"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/rules"
                            className="sports_menu_name p50"
                          >
                            Rules
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/australianrules/squad"
                            className="sports_menu_name p50"
                          >
                            Squad
                          </NavLink>
                        </li>
                      </Collapse>

                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Basketball"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Basketball")}
                      >
                        <p>
                          <span>
                            {/* <ReactSVG src={rugby} /> */}
                            Basketball
                          </span>
                          {isSubMenuDropdownOpen === "Basketball" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Basketball"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/matchup"
                            className="sports_menu_name p50"
                          >
                            Matchup Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/basketball/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Baseball"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Baseball")}
                      >
                        <p>
                          <span>Baseball</span>
                          {isSubMenuDropdownOpen === "Baseball" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Baseball"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/matchup"
                            className="sports_menu_name p50"
                          >
                            Matchup Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/baseball/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        className={
                          isSubMenuDropdownOpen === "Boxing"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Boxing")}
                      >
                        <p>
                          <span>Boxing</span>
                          {isSubMenuDropdownOpen === "Boxing" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Boxing"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/boxing/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        className={
                          isSubMenuDropdownOpen === "Cricket"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Cricket")}
                      >
                        <p>
                          <span>
                            {/* <ReactSVG src={hourse_img_small} /> */}
                            Cricket
                          </span>
                          {isSubMenuDropdownOpen === "Cricket" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Cricket"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/unique-tournament"
                            className="sports_menu_name p50"
                          >
                            Unique Tournament
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/rules"
                            className="sports_menu_name p50"
                          >
                            Rules
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/cricket/squad"
                            className="sports_menu_name p50"
                          >
                            Squad
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Golf"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Golf")}
                      >
                        <p>
                          <span>Golf</span>
                          {isSubMenuDropdownOpen === "Golf" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Golf"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/matchup"
                            className="sports_menu_name p50"
                          >
                            Matchup Import
                          </NavLink>
                        </li> */}
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Ice Hockey"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Ice Hockey")}
                      >
                        <p>
                          <span>Ice Hockey</span>
                          {isSubMenuDropdownOpen === "Ice Hockey" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Ice Hockey"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/matchup"
                            className="sports_menu_name p50"
                          >
                            Matchup Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/icehockey/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        className={
                          isSubMenuDropdownOpen === "Mixed Martial Arts"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() =>
                          handleSubMenuDropdown("Mixed Martial Arts")
                        }
                      >
                        <p>
                          <span>Mixed Martial Arts</span>
                          {isSubMenuDropdownOpen === "Mixed Martial Arts" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Mixed Martial Arts"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/mma/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Golf"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Golf")}
                      >
                        <p>
                          <span>Golf</span>
                          {isSubMenuDropdownOpen === "Golf" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Golf"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/golf/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                      </Collapse> */}

                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Rugby League"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Rugby League")}
                      >
                        <p>
                          <span>
                            {/* <ReactSVG src={rugby} /> */}
                            Rugby League
                          </span>
                          {isSubMenuDropdownOpen === "Rugby League" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Rugby League"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/rules"
                            className="sports_menu_name p50"
                          >
                            Rules
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyleague/squad"
                            className="sports_menu_name p50"
                          >
                            Squad
                          </NavLink>
                        </li>

                        {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/fixture/Rugby League/12"
                            className="sports_menu_name p50"
                          >
                            Games
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Rugby League/12"
                            className="sports_menu_name p50"
                          >
                            Leagues
                          </NavLink>
                        </li>
                        

                        {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Stadiums/Rugby League/12"
                            className="sports_menu_name p50"
                          >
                            Stadiums
                          </NavLink>
                        </li> */}
                        {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/markets/Rugby League/12"
                            className="sports_menu_name p50"
                          >
                            Markets
                          </NavLink>
                        </li>
                        <li className='menu-sub'>
                          <NavLink
                            to='/sport/Cricket/4'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={cricket} />
                            Cricket
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Mixed Martial Arts/5'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={mma} />
                            Mixed Martial Arts
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Boxing/6'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={boxing} />
                            Boxing
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Tennis/7'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={tennis} />
                            Tennis
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Soccer/8'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={soccer} />
                            Soccer
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Australian Rules/9'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={rugby} />
                            Australian Rules
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Baseball/10'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={baseball} />
                            Baseball
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Basketball/11'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={basketball} />
                            Basketball
                          </NavLink>
                        </li> */}
                        {/* <li className='menu-sub'>
                          <NavLink
                            to='/sport/Rugby League/12'
                            className='sports_menu_name'
                          >
                            <ReactSVG src={rugby} />
                            Rugby League
                          </NavLink>
                        </li> */}
                      </Collapse>
                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Rugby Union"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Rugby Union")}
                      >
                        <p>
                          <span>
                            {/* <ReactSVG src={rugby} /> */}
                            Rugby Union
                          </span>
                          {isSubMenuDropdownOpen === "Rugby Union" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Rugby Union"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/rugbyunion/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        className={
                          isSubMenuDropdownOpen === "Soccer"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Soccer")}
                      >
                        <p>
                          <span>Soccer</span>
                          {isSubMenuDropdownOpen === "Soccer" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Soccer"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/sync-seasons"
                            className="sports_menu_name p50"
                          >
                            Sync Seasons
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/rules"
                            className="sports_menu_name p50"
                          >
                            Rules
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/squad"
                            className="sports_menu_name p50"
                          >
                            Squad
                          </NavLink>
                        </li>
                      </Collapse>
                      <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Tennis"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Tennis")}
                      >
                        <p>
                          <span>Tennis</span>
                          {isSubMenuDropdownOpen === "Tennis" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubMenuDropdownOpen === "Tennis"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/dashboard"
                            className="sports_menu_name p50"
                          >
                            Odds Dashboard
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/category"
                            className="sports_menu_name p50"
                          >
                            Category
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/tournaments"
                            className="sports_menu_name p50"
                          >
                            Tournaments
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/team"
                            className="sports_menu_name p50"
                          >
                            Team
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/player"
                            className="sports_menu_name p50"
                          >
                            Player
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/event"
                            className="sports_menu_name p50"
                          >
                            Event
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/market"
                            className="sports_menu_name p50"
                          >
                            Market
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/marketerror"
                            className="sports_menu_name p50"
                          >
                            Market Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/eventerror"
                            className="sports_menu_name p50"
                          >
                            Event Error
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/futurefixtureimport"
                            className="sports_menu_name p50"
                          >
                            Fixture Import
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/automation"
                            className="sports_menu_name p50"
                          >
                            Automation
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tennis/label"
                            className="sports_menu_name p50"
                          >
                            Label
                          </NavLink>
                        </li>
                      </Collapse>
                      <li className="menu-main bg-gray">
                        <NavLink to="/sportplayermerge">
                          <span>Merge Player</span>
                        </NavLink>
                      </li>

                      {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubMenuDropdownOpen === "Mixed Martial Arts"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() =>
                          handleSubMenuDropdown("Mixed Martial Arts")
                        }
                      >
                        <p>
                          <span>
                            <ReactSVG src={mma} />
                            Mixed Martial Arts
                          </span>
                          {isSubMenuDropdownOpen ===
                          "Mixed Martial Arts" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li> */}
                      {/* <Collapse
                        in={
                          isSubMenuDropdownOpen === "Mixed Martial Arts"
                        }
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/fixture/Mixed Martial Arts/5"
                            className="sports_menu_name p50"
                          >
                            Events
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Mixed Martial Arts/5"
                            className="sports_menu_name p50"
                          >
                            Organisations
                          </NavLink>
                        </li>
                        {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Locations/Mixed Martial Arts/5"
                            className="sports_menu_name p50"
                          >
                            Locations
                          </NavLink>
                        </li> 
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/markets/Mixed Martial Arts/5"
                            className="sports_menu_name p50"
                          >
                            Markets
                          </NavLink>
                        </li>
                      </Collapse> */}
                    </Collapse>

                    {/* Boxing Menu */}
                    {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubDropdownOpen === "Boxing"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Boxing")}
                      >
                        <p>
                          <span>
                            <ReactSVG src={boxing} />
                            Boxing
                          </span>
                          {isSubDropdownOpen === "Boxing" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li> */}
                    {/* <Collapse
                        in={isSubDropdownOpen === "Boxing"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/fixture/Boxing/6"
                            className="sports_menu_name p50"
                          >
                            Events
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Boxing/6"
                            className="sports_menu_name p50"
                          >
                            Organisations
                          </NavLink>
                        </li>
                        {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Locations/Boxing/6"
                            className="sports_menu_name p50"
                          >
                            Locations
                          </NavLink>
                        </li> 
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/markets/Boxing/6"
                            className="sports_menu_name p50"
                          >
                            Markets
                          </NavLink>
                        </li>
                      </Collapse> */}

                    {/* Australian Rules */}
                    {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubDropdownOpen === "Australian Rules"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() =>
                          handleSubMenuDropdown("Australian Rules")
                        }
                      >
                        <p>
                          <span>
                            <ReactSVG src={rugby} />
                            Australian Rules
                          </span>
                          {isSubDropdownOpen ===
                          "Australian Rules" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li> */}
                    <Collapse
                      in={isSubDropdownOpen === "Australian Rules"}
                      timeout="auto"
                      unmountOnExit
                    >
                      <li className="menu-sub bg-gray">
                        <NavLink
                          to="/sport/fixture/Australian Rules/9"
                          className="sports_menu_name p50"
                        >
                          Games
                        </NavLink>
                      </li>
                      <li className="menu-sub bg-gray">
                        <NavLink
                          to="/sport/Australian Rules/9"
                          className="sports_menu_name p50"
                        >
                          Leagues
                        </NavLink>
                      </li>
                      {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Stadiums/Australian Rules/9"
                            className="sports_menu_name p50"
                          >
                            Stadiums
                          </NavLink>
                        </li> */}
                      <li className="menu-sub bg-gray">
                        <NavLink
                          to="/australianrules/9/missingData"
                          className="sports_menu_name p50"
                        >
                          Missing Data
                        </NavLink>
                      </li>
                      <li className="menu-sub bg-gray">
                        <NavLink
                          to="/sport/markets/Australian Rules/9"
                          className="sports_menu_name p50"
                        >
                          Markets
                        </NavLink>
                      </li>
                    </Collapse>

                    {/* Soccer */}
                    {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubDropdownOpen === "Soccer"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubDropdown("Soccer")}
                      >
                        <p>
                          <span>
                            <ReactSVG src={soccer} />
                            Soccer
                          </span>
                          {isSubDropdownOpen === "Soccer" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li> */}
                    {/* <Collapse
                        in={isSubDropdownOpen === "Soccer"}
                        timeout="auto"
                        unmountOnExit
                      >
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/fixture/Soccer/8"
                            className="sports_menu_name p50"
                          >
                            Games
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Soccer/8"
                            className="sports_menu_name p50"
                          >
                            Leagues
                          </NavLink>
                        </li>
                        {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Stadiums/Soccer/8"
                            className="sports_menu_name p50"
                          >
                            Stadiums
                          </NavLink>
                        </li> 
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/soccer/8/missingData"
                            className="sports_menu_name p50"
                          >
                            Missing Data
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/markets/Soccer/8"
                            className="sports_menu_name p50"
                          >
                            Markets
                          </NavLink>
                        </li>
                      </Collapse> */}

                    {/* Cricket */}
                    {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubDropdownOpen === "Cricket"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubDropdown("Cricket")}
                      >
                        <p>
                          <span>
                            <ReactSVG src={cricket} />
                            Cricket
                          </span>
                          {isSubDropdownOpen === "Cricket" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li> */}
                    {/* <Collapse
                        in={isSubDropdownOpen === "Cricket"}
                        timeout="auto"
                        unmountOnExit
                      > */}
                    {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/fixture/Cricket/4"
                            className="sports_menu_name p50"
                          >
                            Games
                          </NavLink>
                        </li>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Cricket/4"
                            className="sports_menu_name p50"
                          >
                            Leagues
                          </NavLink>
                        </li> */}
                    {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/Stadiums/Cricket/4"
                            className="sports_menu_name p50"
                          >
                            Stadiums
                          </NavLink>
                        </li> */}
                    {/* <li className="menu-sub bg-gray">
                          <NavLink
                            to="/sport/markets/Cricket/4"
                            className="sports_menu_name p50"
                          >
                            Markets
                          </NavLink>
                        </li> */}
                    {/* </Collapse> */}

                    {/* Rugby Union */}
                    {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubDropdownOpen === "Rugby Union"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubDropdown("Rugby Union")}
                      >
                        <p>
                          <span>
                            <ReactSVG src={rugby} />
                            Rugby Union
                          </span>
                          {isSubDropdownOpen === "Rugby Union" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li> */}

                    {/* <li
                        button
                        // className='menu-main bg-gray'
                        className={
                          isSubDropdownOpen == "Aussie Rules"
                            ? "menu-main bg-yellow"
                            : "menu-main bg-gray"
                        }
                        onClick={() => handleSubMenuDropdown("Aussie Rules")}
                      >
                        <p>
                          <span>
                            <ReactSVG src={mma} />
                            Aussie Rules
                          </span>
                          {isSubDropdownOpen == "Aussie Rules" ? (
                            <ExpandLess />
                          ) : (
                            <ExpandMore />
                          )}
                        </p>
                      </li>
                      <Collapse
                        in={isSubDropdownOpen == "Aussie Rules"}
                        timeout='auto'
                        unmountOnExit
                      >
                        <li className='menu-sub bg-gray'>
                          <NavLink
                            to='/sport/Aussie Rules/9'
                            className='sports_menu_name p50'
                          >
                            Organisation Details
                          </NavLink>
                        </li>
                        <li className='menu-sub bg-gray'>
                          <NavLink
                            to='/sport/fixture/Aussie Rules/9'
                            className='sports_menu_name p50'
                          >
                            Fixtures
                          </NavLink>
                        </li>
                        <li className='menu-sub bg-gray'>
                          <NavLink
                            to='/sport/Aussie Rules/9/stadiums'
                            className='sports_menu_name p50'
                          >
                            Stadiums
                          </NavLink>
                        </li>
                        <li className='menu-sub bg-gray'>
                          <NavLink
                            to='/sport/Aussie Rules/9/markets'
                            className='sports_menu_name p50'
                          >
                            Markets
                          </NavLink>
                        </li>
                      </Collapse> */}
                  </Collapse>
                </>
              );
            } else if (item.label === "Master Data") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    {/* <li className="menu-sub bg-gray">
                        <NavLink
                          to="/masterdata/Stadiums/Master Data/12"
                          className="sports_menu_name p50"
                        >
                          Stadiums
                        </NavLink>
                      </li> */}
                    <li className="menu-sub bg-gray">
                      <NavLink to="/countries" className="sports_menu_name p50">
                        Country
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink to="/sports" className="sports_menu_name p50">
                        Sport List
                      </NavLink>
                    </li>

                    {/* <li className="menu-sub bg-gray">
                        <NavLink to="/users" className="sports_menu_name p50">
                          Users List
                        </NavLink>
                      </li> */}
                    {/* <li className="menu-sub bg-gray">
                        <NavLink
                          to="/bookkeeper"
                          className="sports_menu_name p50"
                        >
                          Bookkeeper
                        </NavLink>
                      </li> */}
                  </Collapse>
                </>
              );
            } else if (item.label === "Bookkeeper") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/bookkeeper"
                        className="sports_menu_name p50"
                      >
                        Bookkeeper List
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/bookkeeper-review"
                        className="sports_menu_name p50"
                      >
                        Bookkeeper Review
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "News") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    {/* <li className="menu-sub bg-gray">
                        <NavLink
                          to="/masterdata/Stadiums/Master Data/12"
                          className="sports_menu_name p50"
                        >
                          Stadiums
                        </NavLink>
                      </li> */}
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/news/article"
                        className="sports_menu_name p50"
                      >
                        News Article
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/news/category"
                        className="sports_menu_name p50"
                      >
                        News Category
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink to="/news/tag" className="sports_menu_name p50">
                        News Tags
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "User Management") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/user-dashboard"
                        className="sports_menu_name p50"
                      >
                        Dashboard
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/subscriber-dashboard"
                        className="sports_menu_name p50"
                      >
                        Subscriber Dashboard
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/soc-smartPlay-dashboard"
                        className="sports_menu_name p50"
                      >
                        SOC SmartPlay Dashboard
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink to="/users" className="sports_menu_name p50">
                        Users List
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "Campaign") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink to="/clients" className="sports_menu_name p50">
                        Clients
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/adcampaign"
                        className="sports_menu_name p50"
                      >
                        Ad Campaign
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "Racing Expert Tips") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/expert-tips"
                        className="sports_menu_name p50"
                      >
                        Expert Tips
                      </NavLink>
                    </li>
                    {user?.role === "admin" ? (
                      <>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/tips-of-the-day"
                            className="sports_menu_name p50"
                          >
                            Tips Of The Day
                          </NavLink>
                        </li>
                      </>
                    ) : (
                      <></>
                    )}
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/backawinner/featured-race"
                        className="sports_menu_name p50"
                      >
                        BAW Featured Race
                      </NavLink>
                    </li>
                    {user?.role === "admin" ? (
                      <>
                        <li className="menu-sub bg-gray">
                          <NavLink
                            to="/backawinner/weekly-newsletter"
                            className="sports_menu_name p50"
                          >
                            Weekly Newsletter
                          </NavLink>
                        </li>
                      </>
                    ) : (
                      <></>
                    )}
                  </Collapse>
                </>
              );
            } else if (item.label === "Sport Expert Tips") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/sport-expert-tips"
                        className="sports_menu_name p50"
                      >
                        Expert Tips
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "Tipping Comp") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/tippingcomp-tips"
                        className="sports_menu_name p50"
                      >
                        Tipping Comp Tips
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/tipping-FAQs"
                        className="sports_menu_name p50"
                      >
                        Tipping Comp FAQs
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/tipping-list"
                        className="sports_menu_name p50"
                      >
                        Tipping Comp List
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/tipping-prize"
                        className="sports_menu_name p50"
                      >
                        Tipping Comp Prize
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/premium-tipping-comps"
                        className="sports_menu_name p50"
                      >
                        Premium Comp fees
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "Subscriptions") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/subscription-active"
                        className="sports_menu_name p50"
                      >
                        Subscriptions Active
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/subscriptionCoupon"
                        className="sports_menu_name p50"
                      >
                        Subscriptions Coupon
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/subscription-dashboard"
                        className="sports_menu_name p50"
                      >
                        Subscriptions Dashboard
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "SOC") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/contact-us"
                        className="sports_menu_name p50"
                      >
                        Contact Us
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/soc-broadcast"
                        className="sports_menu_name p50"
                      >
                        SOC Broadcast
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/testimonial"
                        className="sports_menu_name p50"
                      >
                        Testimonial
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "Fantasy") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/fantasy-coins"
                        className="sports_menu_name p50"
                      >
                        Coins
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/fantasy-kyc"
                        className="sports_menu_name p50"
                      >
                        KYC
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/fantasy-user-coins"
                        className="sports_menu_name p50"
                      >
                        User Coins
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/fantasy-withdraw"
                        className="sports_menu_name p50"
                      >
                        Withdraw
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else if (item.label === "Our Team") {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/backawinner/ourteam"
                        className="sports_menu_name p50"
                      >
                        Team Details
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/backawinner/ourteam-category"
                        className="sports_menu_name p50"
                      >
                        Team Category
                      </NavLink>
                    </li>
                    <li className="menu-sub bg-gray">
                      <NavLink
                        to="/backawinner/ourteam-positions"
                        className="sports_menu_name p50"
                      >
                        Team Positions
                      </NavLink>
                    </li>
                  </Collapse>
                </>
              );
            } else {
              return (
                <>
                  <li
                    button
                    key={index}
                    onClick={() => handleDropdown(item.label)}
                    className={is_expaned ? "menu-main bg-yellow" : "menu-main"}
                  >
                    <p>
                      {item.label}
                      {is_expaned ? <ExpandLess /> : <ExpandMore />}
                    </p>
                  </li>
                  <Collapse in={is_expaned} timeout="auto" unmountOnExit>
                    <li className="menu-sub">
                      {item.subMenu?.map((menu, i) => (
                        <NavLink
                          key={i}
                          to={menu.value}
                          className="sports_menu_name"
                        >
                          {menu?.icon !== "undefined" && (
                            <ReactSVG src={menu.icon} />
                          )}
                          {menu.label}
                        </NavLink>
                      ))}
                    </li>
                  </Collapse>
                </>
              );
            }
          } else {
            return (
              <li>
                <NavLink to={item.value}>{item.label}</NavLink>
              </li>
            );
          }
        })}
      </ul>
    </div>
  );
};

export default Sidebar;
