import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import styled from "styled-components";
import { Link } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import SearchIcons from "../../images/searchIcon.svg";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import ButtonComponent from "../../library/common/components/Button";
import { config } from "../../helpers/config";
import FileUploader from "../../library/common/components/FileUploader";
import Pagination from "@mui/material/Pagination";
import DeleteIcon from "@mui/icons-material/Delete";
import Select, { components } from "react-select";
import moment from "moment-timezone";
import "../tippingPremiumPrize/tippingPremiumPrize.scss";
import _, { values } from "lodash";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const keyOption = [
  {
    label: "Tipping Fees",
    value: "TippingFees",
  },
];

const statusOption = [
  {
    label: "Active",
    value: "active",
  },
  {
    label: "Deleted",
    value: "deleted",
  },
];

const externalStatusOption = [
  {
    label: "All",
    value: null,
  },
  {
    label: "Active",
    value: "active",
  },
  {
    label: "Deleted",
    value: "deleted",
  },
];

class Testimonial extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      isInputModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      idToSend: null,
      isEditMode: false,
      // startDate: null,
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      currentPage: 1,
      rowPerPage: 20,
      offset: 0,
      testimonialValues: {
        firstname: "",
        lastname: "",
        role: "",
        descriptions: "",
        status: "active",
        mediaId: "",
      },
      TestimonialList: [],
      TestimonialCount: 0,
      errorFirstName: "",
      errorLastName: "",
      errorRole: "",
      errorDescription: "",
      isSearch: "",
      selectedTippingPrizeID: "",
      sports: [],
      selectedExternalStatus: null,
      OrgAll: [],
      selectedOrg: null,
      OrgApiCount: 0,
      isOrgSearch: "",
      countOrg: 0,
      searchOrg: [],
      searchOrgCount: 0,
      SearchOrgpage: 0,
      pageOrg: 0,
      errorCreate: "",
      defaultImage: [],
      defaultUploadImage: "",
      draggedItem: null,
      isSortChange: false,
    };
  }

  componentDidMount() {
    this.fetchTestimonial(0, "", null);
    this.fetchSportData();
  }

  componentDidUpdate(prevProps, prevState) {
    const { offset, isSearch, selectedExternalStatus, selectedOrg } =
      this.state;
    if (prevState.offset !== this.state.offset) {
      this.fetchTestimonial(offset, isSearch, selectedExternalStatus);
    }
    if (prevProps.match.path !== this.props.match.path) {
      this.fetchTestimonial(0, "", null);
      this.setState({
        offset: 0,
        currentPage: 1,
        isSearch: "",
        selectedExternalStatus: null,
        selectedOrg: null,
      });
    }
  }

  fetchSportData = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        `/sports/sport/?sportTypeId=${2}`
      );
      if (status === 200) {
        const sportsdata = data?.result.map((s) => ({
          ...s,
          label: s?.sportName,
          value: s?.id,
        }));

        const sdata = _.orderBy(sportsdata, ["label"], ["asc"])?.filter(
          (item) => [9, 4, 12]?.includes(item?.id)
        );
        let alldatas = sdata?.unshift({
          label: "All Sports",
          value: 0,
        });

        this.setState({
          sports: sdata,
          selectedExternalStatus: sdata?.[0]?.id,
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  fetchOrgData = async (page, sID, OrgAll) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/tournament?SportId=${sID}&offset=${page}&limit=${20}`
      );
      if (status === 200) {
        const newdata = data?.result?.rows.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));

        const filterData = _.unionBy(OrgAll, newdata)?.sort((a, b) =>
          a?.label.localeCompare(b?.label)
        );
        const unique = _.uniqBy(filterData, "value");
        let alldatas = unique?.unshift({
          label: "All Tournaments",
          value: 0,
        });

        this.setState((prevState) => ({
          OrgApiCount: prevState.OrgApiCount + 1,
          countOrg: Math.ceil(data?.result?.count / 20),
          OrgAll: unique,
          selectedOrg: unique?.[0]?.value,
        }));

        this.fetchPublicComp(sID, unique?.[0]?.value, 1, []);
      }
    } catch (err) {
      console.error(err);
    }
  };

  handleOrgInputChange = async (page, value, sid) => {
    try {
      const { status, data } = await axiosInstance.get(
        `public/tournament?SportId=${sid}&limit=${20}&offset=${page}&search=${value}`
      );
      if (status === 200) {
        const response = data?.result?.rows;
        const newdata = response.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));

        const mergeData = _.unionBy(this.state.searchOrg, newdata);
        const filterData = _.uniqBy(mergeData, "value");

        this.setState({
          searchOrg: filterData,
          isOrgSearch: value,
          searchOrgCount: Math.ceil(data?.result?.count / 20),
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  handleInputChangeOrg = (newValue, actionMeta) => {
    if (actionMeta.action === "input-change") {
      this.handleOrgInputChange(0, newValue, this.state.selectedExternalStatus);
    }
  };

  handleOnScrollBottomOrg = () => {
    if (
      this.state.isOrgSearch !== "" &&
      this.state.searchOrgCount !== Math.ceil(this.state.SearchOrgpage / 20)
    ) {
      this.handleOrgInputChange(
        this.state.SearchOrgpage + 20,
        this.state.isOrgSearch,
        this.state.selectedExternalStatus
      );

      this.setState((prevState) => ({
        SearchOrgpage: prevState.SearchOrgpage + 20,
      }));
    } else {
      if (
        this.state.countOrg !== 0 &&
        this.state.countOrg !== Math.ceil(this.state.pageOrg / 20 + 1)
      ) {
        this.fetchOrgData(
          this.state.pageOrg + 20,
          this.state.selectedExternalStatus,
          this.state.OrgAll
        );
        this.setState((prevState) => ({ pageOrg: prevState.pageOrg + 20 }));
      }
    }
  };

  async fetchTestimonial(page, search, statusType) {
    let { rowPerPage } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/testimonials?isAdmin=true&search=${search}&status=${
          statusType ? statusType : ""
        }`
      );
      if (status === 200) {
        this.setState({
          TestimonialList: data?.result,
          isLoading: false,
          TestimonialCount: data?.count,
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
    } catch {
      this.setState({
        isLoading: false,
      });
    }
  }

  handalValidate = () => {
    let { testimonialValues } = this.state;
    let flag = true;
    if (
      testimonialValues?.firstname?.trim() === "" ||
      testimonialValues?.firstname === null
    ) {
      flag = false;
      this.setState({
        errorFirstName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorFirstName: "",
      });
    }

    // if (
    //   testimonialValues?.lastname?.trim() === "" ||
    //   testimonialValues?.lastname === null
    // ) {
    //   flag = false;
    //   this.setState({
    //     errorLastName: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorLastName: "",
    //   });
    // }

    // if (
    //   testimonialValues?.role?.trim() === "" ||
    //   testimonialValues?.role === null
    // ) {
    //   flag = false;
    //   this.setState({
    //     errorRole: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorRole: "",
    //   });
    // }

    if (
      testimonialValues?.descriptions?.trim() === "" ||
      testimonialValues?.descriptions === null
    ) {
      flag = false;
      this.setState({
        errorDescription: "This field is mandatory",
      });
    } else {
      this.setState({
        errorDescription: "",
      });
    }

    return flag;
  };

  handleSave = async () => {
    if (this.handalValidate()) {
      const {
        testimonialValues,
        offset,
        isSearch,
        selectedExternalStatus,
        defaultImage,
      } = this.state;
      this.setState({ isLoading: true, isEditMode: false });
      let payload = {
        firstname: testimonialValues?.firstname,
        lastname: testimonialValues?.lastname,
        role: testimonialValues?.role,
        descriptions: testimonialValues?.descriptions,
        status: testimonialValues?.status,
      };
      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            MediaId: fileData?.image?.id,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      }

      try {
        const { status, data } = await axiosInstance.post(
          `/testimonials`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.fetchTestimonial(offset, isSearch, selectedExternalStatus);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
            defaultImage: [],
            defaultUploadImage: "",
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
          defaultImage: [],
          defaultUploadImage: "",
        });

        // this.setActionMessage(true, "Error", err?.response?.data?.message);
      }
    }
  };

  handleUpdate = async () => {
    const {
      testimonialValues,
      selectedTippingPrizeID,
      offset,
      isSearch,
      selectedExternalStatus,
      defaultImage,
    } = this.state;

    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });
      let payload = {
        firstname: testimonialValues?.firstname,
        lastname: testimonialValues?.lastname,
        role: testimonialValues?.role,
        descriptions: testimonialValues?.descriptions,
        status: testimonialValues?.status,
      };

      if (defaultImage?.length > 0) {
        let fileData = await this.setMedia(defaultImage[0]);
        if (fileData) {
          payload = {
            ...payload,
            MediaId: fileData?.image?.id,
          };
          this.setState({
            defaultUploadImage: fileData?.image?.filePath,
          });
        }
      } else {
        payload = {
          ...payload,
          MediaId: testimonialValues?.mediaId,
        };
      }

      try {
        const { status, data } = await axiosInstance.put(
          `/testimonials/${selectedTippingPrizeID}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: "",
          });
          this.fetchTestimonial(offset, isSearch, selectedExternalStatus);
          this.setActionMessage(true, "Success", data?.message);
        } else {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            errorCreate: data?.message,
          });
          this.setActionMessage(true, "Error", data?.message);
        }
      } catch (err) {
        this.setState({
          isLoading: false,
          errorCreate: err?.response?.data?.message,
        });
      }
    }
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorFirstName: "",
      errorLastName: "",
      errorRole: "",
      errorDescription: "",
      errorCreate: "",
      defaultImage: [],
      defaultUploadImage: "",
    });
  };

  inputModal = (item, type) => () => {
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.setState({
        testimonialValues: {
          firstname: item?.firstname,
          lastname: item?.lastname,
          role: item?.role,
          descriptions: item?.descriptions,
          status: item?.status,
          mediaId: item?.MediaId,
        },
        defaultUploadImage: item?.Media?.filePath,
        isEditMode: true,
        selectedTippingPrizeID: item?.id,
      });
    } else {
      this.setState({
        testimonialValues: {
          firstname: "",
          lastname: "",
          role: "",
          descriptions: "",
          status: "active",
          mediaId: "",
        },
        isEditMode: false,
      });
    }
  };

  handleFeatureLogoRemove = () => {
    const { defaultImage, defaultUploadImage } = this.state;
    {
      defaultImage?.length > 0
        ? this.setState({ defaultImage: [] })
        : defaultUploadImage &&
          defaultUploadImage !== "" &&
          this.setState({
            defaultUploadImage: "",
          });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };
  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  deleteItem = async () => {
    const {
      itemToDelete,
      offset,
      isSearch,
      selectedExternalStatus,
      selectedOrg,
    } = this.state;

    try {
      const { status, data } = await axiosInstance.delete(
        `/testimonials/${itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchTestimonial(offset, isSearch, selectedExternalStatus);
        });
        this.setActionMessage(true, "Success", data?.message);
      }
    } catch (err) {
      this.setActionMessage(true, "Error", err?.response?.data?.message);
    }
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage } = this.state;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
  };

  handlePaginationButtonClick = (navDirection) => {
    let { currentPage, rowPerPage, TestimonialList, offset } = this.state;
    if (navDirection === "prev") {
      if (offset >= rowPerPage) {
        this.setState({
          offset: offset - rowPerPage,
          currentPage: currentPage - 1,
        });
      }
    } else {
      this.setState({
        offset: offset + rowPerPage,
        currentPage: currentPage + 1,
      });
    }
  };

  handleClearClick = () => {
    const { selectedExternalStatus, selectedOrg } = this.state;
    this.fetchTestimonial(0, "", selectedExternalStatus);
    this.setState({
      offset: 0,
      currentPage: 1,
      isSearch: "",
    });
  };

  DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        {/* <SelectIndicator /> */}
      </components.DropdownIndicator>
    );
  };

  handleDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    const updatedList = Array.from(this.state?.TestimonialList);
    const [draggedItem] = updatedList.splice(result.source.index, 1);
    updatedList.splice(result.destination.index, 0, draggedItem);
    this.setState({ TestimonialList: updatedList, isSortChange: true });
  };

  handleOrderChange = async () => {
    const { TestimonialList, offset, isSearch, selectedExternalStatus } =
      this.state;

    let newdata = [];
    let categories = TestimonialList?.map((item) => {
      newdata.push(item?.id);
    });
    try {
      const { status, data } = await axiosInstance.put(
        `/testimonials/update/sortOrder`,
        newdata
      );
      if (status === 200) {
        this.setState({ isSortChange: false });
        this.fetchTestimonial(offset, isSearch, selectedExternalStatus);
        this.setActionMessage(true, "Success", data?.message);
      } else {
        this.setState({ isSortChange: false });
      }
    } catch (err) {
      this.setState({ isSortChange: false });
    }
  };

  render() {
    var {
      isModalOpen,
      messageBox,
      isInputModalOpen,
      isLoading,
      isEditMode,
      rowPerPage,
      currentPage,
      offset,
      testimonialValues,
      TestimonialList,
      TestimonialCount,
      errorFirstName,
      errorLastName,
      errorRole,
      errorDescription,
      isSearch,
      selectedExternalStatus,
      errorCreate,
      defaultImage,
      defaultUploadImage,
    } = this.state;
    const pageNumbers = [];

    if (TestimonialCount > 0) {
      for (let i = 1; i <= Math.ceil(TestimonialCount / rowPerPage); i++) {
        pageNumbers.push(i);
      }
    }

    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Typography className="active_p">Testimonial</Typography>
              </Breadcrumbs>
            </Box>
            <Grid container direction="row" alignItems="center">
              <Grid item xs={3}>
                <Typography variant="h1" align="left">
                  Testimonial
                </Typography>
              </Grid>

              <Grid
                item
                xs={9}
                className="admin-filter-wrap admin-fixture-wrap "
              >
                <Select
                  className="React teamsport-select external-select sort-select"
                  value={
                    selectedExternalStatus &&
                    externalStatusOption?.find(
                      (item) => item?.value === selectedExternalStatus
                    )
                  }
                  onChange={(e) => {
                    this.setState({
                      selectedExternalStatus: e?.value === 0 ? "" : e?.value,
                      currentPage: 1,
                      offset: 0,
                    });

                    this.fetchTestimonial(0, isSearch, e?.value);
                  }}
                  options={externalStatusOption}
                  classNamePrefix="select"
                  placeholder="Status"
                />

                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={isSearch}
                  onChange={(e) => {
                    this.setState({ isSearch: e?.target?.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {isSearch && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() =>
                    this.fetchTestimonial(
                      offset,
                      isSearch,
                      selectedExternalStatus
                    )
                  }
                >
                  Search
                </Button>
              </Grid>
              <Grid
                item
                xs={12}
                className="admin-filter-wrap admin-fixture-wrap"
                style={{ marginBottom: "10px", marginTop: "12px" }}
              >
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: this.state.isSortChange
                      ? "#4455c7"
                      : "rgba(0, 0, 0, 0.12)",
                    color: this.state.isSortChange
                      ? "#fff"
                      : "rgba(0, 0, 0, 0.26)",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  disabled={!this.state.isSortChange}
                  onClick={() => {
                    this.handleOrderChange();
                  }}
                >
                  Save Order
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && TestimonialList?.length === 0 && (
              <p>No Data Available</p>
            )}

            {!isLoading && TestimonialList?.length > 0 && (
              <>
                <DragDropContext onDragEnd={this.handleDragEnd}>
                  <TableContainer component={Paper}>
                    <Table className="listTable" aria-label="simple table">
                      <TableHead className="tableHead-row">
                        <TableRow>
                          <TableCell style={{ cursor: "pointer" }}>
                            ID
                          </TableCell>
                          <TableCell>Name</TableCell>
                          <TableCell>Role</TableCell>
                          <TableCell style={{ width: "50%" }}>
                            Descriptions
                          </TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <Droppable droppableId="your-droppable-id">
                        {(provided, snapshot) => (
                          <TableBody
                            className="table_body"
                            ref={provided.innerRef}
                          >
                            <TableRow className="table_row">
                              <TableCell
                                colSpan={100}
                                className="table-seprator"
                              ></TableCell>
                            </TableRow>
                            {TestimonialList?.map((item, index) => {
                              return (
                                <Draggable
                                  key={item?.id}
                                  draggableId={`draggable-${item?.id}`}
                                  index={index}
                                >
                                  {(provided, snapshot) => (
                                    <TableRow
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      style={{
                                        cursor: "all-scroll",
                                        borderBottom: "none",
                                        ...provided.draggableProps.style,
                                      }}
                                      className="table-rows listTable-Row"
                                      key={item?.id}
                                    >
                                      <TableCell> {item?.id} </TableCell>
                                      <TableCell>
                                        {item?.firstname + " " + item?.lastname}
                                      </TableCell>
                                      <TableCell>{item?.role}</TableCell>
                                      <TableCell>
                                        {item?.descriptions}
                                      </TableCell>
                                      <TableCell
                                        style={{ textTransform: "capitalize" }}
                                      >
                                        {item?.status}
                                      </TableCell>
                                      <TableCell>
                                        <Button
                                          onClick={this.inputModal(
                                            item,
                                            "edit"
                                          )}
                                          className="table-btn edit-btn"
                                        >
                                          Edit
                                        </Button>
                                        <Button
                                          onClick={this.setItemToDelete(
                                            item?.id
                                          )}
                                          className="table-btn delete-btn"
                                        >
                                          Delete
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  )}
                                </Draggable>
                              );
                            })}
                            {/* <TableRow>
                              <TableCell colSpan={100} className="pagination">
                                <div className="tablePagination">
                                  <Pagination
                                    hideNextButton
                                    hidePrevButton
                                    disabled={
                                      TestimonialCount / rowPerPage > 1
                                        ? false
                                        : true
                                    }
                                    page={currentPage}
                                    onChange={this.handlePaginationClick}
                                    count={pageNumbers[pageNumbers?.length - 1]}
                                    siblingCount={2}
                                    boundaryCount={1}
                                    size="small"
                                  />
                                </div>
                              </TableCell>
                            </TableRow> */}
                          </TableBody>
                        )}
                      </Droppable>
                    </Table>
                  </TableContainer>
                </DragDropContext>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />

            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Testimonial" : "Edit Testimonial"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    {messageBox.display && (
                      <ActionMessage
                        message={messageBox.message}
                        type={messageBox.type}
                        styleClass={messageBox.styleClass}
                      />
                    )}
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">First Name</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="First Name"
                          value={testimonialValues?.firstname}
                          onChange={(e) =>
                            this.setState({
                              testimonialValues: {
                                ...testimonialValues,
                                firstname: e?.target?.value,
                              },
                              errorFirstName: e?.target?.value
                                ? ""
                                : errorFirstName,
                            })
                          }
                        />
                        {errorFirstName ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorFirstName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Last Name</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Last Name"
                          value={testimonialValues?.lastname}
                          onChange={(e) =>
                            this.setState({
                              testimonialValues: {
                                ...testimonialValues,
                                lastname: e?.target?.value,
                              },
                              // errorLastName: e?.target?.value
                              //   ? ""
                              //   : errorLastName,
                            })
                          }
                        />
                        {/* {errorLastName ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorLastName}
                          </p>
                        ) : (
                          ""
                        )} */}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Role</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="text"
                          color="primary"
                          size="small"
                          placeholder="Role"
                          value={testimonialValues?.role}
                          onChange={(e) =>
                            this.setState({
                              testimonialValues: {
                                ...testimonialValues,
                                role: e?.target?.value,
                              },
                              // errorRole: e?.target?.value ? "" : errorRole,
                            })
                          }
                        />
                        {/* {errorRole ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorRole}
                          </p>
                        ) : (
                          ""
                        )} */}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Description</label>
                        <TextField
                          className="teamsport-textfield rec FAQ-textfield"
                          variant="outlined"
                          type="textarea"
                          multiline
                          maxRows={4}
                          minRows={3}
                          color="primary"
                          size="small"
                          placeholder="Description"
                          value={testimonialValues?.descriptions}
                          onChange={(e) =>
                            this.setState({
                              testimonialValues: {
                                ...testimonialValues,
                                descriptions: e?.target?.value,
                              },
                              errorDescription: e?.target?.value
                                ? ""
                                : errorDescription,
                            })
                          }
                        />
                        {errorDescription ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0px 0px 0px" }}
                          >
                            {errorDescription}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginBottom: "8px",
                        }}
                      >
                        <label className="modal-label">Status</label>
                        <Select
                          className="React teamsport-select event-Tournament-select premium-key-select"
                          classNamePrefix="select"
                          menuPosition="fixed"
                          placeholder="Status"
                          value={
                            testimonialValues?.status &&
                            statusOption?.find((item) => {
                              return item?.value == testimonialValues?.status;
                            })
                          }
                          options={statusOption}
                          onChange={(e) => {
                            this.setState({
                              testimonialValues: {
                                ...testimonialValues,
                                status: e?.value,
                              },
                            });
                          }}
                        />
                      </Grid>
                      <div
                        className="blog-file-upload"
                        style={{ width: "100%", marginTop: "5px" }}
                      >
                        <label className="modal-label">
                          Testimonial User Image{" "}
                        </label>
                        <Box style={{ marginTop: "5px" }}>
                          <FileUploader
                            onDrop={(image) =>
                              this.handleFileUpload("defaultImage", image)
                            }
                          />
                        </Box>
                        <Box
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <div className="logocontainer">
                            {defaultImage?.length > 0
                              ? defaultImage?.map((file, index) => (
                                  <img
                                    className="auto-width"
                                    key={index}
                                    src={file.preview}
                                    alt="icon"
                                  />
                                ))
                              : defaultUploadImage &&
                                defaultUploadImage !== "" && (
                                  <img
                                    className="auto-width"
                                    src={
                                      defaultUploadImage?.includes("uploads")
                                        ? config.mediaUrl + defaultUploadImage
                                        : defaultUploadImage
                                    }
                                    alt="icon"
                                  />
                                )}
                          </div>
                          {/*} {(defaultImage?.length > 0 ||
                            (defaultUploadImage &&
                              defaultUploadImage !== "")) && (
                            <Box className="delete-icon-wrap">
                              <DeleteIcon
                                className="delete-icon"
                                onClick={() => this.handleFeatureLogoRemove()}
                                style={{ cursor: "pointer" }}
                              />
                            </Box>
                          )} */}
                        </Box>
                      </div>
                      <span style={{ fontSize: "12px", color: "red" }}>
                        {errorCreate ? errorCreate : ""}
                      </span>
                    </Grid>
                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
export default Testimonial;
