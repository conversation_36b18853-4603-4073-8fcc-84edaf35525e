import React from "react";
import {
  Grid,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Modal,
  Box,
  Breadcrumbs,
  Typography,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  TableSortLabel,
  IconButton,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import axiosInstance from "../../helpers/Axios";
import { URLS } from "../../library/common/constants";
import ActionMessage from "../../library/common/components/ActionMessage";
import { Loader } from "../../library/common/components";
import ShowModal from "../../components/common/ShowModal/ShowModal";
import Pagination from "@mui/material/Pagination";
// import CreateUsers from "./CreateUsers";
import { Link } from "react-router-dom";
// import StatisticsTableModal from "./ActivityTableModal";
// import CSVExport from "../csvExport/CSVExport";
import { config } from "../../helpers/config";
import moment from "moment-timezone";
import SearchIcons from "../../images/searchIcon.svg";
import FileUploader from "../../library/common/components/FileUploader";
import ButtonComponent from "../../library/common/components/Button";
import Select from "react-select";
import { MdKeyboardBackspace } from "react-icons/md";
// import CreateClient from "./CreateClient";
// import DetailsTableModal from "./DetailsTableModal";

const typeOptions = [
  {
    label: "All",
    value: 1,
  },
  {
    label: "Script",
    value: 2,
  },
  {
    label: "Image",
    value: 3,
  },
];

export default class MediaGallery extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      mediaData: [],
      isInputModalOpen: false,
      idToSend: null,
      isModalOpen: false,
      isLoading: false,
      itemToDelete: null,
      isEditMode: false,
      userId: "",
      messageBox: {
        display: false,
        type: "",
        message: "",
      },
      AdValues: {
        name: "",
        description: "",
        clientId: 0,
        type: "",
        id: "",
      },
      search: "",
      currentPage: 1,
      rowPerPage: 20,
      script: "",
      mediaCount: 0,
      mediaDetail: [],
      advTableListData: [],
      categoryData: [],
      categoryCount: 0,
      TournamentCount: 0,
      TournamentList: [],
      SelectedTournamentList: [],
      errorName: "",
      errorClient: "",
      errorDescription: "",
      errorType: "",
      errorScript: "",
      searchCategory: [],
      searchCategoryCount: 0,
      searchCategoryPage: 0,
      isCategorySearch: "",
      selectedPage: "",
      advPageListOptions: [],
      ExternalAdvPageListOptions: [],
      selectedBanner: "",
      advBannerListOptions: [],
      selectedModalPage: "",
      selectedModalBanner: "",
      image: [],
      uploadImage: "",
      createError: "",
      paginationPage: [],
      mediaClientData: [],
      sortType: "id",
      sortLabelid: false,
      sortName: true,
      sortStartDate: true,
      sortEndDate: true,
      sortDate: null,
      filterEndDate: null,
      startDateOpen: false,
      endDateOpen: false,
      csvListData: [],
      clientOptionsData: [],
      selectedClientOption: [],
      gotClientId: null,
      dataFilter: "",
      isViewSourceModalOpen: false,
      isViewSourceData: {},
    };
  }

  componentDidMount() {
    // if (clientId) {
    //   this.fetchMedialist(0, clientId);
    // }
    this.fetchMedialist(0, "", "id", true);

    this.fetchClientOptions();
  }

  afterChangeRefresh = () => {
    this.fetchMedialist(this.state.offset, "", "id", true);
  };

  fetchCurrentMedia = async (id) => {
    const { status, data } = await axiosInstance.get(
      `/campaign/mediadetails/${id} `
    );
    if (status === 200) {
      this.setState({ AdValues: data, script: data?.script });
      this.setState(() => {
        return {
          AdValues: {
            ...this.state.AdValues,
          },
        };
      });
    }
  };

  inputModal = (item, type) => () => {
    const { AdValues, image, gotClientId } = this.state;
    this.setState({ isInputModalOpen: true });
    if (type === "edit") {
      this.fetchCurrentMedia(item?.id);
      this.setState({
        AdValues: {
          ...AdValues,
          clientId: item?.clientId,
          type: item?.type,
          // url: item?.url,
          // id: item?.id,
        },
        // selectedModalBanner: item?.position_id,
        // selectedModalPage: item?.page_id,
        isEditMode: true,
      });
      if (item?.type === "image") {
        this.setState({
          uploadImage: item?.image,
        });
      } else {
        this.setState({
          script: item?.text,
        });
      }
    } else {
      this.setState({
        AdValues: {
          name: "",
          type: "",
          clientId: gotClientId,
          id: "",
          description: "",
        },
        script: "",
        image: [],
        uploadImage: "",
        isEditMode: false,
      });
    }
  };

  setActionMessage = (display = false, type = "", message = "") => {
    this.setState({ messageBox: { display, type, message } }, () =>
      setTimeout(
        () =>
          this.setState({
            messageBox: { display: false, type: "", message: "" },
          }),
        3000
      )
    );
  };

  setItemToDelete = (id) => () => {
    this.setState({ itemToDelete: id, isModalOpen: true });
  };

  toggleInputModal = () => {
    this.setState({
      isInputModalOpen: !this.state.isInputModalOpen,
      errorName: "",
      errorClient: "",
      errorDescription: "",
      errorType: "",
      errorScript: "",
      image: [],
      uploadImage: "",
      createError: "",
      isLoading: false,
    });
  };

  toggleModal = () => {
    this.setState({
      isModalOpen: !this.state.isModalOpen,
      itemToDelete: null,
    });
  };

  fetchMedialist = async (offsetsize, typeFilter, type, order) => {
    let id = this.props.match.params.id;
    const { rowPerPage, search } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(
        `/campaign/mediadetails/findAll?clientId=${id}&type=${
          typeFilter ? typeFilter : ""
        }&key=${type ? type : ""}&order=${
          order ? "ASC" : "DESC"
        }&limit=${rowPerPage}&offset=${offsetsize}&search=${search}`
      );
      if (status === 200) {
        const pageNumbers = [];
        if (data?.count > 0) {
          for (let i = 1; i <= Math.ceil(data?.count / rowPerPage); i++) {
            pageNumbers.push(i);
          }
        }

        this.setState({
          isLoading: false,
          mediaData: data?.result,
          paginationPage: pageNumbers,
          offset: offsetsize,
          mediaCount: data?.count,
          AdValues: {
            clientId: id,
          },
          gotClientId: id,
        });
        //   this.setActionMessage(
        //     true,
        //     "Success",
        //     `Country variation Created Successfully`
        //   );
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({ isLoading: false });
    }
  };

  fetchClientOptions = async (offsetsize) => {
    const { rowPerPage, search } = this.state;
    this.setState({ isLoading: true });
    try {
      const { status, data } = await axiosInstance.get(`/campaign/client`);
      if (status === 200) {
        let newdata = [];
        let optionData = data?.result?.map((item) => {
          newdata.push({
            label: item?.name,
            value: item?.id,
          });
        });
        const sortedData = newdata?.sort((a, b) => {
          return a?.label.localeCompare(b?.label);
        });
        this.setState({
          clientOptionsData: sortedData,
        });
      } else {
        this.setState({ isLoading: false });
      }
    } catch {
      this.setState({ isLoading: false });
    }
  };

  handalValidate = () => {
    let { AdValues, selectedModalBanner, selectedModalPage, script } =
      this.state;
    let flag = true;
    if (AdValues?.name === "") {
      flag = false;
      this.setState({
        errorName: "This field is mandatory",
      });
    } else {
      this.setState({
        errorName: "",
      });
    }
    if (AdValues?.description === "") {
      flag = false;
      this.setState({
        errorDescription: "This field is mandatory",
      });
    } else {
      this.setState({
        errorDescription: "",
      });
    }

    // if (selectedModalPage === "") {
    //   flag = false;
    //   this.setState({
    //     errorPage: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorPage: "",
    //   });
    // }
    // if (selectedModalPage === "") {
    //   flag = false;
    //   this.setState({
    //     errorPage: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorPage: "",
    //   });
    // }
    // if (
    //   selectedModalBanner === "" &&
    //   !(
    //     selectedModalPage === 9 ||
    //     selectedModalPage === 10 ||
    //     selectedModalPage === 11
    //   )
    // ) {
    //   flag = false;
    //   this.setState({
    //     errorPosition: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorPosition: "",
    //   });
    // }
    if (AdValues?.type === "") {
      flag = false;
      this.setState({
        errorType: "This field is mandatory",
      });
      return flag;
    } else {
      flag = true;
      this.setState({
        errorType: "",
      });
    }
    if (AdValues?.type === "script" && script === "") {
      flag = false;
      this.setState({
        errorScript: "This field is mandatory",
      });
      return flag;
    } else {
      this.setState({
        errorScript: "",
      });
    }

    // if (AdValues?.startDate === "") {
    //   flag = false;
    //   this.setState({
    //     errorstartDate: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorstartDate: "",
    //   });
    // }
    // if (AdValues?.endDate === "") {
    //   flag = false;
    //   this.setState({
    //     errorendDate: "This field is mandatory",
    //   });
    // } else {
    //   this.setState({
    //     errorendDate: "",
    //   });
    // }

    return flag;
  };

  setMedia = async (files) => {
    const formData = new FormData();
    formData.append("image", files ? files : null);
    if (files !== undefined) {
      const { status, data } = await axiosInstance.post(URLS.media, formData, {
        header: { "Content-Type": "multipart/form-data" },
      });
      if (status === 200) {
        return data;
      } else {
        return false;
      }
    }
    return false;
  };

  handleSave = async () => {
    const { HomeArticleData, key, order } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: false });

      const {
        AdValues,
        image,
        script,
        offset,
        sortType,
        sortLabelid,
        dataFilter,
      } = this.state;
      let payload = {
        name: AdValues?.name,
        type: AdValues?.type,
        description: AdValues?.description,
        clientId: AdValues?.clientId,
      };
      if (AdValues?.type === "image") {
        if (image?.length > 0) {
          let fileData = await this.setMedia(image[0]);
          if (fileData) {
            payload = {
              ...payload,
              image: fileData?.image?.filePath,
            };
            this.setState({
              uploadImage: fileData?.image?.filePath,
            });
          }
        }
      } else {
        payload = {
          ...payload,
          script: script,
        };
      }
      let sortData = sortType === "id" && sortLabelid;
      try {
        const { status, data } = await axiosInstance.post(
          `/campaign/mediadetails`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
            image: [],
            uploadImage: "",
          });
          this.fetchMedialist(offset, dataFilter, sortType, sortData);
          this.setActionMessage(true, "Success", `Media Created Successfully`);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  handleUpdate = async () => {
    const { HomeArticleData } = this.state;
    if (this.handalValidate()) {
      this.setState({ isLoading: true, isEditMode: true });

      const {
        AdValues,
        image,
        script,
        uploadImage,
        offset,
        sortType,
        sortLabelid,
        dataFilter,
      } = this.state;
      let payload = {
        name: AdValues?.name,
        type: AdValues?.type,
        description: AdValues?.description,
        clientId: AdValues?.clientId,
      };
      let sortData = sortType === "id" && sortLabelid;
      if (AdValues?.type === "image") {
        if (image?.length > 0) {
          let fileData = await this.setMedia(image[0]);
          if (fileData) {
            payload = {
              ...payload,
              image: fileData?.image?.filePath,
            };
            this.setState({
              uploadImage: fileData?.image?.filePath,
            });
          }
        } else {
          payload = {
            ...payload,
            image: uploadImage,
          };
        }
      } else {
        payload = {
          ...payload,
          script: script,
        };
      }
      try {
        const { status, data } = await axiosInstance.put(
          `/campaign/mediadetails/${AdValues?.id}`,
          payload
        );
        if (status === 200) {
          this.setState({
            isLoading: false,
            isInputModalOpen: false,
            createError: "",
            image: [],
            uploadImage: "",
          });
          this.fetchMedialist(offset, dataFilter, sortType, sortData);
          this.setActionMessage(true, "Success", `Media Edited Successfully`);
        } else {
          this.setActionMessage(true, "Error", data?.message);
          this.setState({ isLoading: false, createError: data?.message });
        }
      } catch (err) {
        this.setActionMessage(true, "Error", err?.response?.data?.message);
        this.setState({
          isLoading: false,
          isInputModalOpen: false,
          createError: err?.response?.data?.message,
        });
      }
    }
  };

  deleteItem = async () => {
    this.setState({ isLoading: false, isModalOpen: false });
    try {
      const {
        selectedPage,
        selectedBanner,
        currentPage,
        offset,
        sortEndDate,
        sortStartDate,
        sortName,
        sortType,
        sortLabelid,
        sortDate,
        filterEndDate,
      } = this.state;
      this.setState({ isLoading: true });
      const { status } = await axiosInstance.delete(
        `/campaign/mediadetails/${this.state.itemToDelete}`
      );
      if (status === 200) {
        this.setState({ itemToDelete: null, isModalOpen: false }, () => {
          this.fetchMedialist(offset, this.state.dataFilter, "id", false);
        });
        this.setActionMessage(true, "Success", "Media Deleted Successfully!");
      }
    } catch (err) {
      this.setActionMessage(true, "Error", "An error occurred while deleting.");
      this.setState({ isLoading: false, isModalOpen: false });
    }
  };

  handleFileUpload = (name, files) => {
    this.setState({ [name]: files });
  };

  handlePaginationClick = (event, page) => {
    let { rowPerPage, dataFilter } = this.state;

    const offsetNew = (Number(page) - 1) * rowPerPage;
    this.setState({
      currentPage: Number(page),
      offset: (Number(page) - 1) * rowPerPage,
    });
    this.fetchMedialist(
      (Number(page) - 1) * rowPerPage,
      dataFilter,
      "id",
      false
    );
  };

  handleMediaTypeChange = (e) => {
    this.setState({
      dataFilter: e?.label,
      currentPage: 1,
    });
    this.fetchMedialist(0, e?.label === "All" ? "" : e?.label, "id", false);
  };

  backToNavigatePage = () => {
    this.props.navigate(`/clients`);
  };

  sortLabelHandler = (type) => {
    const { sortLabelid } = this.state;
    this.setState({ sortType: type });
    if (type === "id") {
      this.fetchMedialist(0, "", type, !sortLabelid);
      this.setState({
        sortLabelid: !sortLabelid,
        currentPage: 1,
      });
    }
  };

  inputViewSourceModal = (data) => {
    this.setState({
      isViewSourceModalOpen: true,
      isViewSourceData: data,
    });
  };
  toggleViewSourceModal = () => {
    this.setState({
      isViewSourceModalOpen: false,
      isViewSourceData: {},
    });
  };
  handleClearClick = () => {
    this.setState({ search: "" });
  };

  render() {
    var {
      mediaData,
      rowPerPage,
      currentPage,
      messageBox,
      isLoading,
      isInputModalOpen,
      isEditMode,
      mediaCount,
      dataFilter,
      type,
      order,
      search,
      AdValues,
      isModalOpen,
      errorName,
      errorType,
      errorDescription,
      errorScript,
      image,
      uploadImage,
      script,
      createError,
      paginationPage,
      sortType,
      sortLabelid,
      isViewSourceModalOpen,
      isViewSourceData,
    } = this.state;
    return (
      <>
        <Grid container className="page-content adminLogin">
          <Grid item xs={12} className="pageWrapper">
            {messageBox.display && (
              <ActionMessage
                message={messageBox.message}
                type={messageBox.type}
                styleClass={messageBox.styleClass}
              />
            )}
            <Box className="bredcrumn-wrap">
              <Breadcrumbs
                separator="/"
                aria-label="breadcrumb"
                className="breadcrumb"
              >
                <Link underline="hover" color="inherit" to="/dashboard">
                  Home
                </Link>
                <Link underline="hover" color="inherit">
                  Campaign
                </Link>
                <Link underline="hover" color="inherit" to="/clients">
                  Client
                </Link>
                <Typography className="active_p">Media Gallery</Typography>
              </Breadcrumbs>
            </Box>

            <Grid container direction="row" alignItems="center">
              <Grid item xs={5}>
                <Box
                  style={{
                    display: "flex",
                    alignItems: "center",
                    columnGap: "20px",
                  }}
                >
                  <Button
                    className="admin-btn-margin admin-btn-back"
                    onClick={this.backToNavigatePage}
                  >
                    <MdKeyboardBackspace />
                  </Button>
                  <Typography variant="h1" align="left">
                    Media Gallery
                  </Typography>
                  {!isLoading && mediaData.length > 0 && (
                    <Typography variant="h6" align="left">
                      / {mediaData[0]?.Client?.name}
                    </Typography>
                  )}
                </Box>
              </Grid>
              <Grid item xs={7} className="admin-filter-wrap">
                <Select
                  className="React cricket-select  external-select"
                  classNamePrefix="select"
                  placeholder="Select Type"
                  value={typeOptions?.find((item) => {
                    return item?.label == dataFilter;
                  })}
                  //   isLoading={isLoading}
                  onChange={(e) => this.handleMediaTypeChange(e)}
                  options={typeOptions}
                />
                <TextField
                  placeholder="Search "
                  size="small"
                  variant="outlined"
                  value={search}
                  onChange={(e) => {
                    this.setState({ search: e.target.value });
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <img src={SearchIcons} alt="icon" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        {search && (
                          <IconButton
                            onClick={() => this.handleClearClick()}
                            edge="end"
                            style={{ minWidth: "unset" }}
                            size="large"
                          >
                            <CancelIcon />
                          </IconButton>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  style={{
                    background: "#ffffff",
                  }}
                />
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455c7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                  }}
                  onClick={() => {
                    this.fetchMedialist(0, dataFilter, type, order);
                    this.setState({ currentPage: 1 });
                  }}
                >
                  Search
                </Button>
                <Button
                  variant="contained"
                  style={{
                    backgroundColor: "#4455C7",
                    color: "#fff",
                    borderRadius: "8px",
                    textTransform: "capitalize",
                    padding: "6px 10px",
                  }}
                  onClick={this.inputModal(null, "create")}
                >
                  Add New
                </Button>
              </Grid>
            </Grid>

            {isLoading && <Loader />}
            {!isLoading && mediaData.length === 0 && <p>No Data Available</p>}
            {!isLoading && mediaData.length > 0 && (
              <>
                <TableContainer component={Paper}>
                  <Table className="listTable" aria-label="simple table">
                    <TableHead className="tableHead-row">
                      <TableRow>
                        <TableCell
                          onClick={() => this.sortLabelHandler("id")}
                          style={{ width: "8%", cursor: "pointer" }}
                        >
                          DID
                          <TableSortLabel
                            active={true}
                            direction={
                              sortType === "id"
                                ? sortLabelid
                                  ? "asc"
                                  : "desc"
                                : "desc"
                            }
                          />
                        </TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>View Source</TableCell>
                        <TableCell>Ad Type</TableCell>
                        <TableCell>Client</TableCell>
                        <TableCell>Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody className="table_body">
                      <TableRow className="table_row">
                        <TableCell
                          colSpan={100}
                          className="table-seprator"
                        ></TableCell>
                      </TableRow>
                      {mediaData?.length > 0 ? (
                        mediaData?.map((media, i) => (
                          <TableRow
                            key={i}
                            className="table-rows listTable-Row"
                          >
                            <TableCell>{media.id}</TableCell>
                            <TableCell>{media.name}</TableCell>
                            <TableCell>{media.description}</TableCell>
                            <TableCell>
                              <Button
                                variant="contained"
                                style={{
                                  backgroundColor: "#4455C7",
                                  color: "#fff",
                                  borderRadius: "8px",
                                  textTransform: "capitalize",
                                }}
                                onClick={() => {
                                  this.inputViewSourceModal(media);
                                }}
                              >
                                View Source
                              </Button>
                            </TableCell>
                            <TableCell>
                              {media?.image === null ? "Script" : "Image"}
                            </TableCell>
                            <TableCell>{media?.Client?.name}</TableCell>
                            <TableCell>
                              <Button
                                onClick={this.inputModal(media, "edit")}
                                className="table-btn edit-btn"
                              >
                                Edit
                              </Button>
                              <Button
                                onClick={this.setItemToDelete(media?.id)}
                                className="table-btn delete-btn"
                              >
                                Delete
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={100}
                            style={{ textAlign: "center" }}
                          >
                            No Data Available
                          </TableCell>
                        </TableRow>
                      )}
                      <TableRow>
                        <TableCell colSpan={100} className="pagination">
                          <div className="tablePagination">
                            <Pagination
                              hideNextButton
                              hidePrevButton
                              disabled={
                                mediaCount / rowPerPage > 1 ? false : true
                              }
                              page={currentPage}
                              onChange={this.handlePaginationClick}
                              count={paginationPage[paginationPage?.length - 1]}
                              siblingCount={2}
                              boundaryCount={1}
                              size="small"
                            />
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <ShowModal
              isModalOpen={isModalOpen}
              onClose={this.toggleModal}
              Content="Are you sure you want to delete?"
              onOkayLabel="Yes"
              onOkay={this.deleteItem}
              onCancel={this.toggleModal}
            />
            <Modal
              className="modal modal-input"
              open={isInputModalOpen}
              onClose={this.toggleInputModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center">
                  {!isEditMode ? "Create New Media" : "Edit Media"}
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleInputModal}
                />
                <Grid container className="page-content adminLogin text-left">
                  <Grid item xs={12} className="pageWrapper api-provider">
                    <Grid container>
                      <Grid
                        item
                        xs={12}
                        style={{ display: "flex", flexDirection: "column" }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Name </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Name"
                          value={AdValues?.name}
                          onChange={(e) =>
                            this.setState({
                              AdValues: {
                                ...AdValues,
                                name: e.target.value,
                              },
                            })
                          }
                        />
                        {errorName ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorName}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          marginTop: "15px",
                        }}
                        className="teamsport-text"
                      >
                        <label className="modal-label"> Description </label>
                        <TextField
                          className="teamsport-textfield eventname"
                          variant="outlined"
                          color="primary"
                          size="small"
                          placeholder="Description"
                          value={AdValues?.description}
                          onChange={(e) =>
                            this.setState({
                              AdValues: {
                                ...AdValues,
                                description: e.target.value,
                              },
                            })
                          }
                        />
                        {errorDescription ? (
                          <p
                            className="errorText"
                            style={{ margin: "-14px 0 0 0" }}
                          >
                            {errorDescription}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {/* <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        marginTop: "15px",
                      }}
                      className="teamsport-text"
                    >
                      <label className="modal-label"> Client </label>
                      <TextField
                        className="teamsport-textfield eventname"
                        variant="outlined"
                        color="primary"
                        size="small"
                        placeholder="Client"
                        value={AdValues?.client}
                        onChange={(e) =>
                          this.setState({
                            AdValues: {
                              ...AdValues,
                              client: e.target.value,
                            },
                          })
                        }
                      />
                      {errorUrl ? (
                        <p
                          className="errorText"
                          style={{ margin: "-14px 0 0 0" }}
                        >
                          {errorUrl}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid> */}
                      {/* <Grid
                      item
                      xs={6}
                      className="national-select"
                    >
                      <label className="modal-label"> Client </label>
                      <Select
                        className="React teamsport-select"
                        classNamePrefix="select"
                        menuPosition="fixed"
                        placeholder="Select Client"
                        value={clientOptionsData?.find((item) => {
               
                          return item?.value == AdValues?.clientId;
                        })}
                        // onChange={(option) => {
                  
                        //   // handleSelectOptionChnage(option?.label);
                        //   this.setState({
                        //     AdValues: {
                        //       ...AdValues,
                        //       clientId: option?.value,
                        //     },
                        //   });
                        // }}
                        options={clientOptionsData}
                        isDisabled={true}
                      />
                      {errorClient ? (
                        <p
                          className="errorText"
                          style={{ margin: "0px 0 0 0" }}
                        >
                          {errorClient}
                        </p>
                      ) : (
                        ""
                      )}
                    </Grid> */}
                      <Grid
                        item
                        xs={12}
                        className="radio-wrap ad-radio"
                        style={{ marginTop: "15px" }}
                      >
                        <FormControl component="fieldset">
                          <label className="modal-label"> Ad Type </label>
                          <RadioGroup
                            aria-label="Ad Type"
                            name="Ad Type"
                            className="gender"
                            value={AdValues?.type}
                            onChange={(e) =>
                              this.setState({
                                AdValues: {
                                  ...AdValues,
                                  type: e.target.value,
                                },
                                createError: "",
                              })
                            }
                          >
                            <FormControlLabel
                              value="image"
                              control={
                                <Radio
                                  color="primary"
                                  checked={AdValues?.type === "image"}
                                />
                              }
                              label="image"
                            />
                            <FormControlLabel
                              value="script"
                              control={
                                <Radio
                                  color="primary"
                                  checked={AdValues?.type === "script"}
                                />
                              }
                              label="script"
                            />
                          </RadioGroup>
                        </FormControl>
                        {errorType && AdValues?.type === "" ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0" }}
                          >
                            {errorType}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                      {AdValues?.type === "image" ? (
                        <Grid item xs={12}>
                          <div className="blog-file-upload">
                            <h6>image</h6>
                            <FileUploader
                              onDrop={(image) =>
                                this.handleFileUpload("image", image)
                              }
                            />
                            <div className="logocontainer">
                              {image?.length > 0
                                ? image?.map((file, index) => (
                                    <>
                                      <img
                                        className="auto-width"
                                        key={index}
                                        src={file.preview}
                                        alt="ad"
                                      />
                                    </>
                                  ))
                                : uploadImage &&
                                  uploadImage !== "" && (
                                    <>
                                      <img
                                        className="auto-width"
                                        src={config.mediaUrl + uploadImage}
                                        alt="ad"
                                      />
                                    </>
                                  )}
                            </div>
                          </div>
                        </Grid>
                      ) : AdValues?.type === "script" ? (
                        <Grid item xs={12}>
                          <TextField
                            id="standard-multiline-static"
                            variant="outlined"
                            multiline
                            rows={4}
                            style={{
                              width: "100%",
                              backgroundColor: "#ffffff",
                            }}
                            placeholder="Script"
                            value={script}
                            onChange={(e) =>
                              this.setState({
                                script: e.target.value,
                              })
                            }
                          />
                          {errorScript && script === "" ? (
                            <p
                              className="errorText"
                              style={{ margin: "0px 0 0 0" }}
                            >
                              {errorScript}
                            </p>
                          ) : (
                            ""
                          )}
                        </Grid>
                      ) : (
                        <></>
                      )}
                    </Grid>

                    <Grid container>
                      <Grid item xs={3}>
                        <div style={{ marginTop: "20px", display: "flex" }}>
                          {!isEditMode ? (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleSave}
                              color="primary"
                              value={!isLoading ? "Save" : "Loading..."}
                              disabled={isLoading}
                            />
                          ) : (
                            <ButtonComponent
                              className="mt-3 admin-btn-green"
                              onClick={this.handleUpdate}
                              color="secondary"
                              value={!isLoading ? "Update" : "Loading..."}
                              disabled={isLoading}
                            />
                          )}

                          <ButtonComponent
                            onClick={this.toggleInputModal}
                            className="mr-lr-30 back-btn-modal"
                            value="Back"
                          />
                        </div>
                        {createError ? (
                          <p
                            className="errorText"
                            style={{ margin: "0px 0 0 0", width: "300px" }}
                          >
                            {createError}
                          </p>
                        ) : (
                          ""
                        )}
                      </Grid>
                    </Grid>
                    {/* </Paper> */}
                  </Grid>
                </Grid>
              </div>
            </Modal>

            <Modal
              className="modal modal-input"
              open={isViewSourceModalOpen}
              onClose={this.toggleViewSourceModal}
            >
              <div
                className={"paper modal-show-scroll"}
                style={{ position: "relative" }}
              >
                <h3 className="text-center modal-head">
                  {/* {isEditMode === true ? "Edit Track " : "Add Track"} */}
                  View Source Details
                </h3>
                <CancelIcon
                  className="admin-close-icon"
                  onClick={this.toggleViewSourceModal}
                />
                <Grid item xs={12} className="runnerInfo">
                  <Grid
                    item
                    xs={12}
                    className="runnerInfo-text"
                    style={{ display: "flex", flexDirection: "column" }}
                  >
                    {isViewSourceData?.type === "image" ? (
                      <Box>
                        <img
                          className="auto-width"
                          src={config.mediaUrl + isViewSourceData?.image}
                          alt="ad"
                        />
                      </Box>
                    ) : (
                      <Box>
                        <TextField
                          id="standard-multiline-static"
                          variant="outlined"
                          multiline
                          rows={4}
                          style={{
                            width: "100%",
                            backgroundColor: "#ffffff",
                          }}
                          placeholder="Script"
                          value={
                            isViewSourceData?.script
                              ? isViewSourceData?.script
                              : ""
                          }
                          // onChange={(e) =>
                          //   this.setState({
                          //     script: e.target.value,
                          //   })
                          // }
                        />
                      </Box>
                    )}
                  </Grid>
                </Grid>

                <Grid container>
                  <Grid item xs={3}>
                    <div style={{ marginTop: "20px", display: "flex" }}>
                      <ButtonComponent
                        onClick={this.toggleViewSourceModal}
                        // className="mr-lr-30"
                        value="Back"
                        style={{ minWidth: "auto" }}
                      />
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Modal>
          </Grid>
        </Grid>
      </>
    );
  }
}
