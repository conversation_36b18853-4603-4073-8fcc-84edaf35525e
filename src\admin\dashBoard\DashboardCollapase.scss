@import "../../assets/scss/variables.scss";

.racing-colleps {
  margin-top: 18px;

  .colleps-accordion {
    margin-bottom: 18px;
    box-shadow: 0px 3px 9px 0px #0000000d;
    background: #fff;
  }

  .MuiAccordionSummary-root {
    padding: 0px 33px;
    background: linear-gradient(90deg, #4455c7 0%, #003764 68%);
    min-height: 45px;
  }

  .MuiAccordionSummary-root.Mui-expanded {
    min-height: 45px;
  }

  .MuiAccordionSummary-content {
    margin: 0px;
  }

  .MuiAccordionSummary-content.Mui-expanded {
    margin: 0px;
  }

  .MuiTypography-body1 {
    font-size: 22px;
    font-family: $primaryFont;
    color: #fff;
    line-height: 31.36px;
    margin-left: 9px;
  }

  .MuiSvgIcon-root {
    fill: #ffffff;
  }

  .accordion_details {
    width: 100%;
    max-width: fit-content;
    overflow-x: auto;
  }
  .fixture-wrape {
    min-width: fit-content;
  }

  .country-title {
    font-family: "VeneerClean-Soft" !important;
    color: #000000;
    font-size: 22.4px;
    line-height: 31.36px;
    border-bottom: 1px solid #4455c7;
    text-align: left;
    margin: 10px 0px 0px;
  }

  .rt-thead {
    max-width: 84px;
    min-width: 74px;

    img {
      transform: rotate(270deg);
      max-width: 74px;
      margin-top: 4px;
    }

    .square-bookmaker {
      // max-width: 35px;
      width: 75%;
    }
  }

  .fixture {
    background: #78c2a7;
  }

  .notfixture {
    background: #d84727;
  }

  .ignore {
    background: #d4d6d8;
  }
}

.fixture-table-wrap {
  padding-top: 17px;
  overflow-x: initial !important;
  .MuiTableRow-root {
    border: none;
  }

  thead {
    .MuiTableCell-root:first-child {
      border: none;
      min-width: 220px;
    }

    .MuiTableCell-root:not(:first-child) {
      border: 1px solid #707070;
    }

    th {
      font-size: 16px;
      line-height: 19px;
      font-weight: 600;
      padding: 7px 0;
      text-align: center;
      border-width: 0 0 1px;
      border-color: #d4d6d8;
      font-family: "Inter", sans-serif;
      height: 70px;
    }
  }

  tbody {
    .seprator {
      padding: 0px;
      height: 13px !important;
    }

    th {
      cursor: pointer;
      display: flex;
      padding: 13px 0;
      grid-column-gap: 9px;
      -webkit-column-gap: 9px;
      column-gap: 9px;
      border: none;
      text-align: left;
      align-items: center;
      min-width: 220px;
      max-width: 220px;
    }

    .MuiTableCell-root:first-child {
      border: none;
    }

    .MuiTableCell-root:not(:first-child) {
      border: 1px solid #d4d6d8;
      min-width: 44px;
    }

    .flag-icon {
      max-width: 37px;
      height: 21.58px;
      object-fit: cover;
    }

    h6 {
      font-size: 16px;
      line-height: 19px;
      color: #191919;
      font-weight: 400;
      word-break: break-word;
      max-width: 180px;
      width: 100%;
      overflow: hidden;
      display: inline-block;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.fixture-info {
  padding: 22px 33px;

  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    grid-column-gap: 25px;
    -webkit-column-gap: 25px;
    column-gap: 25px;
    align-items: center;

    span.sqare {
      width: 15px;
      height: 15px;
    }

    li {
      display: flex;
      align-items: center;
      grid-column-gap: 8px;
      -webkit-column-gap: 8px;
      column-gap: 8px;
    }
  }
}
