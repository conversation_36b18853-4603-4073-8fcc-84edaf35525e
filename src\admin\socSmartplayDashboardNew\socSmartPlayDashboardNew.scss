@import "../../assets/scss/variables";

.soc-smartplay-dashboard-wrap {
  .d-flex-ss {
    display: flex;
  }
  .align-items-center {
    align-items: center;
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .gap-18 {
    gap: 18px;
  }

  .w-100 {
    width: 100%;
  }

  .w-50 {
    width: 50%;
  }

  .mt-18 {
    margin-top: 18px;
  }

  .mb-18 {
    margin-bottom: 18px;
  }

  .header-top-filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #e7e9ec;

    .year-filter {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .header-tab-section {
      .MuiTab-root {
        font-family: $Apotek_Comp_Regular !important;
        text-transform: uppercase;
        font-weight: 400;
        font-size: 31.36px;
        line-height: 37.63px;
        color: #c9c9c9;
      }

      .Mui-selected {
        color: #191919;
      }
    }
  }

  .tab-deatils-section {
    text-align: left;
  }

  // card design

  .card-section {
    display: flex;
    align-items: center;
    column-gap: 18px;
    row-gap: 18px;

    .card-wrap {
      padding: 28px 27px 24px;
      border-radius: 12px;
      border: 1px solid #e7e9ec;
      background-color: #ffffff;

      .card-title {
        font-size: 16px;
        line-height: 19px;
        font-weight: 400;
        font-family: $regulerFont;
        color: $color-Black;
        margin-bottom: 13px;
      }

      .card-title-details {
        display: flex;
        align-items: center;
        column-gap: 18px;

        .count-section {
          display: flex;
          align-items: center;
          column-gap: 8px;

          .coins-wrap {
            width: 36px;
            height: 36px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .count-value {
            font-size: 43.9px;
            line-height: 48.9px;
            font-weight: 600;
            font-family: $regulerFont;
            color: $color-Black;
            letter-spacing: 0px;
            margin: 0px;
          }
        }

        .value-change {
          display: flex;
          align-items: center;
          column-gap: 6px;
          font-size: 18px;
          line-height: 21px;
          font-weight: 400;
          font-family: $regulerFont;
        }
      }
    }
  }

  .bar-chart-section {
    margin-top: 18px;
  }

  .bar-wrap-section {
    padding: 30px;
    border-radius: 12px;
    background-color: $color-White;
    border: 1px solid #e7e9ec;
  }

  .graph-title {
    font-size: 16px;
    line-height: 19px;
    font-family: $regulerFont;
    font-weight: 400;
    color: $color-Black;
  }

  .graph-count-section{
    margin-top: 2px;

    .details-count{
      font-size: 31.36px;
      line-height: 37.63px;
      font-weight: 600;
      font-family: $regulerFont;
      color: $color-Black;
      letter-spacing: 0px;
    }

    .value-change-count {
      column-gap: 6px;
      font-size: 18px;
      line-height: 21px;
      font-weight: 400;
      font-family: $regulerFont;
      color: #1C9A6C;
    }
  }

  .arrow-text{
    font-size: 16px;
    line-height: 19px;
    font-weight: 400;
    font-family: $regulerFont;
    color: $color-Black;
    letter-spacing: 0px;
    cursor: pointer;
  }
}
